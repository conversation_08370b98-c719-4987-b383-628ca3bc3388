# 权限错误处理改进方案

## 问题描述

在当前的代码中，当用户访问微服务应用基础信息页面时，如果缺少相关权限（如 ALB LoadBalancers 接口权限），用户只能看到简单的"权限不足"提示，无法了解具体是哪个模块、哪个接口缺少什么权限。

## 解决方案

### 1. 新增权限管理工具类 (`src/sae-one/utils/permissionUtils.ts`)

- **权限配置映射表**: 定义了各个模块的权限配置信息
- **统一的权限检查函数**: `isForbidden()` 用于检查错误码是否为权限错误
- **权限错误创建函数**: `createPermissionError()` 根据配置创建详细的权限错误对象
- **自定义错误处理器**: `createCustomErrorHandler()` 创建统一的错误处理函数

```typescript
// 权限配置示例
'alb.ListLoadBalancers': {
  moduleName: '应用型负载均衡器(ALB)',
  apiName: 'ListLoadBalancers',
  requiredPolicy: 'AliyunSLBReadOnlyAccess',
  helpUrl: 'https://help.aliyun.com/document_detail/66384.html',
  description: '查看ALB实例列表权限'
}
```

### 2. 权限错误显示组件 (`src/sae-one/components/application/PermissionError.tsx`)

提供了三种显示方式：
- **inline**: 行内显示模式
- **message**: 消息提示模式  
- **balloon**: 气泡悬浮提示模式

### 3. 改进的权限错误处理

#### 在 BindRouteCard 组件中的改进：

**原来的错误处理：**
```javascript
customErrorHandle: (error,data,callback) => {
  const forbidden = isForbidden(error.code);
  let _permissionInfo = cloneDeep(permissionInfo);
  _permissionInfo.alb = !forbidden;
  setPermissionInfo(_permissionInfo);
  !forbidden && callback?.();
}
```

**改进后的错误处理：**
```javascript
customErrorHandle: createCustomErrorHandler(
  'alb.ListLoadBalancers',
  (error) => {
    const _permissionInfo = cloneDeep(permissionInfo);
    _permissionInfo.alb = false;
    setPermissionInfo(_permissionInfo);
    
    const _permissionErrors = { ...permissionErrors };
    _permissionErrors.alb = error;
    setPermissionErrors(_permissionErrors);
  },
  () => {
    const _permissionInfo = cloneDeep(permissionInfo);
    _permissionInfo.alb = true;
    setPermissionInfo(_permissionInfo);
  }
)
```

#### 在 RouteTable 组件中的权限错误显示改进：

**原来的显示：**
```javascript
<Icon type="warning" size="xs" style={{ color: '#ff3333' }} />
<span style={{ marginLeft: 4 }}>
  {!permissionInfo?.alb
    ? intl('saenext.components.application.RouteTable.TheCurrentUserLacksThe')
    : intl('saenext.components.application.RouteTable.TheIngressGatewayInstanceHas')}
</span>
```

**改进后的显示：**
```javascript
<Balloon
  trigger={
    <span style={{ color: '#ff3333', cursor: 'pointer' }}>
      <Icon type="warning" size="xs" style={{ color: '#ff3333', marginRight: 4 }} />
      {intl('saenext.components.application.RouteTable.PermissionDenied')}
    </span>
  }
  align="t"
  triggerType="hover"
  closable={false}
>
  <div style={{ maxWidth: 300 }}>
    <div style={{ fontWeight: 'bold', marginBottom: 8 }}>
      {intl('saenext.components.application.RouteTable.AccessDenied')}
    </div>
    <div style={{ marginBottom: 4 }}>
      <strong>{intl('saenext.components.application.RouteTable.Module')}: </strong>
      {permissionErrors.alb?.moduleName || '应用型负载均衡器(ALB)'}
    </div>
    <div style={{ marginBottom: 4 }}>
      <strong>{intl('saenext.components.application.RouteTable.Interface')}: </strong>
      {permissionErrors.alb?.apiName || 'ListLoadBalancers'}
    </div>
    <div style={{ marginBottom: 4 }}>
      <strong>{intl('saenext.components.application.RouteTable.RequiredPolicy')}: </strong>
      <code style={{ background: '#f5f5f5', padding: '2px 4px', borderRadius: 2 }}>
        {permissionErrors.alb?.requiredPolicy || 'AliyunSLBReadOnlyAccess'}
      </code>
    </div>
    {permissionErrors.alb?.code && (
      <div style={{ marginBottom: 4 }}>
        <strong>{intl('saenext.components.application.RouteTable.ErrorCode')}: </strong>
        {permissionErrors.alb.code}
      </div>
    )}
  </div>
</Balloon>
```

## 改进效果

### 改进前用户体验：
- 用户看到模糊的"权限不足"提示
- 不知道具体是什么权限问题
- 无法快速定位和解决问题

### 改进后用户体验：
- **明确的模块信息**: 用户知道是"应用型负载均衡器(ALB)"模块的问题
- **具体的接口信息**: 用户知道是"ListLoadBalancers"接口调用失败
- **准确的权限策略**: 用户知道需要"AliyunSLBReadOnlyAccess"权限
- **错误代码**: 显示具体的错误代码便于排查
- **帮助文档链接**: 可以直接跳转到相关权限配置文档

## 适用场景

这套改进方案可以应用到项目中所有涉及权限检查的地方：
- 负载均衡器相关接口 (ALB, CLB, NLB)
- 微服务引擎 (MSE) 相关接口
- API网关 (APIG) 相关接口
- VPC、ECS 等其他云服务接口

## 使用方法

1. 在 `permissionUtils.ts` 中添加新的权限配置
2. 使用 `createCustomErrorHandler` 创建错误处理函数
3. 在组件中使用权限错误信息进行显示
4. 添加相应的国际化文本

这样的改进让用户能够快速了解权限问题的具体情况，提升用户体验和问题解决效率。 