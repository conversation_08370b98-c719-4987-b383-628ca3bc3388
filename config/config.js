/**
 * 整个应用构建, 编译配置
 *
 * 详细工程化相关配置 详情参见：
 * https://xconsole.aliyun-inc.com/nexconsole/develop/vcpzm0
 */
import merge from 'webpack-merge';

const IS_IN_DEF_IDE = process.env.IDE_NAME === 'ide-framework-server';
const IS_PRODUCTION = process.env.NODE_ENV === 'production';

module.exports = {
  // 产品名称, 如果是控制台项目一定要配置此选项
  // 保持和你 viper code 相关
  product: 'serverless',
  appId: 'saenext',
  // 路由配置
  routes: {
    index: '/overview',
  },

  host: 'my.console.aliyun.com',

  // 编译构建 typescript
  useTypescript: true,

  // 开启 topbar, sidebar
  consoleBase: {
    enableErrorPromptProxy: true,
  },

  // 加入监控脚本
  armsId: '',

  // 开启 oneConsole 的 meta 标签
  oneConsole: true,
  // 国际化配置
  intl: {
    locale: 'zh',
    products: [
      {
        group: 'aliyun',
        name: 'sae',
        identifier: 'ALIYUN_WIND_MESSAGE',
      },
    ],
    messages: 'locales/messages.js',
  },

  // mocks 配置
  // mocks: {
  //   oneapi: true,
  //   product: 'wind-pro',
  // },

  // 开启低版本浏览器提示
  browserCompatibility: true,

  useTerserPlugin: true,

  // 在 DEF IDE 中启动项目的时候，不自动打开浏览器
  noOpen: IS_IN_DEF_IDE ? true : false,

  // 自定义 webpack 配置
  // webpack: (config) => { /* 请自行用 webpack-merge 和传入的 config 做合并 */}

  webpack: config => {
    const configModified = {
      ...config,
      module: {
        ...config.module,
        rules: [
          ...config.module.rules,
          {
            test: /\.mjs$/,
            include: /node_modules/,
            type: 'javascript/auto',
          },
        ],
      },
    };
    if (IS_PRODUCTION) {
      return configModified;
    }
    const devServer = {
      ...configModified.devServer,
      proxy: {
        '/data/api.json': {
          target: 'https://sae.console.aliyun.com/',
          changeOrigin: true,
          secure: false,
          ws: true,
          withCredentials: true,
        },
        '/tool/': {
          target: 'https://sae.console.aliyun.com/',
          changeOrigin: true,
          secure: false,
          ws: true,
          withCredentials: true,
        },
        '/data/custom.json': {
          target: 'https://sae.console.aliyun.com',
          changeOrigin: true,
          secure: false,
          ws: true,
          withCredentials: true,
        },
      },
    };

    return merge(configModified, {
      devServer,
    });
  },

  // add more configs
};
