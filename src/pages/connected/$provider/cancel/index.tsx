import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import services from '~/services';
import { getParams } from '~/utils/global';

const AuthCancel = (props) => {
  const {
    match: {
      params: { provider },
    },
  } = props;

  const Provider = provider || getParams('provider');
  const AccountId = getParams('accountId');
  const region = localStorage.getItem('authRegionId');
  const [text, setText] = useState('');

  const closeCurrentPage = () => {
    window.opener = null;
    window.open('', '_self');
    window.close();
    localStorage.setItem('authTrigger', Provider);
  };

  useEffect(() => {
    if (AccountId && Provider) {
      setText(intl('saenext.provider.cancel.CancelingAuthorizationDoNotRefresh'));
      services
        .unbindSourceCodeAccount({
          Provider,
          AccountId,
        }, {
          region,
        })
        .then((res) => {
          if (res) {
            setText(intl('saenext.provider.cancel.TheAuthorizationIsCanceledAnd'));
            closeCurrentPage();
          } else {
            setText(intl('saenext.provider.cancel.FailedToCancelAuthorizationPlease'));
          }
        });
    } else {
      setText(intl('saenext.provider.cancel.TheParameterIsIncorrectPlease'));
    }
  }, []);

  return <div className="m-card">{text}</div>;
};

export default AuthCancel;
