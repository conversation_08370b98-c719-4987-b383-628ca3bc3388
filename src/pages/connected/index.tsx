import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { getParams } from '~/utils/global';
import { createSourceCodeAccount } from '~/services/sourceCode';

const AuthConnected = (props) => {
  const {
    match: {
      params: { provider },
    },
  } = props;

  const Code = getParams('code');
  const Provider = provider || getParams('provider');
  const region = localStorage.getItem('authRegionId');
  const [text, setText] = useState('');

  const closeCurrentPage = () => {
    window.opener = null;
    window.open('', '_self');
    window.close();
    localStorage.setItem('authTrigger', Provider);
  };

  useEffect(() => {
    if (Code && Provider) {
      setText(intl('saenext.pages.connected.AuthorizationIsInProgressDo'));
      createSourceCodeAccount({
        Provider,
        Code,
      }, {
        region,
      }).then((res) => {
        if (res) {
          setText(intl('saenext.pages.connected.TheAuthorizationIsSuccessfulAnd'));
          closeCurrentPage();
        } else {
          setText(intl('saenext.pages.connected.AuthorizationFailedPleaseReAuthorize'));
        }
      });
    } else {
      setText(intl('saenext.pages.connected.TheParameterIsIncorrectPlease'));
    }
  }, []);

  return <div className="m-card">{text}</div>;
};

export default AuthConnected;
