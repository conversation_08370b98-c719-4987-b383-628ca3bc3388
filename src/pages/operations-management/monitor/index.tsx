import React, { useState, useEffect } from 'react';
import { intl, Page } from '@ali/cnd';
import useRegion from '~/hooks/useRegion';
import { getParams, setSearchParams } from '~/utils/global';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';
import NsNonitor from '~/components/namespace/monitor';

const MonitorPage = (props) => {
  const regionId = useRegion();
  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : regionId;

  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);

  useEffect(() => {
    console.log('regionId', regionId);
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
  };
  return (
    <Page
      key="base-monitor"
      title={intl('saenext.operations-management.monitor.BasicMonitoring')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
          disabledAll={true}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.operations-management.monitor.BasicMonitoring'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <NsNonitor {...props} regionId={regionId} namespaceId={namespaceId} />
    </Page>
  );
};

export default MonitorPage;
