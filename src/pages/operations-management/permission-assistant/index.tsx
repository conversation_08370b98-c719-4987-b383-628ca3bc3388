import React from 'react';
import { intl, Page } from '@ali/cnd';
import PermissionAssistant from '~/components/enterprise/permission-assistant';

const PermissionAssistantPage = props => {

  return (
    <Page
      key='permission-assistant'
      title={intl("saenext.enterprise.permission.constant.PermissionAssistant")}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
         text: intl("saenext.enterprise.permission.constant.PermissionAssistant")
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <PermissionAssistant />
    </Page>
  );
};

export default PermissionAssistantPage;
