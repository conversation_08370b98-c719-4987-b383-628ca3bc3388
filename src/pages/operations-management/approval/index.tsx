import React, { useState, useEffect } from 'react';
import { intl, Page, Tab } from '@ali/cnd';
import { getParams } from '~/utils/global';
import ApprovalSetting from '~/components/enterprise/approval-setting';
import ApprovalRecords from '~/components/enterprise/approval-records';
import ConcatsTable from '~/components/enterprise/concats';

const ApprovalPage = (props) => {
  const [activeKey, setActiveKey] = useState('approval-setting');

  useEffect(() => {
    const tabParams = getParams('type');
    if (tabParams) {
      setActiveKey(tabParams);
    }
  }, []);
  return (
    <Page
      key="approval"
      title={intl('saenext.operations-management.approval.ApprovalManagement')}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.operations-management.approval.ApprovalManagement'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <Tab
        activeKey={activeKey}
        shape="wrapped"
        unmountInactiveTabs
        onChange={(key) => setActiveKey(key)}
      >
        <Tab.Item
          title={intl('saenext.enterprise.permission.constant.ApprovalSettings')}
          key="approval-setting"
        >
          <div className="mt-s">
            <ApprovalSetting />
          </div>
        </Tab.Item>
        <Tab.Item
          title={intl('saenext.enterprise.permission.constant.ApprovalRecord')}
          key="approval-records"
        >
          <div className="mt-s">
            <ApprovalRecords />
          </div>
        </Tab.Item>
        <Tab.Item title={intl('saenext.enterprise.concats.ContactManagement')} key="concats">
          <div className="mt-s">
            <ConcatsTable />
          </div>
        </Tab.Item>
      </Tab>
    </Page>
  );
};

export default ApprovalPage;
