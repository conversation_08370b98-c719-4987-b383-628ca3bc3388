import React, { useState, useEffect } from 'react';
import { intl, Page, Tab } from '@ali/cnd';
import { getParams } from '~/utils/global';
import useRegion from '~/hooks/useRegion';
import { getArmsDomain } from '~/utils/global';
import ArmsIframe from '~/components/shared/ArmsIframe';
import CachedData from '~/cache/common';

const AlarmPage = (props) => {
  const regionId = useRegion();
  const [activereKey, setActiveKey] = useState('approval-setting');

  useEffect(() => {
    const tabParams = getParams('type');
    if (tabParams) {
      setActiveKey(tabParams);
    }
  }, []);

  return (
    <Page
      key="alarm"
      title={intl('saenext.operations-management.alarm.AlarmManagement')}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.operations-management.alarm.AlarmManagement'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <Tab shape="wrapped" unmountInactiveTabs>
        <Tab.Item key="alarm-rules" title={intl('saenext.micro-app.section.AlertRules')}>
          <ArmsIframe
            regionId={regionId}
            url={`${getArmsDomain(
              regionId,
            )}/?iframeMode=edas&iframeModeApp=sae&regionId=${regionId}#/tracing/alarm/rule/list/${regionId}?tracing`}
          />
        </Tab.Item>
        <Tab.Item
          key="alarm-dispatch"
          title={intl('saenext.micro-app.section.AlertSendingHistory')}
        >
          <ArmsIframe
            regionId={regionId}
            url={`${CachedData.confLink(
              'feature:arms:url',
            )}/index?edasiframeMode=sae&regionId=${regionId}#/alarm/alert/list`}
          />
        </Tab.Item>
        <Tab.Item key="alarm-incident" title={intl('saenext.micro-app.section.AlarmEventHistory')}>
          <ArmsIframe
            regionId={regionId}
            url={`${CachedData.confLink(
              'feature:arms:url',
            )}/index?edasiframeMode=sae&regionId=${regionId}#/alarm/incident`}
          />
        </Tab.Item>
        <Tab.Item key="alarm-contact" title={intl('saenext.micro-app.section.ContactManagement')}>
          <ArmsIframe
            regionId={regionId}
            url={`${CachedData.confLink(
              'feature:arms:url',
            )}/index?edasiframeMode=sae&regionId=${regionId}#/alarm/notifyObject`}
          />
        </Tab.Item>
        <Tab.Item key="alarm-notify" title={intl('saenext.micro-app.section.NotificationPolicy')}>
          <ArmsIframe
            regionId={regionId}
            url={`${CachedData.confLink(
              'feature:arms:url',
            )}/index?edasiframeMode=sae&regionId=${regionId}#/alarm/dispatch`}
          />
        </Tab.Item>
      </Tab>
    </Page>
  );
};

export default AlarmPage;
