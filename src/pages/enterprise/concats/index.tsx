import { intl } from '@ali/cnd';
import React from 'react';
import { Page } from '@ali/cnd';
import ConcatsTable from '~/components/enterprise/concats';

const Concats = () => {
  return (
    <Page
      key="concats"
      title={intl("saenext.enterprise.concats.ContactManagement")}
      breadcrumbs={[
      {
        to: '/',
        text: intl('title.home')
      },
      {
        text: intl("saenext.enterprise.concats.ContactManagement")
      }]}
      // @ts-ignore
      className="custom-page-layout"
      >


      <ConcatsTable />
    </Page>);

};
export default Concats;
