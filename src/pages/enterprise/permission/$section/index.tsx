import React from 'react';
import { Page, intl } from '@ali/cnd';
import { ROUTES } from '../constant';
import ApprovalSetting from '~/components/enterprise/approval-setting';
import ApprovalRecords from '~/components/enterprise/approval-records';
import PermissionAssistant from '~/components/enterprise/permission-assistant';

const PermissionPage = props => {
  const {
    match: {
      params: { section },
    },
    history,
  } = props;

  return (
    <Page
      title={ROUTES.find(o => o.key === section)?.title}
      hasBackArrow
      historyBack="/overview"
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: ROUTES.find(o => o.key === section)?.title,
        },
      ]}
      nav={{
        shape: 'menu',
        defaultActiveKey: ROUTES[0].key,
        activeKey: section,
        items: ROUTES,
        onChange: value => {
          history.push(`/enterprise/permission/${value}`);
        },
      }}
      // @ts-ignore
      className="custom-page-layout"
    >
      {section === 'permission-assistant' &&
        <PermissionAssistant
        />
      }
      {section === 'approval-setting' && <ApprovalSetting />}
      {section === 'approval-records' && <ApprovalRecords />}
    </Page>
  );
};

export default PermissionPage;
