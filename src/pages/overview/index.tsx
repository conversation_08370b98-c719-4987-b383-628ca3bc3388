import { intl } from '@ali/cnd';
import React from 'react';
import { Page } from '@ali/cnd';
import Overview from '~/components/overview'

const OverviewPage = () => {
  return (
    <Page
      key="overview"
      title=""
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.src.sidebar.OverviewPage'),
        },
      ]}
      // @ts-ignore
      className="overview-page-layout"
    >
      <Overview />
    </Page>
  );
};
export default OverviewPage;
