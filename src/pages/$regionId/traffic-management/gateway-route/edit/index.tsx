import React from 'react';
import { Page, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import RouteCreator from '~/components/namespace/route-create';

export default (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;

  const namespaceId = getParams('namespaceId');
  const route = getParams('route');
  const [ingressId, networkType, loadBalance] = route.split('-');

  const okCallback = () => {
    history.push(`/${regionId}/traffic-management/gateway-route/list`);
  };

  const cancelCallback = () => {
    history.push(`/${regionId}/traffic-management/gateway-route/list`);
  };

  return (
    <Page
      title={intl('saenext.route.edit.EditRoute')}
      historyBack={`/${regionId}/traffic-management/gateway-route/list`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/traffic-management/gateway-route/list`,
          text: intl('saenext.gateway-route.edit.GatewayRouting'),
        },
        {
          text: intl('saenext.gateway-route.edit.EditRoute'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <RouteCreator
        {...props}
        regionId={regionId}
        ingressId={ingressId}
        loadBalance={loadBalance}
        networkType={networkType}
        namespaceId={namespaceId}
        okCallback={okCallback}
        cancelCallback={cancelCallback}
      />
    </Page>
  );
};
