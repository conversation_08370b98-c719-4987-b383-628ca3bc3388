import React from 'react';
import { Page, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import RouteDetail from '~/components/namespace/route-detail';

export default (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;

  const namespaceId = getParams('namespaceId');

  return (
    <RouteDetail
      fromPage="traffic-management"
      {...props}
      regionId={regionId}
      namespaceId={namespaceId}
      handleEdit={(route) => {
        history.push(
          `/${regionId}/traffic-management/gateway-route/edit?namespaceId=${namespaceId}&route=${route}`,
        );
      }}
      routeListUrl={`/${regionId}/traffic-management/gateway-route/list?namespaceId=${namespaceId}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/traffic-management/gateway-route/list`,
          text: intl('saenext.gateway-route.detail.GatewayRouting'),
        },
        {
          text: intl('saenext.gateway-route.detail.RouteDetails'),
        },
      ]}
    />
  );
};
