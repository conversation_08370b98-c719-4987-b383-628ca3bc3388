import React from 'react';
import { Page, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import RouteCreator from '~/components/namespace/route-create';

export default (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;

  const namespaceId = getParams('namespaceId');
  const okCallback = () => {
    history.push(`/${regionId}/traffic-management/gateway-route/list`);
  };

  const cancelCallback = () => {
    history.push(`/${regionId}/traffic-management/gateway-route/list`);
  };

  return (
    <Page
      title={intl('saenext.route.create.CreateARoute')}
      historyBack={`/${regionId}/traffic-management/gateway-route/list`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/traffic-management/gateway-route/list`,
          text: intl('saenext.gateway-route.create.GatewayRouting'),
        },
        {
          text: intl('saenext.gateway-route.create.CreateARoute'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <RouteCreator
        {...props}
        regionId={regionId}
        namespaceId={namespaceId}
        okCallback={okCallback}
        cancelCallback={cancelCallback}
        // 区分sae1.0和sae2.0 --- apig仅2.0 支持
        isNewSae={true}
      />
    </Page>
  );
};
