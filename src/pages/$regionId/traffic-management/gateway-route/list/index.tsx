import React, { useState, useEffect, useContext } from 'react';
import { intl, Page } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';
import CachedData from '~/cache/common';
import FeatureContext from '~/utils/featureContext';
import GatewayRoute from '~/components/namespace/gateway';

const RouteList = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;
  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : regionId;
  const { feature } = useContext(FeatureContext);

  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);
  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
  };

  const handleEditRoute = (record) => {
    let slbType = record.SlbType;
    slbType = slbType?.toLowerCase() || '';
    history.push(
      `/${regionId}/traffic-management/gateway-route/edit?namespaceId=${namespaceId}&route=${record.Id}-${slbType}-${record.LoadBalanceType}`,
    );
  };

  const handleCreateRoute = () => {
    history.push(`/${regionId}/traffic-management/gateway-route/create?namespaceId=${namespaceId}`);
  };

  const handleRedirectToEvent = (record) => {
    if (record?.LoadBalanceType === 'mse') {
      window.open(
        `${CachedData.confLink('feature:mse:url')}/#/eventCenter?filterContent=${
          record?.MseGatewayId
        }&filterResourceType=GATEWAY`,
      );
    } else {
      const search = location.search;
      const _search = `${search}&ObjectKind=Ingress&ObjectName=${record.Name}`;
      history.push(`/${regionId}/namespace/${namespaceId}/event${_search}`);
    }
  };
  const handleRouteDetail = (record) => {
    let slbType = record.SlbType;
    slbType = slbType?.toLowerCase() || '';
    history.push(
      `/${regionId}/traffic-management/gateway-route/detail?namespaceId=${namespaceId}&regionId=${regionId}&&route=${record.Id}-${slbType}-${record.LoadBalanceType}&routeName=${record?.Description}`,
    );
  };
  return (
    <Page
      key={regionId}
      title={intl('saenext.gateway-route.list.GatewayRouting')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
          disabledAll={true}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.gateway-route.list.GatewayRouting'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <GatewayRoute
        {...props}
        regionId={regionId}
        namespaceId={namespaceId}
        featureConfig={feature}
        handleEditRoute={handleEditRoute}
        handleCreateRoute={handleCreateRoute}
        handleRedirectToEvent={handleRedirectToEvent}
        handleRouteDetail={handleRouteDetail}
        // 区分sae1.0和sae2.0 --- apig仅2.0 支持
        isNewSae={true}
      />
    </Page>
  );
};

export default RouteList;
