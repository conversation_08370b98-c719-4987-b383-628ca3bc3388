import React, { useState, useEffect, useRef } from 'react';
import { intl, Page, Tab, Message } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';
import ServiceList from '~/components/service-list';
import Grayscale from '~/components/grayscale';
import { forEach } from 'lodash';

const MscPage = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;
  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : regionId;

  const [activeKey, setActiveKey] = useState('service-list');
  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [refreshIndex, setRefreshIndex] = useState(0);
  // 包含命名空间的详细信息
  const [namespace, setNamespace] = useState({});
  const namespaceRef = useRef(new Map());

  useEffect(() => {
    console.log('------', getParams('tab'));
    getParams('tab') && setActiveKey(getParams('tab'));
  }, [getParams('tab')]);

  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
    setNamespaceId(val);
    const _namespace = namespaceRef.current.get(val);
    setNamespace(_namespace);
    setRefreshIndex(Date.now());
  };

  const setNamespaceData = (namespaceData) => {
    forEach(namespaceData, (namespace) => {
      const { NamespaceId } = namespace;
      namespaceRef.current.set(NamespaceId, namespace);
    });
    const _namespace = namespaceRef.current.get(namespaceId);
    setNamespace(_namespace);
  };
  return (
    <Page
      key={regionId}
      title={intl('saenext.traffic-management.msc.MicroserviceGovernance')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
          disabledAll={true}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.traffic-management.msc.MicroserviceGovernance'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      {/* 微服务治理 */}
      <Tab
        activeKey={activeKey}
        onChange={(key: string) => {
          setActiveKey(key);
        }}
        shape="wrapped"
        unmountInactiveTabs
      >
        <Tab.Item key="service-list" title={intl('saenext.traffic-management.msc.ServiceList')}>
          <div className="mt-s">
            <ServiceList namespaceId={namespaceId} regionId={regionId} />
          </div>
        </Tab.Item>
        <Tab.Item key="grayscale" title={intl('saenext.traffic-management.msc.FullLinkGray')}>
          <div className="mt-s">
            <Message type="notice" className="mb-s">
              {intl('saenext.regionId.grayscale.TheFullLinkGrayScale')}
            </Message>
            <Grayscale
              {...props}
              key={refreshIndex}
              regionId={regionId}
              // @ts-ignore
              namespace={namespace}
            />
          </div>
        </Tab.Item>
      </Tab>
    </Page>
  );
};

export default MscPage;
