import React from 'react';
import { Page, intl, Copy } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import TrafficPreview from '~/components/grayscale/TrafficPreview';

export default (props) => {
  const {
    match: {
      params: { regionId },
    },
  } = props;
  const groupId = getParams('groupId');
  const namespaceId = getParams('namespaceId');

  return (
    <Page
      title={
        <Copy text={intl('saenext.grayscale.overview.TrafficDetails')}>
          {intl('saenext.grayscale.overview.TrafficDetails')}
        </Copy>
      }
      historyBack={`/${regionId}/traffic-management/msc?namespaceId=${namespaceId}&tab=grayscale`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/traffic-management/msc?namespaceId=${namespaceId}&tab=grayscale`,
          text: intl('saenext.grayscale.overview.FullLinkGray'),
        },
        {
          text: intl('saenext.grayscale.overview.TrafficDetails'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <TrafficPreview regionId={regionId} namespaceId={namespaceId} groupId={Number(groupId)} />
    </Page>
  );
};
