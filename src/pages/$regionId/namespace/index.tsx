import React, { useEffect, useContext } from 'react';
import { intl, Page, ConsoleContext } from '@ali/cnd';
import CachedData from '~/cache/common';
import { inDebtHandle } from '~/utils/accountHandle';
import NamespaceList from '~/components/namespace/List';

const ListPage = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;
  const { region } = useContext(ConsoleContext);
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const inDebtOverdue = CachedData.getOpenStatus('inDebtOverdueStatus');

  useEffect(() => {
    // 欠费 但未超期
    isInDebt && !inDebtOverdue && inDebtHandle();
  }, [isInDebt]);

  const handleDetail = (record) => {
    const _id = record.NamespaceId || record.RegionId;
    history.push(`/${regionId}/namespace/${_id}/base?name=${record.NamespaceName}`);
  };

  return (
    <Page
      key={region.getCurrentRegionId()}
      title={intl('saenext.regionId.namespace.Namespace')}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.regionId.namespace.Namespace'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <NamespaceList regionId={regionId} handleDetail={handleDetail} />
    </Page>
  );
};

export default ListPage;
