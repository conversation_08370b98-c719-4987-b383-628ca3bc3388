import { Page, intl } from '@ali/cnd';
import React, { useContext } from 'react';
import AcmDetail from '~/components/acm/AcmDetail';
import _ from 'lodash';
import { ROUTES } from '../../$section/constant';
import { getParams } from '~/utils/global';
import FeatureContext from '~/utils/featureContext';

const formatNamespaceId = (value: string) => {
  const [, namespaceId] = value.split(':');
  if (_.isEmpty(namespaceId)) return intl('saenext.section.acm-detail.NamespaceDetails');
  return namespaceId;
};

const AcmDetailPage = (props) => {
  const {
    match: {
      params: { regionId, id: namespaceId },
    },
    history,
    location: { search },
  } = props;

  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  const title = formatNamespaceId(namespaceId);
  const namespaceName = getParams('name') || title || '';

  return (
    <Page
      title={intl('saenext.section.acm-detail.ConfigurationDetails')}
      historyBack={`/${regionId}/namespace/${namespaceId}/acm-list`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/namespace`,
          text: intl('saenext.section.acm-detail.Namespace'),
        },
        {
          to: `/${regionId}/namespace/${namespaceId}/base${search}`,
          text: `${namespaceName}`,
        },
        {
          to: `/${regionId}/namespace/${namespaceId}/acm-list`,
          text: intl('saenext.section.acm-detail.ConfigurationList'),
        },
        {
          text: intl('saenext.section.acm-detail.ConfigurationDetails'),
        },
      ]}
      nav={{
        shape: 'menu',
        defaultActiveKey: 'acm-list',
        // @ts-ignore
        onChange: (value) => {
          history.push(`/${regionId}/namespace/${namespaceId}/${value}?${search}`);
        },
        items: _.filter(ROUTES, (item) => item.checkVisible ? item.checkVisible({ EnableWebApplication }) : true),
      }}
      // @ts-ignore
      className="custom-page-layout"
    >
      <AcmDetail
        component="ConfigurationDetail"
        regionId={regionId}
        namespaceId={namespaceId}
        history={history}
      />
    </Page>
  );
};

export default AcmDetailPage;
