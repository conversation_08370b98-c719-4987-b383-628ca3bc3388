import React from 'react';
import { Page, intl, Copy } from '@ali/cnd';
import { getParams } from '~/utils/global';
import RouteCreator from '~/components/namespace/route-create';

export default (props) => {
  const {
    match: {
      params: { regionId, id: namespaceId },
    },
    history,
  } = props;

  const namespaceName = getParams('name');
  const route = getParams('route');
  const [ingressId, networkType, loadBalance] = route.split('-');

  const okCallback = () => {
    history.push(`/${regionId}/namespace/${namespaceId}/gateway?name=${namespaceName}`);
  };

  const cancelCallback = () => {
    history.push(`/${regionId}/namespace/${namespaceId}/gateway?name=${namespaceName}`);
  };

  return (
    <Page
      title={
        <Copy text={intl('saenext.route.edit.EditRoute')}>
          {intl('saenext.route.edit.EditRoute')}
        </Copy>
      }
      historyBack={`/${regionId}/namespace/${namespaceId}/gateway?name=${namespaceName}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/namespace`,
          text: intl('saenext.route.edit.Namespace'),
        },
        {
          to: `/${regionId}/namespace/${namespaceId}/base?name=${namespaceName}`,
          text: `${namespaceName}`,
        },
        {
          to: `/${regionId}/namespace/${namespaceId}/gateway?name=${namespaceName}`,
          text: intl('saenext.route.edit.GatewayRouting'),
        },
        {
          text: intl('saenext.route.edit.EditRoute'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <RouteCreator
        {...props}
        regionId={regionId}
        ingressId={ingressId}
        loadBalance={loadBalance}
        networkType={networkType}
        namespaceId={namespaceId}
        okCallback={okCallback}
        cancelCallback={cancelCallback}
      />
    </Page>
  );
};
