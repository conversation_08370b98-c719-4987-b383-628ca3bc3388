import React from 'react';
import { Page, intl, Copy } from '@ali/cnd';
import { getParams } from '~/utils/global';
import RouteCreator from '~/components/namespace/route-create';

export default (props) => {
  const {
    match: {
      params: { regionId, id: namespaceId },
    },
    history,
    location: { search },
  } = props;

  const namespaceName = getParams('name');

  const okCallback = () => {
    history.push(`/${regionId}/namespace/${namespaceId}/gateway?name=${namespaceName}`);
  };

  const cancelCallback = () => {
    history.push(`/${regionId}/namespace/${namespaceId}/gateway?name=${namespaceName}`);
  };

  return (
    <Page
      title={
        <Copy text={intl('saenext.route.create.CreateARoute')}>
          {intl('saenext.route.create.CreateARoute')}
        </Copy>
      }
      historyBack={`/${regionId}/namespace/${namespaceId}/gateway${search}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/namespace`,
          text: intl('saenext.route.create.Namespace'),
        },
        {
          to: `/${regionId}/namespace/${namespaceId}/base${search}`,
          text: `${namespaceName}`,
        },
        {
          to: `/${regionId}/namespace/${namespaceId}/gateway${search}`,
          text: intl('saenext.route.create.GatewayRouting'),
        },
        {
          text: intl('saenext.route.create.CreateARoute'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <RouteCreator
        {...props}
        regionId={regionId}
        namespaceId={namespaceId}
        okCallback={okCallback}
        cancelCallback={cancelCallback}
        // 区分sae1.0和sae2.0 --- apig仅2.0 支持
        isNewSae={true}
      />
    </Page>
  );
};
