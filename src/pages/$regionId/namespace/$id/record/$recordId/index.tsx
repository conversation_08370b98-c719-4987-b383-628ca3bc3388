/* eslint react/prop-types: 0 */
import React from 'react';
import { Copy, Page, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import ChangeRecord from '~/components/namespace/record';

const RevisionDetail = (props) => {
  const {
    match: {
      params: { regionId, recordId, id: namespaceId },
    },
    location: { search }, // ?后参数
  } = props;
  const namespaceName = getParams('name');

  return (
    <Page
      title={<Copy text={recordId}>{recordId}</Copy>}
      historyBack={`/${regionId}/namespace/${namespaceId}/revision${search}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/namespace`,
          text: intl('saenext.record.$recordId.Namespace'),
        },
        {
          to: `/${regionId}/namespace/${namespaceId}/base${search}`,
          text: `${namespaceName}`,
        },
        {
          to: `/${regionId}/namespace/${namespaceId}/revision${search}`,
          text: intl('saenext.record.$recordId.ChangeRecord'),
        },
        {
          text: intl('saenext.record.$recordId.ChangeDetails'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <ChangeRecord changeOrderId={recordId} />
    </Page>
  );
};

export default RevisionDetail;
