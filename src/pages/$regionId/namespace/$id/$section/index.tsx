/* eslint react/prop-types: 0 */
import React, { useEffect, Suspense, useContext, useState } from 'react';
import { Page, Copy, intl } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import LoadingSkeleton from '~/components/shared/LoadingSkeleton';
import _ from 'lodash';
import { ROUTES } from './constant';
import FeatureContext from '~/utils/featureContext';
import CachedData from '~/cache/common';
import { inDebtHandle } from '~/utils/accountHandle';
import ConfigMap from '~/components/namespace/config-map';
import SecretList from '~/components/namespace/secret';
import EventList from '~/components/shared/EventList';
import Crontab from '~/components/namespace/crontab';
import Domain from '~/components/namespace/domain';
import AcmList from '~/components/acm/AcmList';
import AcmHistory from '~/components/acm/AcmHistory';
import AcmMonitor from '~/components/acm/AcmMonitor';
import AcmTrail from '~/components/acm/AcmTrail';
import Revision from '~/components/namespace/revision';
import BaseInfo from './components/BaseInfo'
import services from '~/services';
import NsNonitor from '~/components/namespace/monitor';
import RouteDetail from '~/components/namespace/route-detail';
import GatewayRoute from '~/components/namespace/gateway';

const NamespaceDetail = (props) => {
  const {
    match: {
      params: { regionId, id: namespaceId, section },
    },
    history,
    location: { search }, // ?后参数
  } = props;

  const [namespaceInfo, setNamespaceInfo] = useState<any>({});
  const [breadcrumbs,setBreadcrumbs] = useState([]);

  const namespaceName = getParams('name') || namespaceInfo.NamespaceName || '';

  const { feature, webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const inDebtOverdue = CachedData.getOpenStatus('inDebtOverdueStatus');


  useEffect(() => {
    // 欠费 但未超期
    isInDebt && !inDebtOverdue && inDebtHandle();
    fetchNamespaceInfo();
  }, [isInDebt]);

  useEffect(()=>{
    let _breadcrumbs = [
      {
        to: '/',
        text: intl('title.home'),
      },
      {
        to: `/${regionId}/namespace${search}`,
        text: intl('saenext.id.section.Namespace'),
      },
      {
        to: `/${regionId}/namespace/${namespaceId}/base${search}`,
        text: `${namespaceName}`,
      },
      {
        text: `${findBreadcrumb()}`,
      },
    ]
    if (section === 'route-detail' && getParams('routeName')) {
      _breadcrumbs.splice(3, 0, {
        to: `/${regionId}/namespace/${namespaceId}/gateway?name=${namespaceName}`,
        text: `${getParams('routeName')}`,
      });
    }
    setBreadcrumbs(_breadcrumbs);
  },[section, namespaceName])

  const fetchNamespaceInfo = async () => {
    const data = await services.getNamespaceDescribeV2({
      NamespaceId: namespaceId,
    });
    const { Data: baseInfo } = data || {};
    setSearchParams({ 'name': baseInfo.NamespaceName });
    setNamespaceInfo(baseInfo);
  }

  const findBreadcrumb = (items = ROUTES) => {
    const item = _.find(items, (o) => o.key === section);
    if (item) {
      return item.title;
    } else {
      const itemsParent = _.find(items, (o) => section?.startsWith(o.key)) as any;
      // 添加终止条件：如果没有找到父项目或者父项目没有子项目，返回默认值
      return findBreadcrumb(itemsParent.items);
    }
  };

  const handleCreateRoute = () => {
    const search = location.search;
    history.push(
      `/${regionId}/namespace/${namespaceId}/route/create${search}`,
    );
  };

  const handleEditRoute = (record) => {
    const search = location.search;
    let slbType = record.SlbType;
    slbType = slbType?.toLowerCase() || '';
    const _search = `${search}&route=${record.Id}-${slbType}-${record.LoadBalanceType}`;
    history.push(
      `/${regionId}/namespace/${namespaceId}/route/edit${_search}`,
    );
  };

  const handleRouteDetail = (record)=>{
    const search = location.search;
    let slbType = record.SlbType;
    slbType = slbType?.toLowerCase() || '';
    const _search = `${search}&route=${record.Id}-${slbType}-${record.LoadBalanceType}&routeName=${record?.Description}`;
    history.push(
      `/${regionId}/namespace/${namespaceId}/route-detail${_search}`,
    );
  }

  const handleRedirectToEvent = record => {
    if (record?.LoadBalanceType === 'mse') {
      window.open(
        `${CachedData.confLink('feature:mse:url')}/#/eventCenter?filterContent=${record?.MseGatewayId}&filterResourceType=GATEWAY`,
      );
    } else {
      const search = location.search;
      const _search = `${search}&ObjectKind=Ingress&ObjectName=${record.Name}`;
      history.push(`/${regionId}/namespace/${namespaceId}/event${_search}`);
    }
  };

  return (
    <Page
      title={<Copy text={namespaceName}>{namespaceName}</Copy>}
      historyBack={`/${regionId}/namespace`}
      breadcrumbs={breadcrumbs}
      nav={{
        shape: 'menu',
        defaultActiveKey: 'base',
        activeKey: section === 'route-detail' ? 'gateway' : section,
        // @ts-ignore
        onChange: value => {
          history.push(`/${regionId}/namespace/${namespaceId}/${value}?name=${namespaceName}`);
          isInDebt && !inDebtOverdue && inDebtHandle();
        },
        items: _.filter(ROUTES, item =>
          item.checkVisible ? item.checkVisible({ EnableWebApplication }) : true,
        ),
      }}
      // @ts-ignore
      className="custom-page-layout"
    >
      <Suspense fallback={<LoadingSkeleton />}>
        {(!section || section === 'base') && (
          <BaseInfo
            regionId={regionId}
            namespaceId={namespaceId}
            EnableWebApplication={EnableWebApplication}
          />
        )}

        {/* {section === 'gateway' && (
          <GatewayRoute
            {...props}
            regionId={regionId}
            namespaceId={namespaceId}
            featureConfig={feature}
            handleEditRoute={handleEditRoute}
            handleCreateRoute={handleCreateRoute}
            handleRedirectToEvent={handleRedirectToEvent}
            handleRouteDetail={handleRouteDetail}
            // 区分sae1.0和sae2.0 --- apig仅2.0 支持
            isNewSae={true}
          />
        )} */}

        {/* {section === 'config' && (
          <ConfigMap
            regionId={regionId}
            namespaceId={namespaceId}
          />
        )} */}

        {/* {section === 'secret' && (
          <SecretList
            regionId={regionId}
            namespaceId={namespaceId}
          />
        )} */}
        {/* {section === 'route-detail' && (
          <RouteDetail
            {...props}
            regionId={regionId}
            namespaceId={namespaceId}
            namespaceName={namespaceName}
            handleEdit={route => {
              history.push(
                `/${regionId}/namespace/${namespaceId}/route/edit?name=${namespaceId}&route=${route}`,
              );
            }}
            routeListUrl={`/${regionId}/namespace/${namespaceId}/gateway?name=${namespaceName}`}
          />
        )} */}

        {section === 'event' && (
          <EventList
            regionId={regionId}
            namespaceId={namespaceId}
          />
        )}

        {(section === 'domain') && (
          <Domain
            regionId={regionId}
            namespaceId={namespaceId}
          />
        )}
        {(section === 'revision') &&
          <Revision
            {...props}
            regionId={regionId}
            namespaceId={namespaceId}
          />
        }

        {/* {(section === 'monitor') &&
          <NsNonitor
            {...props}
            regionId={regionId}
            namespaceId={namespaceId}
          />
        } */}

        {section === 'crontab' &&
          <Crontab
            {...props}
            regionId={regionId}
            namespaceId={namespaceId}
          />
        }

        {/* {(section === 'acm-list') && (
          <AcmList
            regionId={regionId}
            namespaceId={namespaceId}
            history={history}
          />
        )}

        {(section === 'acm-history') && (
          <AcmHistory
            regionId={regionId}
            namespaceId={namespaceId}
            history={history}
          />
        )}

        {(section === 'acm-monitor') && (
          <AcmMonitor
            regionId={regionId}
            namespaceId={namespaceId}
            history={history}
          />
        )}

        {(section === 'acm-trail') && (
          <AcmTrail
            regionId={regionId}
            namespaceId={namespaceId}
            history={history}
          />
        )} */}
      </Suspense>
    </Page>
  );
};

export default NamespaceDetail;
