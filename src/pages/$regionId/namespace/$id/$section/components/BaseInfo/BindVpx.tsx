import React from 'react';
import services from '~/services';
import _ from 'lodash';
import { intl, Message, Select, SlidePanel, Form, Field, Input } from '@ali/cnd';
import TextRefreshButton from '~/components/shared/TextRefreshButton';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/cache/common';

type Props = {
  regionId: string;
  vpcId: string;
  namespaceId: string;
  namespaceName: string;
  isShowing: boolean;
  isExistApp: boolean;
  onToggleVpcDialog: () => void;
};

type State = {
  loading: boolean;
  vpcList: { label: any; value: string; disabled?: boolean }[];
  vpcLoading: boolean;
};

class FromSlidePanel extends React.Component<Props, State> {
  field = new Field(this);
  constructor(props) {
    super(props);
    this.state = {
      vpcList: [],
      loading: false,
      vpcLoading: false,
    };
  }

  componentDidMount() {
    this.getVpcList();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.vpcId !== this.props.vpcId) {
      this.field.setValue('VpcId', this.props.vpcId);
    }
  }

  getVpcList = async () => {
    this.setState({
      vpcLoading: true,
    });
    const res = await services.getBelongVpcs({
      Force: true,
      RegionId: this.props.regionId,
    });
    const { Data = {} } = res;
    // Data.Vpcs
    const _vpcList = _.map(Data.Vpcs, ({ VpcName, VpcId }) => ({
      label: (
        <>
          <span className="mr-l">{VpcName || VpcId}</span>
          <span className="text-description">{VpcId}</span>
        </>
      ),

      value: VpcId,
      disabled: VpcId === this.props.vpcId,
    }));
    this.setState({
      vpcList: _vpcList,
      vpcLoading: false,
    });
  };

  handleUpdateVpc = () => {
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      }
      const { namespaceId, regionId } = this.props;
      // @ts-ignore
      const VpcId = this.field.getValue('VpcId');

      this.setState({ loading: true });
      services
        .updateNamespaceVpc({
          VpcId: VpcId as string,
          NamespaceId: namespaceId,
          // @ts-ignore
          RegionId: regionId,
        })
        .then((res) => {
          Message.success(
            intl('saenext.components.nspace-detail.FromSlidePanel.VpcBindingSucceeded'),
          );
          this.setState({ loading: false });
          const { onToggleVpcDialog } = this.props;
          onToggleVpcDialog();
        })
        .catch(() => {
          this.setState({ loading: false });
        });
    });
  };

  render() {
    const { vpcList, loading, vpcLoading } = this.state;
    const { isShowing, isExistApp = true, regionId, onToggleVpcDialog } = this.props;
    const { init } = this.field;
    return (
      <>
        <SlidePanel
          title={intl('saenext.components.nspace-detail.FromSlidePanel.BindVpc')}
          isShowing={isShowing}
          width={700}
          isProcessing={loading}
          onClose={onToggleVpcDialog}
          onCancel={onToggleVpcDialog}
          onOk={isExistApp ? onToggleVpcDialog : this.handleUpdateVpc}
        >
          <Form field={this.field}>
            {isExistApp ? (
              <Message type="warning" className="mb-l">
                {intl(
                  'saenext.components.nspace-detail.FromSlidePanel.ApplicationsStillExistInThe',
                )}
              </Message>
            ) : null}

            <Form.Item
              required
              label={intl('saenext.components.nspace-detail.FromSlidePanel.Namespace')}
            >
              <Input readOnly disabled={isExistApp} value={this.props.namespaceName} />
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.components.nspace-detail.FromSlidePanel.Vpc')}
            >
              <Select
                {...init('VpcId', {
                  rules: [
                    {
                      required: true,
                      message: intl('saenext.components.nspace-detail.FromSlidePanel.SelectVpc'),
                    },
                  ],
                })}
                disabled={isExistApp}
                dataSource={vpcList}
                state={vpcLoading ? 'loading' : null}
                style={{ width: '100%' }}
              />

              <div className="flex">
                <TextRefreshButton onClick={() => this.getVpcList()} />
                <ExternalLink
                  className="ml-l"
                  label={intl('saenext.components.nspace-detail.FromSlidePanel.CreateAVpc')}
                  // @ts-ignore
                  url={`${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/vpcs`}
                />
              </div>
            </Form.Item>
          </Form>
        </SlidePanel>
      </>
    );
  }
}

export default FromSlidePanel;
