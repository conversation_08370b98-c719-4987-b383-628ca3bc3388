import { intl } from '@ali/cnd';
export const ROUTES = [
  {
    key: 'base',
    title: intl('saenext.id.section.constant.BasicInformation'),
  },
  {
    key: 'revision',
    title: intl('saenext.id.section.constant.ChangeRecord'),
  },
  {
    key: 'event',
    title: intl('saenext.id.section.constant.EventInformation'),
  },
  // {
  //   key: 'gateway',
  //   title: intl('saenext.id.section.constant.GatewayRouting'),
  // },
  // {
  //   key: 'route-detail',
  //   title: intl('saenext.id.section.constant.RouteDetails'),
  //   visible: false,
  // },
  {
    key: 'domain',
    title: intl('saenext.id.section.constant.DomainNameManagement'),
    checkVisible: ({ EnableWebApplication }) => EnableWebApplication,
  },
  // {
  //   key: 'config',
  //   title: intl('saenext.id.section.constant.ConfigMap'),
  // },
  // {
  //   key: 'secret',
  //   title: intl('saenext.id.section.constant.ConfidentialDictionary'),
  // },
  {
    key: 'crontab',
    title: intl('saenext.id.section.constant.ScheduledStartAndStopRules'),
  },
  // {
  //   key: 'monitor',
  //   title: intl('saenext.id.section.constant.BasicMonitoring'),
  // },
  // {
  //   title: intl('saenext.id.section.constant.DistributedConfigurationManagement'),
  //   key: 'acm',
  //   items: [
  //     {
  //       key: 'acm-list',
  //       title: intl('saenext.id.section.constant.ConfigurationList'),
  //     },
  //     {
  //       key: 'acm-history',
  //       title: intl('saenext.id.section.constant.VersionHistory'),
  //     },
  //     {
  //       key: 'acm-monitor',
  //       title: intl('saenext.id.section.constant.ListenerQuery'),
  //     },
  //     {
  //       key: 'acm-trail',
  //       title: intl('saenext.id.section.constant.PushTrack'),
  //     },
  //   ],
  // },
];
