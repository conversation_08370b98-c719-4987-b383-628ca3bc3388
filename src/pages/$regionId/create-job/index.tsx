import React, { useEffect, useRef, useState } from 'react';
import { intl, Page, Loading } from '@ali/cnd';
import { getParams } from '~/utils/global';
import CachedData from '~/cache/common';
import { useHistory } from 'react-router-dom';

const ConsoleNavHeight = 50;
const BreadcrumbHeight = 52;
const PageNavHeight = 52;
const ReservedSpaceHeight = 16;

const isPre = CachedData.isPre();

const CreateJob = (props) => {
  const {
    match: {
      params: { regionId },
    },
  } = props;
  const history = useHistory();
  const namespaceId = getParams('namespaceId') || '';
  const appId = getParams('appId') || '';
  const iframeRef = useRef(null);
  const [loading, setLoading] = useState(false);
  
  const iframeHeight =
    document.documentElement.clientHeight -
    ConsoleNavHeight -
    BreadcrumbHeight -
    PageNavHeight -
    ReservedSpaceHeight;

  useEffect(() => {
    setLoading(true);
  }, []);

  useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [history]);

  const iframeLoaded = () => {
    setTimeout(() => {
      setLoading(false);
    });
  };

  // 处理来自 iframe 的消息
  const handleMessage = (event: MessageEvent) => {
    const isPre = window.location.host?.startsWith('pre-');
    const origin = isPre
      ? CachedData.confLink('feature:pre-sae:url')
      : CachedData.confLink('feature:sae:url');
    if (event.origin !== origin) {
      return;
    }
    if (event.data === 'cancel') {
      history.push(`/${regionId}/job-list`);
    }
    if (event?.data?.action === 'toJobDetail') {
      const { jobInfo } = event?.data;
      if(jobInfo?.id){
        history.push(`/${regionId}/job-list/${jobInfo.id}/base`);
      }else{
        history.push(`/${regionId}/job-list`);
      }
    }
  };

  return (
    <Page
      title={
        appId
          ? intl('saenext.regionId.create-job.CopyTaskTemplate')
          : intl('saenext.components.job-list.CreateATaskTemplate')
      }
      // @ts-ignore
      className="custom-page-layout"
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/job-list`,
          text: intl('saenext.regionId.job-list.TaskTemplateList'),
        },
        {
          text: intl('saenext.components.job-list.CreateATaskTemplate'),
        },
      ]}
    >
      <Loading visible={loading} style={{ width: '100%', position: 'relative' }}>
        <iframe
          width="100%"
          height={iframeHeight}
          scrolling="no"
          frameBorder={0}
          onLoad={iframeLoaded}
          src={`${
            isPre
              ? CachedData.confLink('feature:pre-sae:url')
              : CachedData.confLink('feature:sae:url')
          }/#/JobList/CreateJob?regionId=${regionId}&namespaceId=${regionId}&${
            appId ? `appId=${appId}` : ''
          }&namespaceId=${namespaceId}&hideSidebar=true&iframeMode=true`}
          ref={iframeRef}
        />
      </Loading>
    </Page>
  );
};

export default CreateJob;
