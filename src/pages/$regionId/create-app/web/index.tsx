import React from 'react';
import { Page, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import WebAppCreator from '~/components/app-create/web-app';

export default (props) => {
  const {
    match: {
      params: { regionId },
    },
  } = props;
  const namespaceId = getParams('namespaceId') || '';

  return (
    <Page
      title={intl('saenext.create-app.web.CreateAnApplication')}
      // @ts-ignore
      className="create-app"
      historyBack={`/${regionId}/app-list/web?namespaceId=${namespaceId}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/web?namespaceId=${namespaceId}`,
          text: intl('saenext.create-app.web.WebApplications'),
        },
        {
          text: intl('saenext.create-app.web.CreateAWebApplication'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <WebAppCreator {...props} />
    </Page>
  );
};
