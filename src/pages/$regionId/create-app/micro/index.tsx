import React, {useContext} from 'react';
import { Page, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import MicroAppCreator from '~/components/app-create/micro-app';
import FeatureContext from '~/utils/featureContext';

export default (props) => {
  const {
    match: {
      params: { regionId },
    },
  } = props;
  const namespaceId = getParams('namespaceId') || '';
  const version = getParams('version') || '';
  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  return (
    <Page
      title={intl('saenext.create-app.micro.CreateAnApplication')}
      // @ts-ignore
      className="create-app custom-page-layout"
      historyBack={`/${regionId}/app-list/micro?namespaceId=${namespaceId}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/micro?namespaceId=${namespaceId}`,
          text: EnableWebApplication
          ? intl('saenext.app-list.micro.MicroserviceApplications')
          : intl('saenext.app-list.micro.ApplicationList'),
        },
        {
          text: EnableWebApplication
          ? intl('saenext.create-app.micro.CreateAMicroserviceApplication')
          : intl('saenext.app-list.micro-app.CreateAnApplication'),
        },
      ]}
    >
      <MicroAppCreator {...props} version={version} />
    </Page>
  );
};
