import React, { useContext } from 'react';
import { Page, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import FeatureContext from '~/utils/featureContext';
import CreateAppYaml from '~/components/app-create/micro-app/yaml';

export default (props) => {
  const {
    match: {
      params: { regionId },
    },
  } = props;
  const namespaceId = getParams('namespaceId') || '';
  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  return (
    <Page
      title={intl('saenext.micro.yaml.YamlCreation')}
      // @ts-ignore
      className="create-app custom-page-layout"
      historyBack={`/${regionId}/app-list/micro?namespaceId=${namespaceId}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/micro?namespaceId=${namespaceId}`,
          text: EnableWebApplication
            ? intl('saenext.app-list.micro.MicroserviceApplications')
            : intl('saenext.app-list.micro.ApplicationList'),
        },
        {
          text: intl('saenext.micro.yaml.YamlCreation'),
        },
      ]}
    >
      <CreateAppYaml {...props} />
    </Page>
  );
};
