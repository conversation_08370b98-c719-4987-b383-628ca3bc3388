import React, { useState, useEffect, useContext, useRef } from 'react';
import { intl, Page, ConsoleContext, Message } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import Grayscale from '~/components/grayscale';
// import NamespaceTag from '~/components/shared/NamespaceTag';
import { forEach } from 'lodash';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';

const GrayscalePage = (props) => {
  const {
    match: {
      params: { regionId },
    },
  } = props;


  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : regionId;

  const tagRef = useRef(null);
  // 默认命名空间id 和 区域id 一致
  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [refreshIndex, setRefreshIndex] = useState(0);
  // 包含命名空间的详细信息
  const [namespace, setNamespace] = useState({});
  const namespaceRef = useRef(new Map());

  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
    const _namespace = namespaceRef.current.get(val);
    setNamespace(_namespace);
    setRefreshIndex(Date.now());
  };

  const setNamespaceData = (namespaceData) => {
    forEach(namespaceData, (namespace) => {
      const { NamespaceId } = namespace;
      namespaceRef.current.set(NamespaceId, namespace);
    });
    const _namespace = namespaceRef.current.get(namespaceId);
    setNamespace(_namespace);
  };

  return (
    <Page
      key={`${regionId}-grayscale`}
      title={intl('saenext.regionId.grayscale.FullLinkGray')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
          disabledAll={true}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.regionId.grayscale.FullLinkGray'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <>
        <Message type="notice" className="mb-s">
          {intl('saenext.regionId.grayscale.TheFullLinkGrayScale')}
        </Message>
        {/* <NamespaceTag
           ref={tagRef}
           value={namespaceId}
           onChange={onNamespaceChange}
           setNamespaceData={setNamespaceData}
          /> */}
        <Grayscale
          {...props}
          key={refreshIndex}
          regionId={regionId}
          // @ts-ignore
          namespace={namespace}
        />
      </>
    </Page>
  );
};

export default GrayscalePage;
