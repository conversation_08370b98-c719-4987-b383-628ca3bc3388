import { intl } from '@ali/cnd';
import React, { Suspense } from 'react';
import { Page, Copy } from '@ali/cnd';
import { lazy } from '~/utils/retry';
import { getParams, unshiftZero } from '~/utils/global';
import LoadingSkeleton from '~/components/shared/LoadingSkeleton';
import InstanceSelect from '~/components/shared/InstanceSelect';

const Shell = lazy(() => import('~/components/shared/Shell'));
const ExternalLink = lazy(() => import('~/components/shared/ExternalLink'));

export default ({
  match: {
    params: { regionId, id: appId, version, instance },
  },
  history,
}) => {
  const applicationName = getParams('name');

  return (
    <Page
      title={
        <div className="flex">
          <span className="mr-s">{intl('saenext.version.instance.LogOnToTheInstance')}</span>
          <Copy text={instance}>{instance}</Copy>
        </div>
      }
      titleExtra={
        <Suspense fallback={null}>
          <span className="mr-s">{intl('saenext.version.instance.Application')}</span>
          <ExternalLink
            icon={false}
            label={applicationName}
            className="pointer color-primary"
            url={`/${regionId}/app-list/${appId}/web-app/base?name=${applicationName}`}
          />

          <span className="mr-s ml-l">{intl('saenext.version.instance.Version')}</span>
          <ExternalLink
            icon={false}
            label={unshiftZero(version)}
            className="pointer color-primary"
            url={`/${regionId}/app-list/${appId}/version/${version}/configure?name=${applicationName}`}
          />

          <InstanceSelect
            applicationID={appId}
            applicationName={applicationName}
            versionId={version}
            value={instance}
            onChange={(value) => {
              history.push(
                `/${regionId}/monitor/${appId}/${version}/${value}?name=${applicationName}`,
              );
            }}
            className="ml"
          />
        </Suspense>
      }
    >
      <Suspense fallback={<LoadingSkeleton showLoading />}>
        <Shell key={instance} applicationId={appId} versionId={version} instanceId={instance} />
      </Suspense>
    </Page>
  );
};
