import React from 'react';
import { intl, Page } from '@ali/cnd';
import PriceCalculatorComponent from '~/components/price-calculator';

const PriceCalculator = () => {

  return (
    <Page
      title={intl('saenext.regionId.price-calculator.PriceCalculator')}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.regionId.price-calculator.PriceCalculator'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <PriceCalculatorComponent />
    </Page>
  );
};

export default PriceCalculator;
