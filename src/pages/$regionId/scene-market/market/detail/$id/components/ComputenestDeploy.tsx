import React from 'react';
import { intl, Page } from '@ali/cnd';
import { createAlfaApp } from '@alicloud/alfa-react';
import CachedData from '~/cache/common';

const ComputenestDeploy = (props) => {
  const { regionId, serviceId, serviceName, history } = props;
  const isPreEnv = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';
  const isIntl = CachedData.isSinSite();

  const OreAlfaApp = createAlfaApp({
    name: '@ali/alfa-cloud-ros-app-ore-form',
    env: isPreEnv ? 'pre' : 'prod',
    locale: window.ALIYUN_CONSOLE_CONFIG.LOCALE || 'zh-CN',
    sandbox: {
      sandBoxUrl: 'about:blank',
      disableFakeBody: true,
      allowResources: ['https://g.alicdn.com/aliyun-ecs/monaco-editor/0.33.0/vs/loader.js'],
      externalsVars: ['goldlog', 'addEventListener', 'getUA', 'location', 'require'],
    },
  });
  const onCreateServiceInstanceSuccess = () => {
    history.push(`/${regionId}/scene-market?tab=deployed`);
  };
  return (
    <Page
      title={serviceName || intl('saenext.scene-market.computenestDeploy.common.title')}
      historyBack={`/${regionId}/scene-market?tab=market`}
    >
      <OreAlfaApp
        componentKey="ComputeNestForm" // 固定值
        source="ComputeNest" // 可选值：ALIYUNMarket、ComputeNest、OOS、ROS
        theme="buy" // buy 或 aliyun 默认值：aliyun
        computeNestRegionId={isIntl ? 'ap-southeast-1' : 'cn-hangzhou'} // 计算巢服务所属的地域，国内：cn-hangzhou、国际：ap-southeast-1
        serviceId={serviceId} // 计算巢服务的ID
        // serviceVersion={serviceVersion} // 可选，默认最新的版本
        onCreateServiceInstanceSuccess={onCreateServiceInstanceSuccess}
        // useFecs // 是否使用跨域接口，OneConsole下不需要传
        defaultTags={[{ key: 'application', value: 'sae' }]}
        // formProps={formProps}
      />
    </Page>
  );
};

export default ComputenestDeploy;
