import React, {useContext} from 'react';
import { ydIntl as intl, useHistory, ConsoleContext } from '@ali/cnd';
import { createAlfaApp } from '@ali/xconsole/alfa';
import ComputenestDeploy from './components/ComputenestDeploy';
import { getParams } from '~/utils/global';
import { startsWith } from 'lodash';

const Architect = createAlfaApp({
  name: '@ali/alfa-cloud-apigateway-widget-apig-architecture-widget',
  className: 'apig-architecture-widget',
});
export default ({
  match: {
    params: { id },
  },
}) => {
  const history = useHistory();
  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const tab = getParams('tab');

  return (
    <React.Fragment>
      {startsWith(id, 'service-') && tab === 'deploy' ? (
        <ComputenestDeploy
          regionId={regionId}
          serviceId={id}
          serviceName={getParams('name')}
          history={history}
        />
      ) : (
        <Architect
          breadcrumbs={[
            {
              text: intl('apigw.detail.id.CloudNativeApiGateway'),
            },
            {
              text: intl('apigw.detail.id.ScenarioTemplate'),
              to: `/${regionId}/scene-market`,
            },
            {
              text: id,
            },
          ]}
          appName="sceneDetail"
          sceneId={id}
          region={regionId}
          onSyncHistory={(path, state) => {
            history.push(path);
          }}
          syncHistory
          history={history}
          product="sae"
          regionList={(window.ALIYUN_CONSOLE_GLOBAL?.regionList || []).map(({ id, name }) => ({
            id,
            name: intl(name),
          }))}
        />
      )}
    </React.Fragment>
  );
};
