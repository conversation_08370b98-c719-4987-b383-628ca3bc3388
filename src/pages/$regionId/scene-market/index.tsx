import { intl, useHistory, ConsoleContext } from '@ali/cnd';
import { createAlfaApp } from '@ali/xconsole/alfa';
import React, { useContext } from 'react';
import get from 'lodash/get';

const AppCenter = createAlfaApp({
  name: '@ali/alfa-cloud-apigateway-widget-apig-architecture-widget',
  version: get(window, "ALIYUN_CONSOLE_GLOBAL.ALFA_APIG_SCENEMARKET", "latest"),
  className: 'apig-architecture-widget',
});

export default () => {
  const history = useHistory();
  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  return (
    <AppCenter
      onSyncHistory={(path, state) => {
        history.push(path);
      }}
      breadcrumbs={[
        {
          text: intl('saenext.regionId.scene-market.ServerlessApplicationEngine'),
        },
        {
          text: intl('saenext.regionId.scene-market.ApplicationCenter'),
        },
      ]}
      appName="sceneMarket"
      region={regionId}
      syncHistory
      history={history}
      product="sae"
    />
  );
};
