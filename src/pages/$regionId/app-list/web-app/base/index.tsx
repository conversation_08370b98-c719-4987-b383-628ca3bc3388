import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import { getParams } from '~/utils/global';
import services from '~/services';
import { Message } from '@ali/cnd';

const Info = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
    location: { search },
  } = props;

  useEffect(() => {
    getWebAppId();
  }, []);

  const getWebAppId = async () => {
    const namespaceID = getParams('namespaceId');
    const appName = getParams('name');

    const { applications = [] } =
      (await services.listWebApplications({
        prefix: appName,
        namespaceID,
        limit: 1,
      })) || {};

    if (applications.length > 0) {
      const { applicationId } = applications[0];
      history.push(`/${regionId}/app-list/${applicationId}/web-app/base/${search}`);
    } else {
      Message.error(intl('saenext.web-app.info.TheApplicationDoesNotExist'));
      history.push(`/`);
    }
  };

  return <></>;
};
export default Info;
