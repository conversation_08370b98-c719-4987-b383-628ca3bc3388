import React, { useContext, useState, useEffect } from 'react';
import { intl, Page, PageProps, Message, Tab } from '@ali/cnd';
import MicroApp, { MicroListKey } from '~/components/app-list/micro-app';
import { getParams, setSearchParams } from '~/utils/global';
import _ from 'lodash';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';
import FeatureContext from '~/utils/featureContext';
import If from '~/components/shared/If';

const ListPage = (props: PageProps) => {
  const {
    match: {
      params: { regionId },
    },
  } = props;

  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : 'all';

  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);

  const { webFeature, feature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;
  const { enableStateful = false } = feature;

  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
  };

  const renderContent = () => {
    return (
      <>
        <If condition={EnableWebApplication}>
          <Message
            type="notice"
            className="mb-s"
            title={intl('saenext.app-list.micro.CommonMicroserviceApplicationsIncludeZookeeper')}
          >
            {intl('saenext.app-list.micro.MicroserviceApplicationsProvideSupportingService')}
          </Message>
        </If>

        <Tab navClassName={!enableStateful ? 'none' : ''}>
          <Tab.Item title={intl('saenext.app-list.micro.StatelessApplication')} key="micro">
            <MicroApp
              history={props.history}
              namespaceList={namespaceData}
              namespaceId={namespaceId}
              key={enableStateful}
              stateful={enableStateful ? 'false' : undefined}
              tableUniqueKey={enableStateful ? `${MicroListKey}-notStateful` : MicroListKey}
            />
          </Tab.Item>
          {enableStateful && (
            <Tab.Item title={intl('saenext.app-list.micro.StatefulApplications')} key="stateful">
              <MicroApp
                history={props.history}
                namespaceList={namespaceData}
                namespaceId={namespaceId}
                stateful={'true'}
                tableUniqueKey={`${MicroListKey}-stateful`}
              />
            </Tab.Item>
          )}
        </Tab>
      </>
    );
  };

  return (
    <Page
      key={regionId}
      title={
        EnableWebApplication
          ? intl('saenext.app-list.micro.MicroserviceApplications')
          : intl('saenext.app-list.micro.ApplicationList')
      }
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: EnableWebApplication
            ? intl('saenext.app-list.micro.MicroserviceApplications')
            : intl('saenext.app-list.micro.ApplicationList'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <>{renderContent()}</>
    </Page>
  );
};

export default ListPage;
