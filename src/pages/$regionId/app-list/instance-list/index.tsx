import React, { useEffect, useRef, useState } from 'react';
import { intl, Page } from '@ali/cnd';
import InstanceList from '~/components/app-list/instance-list';
import { getParams, setSearchParams } from '~/utils/global';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';

const InstanceListPage = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;

  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : 'all';

  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);

  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
  };

  return (
    <Page
      key={regionId}
      title={intl('saenext.app-list.instance-list.InstanceList')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.app-list.instance-list.InstanceList'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <InstanceList
        history={props.history}
        namespaceList={namespaceData}
        namespaceId={namespaceId}
      />
    </Page>
  );
};

export default InstanceListPage;
