import React, { useState, useEffect, useRef, useContext } from 'react';
import { intl, Page, PageProps, Message } from '@ali/cnd';
import WebApp from '~/components/app-list/web-app';
import { getParams, setSearchParams } from '~/utils/global';
// import NamespaceTag from '~/components/shared/NamespaceTag';
import ErrorRegionBoundary from '~/components/shared/ErrorRegionBoundary';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';
import FeatureContext from '~/utils/featureContext';
import CachedData from '~/cache/common';

const ListPage = (props: PageProps) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;

  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : 'all';

  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [showNoneWarn, setShowNoneWarn] = useState(getParams('namespaceId') === 'none');

  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  useEffect(() => {
    if (EnableWebApplication === false) {
      window.xconsoleHistory.push(`/${regionId}/app-list/micro`);
    }
  }, [EnableWebApplication])

  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  // @ts-ignore
  const onNamespaceChange = (val) => {
    setShowNoneWarn(val === 'none');
    setNamespaceId(val);
    setRefreshIndex(Date.now());
  };

  // const pushNoneNamespace = (isEmpty) => {
  //   const tag = tagRef.current;
  //   if (!isEmpty && tag) {
  //     tag.pushNoneNamespace();
  //   }
  // };

  const renderContent = () => {
    return (
      <>
        <Message
          type="notice"
          className="mb-s"
          title={intl('saenext.app-list.web.CommonWebApplicationsIncludeSpring')}
        >
          {intl('saenext.app-list.web.WebApplicationsOnlySupportHttp')}
          <a
            href={CachedData.confLink('help:sae:web-app-doc')}
            target="_blank"
            rel="noopener noreferrer"
          >
            {intl('saenext.app-list.web.ReferenceDocumentation')}
          </a>
        </Message>

        {showNoneWarn ? (
          <Message type="warning" className="mb-s">
            {intl('saenext.app-list.web.NoNamespaceIsSpecifiedFor')}
          </Message>
        ) : null}

        <WebApp key={refreshIndex} history={props.history} namespaceList={namespaceData} />
      </>
    );
  };

  return (
    <Page
      key={regionId}
      title={
        EnableWebApplication
          ? intl('saenext.app-list.web.WebApplications')
          : intl('saenext.app-list.web.ApplicationList')
      }
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: EnableWebApplication
            ? intl('saenext.app-list.web.WebApplications')
            : intl('saenext.app-list.web.ApplicationList'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <ErrorRegionBoundary
        regionGreyKey="regions"
        changeRegion={(regionId) => {
          history.push(`/${regionId}/app-list/web`);
        }}
      >
        {renderContent()}
      </ErrorRegionBoundary>
    </Page>
  );
};

export default ListPage;
