/* eslint react/prop-types: 0 */
import React, { Suspense, useEffect, useState } from 'react';
import { Copy, Page, intl } from '@ali/cnd';
import { getParams, unshiftZero } from '~/utils/global';
import LoadingSkeleton from '~/components/shared/LoadingSkeleton';
import services from '~/services';
import { isEmpty } from 'lodash';
import { DEPLOY_TYPE } from '~/constants/application';
import ConfigureInfo from '~/components/version-detail/configure-info';
import BaseMonitor from '~/components/version-detail/basic-monitor';
import InstanceList from '~/components/version-detail/instance-list';
import LogControl from '~/components/version-detail/log-control';

const VersionDetail = (props) => {
  const {
    match: {
      params: {
        regionId,
        id: applicationID,
        versionId,
        section, // 详情的tab
      },
    },
    history,
    location: { search }, // ?后参数
  } = props;
  const applicationName = getParams('name');
  const namespaceId = getParams('namespaceId');
  const title = unshiftZero(versionId);
  const [appData, setAppData] = useState({});
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [deployType, setDeployType] = useState(DEPLOY_TYPE.IMAGE);

  useEffect(() => {
    // 获取应用的部署类型
    getWebAppDeploy();
  }, []);

  useEffect(() => {
    getWebAppVersionData();
  }, [refreshIndex]);

  const getWebAppDeploy = async () => {
    const res = await services.describeBuildPipeline({
      ApplicationId: applicationID,
      ApplicationName: applicationName,
    }) || {};
    const { Data } = res;
    let _deployType = DEPLOY_TYPE.IMAGE;
    if (!isEmpty(Data)) {
      const { CodeConfig, PackageConfig } = Data;
      if (!isEmpty(CodeConfig)) {
        _deployType = DEPLOY_TYPE.REPOISTORY;
      }
      if (!isEmpty(PackageConfig)) {
        _deployType = DEPLOY_TYPE.WEB_PACKAGE;
      }
    }
    setDeployType(_deployType);
  };

  const getWebAppVersionData = async () => {
    const _appData = await services.getAppVersionConfig({
      applicationID,
      applicationName,
      qualifier: versionId,
    }) || {};
    setAppData(_appData);
  };

  return (
    <Page
      title={<Copy text={title}>{title}</Copy>}
      historyBack={`/${regionId}/app-list/${applicationID}/web-app/version${search}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/web?namespaceId=${namespaceId}`,
          text: intl('saenext.versionId.section.ApplicationList'),
        },
        {
          to: `/${regionId}/app-list/${applicationID}/web-app/base${search}`,
          text: `${applicationName}`,
        },
        {
          to: `/${regionId}/app-list/${applicationID}/web-app/version${search}`,
          text: intl('saenext.versionId.section.VersionList'),
        },
        {
          text: intl('saenext.versionId.section.VersionTitleDetails', { title: title }) as string,
        },
      ]}
      nav={{
        shape: 'tab',
        defaultActiveKey: 'configure',
        activeKey: section,
        // @ts-ignore
        onChange: (value) => {
          history.replace(
            `/${regionId}/app-list/${applicationID}/version/${versionId}/${value}${search}`,
          );
        },
        items: [
          {
            key: 'configure',
            title: intl('saenext.versionId.section.VersionConfiguration'),
          },
          {
            key: 'instances',
            title: intl('saenext.versionId.section.VersionInstance'),
          },
          {
            key: 'monitor',
            title: intl('saenext.versionId.section.BasicMonitoring'),
          },
          {
            key: 'log',
            title: intl('saenext.versionId.section.Log'),
          },
        ],
      }}
      // @ts-ignore
      className="custom-page-layout"
    >
      {(!section || section === 'configure') && (
        <Suspense fallback={<LoadingSkeleton />}>
          <ConfigureInfo
            {...props}
            appData={appData}
            versionId={versionId}
            deployType={deployType}
            setRefreshIndex={setRefreshIndex}
          />
        </Suspense>
      )}

      {section === 'instances' && (
        <Suspense fallback={<LoadingSkeleton />}>
          <InstanceList {...props} appData={appData} versionId={versionId} />
        </Suspense>
      )}

      {section === 'monitor' && (
        <Suspense fallback={<LoadingSkeleton />}>
          <BaseMonitor
            {...props}
            appData={appData}
            versionId={versionId}
            applicationID={applicationID}
          />
        </Suspense>
      )}

      {section === 'log' && (
        <Suspense fallback={<LoadingSkeleton />}>
          <LogControl
            {...props}
            appData={appData}
            versionId={versionId}
            applicationID={applicationID}
          />
        </Suspense>
      )}
    </Page>
  );
};

export default VersionDetail;
