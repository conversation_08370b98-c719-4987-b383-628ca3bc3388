/* eslint react/prop-types: 0 */
import React, { Suspense, useEffect, useState, useMemo, useContext } from 'react';
import { Page, Copy, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import { get, isEmpty } from 'lodash';
import services from '~/services';
import CachedData from '~/cache/common';
import { inDebtHandle } from '~/utils/accountHandle';
import LoadingSkeleton from '~/components/shared/LoadingSkeleton';
import { DEPLOY_TYPE } from '~/constants/application';
import BaseInfo from '~/components/app-detail/web-app/basic-info';
import VersionList from '~/components/version-list';
import BaseMonitor from '~/components/app-monitor/web-app';
import LogControl from '~/components/app-detail/web-app/log-control';
import BuildRecord from '~/components/app-detail/web-app/build-record';
import FeatureContext from '~/utils/featureContext';
import ArmsMonitor from '~/components/arms/web';
import ArmsOverview from '~/components/arms/web/AppOverview';
import ArmsInterface from '~/components/arms/web/AppInterface';
import ArmsSpecifics from '~/components/arms/web/AppSpecifics';
import ArmsNextOverview from '~/components/arms/web/AppNextOverview';

const menuText = new Map()
  .set('base', intl('saenext.web-app.section.BasicInformation'))
  .set('version', intl('saenext.web-app.section.VersionList'))
  .set('monitor', intl('saenext.web-app.section.BasicMonitoring'))
  .set('arms', intl('saenext.web-app.section.ApplicationMonitoring'))
  .set('arms-monitor', intl('saenext.web-app.section.ApplicationMonitoring'))
  .set('arms-overview', intl('saenext.web-app.section.ApplicationMonitoringApplicationOverview'))
  .set('arms-app', intl('saenext.web-app.section.ApplicationMonitoringApplicationDetails'))
  .set('arms-interface', intl('saenext.web-app.section.ApplicationMonitoringApiCall'))
  .set('log', intl('saenext.web-app.section.LogManagement'))
  .set('structure', intl('saenext.web-app.section.BuildRecords'));

const disableArmsMenu = {
  key: 'arms',
  title: intl('saenext.web-app.section.ApplicationMonitoring'),
};

const latestArmsMenu = {
  key: 'arms-monitor',
  title: intl('saenext.web-app.section.ApplicationMonitoring'),
};

const previousArmsMenu = {
  key: 'arms-monitor',
  title: intl('saenext.web-app.section.ApplicationMonitoring'),
  items: [
    {
      key: 'arms-overview',
      title: intl('saenext.web-app.section.ApplicationOverview'),
    },
    {
      key: 'arms-app',
      title: intl('saenext.web-app.section.ApplicationDetails'),
    },
    {
      key: 'arms-interface',
      title: intl('saenext.web-app.section.ApiCall'),
    },
  ],
};

const buildRecordMenu = {
  key: 'structure',
  title: intl('saenext.web-app.section.BuildRecords'),
};

const AppDetail = (props) => {
  const {
    match: {
      params: { regionId, id: applicationID, section },
    },
    history,
    location: { search }, // ?后参数
  } = props;

  const applicationName = getParams('name');
  const [appData, setAppData] = useState<any>({});
  const [armsEnabled, setArmsEnabled] = useState(false);
  const [isLatestMonitor, setIsLatestMonitor] = useState(false);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [deployType, setDeployType] = useState(DEPLOY_TYPE.IMAGE);
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const inDebtOverdue = CachedData.getOpenStatus('inDebtOverdueStatus');
  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  useEffect(() => {
    // 获取应用的部署类型
    getWebAppDeploy();
    // 应用应该显示老版监控还是新版监控
    getWebAppTrace();
  }, []);

  useEffect(() => {
    initWebAppData();
  }, [refreshIndex]);

  useEffect(() => {
    // 欠费 但未超期
    isInDebt && !inDebtOverdue && inDebtHandle();
  }, [isInDebt]);

  const initWebAppData = async () => {
    const res = await services.getWebApplication({
      applicationID,
      applicationName,
    }, (err, data, callback) => {
      if (err.code === 'ApplicationNotFound') {
        history.replace('/404')
        return;
      }
      return err;
    });
    const _armsEnabled = get(res, 'enableAppMetric', false);
    let _isLatestMonitor = false;
    if (_armsEnabled) {
      _isLatestMonitor = await getWebAppTrace();
    }
    setAppData(res);
    setArmsEnabled(_armsEnabled);
    setIsLatestMonitor(_isLatestMonitor);
    return [_armsEnabled, _isLatestMonitor];
  };

  const getWebAppDeploy = async () => {
    const res = await services.describeBuildPipeline({
      ApplicationId: applicationID,
      ApplicationName: applicationName,
    });
    const { Data } = res || {};
    let _deployType = DEPLOY_TYPE.IMAGE;
    if (!isEmpty(Data)) {
      const { CodeConfig, PackageConfig } = Data;
      if (!isEmpty(CodeConfig)) {
        _deployType = DEPLOY_TYPE.REPOISTORY;
      }
      if (!isEmpty(PackageConfig)) {
        _deployType = DEPLOY_TYPE.WEB_PACKAGE;
      }
    }
    setDeployType(_deployType);
  };

  const getWebAppTrace = async () => {
    let _isLatestMonitor = false;
    const res = await services.getTraceApp({
      params: { RegionId: regionId, Pid: applicationID },
      customErrorHandle: (err, data, callback) => {
        _isLatestMonitor = false;
        return _isLatestMonitor;
      }
    });
    const createTime = get(res, 'TraceApp.CreateTime');
    // 时间大于 2024-08-28 00:00:00 都用最新版的监控
    if (createTime >= 1724774400000) {
      _isLatestMonitor = true;
    }
    return _isLatestMonitor;
  };

  const redirectArms = async (callback) => {
    const [_armsEnabled, _isLatestMonitor] = await initWebAppData();
    setArmsEnabled(_armsEnabled);
    setIsLatestMonitor(_isLatestMonitor);
    callback && callback();
    if (_armsEnabled) {
      history.push(`/${regionId}/app-list/${applicationID}/web-app/${_isLatestMonitor ? 'arms-monitor' : 'arms-overview'}?${_search}`);
    } else {
      history.push(`/${regionId}/app-list/${applicationID}/web-app/arms?${_search}`);
    }
  };

  const title = getParams('name') || appData.applicationName || intl('saenext.web-app.section.ApplicationDetails');
  const namespaceId = appData.namespaceID;
  const _search = new URLSearchParams(search);
  _search.delete('taskId');

  const navItems = useMemo(() => {
    let items = [
      {
        key: 'base',
        title: intl('saenext.web-app.section.BasicInformation'),
      },
      {
        key: 'version',
        title: intl('saenext.web-app.section.VersionList'),
      },
      {
        key: 'monitor',
        title: intl('saenext.web-app.section.BasicMonitoring'),
      },
      {
        key: 'log',
        title: intl('saenext.web-app.section.LogManagement'),
      },
    ];

    // Web 应用监控不区分语言 只区分新版和老版
    if (armsEnabled) {
      const enableArmsMenu = isLatestMonitor ? latestArmsMenu : previousArmsMenu;
      items.splice(3, 0, enableArmsMenu);
    } else {
      items.splice(3, 0, disableArmsMenu);
    }

    if (deployType !== DEPLOY_TYPE.IMAGE) {
      items.push(buildRecordMenu);
    }

    return [...items];
  }, [armsEnabled, isLatestMonitor, deployType]);


  return (
    <Page
      title={<Copy text={title}>{title}</Copy>}
      historyBack={`/${regionId}/app-list/web?namespaceId=${namespaceId}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/web?namespaceId=${namespaceId}`,
          text: EnableWebApplication
            ? intl('saenext.app-list.web.WebApplications')
            : intl('saenext.app-list.web.ApplicationList'),
        },
        {
          to: `/${regionId}/app-list/${applicationID}/web-app/base${search}`,
          text: `${applicationName}`,
        },
        {
          text: `${menuText.get(section)}`,
        },
      ]}
      nav={{
        shape: 'menu',
        defaultActiveKey: 'base',
        // @ts-ignore
        // defaultOpenKeys: openKeys,
        // openKeys: [],
        activeKey: section,
        // @ts-ignore
        onChange: (value) => {
          history.push(`/${regionId}/app-list/${applicationID}/web-app/${value}?${_search}`);
          isInDebt && !inDebtOverdue && inDebtHandle();
        },
        // @ts-ignore
        items: navItems,
      }}
      // @ts-ignore
      className="custom-page-layout"
    >
      {(!section || section === 'base') && (
        <Suspense fallback={<LoadingSkeleton />}>
          <BaseInfo
            {...props}
            appData={appData}
            isLatestMonitor={isLatestMonitor}
            setRefreshIndex={setRefreshIndex}
          />
        </Suspense>
      )}

      {section === 'version' && (
        <Suspense fallback={<LoadingSkeleton />}>
          <VersionList
            {...props}
            appData={appData}
            deployType={deployType}
            // 创建版本之后有可能改变app的设置，所以需要更新app
            setRefreshIndex={setRefreshIndex}
          />
        </Suspense>
      )}

      {section === 'monitor' && (
        <Suspense fallback={<LoadingSkeleton />}>
          <BaseMonitor {...props} appData={appData} applicationID={applicationID} />
        </Suspense>
      )}

      {section === 'arms' && !armsEnabled && (
        <Suspense fallback={<LoadingSkeleton />}>
          <ArmsMonitor
            {...props}
            applicationID={applicationID}
            applicationName={applicationName}
            redirectArms={redirectArms}
            minInstanceCount={get(appData, 'scaleConfig.minimumInstanceCount', 0)}
          />
        </Suspense>
      )}

      {section === 'arms-overview' && armsEnabled && (
        <Suspense fallback={<LoadingSkeleton />}>
          <ArmsOverview {...props} regionId={regionId} applicationID={applicationID} />
        </Suspense>
      )}

      {section === 'arms-app' && armsEnabled && (
        <Suspense fallback={<LoadingSkeleton />}>
          <ArmsSpecifics {...props} regionId={regionId} applicationID={applicationID} />
        </Suspense>
      )}

      {section === 'arms-interface' && armsEnabled && (
        <Suspense fallback={<LoadingSkeleton />}>
          <ArmsInterface {...props} regionId={regionId} applicationID={applicationID} />
        </Suspense>
      )}

      {section === 'arms-monitor' && armsEnabled && (
        <Suspense fallback={<LoadingSkeleton />}>
          <ArmsNextOverview
            {...props}
            regionId={regionId}
            applicationID={applicationID}
            applicationName={applicationName}
            redirectArms={redirectArms}
            programmingLanguage={get(appData, 'programmingLanguage', 'java')}
          />
        </Suspense>
      )}

      {section === 'log' && (
        <Suspense fallback={<LoadingSkeleton />}>
          <LogControl {...props} appData={appData} />
        </Suspense>
      )}

      {section === 'structure' && (
        <Suspense fallback={<LoadingSkeleton />}>
          <BuildRecord {...props} deployType={deployType} />
        </Suspense>
      )}
    </Page>
  );
};

export default AppDetail;
