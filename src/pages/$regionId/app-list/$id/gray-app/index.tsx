/**
 *
 * 灰度应用只支持微应用，当前不用区分 web 应用 和 微应用
 */

import React, { useState, useEffect } from 'react';
import { Page, intl, Loading } from '@ali/cnd';
import MicroAppCreator from '~/components/app-create/micro-app';
import services from '~/services';
import { DEPLOY_TYPE } from '~/constants/application';
import { jsonParse } from '~/utils/transfer-data';

export default (props) => {
  const {
    match: {
      params: { regionId, id: applicationID, source },
    },
    history,
  } = props;
  const [isLoading, setIsLoading] = useState(true);
  const [appValue, setAppValue] = useState({});
  const [baseAppId, setBaseAppId] = useState(applicationID);

  useEffect(() => {
    getMicroApplication();
  }, [regionId]);

  async function getMicroApplication() {
    setIsLoading(true);
    const res = await services.describeMicroApplicationConfig({
      AppId: applicationID,
    });
    const { Data: _appData = {} } = res || {};
    const {
      AppId,
      AppName,
      AppDescription,
      NamespaceId,
      VpcId,
      VSwitchId,
      SecurityGroupId,
      Cpu,
      Memory,
      Replicas,
      PackageType,
      ImageUrl,
      AcrInstanceId,
      AcrAssumeRoleArn,
      EnableImageAccl,
      ImagePullSecrets,
      ProgrammingLanguage,
      Jdk,
      Php,
      Python,
      PackageUrl,
      PackageVersion,
      Timezone,
      TomcatConfig,
      WebContainer,
      EdasContainerVersion,
      PhpExtensions,
      PhpPECLExtensions,
      PythonModules,
      PackageRuntimeCustomBuild,
      PhpConfig,
      PhpConfigLocation,
      PvtzDiscovery,
      ...advanceValue
    } = _appData;

    const regexImage = /^Image/i;

    const microAppValue = {
      basicCopyValue: {
        applicationName: `${AppName}`,
        description: AppDescription,
        appType: 'micro',
        namespaceConfig: {
          NamespaceId,
          VpcId,
          VSwitchId,
          SecurityGroupId,
        },
        deployConfig: {
          type: regexImage.test(PackageType) ? DEPLOY_TYPE.IMAGE : DEPLOY_TYPE.MICRO_PACKAGE,
          image: ImageUrl,
          instanceId: AcrInstanceId,
          acrAssumeRoleArn: AcrAssumeRoleArn,
          enableImageAccl: EnableImageAccl,
          imagePullSecrets: ImagePullSecrets,
          vpc: AcrInstanceId ? VpcId : undefined,
          ProgrammingLanguage,
          PackageType,
          Jdk,
          Php,
          Python,
          uploadType: 'url',
          PackageUrl,
          PackageVersion,
          Timezone,
          TomcatConfig,
          WebContainer,
          EdasContainerVersion,
          PhpExtensions: jsonParse(PhpExtensions),
          PhpPECLExtensions: jsonParse(PhpPECLExtensions),
          PythonModules: jsonParse(PythonModules),
          PackageRuntimeCustomBuild,
          PhpConfig,
          PhpConfigLocation,
        },
        spec: {
          cpu: Cpu / 1000,
          memory: Memory / 1024,
        },
        replicas: Replicas,
      },
      ...advanceValue,
    };
    setAppValue(microAppValue);
    setBaseAppId(AppId);
    setIsLoading(false);
  }

  return (
    <Page
      title={intl('saenext.id.gray-app.CreateAGrayscaleApplication')}
      historyBack={`/${regionId}/app-list/micro`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/micro`,
          text: intl('saenext.copy-app.source.MicroserviceApplications'),
        },
        {
          text: intl('saenext.id.gray-app.CreateAGrayscaleApplication'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <Loading visible={isLoading} style={{ width: '100%', height: '100%' }}>
        <MicroAppCreator {...props} appValue={appValue} baseAppId={baseAppId} />
      </Loading>
    </Page>
  );
};
