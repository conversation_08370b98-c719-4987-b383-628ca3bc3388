/* eslint react/prop-types: 0 */
import React, { useState } from 'react';
import { Copy, Page, intl } from '@ali/cnd';
import { getParams } from '~/utils/global';
import ChangeOrderBase from '~/components/app-detail/micro-app/change-order/ChangeOrderBase';

const RevisionDetail = (props) => {
  const {
    match: {
      params: { regionId, recordId, id: appId },
    },
    location: { search }, // ?后参数
  } = props;
  const namespaceId = getParams('namespaceId');
  const title = getParams('name') || intl('saenext.micro-app.section.ApplicationDetails');
  const approvalRecordId = getParams('approvalRecordId') || '';
  const [recordTitle, setRecordTitle] = useState(recordId);

  const refreshBreadcrumbs = (title) => {
    setRecordTitle(title);
  };

  return (
    <Page
      title={<Copy text={recordTitle}>{recordTitle}</Copy>}
      historyBack={`/${regionId}/app-list/${appId}/micro-app/revision${search}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/micro?namespaceId=${namespaceId}`,
          text: intl('saenext.record.$recordId.ApplicationList'),
        },
        {
          to: `/${regionId}/app-list/${appId}/micro-app/base${search}`,
          text: `${title}`,
        },
        {
          to: `/${regionId}/app-list/${appId}/micro-app/revision${search}`,
          text: intl('saenext.record.$recordId.ChangeRecord'),
        },
        {
          text: intl('saenext.record.$recordId.ChangeDetails'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <ChangeOrderBase
        regionId={regionId}
        changeOrderId={recordId}
        appId={appId}
        history={props.history}
        refreshBreadcrumbs={refreshBreadcrumbs}
        approvalRecordId={approvalRecordId}
      />
    </Page>
  );
};

export default RevisionDetail;
