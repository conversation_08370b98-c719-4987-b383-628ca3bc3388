import React from 'react';
import Webshell from '~/components/shared/Webshell';
import { getParams } from '~/utils/global';

const MicroShellPage = (props) => {
  const {
    match: {
      params: { regionId, id: appId, instanceId },
    },
    location: { search },
  } = props;

  const containerId = getParams('container');
  const tokenId = getParams('tokenId');
  const hideMenu = getParams('hideTerminalMenu');

  return (
    <div className='fullsceen'>
      <Webshell
        isFullSceen
        hideMenu={hideMenu === 'true'}
        regionId={regionId}
        appId={appId}
        instanceId={instanceId}
        containerId={containerId || undefined}
        tokenId={tokenId || undefined}
        onClose={() => {
          window.close();
        }}
      />
    </div>
  )
}

export default MicroShellPage;