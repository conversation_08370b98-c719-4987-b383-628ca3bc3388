/* eslint react/prop-types: 0 */
import React, { Suspense, useEffect, useState, useMemo, useContext, useRef } from 'react';
import { Page, Copy, intl, Link, LinkButton, Dialog, Icon } from '@ali/cnd';
import {
  getParams,
  isTypeImage,
  microAppLink,
  setSearchParams,
  removeParams,
} from '~/utils/global';
import LoadingSkeleton from '~/components/shared/LoadingSkeleton';
import services from '~/services';
import { isForbidden } from '~/utils/authUtils';
import { get, isEmpty, isEqual, includes, find } from 'lodash';
import BaseInfo from '~/components/app-detail/micro-app/basic-info';
import PersistLog from '~/components/app-detail/micro-app/log-control/PersistLog';
import RealtimeLog from '~/components/app-detail/micro-app/log-control/RealtimeLog';
import BaseMonitor from '~/components/app-monitor/micro-app';
import MscOverview from '~/components/app-detail/micro-app/msc/AppOverview';
import MscInterface from '~/components/app-detail/micro-app/msc/AppInterface';
import <PERSON><PERSON><PERSON><PERSON> from '~/components/app-detail/micro-app/msc/AppNode';
import MscTraffic from '~/components/app-detail/micro-app/msc/AppTraffic';
import MicroAppContext from '~/utils/microAppContext';
import EventList from '~/components/shared/EventList';
import FeatureContext from '~/utils/featureContext';
import ArmsGoMonitor from '~/components/arms/micro/golang/AppMonitor';
import ArmsGoOverview from '~/components/arms/micro/golang/AppOverview';
import ArmsJavaOverview from '~/components/arms/micro/java/AppOverview';
import ArmsJavaInterface from '~/components/arms/micro/java/AppInterface';
import ArmsJavaSpecifics from '~/components/arms/micro/java/AppSpecifics';
import ArmsJavaSenior from '~/components/arms/micro/java/AppSenior';
import ArmsEbpfMonitor from '~/components/arms/micro/ebpf/AppMonitor';
import MscService from '~/components/app-detail/micro-app/msc/ServiceList';
import MscGrayRules from '~/components/app-detail/micro-app/msc/GrayRuleRoutes';
import ChangeOrderList from '~/components/app-detail/micro-app/change-order/ChangeOrderList';
import AlarmRules from '~/components/app-detail/micro-app/alarm/AlarmRules';
import AlarmDispatch from '~/components/app-detail/micro-app/alarm/DispatchHistory';
import AlarmIncident from '~/components/app-detail/micro-app/alarm/IncidentHistory';
import AlarmContact from '~/components/app-detail/micro-app/alarm/NotifyContact';
import AlarmNotify from '~/components/app-detail/micro-app/alarm/NotifyPolicy';
import AhasOverview from '~/components/app-detail/micro-app/ahas/AppOverview';
import AhasMonitor from '~/components/app-detail/micro-app/ahas/MachineMonitor';
import AhasRules from '~/components/app-detail/micro-app/ahas/RulesControl';
import AhasLog from '~/components/app-detail/micro-app/ahas/OperationLog';
import MseOverview from '~/components/app-detail/micro-app/mse/AppOverview';
import MseInterface from '~/components/app-detail/micro-app/mse/InterfaceDetails';
import MseNode from '~/components/app-detail/micro-app/mse/NodeDetails';
import MseRules from '~/components/app-detail/micro-app/mse/RulesConfigure';
import RemoteDebug from '~/components/app-detail/micro-app/remote-debug';
import { getMseOpenStatus } from '~/utils/openStatus';
import { AES_CONSTANT, trackMicroAppMsc } from '~/tracker';
import Deploy from '~/components/app-detail/micro-app/deploy';
import InstanceDetail from '~/components/app-detail/micro-app/basic-info/InstanceDetail';
import CachedData from '~/cache/common';
import ArmsMonitor from '~/components/app-detail/micro-app/arms-monitor';
import PrometheusMonitorNotEnable from '~/components/shared/PrometheusMonitorField/PrometheusMonitorNotEnable';
import moment from 'moment';
import { confFeature } from '@alicloud/console-one-conf';
import { PrometheusPolicyName } from '~/constants/ignoreMust';

const titleMenu = new Map()
  .set('base', intl('saenext.micro-app.section.BasicInformation'))
  .set('deploy', intl('saenext.micro-app.basic-info.AppActionBar.DeployApplications'))
  .set('instance', intl('saenext.micro-app.section.InstanceDetails'))
  .set('revision', intl('saenext.micro-app.section.ChangeRecord'))
  .set('event', intl('saenext.micro-app.section.ApplicationEvents'))
  .set('realtime-log', intl('saenext.micro-app.section.RealTimeLog'))
  .set('persist-log', intl('saenext.micro-app.section.PersistentLogs'))
  .set('monitor', intl('saenext.micro-app.section.BasicMonitoring'))
  .set('arms', intl('saenext.micro-app.section.ApplicationMonitoring'))
  .set('arms-monitor', intl('saenext.micro-app.section.ApplicationMonitoring'))
  .set('arms-ebpf', intl('saenext.micro-app.section.ApplicationMonitoring'))
  .set('arms-overview', intl('saenext.micro-app.section.ApplicationOverview.2'))
  .set('arms-app', intl('saenext.micro-app.section.ApplicationDetails'))
  .set('arms-interface', intl('saenext.micro-app.section.ApiCall'))
  .set('arms-senior', intl('saenext.micro-app.section.AdvancedMonitoring'))
  .set('msc', intl('saenext.micro-app.section.MicroserviceGovernance'))
  .set('msc-overview', intl('saenext.micro-app.section.ApplicationOverview'))
  .set('msc-interface', intl('saenext.micro-app.section.InterfaceDetails'))
  .set('msc-node', intl('saenext.micro-app.section.NodeDetails'))
  .set('msc-traffic', intl('saenext.micro-app.section.TrafficGovernance'))
  .set('msc-service', intl('saenext.micro-app.section.ServiceList'))
  .set('msc-rules', intl('saenext.micro-app.section.GrayscaleRules'))
  .set('ahas-overview', intl('saenext.micro-app.section.ApplicationOverview'))
  .set('ahas-monitor', intl('saenext.micro-app.section.MachineMonitoring'))
  .set('ahas-rules', intl('saenext.micro-app.section.RuleManagement'))
  .set('ahas-log', intl('saenext.micro-app.section.OperationLog'))
  .set('mse-overview', intl('saenext.micro-app.section.ApplicationOverview'))
  .set('mse-interface', intl('saenext.micro-app.section.InterfaceDetails'))
  .set('mse-node', intl('saenext.micro-app.section.NodeDetails'))
  .set('mse-rules', intl('saenext.micro-app.section.RuleConfiguration'))
  .set('remote-debug', intl('saenext.micro-app.section.RemoteDebugging'))
  .set('alarm-rules', intl('saenext.micro-app.section.AlertRules'))
  .set('alarm-dispatch', intl('saenext.micro-app.section.AlertSendingHistory'))
  .set('alarm-incident', intl('saenext.micro-app.section.AlarmEventHistory'))
  .set('alarm-contact', intl('saenext.micro-app.section.ContactManagement'))
  .set('alarm-notify', intl('saenext.micro-app.section.NotificationPolicy'))
  .set('prometheus-monitor', intl('saenext.micro-app.section.PrometheusMonitoring'));

const v2mscMenu = [
  {
    key: 'msc',
    title: intl('saenext.micro-app.section.MicroserviceGovernance'),
    items: [
      {
        key: 'msc-overview',
        title: intl('saenext.micro-app.section.ApplicationOverview'),
      },
      {
        key: 'msc-interface',
        title: intl('saenext.micro-app.section.InterfaceDetails'),
      },
      {
        key: 'msc-node',
        title: intl('saenext.micro-app.section.NodeDetails'),
      },
      {
        key: 'msc-traffic',
        title: intl('saenext.micro-app.section.TrafficGovernance'),
      },
      {
        key: 'msc-service',
        title: intl('saenext.micro-app.section.ServiceList'),
      },
    ],
  },
];

const v1mscMenu = [
  {
    key: 'msc',
    title: intl('saenext.micro-app.section.MicroserviceGovernance'),
    items: [
      {
        key: 'msc-service',
        title: intl('saenext.micro-app.section.ServiceList'),
      },
    ],
  },
  {
    key: 'msc-rules',
    title: intl('saenext.micro-app.section.GrayscaleRules'),
  },
];

const ahasLimitMenu = {
  key: 'ahas',
  title: intl('saenext.micro-app.section.ThrottlingDegradationJavaOnly'),
  items: [
    {
      key: 'ahas-overview',
      title: intl('saenext.micro-app.section.ApplicationOverview'),
    },
    {
      key: 'ahas-monitor',
      title: intl('saenext.micro-app.section.MachineMonitoring'),
    },
    {
      key: 'ahas-rules',
      title: intl('saenext.micro-app.section.RuleManagement'),
    },
    {
      key: 'ahas-log',
      title: intl('saenext.micro-app.section.OperationLog'),
    },
  ],
};

const mseLimitMenu = {
  key: 'mse',
  title: intl('saenext.micro-app.section.ThrottlingDegradationJavaOnly'),
  items: [
    {
      key: 'mse-overview',
      title: intl('saenext.micro-app.section.ApplicationOverview'),
    },
    {
      key: 'mse-interface',
      title: intl('saenext.micro-app.section.InterfaceDetails'),
    },
    {
      key: 'mse-node',
      title: intl('saenext.micro-app.section.NodeDetails'),
    },
    {
      key: 'mse-rules',
      title: intl('saenext.micro-app.section.RuleConfiguration'),
    },
  ],
};

const javaArmsMenu = {
  key: 'arms-monitor',
  title: intl('saenext.micro-app.section.ApplicationMonitoring'),
  items: [
    {
      key: 'arms-overview',
      title: intl('saenext.micro-app.section.ApplicationOverview.2'),
    },
    {
      key: 'arms-app',
      title: intl('saenext.micro-app.section.ApplicationDetails'),
    },
    {
      key: 'arms-interface',
      title: intl('saenext.micro-app.section.ApiCall'),
    },
    {
      key: 'arms-senior',
      title: intl('saenext.micro-app.section.AdvancedMonitoring'),
    },
  ],
};

const golangArmsMenu = {
  disable: {
    key: 'arms',
    title: intl('saenext.micro-app.section.ApplicationMonitoring'),
  },
  enable: {
    key: 'arms-monitor',
    title: intl('saenext.micro-app.section.ApplicationMonitoring'),
  },
};

const ebpfArmsMenu = {
  key: 'arms-ebpf',
  title: intl('saenext.micro-app.section.ApplicationMonitoring'),
};

const AppDetail = (props) => {
  const {
    match: {
      params: { regionId, id: appId, section },
    },
    history,
    location: { search },
  } = props;

  const [appConfig, setAppConfig] = useState<any>({});
  const [appStatus, setAppStatus] = useState<any>({});
  const [isArmsMonitor, setIsArmsMonitor] = useState<boolean>(false);
  const [resourceQuota, setResourceQuota] = useState<any>({});
  const [scaleRuleCounts, setScaleRuleCounts] = useState<any>({});
  const [scaleRuleEnabled, setScaleRuleEnabled] = useState(false);
  const [scaleRules, setScaleRules] = useState<any>([]);
  const [slbQpsRt, setSlbQpsRt] = useState<any>({});
  const [slbQpsRtPermission, setSlbQpsRtPermission] = useState(true);
  const [instanceGroup, setInstanceGroup] = useState([]);
  const [type, setType] = useState<'preview' | 'deploy' | ''>('');
  const [mseVersion, setMseVersion] = useState<number | null>(null);
  const [mscUpdateStatus, setMscUpdateStatus] = useState('');
  const [enablePrometheus,setEnablePrometheus] = useState(false);
  const [appAddonReleaseInfo, setAppAddonReleaseInfo] = useState({});
  const [prometheusUrl, setPrometheusUrl] = useState('');
  const [hasCmsPermission, setHasCmsPermission] = useState(true);
  const scaleRef = useRef({});
  const { feature, webFeature } = useContext(FeatureContext);
  const { enableNewSaeVersion = false, enableSaeStdVersionNewMse = false } = feature;
  const { EnableWebApplication, AccountOpenTime } = webFeature;
  const saeOldConsoleEnable = confFeature('sae_1.0_console_enable');

  const refreshRef = useRef(null);

  // 是否多版本新用户 --- 轻量版+专业版
  const multipleVersionsTime = get(window, 'ALIYUN_CONSOLE_GLOBAL.multipleVersionsTime');
  const isMultipleNewAccount =
    AccountOpenTime && multipleVersionsTime
      ? AccountOpenTime > moment(multipleVersionsTime).valueOf()
      : false;
  const isSupportMultiVersions = enableNewSaeVersion || isMultipleNewAccount;

  const title =
    getParams('name') || appConfig.AppName || intl('saenext.micro-app.section.ApplicationDetails');
  const namespaceId = appConfig.NamespaceId;

  useEffect(() => {
    getAppQuota();
  }, []);

  useEffect(() => {
    if (section.startsWith('msc-') && section !== 'msc-rules') {
      trackMicroAppMsc({
        behavior: 'VIEW',
        stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
        appId: appId,
        section,
        trackType: 'MSC',
      });
    }
  }, [section]);

  useEffect(() => {
    if (appConfig.ProgrammingLanguage === 'java' && appConfig.SaeVersion === 'v1') {
      getMscUpdateStatus();
    }
  }, [appConfig.ProgrammingLanguage, appConfig.SaeVersion]);

  useEffect(() => {
    refreshRef.current = refresh;
  });

  useEffect(() => {
    refreshRef.current();
    const interval = setInterval(() => {
      refreshRef.current();
    }, 10000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const refresh = () => {
    getMicroAppData();
    getMicroAppStatus();
    getMicroAppArmsStatus();
  };

  // 判读v1是否开启了微服务治理
  const handleV1IsOpenMsc = (info) => {
    const SaeVersion = get(info, 'SaeVersion', '');
    const readinessObj = (info?.Readiness && JSON.parse(info?.Readiness)) || {};
    if (
      (SaeVersion === 'v1' || !SaeVersion) &&
      get(readinessObj, 'httpGet.port') != 54199 &&
      !includes(get(appConfig, 'PreStop', ''), '127.0.0.1:54199/offline') &&
      !get(appConfig, 'EnableAhas', false)
    ) {
      return false;
    } else {
      return true;
    }
  };
  const getMicroAppData = async () => {
    const data = await services.describeMicroApplicationConfig(
      {
        AppId: appId,
      },
      true,
      (err, data, callback) => {
        if (err.code === 'InvalidAppId.NotFound') {
          history.replace('/404');
          return;
        }
        return err;
      },
    );
    const _appConfig = get(data, 'Data', {});
    getAddonReleaseInfo(_appConfig);
    if (isEmpty(_appConfig) || isEqual(appConfig, _appConfig)) return;
    setSearchParams({
      name: _appConfig.AppName,
    });
    setAppConfig({ ..._appConfig });
  };

  const getAddonReleaseInfo = async (_appConfig) => {
    const policyRes = await services.CreateIntegrationPolicy({
      params: {
        content: {
          policyName: CachedData.lang === 'zh-CN' ? PrometheusPolicyName : 'SAE-IntegrationPolicy',
          policyType: 'SAE',
        },
      },
      customErrorHandle: (error, data, callback) => {
        if (isForbidden(error.code)) {
          setHasCmsPermission(false);
        } else {
          setHasCmsPermission(true);
        }
      },
    });
    const policyId = policyRes?.policy?.policyId || '';
    if (policyId) {
      const addonReleaseRes = await services.ListAddonReleases({
        params: {
          policyId,
          addonName: 'cloud-sae-custom',
        },
      });
      const _curAppAddonRelease = find(get(addonReleaseRes, 'releases', []), (item) => {
        const config = get(item, 'config', '') ? JSON.parse(get(item, 'config', '')) : {};
        return (
          get(config, '_entity.instance_id', '') === _appConfig?.AppId &&
          get(config, '_entity.vpc_id', '') === _appConfig?.VpcId
        );
      });
      const curAppAddonReleaseConfig = isEmpty(_curAppAddonRelease)
        ? {}
        : JSON.parse(get(_curAppAddonRelease, 'config', '')) || {};
      let _enablePrometheus = false;
      if(!isEmpty(curAppAddonReleaseConfig) && get(curAppAddonReleaseConfig,'port','') && get(curAppAddonReleaseConfig,'metricPath','')){
        _enablePrometheus = true;
      }else{
        _enablePrometheus = false;
      }
      setEnablePrometheus(_enablePrometheus);
      setAppAddonReleaseInfo({
        policyId,
        addonVersion: _curAppAddonRelease?.nextVersion || _curAppAddonRelease?.version || '',
        config: curAppAddonReleaseConfig,
        conditions:get(_curAppAddonRelease,'conditions',[]),
        enablePrometheus: _enablePrometheus
      });
      if (_enablePrometheus) {
        const storageRes = await services.ListIntegrationPolicyStorageRequirements({
          params: {
            policyId,
            addonName: 'cloud-sae-custom',
            addonReleaseName: `sae-custom-${_appConfig?.AppId}`,
          },
        });
        const clusterId = get(storageRes, 'storageRequirements[0].status.instanceId', '');
        clusterId &&
          setPrometheusUrl(
            `${CachedData.confLink(
              'feature:cms:url',
            )}/prom/instances/details?clusterId=${clusterId}&regionId=${regionId}&path=metric-explorer`,
          );
      }
    }
  };

  const getMicroAppStatus = async () => {
    const data = await services.describeMicroApplicationStatus(
      {
        AppId: appId,
      },
      true,
    );
    const _appStatus = get(data, 'Data', {});
    if (isEmpty(_appStatus) || isEqual(appStatus, _appStatus)) return;
    setAppStatus(_appStatus);
  };

  const getMicroAppArmsStatus = async () => {
    const { Data: { Enable = false } = {} } =
      (await services.queryMicroAppArmsStatus(
        {
          AppId: appId,
        },
        true,
      )) || {};
    setIsArmsMonitor(Enable);
  };

  const getAppQuota = async () => {
    const { Data = {} } =
      (await services.getResourceQuota({
        RegionId: regionId,
      })) || {};
    if (isEmpty(Data) || isEqual(resourceQuota, Data)) return;
    setResourceQuota(Data);
  };

  const getMscUpdateStatus = async () => {
    const mseStatus = await getMseOpenStatus();

    // Status: 1 未开通；2 已开通
    // Version: 1 专业版；2 企业版
    const { Status: openStatus, Version: mseVersion } = mseStatus || {};
    setMseVersion(mseVersion);

    if (openStatus === 2) {
      const data = await services.getAppMseStatus({
        params: {
          AppId: appId,
          // 专业版传 false，企业版传 true
          EnableAhas: mseVersion === 2,
        },
      });
      const mseNamespace = get(data, 'Data.MseAppNameSpace');
      if (mseNamespace === 'default') {
        setMscUpdateStatus('enabled');
      }
    }
  };

  const armsMenu = useMemo(() => {
    const isImage = isTypeImage(appConfig.PackageType);
    const isGo = isEqual(get(appConfig, 'ProgrammingLanguage'), 'golang');
    const isJava = isEqual(get(appConfig, 'ProgrammingLanguage'), 'java');
    const isPhp = isEqual(get(appConfig, 'ProgrammingLanguage'), 'php');
    const isPython = isEqual(get(appConfig, 'ProgrammingLanguage'), 'python');
    const isOther = isEqual(get(appConfig, 'ProgrammingLanguage'), 'other');
    const ebpf = get(feature, 'ebpf', false);
    const armsEnabled = get(appConfig, 'EnableNewArms', false);
    const NewSaeVersion = get(appConfig, 'NewSaeVersion', '');
    if (NewSaeVersion === 'pro' && (isJava || isPython || isGo)) {
      return {
        key: 'arms-monitor',
        title: intl('saenext.micro-app.section.ApplicationMonitoring'),
      };
    }
    if (!NewSaeVersion || NewSaeVersion === 'std') {
      if ((isGo && isImage) || isPython) {
        const _goArmsMenu = armsEnabled ? golangArmsMenu.enable : golangArmsMenu.disable;
        return _goArmsMenu;
      }
      if (isJava) {
        const isDragonwell17 = isEqual(get(appConfig, 'Jdk'), 'Dragonwell 17');
        if (isDragonwell17) {
          const monitor17 = get(feature, 'monitor17', false);
          if (monitor17) {
            return javaArmsMenu;
          }
        }
        return javaArmsMenu;
      }
      if (isPhp) {
        const phpEbpf = get(feature, 'php_ebpf', false);
        if (ebpf) {
          const _phpArmsMenu = phpEbpf ? ebpfArmsMenu : javaArmsMenu;
          return _phpArmsMenu;
        }
      }
      if (isPython || isOther) {
        if (ebpf) {
          return ebpfArmsMenu;
        }
      }
    }

    // 没有符合的任一条件 则不显示应用监控
    return null;
  }, [appConfig, feature]);

  const mscMenu = useMemo(() => {
    let _mscMenu = [];
    const { ahas_migrate = false } = feature;
    const isLatest = isEqual(get(appConfig, 'SaeVersion'), 'v2');
    const isPrevious = isEqual(get(appConfig, 'SaeVersion'), 'v1');
    const NewSaeVersion = get(appConfig, 'NewSaeVersion', '');

    const isNotOpenMse_saev1 = !handleV1IsOpenMsc(appConfig);

    if (
      NewSaeVersion === 'pro' ||
      ((!NewSaeVersion || NewSaeVersion === 'std') &&
        enableSaeStdVersionNewMse &&
        (isLatest || isNotOpenMse_saev1))
    ) {
      return v2mscMenu;
    }
    if (isLatest) {
      return v2mscMenu;
    }
    if (isPrevious) {
      // mse 和 ahas 限流降级
      const limitMenu = ahas_migrate ? mseLimitMenu : ahasLimitMenu;
      _mscMenu = [...v1mscMenu, limitMenu];
      return _mscMenu;
    }
    return _mscMenu;
  }, [appConfig, feature]);

  const navItems = useMemo(() => {
    let items = [
      {
        key: 'base',
        title: intl('saenext.micro-app.section.BasicInformation'),
      },
      {
        key: 'revision',
        title: intl('saenext.micro-app.section.ChangeRecord'),
      },
      {
        key: 'event',
        title: intl('saenext.micro-app.section.ApplicationEvents'),
      },
      {
        key: 'log',
        title: intl('saenext.micro-app.section.LogManagement'),
        items: [
          {
            key: 'realtime-log',
            title: intl('saenext.micro-app.section.RealTimeLog'),
          },
          {
            key: 'persist-log',
            title: intl('saenext.micro-app.section.PersistentLogs'),
          },
        ],
      },
      {
        key: 'monitor',
        title: intl('saenext.micro-app.section.BasicMonitoring'),
      },
      {
        key: 'prometheus-monitor',
        title: (
          <>
            {enablePrometheus && prometheusUrl ? (
              <a href={prometheusUrl} target="_blank">
                {intl('saenext.micro-app.section.PrometheusMonitoring')}

                <Icon type="external_link" size="small" className="ml-xs" />
              </a>
            ) : (
              intl('saenext.micro-app.section.PrometheusMonitoring')
            )}
          </>
        ),

        visible: get(appConfig, 'NewSaeVersion', '') !== 'lite',
      },
      {
        key: 'remote-debug',
        title: intl('saenext.micro-app.section.RemoteDebugging'),
      },
      {
        key: 'alarm',
        title: intl('saenext.micro-app.section.NotificationAlert'),
        items: [
          {
            key: 'alarm-rules',
            title: intl('saenext.micro-app.section.AlertRules'),
          },
          {
            key: 'alarm-dispatch',
            title: intl('saenext.micro-app.section.AlertSendingHistory'),
          },
          {
            key: 'alarm-incident',
            title: intl('saenext.micro-app.section.AlarmEventHistory'),
          },
          {
            key: 'alarm-contact',
            title: intl('saenext.micro-app.section.ContactManagement'),
          },
          {
            key: 'alarm-notify',
            title: intl('saenext.micro-app.section.NotificationPolicy'),
          },
        ],
      },
    ];

    // 如果 ProgrammingLanguage 为空 默认 java
    const isJava = isEqual(get(appConfig, 'ProgrammingLanguage', 'java'), 'java');
    const NewSaeVersion = get(appConfig, 'NewSaeVersion', '');

    if (NewSaeVersion === 'pro' || ((!NewSaeVersion || NewSaeVersion === 'std') && isJava)) {
      items.splice(6, 0, ...mscMenu);
    }

    if (!isEmpty(armsMenu)) {
      items.splice(5, 0, armsMenu);
    }

    return [...items];
  }, [appConfig, feature, enablePrometheus, prometheusUrl]);

  const { v1Link } = microAppLink(appId, regionId);

  const isNewMsc = useMemo(() => {
    const SaeVersion = get(appConfig, 'SaeVersion', '');
    const NewSaeVersion = get(appConfig, 'NewSaeVersion', '');
    const isNotOpenMse_saev1 = !handleV1IsOpenMsc(appConfig);
    const isJava = isEqual(get(appConfig, 'ProgrammingLanguage'), 'java');
    if (
      NewSaeVersion === 'pro' ||
      ((!NewSaeVersion || NewSaeVersion === 'std') &&
        enableSaeStdVersionNewMse &&
        isJava &&
        (SaeVersion === 'v2' || isNotOpenMse_saev1))
    ) {
      return true;
    } else {
      return false;
    }
  }, [appConfig, enableSaeStdVersionNewMse]);

  const microServiceEnable = useMemo(() => {
    const SaeVersion = get(appConfig, 'SaeVersion', '');
    const NewSaeVersion = get(appConfig, 'NewSaeVersion', '');
    const MicroserviceEngineConfig = get(appConfig, 'MicroserviceEngineConfig', '');
    const isNotOpenMse_saev1 = !handleV1IsOpenMsc(appConfig);
    const isJava = isEqual(get(appConfig, 'ProgrammingLanguage'), 'java');
    if (
      NewSaeVersion === 'pro' ||
      ((!NewSaeVersion || NewSaeVersion === 'std') &&
        enableSaeStdVersionNewMse &&
        isJava &&
        (SaeVersion === 'v2' || isNotOpenMse_saev1))
    ) {
      const enable = MicroserviceEngineConfig
        ? JSON.parse(MicroserviceEngineConfig)?.enable
        : false;
      return enable;
    } else {
      return false;
    }
  }, [appConfig, enableSaeStdVersionNewMse]);

  return (
    <Page
      title={
        <Copy text={title} style={{ paddingRight: 24 }}>
          {title}
        </Copy>
      }
      historyBack={`/${regionId}/app-list/micro`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/micro`,
          text: EnableWebApplication
            ? intl('saenext.app-list.micro.MicroserviceApplications')
            : intl('saenext.app-list.micro.ApplicationList'),
        },
        {
          to: `/${regionId}/app-list/${appId}/micro-app/base${search}`,
          text: `${title}`,
        },
        {
          text: `${titleMenu.get(section)}`,
        },
      ]}
      extra={
        CachedData.isSinSite() ||
        isSupportMultiVersions ||
        (!saeOldConsoleEnable && moment().isAfter('2025-05-31 23:59:59')) ? null : (
          <LinkButton
            onClick={() => {
              const dialog = Dialog.confirm({
                title: intl('saenext.returnTo.oldConsole'),
                content: (
                  <>
                    {intl.html('saenext.oldConsole.OfflineAnnouncement', {
                      href: CachedData.confLink('help:sae:old-console-offline-announcement'),
                    })}
                  </>
                ),

                style: {
                  width: 480,
                },
                onOk: () => {
                  window.open(v1Link, '_blank');
                },
                onCancel: () => {
                  dialog.hide();
                },
              });
            }}
          >
            {intl('saenext.micro-app.section.ReturnToThePreviousConsole')}
          </LinkButton>
        )
      }
      nav={{
        shape: 'menu',
        defaultActiveKey: 'base',
        activeKey: section,
        onChange: (value) => {
          if (value !== 'realtime-log' && getParams('podId')) {
            removeParams('podId');
          }
          if (getParams('targetPart')) {
            removeParams('targetPart');
          }
          if (enablePrometheus && value === 'prometheus-monitor') {
            return;
          }
          history.push(
            `/${regionId}/app-list/${appId}/micro-app/${value}${window.location.search}`,
          );
        },
        items: navItems,
      }}
      // @ts-ignore
      className="custom-page-layout"
    >
      <MicroAppContext.Provider
        value={{
          appConfig,
          appStatus,
          instanceGroup,
          setInstanceGroup,
          resourceQuota,
          scaleRules,
          setScaleRules,
          scaleRuleCounts,
          setScaleRuleCounts,
          scaleRuleEnabled,
          setScaleRuleEnabled,
          scaleRef,
          slbQpsRt,
          setSlbQpsRt,
          type,
          setType,
          mseVersion,
          mscUpdateStatus,
          slbQpsRtPermission,
          setSlbQpsRtPermission,
        }}
      >
        {(!section || section === 'base') && (
          <Suspense fallback={<LoadingSkeleton />}>
            <BaseInfo
              {...props}
              appConfig={appConfig}
              appStatus={appStatus}
              isArmsMonitor={isArmsMonitor}
              refresh={refresh}
              isMultipleNewAccount={isMultipleNewAccount}
              isSupportMultiVersions={isSupportMultiVersions}
              appAddonReleaseInfo={appAddonReleaseInfo}
              hasCmsPermission={hasCmsPermission}
            />
          </Suspense>
        )}

        {section === 'deploy' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <Deploy
              {...props}
              appConfig={appConfig}
              appStatus={appStatus}
              appAddonReleaseInfo={appAddonReleaseInfo}
              refresh={refresh}
              hasCmsPermission={hasCmsPermission}
            />
          </Suspense>
        )}

        {section === 'instance' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <InstanceDetail
              {...props}
              appConfig={appConfig}
              appStatus={appStatus}
              refresh={refresh}
            />
          </Suspense>
        )}

        {(!section || section === 'revision') && (
          <Suspense fallback={<LoadingSkeleton />}>
            <ChangeOrderList {...props} appId={appId} regionId={regionId} />
          </Suspense>
        )}

        {(!section || section === 'event') && (
          <Suspense fallback={<LoadingSkeleton />}>
            <EventList {...props} regionId={regionId} namespaceId={namespaceId} appId={appId} />
          </Suspense>
        )}

        {section === 'realtime-log' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <RealtimeLog
              regionId={regionId}
              appId={appId}
              appConfig={appConfig}
              podId={getParams('podId')}
              containerId={getParams('containerId')}
            />
          </Suspense>
        )}

        {section === 'persist-log' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <PersistLog appId={appId} regionId={regionId} />
          </Suspense>
        )}

        {section === 'monitor' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <BaseMonitor {...props} appId={appId} regionId={regionId} />
          </Suspense>
        )}

        {/* golang 未开启 */}
        {section === 'arms' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <ArmsGoMonitor
              lang={get(appConfig, 'ProgrammingLanguage')}
              packageType={get(appConfig, 'PackageType')}
              redirectDeploy={() => {
                history.push(
                  `/${regionId}/app-list/${appId}/micro-app/base?name=${title}&action=deploy`,
                );
              }}
            />
          </Suspense>
        )}

        {section === 'arms-monitor' && (
          <Suspense fallback={<LoadingSkeleton />}>
            {/* golang 已开启 */}
            {(!appConfig?.NewSaeVersion || appConfig?.NewSaeVersion === 'std') && (
              <ArmsGoOverview appId={appId} regionId={regionId} />
            )}
            {/* 专业版应用监控 */}
            {appConfig?.NewSaeVersion === 'pro' && (
              <ArmsMonitor appConfig={appConfig} regionId={regionId} history={history} />
            )}
          </Suspense>
        )}

        {/* java 应用监控 */}
        {section === 'arms-overview' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <ArmsJavaOverview appId={appId} regionId={regionId} />
          </Suspense>
        )}

        {section === 'arms-app' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <ArmsJavaSpecifics appId={appId} regionId={regionId} />
          </Suspense>
        )}

        {section === 'arms-interface' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <ArmsJavaInterface appId={appId} regionId={regionId} />
          </Suspense>
        )}

        {section === 'arms-senior' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <ArmsJavaSenior
              appId={appId}
              regionId={regionId}
              appStatus={appStatus}
              refresh={refresh}
            />
          </Suspense>
        )}

        {/* ebpf 应用监控 */}
        {section === 'arms-ebpf' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <ArmsEbpfMonitor appId={appId} appName={title} regionId={regionId} />
          </Suspense>
        )}

        {section === 'prometheus-monitor' && !get(appConfig, 'EnablePrometheus', false) && (
          <Suspense fallback={<LoadingSkeleton />}>
            <PrometheusMonitorNotEnable appConfig={appConfig} appAddonReleaseInfo={appAddonReleaseInfo} refresh={refresh} regionId={regionId} history={history} hasCmsPermission={hasCmsPermission} />
          </Suspense>
        )}

        {section === 'msc-overview' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MscOverview
              {...props}
              regionId={regionId}
              appId={appId}
              appConfig={appConfig}
              appStatus={appStatus}
              NewSaeVersion={appConfig?.NewSaeVersion || ''}
              microServiceEnable={microServiceEnable}
              isNewMsc={isNewMsc}
            />
          </Suspense>
        )}

        {section === 'msc-interface' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MscInterface
              {...props}
              regionId={regionId}
              appId={appId}
              appConfig={appConfig}
              appStatus={appStatus}
              NewSaeVersion={appConfig?.NewSaeVersion || ''}
              microServiceEnable={microServiceEnable}
              isNewMsc={isNewMsc}
            />
          </Suspense>
        )}

        {section === 'msc-node' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MscNode
              {...props}
              regionId={regionId}
              appId={appId}
              appConfig={appConfig}
              appStatus={appStatus}
              NewSaeVersion={appConfig?.NewSaeVersion || ''}
              microServiceEnable={microServiceEnable}
              isNewMsc={isNewMsc}
            />
          </Suspense>
        )}

        {section === 'msc-traffic' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MscTraffic
              {...props}
              regionId={regionId}
              appId={appId}
              appConfig={appConfig}
              appStatus={appStatus}
              NewSaeVersion={appConfig?.NewSaeVersion || ''}
              microServiceEnable={microServiceEnable}
              isNewMsc={isNewMsc}
              albGray={get(feature, 'albGray', false)}
            />
          </Suspense>
        )}

        {section === 'msc-service' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MscService {...props} regionId={regionId} applicationID={appId} />
          </Suspense>
        )}

        {section === 'msc-rules' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MscGrayRules
              {...props}
              appId={appId}
              regionId={regionId}
              appConfig={appConfig}
              albGray={get(feature, 'albGray', false)}
            />
          </Suspense>
        )}

        {section === 'ahas-overview' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AhasOverview {...props} appId={appId} regionId={regionId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'ahas-monitor' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AhasMonitor {...props} appId={appId} regionId={regionId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'ahas-rules' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AhasRules {...props} appId={appId} regionId={regionId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'ahas-log' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AhasLog {...props} appId={appId} regionId={regionId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'mse-overview' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MseOverview {...props} regionId={regionId} appId={appId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'mse-interface' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MseInterface {...props} regionId={regionId} appId={appId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'mse-node' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MseNode {...props} regionId={regionId} appId={appId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'mse-rules' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <MseRules {...props} regionId={regionId} appId={appId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'remote-debug' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <RemoteDebug {...props} regionId={regionId} applicationID={appId} />
          </Suspense>
        )}

        {section === 'alarm-rules' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AlarmRules {...props} regionId={regionId} appId={appId} appConfig={appConfig} />
          </Suspense>
        )}

        {section === 'alarm-dispatch' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AlarmDispatch {...props} regionId={regionId} appId={appId} />
          </Suspense>
        )}

        {section === 'alarm-incident' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AlarmIncident {...props} regionId={regionId} appId={appId} />
          </Suspense>
        )}

        {section === 'alarm-contact' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AlarmContact {...props} regionId={regionId} appId={appId} />
          </Suspense>
        )}

        {section === 'alarm-notify' && (
          <Suspense fallback={<LoadingSkeleton />}>
            <AlarmNotify {...props} regionId={regionId} appId={appId} />
          </Suspense>
        )}
      </MicroAppContext.Provider>
    </Page>
  );
};

export default AppDetail;
