import React, { useState, useEffect } from 'react';
import { Page, intl, Loading } from '@ali/cnd';
import WebAppCreator from '~/components/app-create/web-app';
import MicroAppCreator from '~/components/app-create/micro-app';
import services from '~/services';
import { getParams } from '~/utils/global';
import { get } from 'lodash';
import { DEPLOY_TYPE } from '~/constants/application';
import { jsonParse } from '~/utils/transfer-data';
import { parseHostsWeb } from '~/components/shared/CustomHostField/utils';

export default (props) => {
  const {
    match: {
      params: { regionId, id: applicationID, source },
    },
    history,
  } = props;
  const applicationName = getParams('name');
  const [isLoading, setIsLoading] = useState(true);
  const [appValue, setAppValue] = useState({});

  useEffect(() => {
    if (source !== 'micro' && source !== 'web') {
      history.replace('/');
    }
  }, [source]);

  useEffect(() => {
    if (source === 'web') {
      getWebApplication();
    } else if (source === 'micro') {
      getMicroApplication();
    }
  }, [regionId]);

  async function getWebApplication() {
    setIsLoading(true);
    const _appData = await services.getWebApplication({
      applicationID,
      applicationName,
    });

    const _webAppValue = {
      basicCopyValue: {
        applicationName: `${_appData?.applicationName}-copy`,
        description: _appData?.description,
        appType: 'web',
        namespaceID: _appData?.namespaceID,
        disableURLInternet: get(_appData, 'httpTriggerConfig.disableURLInternet'),
        authType: get(_appData, 'httpTriggerConfig.authType'),
        caPort: _appData?.caPort,
        timeout: _appData?.timeout,
        spec: {
          cpu: _appData?.cpu,
          memory: _appData?.memorySize / 1024,
        },
        alwaysAllocateCPU: get(_appData, 'scaleConfig.alwaysAllocateCPU', false),
        instanceConcurrency: _appData?.instanceConcurrency,
        scaleConfig: {
          start: get(_appData, 'scaleConfig.minimumInstanceCount') || 2,
          end: !get(_appData, 'scaleConfig.minimumInstanceCount', 0)
            ? 10
            : get(_appData, 'scaleConfig.maximumInstanceCount', 10),
        },
      },
      advanceCopyValue: {
        startCmd: {
          Command: JSON.parse(get(_appData, 'command') || '[]')[0],
          CommandArgs: JSON.parse(get(_appData, 'args') || '[]'),
        },
        logConfig: _appData?.logConfig,
        logTupleConfig: {
          slsConfig: get(_appData, 'slsConfig', null),
          logConfig: get(_appData, 'logConfig', null),
        },
        environmentVariables: _appData?.environmentVariables,
        hostsArr: parseHostsWeb(_appData.customHostAlias),
        armsConfig: {
          enableAppMetric: Boolean(_appData?.enableAppMetric),
          programmingLanguage: get(_appData, 'programmingLanguage', 'java'),
        },
        healthCheckConfig: {
          startupProbe: get(_appData, 'startupProbe', null),
          livenessProbe: get(_appData, 'livenessProbe', null),
        },
        vpcConfig: {
          ...get(_appData, 'vpcConfig', {}),
          enableVpc: !!(
            get(_appData, 'vpcConfig.vpcId', '') &&
            get(_appData, 'vpcConfig.vSwitchIds', []).length &&
            get(_appData, 'vpcConfig.securityGroupId', '')
          ),

          internetAccess: get(_appData, 'internetAccess', true),
        },
        nasConfig: get(_appData, 'nasConfig', {}),
        ossMountConfig: get(_appData, 'ossMountConfig', {}),
      },
    };

    setAppValue(_webAppValue);
    setIsLoading(false);
  }

  async function getMicroApplication() {
    setIsLoading(true);
    const { Data: _appData = {} } =
      (await services.describeMicroApplicationConfig({
        AppId: applicationID,
      })) || {};

    const {
      AppName,
      AppDescription,
      NamespaceId,
      VpcId,
      VSwitchId,
      SecurityGroupId,
      Cpu,
      Memory,
      Replicas,
      PackageType,
      ImageUrl,
      AcrInstanceId,
      AcrAssumeRoleArn,
      EnableImageAccl,
      ImagePullSecrets,
      ProgrammingLanguage,
      Jdk,
      Php,
      Python,
      Dotnet,
      PackageUrl,
      PackageVersion,
      Timezone,
      TomcatConfig,
      WebContainer,
      EdasContainerVersion,
      PhpExtensions,
      PhpPECLExtensions,
      PythonModules,
      PackageRuntimeCustomBuild,
      PhpConfig,
      PhpConfigLocation,
      Command,
      CommandArgs,
      ...advanceValue
    } = _appData;

    const regexImage = /^Image/i;

    const _microAppValue = {
      basicCopyValue: {
        applicationName: `${AppName}-copy`,
        description: AppDescription,
        appType: 'micro',
        namespaceConfig: {
          NamespaceId,
          VpcId,
          VSwitchId,
          SecurityGroupId,
        },
        deployConfig: {
          type: regexImage.test(PackageType) ? DEPLOY_TYPE.IMAGE : DEPLOY_TYPE.MICRO_PACKAGE,
          image: ImageUrl,
          instanceId: AcrInstanceId,
          acrAssumeRoleArn: AcrAssumeRoleArn,
          enableImageAccl: EnableImageAccl,
          imagePullSecrets: ImagePullSecrets,
          vpc: AcrInstanceId ? VpcId : undefined,
          ProgrammingLanguage,
          PackageType,
          Jdk,
          Php,
          Python,
          Dotnet,
          uploadType: 'url',
          PackageUrl,
          PackageVersion,
          Timezone,
          TomcatConfig,
          WebContainer,
          EdasContainerVersion,
          PhpExtensions: jsonParse(PhpExtensions),
          PhpPECLExtensions: jsonParse(PhpPECLExtensions),
          PythonModules: jsonParse(PythonModules),
          PackageRuntimeCustomBuild,
          PhpConfig,
          PhpConfigLocation,
          Command,
          CommandArgs,
        },
        spec: {
          cpu: Cpu / 1000,
          memory: Memory / 1024,
        },
        replicas: Replicas,
      },
      Command,
      CommandArgs,
      ...advanceValue,
    };
    setAppValue(_microAppValue);
    setIsLoading(false);
  }

  return (
    <Page
      title={intl('saenext.copy-app.source.CopyApplication')}
      historyBack={`/${regionId}/app-list/${source}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/app-list/${source}`,
          text:
            source === 'micro'
              ? intl('saenext.copy-app.source.MicroserviceApplications')
              : intl('saenext.copy-app.source.WebApplications'),
        },
        {
          text: intl('saenext.copy-app.source.CopyApplication'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <Loading visible={isLoading} style={{ width: '100%', height: '100%' }}>
        {
          source === 'micro' ? (
            <MicroAppCreator {...props} appValue={appValue} />
          ) : (
            <WebAppCreator {...props} appValue={appValue} />
          )
        }
      </Loading>
    </Page>
  );
};
