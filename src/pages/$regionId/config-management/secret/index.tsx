import React, { useState, useEffect } from 'react';
import { intl, Page } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';
import SecretList from '~/components/namespace/secret';

const SecretPage = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;
  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : regionId;

  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);
  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
  };
  return (
    <Page
      key={regionId}
      title={intl('saenext.config-management.secret.Secret')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
          disabledAll={true}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.config-management.secret.Secret'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <SecretList regionId={regionId} namespaceId={namespaceId} />
    </Page>
  );
};

export default SecretPage;
