import React, { useState, useEffect } from 'react';
import { intl, Page, Tab } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';
import AcmList from '~/components/acm/AcmList';
import AcmHistory from '~/components/acm/AcmHistory';
import AcmMonitor from '~/components/acm/AcmMonitor';
import AcmTrail from '~/components/acm/AcmTrail';

const AcmManagement = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;
  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : regionId;

  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);
  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
  };
  return (
    <Page
      key={regionId}
      title={intl('saenext.config-management.acm.DistributedConfigurationAcm')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
          disabledAll={true}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.config-management.acm.DistributedConfigurationAcm'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <Tab unmountInactiveTabs>
        <Tab.Item title={intl('saenext.config-management.acm.ConfigurationList')} key="acm-list">
          <div className="mt-l">
            <AcmList regionId={regionId} namespaceId={namespaceId} history={history} />
          </div>
        </Tab.Item>
        <Tab.Item title={intl('saenext.config-management.acm.VersionHistory')} key="acm-history">
          <div className="mt-l">
            <AcmHistory regionId={regionId} namespaceId={namespaceId} history={history} />
          </div>
        </Tab.Item>
        <Tab.Item title={intl('saenext.config-management.acm.ListenerQuery')} key="acm-monitor">
          <div className="mt-l">
            <AcmMonitor regionId={regionId} namespaceId={namespaceId} history={history} />
          </div>
        </Tab.Item>
        <Tab.Item title={intl('saenext.config-management.acm.PushTrack')} key="acm-trail">
          <div className="mt-l">
            <AcmTrail regionId={regionId} namespaceId={namespaceId} history={history} />
          </div>
        </Tab.Item>
      </Tab>
    </Page>
  );
};

export default AcmManagement;
