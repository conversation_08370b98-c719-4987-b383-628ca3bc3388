import React from 'react';
import { intl, Page } from '@ali/cnd';
import { getParams } from '~/utils/global';
import AcmDetail from '~/components/acm/AcmDetail';
const AcmDetailPage = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
    location: { search },
  } = props;
  const namespaceId = getParams('namespaceId') || regionId;

  return (
    <Page
      title={intl('saenext.section.acm-detail.ConfigurationDetails')}
      historyBack={`/${regionId}/config-management/acm?namespaceId=${namespaceId}`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/namespace`,
          text: intl('saenext.acm.acm-detail.DistributedConfigurationAcm'),
        },
        {
          text: intl('saenext.section.acm-detail.ConfigurationDetails'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <AcmDetail
        component="ConfigurationDetail"
        regionId={regionId}
        namespaceId={namespaceId}
        history={history}
      />
    </Page>
  );
};

export default AcmDetailPage;
