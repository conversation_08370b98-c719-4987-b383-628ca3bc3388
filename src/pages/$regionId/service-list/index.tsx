import React, { useEffect, useRef, useState } from 'react';
import { intl, Page, CndTable, Copy, Link, Actions, LinkButton } from '@ali/cnd';
import services from '~/services';
import { getParams, setSearchParams } from '~/utils/global';
import { get, isEmpty, map } from 'lodash';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';
import ServiceMeta from '~/components/app-detail/micro-app/msc/ServiceMeta';

const ServiceListPage = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;
  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : regionId;
  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    setNamespaceId(_namespaceId);
    setSearchParams({ namespaceId: _namespaceId });
  }, [regionId]);

  useEffect(() => {
    setRefreshIndex(Date.now());
  }, [namespaceId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
  };
  const fetchData = async (params) => {
    const { current = 1, pageSize = 10, ServiceName = '', AppName = '' } = params;
    const res = await services.ListNamespacedAppServices({
      params: {
        NamespaceId: !namespaceId || namespaceId === 'all' ? '' : namespaceId,
      },
    });
    let serviceList = get(res, 'Data', []);
    if (ServiceName || AppName) {
      serviceList = serviceList.filter(
        (item) => item.ServiceName.includes(ServiceName) && item.AppName.includes(AppName),
      );
    }
    let currentPageServices = serviceList.slice((current - 1) * pageSize, current * pageSize);
    const currentAppId = Array.from(new Set(map(currentPageServices, (item) => item.AppId)));
    const currentAppEnabledMsc = await Promise.all(
      currentAppId.map((item) => {
        return services.describeMicroApplicationConfig({
          AppId: item,
        });
      }),
    );
    let appEnabledMscMap = {};
    map(currentAppEnabledMsc, (item) => {
      Reflect.set(
        appEnabledMscMap,
        item.Data.AppId,
        !isEmpty(get(item, 'Data.MseApplicationId', '')),
      );
    });
    currentPageServices = map(currentPageServices, (item) => {
      return {
        ...item,
        enableMsc: appEnabledMscMap[item.AppId] || false,
      };
    });
    return {
      data: currentPageServices,
      total: serviceList.length,
    };
  };

  const search = {
    defaultDataIndex: 'ServiceName',
    defaultSelectedDataIndex: 'ServiceName',
    // onlySupportOne: true,
    options: [
      {
        label: intl('saenext.regionId.service-list.ServiceName'),
        dataIndex: 'ServiceName',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.regionId.service-list.EnterAServiceName'),
        },
      },
      {
        label: intl('saenext.regionId.service-list.ApplicationName'),
        dataIndex: 'AppName',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.regionId.service-list.EnterAnApplicationName'),
        },
      },
    ],
  };

  const columns = [
    {
      key: 'ServiceName',
      title: intl('saenext.components.msc.ServiceList.ServiceName'),
      dataIndex: 'ServiceName',
      cell: (value) => {
        return <Copy text={value}>{value}</Copy>;
      },
    },
    {
      key: 'AppName',
      title: intl('saenext.regionId.service-list.Application'),
      dataIndex: 'AppName',
      cell: (value, index, record) => {
        return (
          <Copy text={value}>
            <Link to={`/${regionId}/app-list/${record.AppId}/micro-app/base`}>{value}</Link>
          </Copy>
        );
      },
    },
    {
      key: 'ServiceType',
      title: intl('saenext.components.msc.ServiceList.ServiceType'),
      dataIndex: 'ServiceType',
      cell: (value) => <div>{value || '-'}</div>,
    },
    {
      key: 'ServiceVersion',
      title: intl('saenext.components.msc.ServiceList.VersionNumber'),
      dataIndex: 'ServiceVersion',
      cell: (value) => <div>{value || '-'}</div>,
    },
    {
      key: 'ServiceGroup',
      title: intl('saenext.components.msc.ServiceList.Group'),
      dataIndex: 'ServiceGroup',
      cell: (value) => <div>{value || '-'}</div>,
    },
    {
      key: 'operations',
      title: intl('saenext.components.msc.ServiceList.Operation'),
      width: 120,
      cell: (value, index, record) => {
        return (
          <Actions>
            <ServiceMeta record={record} applicationID={record?.AppId}>
              <LinkButton disabled={!record?.enableMsc}>
                {intl('saenext.components.msc.ServiceList.Metadata')}
              </LinkButton>
            </ServiceMeta>
          </Actions>
        );
      },
    },
  ];

  return (
    <Page
      key={regionId}
      title={intl('saenext.regionId.service-list.ServiceList')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
          disabledAll={true}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.regionId.service-list.InstanceList'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <CndTable
        fetchData={fetchData}
        showRefreshButton
        refreshIndex={refreshIndex}
        search={search}
        columns={columns}
      />
    </Page>
  );
};
export default ServiceListPage;
