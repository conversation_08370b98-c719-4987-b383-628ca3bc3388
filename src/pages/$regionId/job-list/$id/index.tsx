import React, { useEffect, useState } from 'react';
import { intl, Page, Loading } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import CachedData from '~/cache/common';
import { useHistory } from 'react-router-dom';
import services from '~/services';

const ConsoleNavHeight = 50;
const BreadcrumbHeight = 52;
const PageNavHeight = 52;
const ReservedSpaceHeight = 16;

const isPre = CachedData.isPre();

const JobDetail = props => {
  const {
    match: {
      params: { regionId,id },
    },
  } = props;
  const history = useHistory();
  const appId = id || '';
  const appName = getParams('appName') || '';
  const namespaceId = getParams('namespaceId') || '';
  const page = getParams('page') || 'JobDetail';
  const iframeHeight =
    document.documentElement.clientHeight -
    ConsoleNavHeight -
    BreadcrumbHeight -
    PageNavHeight -
    ReservedSpaceHeight;
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getJobDetail();
    setLoading(true);
  }, []);

  useEffect(() => {
    window.addEventListener('message', handleToJobList);
    return () => {
      window.removeEventListener('message', handleToJobList);
    };
  }, [history]);

  const iframeLoaded = () => {
    setTimeout(()=>{
      setLoading(false);
    })
  };

  const handleToJobList=(event: MessageEvent)=>{
    const isPre = window.location.host?.startsWith('pre-');
    const origin = isPre
    ? CachedData.confLink('feature:pre-sae:url')
    : CachedData.confLink('feature:sae:url');
    if (event.origin !== origin){
      return;
    }
    if (event.data === 'cancel') {
      history.push(`/${regionId}/job-list`);
    }
    if (event?.data?.action === 'toJobDetail') {
      const { jobInfo } = event?.data;
      if(jobInfo?.id){
        history.push(`/${regionId}/job-list/${jobInfo.id}/base`);
      }else{
        history.push(`/${regionId}/job-list`);
      }
    }
  }

  const getJobDetail = async () => {
    const { Data } = await services.DescribeJob({
      params: {
        AppId: appId,
      },
    });
    if (!Data) return;
    const { AppName, NamespaceId } = Data;
    setSearchParams({
      appName: AppName,
      namespaceId: NamespaceId
    })
  };

  return (
    <Page
      title={appName}
      historyBack={`/${regionId}/job-list`}
      // @ts-ignore
      className="custom-page-layout"
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/job-list`,
          text: intl('saenext.regionId.job-list.TaskTemplateList'),
        },
        {
          text: appName,
        },
      ]}
    >
      <Loading visible={loading} style={{ width: '100%', position: 'relative' }}>
        <iframe
          width="100%"
          height={iframeHeight}
          scrolling="no"
          frameBorder={0}
          onLoad={iframeLoaded}
          src={`${
            isPre
              ? CachedData.confLink('feature:pre-sae:url')
              : CachedData.confLink('feature:sae:url')
          }/#/JobList/${page}?appId=${appId}&appName=${appName}&regionId=${regionId}&namespaceId=${namespaceId}&hideSidebar=true&iframeMode=true`}
        />
      </Loading>
    </Page>
  );
};

export default JobDetail;
