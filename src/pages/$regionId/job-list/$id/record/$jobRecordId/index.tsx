import React from 'react';
import { intl, Copy, Page } from '@ali/cnd';
import ExecutionRecordDetail from '~/components/job-list/ExecutionRecordDetail';

const JobRecordDetail = (props) => {
  const {
    match: {
      params: { regionId, id: appId, jobRecordId: recordId },
    },
    history,
    location: { search }, // ?后参数
  } = props;

  return (
    <Page
      title={<Copy text={recordId}>{recordId}</Copy>}
      historyBack={`/${regionId}/job-list/${appId}/record`}
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/job-list`,
          text: intl('saenext.record.jobRecordId.TaskTemplateList'),
        },
        {
          to: `/${regionId}/job-list/${appId}/base`,
          text: intl('saenext.record.jobRecordId.TaskTemplateDetails'),
        },
        {
          to: `/${regionId}/job-list/${appId}/record`,
          text: intl('saenext.record.jobRecordId.TaskRecord'),
        },
        {
          text: intl('saenext.record.jobRecordId.TaskRecordDetails'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <ExecutionRecordDetail {...props} regionId={regionId} appId={appId} jobId={recordId} />
    </Page>
  );
};

export default JobRecordDetail;
