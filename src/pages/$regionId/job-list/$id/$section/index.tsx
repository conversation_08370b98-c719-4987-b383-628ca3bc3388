import React, { useEffect, useRef, useState } from 'react';
import { Switch, Route } from 'react-router-dom';
import { intl, Page, Copy } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import services from '~/services';
import { find, flatMap, get, isEmpty, isEqual } from 'lodash';
import JobContext from '~/utils/jobContext';
import BasicInfo from '~/components/job-detail/basic-info';
import EventList from '~/components/shared/EventList';
import RealtimeLog from '~/components/app-detail/micro-app/log-control/RealtimeLog';
import PersistLog from '~/components/app-detail/micro-app/log-control/PersistLog';
import BaseMonitor from '~/components/app-monitor/micro-app';
import ExecutionRecord from '~/components/job-list/ExecutionRecord';
import JobActionBar from '~/components/job-detail/basic-info/JobActionBar';

const JobDetail = (props) => {
  const {
    match: {
      params: { regionId, id: jobId, section },
    },
    history,
    location: { search },
  } = props;

  const [jobConfig, setJobConfig] = useState<any>({});
  const [jobStatus, setJobStatus] = useState<any>({});
  const [lastChangeOrder, setLastChangeOrder] = useState<any>({});
  const [isOnlyShenZhenA, setIsOnlyShenZhenA] = useState(false);

  const title =
    getParams('name') || jobConfig.AppName || intl('saenext.micro-app.section.ApplicationDetails');

  const refreshRef = useRef(null);

  const refreshJob = () => {
    getJobDetail();
    getJobStatus();
    getJobOrderStatus();
  };

  useEffect(() => {
    refreshRef.current = refreshJob;
  });

  useEffect(() => {
    refreshRef.current();

    const interval = setInterval(() => {
      refreshRef.current();
    }, 10000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const getJobDetail = async () => {
    const data = await services.DescribeJob({
      params: {
        AppId: jobId,
      },
    });
    if (!data) return;
    const { Data } = data;
    if (isEmpty(Data) || isEqual(jobConfig, Data)) return;
    setJobConfig(Data);
    setSearchParams({
      name: Data.AppName,
    });
  };

  const getJobStatus = async () => {
    const data = await services.DescribeJobStatus({
      params: {
        AppId: jobId,
      },
    });
    if (!data) return;
    const { Data } = data;
    if (isEmpty(Data) || isEqual(jobStatus, Data)) return;
    setJobStatus(Data);
  };

  const getJobOrderStatus = async () => {
    const data = await services.ListChangeOrders({
      params: {
        AppId: jobId,
        CurrentPage: 1,
        PageSize: 1,
      },
    });
    if (!data) return;
    const { Data } = data;
    const _lastChangeOrder = get(Data, 'ChangeOrderList.[0]');
    if (isEmpty(_lastChangeOrder) || isEqual(_lastChangeOrder, lastChangeOrder)) return;
    setLastChangeOrder(_lastChangeOrder);
  };

  const NAV_ITEMS = [
    {
      key: 'base',
      title: intl('saenext.id.section.TaskTemplateDetails'),
      render: () => <BasicInfo />,
    },
    {
      key: 'record',
      title: intl('saenext.id.section.TaskRecord'),
      render: () => (
        <ExecutionRecord
          {...props}
          regionId={regionId}
          namespaceId={jobConfig.NamespaceId}
          appId={jobId}
        />
      ),
    },
    {
      key: 'event',
      title: intl('saenext.id.section.EventInformation'),
      render: () => (
        <EventList
          {...props}
          regionId={regionId}
          namespaceId={jobConfig.NamespaceId}
          appId={jobId}
        />
      ),
    },
    {
      key: 'log',
      title: intl('saenext.micro-app.section.LogManagement'),
      items: [
        {
          key: 'realtime-log',
          title: intl('saenext.micro-app.section.RealTimeLog'),
          render: () => <RealtimeLog regionId={regionId} appId={jobId} appConfig={jobConfig} />,
        },
        {
          key: 'persist-log',
          title: intl('saenext.micro-app.section.PersistentLogs'),
          render: () => <PersistLog appId={jobId} regionId={regionId} showKafka={false} />,
        },
      ],
    },
    {
      key: 'monitor',
      title: intl('saenext.micro-app.section.BasicMonitoring'),
      render: () => <BaseMonitor {...props} appId={jobId} regionId={regionId} />,
    },
  ];

  const SECTION_ITEMS = flatMap(NAV_ITEMS, (item) => item.items || item) as any[];

  return (
    <Page
      title={
        <Copy text={title} className="pr-xl">
          {title}
        </Copy>
      }
      titleExtra={
        section === 'base' && (
          <JobContext.Provider
            value={{
              jobConfig,
              jobStatus,
              lastChangeOrder,
              refreshJob,
              isOnlyShenZhenA,
              setIsOnlyShenZhenA,
            }}
          >
            <JobActionBar regionId={regionId} jobId={jobId} refresh={refreshJob} />
          </JobContext.Provider>
        )
      }
      historyBack={`/${regionId}/job-list`}
      // @ts-ignore
      className="custom-page-layout"
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          to: `/${regionId}/job-list`,
          text: intl('saenext.regionId.job-list.TaskTemplateList'),
        },
        {
          to: `/${regionId}/job-list/${jobId}/base`,
          text: title,
        },
        {
          text: find(SECTION_ITEMS, { key: section })?.title || '',
        },
      ]}
      nav={{
        shape: 'menu',
        defaultActiveKey: NAV_ITEMS[0].key,
        activeKey: section,
        items: NAV_ITEMS,
        onChange: (value) => {
          history.push(`/${regionId}/job-list/${jobId}/${value}`);
        },
      }}
    >
      <JobContext.Provider
        value={{
          jobConfig,
          jobStatus,
          lastChangeOrder,
          refreshJob,
          isOnlyShenZhenA,
          setIsOnlyShenZhenA,
        }}
      >
        <Switch>
          {SECTION_ITEMS.map((item) => {
            const { key, ...rest } = item;
            return <Route key={key} path={`/:regionId/job-list/:id/${key}`} exact {...rest} />;
          })}
        </Switch>
      </JobContext.Provider>
    </Page>
  );
};

export default JobDetail;
