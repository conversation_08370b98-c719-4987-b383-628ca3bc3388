import React, { useEffect, useRef, useState } from 'react';
import { intl, Page } from '@ali/cnd';
import CachedData from '~/cache/common';
import { inDebtHandle } from '~/utils/accountHandle';
import JobList from '~/components/job-list';
// import NamespaceTag from '~/components/shared/NamespaceTag';
import { getParams, setSearchParams } from '~/utils/global';
import NamespaceSelector, { NAMESPACE_KEY } from '~/components/shared/NamespaceSelector';

const JobListPage = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;

  const storageNsId = sessionStorage.getItem(NAMESPACE_KEY);
  const paramsNsId = getParams('namespaceId');
  const defaultNsId = paramsNsId || storageNsId;
  const _namespaceId = defaultNsId && defaultNsId.startsWith(regionId) ? defaultNsId : 'all';

  const [namespaceId, setNamespaceId] = useState(_namespaceId);
  const [namespaceData, setNamespaceData] = useState([]);
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    setNamespaceId(namespaceId);
    setSearchParams({ namespaceId: namespaceId });
  }, [regionId]);

  const onNamespaceChange = (val) => {
    setNamespaceId(val);
    setRefreshIndex(Date.now());
  };

  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const inDebtOverdue = CachedData.getOpenStatus('inDebtOverdueStatus');

  useEffect(() => {
    // 欠费 但未超期
    isInDebt && !inDebtOverdue && inDebtHandle();
  }, [isInDebt]);

  return (
    <Page
      key={regionId}
      title={intl('saenext.regionId.job-list.TaskTemplateList')}
      titleExtraAlign="left"
      titleExtra={
        <NamespaceSelector
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
          className="ml"
          style={{ width: 250 }}
        />
      }
      breadcrumbs={[
        {
          to: '/',
          text: intl('title.home'),
        },
        {
          text: intl('saenext.regionId.job-list.TaskTemplateList'),
        },
      ]}
      // @ts-ignore
      className="custom-page-layout"
    >
      <>
        {/* <NamespaceTag
          ref={tagRef}
          value={namespaceId}
          onChange={onNamespaceChange}
          setNamespaceData={setNamespaceData}
        /> */}

        <JobList history={props.history} namespaceList={namespaceData} namespaceId={namespaceId} key={refreshIndex} v1Micro />
      </>
    </Page>
  );
};

export default JobListPage;
