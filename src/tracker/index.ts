import getGcLevel from '../utils/getGcLevel';

import AES from "@ali/aes-tracker";
import AESPluginEvent from "@ali/aes-tracker-plugin-event";
import AESPluginPV from "@ali/aes-tracker-plugin-pv";
import AESPluginAutolog from "@ali/aes-tracker-plugin-autolog";
import AESPluginJSError from "@ali/aes-tracker-plugin-jserror";
import AESPluginAPI from "@ali/aes-tracker-plugin-api";
import AESPluginResourceError from "@ali/aes-tracker-plugin-resourceError";
import AESPluginEmogine from '@ali/aes-tracker-plugin-emogine';
import { AplusWeb } from '@ali/aplus_universal/plugins/web.js';
import AES_CONSTANT, { ITrackResource, ITrackCore, ITrackOpt, ITrackRequestErr, ITrackWhiteScreenErr, ITrackMicroAppMsc } from './constant';
import { convertUrlPathname, convertUrlSearch } from './utils';
import { find, forEach, get } from 'lodash';

const config = window.ALIYUN_CONSOLE_CONFIG;
const aes = new AES({
  pid: "743ifa",
  user_type: 6,
  env: config.fEnv ? config.fEnv : 'prod',
  username: config.CURRENT_PK || '',
  uid: config.MAIN_ACCOUNT_PK || '',
  dim1: getGcLevel()  // GC level
})

const sendEvent = aes.use(AESPluginEvent)
aes.use(AESPluginPV, {
  enableHistory: true,
  getPageId: url => {
    const urlObj = new URL(url);
    urlObj.search = convertUrlSearch(urlObj.searchParams);
    urlObj.pathname = convertUrlPathname(urlObj.pathname);
    const decodeUrl = decodeURIComponent(urlObj.href);
    return decodeUrl;
  }
});
aes.use(AESPluginAutolog);
aes.use(AESPluginJSError);
aes.use(AESPluginAPI, {
    ignoreList: [
      '//console-base.log-global.aliyuncs.com/logstores/pre/track',
      function (url, obj) {
        return !obj.params || !obj.params.includes('action=')
      }
    ],
    sendResponseOnSuccess: true,
    parseResponse: function (response, statusCode, reqInfo) {
      var { params, url } = reqInfo;
      if (url === '/data/api.json' || url === 'data/api.json') {
        try {
          var actionReg = /action=([^&]*)/;
          var regionReg = /region=([^&]*)/i;
          var decodeBody = decodeURIComponent(reqInfo?.body) || '';
          var success = (response.Success && response.ErrorCode === "Success") || [200, '200'].includes(response?.httpStatusCode);
          return {
            url: (params.match(actionReg) && params.match(actionReg)[1]) || params,
            success: success,
            msg: response?.data?.message ?? response?.message,
            traceId: response?.data?.requestId ?? response?.requestId,
            trace_id: response?.data?.requestId ?? response?.requestId,
            status: response?.httpStatusCode,
            code: response?.data?.code ?? response?.code,
            body: (decodeBody.match(regionReg) && decodeBody.match(regionReg)[1]) || reqInfo.body
          }
        } catch (e) { }
      }
    }
  });
aes.use(AESPluginResourceError);
aes.use(AESPluginEmogine, {
  plugin_emogine_enable_auto_reset: true
})

aes.log = aes.before(aes.log, function (type, data) {
  // const uid = config.CURRENT_PK || config.MAIN_ACCOUNT_PK;
  // const { testUid = [] } = window["ALIYUN_CONSOLE_GLOBAL"];
  const isInnerUser = get(window, 'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS.inner_user');
  if (isInnerUser) {
    return [];
  } else {
    return [type, data];
  }
})

AplusWeb.create();

const trackResource = (props: ITrackResource) => {
  const { advance, imageType, stage, existApps, behavior, deployType, buildType } = props;
  sendEvent(AES_CONSTANT.AES_EVENT_TYPE.RESOURCE_TRACK, {
    c1: behavior,
    c2: existApps,
    c3: stage,
    c4: advance,
    c5: imageType,
    c6: deployType,
    c7: buildType
  })
}

const trackCore = (props: ITrackCore) => {
  const { behavior, stage, extra } = props;
  sendEvent(AES_CONSTANT.AES_EVENT_TYPE.CORE_TRACK, {
    c1: behavior,
    c2: stage,
    c3: extra
  })
}

const trackOpt = (props: ITrackOpt) => {
  const { behavior, stage, extra } = props;
  sendEvent(AES_CONSTANT.AES_EVENT_TYPE.OPT_TRACK, {
    c1: behavior,
    c2: stage,
    c3: extra
  })
}

const trackWhiteScreenErr = (props: ITrackWhiteScreenErr) => {
  const {
    pathname,
    search,
    error,
    extra,
  } = props;
  sendEvent(AES_CONSTANT.AES_EVENT_TYPE.WHITE_SCREEN_ERROR, {
    c1: pathname,
    c2: search,
    c3: error,
    c4: extra,
  })
}

const trackRequestErr = (props: ITrackRequestErr) => {
  const {
    action,
    code,
    message,
    pathname,
    region,
    requestId,
    product,
  } = props;
  sendEvent(AES_CONSTANT.AES_EVENT_TYPE.REQUEST_ERROR, {
    c1: action,
    c2: code,
    c3: message,
    c4: pathname,
    c5: region,
    c6: requestId,
    c7: product,
  })
}

const trackMicroAppMsc=(props:ITrackMicroAppMsc)=>{
  const {
    behavior,
    stage,
    appId ,
    section,
    trackType,
  } = props;
  sendEvent(AES_CONSTANT.AES_EVENT_TYPE.MICROAPP_MSC_TRACK,{
    c1: behavior,
    c2: stage,
    c3: appId,
    c4: section,
    c5: trackType,
  })
}

export { trackResource, trackCore, trackOpt, trackWhiteScreenErr, trackRequestErr,trackMicroAppMsc, AES_CONSTANT };
