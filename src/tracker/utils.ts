import { find, forEach, get } from "lodash";

export const convertUrlSearch = (searchParams) => {
  const paramsArr = [];

  for (const [key] of searchParams) {
    paramsArr.push(`${key}={${key}}`);
  }
  const convertedString = paramsArr.join('&');
  return convertedString;
}

export const convertUrlPathname = (urlPathname) => {
  const pathArr = urlPathname.split('/');

  const regions = get(window, 'ALIYUN_CONSOLE_CONFIG.REGIONS', []);
  if (find(regions, { regionId: pathArr[1] })) {
    pathArr[1] = '{region}';
  }

  // webshell
  if (pathArr[2] === 'monitor') {
    pathArr[3] = '{app_id}';
    pathArr[4] = '{version_id}';
    pathArr[5] = '{instance_id}';
  }

  const appIdReg = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  forEach(pathArr, (item, index: number) => {
    if (pathArr[index - 1] === 'app-list' && appIdReg.test(item)) {
      pathArr[index] = '{app_id}';

      if (pathArr[index + 1] === 'version') {
        pathArr[index + 2] = '{version_id}';
      }
    }
    if (pathArr[index - 1] === 'namespace' && item) {
      pathArr[index] = '{namespace_id}';
    }
    if (pathArr[index - 1] === 'instance-list' && item) {
      pathArr[index] = '{instance_id}';
    }
  })

  const pathname = pathArr.join('/');
  return pathname;
}
