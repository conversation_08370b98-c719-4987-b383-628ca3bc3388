import { DEPLOY_TYPE } from "../components/shared/DeploySelectorField/constant";

const isPreConsoleEnv = window.ALIYUN_CONSOLE_CONFIG?.fEnv === 'pre';

enum AES_EVENT_TYPE {
  CREATE_APP = "CREATE_APP",  // 弃用
  SUCCESS_CREATED = "SUCCESS_CREATED",  // 弃用
  APP_TRACK = "APP_TRACK", // 弃用
  RESOURCE_TRACK = "RESOURCE_TRACK",
  CORE_TRACK = "CORE_TRACK",
  OPT_TRACK = "OPT_TRACK",
  WHITE_SCREEN_ERROR = "WHITE_SCREEN_ERROR",
  REQUEST_ERROR = "REQUEST_PROMPT_ERROR",
  MICROAPP_MSC_TRACK = "MICROAPP_MSC_TRACK",
}

enum AES_STAGE_TYPE {
  TRIGGER = "TRIGGER",
  SUCCESS = "SUCCESS",
  FAIL = "FAIL",
  EXIT = "EXIT",
}

enum RESOURCE_BEHAVIOR_TYPE {
  CREATE = "CREATE",
  CREATE_FORM = "CREATE_FORM",
  DELETE = "DELETE",
}

enum CORE_BEHAVIOR_TYPE {
  VERSION = "VERSION",
  TRAFFIC = "TRAFFIC",
  CUSTOM_HOST = "CUSTOM_HOST"
}

enum OPT_BEHAVIOR_TYPE {
  LOG = "LOG",
  MONITOR = "MONITOR",
  WEB_SHELL = "WEB_SHELL"
}

export default {
  TRACKER_HOST: 'cn-hangzhou.log.aliyuncs.com',
  PROJECT: 'sae-console-tracker',
  LOGSTORE: isPreConsoleEnv ? 'development' : 'production',
  AES_EVENT_TYPE,
  AES_STAGE_TYPE,
  RESOURCE_BEHAVIOR_TYPE,
  CORE_BEHAVIOR_TYPE,
  OPT_BEHAVIOR_TYPE,
};

export interface ITrackResource {
  behavior: RESOURCE_BEHAVIOR_TYPE,
  stage: AES_STAGE_TYPE,
  existApps?: number, // 表示操作前 剩余应用数
  deployType?: DEPLOY_TYPE,
  buildType?: string;
  advance?: boolean,
  imageType?: string, // "Default" | "Custom" | "Private"
  extra?: string,
}

export interface ITrackCore {
  behavior: CORE_BEHAVIOR_TYPE,
  stage: AES_STAGE_TYPE,
  extra?: string,
}

export interface ITrackOpt {
  behavior: OPT_BEHAVIOR_TYPE,
  stage: AES_STAGE_TYPE,
  extra?: string,
}

export interface ITrackWhiteScreenErr {
  pathname: string,
  search: string,
  error: string,
  extra?: string,
}

export interface ITrackRequestErr {
  action: string,
  code: string,
  message: string,
  pathname: string,
  region: string,
  requestId: string,
  product: string,
}

export interface ITrackMicroAppMsc {
  behavior: 'CREATE' | 'EDIT' | 'DELETE' | 'VIEW';
  stage: AES_STAGE_TYPE;
  appId: string;
  section: string;  // 微服务治理菜单路径
  trackType: 'MSC' | 'LOSSLESS' | 'GRAYSCALE_RULE' | 'HEALTH_READINESS';
}
