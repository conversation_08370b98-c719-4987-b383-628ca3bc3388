<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="aplus-core" content="aplus.js">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Serverless应用引擎(SAE)</title>
  <script nonce="W39KS1q9gTjiXWJyKrGy">
    var ONE_CONSOLE_TOOL = {
      extend: function (o, n) {
        if (!o) o = {};
        if (!n) n = {};
        for (var p in n) {
          o[p] = n[p];
        }
        return o;
      }
    };
  </script>
</head>

<body>
  <div id="app"></div>

  <script>
    window.CONSOLE_BASE_SETTINGS = {
      PRODUCT_ID: 'serverless',
      SIDE_PANEL: true
    };
  </script>

  <script
    src="https://oneconsole.oss-cn-shanghai.aliyuncs.com/dev-new-one-mcms/saenext/saenext_zh-cn.js?timestamp=1725516748370"
    charset="utf-8" nonce="W39KS1q9gTjiXWJyKrGy"></script>
  <script nonce="W39KS1q9gTjiXWJyKrGy">
    var aliyunConsoleI18nMessage = {};
    aliyunConsoleI18nMessage = ONE_CONSOLE_TOOL.extend(window["saenext_zh-cn"], aliyunConsoleI18nMessage);
  </script>
  <script nonce="W39KS1q9gTjiXWJyKrGy">
    var ALIYUN_CONSOLE_I18N_MESSAGE =
      aliyunConsoleI18nMessage;
  </script>
  <% if (__dev__) { %>
    <!-- 以下只在开发环境生效，千万不要写出 这个 if 判断，线上具体配置以 Viper 上的配置为主-->
    <button onclick="toggle()"
      style="position: absolute; right: 54px; bottom: 4px; color: white; background-color: #0064C8; border: none; border-radius: 4px; height: 20px; z-index: 20;">
      设置本地 token
    </button>
    <section id="local_token_section" style="display: none; position: absolute; right: 154px; bottom: 4px; z-index: 20">
      <label id="label_token" style="margin-right: 8px;"></label>
      <label><b>Update token: </b></label>
      <input id="local_token" type="password">
      <button onclick="save()">更新</button>
    </section>
    <script>
      (function (w, d, s, q, i) {
        w[q] = w[q] || [];
        var f = d.getElementsByTagName(s)[0], j = d.createElement(s);
        j.async = true;
        j.id = 'beacon-aplus';
        j.setAttribute('exparams', 'clog=o&userid=<用户ID>&aplus&sidx=aplusSidex&ckx=aplusCkx');
        j.src = "//g.alicdn.com/alilog/mlog/aplus_v2.js";
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'aplus_queue');
      const toggle = () => {
        var target = document.getElementById('local_token_section');
        if (target.style.display === 'none') {
          target.style.display = "block";
        } else {
          target.style.display = "none";
        }
        var label = document.getElementById('label_token');
        label.innerHTML = "<b>Current token: </b>" + localStorage.getItem('sec_token');
      }
      const save = () => {
        const val = document.getElementById('local_token').value || "";
        var target = document.getElementById('local_token_section');
        target.style.display = "none";
        localStorage.setItem('sec_token', val);
        location.reload();
      }
      const key = localStorage.getItem('sec_token') || "";
      var ALIYUN_CONSOLE_CONFIG = {
        LANG: 'zh',
        LOCALE: 'zh-CN',
        portalType: 'one',
        SEC_TOKEN: key,
        MAIN_ACCOUNT_PK: '****************',
        CHANNEL_FEATURE_STATUS: {},
        CHANNEL_LINKS: {},
        REGIONS: [
          {
              "name": "华南1（深圳）",
              "physicalList": [
                  {
                      "id": "cn-shenzhen"
                  }
              ],
              "regionId": "cn-shenzhen",
              "zoneList": []
          },
          {
              "name": "华北2（北京）",
              "physicalList": [
                  {
                      "id": "cn-beijing"
                  }
              ],
              "regionId": "cn-beijing",
              "zoneList": []
          },
          {
              "name": "华东2（上海）",
              "physicalList": [
                  {
                      "id": "cn-shanghai"
                  }
              ],
              "regionId": "cn-shanghai",
              "zoneList": []
          },
          {
              "name": "华北3（张家口）",
              "physicalList": [
                  {
                      "id": "cn-zhangjiakou"
                  }
              ],
              "regionId": "cn-zhangjiakou",
              "zoneList": []
          },
          {
              "name": "华东1（杭州）",
              "physicalList": [
                  {
                      "id": "cn-hangzhou"
                  }
              ],
              "regionId": "cn-hangzhou",
              "zoneList": []
          },
          {
              "name": "西南1（成都）",
              "physicalList": [
                  {
                      "id": "cn-chengdu"
                  }
              ],
              "regionId": "cn-chengdu",
              "zoneList": []
          },
          {
              "name": "中国香港",
              "physicalList": [
                  {
                      "id": "cn-hongkong"
                  }
              ],
              "regionId": "cn-hongkong",
              "zoneList": []
          },
          {
              "name": "新加坡",
              "physicalList": [
                  {
                      "id": "ap-southeast-1"
                  }
              ],
              "regionId": "ap-southeast-1",
              "zoneList": []
          },
          {
              "name": "美国（硅谷）",
              "physicalList": [
                  {
                      "id": "us-west-1"
                  }
              ],
              "regionId": "us-west-1",
              "zoneList": []
          },
      ],
        FEATURE_STATUS: {
          sae1_enable: true
        },
        OPEN_STATUS: {
          "serverless": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "true",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "acr": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "true",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "nas": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "oos": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "false",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "ahas": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "ahaspro": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "sae": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "false",
            "prepaidExpireSoon": "false",
            "keepProduct": "false",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "eventbridge": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "arms": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "sls": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "alikafka": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          },
          "oss": {
            "paused": "false",
            "inDebtSoon": "false",
            "creditScore": "0",
            "inDebt": "false",
            "spiTest": "false",
            "imageAgreement": "NONE",
            "spotAgreement": "unsigned",
            "prepaid": "false",
            "osStopMode": "keep",
            "enabled": "true",
            "prepaidExpireSoon": "false",
            "keepProduct": "true",
            "site": "false",
            "publicImageAgreement": "unsigned",
            "prohibitedByRiskControl": "false",
            "servicizing": "false",
            "inDebtOverdue": "false",
            "bizAPI": "false",
            "prepaidOverdue": "false",
            "arrearageStatus": "unknown",
            "userStopMode": "keep",
            "authentication": "true"
          }
        },
        fEnv: 'dev'
      };
      var ALIYUN_CONSOLE_GLOBAL = {
        devEnvList: [
          {
            "value": "qingdao_test",
            "label": "青岛环境"
          },
          {
            "value": "beijing_test",
            "label": "北京环境"
          },
          {
            "value": "beta",
            "label": "预发环境"
          },
          {
            "value": "beta",
            "label": "预发环境"
          },
          {
            "label": "深圳预发集群1",
            "value": "sz1pre"
          },
          {
            "label": "深圳预发环境2",
            "value": "sz2pre"
          },
          {
            "label": "深圳预发环境3",
            "value": "sz3pre"
          }
        ],
        regions: [
          {
            "id": "cn-shanghai",
            "name": "华东2（上海）"
          },
          {
            "id": "cn-hangzhou",
            "name": "华东1（杭州）"
          },
          {
            "id": "cn-zhangjiakou",
            "name": "华北3（张家口）"
          },
          {
            "id": "cn-shenzhen",
            "name": "华南1（深圳）"
          },
        ],
        onAsiFeature: [
          {
            "id": "cn-shanghai",
            "name": "华东2（上海）"
          },
        ],
      }
      var RISK_INFO = {
        GETUA: function () {
          return 'mock-collina-ua'
        },
        UMID: 'mock-umid'
      };
    </script>
    <% } %>
</body>

</html>
