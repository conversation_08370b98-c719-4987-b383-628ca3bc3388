/* eslint-disable quote-props, semi, comma-dangle */

// 如果生产环境需要接入OneConsole, 请勿修改或删除对于下面公共变量的引用
// - ALIYUN_WIND_MESSAGE
// - ALIYUN_CONSOLE_MESSAGE
// 如果需要在dev环境模拟生产环境的输出,
// 首先确保美杜莎(http://mcms-portal.alibaba-inc.com/)的项目仓库已被创建,
// 如VPC的美杜莎项目:
// - group: aliyun
// - name: vpcnext-console-aliyun-com
// 请在.windrc中找到intl配置字段并增加如下的配置:
// "intl": {
//   "locale": "zh-CN",
//   "products": [
//     {
//       "group": "aliyun",
//       "name": "vpcnext-console-aliyun-com",
//       "identifier": "ALIYUN_CONSOLE_MESSAGE"
//     },
//     {
//       "group": "aliyun",
//       "name": "wind",
//       "identifier": "ALIYUN_WIND_MESSAGE"
//     }
//   ]
// }
// 使用如上配置后, 重新启动dev开发调试服务器(def dev),
// 将会在上述的公共变量中输出对应仓库的字典配置

// import CachedData from '~/cache/common';
// import locales from './strings';

export default {
  // ...locales[CachedData.lang],
  ...(window.ALIYUN_WIND_MESSAGE || {}),
  ...(window.ALIYUN_CONSOLE_I18N_MESSAGE || {})
};
