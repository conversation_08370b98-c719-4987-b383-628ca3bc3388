// 权限错误相关的国际化文本
export const permissionErrorMessages = {
  // PermissionError 组件相关文本
  'saenext.components.PermissionError.AccessDenied': '访问被拒绝',
  'saenext.components.PermissionError.Module': '模块',
  'saenext.components.PermissionError.Interface': '接口',
  'saenext.components.PermissionError.RequiredPolicy': '所需权限策略',
  'saenext.components.PermissionError.ErrorCode': '错误代码',
  'saenext.components.PermissionError.ViewPermissionHelp': '查看权限帮助文档',
  'saenext.components.PermissionError.PermissionRequired': '需要权限',
  'saenext.components.PermissionError.NoPermission': '无权限',
  'saenext.components.PermissionError.SimpleMessage': '{moduleName}模块缺少{requiredPolicy}权限',

  // BindRouteCard 组件权限相关文本
  'saenext.components.application.BindRouteCard.PermissionNotice': '权限提醒',
  'saenext.components.application.BindRouteCard.SomeGatewayTypesUnavailable': '以下网关类型因权限不足暂不可用：',
  'saenext.components.application.BindRouteCard.RequiredPolicy': '所需权限策略',

  // RouteTable 组件权限相关文本
  'saenext.components.application.RouteTable.PermissionDenied': '权限不足',
  'saenext.components.application.RouteTable.AccessDenied': '访问被拒绝',
  'saenext.components.application.RouteTable.Module': '模块',
  'saenext.components.application.RouteTable.Interface': '接口',
  'saenext.components.application.RouteTable.RequiredPolicy': '所需权限策略',
  'saenext.components.application.RouteTable.ErrorCode': '错误代码',

  // BindIp 组件权限相关文本
  'saenext.components.application.BindIp.PermissionDenied': '权限不足',
  'saenext.components.application.BindIp.AccessDenied': '访问被拒绝',
  'saenext.components.application.BindIp.Module': '模块',
  'saenext.components.application.BindIp.Interface': '接口',
  'saenext.components.application.BindIp.RequiredPolicy': '所需权限策略',
  'saenext.components.application.BindIp.ErrorCode': '错误代码',

  // BindNlb 组件权限相关文本
  'saenext.components.application.BindNlb.PermissionDenied': '权限不足',
  'saenext.components.application.BindNlb.AccessDenied': '访问被拒绝',
  'saenext.components.application.BindNlb.Module': '模块',
  'saenext.components.application.BindNlb.Interface': '接口',
  'saenext.components.application.BindNlb.RequiredPolicy': '所需权限策略',
  'saenext.components.application.BindNlb.ErrorCode': '错误代码',
};