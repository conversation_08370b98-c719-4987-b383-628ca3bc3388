@title-size: 14px;

.flow-portal {
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  .flow-control {
    padding: 24px 0px;
    background-image: url(https://img.alicdn.com/imgextra/i1/O1CN01mZ9Lzw1efKbJfcKF4_!!6000000003898-55-tps-16-16.svg);
    .flow-title {
      display: inline-block;
      width: 100%;
      font-size: @title-size;
      color: #333333;
      text-align: center;
      font-weight: 500;
      margin-bottom: 16px;
    }
    .flow-chart {
      width: 100%;
      text-align: center;
    }
    .flow-legend {
      font-size: 12px;
      color: #333333;
      text-align: left;
      margin-top: 16px;
      .legend() {
        content: '';
        display: inline-block;
        margin-right: 5px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 16px;
      }
      .circular {
        &::before {
          .legend();
          background: #179DEB;
        }
      }
      .circulara {
        &::before {
          .legend();
          background: #24D5F0;
        }
      }
      .circularb {
        &::before {
          .legend();
          background: #79F2A6;
        }
      }
    }
  }
}

.swimlane-portal {
  .next-input-label {
    padding-left: 0 !important;
    .select-label {
      height: 26px;
      line-height: 26px;
      border-radius: 2px;
      padding: 0 8px;
      margin: 0 2px;
      font-size: 12px;
      color: rgb(51, 51, 51);
      background-color: #F7F9FA;
    }
  }
  .swimlane-card {
    height: 400px;
    width: 380px;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    .cnd-search {
      .flex {
        width: 100%;
        .rc-search {
          width: 100%;
        }
      }
    }
    .next-table-cell {
      cursor: pointer;
    }
    .next-col-fixed-8 {
      width: 120px;
      max-width: 120px;
    }
  }
  .swimlane-panel {
    height: 400px;
    width: calc(100% - 396px);
    margin-left: 16px;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
  }
  .swimlane {
    padding: 16px;
    border-radius: 2px;
    border: 1px solid #e5e5e5;
  }
}

.flow-bars {
  .next-input-label {
    padding-left: 0 !important;
    .select-label {
      height: 26px;
      line-height: 26px;
      border-radius: 2px;
      padding: 0 8px;
      margin: 0 2px;
      font-size: 12px;
      color: rgb(51, 51, 51);
      background-color: #F7F9FA;
    }
  }
}

.qps-data {
  padding: 16px 8px;
  border-radius: 2px;
  border: 1px solid #e5e5e5;
  .flex-column {
    display: flex;
    flex-direction: column;
    color: #666666;
  }
  .symbol {
    height: 44px;
    margin-left: 4px;
    margin-right: 12px;
  }
}

.qps-chart {
  margin-top: 16px;
}


.step-timeline {
  .timeline-dot {
    width: 22px;
    height: 22px;
    border-radius: 11px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 9px;
    margin-top: -8px;
    border: solid 1px #e5e5e5;
    color: #666666;
  }
  .next-timeline-item-content {
    margin-bottom: 16px;
    .next-timeline-item-title {
      margin-top: 0;
    }
  }
}

.swimlane-timeline {
  .next-timeline-item-content {
    width: 100%;
    padding-right: 16px;
    .next-table th {
      background-color: #f6f6f6;
    }
    .next-table td {
      background-color: #f6f6f6;
    }
    .next-form-item-label {
      font-weight: 500;
    }
  }
}

.swimlane-collapse {
  .next-collapse {
    .next-collapse-panel {
      .next-collapse-panel-title {
        padding: 8px 16px 8px 36px !important;
        .next-collapse-panel-icon {
          margin-top: 6px;
        }
      }
    }
  }
}

.transfer-new {
  .transfer-left-box {
    padding: 0 10px;
    border: 1px solid #cbcbcb;
    .aliyun-widget-checkbox-group .aliyun-widget-checkbox-wrapper:first-child{
      margin-left: 8px;
    }
  }

  .transfer-left-title {
    margin-top: 10px;
    font-family: 500;
    color: #333;
    display: flex;
    justify-content: space-between;
    height: 20px;
  }

  .transfer-checkbox-box {
    display: inline-block;
    padding-right: 0;
    height: 283px;
    overflow: scroll;
    width: 100%;
  }

  .transfer-checkbox-li {
    display: block;
    width: 100%;
    margin-bottom: 0;

    .transfer-checkbox-li-text {
      display: inline-block;
      max-width: calc(100% - 30px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .transfer-footer {
    border-left: 1px solid #cbcbcb;
    border-right: 1px solid #cbcbcb;
    border-bottom: 1px solid #cbcbcb;
    padding: 2px 10px 0 10px;
    height: 33px;
  }
  .transfer-footer-delete{
    line-height: 29px;
    color: #0070cc;
    cursor: pointer;
  }

  .transfer-right-box {
    padding: 0 10px;
    border-top: 1px solid #cbcbcb;
    border-right: 1px solid #cbcbcb;
    border-bottom: 1px solid #cbcbcb;
    background-color: #f6f6f6;
    height: 370px;
    overflow: scroll;
  }

  .transfer-right-title {
    margin-top: 10px;
    font-family: 500;
    color: #333;
    height: 20px;
    line-height: 20px;
  }

  .transfer-right-tag-text {
    background-color: #fff;
    border: 1px solid #e5e5e5;
    display: inline-block;
    padding: 3px 10px;
    border-radius: 2px;
  }

  .transfer-right-tag-li {
    margin: 5px 0;
  }

  .transfer-right-tag-close {
    cursor: pointer;
  }

  .transfer-right-tag-close .next-icon:before {
    width: 10px;
    font-size: 10px;
  }
}

.flow-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .swimlane-panel {
    width: calc(50% - 8px);
    height: 400px;
    padding: 16px;
    border-radius: 2px;
    border: 1px solid #e5e5e5;
    margin-bottom: 16px;
  }
}



