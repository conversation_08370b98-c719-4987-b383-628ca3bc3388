.layout {
  background: #EFF3F8;
  height: 42px;
  border-radius: 4px;
  margin-top: 16px;
  display: flex;
  align-items: center;
  padding-left: 17px;
  .compare {
    display: inline-flex;
    align-items: center;
  }
}

.x0-chart {
  .next-loading-wrap {
    height: 100%;
    // .aisc-widgets {
      // width: 100% !important;
      // height: 100% !important;
      // div {
      //   height: 100%;
      //   canvas {
      //     width: 100% !important;
      //     height: 100% !important;
      //   }
      // }
  //   }
  }
}

.legend {
  display: flex;
  margin-top: 8px;
  .legend-rx {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #0064C8;
    }
  }
  .legend-tx {
    display: flex;
    align-items: center;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #42B3A4;
    }
  }
  .legend-2xx {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #23b066;
    }
  }
  .legend-3xx {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #FAC31E;
    }
  }
  .legend-4xx {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #f98e1a;
    }
  }
  .legend-5xx {
    display: flex;
    align-items: center;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #e84738;
    }
  }
  .legend-total {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #6B67E0;
    }
  }
  .legend-active {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #23b066;
    }
  }
  .legend-idle {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      content: '';
      margin-right: 4px;
      background-color: #FAC31E;
    }
  }
  .legend-req {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      // width: 6px;
      // height: 6px;
      width: 10px;
      height: 2px;
      content: '';
      margin-right: 4px;
      background-color: #0064C8;
    }
  }
  .legend-ins {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      // width: 6px;
      // height: 6px;
      width: 10px;
      height: 2px;
      content: '';
      margin-right: 4px;
      background-color: #0064C8;
    }
  }
  .legend-cpu {
    display: flex;
    align-items: center;
    margin-right: 12px;
    &::before {
      display: inline-block;
      width: 10px;
      height: 2px;
      content: '';
      margin-right: 4px;
      background-color: #23b066;
    }
  }
}

.discern {
  height: 24px;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .solid {
    display: flex;
    align-items: center;
    margin-right: 16px;
    &::before {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 6px;
      content: '';
      margin-right: 4px;
      background-color: #333333;
    }
  }
  .stripe {
    display: flex;
    align-items: center;
    &::before {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 6px;
      content: '';
      margin-right: 4px;
      // background-color: #42B3A4;
      background-image: linear-gradient(0deg, #666 20%, #fff 0, #333 40%, #fff 0, #333 60%, #fff 0,  #333 80%, #fff 0,  #333 100%, #fff 0);
      background-size: 3px;
    }
  }
}

.g2-container {
  display: flex;
  align-items: start;
}

.border-normal {
  background-color: #ffffff;
  border: 1px solid #c0c6cc !important;
}

.border-left-none {
  &+span span span {
    background-color: #ffffff;
    border-top: 1px solid #e3e4e6 !important;
    border-bottom: 1px solid #e3e4e6 !important;
    border-left: 1px solid #ffffff !important;
    border-right: 1px solid #ffffff;
    border-radius: 0px;
  }
}

.simple-normal {
  background-color: #ffffff;
  border-top: 1px solid #c0c6cc !important;
  border-bottom: 1px solid #c0c6cc !important;
  border-left: 1px solid #c0c6cc !important;
  border-right: 1px solid #ffffff;
  border-radius: 0px;
}

.border-left-none {
  & + span span span {
    background-color: #ffffff;
    border-top: 1px solid #c0c6cc !important;
    border-bottom: 1px solid #c0c6cc !important;
    border-left: 1px solid #ffffff !important;
    border-right: 1px solid #ffffff;
    border-radius: 0px;
  }
}