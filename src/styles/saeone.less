.link-primary {
  color: #0070cc;
  cursor: pointer;
}

.link-primary-add {
  white-space: nowrap;

  span {
    margin-left: 4px;
  }

  &:before {
    vertical-align: initial;
  }
}

.condition {
  .aliyun-widget-table-empty {
    padding: 16px 0 17px;
  }

  .common-block {
    .aliyun-widget-input-control {
      padding-right: 0;
    }

    .aliyun-widget-number-picker-handler {
      display: block;
    }
  }
}

.micro {
  margin-left: -16px;

  .aliyun-widget-table td {
    border: none;
  }

  .aliyun-widget-table-header {
    tr {
      display: none;
    }
  }

  .aliyun-widget-table-empty {
    padding: 0;
  }
}


.reg-result-box {
  width: 100%;
  min-height: 200px;
  max-height: 200px;
  overflow-y: scroll;
  border: 1px solid #ddd;
  background-color: #f7f7f7;
  padding: 10px;
}

.pointer {
  cursor: pointer;
}
