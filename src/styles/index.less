@import "./shell.less";
@import "./extra.less";
@import './xconsole.less';
@import './boundary.less';
@import './monitor.less';
@import './namespace.less';
@import './deep.less';
@import './saeone.less';
@import './banner.less';
@import './overview.less';
@import './grayscale.less';
@import './armsIconfont/iconfont.css';
@color-hover: #F7F9FA;
@color-active: #EFF3F8;
@color-font: #333333;

// grafana 字体影响问题
body{
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
}

.inline-block {
  display: inline-block;
}

.block {
  display: block;
}

.none {
  display: none !important;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.mt {
  margin-top: 16px;
  &-xs {
    margin-top: 4px;
  }
  &-s {
    margin-top: 8px;
  }
  &-l {
    margin-top: 16px;
  }
  &-xl {
    margin-top: 24px;
  }
}

.mb {
  margin-bottom: 16px;
  &-xs {
    margin-bottom: 4px;
  }
  &-s {
    margin-bottom: 8px;
  }
  &-l {
    margin-bottom: 16px;
  }
  &-xl {
    margin-bottom: 24px;
  }
  &-none {
    margin-bottom: 0!important;
  }
}

.ml {
  margin-left: 16px;
  &-xs {
    margin-left: 4px;
  }
  &-s {
    margin-left: 8px;
  }
  &-l {
    margin-left: 16px;
  }
  &-xl {
    margin-left: 24px;
  }
  &-xxl {
    margin-left: 32px;
  }
  &-xxxl {
    margin-left: 48px;
  }
}

.mr {
  margin-right: 16px;
  &-xs {
    margin-right: 4px;
  }
  &-s {
    margin-right: 8px;
  }
  &-l {
    margin-right: 16px;
  }
  &-xl {
    margin-right: 24px;
  }
  &-none {
    margin-right: 0 !important;
  }
}

.m-card {
  margin: 12px 24px;
}

.pt {
  &-xs {
    padding-top: 4px;
  }
  &-s {
    padding-top: 8px;
  }
  &-l {
    padding-top: 16px;
  }
  &-xl {
    padding-top: 24px;
  }
}

.pb {
  &-xs {
    padding-bottom: 4px;
  }
  &-s {
    padding-bottom: 8px;
  }
  &-l {
    padding-bottom: 16px;
  }
  &-xl {
    padding-bottom: 24px;
  }
}

.pl {
  &-xs {
    padding-left: 4px;
  }
  &-s {
    padding-left: 8px;
  }
  &-l {
    padding-left: 16px;
  }
  &-xl {
    padding-left: 24px;
  }
}

.pr {
  &-xs {
    padding-right: 4px;
  }
  &-s {
    padding-right: 8px;
  }
  &-l {
    padding-right: 16px;
  }
  &-xl {
    padding-right: 24px;
  }
}

.pd-xs {
  padding: 7px;
}

.pd-card {
  padding: 16px;
}

.p-50 {
  padding: 50px;
}

.w-50 {
  width: calc(50% - 10px);
}

.w-80 {
  width: 80% !important;
}

.w-90 {
  width: 90%;
}

.w-100 {
  width: 100%;
}

.pointer {
  cursor: pointer;
}

.h3 {
  margin-bottom: 12px;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  letter-spacing: 0;
  line-height: 22px;
}

.flex {
  display: flex;
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-start {
  align-items: flex-start;
}

.align-stretch{
  align-items: stretch;
}

.item-start {
  align-items: start;
}

.flex-col {
  flex-direction: column;
}

.flex-row-revers{
  flex-direction: row-reverse;
}

.text-description {
  color: #888;
  line-height: 1.5;
}

.text-warning {
  color: red;
}

.text-error {
  color: rgb(255, 51, 51);
}

.gray-text {
  color: #555555
}

.border {
  border: 1px solid #e3e4e6;
  &-radius {
    border: 1px solid #e3e4e6;
    border-radius: 4px;
  }
}

.border {
  &-l {
    border-left: 1px solid #e3e4e6;
  }
  &-r {
    border-right: 1px solid #e3e4e6;
  }
  &-t {
    border-top: 1px solid #e3e4e6;
  }
  &-b {
    border-bottom: 1px solid #e3e4e6;
  }
}

.border-none {
  border: 0;
}


.border-primary {
  border-color: #0070cc !important;
}

.color-primary {
  color: #0070cc !important;
}

.text-line {
  line-height: 1.5;
}

.text-bold {
  font-weight: bold;
}

.text-500 {
  font-weight: 500;
}

.color-text-description {
  color: #888
}

.scale-medium {
  scale: 1.6
}

.list-disc {
  list-style: disc;
}

.text {
  &-xs {
    font-size: smaller;
  }
  &-s {
    font-size: small;
  }
  &-m {
    font-size: medium;
  }
  &-l {
    font-size: large;
  }
  &-xl {
    font-size: x-large;
  }
}

a.disabled-link {
  pointer-events: none;
  color: gray;
}

p{
  margin-top: 0;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.break-all {
  word-break: break-all;
}

.list-decimal {
  list-style: decimal;
}

.line18 {
  line-height: 18px;
}

.text-12 {
  font-size: 12px;
}

.title-text {
  color: #555555;
}
.content-text {
  color: #333333;
}

.loop(@n) when (@n > 0) {
  .grid-container-@{n} {
    display: grid;
    grid-template-columns: repeat(@n, 1fr); /* 创建n列等宽的格子 */
    gap: 8px; /* 设置项目之间的间距 */
  }

  .loop(@n - 1);
}

.loop(12);

.pro-gradient-text{
  background: linear-gradient(to right, #ff6600, #782a1b); /* 从左到右的渐变色 */
  -webkit-background-clip: text; /* 将背景剪裁为文本 */
  -webkit-text-fill-color: transparent; /* 将文本填充颜色设置为透明 */
}
