.blue-pointer {
  color: #06c;
  cursor: pointer;
}

.button-disabled {
  pointer-events: none;
  cursor: default;
  opacity: 0.6;
  line-height: 0;
  margin-right: 4px;
}

.button-enabled {
  cursor: pointer;
  color: #0064c8;
  margin-right: 4px;
}

.select-popup.next-overlay-inner.next-select-spacing-tb.next-select-single-menu .next-menu-item-inner{
  height: max-content;
  padding: 4px 0;
  width: 360px;
}

.select-popup.next-overlay-inner.next-select-spacing-tb.next-select-multiple-menu .next-menu-item-inner{
  height: max-content;
  padding: 4px 0;
  width: 360px;
}

.select-popup {
  .next-menu-item-text {
    .wind-rc-info {
      margin-bottom: 0;
    }
  }
}

.ib {
  display: inline-block;
}

.arrow-animation {
  -webkit-animation: rotating 2s linear infinite;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

.basic-detail-item {
  display: inline-block;
  text-align: center;
  width: 25%;
  border-right: 1px solid #ddd;
  &:last-child {
    border: none;
  }
  .bdi-title {
    font-size: 14px;
    margin-bottom: 40px;
  }
  .bdi-statistic {
    font-size: 25px;
    color: #0070cc;
    margin-right: 10px;
  }
}

.nstage-root {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  .nstage-status {
    display: flex;
    align-items: center;
    padding: 16px;
    min-width: 200px;
    .title {
      font-weight: bold; 
      margin-left: 16px;
    }
    .wind-rc-status-indicator {
      .wind-rc-status-indicator-icon:before {
        width: auto !important;
        font-size: 12px !important;
      }
    }
  }
  .nstage-table {
    flex: 1;
    padding-top: 16px;
    padding-left: 16px;
    padding-right: 16px;
    background-color: #f5f6f5;
    .next-table {
      .next-table-body {
        .next-table-expanded-row {
          background-color: #fff;
        }
        .wind-rc-status-indicator-container {
          .wind-rc-status-indicator-icon:before {
            width: auto!important;
            font-size: 12px!important;
          }
        }
      }
    }
  }
}

.nrecord-timeline {
  .next-timeline-item-timeline {
    width: 24px;
    .next-timeline-item-node {
      .wind-rc-status-indicator-container {
        .wind-rc-status-indicator-icon:before {
          width: auto!important;
          font-size: 12px!important;
        }
      }
    }
  }
  .next-timeline-item-content {
    width: calc(100% - 24px);
    margin-bottom: 16px;
    .next-timeline-item-title {
      height: 28px;
      line-height: 28px;
      margin-top: 0;
      margin-bottom: 16px;
    }
    .next-timeline-item-body {
      .nstage-panel {
        padding: 16px;
        background: rgb(245, 246, 250);
        .next-table {
          .next-table-body {
            .next-table-expanded-row {
              background-color: #fff;
            }
            .wind-rc-status-indicator-container {
              .wind-rc-status-indicator-icon:before {
                width: auto!important;
                font-size: 12px!important;
              }
            }
          }
        }
      }
    }
  }
}

.npipe-root {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  margin-bottom: 16px;
  .npipe-status {
    display: flex;
    align-items: center;
    padding: 16px;
    min-width: 200px;
    .title {
      font-weight: bold; 
      margin-left: 16px;
    }
    .wind-rc-status-indicator {
      .wind-rc-status-indicator-icon:before {
        width: auto !important;
        font-size: 12px !important;
      }
    }
  }
  .npipe-panel {
    flex: 1;
    .nstage-panel {
      padding: 8px;
      width: 100%;
      background: rgb(245, 246, 250);
      .next-collapse {
        margin-bottom: 8px;
        &:last-child {
          margin-bottom: 0;
        }
        .next-collapse-panel-title {
          background-color: #fff;
          padding: 8px 0 8px 36px;
          &:hover {
            background-color: #fff;
          }
          .wind-rc-status-indicator-container {
            .wind-rc-status-indicator-icon:before {
              width: auto!important;
              font-size: 12px!important;
            }
          }
        }
        .next-collapse-panel-content {
          padding: 0;
          .nstage-panel-body {
            padding: 12px 12px 12px 36px;
            background: rgb(4, 43, 53);
          }
        }
      }
    }
  }
}

.npipe-timeline {
  :not(:last-child) {
    .next-timeline-item .next-timeline-item-timeline .next-timeline-item-tail {
      display: block!important;
    }
  }
  .next-timeline-item-timeline {
    .next-timeline-item-node {
      .wind-rc-status-indicator-icon:before {
        width: auto!important;
        font-size: 12px!important;
      }
    }
  }
  .next-timeline-item-content {
    width: 100%;
    padding-right: 16px;
    .next-timeline-item-title {
      height: auto;
      line-height: normal;
      margin-top: 0;
      .npipe-timeline-item {
        display: flex;
        padding-top: 6px;
        .nstage-panel {
          padding: 8px;
          width: 100%;
          margin-left: 16px;
          background: rgb(245, 246, 250);
          .next-collapse {
            margin-bottom: 8px;
            &:last-child {
              margin-bottom: 0;
            }
            .next-collapse-panel-title {
              background-color: #fff;
              padding: 8px 0 8px 36px;
              &:hover {
                background-color: #fff;
              }
              .wind-rc-status-indicator-container {
                .wind-rc-status-indicator-icon:before {
                  width: auto!important;
                  font-size: 12px!important;
                }
              }
            }
            .next-collapse-panel-content {
              padding: 0;
              .nstage-panel-body {
                padding: 12px 12px 12px 36px;
                background: rgb(4, 43, 53);
              }
            }
          }
        }
      }
    }
  }
}