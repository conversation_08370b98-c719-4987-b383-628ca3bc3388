@box-space: 16px;
@border-width: 1px;
@right-side-width: 390px;
@border-radius: 4px;
@border-color: #e5e5e5;


.overview {
  display: flex;
  width: 100%;
  .main-font() {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }
  .base-border() {
    padding: @box-space;
    border-radius: @border-radius;
    border: @border-width solid @border-color;
  }
  .base-flex-wrapper() {
    display: flex;
    flex-wrap: wrap;
  }
  .base-flex-center() {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .base-icon-card() {
    height: 64px;
    cursor: pointer;
    border-radius: @border-radius;
    margin: 0 calc(@box-space / 2) @box-space;
    width: calc((100% - 2 * @box-space) / 2);
    border: @border-width solid #d1d5d9;
    transition: box-shadow 0.3s;
    &:hover {
      box-shadow: 1px 1px 4px 0 rgba(0, 0, 0, 0.13);
    }
  }
  .panel-sidebar {
    // flex: 1;
    margin-right: @box-space;
    width: calc(100% - 390px - @box-space);
    .consume {
      .base-border();
      margin-bottom: @box-space;
      .title {
        .main-font();
        display: flex;
        align-items: center;
        .timeout-message {
          height: 24px;
          padding-top: 4px;
          padding-left: 8px;
          padding-right: 8px;
          .next-message-symbol:before {
            font-size: 12px !important;
            width: 12px !important;
            margin-top: -2px;
          }
        }
      }
      .wrapper {
        // .base-flex-wrapper();
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-column-gap: 8px;
        grid-row-gap: 8px;

        .next-consume {
          background-color: #F6F6F6;
          border-radius: 4px;
          padding: 16px;
          // width: calc(100% / 4);
          // &:nth-child(-n+4) {
          //   margin-bottom: calc(@box-space / 2 * 3);
          // }
        }
      }
      .divider {
        height: 1px;
        background-color: #e5e5e5;
        margin: @box-space calc(-1 * @box-space);;
      }

    }
    .yundun {
      .base-border();
      margin-bottom: @box-space;
      .title {
        .main-font();
        display: flex;
        align-items: center;
        .display {
          display: inline-flex;
          align-items: center;
          padding: 0 12px;
          min-width: 100px;
          outline: 0;
          color: #333333;
          height: 24px;
          font-size: 12px;
          border-radius: 2px;
          border: 1px solid #C0C6CC;
        }
        .timeout-message {
          height: 24px;
          padding-top: 4px;
          padding-left: 8px;
          padding-right: 8px;
          .next-message-symbol:before {
            font-size: 12px !important;
            width: 12px !important;
            margin-top: -2px;
          }
        }
      }
      .wrapper {
        .base-flex-wrapper();
        .next-consume {
          width: calc(100% / 4);
          &:nth-child(-n+4) {
            margin-bottom: calc(@box-space / 2 * 3);
          }
        }
      }
      .divider {
        height: 1px;
        background-color: #e5e5e5;
        margin: @box-space calc(-1 * @box-space);;
      }

    }
    .recent-year {
      .base-border();
      margin-bottom: @box-space;
      .title {
        .main-font();
        display: flex;
        align-items: center;
        .timeout-message {
          height: 24px;
          padding-top: 4px;
          padding-left: 8px;
          padding-right: 8px;
          .next-message-symbol:before {
            font-size: 12px !important;
            width: 12px !important;
            margin-top: -2px;
          }
        }
        .select-app-versions{
          .next-select-trigger-search{
            height: 22px;
            margin-bottom:0px;
          }
          .next-tag{
            height: 22px;
            line-height: 22px;
            margin-bottom:0px;
          }
        }
      }
      .wrapper {
        .base-flex-wrapper();
      }
    }
    .trend-app {
      .base-border();
      margin-bottom: @box-space;
      .title {
        .main-font();
        display: flex;
        align-items: center;
      }
      .wrapper {
        .base-flex-wrapper();
      }
    }

    .muliti-version-guide {
      .base-border();
      margin-bottom: @box-space;

      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
      }

      .desc {
        color: #555;
        line-height: 20px;
      }

      .content {
        display: grid;
        grid-template-columns: var(--grid-template-columns);
        grid-column-gap: 8px;
        margin-top: 16px;

        .version-item {
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding: 16px;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          box-sizing: content-box;
          background:
            linear-gradient(to right, var(--multi-version-item-backgroundColor), white),
            linear-gradient(to bottom, var(--multi-version-item-backgroundColor), white);
          background-blend-mode: normal;

          .item-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
          }
          .item-feature{
            margin-top: 16px;
            padding: 16px;
            background: #fff;
            border-radius: 4px;
            margin-bottom: 16px;
          }
          .recommend-pro{
            position: absolute; /* 绝对定位 */
            top: 6px; /* 调整文字距离三角形顶部的距离 */
            right: 2px; /* 调整文字相对于三角形的位置 */
            transform: rotate(45deg); /* 将文字旋转45度以匹配三角形角度 */
            color: white; /* 设置文字颜色 */
            font-size: 12px; /* 设置文字大小 */
            font-weight: 500;
          }
        }
        .pro-item::before{
          content: ""; /* 伪元素必须设置content属性 */
          position: absolute; /* 绝对定位 */
          top: 0; /* 定位到顶部 */
          right: 0; /* 定位到右侧外边 */
          width: 0; /* 三角形宽度为0 */
          height: 0; /* 三角形高度为0 */
          border-style: solid; /* 边框样式 */
          border-width: 45px 0 0 45px; /* 控制三角形的大小和形状 */
          border-color: #ff6347 transparent transparent transparent; /* 设置三角形颜色，从上到下依次是上右下左 */
        }

      }
    }
  }

  .insight-sidebar {
    width: @right-side-width;
    .actions {
      .base-border();
      margin-bottom: @box-space;
      .title {
        .main-font();
      }
      .wrapper {
        .base-flex-wrapper();
        .next-action {
          width: calc((100% - @box-space) / 2);

          &:first-child {
            margin-right: @box-space;
            margin-bottom: calc(@box-space / 2);
          }
          &:nth-child(2) {
            margin-bottom: calc(@box-space / 2);
          }

          &:nth-child(3) {
            margin-right: @box-space;
          }
        }
      }
    }
    .runtime {
      .base-border();
      padding-bottom: 0;
      border-bottom: none;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      .title {
        .main-font();
        color: #000000;
        font-weight: normal;
      }
      .wrapper {
        .base-flex-wrapper();
        // 抵消边距
        margin: 0 calc(-1 *(@box-space / 2));
        .next-runtime {
          .base-flex-center();
          .base-icon-card();
          flex-direction: row;
          padding-left: @box-space;
          justify-content: flex-start;
        }
      }
    }
    .framework {
      .base-border();
      padding-bottom: 0;
      margin-bottom: @box-space;
      padding-top: calc(@box-space / 2);
      border-top: none;
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      .title {
        .main-font();
        color: #000000;
        font-weight: normal;
      }
      .wrapper {
        .base-flex-wrapper();
        margin: 0 calc(-1 *(@box-space / 2));
        .next-framework {
          .base-flex-center();
          .base-icon-card();
          flex-direction: row;
          padding-left: @box-space;
          justify-content: flex-start;
        }
      }
    }
    .practices {
      .base-border();
      margin-bottom: @box-space;
      .title {
        .main-font();
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .wrapper {
        .base-flex-wrapper();
        flex-direction: column;
        .next-practice {
          margin-bottom: calc(@box-space / 2);

          &:last-child {
            margin-bottom: 0;
          }
          .text {
            font-size: 12px;
            font-weight: normal;
            line-height: 12px;
          }
        }
      }
    }
    .news {
      .base-border();
      .title {
        .main-font();
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .wrapper {
        .base-flex-wrapper();
        margin-bottom: calc(-1 * @box-space / 2);
      }
    }
  }

}
