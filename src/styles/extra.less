// 需要修改组件内自带样式的  放在此文件内
// 其他组件样式 如有需要请根据组件名称 创建less文件
.grey-rule .next-select {
  width: 100% !important;
}

.next-icon-warning {
  color: #FFA003;
}

.next-icon-error {
  color: #FF3333;
}

.next-icon-success {
  color: #1DC11D;
}

.next-icon-minus_fill {
  color: #e3e4e6;
}

.windcc-app-layout__content {
  background-color: #fff;
}

.windcc-page__content-main {
  min-width: 1130px !important;
  padding-bottom:16px !important;
}

.full24-width {
  width: 100% !important;

  .next-select {
    width: 100%;
  }

  .next-number-picker {
    width: 100% !important;
  }
}

.next-number-picker {
  .next-input {
    .next-input-control {
      .next-number-picker-handler {
        .next-btn {
          min-width: 40px;
          border: 0;
        }
      }
    }
  }
}

.statics {
  .next-card-extra {
    padding-top: 8px;
  }
}

.next-card {
  .next-card-body {
    .next-card-content {
      height: auto !important;

      .wind-rc-data-fields {
        .wind-rc-data-fields-item {
          .next-row {
            .wind-rc-data-fields-item-label {
              color: #555;
            }
          }
        }
      }
    }
  }
}

.next-extra-card {
  .wind-rc-info {
    margin-bottom: 8px;

    .wind-rc-info-title-container {
      margin-bottom: 24px;
    }

    .wind-rc-info-content {
      margin-bottom: 0;
    }
  }

  .next-collapse-panel-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    background-color: #fff;

    &:hover {
      background-color: #fff;
    }
  }
}

.codeMirror-height-auto .CodeMirror {
  .CodeMirror-scroll {
    min-height: 400px;
  }
}

.next-radio-button>label:hover,
.next-btn.next-btn-normal:hover {
  background-color: @color-hover;
}

.next-radio-button>label:active,
.next-btn.next-btn-normal:active {
  background-color: @color-active;
}

.creating-process-card {
  .next-step-item-node-dot {
    background-color: #0070cc !important;
    width: 8px !important;
    height: 8px !important;
  }

  .next-step-item-body {
    margin-top: 4px;

    .next-step-item-content {
      color: #888888 !important;

      .next-message-content {
        padding: 0 4px !important;
      }
    }

    .next-step-item-title {
      color: #888888 !important;
    }
  }
}

.deep-number-range {
  width: 100%;
  display: flex;
  align-items: center;

  .deep-number-range-item {
    flex: 1;

    .next-number-picker {
      width: 100%;
    }
  }

  .deep-number-range-split {
    display: inline-block;
    width: 10px;
    border-bottom: 1px solid rgba(31, 56, 88, .2);
    display: inline-block;
    margin: 0 10px;
    vertical-align: middle;
    line-height: 28px;
  }
}

.radio-item-120 {
  display: inline-block;
  min-width: 120px;
  margin-right: 24px;
}

.monitor {
  .stat-card-header {
    padding: 0;
  }

  .next-card-content-container {
    margin-top: 16px;
    padding-bottom: 8px;

    .stat-card-item-title {
      font-size: 14px;
      color: #333 !important;
      font-weight: 500;
      margin-bottom: 8px;
    }
  }

}

.app-monitor {
  .next-card-head-main {
    // height: 90px;
    height: 72px;
    line-height: 1;

    .next-card-title {
      max-width: 100%;

      .vs {
        color: #ff6a00;
        font-weight: 900;
        margin-left: 8px;
        margin-right: 8px;
      }
    }
  }
}

.next-input-textarea {
  #description {
    padding: 12px;
  }
}

.app-creator-container {
  padding-bottom: 80px;

  .step-content {
    min-width: 800px;
    padding: 0 20px;

    .next-form-item {
      margin-bottom: 8px;
    }

    .deep-number-range-item .next-number-picker-normal.next-medium {
      min-width: 246px;
      max-width: 246px;
    }

    .deep-number-range-split {
      min-width: 10px !important;
    }

    .deploy-group {
      width: 100%;
      max-width: 800px;
      min-width: 50%;

      .next-radio-wrapper {
        margin-right: 0;
      }
    }

    .next-radio-label {
      margin-right: 0;
    }
  }

  .step-footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 80px;
    z-index: 10;
    padding: 15px 50px 15px 20px;
    background: #fff;
    border-top: 1px solid #ebecec;
    box-shadow: 0 -15px 35px 0 rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    // justify-content: space-between;
    // min-width: max-content;

    .bk-button {
      color: #fff;
      border-radius: 0;
      border-width: 0;
      background-color: #ff6600;

      &:hover {
        color: #fff;
        background-color: #ff4400;
      }
    }

    .nk-button {
      border-radius: 0;
    }

    .loading {
      color: #ff8a00;
      font-size: 18px;
      font-weight: 500;
    }

    .price-footer {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      overflow-x: auto;
      overflow-y: hidden;
      white-space: nowrap;
      flex-wrap: nowrap;
      height: 100%;
      gap: 12px;

      &::-webkit-scrollbar {
        display: none;
      }

      // 当小屏幕（笔记本）数据展示极限情况下，内容滚动展示
      // 此样式为滚动区左右两端遮罩
      background: linear-gradient(to right, #fff, transparent),
      linear-gradient(to right, rgba(0, 0, 0, 0.2), transparent),
      linear-gradient(to left, #fff, transparent),
      linear-gradient(to left, rgba(0, 0, 0, 0.2), transparent);
      background-size: 100px 100%,
      20px 100%,
      100px 100%,
      20px 100%;
      /* 增大尺寸 */
      background-position: left,
      left,
      right,
      right;
      background-repeat: no-repeat;
      background-attachment: local,
      scroll,
      local,
      scroll;

      >div {
        flex: 0 0 auto;
      }

      .refer-box {
        flex: 1;
        width: max-content;
        height: 100%;

        .package, .unpackage {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }
      }

      .package-box {
        height: 100%;
        display: flex;
        gap: 12px;

        .price-box {
          &:last-child {
            &::after {
              display: none;
            }
          }
        }
      }

      .price-box {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        height: 100%;

        &:not(:last-child) {
          &::after {
            content: "";
            position: absolute;
            right: -6px;
            width: 1px;
            background: #ebecec;
            height: 100%;
          }
        }

        .option {
          font-size: 12px;
          color: #333333;
          margin-bottom: -6px;
          display: flex;
          align-items: center;
        }

        .prefix {
          font-size: 12px;
          color: #ff8a00;
          margin-right: 4px;
          margin-bottom: -6px;
        }

        .amount {
          font-size: 22px;
          font-weight: 500;
          color: #ff8a00;
        }

        .unit {
          margin-bottom: -6px;
          margin-left: 6px;
        }

        .break {
          width: 1px;
          display: inline-block;
          background: #ebecec;
          margin: 0px 12px;
          height: 34px;
        }
      }

      .discount-box {
        display: flex;
        flex-direction: column;
        justify-content: center;

        .amount {
          color: green;
        }

        .detail {
          margin-bottom: 4px;
          color: #ff8a00;
          display: flex;
          align-items: center;
          cursor: pointer;
        }
      }
    }
  }

  .next-collapse-panel-content {
    padding-left: 24px;
    padding-right: 24px;
  }
}

.CndCopy {
  display: inline-flex;
}

.source-config-form {
  .next-form-item {
    margin-bottom: 12px;
  }

  .flex-radio-group {
    .next-radio-wrapper {
      align-items: center;
      height: 32px !important;
      line-height: 32px !important;
      margin: 0 !important;
    }
  }

  .trigger-radio-group {
    .next-radio-wrapper {
      height: 54px;
      line-height: 16px;

      .color-text-description {
        margin-left: 24px;
      }
    }
  }

  .tag-input {
    margin-left: 210px;
    margin-top: -20px;
  }

  .repo-box-group {
    .next-radio-wrapper {
      height: 72px !important;
      width: 94px;
      margin-right: 8px;

      .next-radio-label {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
  }

  .item-operation {
    width: calc(100% - 40px);
    margin-right: 8px !important;
  }

  .item-account {
    display: inline-flex;
    vertical-align: middle;
    justify-content: space-between;
  }
}

.cicd-detail-container {
  padding-bottom: 16px;

  .next-table-cell-wrapper {
    line-height: 23px;
  }
}

.log-container {
  height: calc(100% - 64px);
  background-color: #263238;
  overflow: scroll;

  .CodeMirror {
    border: none;

    .CodeMirror-scroll {
      height: 100%;
    }
  }
}

.cicd-list-container {

  .next-icon.next-medium:before,
  .next-icon.next-medium .next-icon-remote {
    width: 16px;
    font-size: 16px;
  }

  .vc-image {
    width: 20px !important;
    height: 20px !important;
  }
}

.account-menu-item {
  display: flex;
  align-items: center;
  height: 32px;
  line-height: 32px;
  justify-content: space-between;

  span>img {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    margin-right: 4px;
  }
}

.log-panel {
  display: flex;
  align-items: start;
  justify-content: space-between;

  .next-select-single {
    margin-bottom: 8px;
  }

  .next-range-picker {
    margin-bottom: 8px;
    margin-right: 8px;
  }
}

.app-log,
.gray-log {
  .next-loading-wrap {
    height: 100%;

    .codeMirror-height-auto {
      height: 100%;

      .CodeMirror {
        height: 100%;
      }
    }
  }
}

.pipeline {
  .next-loading-wrap {
    height: 100%;

    .next-table-cell-wrapper {
      line-height: 26px;
    }
  }
}

.multiple-chart {
  .next-select-trigger {
    min-width: 80px;

    .next-input {
      border: none;
    }
  }
}

.field-item-extra {
  margin-left: 220px;
  margin-top: -8px;
  margin-bottom: 16px;
}

.theme-xconsole {
  --input-maxlen-error-color: var(--color-error-7, #C80000);
}

.create-apptype {
  .next-radio-group.next-radio-button {
    width: 100%;

    .next-radio-wrapper {
      border: solid 1px #C0C6CC;
      padding: 10px;
      width: calc(50% - 8px);
      min-width: 220px;

      &.checked {
        border-color: #0070cc;
        // background: #e5fcff;
      }

      .next-radio-label {
        margin: 0;
      }

      .card {
        text-align: left;
        line-height: 160%;

        .title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 4px;
        }
      }
    }
  }
}

.package-deploy-panel {
  margin-bottom: 16px;
  padding: 0 16px;
  height: 40px;
  font-size: 12px;
  line-height: 40px;
  color: #333;
  background-color: #f7f7f7;

  &.requried:before {
    margin-right: 4px;
    content: "*";
    color: #C80000;
  }
}

.timestamp {
  position: absolute;
  line-height: 32px;
}

.full24-tiled {
  display: flex;
  position: relative;

  .table-field-items {
    width: 100%;
    padding-bottom: 40px;

    .table-field-layout-tiled {
      .table-field-tiled-item {
        display: flex;
        flex-direction: row-reverse;

        // 行内容
        .tiled-item-form {
          flex: 1;

          .deep-form {
            width: 100%;
            display: flex;

            .deep-text-form-field {
              flex: 1;
              margin-right: 16px;
              margin-bottom: 8px !important;

              .next-form-item-control {
                width: 100%;
                flex: 1;
                max-width: 100%;

                .next-select {
                  width: 100%;
                }
              }
            }

            .deep-select-form-field {
              flex: 1;
              margin-right: 16px;
              margin-bottom: 8px !important;

              .next-form-item-control {
                width: 100%;
                flex: 1;
                max-width: 100%;

                .next-select {
                  width: 100%;
                }
              }
            }

            .deep-number-form-field {
              flex: 1;
              margin-right: 16px;
              margin-bottom: 8px !important;

              .next-form-item-control {
                width: 100%;
                flex: 1;
                max-width: 100%;

                .next-number-picker {
                  width: 100%;
                }
              }
            }
          }
        }

        // 操作按钮
        .tiled-item-title {
          padding: 0 !important;
          background: #fff !important;
          border-bottom: none !important;

          .item-actions {
            .next-icon-alone:before {
              font-size: 20px;
              // color: #0070cc;
            }

            button {
              padding: 0;
              background: #fff;
              width: 22px;
              display: flex;
              align-items: center;
              margin: 0 !important;
            }
          }
        }
      }
    }
  }

  .table-field-buttons {
    position: absolute;
    bottom: 0;
    left: 0;
    margin-bottom: 0 !important;

    button {
      height: 32px;
      margin-top: 4px;
      padding: 0 16px
    }
  }
}

.full24-table {
  width: 100% !important;

  .table-field-items {
    .table-field-layout-table {
      min-height: 100px !important;
      margin-bottom: 16px !important;
      padding-bottom: 8px !important;
      border-bottom: 1px solid #e3e4e6;

      .table-field-table-row {
        .table-field-table-cell {
          padding: 8px 8px 0px 8px !important;

          .item-actions {
            width: 30px;
            display: flex;
            justify-content: center;

            .next-icon-alone:before {
              font-size: 20px;
              // color: #0070cc;
            }

            button {
              padding: 0;
              // margin-left: 16px;
              background: #fff;
            }
          }

          .next-number-picker {
            width: 100%;
          }
        }
      }
    }
  }

  .table-field-buttons {
    button {
      height: 32px;
      margin-top: 0px;
      padding: 0 16px
    }
  }
}

.nat-radio-field {
  .next-form-item-help {
    padding-left: 200px;
  }
}

.time-selector {
  .next-select-inner {
    width: 110px !important;
    min-width: 110px !important;
  }
}

.vswitchs-tag.next-tag-group {
  .next-tag {
    height: max-content;

    .next-tag-body {
      text-align: left;
    }
  }
}

.create-app {
  .sc-1bsfzqf-0 {
    padding: 0;
    padding-top: 16px;
  }
}

.deploy-selector {
  width: 100%;

  .next-form-item-control {
    .next-radio-group {
      .next-radio-label {
        display: inline-flex;
        align-items: center;
        width: calc(100% - 180px);

        .inline-block {
          width: 150px !important;
        }
      }
    }
  }

  // max-width: 800px;
  // min-width: 50%;
  // .next-radio-wrapper {
  //   margin-right: 0;
  // }
}

.next-table {
  .next-table-empty {
    height: max-content;

    a {
      color: #0070cc;

      :hover {
        cursor: pointer;
      }
    }
  }
}

.xconsole-rc-region-item-count {
  border-radius: 2px;
  background-color: #efefef;

  margin-left: 12px;
  padding: 0 4px;
  float: right;
  line-height: 18px;
  height: 18px;
  margin-top: 8px;
}

.price-table {
  .color-notice {
    color: #FF5C03;
  }
}

.next-card {
  .next-card-divider::before {
    border-bottom: 1px solid #e4e8ee;
  }
}

.next-card.overflow-visible {
  overflow: visible;

  .next-card-content {
    overflow: visible;
  }
}

.namespace-card {
  .next-col-12 {
    .next-col-fixed-8 {
      flex: 0 0 220px;
      width: 220px;
      max-width: 220px;
    }
  }
}

.mtable-tree {
  .next-table {
    .next-table-inner {
      .next-table-body {
        .next-table-row {
          .next-table-cell {
            .next-table-cell-wrapper {
              display: flex;

              .next-table-tree-placeholder {
                width: var(--icon-expend-width);
                height: var(--icon-expend-height);
              }
            }
          }
        }
      }
    }
  }
}

.next-number-picker {
  .next-error.next-input {
    border-color: #C80000;
  }
}

.inline-extra .deep-form-field-extra {
  display: inline;
}

.next-table {
  .next-form-item {
    margin-bottom: 0;
  }
}

.nacos-select {
  .next-form-item-control {
    .next-select {
      width: 100%;
    }
  }
}

.package-box-group {
  .next-radio-wrapper {
    height: 72px !important;
    width: 94px;
    margin-right: 8px;
    border: solid 1px #C0C6CC;
    padding: 10px;
    text-align: center;

    .next-radio {
      display: none;
    }

    &.checked {
      border-color: #0070cc;

      // background: #e5fcff;
      .next-radio-label {
        color: #0064c8;
      }
    }

    .next-radio-label {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin: 0;
    }
  }
}

.next-form-item-control>.next-form-preview {
  height: 32px !important;
  line-height: 32px !important;
  display: inline-flex;
  align-items: center;
}

.route-config-table{
  .next-table-header {
    display: none;
  }
  td {
    border-bottom: none;
  }
  .next-table-cell{
    .next-table-cell-wrapper{
      padding-left: 0;
      padding-right: 8px;
    }
  }
}


.extra-table {
  .wind-rc-table {
    .action-bar {
      .action-bar-right {
        display: flex;
        flex: 1;
        justify-content: space-between;
      }
    }
  }
}

ali-alfa-cloud-cms-widget-home {
  #cms {
    padding: 0;
  }
}

.fullsceen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.next-tabs.next-medium .next-tabs-tab-inner{
  padding: 8px 16px;
}
.next-tabs-pure>.next-tabs-bar .next-tabs-nav-container .next-tabs-tab{
  color: #333;
}
.next-radio-button>label .next-radio-label{
  color: #333;
}
.windcc-app-layout__nav{
  .next-menu{
    .next-menu-content{
      .next-menu-item-inner{
        height: 36px !important;
        line-height: 36px !important;
      }
    }
  }
}

.windcc-app-layout__content{
  .custom-page-layout{
    >div:nth-of-type(1) {
      margin-top:24px;
      margin-bottom: 8px;
      height: 20px;
      line-height: 20px;
    }
    >div:nth-of-type(2) {
      height: 36px;
      line-height: 36px;
      >div{
        height: 36px;
        line-height: 36px;
        h3{
          font-size: 24px;
          font-weight: 600;
          height: 36px;
          line-height: 36px;
          .wind-rc-truncate{
            max-width:100% !important;
          }
        }
        >span {
          margin-right: 8px !important;
          height: 36px;
          line-height: 36px;
        }
      }
    }
    .next-menu{
      .next-menu-item{
        height: 36px !important;
        line-height: 36px !important;
      }
    }
  }
  .overview-page-layout{
    >div:nth-of-type(1) {
      margin-top:24px;
      margin-bottom: 8px;
      height: 20px;
      line-height: 20px;
    }
  }
  .route-detail-page{
    >div:nth-of-type(1){
      margin-top:0px;
      h3{
        font-size: 24px;
      }
    }
  }
}
.next-breadcrumb {
  .next-breadcrumb-item{
    .next-breadcrumb-text,.next-breadcrumb-text>a{
      color: #555;
    }
    .next-breadcrumb-text.activated{
      color: #aaa;
    }
    .next-breadcrumb-separator{
      color: #aaa;
    }
  }
}

.wind-rc-data-fields{
  .wind-rc-data-fields-item-label{
    color: #333 !important;
    font-weight: 500 !important;
  }
}

.recent-year-range-picker{
  .next-range-picker-trigger-input.next-input{
    min-width: auto;
  }
}

.modal_slidepanel__footer {
  z-index: 1;
}

.image-table .next-table-cell-wrapper {
  overflow: visible !important;
}

.field-hide {
  width: 0 !important;
  height: 0 !important;
  overflow: hidden;
  border: none;
  display: block;
}

.hacker-watermark {
  position: absolute;
  top: 10px;
  left: 500px;
  z-index: 1000;
  height: 26px;
  line-height: 26px;
  font-size: 18px;
  background-color: #0070cc;
  color: #fff;
  text-align: center
}

.next-card-collapse {
  .next-collapse-panel-title {
    background-color: #f7f9fa !important;
  }
  .next-collapse-panel-expanded .next-collapse-panel-content {
    padding-top: 8px;
  }
}

.select-flex-content {
  .next-menu-content {
    display: flex;
    flex-wrap: wrap;
  }

  .next-menu-item {
    padding: 0 !important;
    flex: 0 0 calc(100% / 7);
    text-align: center;

    &.next-selected {
      background-color: #EFF3F8 !important;

      .next-icon-select {
        display: none;
      }

      .next-menu-item-text {
        color: #0064C8;
      }
    }
  }
}

.input-border {
  width: max-content;
  border: 1px solid #e5e5e5;
  outline: none;
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: #0064C8;
  }

  .condition-item {
    .next-input {
      background-color: #F7F9FA;
    }
  }
}

.yaml-deploy {
  .next-upload-dragable .next-upload-drag {
    border-color: #ccc;
  }
  .codeMirror-height-auto .CodeMirror {
    border-color: #ccc;
  }
}

.wind-rc-status-indicator {
  display: inline-flex;
  align-items: flex-start;
}