import { useEffect, useMemo, useState } from 'react'
import services from '~/services';
import { flatten, isEmpty } from 'lodash';

const useInstances = (props) => {
  const { appId } = props;
  const [instances, setInstances] = useState([]);

  useEffect(() => {
    getInstances();
  }, []);

  const getInstances = async () => {
    const instanceGroup = await getInstanceGroup();
    const instancesArr = await Promise.all(instanceGroup.map(async (item) => {
      const { GroupId } = item;
      const instanceList = await getInstanceList(GroupId);
      return instanceList;
    }));
    setInstances(flatten(instancesArr));
  };

  const getInstanceGroup = async () => {
    const res = await services.DescribeApplicationGroups(
      {
        AppId: appId,
      },
      true,
    );
    const { Data = [] } = res || {};
    return Data;
  };

  const getInstanceList = async (GroupId) => {
    const _params = {
      AppId: appId,
      GroupId,
      CurrentPage: 1,
      PageSize: 50,
    };
    const res = await services.DescribeApplicationInstances(_params, true);
    const { Data: { Instances = [], TotalSize = 0 } = {} } = res || {};
    return Instances;
  };
  
  const existSidecarContainer = useMemo(() => {
    const exist = instances.some((item) => !isEmpty(item.SidecarContainersStatus));
    return exist;
  }, [instances]);

  return {
    instanceList: instances,
    existSidecarContainer,
  };
}

export default useInstances;