import { useEffect, useState } from 'react';
import { get } from 'lodash';
import services from '~/services';

const useSecurityGroupInfo = (props) => {
  const { regionId, vpcId, securityGroupId } = props;

  const [securityGroup, setSecurityGroup] = useState<any>({});

  useEffect(() => {
    if (vpcId) {
      getSecurityGroups();
    }
  }, [vpcId, securityGroupId]);

  const getSecurityGroups = async () => {
    const res = await services.DescribeSecurityGroups({
      RegionId: regionId,
      VpcId: vpcId,
      SecurityGroupId: securityGroupId,
    });
    if (!res) return;
    const { SecurityGroups: { SecurityGroup = [] } = {} } = res;
    setSecurityGroup(get(SecurityGroup, '[0]', {}));
  };

  return {
    securityGroup,
  }
}

export default useSecurityGroupInfo;