import { useEffect, useState } from 'react';
import { get } from 'lodash';
import services from '~/services';

const useVpcInfo = (props) => {
  const { vpcId } = props;

  const [vpc, setVpc] = useState<any>({});

  useEffect(() => {
    if (vpcId) {
      getVpcs();
    }
  }, [vpcId]);

  const getVpcs = async () => {
    const res = await services.DescribeVpcs({
      params: {
        VpcId: vpcId,
      },
      ignoreError: true,
    });
    if (!res) return;
    const { Vpcs: { Vpc = [] } = {} } = res;
    setVpc(get(Vpc, '[0]', {}));
  };

  return {
    vpc,
  };
};

export default useVpcInfo;