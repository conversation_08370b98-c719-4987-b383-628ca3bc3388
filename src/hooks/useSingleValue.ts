import { useState } from 'react';

/**
 * 自定义 Hook 接收单值的 value、onChange, 返回数组类型的 value和 onChange，用于 Table 单选的场景。
 * @param props - 包含 value 和 onChange 的对象。
 * @returns 状态数组和状态更新函数。
 */
const useSingleValue = <T>(props: { value: T | null, onChange?: (value: T | undefined) => void }): [T[], (value: T[]) => void] => {
  const { value, onChange } = props;
  const [stateValue, setStateValue] = useState<T[]>([value as T]);

  const onChangeArr = (val: T[]): void => {
    if (val.length > 0) {
      setStateValue(val);
      onChange?.(val[0]);
    } else {
      setStateValue([]);
      onChange?.(undefined); // 当没有新值时传递 undefined 给 onChange
    }
  };

  return [stateValue, onChangeArr];
};

export default useSingleValue;