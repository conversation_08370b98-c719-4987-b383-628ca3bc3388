import { useEffect, useState } from 'react';
import services from '~/services';
import { isForbidden } from '~/utils/authUtils';

const useVSwitchInfo = (props) => {
  const { vpcId, vSwitchId } = props;

  const [vSwitchList, setVSwitchList] = useState<any>([]);
  const [isOnlyShenZhenA, setIsOnlyShenZhenA] = useState(false);
  const [authed, setAuthed] = useState(true);

  useEffect(() => {
    if (vpcId) {
      getVswitches();
    }
  }, [vpcId, vSwitchId]);

  const getVswitches = async () => {
    const res = await services.DescribeVSwitches({
      params: {
        VpcId: vpcId,
        VSwitchId: vSwitchId,
      },
      customErrorHandle: (error, _p, cb) => {
        if (isForbidden(error.code)) {
          setAuthed(false);
        }
      },
    });
    if (!res) return;
    const { VSwitches: { VSwitch = [] } = {} } = res;
    setVSwitchList(VSwitch);
    const _isOnlyShenZhenA =
      VSwitch.length > 0 &&
      VSwitch.every(item => {
        return item.ZoneId === 'cn-shenzhen-a';
      });
    setIsOnlyShenZhenA(_isOnlyShenZhenA);
  }

  return {
    vSwitchList,
    isOnlyShenZhenA,
    authed,
  };
};

export default useVSwitchInfo;