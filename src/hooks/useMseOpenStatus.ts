import { useContext, useEffect } from 'react'
import FeatureContext from '~/utils/featureContext';
import { getMseOpenStatus } from '~/utils/openStatus';

const useMseOpenStatus = () => {
  const { openStatus, setOpenStatus } = useContext(FeatureContext);

  useEffect(() => {
    queryMseStatus();
  }, []);

  const queryMseStatus = async () => {
    const mseStatus = await getMseOpenStatus();
    if (!mseStatus) return;

    const { Status, Version, FreeVersion } = mseStatus;
    setOpenStatus({
      ...openStatus,
      [`mse${Version}`]: Status === 2,
    });
  };
  
  return {
    openStatus,
  }
}

export default useMseOpenStatus