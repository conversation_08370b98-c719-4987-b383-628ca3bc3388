type AppItem = {
  applicationID: string;
  applicationName: string;
  args: string;
  caPort: number;
  command: string;
  cpu: number;
  createdTime: string;
  customDomainName?: string;
  customHealthCheckConfig?: {
    httpGetUrl: string;
    initialDelaySeconds: number;
    periodSeconds: number;
    timeoutSeconds: number; 
    failureThreshold: number; 
    successThreshold: number;
  }; 
  description: string;
  enableAppMetric?: boolean;
  environmentVariables?: {
    [key: string]: string;
  };
  httpTriggerConfig: {
    authType: string;
    disableURLInternet: boolean;  // 入口流量类型
  };
  imageConfig: {
    accelerationType: string;
    image: string;
  };
  instanceConcurrency: number; // 单实例并发请求数
  lastModifiedTime: string;
  logConfig?: {
    logstore: string;
    project: string;
  };
  memorySize: number;
  namespaceID: string;
  namespaceName: string;
  scaleConfig: {
    alwaysAllocateCPU: boolean; // CPU 分配策略
    maximumInstanceCount: number;
    minimumInstanceCount: number;
  };
  timeout: number;
  vpcConfig?: {
    vpcId: string;
    securityGroupId: string;
    vSwitchIds: string[];
  };
  nasConfig: {
    mountPoints: {
      nasId: string;
      mountDomain: string;
      nasPath: string;
      mountDir: string
    }[];
  };
  ossMountConfig: {
    mountPoints: {
      bucketName: string;
      bucketPath: string;
      mountDir: string;
      readOnly: boolean;
    }[];
  };
};


type WebAppCopyItem  ={
  imageConfig: {
    accelerationType: string;
    image: string;
  };
  basicCopyValue: {
    applicationName:  string;
    description: string;
    appType: 'web' | 'micro';
    namespaceID: string;
    disableURLInternet: boolean;  // 入口流量类型
    authType: string;  // 调用鉴权认证
    caPort: string; // HTTP 监听端口
    timeout: number; // 请求超时时间
    spec: {
      cpu: number;
      memory: number;
    };
    alwaysAllocateCPU: boolean; // CPU 分配策略
    instanceConcurrency: number; // 单实例并发请求数
    scaleConfig: {
      start: number,
      end: number,
    }
  };
  advanceCopyValue: {
    startCmd: {
      Command: string[];
      CommandArgs: string[];
    };
    logConfig: {
      logstore: string;
      project: string;
    };
    environmentVariables: {
      [key: string]: string;
    };
    customHealthCheckConfig: {
      enableHealthCheck: boolean;
      httpGetUrl: string;
      initialDelaySeconds: number;  // 首次探测延迟时间
      periodSeconds: number; // 探测时间间隔
      timeoutSeconds: number; // 探测超时时间
      failureThreshold: number; // 最大失败次数
      successThreshold: number; // 探测成功阈值
    };
    armsConfig: {
      enableAppMetric: boolean;
    };
    vpcConfig: {
      enableVpc: boolean;
      vpcId: string;
      securityGroupId: string;
      vSwitchIds: string[];
    };
    nasConfig: {
      mountPoints: {
        nasId: string;
        mountDomain: string;
        nasPath: string;
        mountDir: string
      }[];
    };
    ossMountConfig: {
      mountPoints: {
        bucketName: string;
        bucketPath: string;
        mountDir: string;
        readOnly: boolean;
      }[];
    };
  };
  storageKey: () => 'nas' | 'oss';
};