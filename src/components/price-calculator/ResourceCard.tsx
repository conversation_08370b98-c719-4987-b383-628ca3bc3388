import React from 'react';
import { Button, Card, Form, Icon, intl, NumberPicker } from '@ali/cnd';
import ResourceContent from './ResourceContent';

const ResourceCard = (props) => {
  const { field, envType, regionId, title, name, onDelete } = props;

  const titleContent = (
    <div className='flex'>
      <div>{title}</div>
      <Form.Item
        size='small'
        name={`${name}.AppCount`}
        className='mb-none ml'
      >
        <NumberPicker
          defaultValue={1}
          min={1}
          hasTrigger={false}
          size="small"
          innerAfter={intl('general.unit.count')}
          className='flex'
          style={{ width: 100 }}
        />
      </Form.Item>
    </div>
  )

  return (
    <Card
      className='resource-card mt-s overflow-visible'
      contentHeight={350}
      title={titleContent}
      extra={
        <Button
          text
          onClick={onDelete}
        >
          <Icon type="ashbin" />
        </Button>
      }
    >
      <ResourceContent
        name={name}
        field={field}
        envType={envType}
        regionId={regionId}
      />
    </Card>
  )
}

export default ResourceCard;