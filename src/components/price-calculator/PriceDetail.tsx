import { intl } from '@ali/cnd';
import React from 'react';
import { Icon, Loading, CndTable } from '@ali/cnd';
import { Id2Label, Id2Price, PriceTypes } from './constants';
import { like } from './like';
import _ from 'lodash';
import SimpleCollapse from '~/components/shared/SimpleCollapse';
import CachedData from '~/cache/common';

type PriceData = {
  TotalPrice: number;
  Items: Array<any>;
  Label: Array<any>;
  Duration: string; //'年' | '月'
};

type Props = {
  priceData: PriceData;
  loading: boolean;
};

type State = {
  Items?: Array<any>;
  collapsed: boolean;
  Label: Array<any>;
  TotalPrice: number;
  Duration: string; //'年' | '月'
};

export default class PriceDetail extends React.Component<Props, State> {
  constructor(props) {
    super(props);

    this.state = {
      collapsed: false,
      Items: this.props.priceData.Items,
      Label: this.props.priceData.Label,
      TotalPrice: this.props.priceData.TotalPrice,
      Duration: this.props.priceData.Duration,
    };

    this.detailTable = this.detailTable.bind(this);
    this.packDom = this.packDom.bind(this);
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.priceData !== this.props.priceData) {
      this.setState(nextProps.priceData);
    }
  }

  setSteps(arr) {
    let listItems = arr.map((v, i) => {
      if (i === arr.length - 1) {
        return (
          <span>
            {intl('saenext.components.price-calculator.PriceDetail.Gradient')}
            {i + 1}{intl("sae.common.comma")} &#91;{v.Begin},&#43;∞&#41; {v.Unit} ¥{v.Price}
          </span>
        );
      } else {
        return (
          <span>
            {intl('saenext.components.price-calculator.PriceDetail.Gradient')}
            {i + 1}{intl("sae.common.comma")} &#91;{v.Begin},{v.End}&#41; {v.Unit} ¥{v.Price}
            <br></br>
          </span>
        );
      }
    });
    if (arr.length === 1) {
      listItems = <span>¥{_.get(arr, '[0].Price')}</span>;
    }
    return listItems;
  }

  renderCharges(v: string) {
    const PackCU = /^(PACK_CU)/;
    const PostCu = /^(POST_CU)/;
    if (PackCU.test(v)) {
      return intl('saenext.components.price-calculator.PriceDetail.CuResourcePackage');
    }
    if (PostCu.test(v)) {
      return intl('saenext.components.price-calculator.PriceDetail.CuUsage');
    }
    return Id2Label[v] || v;
  }

  packDom() {
    let itemlist = this.state.Items.filter((v) => {
      if (v.Type === 'PACK') {
        return true;
      }
    });
    return (
      <CndTable dataSource={itemlist} hasBorder={false}>
        <CndTable.Column
          title={intl('saenext.components.price-calculator.PriceDetail.Charges')}
          dataIndex="Id"
          cell={this.renderCharges}
        />

        <CndTable.Column
          title={intl(
            'saenext.components.price-calculator.PriceDetail.ResourcePackageSpecifications',
          )}
          dataIndex="Id"
          cell={(v) => {
            return Id2Price[v] || v;
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.price-calculator.PriceDetail.NumberOfPurchases')}
          dataIndex="Count"
          cell={(v) => {
            return intl('saenext.components.price-calculator.PriceDetail.V', { v: v });
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.price-calculator.PriceDetail.FeePerThisstateduration', {
            thisStateDuration: this.state.Duration,
          })}
          dataIndex="Price"
          cell={(v) => {
            return `¥${v}`;
          }}
        />
      </CndTable>
    );
  }
  detailTable() {
    return (
      <div className="pt-l pb-l">
        {this.state.Label.find(
          (v) => v === intl('saenext.components.price-calculator.PriceDetail.ResourcePackage'),
        )
          ? this.packDom()
          : null}
        {this.state.Label.find(
          (v) => v === intl('saenext.components.price-calculator.PriceDetail.PayAsYouGo'),
        ) ? (
          <>
            {this.state.Label.find(
              (v) => v === intl('saenext.components.price-calculator.PriceDetail.ResourcePackage'),
            ) ? (
              <div
                style={{
                  marginTop: '20px',
                  marginBottom: '10px',
                }}
              >
                {intl('saenext.components.price-calculator.PriceDetail.PayAsYouGoBeyond')}
              </div>
            ) : null}
            <CndTable
              dataSource={this.state.Items.filter((item) => {
                return item.Id != 'POST_INTERNET_OUTBOUND' && item.Type === 'POST';
              })}
              hasBorder={false}
              key="detailTable"
            >
              <CndTable.Column
                title={intl('saenext.components.price-calculator.PriceDetail.Charges')}
                dataIndex="Id"
                cell={this.renderCharges}
              />

              <CndTable.Column
                title={intl(
                  'saenext.components.price-calculator.PriceDetail.TotalResourcesPerThisstateduration',
                  { thisStateDuration: this.state.Duration },
                )}
                dataIndex="Amount"
                cell={(value, index, record) => {
                  return `${value}${record.Unit}`;
                }}
              />

              <CndTable.Column
                title={intl('saenext.components.price-calculator.PriceDetail.UnitPrice')}
                dataIndex="Steps"
                cell={(value) => this.setSteps(value || [])}
              />

              <CndTable.Column
                title={intl(
                  'saenext.components.price-calculator.PriceDetail.TotalFeePerThisstateduration',
                  { thisStateDuration: this.state.Duration },
                )}
                dataIndex="Price"
                cell={(value) => `¥${value}`}
              />
            </CndTable>
            {this.state.Items.find((item) => item.Id === 'POST_INTERNET_OUTBOUND') ? (
              <>
                <div
                  style={{
                    marginTop: '22px',
                    marginBottom: '6px',
                  }}
                >
                  {intl('saenext.components.price-calculator.PriceDetail.OtherCharges')}
                </div>
                <CndTable
                  dataSource={this.state.Items.filter((item) => {
                    return item.Id === 'POST_INTERNET_OUTBOUND';
                  })}
                  hasBorder={false}
                >
                  <CndTable.Column
                    title={intl('saenext.components.price-calculator.PriceDetail.Charges')}
                    dataIndex="Id"
                    cell={this.renderCharges}
                  />

                  <CndTable.Column
                    title={intl(
                      'saenext.components.price-calculator.PriceDetail.TotalResourcesPerThisstateduration',
                      { thisStateDuration: this.state.Duration },
                    )}
                    dataIndex="Amount"
                    cell={(value, index, record) => {
                      return `${value}${record.Unit}`;
                    }}
                  />

                  <CndTable.Column
                    title={intl('saenext.components.price-calculator.PriceDetail.UnitPrice')}
                    dataIndex="Steps"
                    cell={(value) => this.setSteps(value || [])}
                  />

                  <CndTable.Column
                    title={intl(
                      'saenext.components.price-calculator.PriceDetail.TotalFeePerThisstateduration',
                      { thisStateDuration: this.state.Duration },
                    )}
                    dataIndex="Price"
                    cell={(value) => `¥${value}`}
                  />
                </CndTable>
              </>
            ) : null}
          </>
        ) : null}
      </div>
    );
  }

  render() {
    const { loading } = this.props;
    return (
      <Loading visible={loading} style={{ width: '100%' }}>
        <ul style={{ marginTop: '16px', marginBottom: '8px' }}>
          <li
            style={{
              display: 'flex',
              fontWeight: '500',
              height: '36px',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                fontSize: '20px',
                lineHeight: '36px',
                paddingRight: '8px',
              }}
            >
              SAE {this.state.Label?.join('+')}
            </div>
            {this.state.Label.includes(PriceTypes.PACK) && (
              <>
                <div
                  style={{
                    color: '#3DB451',
                    backgroundColor: 'rgba(99,195,114,.1)',
                    border: '1px solid #3DB451',
                    borderRadius: '24px',
                    fontSize: '12px',
                    lineHeight: '20px',
                    padding: '2px 8px',
                    marginTop: '6px',
                    marginBottom: '6px',
                  }}
                >
                  {intl('saenext.components.price-calculator.PriceDetail.LowestCost')}
                </div>
                <div
                  style={{
                    display: 'flex',
                    color: '#FF6A00',
                    fontSize: '14px',
                    marginLeft: '8px',
                  }}
                >
                  {like}
                  <span
                    style={{
                      // verticalAlign: 'text-bottom',
                      paddingLeft: '4px',
                    }}
                  >
                    {intl(
                      'saenext.components.price-calculator.PriceDetail.RecommendPurchaseSaeMoonBag',
                    )}
                  </span>
                </div>
                <a
                  href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=sae_cupackage_dp_cn`}
                  target="_blank"
                  style={{
                    fontSize: '14px',
                    color: '#FF6A00',
                    marginLeft: '16px',
                    height: '18px',
                  }}
                >
                  {intl('saenext.components.price-calculator.PriceDetail.BuyNow')}
                  <Icon type="arrow-right" className="ml-xs" size="small" />
                </a>
              </>
            )}
          </li>
          <li style={{ display: 'flex', lineHeight: '52px' }}>
            <div
              style={{
                fontSize: '14px',
                paddingRight: '8px',
              }}
            >
              {intl('saenext.components.price-calculator.PriceDetail.TotalApplicationInstanceCost')}
              /{this.state.Duration}
            </div>
            <div
              style={{
                fontSize: '36px',
                color: '#FF6A00',
              }}
            >
              <span style={{ fontSize: '20px', lineHeight: '20px' }}>¥</span>
              {this.state.TotalPrice}
            </div>
          </li>
        </ul>
        <SimpleCollapse
          text={intl('saenext.components.price-calculator.PriceDetail.CostDetails')}
          defaultOpen={false}
        >
          {this.detailTable()}
        </SimpleCollapse>
      </Loading>
    );
  }
}
