import React, { useContext, useEffect, useState } from 'react';
import { confFeature, Form, Input, intl, NumberPicker, Radio, Select, Switch } from '@ali/cnd';
import If from '../shared/If';
import DecimalNumberPicker from '../shared/Sidecar/DecimalNumberPicker';
import CpuMemField, { ResourceSpec } from '../shared/CpuMemField';
import IdleContent from './IdleContent';
import { get, isUndefined } from 'lodash';
import TextWithBalloon from '../shared/TextWithBalloon';
import FeatureContext from '~/utils/featureContext';
import moment from 'moment';

const ResourceContent = (props) => {
  const { name, field, envType, regionId } = props;

  const diskSize_enable = confFeature('diskSize_enable');

  const { feature, webFeature } = useContext(FeatureContext);
  const { idle } = feature;

  const AccountOpenTime = webFeature?.AccountOpenTime;
  const multipleVersionsTime = get(window, 'ALIYUN_CONSOLE_GLOBAL.multipleVersionsTime');
  // 是否多版本新用户 --- 轻量版+专业版
  const isMultipleNewAccount =
    AccountOpenTime && multipleVersionsTime
      ? AccountOpenTime > moment(multipleVersionsTime).valueOf()
      : false;
  // 判断老用户是否加白 --- 加白：轻量版+标准版+专业版  未加白：不区分版本
  const enableNewSaeVersion = feature?.enableNewSaeVersion;
  // 多版本新用户是否加白 加白则支持标准版
  const multipleNewAccountStdEnable = confFeature('multipleNewAccount_std_enable');

  useEffect(() => {
    if (enableNewSaeVersion || isMultipleNewAccount) {
      field.setValue(`${name}.NewSaeVersion`, 'lite');
    } else {
      field.setValue(`${name}.NewSaeVersion`, 'standard');
    }
  }, [enableNewSaeVersion, isMultipleNewAccount])

  const {
    ResourceType = 'default',
    MinInstanceCount,
    MaxInstanceCount,
    EnableCpuIdle,
  } = field.getValue(`${name}`);

  const onResourceTypeChange = (v) => {
    const defaultSpec = ResourceSpec[v];
    field.setValue(`${name}.CpuCore`, defaultSpec.cpu);
    field.setValue(`${name}.MemoryGiB`, defaultSpec.memory);
  };

  const countValid = (rule, value, callback) => {
    const { MinInstanceCount, MaxInstanceCount } = field.getValue(`${name}`);
    if (
      !isUndefined(MaxInstanceCount) &&
      !isUndefined(MinInstanceCount) &&
      MaxInstanceCount < MinInstanceCount
    ) {
      return callback(
        intl('saenext.components.price-calculator.BasicInformation.TheMaximumNumberOfInstances'),
      );
    } else {
      return callback();
    }
  };

  return (
    <>
      {enableNewSaeVersion || isMultipleNewAccount ?
        <Form.Item
          required
          label={intl('saenext.components.price-calculator.ResourceContent.VersionType')}
          name={`${name}.NewSaeVersion`}
        >
          <Radio.Group
            defaultValue="lite"
            dataSource={[
              {
                label: intl('saenext.components.price-calculator.ResourceContent.LightweightVersion'),
                value: 'lite',
              },
              {
                label: intl('saenext.components.price-calculator.ResourceContent.StandardEdition'),
                value: 'standard',
                visible: (!isMultipleNewAccount && enableNewSaeVersion) || (isMultipleNewAccount && multipleNewAccountStdEnable)
              },
              {
                label: intl(
                  'saenext.components.price-calculator.ResourceContent.ProfessionalEdition',
                ),
                value: 'pro',
              },
            ].filter(item => item.visible !== false)}
          />
        </Form.Item>
        :
        <Form.Item
          required
          className='none'
          name={`${name}.NewSaeVersion`}
        >
          <Input defaultValue={'standard'} />
        </Form.Item>
      }
      <div className="grid-container-4">
        <Form.Item
          required
          label={intl('saenext.components.price-calculator.ResourceContent.ResourceType')}
        >
          <Radio.Group
            name={`${name}.ResourceType`}
            defaultValue="default"
            dataSource={[
              {
                label: 'Intel',
                value: 'default',
              },
              {
                label: 'Hygon',
                value: 'haiguang',
              },
            ]}
            onChange={onResourceTypeChange}
          />
        </Form.Item>
        <Form.Item
          label={intl('saenext.components.price-calculator.ResourceContent.InstanceType')}
          required
        >
          <CpuMemField
            key={`${ResourceType}-spec`}
            field={field}
            resourceType={ResourceType}
            cpuName={`${name}.CpuCore`}
            memoryName={`${name}.MemoryGiB`}
          />
        </Form.Item>
        <If condition={diskSize_enable}>
          <Form.Item
            label={intl('saenext.micro-app.basic-info.SpecDialog.TemporaryDiskSize')}
            name={`${name}.EphemeralStorageGiB`}
            required
          >
            <NumberPicker
              defaultValue={20}
              min={20}
              max={500}
              innerAfter="GiB"
              hasTrigger={false}
              style={{ width: 250 }}
            />
          </Form.Item>
        </If>
      </div>
      <div className="grid-container-4">
        <If condition={envType === 'Production'}>
          <Form.Item
            required
            label={intl('saenext.components.price-calculator.ResourceContent.PeakDurationPerDay')}
            name={`${name}.HighLoadSeconds`}
          >
            <DecimalNumberPicker
              min={0}
              max={24}
              ratio={3600}
              innerAfter={intl('saenext.components.price-calculator.ResourceContent.Hours')}
              hasTrigger={false}
              style={{ width: 250 }}
              onChange={(v) =>
                !isUndefined(v) && field.setValue(`${name}.LowLoadSeconds`, 24 * 3600 - v)
              }
            />
          </Form.Item>
          <Form.Item
            required
            label={intl('saenext.components.price-calculator.ResourceContent.PeakInstances')}
            name={`${name}.MaxInstanceCount`}
            validator={countValid}
          >
            <NumberPicker
              min={0}
              innerAfter={intl('general.unit.count')}
              hasTrigger={false}
              style={{ width: 250 }}
              onChange={() =>
                !isUndefined(MinInstanceCount) && field.validate(`${name}.MinInstanceCount`)
              }
            />
          </Form.Item>
        </If>
        <Form.Item
          required
          label={
            envType === 'Production' ? (
              intl('saenext.components.price-calculator.ResourceContent.DailyNormalDuration')
            ) : (
              <TextWithBalloon
                text={intl('saenext.components.price-calculator.BasicInformation.UseDuration')}
                tips={intl(
                  'saenext.components.price-calculator.BasicInformation.TestEnvironmentRecommendIsCalculated',
                )}
              />
            )
          }
          name={`${name}.LowLoadSeconds`}
        >
          <DecimalNumberPicker
            min={0}
            max={24}
            ratio={3600}
            innerAfter={intl('saenext.components.price-calculator.ResourceContent.Hours')}
            hasTrigger={false}
            style={{ width: 250 }}
            onChange={(v) =>
              !isUndefined(v) && field.setValue(`${name}.HighLoadSeconds`, 24 * 3600 - v)
            }
          />
        </Form.Item>
        <Form.Item
          required
          label={
            envType === 'Production'
              ? intl('saenext.components.price-calculator.ResourceContent.NumberOfNormalInstances')
              : intl('saenext.components.price-calculator.BasicInformation.TotalNumberOfInstances')
          }
          name={`${name}.MinInstanceCount`}
          validator={countValid}
        >
          <NumberPicker
            min={0}
            innerAfter={intl('general.unit.count')}
            hasTrigger={false}
            style={{ width: 250 }}
            onChange={() =>
              !isUndefined(MaxInstanceCount) && field.validate(`${name}.MaxInstanceCount`)
            }
          />
        </Form.Item>
      </div>
      <If condition={isMultipleNewAccount || idle}>
        <Form.Item
          required
          label={intl('saenext.components.price-calculator.ResourceContent.IdleMode')}
          name={`${name}.EnableCpuIdle`}
          size="small"
          labelTextAlign="left"
          labelCol={{ fixedSpan: 4 }}
        >
          <Switch defaultChecked={false} className="mt-xs" />
        </Form.Item>
        <If condition={EnableCpuIdle}>
          <IdleContent name={name} field={field} regionId={regionId} />
        </If>
      </If>

      <div className="none">
        <Form.Item key={regionId} name={`${name}.RegionId`}>
          <Input value={regionId} />
        </Form.Item>
        <Form.Item name={`${name}.EnvType`}>
          <Input value={envType} />
        </Form.Item>
        <Form.Item name={`${name}.CpuStrategy`}>
          <Input value={'always'} />
        </Form.Item>
        <Form.Item name={`${name}.AppType`}>
          <Input value={'MicroService'} />
        </Form.Item>
      </div>
    </>
  );
};

export default ResourceContent;
