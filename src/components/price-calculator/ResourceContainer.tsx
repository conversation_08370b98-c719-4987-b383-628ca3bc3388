import { intl } from '@ali/cnd';
import { Button, Form, Icon } from '@ali/cnd';
import React from 'react';
import ResourceCard from './ResourceCard';

const ResourceContainer = (props) => {
  const { field, name, envType, regionId } = props;

  const value = field.getValue(name);

  const onAdd = (val = {}) => {
    field.addArrayValue(name, value.length, val);
  };

  const onDelete = (index) => {
    field.deleteArrayValue(name, index);
  };

  return (
    <>
      {value.map((item, index) => (
        <ResourceCard
          key={index}
          field={field}
          envType={envType}
          regionId={regionId}
          title={intl('saenext.components.price-calculator.ResourceContainer.ApplicationIndex', {
            index: index + 1,
          })}
          name={`${name}.${index}`}
          onDelete={() => onDelete(index)}
        />
      ))}
      <Button className="mt" onClick={() => onAdd()}>
        <Icon type="add" />
        <span>{intl('saenext.components.price-calculator.ResourceContainer.AddApplication')}</span>
      </Button>
    </>
  );
};

export default ResourceContainer;
