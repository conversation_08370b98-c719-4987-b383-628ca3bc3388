import React from 'react';
import { intl } from '@ali/cnd';
import { Tab } from '@ali/cnd';
import PriceContent from './PriceContent';
import './index.less';

const PriceCalculator = () => {
  return (
    <>
      <Tab
        shape="pure"
        defaultActiveKey="Production"
      >
        <Tab.Item
          key="Production"
          title={intl('saenext.components.price-calculator.ProductionEnvironment')}
        >
          <PriceContent
            envType="Production"
          />
        </Tab.Item>
        <Tab.Item 
          key="Test"
          title={intl('saenext.components.price-calculator.TestEnvironment')}
        >
          <PriceContent
            envType="Test"
          />
        </Tab.Item>
      </Tab>
    </>
  );
}

export default PriceCalculator;