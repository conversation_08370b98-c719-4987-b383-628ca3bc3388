import { intl } from '@ali/cnd';
import _ from 'lodash';

export const Id2Label = {
  PACK_CPU_5K_1: intl('saenext.components.price-calculator.constants.CpuResourcePackage'),
  PACK_CPU_5W_3: intl('saenext.components.price-calculator.constants.CpuResourcePackage'),
  PACK_CPU_6W_12: intl('saenext.components.price-calculator.constants.CpuResourcePackage'),
  PACK_CPU_50W_12: intl('saenext.components.price-calculator.constants.CpuResourcePackage'),
  PACK_MEMORY_1W_1: intl('saenext.components.price-calculator.constants.MemoryResourcePackage'),
  PACK_MEMORY_10W_3: intl('saenext.components.price-calculator.constants.MemoryResourcePackage'),
  PACK_MEMORY_12W_12: intl('saenext.components.price-calculator.constants.MemoryResourcePackage'),
  PACK_MEMORY_100W_12: intl('saenext.components.price-calculator.constants.MemoryResourcePackage'),
  POST_MICRO_SERVICE_CPU: intl(
    'saenext.components.price-calculator.constants.MicroserviceCpuUsage',
  ),
  POST_MICRO_SERVICE_MEMORY: intl(
    'saenext.components.price-calculator.constants.MicroserviceMemoryUsage',
  ),
  POST_WEB_ALWAYS_CPU: intl(
    'saenext.components.price-calculator.constants.WebCpuUsageFixedAllocation',
  ),
  POST_WEB_ALWAYS_MEMORY: intl(
    'saenext.components.price-calculator.constants.WebMemoryUsageFixedAllocation',
  ),
  POST_WEB_REQUEST_CPU: intl(
    'saenext.components.price-calculator.constants.WebCpuUsageAllocatedBy',
  ),
  POST_WEB_REQUEST_MEMORY: intl(
    'saenext.components.price-calculator.constants.WebMemoryUsageAllocatedBy',
  ),
  POST_REQUEST_COUNT: intl('saenext.components.price-calculator.constants.NumberOfRequests'),
  POST_INTERNET_OUTBOUND: intl('saenext.components.price-calculator.constants.InternetExitTraffic'),
};
export const Id2Price = {
  PACK_CPU_5K_1: intl('saenext.components.price-calculator.constants.NuclearHoursMonth'),
  PACK_CPU_5W_3: intl('saenext.components.price-calculator.constants.CoreHoursSeason'),
  PACK_CPU_6W_12: intl('saenext.components.price-calculator.constants.NuclearHoursYear'),
  PACK_CPU_50W_12: intl('saenext.components.price-calculator.constants.NuclearHoursYear.1'),
  PACK_MEMORY_1W_1: intl('saenext.components.price-calculator.constants.GibHoursMonth'),
  PACK_MEMORY_10W_3: intl('saenext.components.price-calculator.constants.GibHoursSeason'),
  PACK_MEMORY_12W_12: intl('saenext.components.price-calculator.constants.GibHoursYear'),
  PACK_MEMORY_100W_12: intl('saenext.components.price-calculator.constants.GibHoursYear.1'),
  PACK_CU_1000W_1: intl('saenext.components.price-calculator.constants.MillionCuMonth'),
  PACK_CU_3000W_1: intl('saenext.components.price-calculator.constants.MillionCuMonth.1'),
  PACK_CU_30000W_3: intl('saenext.components.price-calculator.constants.BillionCuSeason'),
  PACK_CU_6000W_12: intl('saenext.components.price-calculator.constants.MillionCuYear'),
  PACK_CU_36000W_12: intl('saenext.components.price-calculator.constants.BillionCuYear'),
  PACK_CU_300000W_12: intl('saenext.components.price-calculator.constants.BillionCuYear.1'),
  PACK_CU_1000000W_12: intl('saenext.components.price-calculator.constants.BillionCuYear.2'),
};

export const Regionlist = _.get(window, 'ALIYUN_CONSOLE_CONFIG.REGIONS', []);

export const CpuMemDataSource = [
  {
    label: '0.5',
    value: 0.5,
    children: [
      { label: '1', value: 1 },
      { label: '2', value: 2 },
    ],
  },
  {
    label: '1',
    value: 1,
    children: [
      { label: '1', value: 1 },
      { label: '2', value: 2 },
      { label: '4', value: 4 },
    ],
  },
  {
    label: '2',
    value: 2,
    children: [
      { label: '2', value: 2 },
      { label: '4', value: 4 },
      { label: '8', value: 8 },
    ],
  },
  {
    label: '4',
    value: 4,
    children: [
      { label: '4', value: 4 },
      { label: '8', value: 8 },
      { label: '16', value: 16 },
    ],
  },
  {
    label: '8',
    value: 8,
    children: [
      { label: '8', value: 8 },
      { label: '16', value: 16 },
      { label: '64', value: 64, disabled: true },
    ],
  },
  {
    label: '12',
    value: 12,
    children: [
      { label: '12', value: 12 },
      { label: '24', value: 24 },
      { label: '48', value: 48, disabled: true },
    ],
  },
  {
    label: '16',
    value: 16,
    children: [
      { label: '16', value: 16 },
      { label: '32', value: 32 },
      { label: '64', value: 64, disabled: true },
    ],
  },
  {
    label: '32',
    value: 32,
    disabled: true,
    children: [
      { label: '64', value: 64, disabled: true },
      { label: '128', value: 128, disabled: true },
    ],
  },
];

export const PriceTypes = {
  POST: intl('saenext.components.price-calculator.PayAsYouGo'),
  PACK: intl('saenext.components.price-calculator.ResourcePackage'),
};
