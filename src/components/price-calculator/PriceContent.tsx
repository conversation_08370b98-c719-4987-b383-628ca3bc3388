import React, { useMemo, useState } from 'react';
import PriceDetail from './PriceDetail';
import services from '~/services';
import { intl, Tab } from '@ali/cnd';
import { PriceTypes } from './constants';
import PriceForm from './PriceForm';

type Props = {
  envType: 'Production' | 'Test';
}
const PriceContent = (props: Props) => {
  const {
    envType,
  } = props;

  const [showDetail, setShowDetail] = useState(false);
  const [loadaing, setLoading] = useState(false);
  const [priceData, setPriceData] = useState({
    TotalPrice: 0,
    Items: [],
    PostPayTotalPrice: 0,
    PostPayItems: [],
    Label: [],
    Duration: '',
  });
  const [postPriceData, setPostPriceData] = useState({
    TotalPrice: 0,
    Items: [],
    Label: [PriceTypes.POST],
    Duration: '',
  });

  const changeData = async (baseForm: any) => {
    setLoading(true);
    const { Features, Duration } = baseForm;
    const res = await services.getPriceEstimate({
      content: {
        Duration,
        Features,
        Version: "v2",
      },
    });
    setLoading(false);

    if (!res) return;

    const items = res.Data.Items || [];
    const Labels: Array<string> = [];
    for (const val of items) {
      const typeName = PriceTypes[val.Type];
      if (!Labels.includes(typeName)) {
        Labels.push(typeName);
      }
      if (Labels.length === 2) break;
    }
    const priceData = {
      TotalPrice: res.Data.TotalPrice || 0,
      Items: res.Data.Items || [],
      PostPayTotalPrice: res.Data.PostPayTotalPrice || 0,
      PostPayItems: res.Data.PostPayItems || [],
      Label: Labels,
      Duration:
        baseForm.Duration === 12
          ? intl('saenext.components.price-calculator.Year')
          : intl('saenext.components.price-calculator.Month'),
    };
    setPriceData(priceData);

    const postPriceData = {
      TotalPrice: res.Data.PostPayTotalPrice || 0,
      Items: res.Data.PostPayItems || [],
      Label: [PriceTypes.POST],
      Duration:
        baseForm.Duration === 12
          ? intl('saenext.components.price-calculator.Year')
          : intl('saenext.components.price-calculator.Month'),
    };
    setPostPriceData(postPriceData);

    setShowDetail(true);
  }

  return (
    <>
      <PriceForm
        envType={envType}
        getPrice={changeData}
        setShowDetail={setShowDetail}
        loadaing={loadaing}
      />
      {showDetail && (
        <Tab>
          {priceData?.Label?.join('+') !== PriceTypes.POST &&
            <Tab.Item
              key="pack&post"
              title={priceData?.Label?.join('+')}
            >
              <PriceDetail
                priceData={priceData}
                loading={loadaing}
              />
            </Tab.Item>
          }
          <Tab.Item
            key="only-post"
            title={PriceTypes.POST}
          >
            <PriceDetail
              priceData={postPriceData}
              loading={loadaing}
            />
          </Tab.Item>
        </Tab>
      )}
    </>
  )
}

export default PriceContent;