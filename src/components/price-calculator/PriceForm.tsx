import React from 'react';
import { Button, Field, Form, intl, Radio, Select, Switch } from '@ali/cnd';
import { Regionlist } from './constants';
import ResourceContainer from './ResourceContainer';

const PriceForm = (props) => {
  const { envType, getPrice, setShowDetail, loadaing } = props;

  const field = Field.useField({
    values: {
      Features: [{}],
    },
    onChange: () => {
      setShowDetail(false);
    },
    parseName: true,
  });

  const { getValues } = field;
  const { regionId } = getValues();

  const onSubmit = async () => {
    const { values, errors } = await field.validatePromise();
    if (errors) return;

    await getPrice(values);
  };

  return (
    <>
      <Form className="price-calculator-form" useLabelForErrorMessage field={field}>
        <Form.Item
          required
          label={intl('saenext.components.price-calculator.BasicInformation.Region')}
          style={{ width: '30%' }}
        >
          <Select style={{ width: '100%' }} name="regionId" defaultValue={Regionlist[0].regionId}>
            {Regionlist.map((val) => (
              <Select.Option value={val.regionId}>{val.name}</Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item>
          <ResourceContainer field={field} name="Features" envType={envType} regionId={regionId} />
        </Form.Item>
        <Form.Item
          required
          label={intl('saenext.components.price-calculator.BasicInformation.UseDuration')}
        >
          <Radio.Group name="Duration" defaultValue={1}>
            <Radio
              value={1}
              label={intl('saenext.components.price-calculator.BasicInformation.Month')}
            />

            <Radio
              value={12}
              label={intl('saenext.components.price-calculator.BasicInformation.Year')}
            />
          </Radio.Group>
        </Form.Item>
      </Form>
      <Button type="primary" onClick={onSubmit} loading={loadaing}>
        {intl('saenext.components.price-calculator.PriceForm.CalculateCost')}
      </Button>
    </>
  );
};

export default PriceForm;
