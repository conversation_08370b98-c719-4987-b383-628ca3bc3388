import { intl } from '@ali/cnd';
import {
  <PERSON><PERSON>,
  Card,
  CndRcSelect,
  DatePicker2,
  Form,
  Input,
  Message,
  Radio,
  Select,
} from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import If from '../shared/If';
import services from '~/services';
import { get, map } from 'lodash';
import dayjs from 'dayjs';
import { jsonParse } from '~/utils/transfer-data';

const IdleContent = (props) => {
  const { name, field, regionId } = props;

  const [refreshIndex, setRefreshIndex] = useState(0);
  const [idleType, setIdleType] = useState('ecs');
  const [ecsInsatnce, setEcsInsatnce] = useState('');
  const [dateSelected, setDateSelected] = useState(dayjs().subtract(1, 'day'));

  useEffect(() => {
    setEcsInsatnce('');
    setRefreshIndex(refreshIndex + 1);
  }, [regionId]);

  useEffect(() => {
    field.reset(`${name}.CpuUtilMetrics`);
    if (!ecsInsatnce) return;
    getEcsMetric();
  }, [ecsInsatnce, dateSelected]);

  const fetchEcsInstance = async (search, pageNumber = 1) => {
    const res = await services.DescribeInstanceStatus({
      RegionId: regionId,
      PageNumber: pageNumber,
      PageSize: 10,
      'InstanceId.1': search || undefined,
    });
    const ecsInsatnces = get(res, 'InstanceStatuses.InstanceStatus', []);
    const data = ecsInsatnces.map((item) => ({
      label: item.InstanceId,
      value: item.InstanceId,
    }));
    return {
      data,
      total: get(res, 'TotalCount', 0),
    };
  };

  const getEcsMetric = async () => {
    const res = await services.DescribeMetricListFromProxy({
      RegionId: regionId,
      Namespace: 'acs_ecs_dashboard',
      MetricName: 'cpu_total',
      Dimensions: JSON.stringify([
        {
          instanceId: ecsInsatnce,
        },
      ]),
      StartTime: dateSelected.startOf('day').valueOf(),
      EndTime: dateSelected.endOf('day').valueOf(),
    });
    if (!res) {
      Message.error(
        intl('saenext.components.price-calculator.IdleContent.FailedToObtainEcsInstance'),
      );
      return;
    }
    const { Datapoints } = res;
    const dataArr = jsonParse(Datapoints) || [];
    const averageArr = map(dataArr, 'Average');
    setCpuUtilMetrics(averageArr);
  };

  const setCpuUtilMetrics = (v?) => {
    if (v) {
      field.reset(`${name}.CpuUtilMetrics`);
    }
    field.setValue(`${name}.CpuUtilMetrics`, v);
  };

  const metricValidator = (rule, value, callback) => {
    if (!ecsInsatnce) {
      callback(intl('saenext.components.price-calculator.IdleContent.SelectAnEcsInstance'));
    } else if (!value) {
      callback(
        <>
          {intl('saenext.components.price-calculator.IdleContent.FailedToObtainEcsInstance.1')}

          <Button
            text
            type="primary"
            onClick={() => {
              getEcsMetric();
            }}
          >
            {intl('saenext.components.price-calculator.IdleContent.ClickRetry')}
          </Button>
        </>,
      );
    } else if (value?.length === 0) {
      callback(intl('saenext.components.price-calculator.IdleContent.ThisEcsInstanceHasNo'));
    } else {
      callback();
    }
  };

  return (
    <Card
      className="overflow-visible"
      title={
        <div className="text-12">
          {intl('saenext.components.price-calculator.IdleContent.IdleProportionAssessment')}
        </div>
      }
      contentHeight={160}
    >
      <Form.Item
        required
        label={intl('saenext.components.price-calculator.IdleContent.CpuUtilization')}
      >
        <Radio.Group
          value={idleType}
          onChange={(v: string) => setIdleType(v)}
          dataSource={[
            {
              label: intl('saenext.components.price-calculator.IdleContent.SelectAnEcsInstance.1'),
              value: 'ecs',
            },
            {
              label: intl('saenext.components.price-calculator.IdleContent.ManualSelection'),
              value: 'level',
            },
          ]}
        />
      </Form.Item>
      <If condition={idleType === 'ecs'}>
        <div className="grid-container-4">
          <Form.Item>
            <CndRcSelect
              refreshIndex={refreshIndex}
              fetchData={fetchEcsInstance}
              value={ecsInsatnce}
              onChange={(v: string) => setEcsInsatnce(v)}
              useBackendSearch
              style={{ width: 250 }}
            />
          </Form.Item>
          <Form.Item
            help={intl('saenext.components.price-calculator.IdleContent.TimeRangeOfEcsInstance')}
          >
            <DatePicker2
              hasClear={false}
              value={dateSelected}
              onChange={(v) => {
                setDateSelected(v);
              }}
              disabledDate={(date) => date.isAfter(dayjs().subtract(1, 'day'))}
              style={{ width: 250 }}
            />
          </Form.Item>
        </div>
        <Form.Item name={`${name}.CpuUtilMetrics`} validator={metricValidator}>
          <Input className="none" />
        </Form.Item>
      </If>
      <If condition={idleType === 'level'}>
        <Form.Item required name={`${name}.CpuUtilLevel`}>
          <Select
            defaultValue={'L1'}
            dataSource={[
              {
                label: '<3%',
                value: 'L1',
              },
              {
                label: '3%-6%',
                value: 'L2',
              },
              {
                label: '6%-10%',
                value: 'L3',
              },
              {
                label: '10%-15%',
                value: 'L4',
              },
              {
                label: '15%-20%',
                value: 'L5',
              },
              {
                label: '>20%',
                value: 'L6',
              },
            ]}
            style={{ width: 250 }}
          />
        </Form.Item>
      </If>
    </Card>
  );
};

export default IdleContent;
