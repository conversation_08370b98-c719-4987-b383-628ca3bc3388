import React, { useEffect, useState } from "react";
import AcmWidgetWrap from "./AcmWidgetWrap";
import { loader } from '@monaco-editor/react';

declare global {
  interface Window {
    monaco:any;
  }
}

const AcmDetail = (props) => {
  const {
    regionId,
    namespaceId,
    history,
  } = props;

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loader.config({
      paths: {
        vs: 'https://g.alicdn.com/cm-design/mw-vendor/1.1.4/vs',
      },
    });
    loader.init().then((monaco) => {
      window.monaco = monaco;
      setLoading(false)
    })
  }, [])

  return (
    <>
      <AcmWidgetWrap
        component="ConfigurationDetail"
        regionId={regionId}
        namespaceId={namespaceId}
        history={history}
        loading={loading}
      />
    </>
  )
}

export default AcmDetail;