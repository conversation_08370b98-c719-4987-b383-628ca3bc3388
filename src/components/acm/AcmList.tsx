import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import AcmWidgetWrap from './AcmWidgetWrap';
import { Message } from '@ali/cnd';
import { loader } from '@monaco-editor/react';
import CachedData from '~/cache/common';

const AcmList = (props) => {
  const { regionId, namespaceId, history } = props;

  const [loading, setLoading] = useState(true);
  const isIntl = CachedData.isSinSite();

  useEffect(() => {
    loader.config({
      paths: {
        vs: 'https://g.alicdn.com/cm-design/mw-vendor/1.1.4/vs',
      },
    });
    loader.init().then((monaco) => {
      window.monaco = monaco;
      setLoading(false);
    });
  }, []);

  return (
    <>
      <Message type="notice" className="mb">
        <div className="text-line">
          {intl('saenext.components.acm.AcmList.SaeBuiltInDistributedConfiguration')}
        </div>
        <div className="text-line">
          <span>{intl('saenext.components.acm.AcmList.HoweverConsideringTheSelectionFrom')}</span>
          <a
            href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=${isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'}`}
            target="_blank"
          >
            {intl('saenext.components.acm.AcmList.NacosConfigurationCenterForMse')}
          </a>
          {intl("saenext.common.full-stop")}
        </div>
        <div className="text-line color-orange">
          {intl('saenext.components.acm.AcmList.ForTheTimeBeingThe')}
        </div>
      </Message>
      <AcmWidgetWrap
        component="ConfigurationManagement"
        regionId={regionId}
        namespaceId={namespaceId}
        history={history}
        loading={loading}
      />
    </>
  );
};

export default AcmList;
