import React, { useEffect, useState } from "react";
import createLoader from '@ali/widget-loader';
import moment from 'moment';
import getEventEmitter from '@ali/widget-loader/lib/getEventEmitter';
import services from "~/services";
import { Loading } from "@ali/cnd";
import { getParams } from "~/utils/global";
import CachedData from "~/cache/common";

const AcmWidgetWrap = (props) => {
  const {
    component,
    regionId,
    namespaceId,
    history,
    loading = false,
  } = props;

  const [namespaceTenantId, setNamespaceTenantId] = useState('');
  const namespaceName = getParams('name') || '';

  useEffect(() => {
    handleEventListeners();
    queryNamespaceDetail();
  }, [regionId, namespaceId])

  const queryNamespaceDetail = async () => {
    const { Data = {} } = await services.getNamespaceResources({
      RegionId: regionId,
      NamespaceId: namespaceId,
    }) || {};
    setNamespaceTenantId(Data?.TenantId)
  };

  const handleViewConfigDetail = (value, tab) => {
    const curTab = tab || 'content';
    history.push(`/${regionId}/config-management/acm/acm-detail?namespaceId=${namespaceId}&dataId=${value.DataId}&group=${value.Group
      }&name=${namespaceName}&regionId=${regionId}&tab=${curTab}`)
    // history.push(
    //   `/${regionId}/namespace/${namespaceId}/acm-list/acm-detail/#/?z=1&dataId=${value.DataId}&group=${value.Group
    //   }&namespaceId=${namespaceId}&name=${namespaceName}&regionId=${regionId}&tab=${curTab}`,
    // );
  };

  const handleViewConfigs = (val) => {
    history.push(`/${regionId}/config-management/acm`);
    // history.push(`/${regionId}/namespace/${namespaceId}/acm-list${location.search}`);
  };

  const handleEventListeners = () => {
    const eventEmitter = getEventEmitter();
    eventEmitter.on('@ali/widget-edas-acm:view-config-detail', value => handleViewConfigDetail(value, 'content'));
    eventEmitter.on('@ali/widget-edas-acm:view-configlist', handleViewConfigs);
    eventEmitter.on('@ali/widget-edas-acm:view-config-trace', value => handleViewConfigDetail(value, 'trace'));
    // eventEmitter.on('@ali/widget-edas-acm:features-config', handleFeatures);
  }

  const loadWidget = createLoader({
    initiator: 'sae',
    dependencies: {
      lodash: window._,
      moment,
      // monaco,
      // '@ali/wind': windLibrary,
      // '@ali/wind-intl/lib/Provider': Provider,
      '@ali/widget-utils-console': {
        getLocale() {
          return CachedData.lang;
        },
      },
    },
    windRuntime: {
      runtimeVersion: '3.4.29' // 指定版本，此版本包含 wind 3
    }
  });

  const AcmWidget = loadWidget({
    id: '@ali/widget-edas-acm',
    version: '1.8.5',
  }, {
    lazy: true,
  })

  const regions = window.ALIYUN_CONSOLE_CONFIG.REGIONS;

  return (
    <>

      {loading ?
        <Loading
          visible={loading}
          className="full-width full-height"
        />
        :
        <AcmWidget
          component={component}
          isSAEProduct={true}
          insertAppName='sae'
          regions={regions}
          namespaces={[{
            value: namespaceTenantId,
            label: namespaceName,
          }]}
        />
      }
    </>
  )
}

export default AcmWidgetWrap;
