import { Loading } from '@ali/cnd';
import { isEmpty } from 'lodash';
import React, { useContext } from 'react'
import MicroAppStatics from '~/components/app-detail/micro-app/basic-info/MicroAppStatics';
import JobContext from '~/utils/jobContext';
import JobSetting from './JobSetting';
import JobInfo from './JobInfo';
import ChangeOrderStatus from '~/components/app-detail/micro-app/basic-info/ChangeOrderStatus';

const BasicInfo = (props) => {
  const { jobConfig, lastChangeOrder } = useContext(JobContext);

  const { RegionId, AppId } = jobConfig;

  const { Status, ChangeOrderId } = lastChangeOrder;

  const jobOrderStatus = {
    LastChangeOrderRunning: Status === 1,
    LastChangeOrderId: ChangeOrderId,
  }

  return (
    <Loading visible={isEmpty(jobConfig)} className="full-width">
      <ChangeOrderStatus appStatus={jobOrderStatus} regionId={RegionId} appId={AppId} showSlide />
      <MicroAppStatics appConfig={jobConfig} hideDisk />
      <JobInfo className='mt' />
      <JobSetting className='mt' />
    </Loading>
  )
}

export default BasicInfo;