import { intl } from '@ali/cnd';
import { Card, CndTable, DateTime, StatusIndicator } from '@ali/cnd';
import React, { useContext } from 'react';
import JobContext from '~/utils/jobContext';
import { jsonParse } from '~/utils/transfer-data';
import HttpSetting from './HttpSetting';
import If from '~/components/shared/If';
import { ETriggerType, TriggerTypeMap } from './constants';
import CronLaterBalloon from '~/components/shared/CronLaterBalloon';
import { isUndefined } from 'lodash';

const JobSetting = (props) => {
  const { className } = props;
  const { jobConfig, jobStatus } = useContext(JobContext);

  const triggerConfig = jsonParse(jobConfig.TriggerConfig);
  const { type: triggerType, config: triggerConfigContent } = triggerConfig || {};

  const triggerTypeName = TriggerTypeMap[triggerType] || triggerType;

  const dataSource = {
    ...jobConfig,
    ...jobStatus,
    ...triggerConfigContent,
  };

  return (
    <Card
      title={intl('saenext.job-detail.basic-info.JobSetting.TaskSettings')}
      className={`next-extra-card ${className}`}
      showHeadDivider={false}
      contentHeight={'auto'}
    >
      <p>
        {intl('saenext.job-detail.basic-info.JobSetting.Type')}
        {triggerTypeName}
        <If condition={triggerType === ETriggerType.Http}>
          <a
            href="https://help.aliyun.com/document_detail/396330.html#section-mto-vo1-3ph"
            target="_blank"
          >
            {intl('saenext.job-detail.basic-info.JobSetting.ConfigureParametersForOneTime')}
          </a>
        </If>
      </p>

      <If condition={triggerType === ETriggerType.Http}>
        <HttpSetting dataSource={dataSource} triggerType={triggerType} />
      </If>

      <CndTable
        dataSource={[dataSource]}
        hasBorder={false}
        primaryKey="AppId"
        style={{ marginTop: 20 }}
      >
        <CndTable.Column
          width={50}
          title={intl('saenext.job-detail.basic-info.JobSetting.ExecutionPolicy')}
          dataIndex="TriggerConfig"
          cell={(val) =>
            val && (
              <>
                {triggerType === 'time' ? (
                  <>
                    <span>{triggerConfigContent}</span>
                    <CronLaterBalloon trigger={triggerConfigContent} />
                  </>
                ) : (
                  <span className="mr">{TriggerTypeMap[triggerType]}</span>
                )}
              </>
            )
          }
        />

        <CndTable.Column
          width={50}
          title={intl('saenext.job-detail.basic-info.JobSetting.TaskStatus')}
          dataIndex="Suspend"
          cell={(value) => (
            <StatusIndicator type={value ? 'disabled' : 'success'} shape="dot">
              {value
                ? intl('saenext.job-detail.basic-info.JobSetting.Stopped')
                : intl('saenext.job-detail.basic-info.JobSetting.Started')}
            </StatusIndicator>
          )}
        />

        <CndTable.Column
          width={50}
          title={intl('saenext.job-detail.basic-info.JobSetting.NumberOfRunningSuccessfulFailed')}
          dataIndex="Result"
          cell={(value, index, record) => {
            const { Active, Succeeded, Failed } = record;
            if (!isUndefined(Active)) {
              return (
                <div>
                  {Active} / {Succeeded} / {Failed}
                </div>
              );
            } else {
              return '-';
            }
          }}
        />

        <CndTable.Column
          width={50}
          title={intl('saenext.job-detail.basic-info.JobSetting.ConcurrencyPolicy')}
          dataIndex="ConcurrencyPolicy"
        />

        <CndTable.Column
          width={50}
          title={intl('saenext.job-detail.basic-info.JobSetting.Timeout')}
          dataIndex="Timeout"
          cell={(val) => `${val} s`}
        />

        <CndTable.Column
          width={50}
          title={intl('saenext.job-detail.basic-info.JobSetting.NumberOfRetries')}
          dataIndex="BackoffLimit"
          cell={(val) => intl('saenext.job-detail.basic-info.JobSetting.ValTimes', { val: val })}
        />

        <CndTable.Column
          width={50}
          title={intl('saenext.job-detail.basic-info.JobSetting.EnableTaskSharding')}
          dataIndex="Slice"
          cell={(val) => {
            return val
              ? intl('saenext.job-detail.basic-info.JobSetting.Enabled')
              : intl('saenext.job-detail.basic-info.JobSetting.NotEnabled');
          }}
        />

        <CndTable.Column
          width={50}
          title={intl('saenext.job-detail.basic-info.JobSetting.LastTaskStartTime')}
          dataIndex="StartTime"
          cell={(val) => (val ? <DateTime value={val * 1000} /> : '-')}
        />
      </CndTable>
    </Card>
  );
};

export default JobSetting;
