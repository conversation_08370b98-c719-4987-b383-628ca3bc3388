import React, { useContext, useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Co<PERSON>, <PERSON>Fields, Icon, intl, LinkButton, ToolTipCondition } from '@ali/cnd';
import CachedData from '~/cache/common';
import DescriptionDialog from '~/components/app-detail/micro-app/basic-info/DescriptionDialog';
import If from '~/components/shared/If';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import useVpcInfo from '~/hooks/useVpcInfo';
import useVSwitchInfo from '~/hooks/useVSwitchInfo';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';
import JobContext from '~/utils/jobContext';
import useSecurityGroupInfo from '~/hooks/useSecurityGroupInfo';
import SecurityGroupDialog from '~/components/app-detail/micro-app/basic-info/SecurityGroupDialog';
import { isEmpty } from 'lodash';
import VSwitchesInfo from '~/components/shared/VSwitchesInfo';
import VSwitchDialog from '~/components/app-detail/micro-app/basic-info/VSwitchDialog';
import SpecDialog from '~/components/app-detail/micro-app/basic-info/SpecDialog';

const JobInfo = (prop) => {
  const { className } = prop;

  const [zoneWarning, setZoneWarning] = useState(false);

  const { jobConfig, jobStatus, lastChangeOrder, refreshJob, setIsOnlyShenZhenA } =
    useContext(JobContext);

  const { RegionId: regionId } = jobConfig;

  const LastChangeOrderRunning = lastChangeOrder?.Status === 1;

  const isInDebt = CachedData.getOpenStatus('inDebtStatus');

  const dataSource = {
    ...jobConfig,
    ...jobStatus,
  };

  const { vpc } = useVpcInfo({
    vpcId: jobConfig.VpcId,
  });

  const { vSwitchList, authed, isOnlyShenZhenA } = useVSwitchInfo({
    vpcId: jobConfig.VpcId,
    vSwitchId: jobConfig.VSwitchId,
  });

  const { securityGroup } = useSecurityGroupInfo({
    regionId,
    vpcId: jobConfig.VpcId,
    securityGroupId: jobConfig.SecurityGroupId,
  });

  useEffect(() => {
    setIsOnlyShenZhenA(isOnlyShenZhenA);
  }, [isOnlyShenZhenA]);

  const renderDisabledTip = (conditions: string[]) => {
    if (isEmpty(conditions)) return '';

    const conditionTips = {
      isInDebt: isInDebt
        ? intl('saenext.micro-app.basic-info.AppActionBar.YourAccountIsOverduePlease')
        : '',
      LastChangeOrderRunning: LastChangeOrderRunning
        ? intl('saenext.job-detail.basic-info.JobInfo.TheTaskTemplateIsChanging')
        : '',
    };

    for (const condition of conditions) {
      const tip = conditionTips[condition];
      if (tip) return tip;
    }

    return '';
  };

  return (
    <Card
      title={intl('saenext.job-detail.basic-info.JobInfo.BasicTaskInformation')}
      className={`next-extra-card ${className}`}
      showHeadDivider={false}
      contentHeight={'auto'}
    >
      <DataFields
        dataSource={dataSource}
        items={[
          {
            dataIndex: 'AppName',
            label: intl('saenext.job-detail.basic-info.JobInfo.TaskTemplateName'),
            render: (val) => <Copy text={val}>{val}</Copy>,
          },
          {
            dataIndex: 'AppId',
            label: intl('saenext.job-detail.basic-info.JobInfo.TaskTemplateId'),
            render: (value) => <Copy text={value}>{value}</Copy>,
          },
          {
            dataIndex: 'NamespaceId',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.Namespace'),
            render: (value) =>
              value && (
                <Copy text={value}>
                  <a href={`/${regionId}/namespace/${value}/base`} target="_blank">
                    {value}
                  </a>
                </Copy>
              ),
          },
          {
            dataIndex: 'VpcId',
            label: 'VPC',
            render: (value) =>
              value && (
                <>
                  <If condition={vpc?.VpcName}>
                    <p>
                      <span className="text-description">
                        {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
                      </span>
                      {vpc?.VpcName}
                    </p>
                  </If>
                  <p>
                    <span className="text-description">ID: </span>
                    <Copy text={value}>
                      <a
                        href={CachedData.confLink('feature:vpc:vpcs:url', {
                          regionId: regionId,
                          vpcId: value,
                        })}
                        target="_blank"
                      >
                        {value}
                      </a>
                    </Copy>
                  </p>
                </>
              ),
          },
          {
            dataIndex: 'VSwitchId',
            label: (
              <UnAuthedLabel
                text={
                  <>
                    <span>{intl('saenext.micro-app.basic-info.AppBaseInfo.Switch')}</span>
                    <If condition={jobConfig.VSwitchId?.split(',').length === 1}>
                      <TextWithBalloon
                        text=""
                        tips={
                          <div style={{ color: '#FFA003' }}>
                            {intl(
                              'saenext.micro-app.basic-info.AppBaseInfo.CurrentlyOnlyOneSwitchVswitch',
                            )}
                          </div>
                        }
                      />
                    </If>
                  </>
                }
                authed={authed}
                authKey="AliyunVPCReadOnlyAccess"
              />
            ),

            render: (value) => (
              <>
                <VSwitchesInfo
                  regionId={regionId}
                  vSwitchList={vSwitchList}
                  vSwitchIds={value}
                  replicas={jobConfig.Replicas}
                  setZoneWarning={setZoneWarning}
                />

                <ToolTipCondition
                  show={LastChangeOrderRunning || isInDebt}
                  tip={renderDisabledTip(['isInDebt', 'LastChangeOrderRunning'])}
                  align="r"
                >
                  <VSwitchDialog appConfig={jobConfig} refresh={refreshJob}>
                    <LinkButton disabled={LastChangeOrderRunning || isInDebt} className="ml10">
                      {zoneWarning
                        ? intl('saenext.micro-app.basic-info.AppBaseInfo.WeRecommendThatYouUse')
                        : intl('saenext.micro-app.basic-info.AppBaseInfo.MultiVswitchDeployment')}
                    </LinkButton>
                  </VSwitchDialog>
                </ToolTipCondition>
              </>
            ),
          },
          {
            dataIndex: 'SecurityGroupId',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.SecurityGroup'),
            render: (value) =>
              value && (
                <>
                  <If condition={securityGroup.SecurityGroupName}>
                    <p>
                      <span className="text-description">
                        {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
                      </span>
                      {securityGroup.SecurityGroupName}
                    </p>
                  </If>
                  <p>
                    <span className="text-description">ID: </span>
                    <Copy text={value}>
                      <a
                        href={`${CachedData.confLink(
                          'feature:ecs:url',
                        )}/securityGroupDetail/region/${regionId}/groupId/${value}/detail/intranetIngress`}
                        target="_blank"
                      >
                        {value}
                      </a>
                    </Copy>
                  </p>
                  <ToolTipCondition
                    show={LastChangeOrderRunning || isInDebt}
                    tip={renderDisabledTip(['isInDebt', 'LastChangeOrderRunning'])}
                  >
                    <SecurityGroupDialog appConfig={jobConfig} refresh={refreshJob}>
                      <LinkButton disabled={LastChangeOrderRunning || isInDebt} className="ml10">
                        {intl('saenext.micro-app.basic-info.AppBaseInfo.SwitchSecurityGroups')}
                      </LinkButton>
                    </SecurityGroupDialog>
                  </ToolTipCondition>
                </>
              ),
          },
          {
            dataIndex: 'Cpu',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.InstanceType'),
            render: (value) =>
              value && (
                <>
                  <span>
                    {value / 1000} Core, {jobConfig?.Memory / 1024} GiB,
                    {/* <span className="ml-xs">
                      {intl('saenext.micro-app.basic-info.AppBaseInfo.GibSystemDiskSpaceGib.new', {
                        diskSize: jobConfig?.DiskSize || 20,
                      })}
                    </span> */}
                  </span>
                  <ToolTipCondition
                    show={LastChangeOrderRunning || isInDebt}
                    tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart', 'isInDebt'])}
                    align="r"
                  >
                    <SpecDialog
                      type="job"
                      appConfig={jobConfig}
                      refresh={refreshJob}
                      isSupportHygon={false}
                      title={intl('saenext.micro-app.basic-info.AppBaseInfo.ChangeSpecifications')}
                    >
                      <LinkButton
                        disabled={LastChangeOrderRunning || isInDebt}
                        className="ml-s"
                      >
                        {intl('saenext.micro-app.basic-info.AppBaseInfo.ChangeSpecifications')}
                      </LinkButton>
                    </SpecDialog>
                  </ToolTipCondition>
                </>
              ),
          },
          {
            dataIndex: 'ImageUrl',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ImageAddress'),
            visible: !!jobConfig?.ImageUrl,
            render: (value) => value && <Copy text={value}>{value}</Copy>,
          },
          {
            dataIndex: 'PackageUrl',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.PackageName'),
            visible: !!jobConfig?.PackageUrl,
            render: (value) => {
              if (!value) {
                return;
              }
              const url = value?.replace(/.*\//, '')?.replace(/\?.*/, '') || '';
              const { PackageVersion = '' } = jobConfig;
              const result = `${url}:${PackageVersion}`;
              return <Copy text={result}>{result}</Copy>;
            },
          },
          {
            dataIndex: 'Replicas',
            label: intl('saenext.job-detail.basic-info.JobInfo.NumberOfConcurrentInstances'),
          },
          {
            dataIndex: 'AppDescription',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationDescription'),
            render: (value) => (
              <>
                {value ? (
                  <span style={{ wordBreak: 'break-all' }}>
                    {value}
                    <Copy showIcon text={value} />
                  </span>
                ) : (
                  <span>--</span>
                )}

                <DescriptionDialog appConfig={jobConfig} refresh={refreshJob}>
                  <Button
                    // disabled={LastChangeOrderRunning || isInDebt}
                    type="primary"
                    text
                    iconSize="small"
                    className="ml10"
                  >
                    <Icon type="edit" />
                  </Button>
                </DescriptionDialog>
              </>
            ),
          },
        ].filter((item) => item.visible !== false)}
      />
    </Card>
  );
};

export default JobInfo;
