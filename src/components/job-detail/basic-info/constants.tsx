import { intl } from '@ali/cnd';
export enum ETriggerType {
  Time = 'time',
  Http = 'http',
}

export const TriggerTypeMap = {
  [ETriggerType.Time]: intl('saenext.job-detail.basic-info.constants.PeriodicTasks'),
  [ETriggerType.Http]: intl('saenext.job-detail.basic-info.constants.OneTimeTaskTriggeredBy'),
};

export enum ESecurityConfig {
  None = 'none',
  Referer = 'referer',
  Ip = 'ip',
}

export const SecurityConfigMap = {
  [ESecurityConfig.None]: intl('saenext.job-detail.basic-info.constants.None'),
  [ESecurityConfig.Referer]: intl('saenext.job-detail.basic-info.constants.SecureDomainName'),
  [ESecurityConfig.Ip]: intl('saenext.job-detail.basic-info.constants.IpNetworkSegment'),
};
