import { intl } from '@ali/cnd';
import React from 'react';
import { Card, Copy, DataFields, Truncate } from '@ali/cnd';
import { ESecurityConfig, SecurityConfigMap } from './constants';

const HttpSetting = (props) => {
  const { dataSource } = props;

  const { securityConfig } = dataSource;

  return (
    <Card>
      <DataFields
        dataSource={dataSource}
        items={[
          {
            dataIndex: 'PublicWebHookUrls',
            label: intl('saenext.job-detail.basic-info.HttpSetting.PublicNetworkRequestUrl'),
            render: (val) => {
              return val?.map((item) => (
                <Copy text={item}>
                  <a href={item} target="_blank">
                    <Truncate type="width" threshold={380} showTooltip={false}>
                      {item}
                    </Truncate>
                  </a>
                </Copy>
              ));
            },
          },
          {
            dataIndex: 'VpcWebHookUrls',
            label: intl('saenext.job-detail.basic-info.HttpSetting.PrivateNetworkRequestUrl'),
            render: (val) => {
              return val?.map((item) => (
                <Copy text={item}>
                  <a href={item} target="_blank">
                    <Truncate type="width" threshold={380} showTooltip={false}>
                      {item}
                    </Truncate>
                  </a>
                </Copy>
              ));
            },
          },
          {
            dataIndex: 'method',
            label: intl('saenext.job-detail.basic-info.HttpSetting.RequestMethod'),
            render: (val) => val.join(', '),
          },
          {
            dataIndex: 'securityConfig',
            label: intl('saenext.job-detail.basic-info.HttpSetting.SecurityConfiguration'),
            render: (val) => SecurityConfigMap[val] || val,
          },
          {
            dataIndex: 'securityConfigReferer',
            label: intl('saenext.job-detail.basic-info.HttpSetting.SecureDomainName'),
            // @ts-ignore
            visible: securityConfig === ESecurityConfig.Referer,
            render: (val) => {
              return val?.map((item) => (
                <div>
                  <Copy text={item}>{item}</Copy>
                </div>
              ));
            },
          },
          {
            dataIndex: 'ip',
            label: intl('saenext.job-detail.basic-info.HttpSetting.IpNetworkSegment'),
            // @ts-ignore
            visible: securityConfig === ESecurityConfig.Ip,
            render: (val) => {
              return val?.map((item) => (
                <div>
                  <Copy text={item}>{item}</Copy>
                </div>
              ));
            },
          },
        ].filter((item) => item.visible !== false)}
      />
    </Card>
  );
};

export default HttpSetting;
