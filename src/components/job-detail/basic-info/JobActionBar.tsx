import React, { useContext } from 'react';
import { intl, LinkButton, useHistory } from '@ali/cnd';
import {
  Button,
  Dialog,
  Message,
  ToolTipCondition,
  Badge,
  ErrorPrompt2 as errorPrompt,
} from '@ali/cnd';
import JobContext from '~/utils/jobContext';
import CachedData from '~/cache/common';
import services from '~/services';
import { isEmpty } from 'lodash';
import RequestWrap from '~/components/shared/RequestWrap';
import VSwitchDialog from '~/components/app-detail/micro-app/basic-info/VSwitchDialog';
import RollbackSlide from '~/components/app-detail/micro-app/basic-info/RollbackSlide';

const JobActionBar = (props) => {
  const { jobId, refresh = () => {}, regionId } = props;

  const history = useHistory();

  const { jobConfig, jobStatus, lastChangeOrder, isOnlyShenZhenA } = useContext(JobContext);

  const { AppId, Suspend = true } = jobConfig;

  const { Status, ChangeOrderId } = lastChangeOrder;

  const LastChangeOrderRunning = Status === 1;

  const isInDebt = CachedData.getOpenStatus('inDebtStatus');

  const execForbid = jobConfig.ConcurrencyPolicy === 'Forbid' && jobStatus.Active > 0;
  const execSuspend = jobConfig.Suspend;

  const goToDeploy = () => {
    history.push(
      `/${regionId}/job-list/${AppId}?page=JobDetail/ApplyUpdate&namespaceId=${jobConfig.NamespaceId}&name=${jobConfig.AppName}`,
    );
    // history.push(`/${regionId}/job-list/${AppId}/deploy`);
  };

  const handleExecJob = async () => {
    const res = await services.ExecJob({
      params: {
        AppId: jobId,
      },
    });
    const { Data: { Success = false } = {} } = res || {};
    if (Success) {
      Message.success(intl('saenext.job-detail.basic-info.JobActionBar.SuccessfulExecution'));
      history.push(`/${regionId}/job-list/${AppId}/record`);
    } else {
      Message.error(intl('saenext.job-detail.basic-info.JobActionBar.ExecutionFailed'));
    }
  };

  const handleSuspendJob = () => {
    const SuspendMap = {
      true: {
        action: intl('saenext.job-detail.basic-info.JobActionBar.Start'),
        title: intl('saenext.job-detail.basic-info.JobActionBar.EnableTasks'),
        content: intl('saenext.job-detail.basic-info.JobActionBar.AfterTheTaskIsStarted'),
      },
      false: {
        action: intl('saenext.job-detail.basic-info.JobActionBar.Stop'),
        title: intl('saenext.job-detail.basic-info.JobActionBar.StopATask'),
        content: intl('saenext.job-detail.basic-info.JobActionBar.AfterStoppingTheSystemWill'),
      },
    };

    const { action, title, content } = SuspendMap[Suspend];

    Dialog.confirm({
      title,
      content: <p style={{ width: 500 }}>{content}</p>,

      onOk: async () => {
        const res = await services.SuspendJob({
          params: {
            AppId: jobId,
            Suspend: !Suspend,
          },
        });

        const { Data: { success = false } = {} } = res || {};
        if (success) {
          Message.success(
            intl('saenext.job-detail.basic-info.JobActionBar.ActionSucceeded', { action: action }),
          );
          refresh();
          return true;
        } else {
          Message.error(
            intl('saenext.job-detail.basic-info.JobActionBar.ActionFailed', { action: action }),
          );
          return false;
        }
      },
      okProps: { children: intl('saenext.common.dialog.ok') },
      cancelProps: { children: intl('saenext.common.dialog.cancel') },
    });
  };

  const deleteJobConfirm = () => {
    Dialog.alert({
      title: intl('saenext.components.job-list.DeleteATaskTemplate'),
      content: (
        <p style={{ width: 500 }}>{intl('saenext.components.job-list.AfterYouDeleteATask')}</p>
      ),

      onOk: async () => {
        return await handleDeleteJob();
      },
      footerActions: ['ok', 'cancel'],
      okProps: { children: intl('saenext.common.dialog.ok') },
      cancelProps: { children: intl('saenext.common.dialog.cancel') },
    });
  };

  const handleDeleteJob = async () => {
    try {
      const data = await services.DeleteJob({
        params: {
          AppId: jobId,
        },
        options: {
          region: regionId,
        }
      });

      const { Data: { success = false } = {} } = data || {};
      if (!success) {
        Message.error(intl('saenext.components.job-list.OperationFailed'));
        return false;
      } else {
        Message.success(intl('saenext.components.job-list.DeletedSuccessfully'));
        history.push(`/${regionId}/job-list`);
        return true;
      }
    } catch (error) {
      if (error.code === 'FetcherErrorRiskCancelled') {
        // 风控弹窗点取消
        return false;
      } else {
        // 通用控制台弹窗
        errorPrompt(error);
        return false;
      }
    }
  };

  const handleChangeVswitch = () => {
    const dialog = Dialog.alert({
      content: (
        <div>
          {intl('saenext.micro-app.basic-info.ShenzhenZoneAIsAbout')}{' '}
          <VSwitchDialog
            appConfig={jobConfig}
            refresh={() => {
              dialog.hide();
              refresh();
            }}
          >
            <LinkButton className="ml10">
              {intl('saenext.micro-app.basic-info.AppBaseInfo.MultiVswitchDeployment')}
            </LinkButton>
          </VSwitchDialog>
          {intl('saenext.micro-app.basic-info.ConfigureSwitchesInOtherZones')}
        </div>
      ),

      style: { width: 640 },
    });
  };

  const handleOnlyZhenZhenA = () => {
    if (isOnlyShenZhenA) {
      handleChangeVswitch();
      throw new Error('vSwitch only contains ShenZhen A zone');
    }
  };

  const renderDisabledTip = (conditions: string[]) => {
    if (isEmpty(conditions)) return '';

    const conditionTips = {
      isInDebt: isInDebt
        ? intl('saenext.micro-app.basic-info.AppActionBar.YourAccountIsOverduePlease')
        : '',
      LastChangeOrderRunning: LastChangeOrderRunning
        ? intl('saenext.job-detail.basic-info.JobActionBar.TheTaskTemplateIsChanging')
        : '',
      execForbid: execForbid
        ? intl('saenext.job-detail.basic-info.JobActionBar.YourTaskConcurrencyPolicyIs')
        : '',
      execSuspend: execSuspend
        ? intl('saenext.job-detail.basic-info.JobActionBar.YourTaskIsStoppedStart')
        : '',
    };

    for (const condition of conditions) {
      const tip = conditionTips[condition];
      if (tip) return tip;
    }

    return '';
  };

  return (
    <div>
      <ToolTipCondition
        show={LastChangeOrderRunning || isInDebt}
        align="t"
        tip={renderDisabledTip(['isInDebt', 'LastChangeOrderRunning'])}
      >
        <Button
          onClick={isOnlyShenZhenA ? handleChangeVswitch : goToDeploy}
          type="primary"
          className="ml-s"
          disabled={LastChangeOrderRunning || isInDebt}
        >
          {intl('saenext.job-detail.basic-info.JobActionBar.EditTaskTemplate')}
        </Button>
      </ToolTipCondition>
      <ToolTipCondition
        show={LastChangeOrderRunning || isInDebt}
        align="t"
        tip={renderDisabledTip(['isInDebt', 'LastChangeOrderRunning'])}
      >
        <RollbackSlide appId={jobId} type="job">
          <Button
            className="ml-s"
            disabled={LastChangeOrderRunning || isInDebt}
            onClick={handleOnlyZhenZhenA}
          >
            {intl('saenext.micro-app.basic-info.AppActionBar.RollbackHistory')}
          </Button>
        </RollbackSlide>
      </ToolTipCondition>
      <ToolTipCondition
        show={LastChangeOrderRunning || isInDebt || execForbid || execSuspend}
        align="t"
        tip={renderDisabledTip(['isInDebt', 'LastChangeOrderRunning', 'execForbid', 'execSuspend'])}
      >
        <RequestWrap>
          <Button
            onClick={handleExecJob}
            disabled={LastChangeOrderRunning || isInDebt || execForbid || execSuspend}
            className="ml-s"
          >
            {intl('saenext.job-detail.basic-info.JobActionBar.PerformTasksManually')}
          </Button>
        </RequestWrap>
      </ToolTipCondition>
      <ToolTipCondition
        show={LastChangeOrderRunning || isInDebt}
        align="t"
        tip={renderDisabledTip(['isInDebt', 'LastChangeOrderRunning'])}
      >
        <Button
          type="primary"
          onClick={handleSuspendJob}
          disabled={LastChangeOrderRunning || isInDebt}
          className="ml-s"
        >
          {Suspend
            ? intl('saenext.job-detail.basic-info.JobActionBar.StartATask')
            : intl('saenext.job-detail.basic-info.JobActionBar.StopATask')}
        </Button>
      </ToolTipCondition>

      <ToolTipCondition
        show={LastChangeOrderRunning}
        align="l"
        tip={renderDisabledTip(['LastChangeOrderRunning'])}
      >
        <Button disabled={LastChangeOrderRunning} onClick={deleteJobConfirm} className="ml-s">
          {intl('saenext.job-detail.basic-info.JobActionBar.DeleteATaskTemplate')}
        </Button>
      </ToolTipCondition>
    </div>
  );
};

export default JobActionBar;
