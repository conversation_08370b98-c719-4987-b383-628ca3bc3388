import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Form, Field, Icon, Message, Switch, ConsoleContext } from '@ali/cnd';
import { TableField, TextField, SelectField } from '@ali/deep';
import services from '~/services';
import _ from 'lodash';
import RefreshButton from '../../RefreshButton';
import CachedData from '~/cache/common';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 16, align: 'center' },
  labelCol: { span: 8, style: { width: 220, maxWidth: 220 } },
};

const OssSelector = (props, ref) => {
  const { value, onChange = () => {} } = props;

  const [useOss, setUseOss] = useState(false);
  const [bucketList, setBucketList] = useState([]);
  const [bucketLoading, setBucketLoading] = useState(false);
  const [ossRamWarn, setOssRamWarn] = useState(false);
  // @ts-ignore
  let tableRef = React.createRef() as any;
  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const field = Field.useField({
    onChange: () => {
      _.debounce(fieldValueChange, 500)();
    },
  });
  const { init } = field;
  const ossEnabled = CachedData.getOpenStatus(`ossStatus`);

  useEffect(() => {
    initValue();
  }, [value.length]);

  useEffect(() => {
    useOss && getBucketList();
  }, [useOss]);

  useImperativeHandle(
    ref,
    () => ({
      useOss,
      validate,
    }),
    [useOss],
  );

  const initValue = () => {
    if (value && value.length) {
      setUseOss(true);
      const ossList = [...value];
      field.setValues({ ossEnabled: true, ossList });
    }
  };

  const getBucketList = async () => {
    setBucketLoading(true);
    const { ListAllMyBucketsResult } = await services.ListBuckets({
      params: { 'max-keys': 999 },
      customErrorHandle: (error, data) => {
        if (error.code === 'AccessDenied') {
          setOssRamWarn(true);
          return {};
        }
        return error;
      },
    });
    const bucketList = _.get(ListAllMyBucketsResult, 'Buckets.Bucket', []);
    if (Array.isArray(bucketList)) {
      setBucketList(
        _.map(
          _.filter(bucketList, (v) => v.Region == regionId),
          (item) => {
            const { Name } = item;
            return {
              key: Name,
              value: Name,
              ...item,
            };
          },
        ),
      );
    } else {
      setBucketList([
        {
          key: _.get(bucketList, 'Name'),
          value: _.get(bucketList, 'Name'),
          ...bucketList,
        },
      ]);
    }
    setBucketLoading(false);
  };

  // 校验
  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((error, value) => {
        if (error) {
          resolve(false);
          return;
        }
        resolve(true);
      });
    });
  };

  const fieldValueChange = () => {
    const { getValue, validate } = field;

    let _ossConfigs = [];

    // 当开关关闭的时候 不会触发下validate
    const _ossEnabled = getValue('ossEnabled');
    if (!_ossEnabled) {
      onChange(_ossConfigs);
      return;
    }

    validate(async (error, values: any) => {
      if (error) return;
      const { ossList } = values;
      _ossConfigs = _.map(ossList, (item) => {
        const { bucketName, bucketPath, mountDir, readOnly } = item;
        return {
          bucketName,
          bucketPath,
          mountDir,
          readOnly,
        };
      });
      onChange(_ossConfigs);
    });
  };

  const handleBucketChange = ({ formGroupId, item }) => {
    // 原始组件未清空之后挂在目录、容器路径、权限
  };
  const tableValidation = (value = []) => {
    const mountDirs = [];
    for (let i = 0; i < value.length; i++) {
      const item = value[i];
      if (!item) {
        return intl('saenext.OssSelectorField.web-app.OssSelector_back.TheOssDataConfigurationIs');
      }
      if (!item.bucketName || !item.bucketPath || !item.mountDir || item.readOnly === undefined) {
        return intl('saenext.OssSelectorField.web-app.OssSelector_back.TheOssDataConfigurationIs');
      }
      if (item.bucketPath && !item.bucketPath.startsWith('/')) {
        return intl(
          'saenext.OssSelectorField.web-app.OssSelector_back.TheOssDataConfigurationIs.1',
        );
      }

      mountDirs.push(item.mountDir);
    }
    if (_.uniq(mountDirs).length !== mountDirs.length) {
      return intl('saenext.OssSelectorField.web-app.OssSelector_back.TheContainerPathIsDuplicate');
    }
    return true;
  };

  return (
    <>
      {/* {ossEnabled ? null : (
        <Message type="warning" className="mb-l">
          {intl('saenext.OssSelectorField.web-app.OssSelector_back.YouHaveNotActivatedObject')}

          <a
            href="https://common-buy.aliyun.com/?commodityCode=oss"
            target="_blank"
            className="ml-s"
          >
            {intl('saenext.OssSelectorField.web-app.OssSelector_back.ActivateNow')}
          </a>
        </Message>
      )} */}

      <OpenStatusMessage
        product="oss"
      />

      <Form field={field}>
        <Form.Item
          label={intl('saenext.OssSelectorField.web-app.OssSelector_back.EnableOssObjectStorage')}
          {...(fieldLayout as any)}
          help={
            <div>
              <span>
                {intl(
                  'saenext.OssSelectorField.web-app.OssSelector_back.OssObjectsHaveObviousAdvantages',
                )}
              </span>
              <span className="color-text-description">
                {intl('saenext.OssSelectorField.web-app.OssSelector_back.WeDoNotRecommendThat')}
                <a href={CachedData.confLink('help:sae:log-management-sls')} target="_blank">
                  {intl(
                    'saenext.OssSelectorField.web-app.OssSelector_back.HowToConfigureLogCollection',
                  )}
                </a>
              </span>
            </div>
          }
        >
          <Switch
            checked={useOss}
            disabled={!ossEnabled}
            {...init('ossEnabled', {
              initValue: useOss,
              props: {
                onChange: (value) => {
                  setUseOss(value);
                  if (!value) {
                    setOssRamWarn(false);
                  }
                },
              },
            })}
          />
        </Form.Item>
        {useOss && (
          <>
            {CachedData.isSubAccount() && ossRamWarn ? (
              <Message type="warning" className="mb-l">
                {intl(
                  'saenext.OssSelectorField.web-app.OssSelector_back.CurrentlyYourAccountHasNot',
                )}

                <a
                  href={`${CachedData.confLink('feature:ram:url')}/#/role/authorize`}
                  target="_blank"
                  className="ml-s mr-s"
                >
                  {intl('saenext.OssSelectorField.web-app.OssSelector_back.RamAccessControl')}
                </a>
                {intl(
                  'saenext.OssSelectorField.web-app.OssSelector_back.UseTheProductAfterAuthorization',
                )}
              </Message>
            ) : null}

            <Message
              type="notice"
              title={intl(
                'saenext.OssSelectorField.web-app.OssSelector_back.MountDirectoryNamingConventions',
              )}
              className="mb-l"
            >
              <div className="text-line">
                {intl('saenext.OssSelectorField.web-app.OssSelector_back.UseUtfCharactersThatMeet')}
              </div>
              <div className="text-line">
                {intl('saenext.OssSelectorField.web-app.OssSelector_back.EnterAPathThatStarts')}
              </div>
              <div className="text-line">
                {intl('saenext.OssSelectorField.web-app.OssSelector_back.TheContainerPathCannotBe')}
              </div>
            </Message>
            <TableField
              name="ossList"
              className="full24-table"
              layout="TABLER"
              showIndex={false}
              showTableHead={true}
              showSortable={false}
              minItems={1}
              addButtonText={intl('saenext.OssSelectorField.web-app.OssSelector_back.Add')}
              showDeleteConfirm={false}
              actionsColumnWidth={80}
              delButtonText={<Icon type="delete" />}
              ref={(c) => (tableRef = c?.getInstance?.() || c)}
              validation={[
                {
                  type: 'customValidate',
                  param: tableValidation,
                },
              ]}
            >
              <SelectField
                name="bucketName"
                label={
                  <div className="flex">
                    <span>Bucket</span>
                    <RefreshButton handler={getBucketList} className="ml-s" />
                  </div>
                }
                dataSource={bucketList}
                state={bucketLoading ? 'loading' : null}
                onChange={(item) => handleBucketChange(item)}
                showSearch
                notFoundContent={
                  <>
                    {intl(
                      'saenext.OssSelectorField.web-app.OssSelector_back.NoBucketIsAvailableIn',
                    )}
                    <a target="_blank" href={CachedData.confLink('feature:oss:url')}>
                      {intl('saenext.OssSelectorField.web-app.OssSelector_back.OssConsole')}
                    </a>
                    {intl('saenext.OssSelectorField.web-app.OssSelector_back.Create')}
                  </>
                }
              />

              <TextField
                name="bucketPath"
                label={intl('saenext.OssSelectorField.web-app.OssSelector_back.MountDirectory')}
                placeholder={intl(
                  'saenext.OssSelectorField.web-app.OssSelector_back.ExampleFolderaFolderb',
                )}
              />

              <TextField
                name="mountDir"
                label={intl('saenext.OssSelectorField.web-app.OssSelector_back.ContainerPath')}
                placeholder={intl(
                  'saenext.OssSelectorField.web-app.OssSelector_back.ExampleTmpOss',
                )}
              />

              <SelectField
                name="readOnly"
                label={intl('saenext.OssSelectorField.web-app.OssSelector_back.Permission')}
                dataSource={[
                  {
                    label: intl('saenext.OssSelectorField.web-app.OssSelector_back.ReadOnly'),
                    value: true,
                  },
                  {
                    label: intl('saenext.OssSelectorField.web-app.OssSelector_back.ReadWrite'),
                    value: false,
                  },
                ]}
              />
            </TableField>
          </>
        )}
      </Form>
    </>
  );
};

export default forwardRef(OssSelector);
