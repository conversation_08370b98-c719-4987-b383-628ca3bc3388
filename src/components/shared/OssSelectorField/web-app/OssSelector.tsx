import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import {
  Button,
  Form,
  Icon,
  Input,
  Message,
  Select,
  Switch,
  CndTable,
  ConsoleContext,
} from '@ali/cnd';
import services from '~/services';
import _ from 'lodash';
import RefreshButton from '../../RefreshButton';
import CachedData from '~/cache/common';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 16, align: 'center' },
  labelCol: { span: 8, style: { width: 220, maxWidth: 220 } },
};

const OssSelector = (props, ref) => {
  const { value, ossEnabled, onChange = () => {} } = props;

  const [useOss, setUseOss] = useState(false);
  const [ossList, setOssList] = useState<any>([{}]);
  const [bucketList, setBucketList] = useState([]);
  const [bucketLoading, setBucketLoading] = useState(false);
  const [ossRamWarn, setOssRamWarn] = useState(false);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  useEffect(() => {
    initValue();
  }, [value]);

  useEffect(() => {
    useOss && getBucketList();
  }, [useOss]);

  useImperativeHandle(
    ref,
    () => ({
      useOss,
    }),
    [useOss, ossList],
  );

  const initValue = () => {
    if (value && value.length) {
      setUseOss(true);
      setOssList([...value]);
    }
  };

  const getBucketList = async () => {
    setBucketLoading(true);
    const { ListAllMyBucketsResult } = await services.ListBuckets({
      params: { 'max-keys': 999 },
      customErrorHandle: (error, data) => {
        if (error.code === 'AccessDenied') {
          setOssRamWarn(true);
          return {};
        }
        return error;
      },
    });
    const bucketList = _.get(ListAllMyBucketsResult, 'Buckets.Bucket', []);
    if (Array.isArray(bucketList)) {
      setBucketList(
        _.map(
          _.filter(bucketList, (v) => v.Region == regionId),
          (item) => {
            const { Name } = item;
            return {
              key: Name,
              value: Name,
              ...item,
            };
          },
        ),
      );
    } else {
      setBucketList([
        {
          key: _.get(bucketList, 'Name'),
          value: _.get(bucketList, 'Name'),
          ...bucketList,
        },
      ]);
    }
    setBucketLoading(false);
  };

  const handleSwitchChange = (val) => {
    setUseOss(val);
    if (val) {
      onChange([
        {
          readOnly: true,
        },
      ]);
    } else {
      setOssRamWarn(false);
      onChange([]);
    }
  };

  const handleOssConfigChange = (idx, val, key) => {
    ossList[idx][key] = val;
    setOssList(ossList);
    onChange(ossList);
  };

  const handleAddOss = () => {
    ossList.push({
      readOnly: true,
    });
    setOssList(ossList);
    onChange(ossList);
  };

  const handleDeleteOss = (idx) => {
    if (ossList.length === 1) {
      Message.warning(intl('saenext.OssSelectorField.web-app.OssSelector.KeepAtLeastOneRecord'));
      return;
    }

    ossList.splice(idx, 1);
    setOssList(ossList);
    onChange(ossList);
  };

  return (
    <>
      {CachedData.getOpenStatus(`ossStatus`) ? null : (
        <Message type="warning" className="mb-l">
          {intl('saenext.OssSelectorField.web-app.OssSelector.YouHaveNotActivatedObject')}

          <a
            href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=oss`}
            target="_blank"
            className="ml-s"
          >
            {intl('saenext.OssSelectorField.web-app.OssSelector.ActivateNow')}
          </a>
        </Message>
      )}

      <Form {...(fieldLayout as any)}>
        <Form.Item
          label={intl('saenext.OssSelectorField.web-app.OssSelector.EnableOssObjectStorage')}
          help={
            <div>
              <span>
                {intl(
                  'saenext.OssSelectorField.web-app.OssSelector.OssObjectsHaveObviousAdvantages',
                )}
              </span>
              <span className="color-text-description">
                {intl('saenext.OssSelectorField.web-app.OssSelector.WeDoNotRecommendThat')}
                <a href={CachedData.confLink('help:sae:log-management-sls')} target="_blank">
                  {intl('saenext.OssSelectorField.web-app.OssSelector.HowToConfigureLogCollection')}
                </a>
              </span>
            </div>
          }
        >
          <Switch checked={useOss} disabled={!ossEnabled} onChange={handleSwitchChange} />
        </Form.Item>
      </Form>

      {useOss && (
        <>
          {CachedData.isSubAccount() && ossRamWarn ? (
            <Message type="warning" className="mb-l">
              {intl('saenext.OssSelectorField.web-app.OssSelector.CurrentlyYourAccountHasNot')}

              <a
                href={`${CachedData.confLink('feature:ram:url')}/#/role/authorize`}
                target="_blank"
                className="ml-s mr-s"
              >
                {intl('saenext.OssSelectorField.web-app.OssSelector.RamAccessControl')}
              </a>
              {intl('saenext.OssSelectorField.web-app.OssSelector.UseTheProductAfterAuthorization')}
            </Message>
          ) : null}

          <Message
            type="notice"
            title={intl(
              'saenext.OssSelectorField.web-app.OssSelector.MountDirectoryNamingConventions',
            )}
            className="mb-l"
          >
            <div className="text-line">
              {intl('saenext.OssSelectorField.web-app.OssSelector.UseUtfCharactersThatMeet')}
            </div>
            <div className="text-line">
              {intl('saenext.OssSelectorField.web-app.OssSelector.EnterAPathThatStarts')}
            </div>
            <div className="text-line">
              {intl('saenext.OssSelectorField.web-app.OssSelector.TheContainerPathCannotBe')}
            </div>
          </Message>
          <>
            <CndTable
              dataSource={ossList}
              primaryKey="key"
              style={{ width: '100%' }}
              hasBorder={false}
            >
              <CndTable.Column
                title={
                  <span>
                    <span>Bucket</span>
                  </span>
                }
                width="25%"
                dataIndex="bucketName"
                // @ts-ignore
                cell={(bucketName, idx, record) => (
                  <div className="flex" style={{ maxWidth: 340 }}>
                    <Select
                      value={bucketName}
                      dataSource={bucketList}
                      state={bucketLoading ? 'loading' : undefined}
                      onChange={(val, actionType, item) =>
                        handleOssConfigChange(idx, val, 'bucketName')
                      }
                      style={{ flex: 1 }}
                      showSearch
                      notFoundContent={
                        <>
                          {intl(
                            'saenext.OssSelectorField.web-app.OssSelector.NoBucketIsAvailableIn',
                          )}
                          <a target="_blank" href={CachedData.confLink('feature:oss:url')}>
                            {intl('saenext.OssSelectorField.web-app.OssSelector.OssConsole')}
                          </a>
                          {intl('saenext.OssSelectorField.web-app.OssSelector.Create')}
                        </>
                      }
                    />

                    <RefreshButton handler={getBucketList} className="ml-l" />
                  </div>
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>
                      {intl('saenext.OssSelectorField.web-app.OssSelector.MountDirectory')}
                    </span>
                  </span>
                }
                dataIndex="bucketPath"
                width="25%"
                // @ts-ignore
                cell={(bucketPath, idx) => (
                  <Input
                    value={bucketPath}
                    placeholder={intl(
                      'saenext.OssSelectorField.web-app.OssSelector.ExampleFolderaFolderbLeaveThe',
                    )}
                    style={{ width: '100%' }}
                    onChange={(val) => handleOssConfigChange(idx, val, 'bucketPath')}
                    trim={true}
                  />
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>
                      {intl('saenext.OssSelectorField.web-app.OssSelector.ContainerPath')}
                    </span>
                  </span>
                }
                dataIndex="mountDir"
                width="25%"
                // @ts-ignore
                cell={(val, idx, record) => (
                  <Input
                    value={val}
                    placeholder={intl('saenext.OssSelectorField.web-app.OssSelector.ExampleTmpOss')}
                    style={{ width: '100%' }}
                    onChange={(val) => handleOssConfigChange(idx, val, 'mountDir')}
                    trim={true}
                  />
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>{intl('saenext.OssSelectorField.web-app.OssSelector.Permission')}</span>
                  </span>
                }
                dataIndex="readOnly"
                width="15%"
                // @ts-ignore
                cell={(val, idx, record) => (
                  <Select
                    value={val}
                    onChange={(val) => handleOssConfigChange(idx, val, 'readOnly')}
                    dataSource={[
                      {
                        value: true,
                        label: intl('saenext.OssSelectorField.web-app.OssSelector.ReadOnly'),
                      },
                      {
                        value: false,
                        label: intl('saenext.OssSelectorField.web-app.OssSelector.ReadWrite'),
                      },
                    ]}
                    style={{ width: '100%' }}
                  />
                )}
              />

              <CndTable.Column
                title={intl('saenext.OssSelectorField.web-app.OssSelector.Operation')}
                width="10%"
                // @ts-ignore
                cell={(val, idx, record) => (
                  <Button text className="scale-medium" onClick={() => handleDeleteOss(idx)}>
                    <Icon type="delete" />
                  </Button>
                )}
              />
            </CndTable>
            {ossList.length < 5 && (
              <Button style={{ marginTop: 16 }} onClick={handleAddOss}>
                <Icon type="add" />
                {intl('saenext.OssSelectorField.web-app.OssSelector.Add')}
              </Button>
            )}
          </>
        </>
      )}
    </>
  );
};

export default forwardRef(OssSelector);
