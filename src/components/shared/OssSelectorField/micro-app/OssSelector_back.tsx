import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Form, Field, Icon, Input, Message, Switch, ConsoleContext } from '@ali/cnd';
import { TableField, TextField, SelectField } from '@ali/deep';
import services from '~/services';
import _ from 'lodash';
import RefreshButton from '../../RefreshButton';
import { isForbidden } from '~/utils/authUtils';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';
import CachedData from '~/cache/common';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const OssSelector = (props, ref) => {
  const { value, onChange = () => {}, NewSaeVersion = '' } = props;

  const [useOss, setUseOss] = useState(false);
  const [akIdInputType, setAkIdInputType] = useState('password');
  const [akSecretInputType, setAkSecretInputType] = useState('password');
  const [bucketList, setBucketList] = useState([]);
  const [bucketLoading, setBucketLoading] = useState(false);
  const [authedOSS, setAuthedOSS] = useState(true);
  // @ts-ignore
  let tableRef = React.createRef() as any;
  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const field = Field.useField({
    onChange: (name, value) => {
      if (name === 'ossEnabled' && !!value) {
        // 启用时不 onChange 不会触发校验
        return;
      }
      _.debounce(fieldValueChange, 500)();
    },
  });
  const { init } = field;

  useEffect(() => {
    initValue();
  }, [value]);

  useEffect(() => {
    useOss && getBucketList();
  }, [useOss]);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [useOss],
  );

  const initValue = () => {
    const { OssMountDescs = [] } = value || {};
    if (OssMountDescs.length) {
      const { OssAkId, OssAkSecret } = value || {};
      setUseOss(true);
      field.setValues({
        ossEnabled: true,
        OssAkId,
        OssAkSecret,
        ossList: OssMountDescs,
      });
    }
  };

  const getBucketList = async () => {
    setBucketLoading(true);
    const { ListAllMyBucketsResult } =
      (await services.ListBuckets({
        params: { 'max-keys': 999 },
        customErrorHandle: (error, data) => {
          if (isForbidden(error.code)) {
            setAuthedOSS(false);
          } else {
            setAuthedOSS(true);
          }
        },
      })) || {};
    const bucketList = _.get(ListAllMyBucketsResult, 'Buckets.Bucket', []);
    if (Array.isArray(bucketList)) {
      setBucketList(
        _.map(
          _.filter(bucketList, (v) => v.Region == regionId),
          (item) => {
            const { Name } = item;
            return {
              key: Name,
              value: Name,
              ...item,
            };
          },
        ),
      );
    } else {
      setBucketList([
        {
          key: _.get(bucketList, 'Name'),
          value: _.get(bucketList, 'Name'),
          ...bucketList,
        },
      ]);
    }
    setBucketLoading(false);
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((error, value) => {
        if (error) {
          resolve(' ');
          return;
        }
        resolve(true);
      });
    });
  };

  const fieldValueChange = () => {
    const { getValues } = field;

    const { ossEnabled, OssAkId, OssAkSecret, ossList: OssMountDescs } = getValues() as any;
    if (!ossEnabled) {
      onChange({
        OssAkId: '',
        OssAkSecret: '',
        OssMountDescs: [],
      });
      return;
    }
    onChange({
      OssAkId,
      OssAkSecret,
      OssMountDescs,
    });
  };

  const handleBucketChange = ({ formGroupId, item }) => {
    // 原始组件未清空之后挂在目录、容器路径、权限
  };
  const tableValidation = (value = []) => {
    const mountDirs = [];
    for (let i = 0; i < value.length; i++) {
      const item = value[i];
      if (!item) {
        return intl(
          'saenext.OssSelectorField.micro-app.OssSelector_back.TheOssDataConfigurationIs',
        );
      }
      if (!item.bucketName || !item.bucketPath || !item.mountPath || item.readOnly === undefined) {
        return intl(
          'saenext.OssSelectorField.micro-app.OssSelector_back.TheOssDataConfigurationIs',
        );
      }
      if (item.mountPath) {
        if (NewSaeVersion === 'pro' && item.mountPath === '/tmp') {
          return intl(
            'saenext.OssSelectorField.micro-app.OssSelector_back.ForProfessionalApplicationsTheTmp',
          );
        }
        const regex = /^(\/[a-zA-Z0-9._-]+)+\/{0,1}$/;
        if (!regex.test(item.mountPath)) {
          return intl(
            'saenext.OssSelectorField.micro-app.OssSelector_back.TheContainerPathFormatIs',
          );
        }
      }
      mountDirs.push(item.mountPath);
    }
    if (_.uniq(mountDirs).length !== mountDirs.length) {
      return intl(
        'saenext.OssSelectorField.micro-app.OssSelector_back.TheContainerPathIsDuplicate',
      );
    }
    return true;
  };

  return (
    <>
      <Form field={field}>
        <Form.Item
          label={intl('saenext.OssSelectorField.micro-app.OssSelector_back.EnableOssObjectStorage')}
          {...(fieldLayout as any)}
          help={
            <div>
              <span>
                {intl(
                  'saenext.OssSelectorField.micro-app.OssSelector_back.OssObjectsHaveObviousAdvantages',
                )}
              </span>
              <span className="color-text-description">
                {intl('saenext.OssSelectorField.micro-app.OssSelector_back.WeDoNotRecommendThat')}
                <a
                  href={CachedData.confLink('help:sae:set-log-collection-to-sls')}
                  target="_blank"
                >
                  {intl(
                    'saenext.OssSelectorField.micro-app.OssSelector_back.HowToConfigureLogCollection',
                  )}
                </a>
              </span>
            </div>
          }
        >
          <Switch
            checked={useOss}
            {...init('ossEnabled', {
              initValue: useOss,
              props: {
                onChange: (value) => setUseOss(value),
              },
            })}
          />
        </Form.Item>
        {useOss && (
          <>
            <Message
              title={intl('saenext.OssSelectorField.micro-app.OssSelector_back.Precautions')}
              type="warning"
              className="mb-l"
            >
              <div className="text-line">
                {intl(
                  'saenext.OssSelectorField.micro-app.OssSelector_back.AccesskeyIdAndSecretAre',
                )}
                <a href={`${CachedData.confLink('feature:usercenter:url')}`} target="_blank">
                  {intl(
                    'saenext.OssSelectorField.micro-app.OssSelector_back.HowToQueryAccesskeyId',
                  )}
                </a>
              </div>
              <div className="text-line">
                {intl(
                  'saenext.OssSelectorField.micro-app.OssSelector_back.WeStronglyRecommendThatYou',
                )}
              </div>
              <div className="text-line">
                {intl('saenext.OssSelectorField.micro-app.OssSelector_back.OssIsNotRecommendedAs')}
                <a href={CachedData.confLink('help:sae:configure-log-collection-to-sls')} target="_blank">
                  {intl(
                    'saenext.OssSelectorField.micro-app.OssSelector_back.HowToConfigureLogCollection',
                  )}
                </a>
              </div>
              <br />
              <div>
                {intl(
                  'saenext.OssSelectorField.micro-app.OssSelector_back.MountDirectoryNamingConventions',
                )}
              </div>
              <div className="text-line">
                {intl(
                  'saenext.OssSelectorField.micro-app.OssSelector_back.UseUtfCharactersThatMeet',
                )}
              </div>
              <div className="text-line">
                {intl(
                  'saenext.OssSelectorField.micro-app.OssSelector_back.SupportTheRootDirectoryAnd',
                )}
              </div>
            </Message>
            <div className="flex">
              <Form.Item required label="AccessKey ID" {...(fieldLayout as any)}>
                <Input
                  {...init('OssAkId', {
                    initValue: '',
                    props: {
                      onFocus: () => setAkIdInputType(''),
                      onBlur: () => setAkIdInputType('password'),
                    },
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.OssSelectorField.micro-app.OssSelector_back.AccesskeyIdCannotBeEmpty',
                        ),
                      },
                    ],
                  })}
                  placeholder=""
                  htmlType={akIdInputType}
                  style={{ width: 300 }}
                />
              </Form.Item>
              <Form.Item
                required
                label="AccessKey Secret"
                labelAlign="left"
                labelTextAlign="left"
                {...(fieldLayout as any)}
              >
                <Input
                  {...init('OssAkSecret', {
                    initValue: '',
                    props: {
                      onFocus: () => setAkSecretInputType(''),
                      onBlur: () => setAkSecretInputType('password'),
                    },
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.OssSelectorField.micro-app.OssSelector_back.AccesskeySecretCannotBeEmpty',
                        ),
                      },
                    ],
                  })}
                  placeholder=""
                  htmlType={akSecretInputType}
                  style={{ width: 300 }}
                />
              </Form.Item>
            </div>
            <TableField
              name="ossList"
              className="full24-table"
              layout="TABLER"
              showIndex={false}
              showTableHead={true}
              showSortable={false}
              minItems={1}
              addButtonText={intl('saenext.OssSelectorField.micro-app.OssSelector_back.Add')}
              showDeleteConfirm={false}
              actionsColumnWidth={80}
              delButtonText={<Icon type="delete" />}
              ref={(c) => (tableRef = c?.getInstance?.() || c)}
              validation={[
                {
                  type: 'customValidate',
                  param: tableValidation,
                },
              ]}
            >
              <SelectField
                name="bucketName"
                label={
                  <div className="flex">
                    <span>Bucket</span>
                    <RefreshButton handler={getBucketList} className="ml-s" />
                    <UnAuthedLabel text="" authed={authedOSS} authKey="AliyunOSSReadOnlyAccess" />
                  </div>
                }
                dataSource={bucketList}
                state={bucketLoading ? 'loading' : null}
                onChange={(item) => handleBucketChange(item)}
                showSearch
                notFoundContent={
                  <>
                    {intl(
                      'saenext.OssSelectorField.micro-app.OssSelector_back.NoBucketIsAvailableIn',
                    )}
                    <a target="_blank" href={CachedData.confLink('feature:oss:url')}>
                      {intl('saenext.OssSelectorField.micro-app.OssSelector_back.OssConsole')}
                    </a>
                    {intl('saenext.OssSelectorField.micro-app.OssSelector_back.Create')}
                  </>
                }
              />

              <TextField
                name="bucketPath"
                label={intl('saenext.OssSelectorField.micro-app.OssSelector_back.MountDirectory')}
                placeholder={intl(
                  'saenext.OssSelectorField.micro-app.OssSelector_back.ExampleFolderaFolderb',
                )}
              />

              <TextField
                name="mountPath"
                label={intl('saenext.OssSelectorField.micro-app.OssSelector_back.ContainerPath')}
                placeholder={intl(
                  'saenext.OssSelectorField.micro-app.OssSelector_back.ExampleTmpOss',
                )}
              />

              <SelectField
                name="readOnly"
                label={intl('saenext.OssSelectorField.micro-app.OssSelector_back.Permission')}
                dataSource={[
                  {
                    label: intl('saenext.OssSelectorField.micro-app.OssSelector_back.ReadOnly'),
                    value: true,
                  },
                  {
                    label: intl('saenext.OssSelectorField.micro-app.OssSelector_back.ReadWrite'),
                    value: false,
                  },
                ]}
              />
            </TableField>
          </>
        )}
      </Form>
    </>
  );
};

export default forwardRef(OssSelector);
