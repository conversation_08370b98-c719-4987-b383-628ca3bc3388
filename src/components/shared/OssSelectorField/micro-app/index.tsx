import React, { Component } from 'react';
import OssSelector from './OssSelector_back';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';

type Props = {
  value?: Object;
  onChange?: Function;
};

class OssSelectorFieldClass extends Component<Props> {
  private ossRef = React.createRef() as any;

  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.ossRef.validate();
  }

  render() {

    return (
      <OssSelector
        {...this.props}
        ref={(ref) => this.ossRef = ref}
      />
    )
  }
}

class OssSelectorField extends BaseField {
  props;
  static displayName = 'OssSelectorField';

  // static propTypes = {
  //   ...BaseField.propTypes,
  // };

  // static defaultProps = {
  //   ...BaseField.defaultProps,
  // };

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return (
      <OssSelectorFieldClass
        {...newProps}
      />
    );
  }
}

export default ConfigProvider.config(OssSelectorField as any)
