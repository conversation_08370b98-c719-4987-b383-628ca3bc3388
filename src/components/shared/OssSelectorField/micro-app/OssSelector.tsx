import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import {
  Button,
  Form,
  Icon,
  Input,
  Message,
  Select,
  Switch,
  CndTable,
  ConsoleContext,
} from '@ali/cnd';
import services from '~/services';
import _ from 'lodash';
import RefreshButton from '../../RefreshButton';
import CachedData from '~/cache/common';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const OssSelector = (props, ref) => {
  const { value, onChange = () => {} } = props;

  const [useOss, setUseOss] = useState(false);
  const [akIdInputType, setAkIdInputType] = useState('password');
  const [akSecretInputType, setAkSecretInputType] = useState('password');
  const [ossAkId, setOssAkId] = useState('');
  const [ossAkSecret, setOssAkSecret] = useState('');
  const [ossList, setOssList] = useState<any>([{}]);
  const [bucketList, setBucketList] = useState([]);
  const [bucketLoading, setBucketLoading] = useState(false);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  useEffect(() => {
    initValue();
  }, [value]);

  useEffect(() => {
    useOss && getBucketList();
  }, [useOss]);

  useEffect(() => {
    if (!useOss) {
      onChange({
        OssAkId: '',
        OssAkSecret: '',
        OssMountDescs: [],
      });
    }
  }, [useOss]);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [useOss, ossList],
  );

  const initValue = () => {
    const { OssAkId = '', OssAkSecret = '', OssMountDescs = [] } = value || {};
    if (OssMountDescs.length) {
      setUseOss(true);
      setOssAkId(OssAkId);
      setOssAkSecret(OssAkSecret);
      setOssList([...OssMountDescs]);
    }
  };

  const getBucketList = async () => {
    setBucketLoading(true);
    const { ListAllMyBucketsResult } = await services.ListBuckets({
      params: { 'max-keys': 999 },
      customErrorHandle: (error, data) => {},
    });
    const bucketList = _.get(ListAllMyBucketsResult, 'Buckets.Bucket', []);
    if (Array.isArray(bucketList)) {
      setBucketList(
        _.map(
          _.filter(bucketList, (v) => v.Region == regionId),
          (item) => {
            const { Name } = item;
            return {
              key: Name,
              value: Name,
              ...item,
            };
          },
        ),
      );
    } else {
      setBucketList([
        {
          key: _.get(bucketList, 'Name'),
          value: _.get(bucketList, 'Name'),
          ...bucketList,
        },
      ]);
    }
    setBucketLoading(false);
  };

  const handleSwitchChange = (val) => {
    setUseOss(val);
    if (val) {
      onChange({
        OssAkId: '',
        OssAkSecret: '',
        OssMountDescs: [
          {
            readOnly: true,
          },
        ],
      });
    }
  };

  const handleAkIdChange = (val) => {
    setOssAkId(val);
    onChange({
      ...value,
      OssAkId: val,
    });
  };

  const handleAkSecretChange = (val) => {
    setOssAkSecret(val);
    onChange({
      ...value,
      OssAkSecret: val,
    });
  };

  const handleOssConfigChange = (idx, val, key) => {
    ossList[idx][key] = val;
    setOssList(ossList);
    onChange({
      ...value,
      OssMountDescs: ossList,
    });
  };

  const handleAddOss = () => {
    ossList.push({
      readOnly: true,
    });
    setOssList(ossList);
    onChange({
      ...value,
      OssMountDescs: ossList,
    });
  };

  const handleDeleteOss = (idx) => {
    if (ossList.length === 1) {
      Message.warning(intl('saenext.OssSelectorField.micro-app.OssSelector.KeepAtLeastOneRecord'));
      return;
    }

    ossList.splice(idx, 1);
    setOssList(ossList);
    onChange({
      ...value,
      OssMountDescs: ossList,
    });
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      if (!useOss) {
        resolve(true);
        return;
      }
      if (!ossAkId || !ossAkSecret) {
        resolve(intl('saenext.OssSelectorField.micro-app.OssSelector.PleaseFillInAccesskeyId'));
        return;
      }
      _.forEach(ossList, (item) => {
        if (
          !item.bucketName ||
          !item.bucketPath ||
          !item.mountPath ||
          item.readOnly === undefined
        ) {
          resolve(
            intl('saenext.OssSelectorField.micro-app.OssSelector.CompleteTheOssConfiguration'),
          );
          return;
        }
      });
      resolve(true);
    });
  };

  return (
    <>
      <Form {...(fieldLayout as any)}>
        <Form.Item
          label={intl('saenext.OssSelectorField.micro-app.OssSelector.EnableOssObjectStorage')}
          help={
            <div>
              <span>
                {intl(
                  'saenext.OssSelectorField.micro-app.OssSelector.OssObjectsHaveObviousAdvantages',
                )}
              </span>
              <span className="color-text-description">
                {intl('saenext.OssSelectorField.micro-app.OssSelector.WeDoNotRecommendThat')}
                <a href={CachedData.confLink('help:sae:configure-log-collection-to-sls')} target="_blank">
                  {intl(
                    'saenext.OssSelectorField.micro-app.OssSelector.HowToConfigureLogCollection',
                  )}
                </a>
              </span>
            </div>
          }
        >
          <Switch checked={useOss} onChange={handleSwitchChange} />
        </Form.Item>
      </Form>

      {useOss && (
        <>
          <Message
            title={intl('saenext.OssSelectorField.micro-app.OssSelector.Precautions')}
            type="warning"
            className="mb-l"
          >
            <div className="text-line">
              {intl('saenext.OssSelectorField.micro-app.OssSelector.AccesskeyIdAndSecretAre')}
              <a href={`${CachedData.confLink('feature:usercenter:url')}/#/manage/ak`} target="_blank">
                {intl('saenext.OssSelectorField.micro-app.OssSelector.HowToQueryAccesskeyId')}
              </a>
            </div>
            <div className="text-line">
              {intl('saenext.OssSelectorField.micro-app.OssSelector.WeStronglyRecommendThatYou')}
            </div>
            <div className="text-line">
              {intl('saenext.OssSelectorField.micro-app.OssSelector.OssIsNotRecommendedAs')}
              <a href={CachedData.confLink('help:sae:configure-log-collection-to-sls')} target="_blank">
                {intl('saenext.OssSelectorField.micro-app.OssSelector.HowToConfigureLogCollection')}
              </a>
            </div>
            <br />
            <div>
              {intl(
                'saenext.OssSelectorField.micro-app.OssSelector.MountDirectoryNamingConventions',
              )}
            </div>
            <div className="text-line">
              {intl('saenext.OssSelectorField.micro-app.OssSelector.UseUtfCharactersThatMeet')}
            </div>
            <div className="text-line">
              {intl('saenext.OssSelectorField.micro-app.OssSelector.SupportTheRootDirectoryAnd')}
            </div>
          </Message>
          <>
            <Form inline>
              <Form.Item label="AccessKey ID:">
                <Input
                  placeholder=""
                  onFocus={() => {
                    setAkIdInputType('');
                  }}
                  onBlur={() => {
                    setAkIdInputType('password');
                  }}
                  htmlType={akIdInputType}
                  value={ossAkId}
                  onChange={handleAkIdChange}
                  style={{ width: 300 }}
                />
              </Form.Item>
              <Form.Item label="AccessKey Secret:">
                <Input
                  placeholder=""
                  onFocus={() => {
                    setAkSecretInputType('');
                  }}
                  onBlur={() => {
                    setAkSecretInputType('password');
                  }}
                  htmlType={akSecretInputType}
                  value={ossAkSecret}
                  onChange={handleAkSecretChange}
                  style={{ width: 300 }}
                />
              </Form.Item>
            </Form>
            <CndTable
              dataSource={ossList}
              primaryKey="key"
              style={{ width: '100%' }}
              hasBorder={false}
            >
              <CndTable.Column
                title={
                  <span>
                    <span>Bucket</span>
                  </span>
                }
                width="25%"
                dataIndex="bucketName"
                // @ts-ignore
                cell={(bucketName, idx, record) => (
                  <div className="flex" style={{ maxWidth: 340 }}>
                    <Select
                      value={bucketName}
                      dataSource={bucketList}
                      state={bucketLoading ? 'loading' : undefined}
                      onChange={(val, actionType, item) =>
                        handleOssConfigChange(idx, val, 'bucketName')
                      }
                      style={{ flex: 1 }}
                      showSearch
                      notFoundContent={
                        <>
                          {intl(
                            'saenext.OssSelectorField.micro-app.OssSelector.NoBucketIsAvailableIn',
                          )}
                          <a target="_blank" href={CachedData.confLink('feature:oss:url')}>
                            {intl('saenext.OssSelectorField.micro-app.OssSelector.OssConsole')}
                          </a>
                          {intl('saenext.OssSelectorField.micro-app.OssSelector.Create')}
                        </>
                      }
                    />

                    <RefreshButton handler={getBucketList} className="ml-l" />
                  </div>
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>
                      {intl('saenext.OssSelectorField.micro-app.OssSelector.MountDirectory')}
                    </span>
                  </span>
                }
                width="25%"
                dataIndex="bucketPath"
                // @ts-ignore
                cell={(bucketPath, idx) => (
                  <Input
                    value={bucketPath}
                    placeholder={intl(
                      'saenext.OssSelectorField.micro-app.OssSelector.ExampleFolderaFolderbLeaveThe',
                    )}
                    style={{ width: '100%' }}
                    onChange={(val) => handleOssConfigChange(idx, val, 'bucketPath')}
                    trim={true}
                  />
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>
                      {intl('saenext.OssSelectorField.micro-app.OssSelector.ContainerPath')}
                    </span>
                  </span>
                }
                width="25%"
                dataIndex="mountPath"
                // @ts-ignore
                cell={(val, idx, record) => (
                  <Input
                    value={val}
                    placeholder={intl(
                      'saenext.OssSelectorField.micro-app.OssSelector.ExampleTmpOss',
                    )}
                    style={{ width: '100%' }}
                    onChange={(val) => handleOssConfigChange(idx, val, 'mountPath')}
                    trim={true}
                  />
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>{intl('saenext.OssSelectorField.micro-app.OssSelector.Permission')}</span>
                  </span>
                }
                width="15%"
                dataIndex="readOnly"
                // @ts-ignore
                cell={(val, idx, record) => (
                  <Select
                    value={val}
                    onChange={(val) => handleOssConfigChange(idx, val, 'readOnly')}
                    dataSource={[
                      {
                        value: true,
                        label: intl('saenext.OssSelectorField.micro-app.OssSelector.ReadOnly'),
                      },
                      {
                        value: false,
                        label: intl('saenext.OssSelectorField.micro-app.OssSelector.ReadWrite'),
                      },
                    ]}
                    style={{ width: '100%' }}
                  />
                )}
              />

              <CndTable.Column
                title={intl('saenext.OssSelectorField.micro-app.OssSelector.Operation')}
                width="10%"
                // @ts-ignore
                cell={(val, idx, record) => (
                  <Button text className="scale-medium" onClick={() => handleDeleteOss(idx)}>
                    <Icon type="delete" />
                  </Button>
                )}
              />
            </CndTable>
            {ossList.length < 10 && (
              <Button style={{ marginTop: 16 }} onClick={handleAddOss}>
                <Icon type="add" />
                {intl('saenext.OssSelectorField.micro-app.OssSelector.Add')}
              </Button>
            )}
          </>
        </>
      )}
    </>
  );
};

export default forwardRef(OssSelector);
