import React, { useState, useEffect, useMemo } from 'react';
import { intl, Icon, Message, Balloon } from '@ali/cnd';
import { Radio, RadioField, SelectField } from '@ali/deep';
import services from '~/services';
import { get, map, isEmpty, isEqual } from 'lodash';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';
import { isForbidden } from '~/utils/authUtils';
import CachedData from '~/cache/common';

const formItemLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};
const ExitMicroNacosInstance = '-1';

const NacosRegistrationField = (props) => {
  const [authedMSE, setAuthedMSE] = useState(true);
  const { field, microRegistration, microRegistrationConfig } = props;
  let mseNacosInstance = '';
  if (isEqual(microRegistration, '2')) {
    mseNacosInstance = ExitMicroNacosInstance;
    if (!isEmpty(microRegistrationConfig)) {
      const registrationConfig = JSON.parse(microRegistrationConfig);
      if (!isEmpty(registrationConfig)) {
        mseNacosInstance = registrationConfig.instanceId;
      }
    }
  }

  const { MicroRegistration = microRegistration, MicroNacosInstanceId: mseNacosInstanceId = '' } =
    field?.getValues() || {};
  const [loading, setLoading] = useState(false);
  const [mseNacosInstances, setMseNacosInstances] = useState([]);
  const [mseNacosNamespaces, setMseNacosNamespaces] = useState([]);
  const [mseNacosNamespaceNotFound, setMseNacosNamespaceNotFound] = useState(false);

  useEffect(() => {
    if (MicroRegistration === '2') {
      getMseNacosInstances();
      getMseNacosNamespaces(mseNacosInstance);
    }
  }, [MicroRegistration, mseNacosInstance]);

  const getMseNacosInstances = async () => {
    const res = await services.listMseNacosInstances({
      params: {
        PageNum: 1,
        PageSize: 99,
      },
      customErrorHandle: (error) => {
        setAuthedMSE(!isForbidden(error.code));
      },
    });
    let _nacosInstances = map(get(res, 'Data', []), (item) => ({
      ...item,
      label: item?.ClusterAliasName,
      value: item?.InstanceId,
    }));
    // 说明是存量应用
    // if (microRegistration === '2' && mseNacosInstance === ExitMicroNacosInstance) {
    //   _nacosInstances = [
    //     {
    //       label: intl('saenext.components.shared.NacosRegistrationField.SelectNoInstance'),
    //       value: ExitMicroNacosInstance,
    //     },
    //     ..._nacosInstances,
    //   ];
    // }
    _nacosInstances = [
      {
        label: intl('saenext.components.shared.NacosRegistrationField.SelectNoInstance'),
        value: ExitMicroNacosInstance,
      },
      ..._nacosInstances,
    ];

    setMseNacosInstances(_nacosInstances);
  };

  const getMseNacosNamespaces = async (instanceId) => {
    let _mseNacosNamespaceNotFound = false;
    if (!instanceId || instanceId === ExitMicroNacosInstance) return;
    setLoading(true);
    const res = await services.listMseNacosNamespaces({
      params: {
        InstanceId: instanceId,
      },
      customErrorHandle: (err, data, callback) => {
        if (err?.code === 'InvalidParameter'|| err?.code === 'NotFound') {
          _mseNacosNamespaceNotFound = true;
        } else {
          callback();
        }
      },
    });
    const { Data = [] } = res || {};
    const _nacosNamespaces = map(Data, (item) => ({
      ...item,
      label:
        isEmpty(item?.Namespace) && item?.NamespaceShowName === 'public' ? (
          <Balloon trigger={<span>{item?.NamespaceShowName}</span>} align="r" closable={false}>
            <span>
              {intl(
                'saenext.components.shared.NacosRegistrationField.PublicNamespaceDoesNotSupport',
              )}
            </span>
          </Balloon>
        ) : (
          <span>{item?.NamespaceShowName}</span>
        ),

      disabled: isEmpty(item?.Namespace) && item?.NamespaceShowName === 'public',
      value: item?.Namespace || 'public',
    }));
    setLoading(false);
    setMseNacosNamespaces(_nacosNamespaces);
    setMseNacosNamespaceNotFound(_mseNacosNamespaceNotFound);
    return _mseNacosNamespaceNotFound;
  };

  const microRegistrationHelp = useMemo(() => {
    if (MicroRegistration === '0') {
      return intl('saenext.components.shared.NacosRegistrationField.WhenYouSelectSaeBuilt');
    } else if (MicroRegistration === '2') {
      return '';
    } else {
      return intl('saenext.components.shared.NacosRegistrationField.WhenYouSelectTheRegistry');
    }
  }, [MicroRegistration]);

  const handleNacosInstanceChange = (item) => {
    const { value: instanceId } = item;
    field?.setValues({ MicroNacosNamespace: '' });
    setMseNacosNamespaces([]);
    getMseNacosNamespaces(instanceId);
  };

  return (
    <>
      <div>
        <Message
          type="notice"
          className="mb-l"
          title={intl('saenext.components.shared.NacosRegistrationField.UsageRestrictions')}
        >
          <div className="text-line">
            {intl('saenext.components.shared.NacosRegistrationField.TheBuiltInNacosAnd')}
            <a
              href={CachedData.confLink('help:sae:microservices-use-the-sae-built-in-nacos-registry')}
              target="_blank"
            >
              {intl('saenext.components.shared.NacosRegistrationField.SaeBuiltInNacosClient')}
            </a>
            <span>{intl('saenext.components.shared.NacosRegistrationField.And')}</span>
            <a
              href={CachedData.confLink('help:sae:microservices-use-an-mse-nacos-registry')}
              target="_blank"
            >
              {intl('saenext.components.shared.NacosRegistrationField.MseNacosClientVersionLimits')}
            </a>
            {intl('saenext.common.full-stop')}
          </div>
          <div className="text-line">
            2. {intl('saenext.components.shared.NacosRegistrationField.TheBuiltInNacosOf')}
          </div>
        </Message>
      </div>

      <RadioField
        name="MicroRegistration"
        defaultValue={'0'}
        label={intl(
          'saenext.components.shared.NacosRegistrationField.NacosRegistryServiceDiscovery',
        )}
        help={microRegistrationHelp}
        {...formItemLayout}
        onChange={({ value }) => {
          field?.setValues({
            MicroNacosInstanceId: '',
            MicroNacosNamespace: '',
            MicroRegistrationConfig: undefined,
          });
          setMseNacosNamespaces([]);
        }}
      >
        <Radio
          value={'0'}
          label={intl('saenext.components.shared.NacosRegistrationField.SaeBuiltInNacos')}
        />

        <Radio value={'2'}>
          <>
            {intl('saenext.components.shared.NacosRegistrationField.MseNacosEnterpriseEdition')}

            <a href={CachedData.confLink('feature:mse:productIntro')} target="_blank">
              <Icon type="external-link" size="xs" className="ml-s" />
            </a>
          </>
        </Radio>
        <Radio
          value={'1'}
          label={intl(
            'saenext.components.shared.NacosRegistrationField.UserCreatedRegistryNacosZk',
          )}
        />
      </RadioField>
      {MicroRegistration === '2' ? (
        <>
          <div className="flex nacos-select">
            <SelectField
              validation={[
                {
                  type: 'customValidate',
                  param: async (value) => {
                    const mseNacosNamespaceNotFound = await getMseNacosNamespaces(value);
                    if (mseNacosNamespaceNotFound) {
                      return intl(
                        'saenext.components.shared.NacosRegistrationField.TheInstanceHasBeenDeleted',
                      );
                    }
                    return true;
                  },
                },
                {
                  type: 'required',
                  message: intl(
                    'saenext.components.shared.NacosRegistrationField.SelectMseNacosInstance',
                  ),
                },
              ]}
              labelAlign="left"
              labelTextAlign="left"
              labelCol={{
                style: { width: 220, maxWidth: 220 },
              }}
              wrapperCol={{
                style: { width: 260, maxWidth: 260 },
              }}
              name="MicroNacosInstanceId"
              label={
                <UnAuthedLabel
                  authed={authedMSE}
                  text={intl('saenext.components.shared.NacosRegistrationField.MseNacosInstances')}
                  authKey="AliyunMSEReadOnlyAccess"
                />
              }
              dataSource={mseNacosInstances}
              onChange={handleNacosInstanceChange}
              state={mseNacosNamespaceNotFound ? 'error' : null}
            />

            {mseNacosInstanceId !== ExitMicroNacosInstance ? (
              <SelectField
                required
                labelAlign="left"
                labelTextAlign="right"
                labelCol={{
                  style: { width: 100, maxWidth: 100 },
                }}
                wrapperCol={{
                  style: { width: 260, maxWidth: 260 },
                }}
                name="MicroNacosNamespace"
                label={intl('saenext.components.shared.NacosRegistrationField.Namespace')}
                dataSource={mseNacosNamespaces}
                state={loading ? 'loading' : null}
              />
            ) : null}
          </div>
          <div style={{ color: '#888', marginLeft: 220 }}>
            <div>{intl('saenext.components.shared.NacosRegistrationField.IfYouDoNotSelect')}</div>
            <div>
              {intl('saenext.components.shared.NacosRegistrationField.IfYouSelectASpecific')}
            </div>
          </div>
        </>
      ) : null}
    </>
  );
};
export default NacosRegistrationField;

export function parseRegistration(value) {
  let _value = {};
  if (isEqual(get(value, 'MicroRegistration'), '2')) {
    const _registrationConfig = get(value, 'MicroRegistrationConfig', '{}');
    if (!isEmpty(_registrationConfig)) {
      const registrationConfig = JSON.parse(_registrationConfig);
      if (!isEmpty(registrationConfig)) {
        Reflect.set(_value, 'MicroNacosInstanceId', registrationConfig.instanceId);
        Reflect.set(_value, 'MicroNacosNamespace', registrationConfig.namespace);
      } else {
        Reflect.set(_value, 'MicroNacosInstanceId', '-1');
      }
    }
  }
  return _value;
}

export const formatRegistration = (params) => {
  const { MicroNacosInstanceId, MicroNacosNamespace, MicroRegistration } = params;

  if (MicroRegistration === '2') {
    if (MicroNacosInstanceId !== '-1') {
      params.MicroRegistrationConfig = JSON.stringify({
        instanceId: MicroNacosInstanceId,
        namespace: MicroNacosNamespace,
      });
    } else {
      params.MicroRegistrationConfig = '';
    }
  }

  delete params.MicroNacosInstanceId;
  delete params.MicroNacosNamespace;
};
