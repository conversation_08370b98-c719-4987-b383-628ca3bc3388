import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { isEmpty, isUndefined } from 'lodash';
import flatFieldValue from '~/hoc/flatFieldValue';
import { jsonParse, jsonStringify } from '~/utils/transfer-data';
import BinShell, { COMMAND, COMMAND_LIST } from './BinShell';
import DockerShell from './DockerShell';
import { Button, Message } from '@ali/cnd';
import If from '../If';

const StartCommandArgs = (props) => {
  const { value, onChange } = props;

  const [isBinShell, setIsBinShell] = useState<boolean | undefined>();

  useEffect(() => {
    parseValue(value);
  }, [jsonStringify(value)]);

  const parseValue = (v) => {
    const { Command, CommandArgs = '' } = v;
    const commandArgs = jsonParse(CommandArgs) || [];
    if (isEmpty(commandArgs)) return;
    if (COMMAND_LIST.includes(Command) && commandArgs.length === 2 && commandArgs[0] === '-c') {
      intBinShellType(true);
    } else {
      intBinShellType(false);
    }
  };

  const intBinShellType = (v) => {
    // 只在第一次有值回显时判断，避免编辑时类型变化
    if (!isUndefined(isBinShell)) return;
    setIsBinShell(v);
  };

  const transIntoBinShell = () => {
    setIsBinShell(true);

    const { Command, CommandArgs = '' } = value;
    const commandArgs = jsonParse(CommandArgs) || [];
    const binArgs = [Command, ...commandArgs];
    onChange({
      Command: COMMAND.SHELL,
      CommandArgs: jsonStringify(['-c', binArgs.join('\n')]),
    });
  };

  return (
    <>
      <If condition={isBinShell === false}>
        <Message type="notice" className="mb">
          {intl('saenext.shared.StartCommandArgs.WhetherToConvertToA')}

          <Button text type="primary" className="ml-s" onClick={transIntoBinShell}>
            {intl('saenext.shared.StartCommandArgs.Conversion')}
          </Button>
        </Message>
      </If>
      {isBinShell !== false ? <BinShell {...props} /> : <DockerShell {...props} />}
    </>
  );
};

export default StartCommandArgs;
