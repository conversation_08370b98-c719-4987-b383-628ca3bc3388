import React from 'react';
import { Button, Grid, Icon, Input, intl, LinkButton } from '@ali/cnd';

const { Row, Col } = Grid;

const ArgsRow = (props) => {
  const { value = [], onChange = () => {}, isPreview } = props;

  const onAdd = () => {
    value.push('');
    onChange(value);
  };

  const onDelete = (idx) => {
    value.splice(idx, 1);
    onChange(value);
  };

  const onItemChange = (idx, val) => {
    value[idx] = val;
    onChange(value);
  };

  return (
    <>
      {value.map((item, idx) => (
        <Row className="mt-s mb-s" key={idx} gutter={16}>
          <Col span={23}>
            <Input.TextArea
              rows={1}
              placeholder={intl(
                'saenext.shared.StartCommandArgs.ArgsRow.ControlTheInputParametersOf',
              )}
              value={item}
              onChange={(v) => {
                onItemChange(idx, v);
              }}
            />
          </Col>
          <Col span={1} className="flex">
            <Button
              text
              className={isPreview ? 'none' : 'scale-medium'}
              onClick={() => {
                onDelete(idx);
              }}
            >
              <Icon type="delete" />
            </Button>
          </Col>
        </Row>
      ))}

      <Button className="mt-s" onClick={() => onAdd()}>
        <Icon type="add" />
        <span>{intl('saenext.shared.ValueTable.Add')}</span>
      </Button>
    </>
  );
};

export default ArgsRow;
