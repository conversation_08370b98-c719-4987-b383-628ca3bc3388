import { intl } from '@ali/cnd';
import React, { useMemo } from 'react';
import { Form, Input } from '@ali/cnd';
import { jsonParse, jsonStringify } from '~/utils/transfer-data';
import C from '~/constants/common';
import ArgsRow from './ArgsRow';
import { isEmpty } from 'lodash';

const DockerShell = (props) => {
  const { value = {}, onChange = () => {} } = props;

  const args = useMemo(() => {
    const commandArgs = jsonParse(value.CommandArgs) || [];
    return commandArgs;
  }, [value.CommandArgs]);

  const onCommandChange = (v) => {
    onChange({
      ...value,
      Command: v,
    });
  };

  const onArgsChange = (v) => {
    onChange({
      ...value,
      CommandArgs: isEmpty(v) ? '' : jsonStringify(v),
    });
  };

  return (
    <div style={{ maxWidth: 1000 }}>
      <Form.Item
        label={intl('saenext.shared.StartCommandArgs.DockerShell.StartCommand')}
        {...C.FORM_LAYOUT_LEFT}
      >
        <Input
          placeholder={intl(
            'saenext.shared.StartCommandArgs.DockerShell.InputCommandsThatControlContainer',
          )}
          value={value.Command}
          onChange={onCommandChange}
        />
      </Form.Item>
      <Form.Item
        label={intl('saenext.shared.StartCommandArgs.DockerShell.StartupParameters')}
        {...C.FORM_LAYOUT_LEFT}
      >
        <ArgsRow value={args} onChange={onArgsChange} />
      </Form.Item>
    </div>
  );
};

export default DockerShell;
