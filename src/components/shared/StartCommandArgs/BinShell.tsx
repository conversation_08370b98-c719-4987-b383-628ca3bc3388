import { Radio, ShellEditor } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import ResizeWrap from '../ResizeWrap';
import { jsonParse, jsonStringify } from '~/utils/transfer-data';
import { isEmpty } from 'lodash';

export enum COMMAND {
  SHELL = '/bin/sh',
  BASH = '/bin/bash',
}

export const COMMAND_LIST = [COMMAND.SHELL, COMMAND.BASH];

const BinShell = (props) => {
  const {
    value,
    onChange,
    ...restProps
  } = props;
  
  const defaultCommand = COMMAND.SHELL;

  const [command, setCommand] = useState<string>(defaultCommand);
  const [args, setArgs] = useState('');

  useEffect(() => {
    parseValue(value)
  }, [jsonStringify(value)])

  const parseValue = (v) => {
    const { Command, CommandArgs = [] } = v;
    const commandArgs = jsonParse(CommandArgs) || [];
    if (commandArgs.length === 2 && commandArgs[0] === '-c') {
      const [ dashC, argsStr ] = commandArgs;
      
      setCommand(Command);
      setArgs(argsStr);
    } else {
      setCommand(defaultCommand);
      setArgs('');
    }
  }
  
  const onCmdChange = (v) => {
    setCommand(v);
    if (!isEmpty(args)) {
      onChange({
        Command: v,
        CommandArgs: jsonStringify(['-c', args]),
      })
    }
  }

  const onArgsChange = (v) => {
    setArgs(v);
    if (!isEmpty(v)) {
      onChange({
        Command: command,
        CommandArgs: jsonStringify(['-c', v]),
      })
    } else {
      onChange({
        Command: '',
        CommandArgs: ''
      })
    }
  }

  return (
    <>
      <Radio.Group
        className='mb'
        value={command}
        onChange={onCmdChange}
      >
        <Radio id="/bin/sh" value="/bin/sh">
         {` >_ /bin/sh`}
        </Radio>
        <Radio id="/bin/bash" value="/bin/bash">
          {`>_ /bin/bash`}
        </Radio>
      </Radio.Group>
      <ResizeWrap>
        <ShellEditor
          value={args}
          onChange={onArgsChange}
          {...restProps}
        />
      </ResizeWrap>
    </>
  )
}

export default BinShell;