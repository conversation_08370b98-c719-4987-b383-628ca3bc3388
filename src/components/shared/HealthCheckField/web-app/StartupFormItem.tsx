import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Form, Field, Switch, Radio, Input, NumberPicker, Button, Icon } from '@ali/cnd';
import { TableField, TextField } from '@ali/deep';

type StartupProps = {
  field: Field;
  value: {
    startupEnabled: boolean;
    initialDelaySeconds?: number;
    timeoutSeconds?: number;
    periodSeconds?: number;
    failureThreshold?: number;
    probeHandler: {
      [key: string]: {
        path?: string;
        port: number;
        httpHeaders?: {
          name: string;
          value: string;
        }[];
      };
    };
  };
};

type StartupState = {
  startupEnabled: boolean;
  showSeniorOption: boolean;
  isHttpCheck: boolean;
};

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 16, align: 'center' },
  labelCol: { span: 8, style: { width: 220, maxWidth: 220 } },
};

class StartupFormItem extends Component<StartupProps, StartupState> {
  constructor(props) {
    super(props);
    this.state = {
      startupEnabled: false,
      showSeniorOption: false,
      isHttpCheck: true,
    };
  }

  componentDidUpdate(prevProps, prevState) {
    const { field, value } = this.props;

    if (prevProps.value !== value) {
      const {
        startupEnabled,
        failureThreshold,
        initialDelaySeconds,
        periodSeconds,
        timeoutSeconds,
        probeHandler = {},
      } = value;

      this.setState({ startupEnabled });
      if (!startupEnabled) return;

      const _values = {
        startupEnabled,
        failureThreshold,
        initialDelaySeconds,
        periodSeconds,
        timeoutSeconds,
      };

      const _isHttpCheck = Reflect.has(probeHandler, 'httpGet');

      if (_isHttpCheck) {
        const { path, port, httpHeaders = [] } = probeHandler.httpGet || {};
        this.setState({ showSeniorOption: !!httpHeaders.length });
        Object.assign(_values, {
          path,
          port,
          checkType: 'httpGet',
          httpHeaders,
        });
      }
      if (Reflect.has(probeHandler, 'tcpSocket')) {
        const { port } = probeHandler.tcpSocket;
        Object.assign(_values, {
          port,
          checkType: 'tcpSocket',
        });
      }
      this.setState({
        isHttpCheck: _isHttpCheck,
      });
      field.setValues(_values);
    }
  }

  validation(value) {
    for (let item of value) {
      if (!item) {
        return intl('saenext.HealthCheckField.web-app.StartupFormItem.IncompleteAdvancedSettings');
      }
      const { name, value } = item;
      if (!name || !value) {
        return intl('saenext.HealthCheckField.web-app.StartupFormItem.IncompleteAdvancedSettings');
      }
    }
    return true;
  }

  render() {
    const { field } = this.props;
    const { init } = field;
    const { startupEnabled, showSeniorOption, isHttpCheck } = this.state;

    return (
      <>
        <Form.Item
          label={intl(
            'saenext.HealthCheckField.web-app.StartupFormItem.ApplicationStartupCheckStartupConfiguration',
          )}
          className="full24-width"
          {...(fieldLayout as any)}
          help={intl('saenext.HealthCheckField.web-app.StartupFormItem.WhenEnabledSaeHelpsYou')}
        >
          <Switch
            checked={startupEnabled}
            {...init('startupEnabled', {
              initValue: startupEnabled,
              props: {
                onChange: (value) => {
                  this.setState({ startupEnabled: value });
                },
              },
            })}
          />
        </Form.Item>
        {startupEnabled ? (
          <>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.StartupFormItem.InspectionMethod')}
              className="full24-width"
              {...(fieldLayout as any)}
            >
              <Radio.Group
                {...init('checkType', {
                  initValue: 'httpGet',
                  props: {
                    onChange: (value) => {
                      this.setState({ isHttpCheck: value === 'httpGet' });
                    },
                  },
                })}
              >
                <Radio
                  id="httpGet"
                  value="httpGet"
                  label={intl('saenext.HealthCheckField.web-app.StartupFormItem.HttpRequestCheck')}
                />
                <Radio
                  id="tcpSocket"
                  value="tcpSocket"
                  label={intl('saenext.HealthCheckField.web-app.StartupFormItem.TcpPortCheck')}
                />
              </Radio.Group>
            </Form.Item>
            {isHttpCheck ? (
              <Form.Item
                required
                label={intl('saenext.HealthCheckField.web-app.StartupFormItem.Path')}
                {...(fieldLayout as any)}
                className="full24-width"
              >
                <Input
                  {...init('path', {
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.HealthCheckField.web-app.StartupFormItem.EnterTheUrlPathOf',
                        ),
                      },
                    ],
                  })}
                  placeholder={intl(
                    'saenext.HealthCheckField.web-app.StartupFormItem.EnterTheUrlPathOf.1',
                  )}
                />
              </Form.Item>
            ) : null}

            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.StartupFormItem.Port')}
              className="full24-width"
              {...(fieldLayout as any)}
              help={
                isHttpCheck
                  ? intl(
                      'saenext.HealthCheckField.web-app.StartupFormItem.SaeAttemptsToEstablishAn',
                    )
                  : intl('saenext.HealthCheckField.web-app.StartupFormItem.SaeAttemptsToEstablishA')
              }
            >
              <NumberPicker
                {...init('port', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.StartupFormItem.EnterAPortRangingFrom',
                      ),
                    },
                  ],
                })}
                min={1}
                max={65535}
                placeholder={intl(
                  'saenext.HealthCheckField.web-app.StartupFormItem.EnterAPortInThe',
                )}
              />
            </Form.Item>

            {isHttpCheck ? (
              <Form.Item label=" " {...(fieldLayout as any)} className="full24-width">
                <Button
                  text
                  type="primary"
                  onClick={() => {
                    this.setState({
                      showSeniorOption: !showSeniorOption,
                    });
                  }}
                >
                  {showSeniorOption
                    ? intl(
                        'saenext.HealthCheckField.web-app.StartupFormItem.HideAdvancedSettingsHttpHeader',
                      )
                    : intl(
                        'saenext.HealthCheckField.web-app.StartupFormItem.DisplaysAdvancedSettingsHttpHeader',
                      )}
                </Button>
              </Form.Item>
            ) : null}

            {isHttpCheck && showSeniorOption ? (
              <TableField
                name="httpHeaders"
                label="HTTP Header"
                layout="TILED"
                showIndex={false}
                showSortable={false}
                className="full24-tiled"
                {...fieldLayout}
                minItems={1}
                addButtonText={intl('saenext.HealthCheckField.web-app.StartupFormItem.Add')}
                showDeleteConfirm={false}
                delButtonText={<Icon type="delete" />}
                help={intl(
                  'saenext.HealthCheckField.web-app.StartupFormItem.TheCustomHttpHeaderThat',
                )}
                validation={[
                  {
                    type: 'customValidate',
                    param: this.validation,
                  },
                ]}
              >
                {/* <div className="flex"> */}
                <TextField
                  name="name"
                  placeholder={intl('saenext.HealthCheckField.web-app.StartupFormItem.Variable')}
                />

                <TextField
                  name="value"
                  placeholder={intl('saenext.HealthCheckField.web-app.StartupFormItem.Value')}
                />

                {/* </div> */}
              </TableField>
            ) : null}

            <Form.Item
              required
              label={intl(
                'saenext.HealthCheckField.web-app.StartupFormItem.InitialDelayTimeSeconds',
              )}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl(
                'saenext.HealthCheckField.web-app.StartupFormItem.TheNumberOfSecondsDelayed',
              )}
            >
              <NumberPicker
                {...init('initialDelaySeconds', {
                  initValue: 0,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.StartupFormItem.EnterADelayTimeRanging',
                      ),
                    },
                  ],
                })}
                min={0}
                max={240}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.StartupFormItem.TimeoutPeriodSeconds')}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl('saenext.HealthCheckField.web-app.StartupFormItem.TheTimeoutPeriodOfThe')}
            >
              <NumberPicker
                {...init('timeoutSeconds', {
                  initValue: 1,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.StartupFormItem.PleaseEnterTheDetectionTimeout',
                      ),
                    },
                  ],
                })}
                min={1}
                max={240}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.StartupFormItem.CheckPeriodSeconds')}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl('saenext.HealthCheckField.web-app.StartupFormItem.TheFrequencyOfTheProbe')}
            >
              <NumberPicker
                {...init('periodSeconds', {
                  initValue: 10,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.StartupFormItem.PleaseEnterTheInspectionPeriod',
                      ),
                    },
                  ],
                })}
                min={1}
                max={240}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.StartupFormItem.FailureThresholdTimes')}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl(
                'saenext.HealthCheckField.web-app.StartupFormItem.TheDetectionFailsAtLeast',
              )}
            >
              <NumberPicker
                {...init('failureThreshold', {
                  initValue: 3,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.StartupFormItem.PleaseEnterTheFailureThreshold',
                      ),
                    },
                  ],
                })}
                min={1}
              />
            </Form.Item>
          </>
        ) : null}
      </>
    );
  }
}

export default StartupFormItem;
