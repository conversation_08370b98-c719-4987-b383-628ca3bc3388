import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Form, Field, Switch, Radio, Input, NumberPicker, Button, Icon } from '@ali/cnd';
import { TableField, TextField } from '@ali/deep';

type LivenessProps = {
  field: Field;
  value: {
    livenessEnabled: boolean;
    initialDelaySeconds?: number;
    timeoutSeconds?: number;
    periodSeconds?: number;
    failureThreshold?: number;
    probeHandler: {
      [key: string]: {
        path?: string;
        port: number;
        httpHeaders?: {
          name: string;
          value: string;
        }[];
      };
    };
  };
};

type LivenessState = {
  livenessEnabled: boolean;
  showSeniorOption: boolean;
};

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 16, align: 'center' },
  labelCol: { span: 8, style: { width: 220, maxWidth: 220 } },
};

class LivenessFormItem extends Component<LivenessProps, LivenessState> {
  constructor(props) {
    super(props);
    this.state = {
      livenessEnabled: false,
      showSeniorOption: false,
    };
  }

  componentDidUpdate(prevProps, prevState) {
    const { field, value } = this.props;
    if (prevProps.value !== value) {
      const {
        livenessEnabled,
        failureThreshold,
        initialDelaySeconds,
        periodSeconds,
        timeoutSeconds,
        probeHandler = {},
      } = value;

      this.setState({ livenessEnabled });

      if (!livenessEnabled) return;
      const _values = {
        livenessEnabled,
        checkType: 'httpGet',
        failureThreshold,
        initialDelaySeconds,
        periodSeconds,
        timeoutSeconds,
      };

      const { path, port, httpHeaders = [] } = probeHandler.httpGet || {};
      this.setState({ showSeniorOption: !!httpHeaders.length });
      Object.assign(_values, {
        path,
        port,
        httpHeaders,
      });
      field.setValues(_values);
    }
  }

  validation(value) {
    for (let item of value) {
      if (!item) {
        return intl('saenext.HealthCheckField.web-app.LivenessFormItem.IncompleteAdvancedSettings');
      }
      const { name, value } = item;
      if (!name || !value) {
        return intl('saenext.HealthCheckField.web-app.LivenessFormItem.IncompleteAdvancedSettings');
      }
    }
    return true;
  }

  render() {
    const { field } = this.props;
    const { init } = field;
    const { livenessEnabled, showSeniorOption } = this.state;

    return (
      <>
        <Form.Item
          label={intl(
            'saenext.HealthCheckField.web-app.LivenessFormItem.ApplicationInstanceSurvivalCheckLiveness',
          )}
          className="full24-width"
          {...(fieldLayout as any)}
          help={intl(
            'saenext.HealthCheckField.web-app.LivenessFormItem.WhenEnabledSaeChecksWhether',
          )}
        >
          <Switch
            disabled={true}
            checked={livenessEnabled}
            {...init('livenessEnabled', {
              initValue: livenessEnabled,
              props: {
                onChange: (value) => {
                  this.setState({ livenessEnabled: value });
                },
              },
            })}
          />
        </Form.Item>
        {livenessEnabled ? (
          <>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.LivenessFormItem.InspectionMethod')}
              className="full24-width"
              {...(fieldLayout as any)}
            >
              <Radio.Group
                {...init('checkType', {
                  initValue: 'httpGet',
                })}
              >
                <Radio
                  id="httpGet"
                  value="httpGet"
                  label={intl('saenext.HealthCheckField.web-app.LivenessFormItem.HttpRequestCheck')}
                />
              </Radio.Group>
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.LivenessFormItem.Path')}
              {...(fieldLayout as any)}
              className="full24-width"
            >
              <Input
                {...init('path', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.LivenessFormItem.EnterTheUrlPathOf',
                      ),
                    },
                  ],
                })}
                placeholder={intl(
                  'saenext.HealthCheckField.web-app.LivenessFormItem.EnterTheUrlPathOf.1',
                )}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.LivenessFormItem.Port')}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl(
                'saenext.HealthCheckField.web-app.LivenessFormItem.SaeAttemptsToEstablishAn',
              )}
            >
              <NumberPicker
                {...init('port', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.LivenessFormItem.EnterAPortRangingFrom',
                      ),
                    },
                  ],
                })}
                min={0}
                max={65535}
                placeholder={intl(
                  'saenext.HealthCheckField.web-app.LivenessFormItem.EnterAPortInThe',
                )}
              />
            </Form.Item>
            <Form.Item label=" " {...(fieldLayout as any)} className="full24-width">
              <Button
                text
                type="primary"
                onClick={() => {
                  this.setState({
                    showSeniorOption: !showSeniorOption,
                  });
                }}
              >
                {showSeniorOption
                  ? intl(
                      'saenext.HealthCheckField.web-app.LivenessFormItem.HideAdvancedSettingsHttpHeader',
                    )
                  : intl(
                      'saenext.HealthCheckField.web-app.LivenessFormItem.DisplaysAdvancedSettingsHttpHeader',
                    )}
              </Button>
            </Form.Item>
            {showSeniorOption ? (
              <TableField
                name="httpHeaders"
                label="HTTP Header"
                layout="TILED"
                showIndex={false}
                showSortable={false}
                {...fieldLayout}
                minItems={1}
                addButtonText={intl('saenext.HealthCheckField.web-app.LivenessFormItem.Add')}
                className="full24-tiled"
                showDeleteConfirm={false}
                delButtonText={<Icon type="delete" />}
                help={intl(
                  'saenext.HealthCheckField.web-app.LivenessFormItem.TheCustomHttpHeaderThat',
                )}
                validation={[
                  {
                    type: 'customValidate',
                    param: this.validation,
                  },
                ]}
              >
                <TextField
                  name="name"
                  placeholder={intl('saenext.HealthCheckField.web-app.LivenessFormItem.Variable')}
                />

                <TextField
                  name="value"
                  placeholder={intl('saenext.HealthCheckField.web-app.LivenessFormItem.Value')}
                />
              </TableField>
            ) : null}

            <Form.Item
              required
              label={intl(
                'saenext.HealthCheckField.web-app.LivenessFormItem.InitialDelayTimeSeconds',
              )}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl(
                'saenext.HealthCheckField.web-app.LivenessFormItem.TheNumberOfSecondsDelayed',
              )}
            >
              <NumberPicker
                {...init('initialDelaySeconds', {
                  initValue: 0,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.LivenessFormItem.EnterADelayTimeRanging',
                      ),
                    },
                  ],
                })}
                min={0}
                max={240}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.LivenessFormItem.TimeoutPeriodSeconds')}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl('saenext.HealthCheckField.web-app.LivenessFormItem.TheTimeoutPeriodOfThe')}
            >
              <NumberPicker
                {...init('timeoutSeconds', {
                  initValue: 1,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.LivenessFormItem.PleaseEnterTheDetectionTimeout',
                      ),
                    },
                  ],
                })}
                min={1}
                max={240}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.HealthCheckField.web-app.LivenessFormItem.CheckPeriodSeconds')}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl(
                'saenext.HealthCheckField.web-app.LivenessFormItem.TheFrequencyOfTheProbe',
              )}
            >
              <NumberPicker
                {...init('periodSeconds', {
                  initValue: 10,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.LivenessFormItem.PleaseEnterTheInspectionPeriod',
                      ),
                    },
                  ],
                })}
                min={1}
                max={240}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl(
                'saenext.HealthCheckField.web-app.LivenessFormItem.FailureThresholdTimes',
              )}
              className="full24-width"
              {...(fieldLayout as any)}
              help={intl(
                'saenext.HealthCheckField.web-app.LivenessFormItem.TheDetectionFailsAtLeast',
              )}
            >
              <NumberPicker
                {...init('failureThreshold', {
                  initValue: 3,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.HealthCheckField.web-app.LivenessFormItem.PleaseEnterTheFailureThreshold',
                      ),
                    },
                  ],
                })}
                min={1}
              />
            </Form.Item>
          </>
        ) : null}
      </>
    );
  }
}

export default LivenessFormItem;
