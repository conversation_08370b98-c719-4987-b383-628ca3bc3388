import React, { Component } from 'react';
import { Form, Field } from '@ali/cnd';
import { isEmpty } from 'lodash';
import StartupFormItem from './StartupFormItem';
// @ts-ignore
import LivenessFormItem from './LivenessFormItem';

type Props = {
  value: any;
  isUpdate: boolean;
  onChange: (value: any) => void;
};
type State = {
  healthCheck: {
    startupProbe: {};
    livenessProbe: {};
  };
  startupValues: {};
  livenessValues: {};
};

type StartupValue = {
  startupEnabled: boolean;
  checkType: 'httpGet' | 'tcpSocket';
  path: string;
  port: number;
  httpHeaders?: { name: string; value: string }[];
  initialDelaySeconds: number;
  timeoutSeconds: number;
  periodSeconds: number;
  failureThreshold: number;
};

type LivenessValue = {
  livenessEnabled: boolean;
  checkType: 'httpGet';
  path: string;
  port: number;
  httpHeaders?: { name: string; value: string }[];
  initialDelaySeconds: number;
  timeoutSeconds: number;
  periodSeconds: number;
  failureThreshold: number;
};

class HealthCheck extends Component<Props, State> {

  startupField = new Field(this, {
    onChange: () => {
      this.changStartupProbe();
    }
  })

  livenessField = new Field(this, {
    onChange: () => {
      this.changeLivenessProbe();
    }
  })

  constructor(props) {
    super(props);
    const { value, isUpdate, onChange } = props;
    this.state = {
      healthCheck: {
        startupProbe: isUpdate ? {} : null,
        livenessProbe: isUpdate ? {} : null,
      },
      startupValues: {},
      livenessValues: {},
    };
    onChange && onChange(this.state.healthCheck);
    if (!isEmpty(value)) this.initStates();
  }

  componentDidUpdate(prevProps, prevState) {
    const { value } = this.props
    if (prevProps.value !== value) {
      // 重新赋值
      this.initStates();
    }
  }

  initStates() {
    const { value } = this.props;
    const { startupProbe = {}, livenessProbe = {} } = value;
    const startupEnabled = !!(Object.keys(startupProbe || {}).length);
    const livenessEnabled = !!(Object.keys(livenessProbe || {}).length);

    this.setState({
      healthCheck: {
        startupProbe,
        livenessProbe,
      },
      startupValues: {
        startupEnabled,
        ...startupProbe,
      },
      livenessValues: {
        livenessEnabled,
        ...livenessProbe,
      }
    });
    

  }

  validate = () => {

    let startupValidate = false;
    this.startupField.validate((error, value) => {
      startupValidate = error ? false : true
    })

    let livenessValidate = false;
    this.livenessField.validate((error, value) => {
      livenessValidate = error ? false : true
    })
    return startupValidate && livenessValidate;
  }

  changStartupProbe() {
    const { validate, getValue } = this.startupField;
    const { isUpdate, onChange } = this.props;
    const { healthCheck } = this.state;

    let _healthCheck = {
      ...healthCheck,
      startupProbe: isUpdate ? {} : null,
    };
    // 当开关关闭的时候 不会触发下validate
    const _startupEnabled = getValue('startupEnabled');
    if (!_startupEnabled) {
      onChange(_healthCheck);
      this.setState({
        healthCheck: _healthCheck
      });
      return;
    };

    validate(async (error, values) => {
      if (error) return;
      
      const { startupEnabled } = values as unknown as StartupValue;
     
      if (startupEnabled) {
        const { 
          checkType = 'httpGet',
          path,
          port,
          initialDelaySeconds = 0,
          timeoutSeconds = 1,
          periodSeconds = 10,
          failureThreshold = 3,
        } = values as unknown as StartupValue;
        const _startupProbe = {
          initialDelaySeconds,
          timeoutSeconds,
          periodSeconds,
          failureThreshold,
        }
        if (checkType === 'httpGet') {
          // @ts-ignore
          const { httpHeaders = [] } = values;
          // const _httpHeaders = [];
          // httpHeaders && httpHeaders.forEach(item => {
          //   const { name, value } = item || {};
          //   if (name && value) {
          //     _httpHeaders.push({ name, value });
          //   }
          // });
          Object.assign(_startupProbe, {
            probeHandler: {
              httpGet: {
                path,
                port,
                httpHeaders
              }
            }
          })
        }
        if (checkType === 'tcpSocket') {
          Object.assign(_startupProbe, {
            probeHandler: {
              tcpSocket: { port },
            }
          })
        }
        _healthCheck = {
          ...healthCheck,
          startupProbe: _startupProbe,
        }
      }
      onChange(_healthCheck);
      this.setState({
        healthCheck: _healthCheck
      });
    });
  }

  changeLivenessProbe() {
    const { validate, getValue } = this.livenessField;
    const { isUpdate, onChange } = this.props;
    const { healthCheck } = this.state;

    let _healthCheck = {
      ...healthCheck,
      livenessProbe: isUpdate ? {} : null,
    };
    // 当开关关闭的时候 不会触发下validate
    const _livenessEnabled = getValue('livenessEnabled');
    if (!_livenessEnabled) {
      onChange(_healthCheck);
      this.setState({
        healthCheck: _healthCheck
      });
      return;
    };

    validate(async (error, values) => {
      
      if (error) return;
      
      const { livenessEnabled } = values as unknown as LivenessValue;
      if (livenessEnabled) {
        const { 
          checkType = 'httpGet',
          path,
          port,
          initialDelaySeconds = 0,
          timeoutSeconds = 1,
          periodSeconds = 10,
          failureThreshold = 3,
        } = values as unknown as LivenessValue;
        const _livenessProbe = {
          initialDelaySeconds,
          timeoutSeconds,
          periodSeconds,
          failureThreshold,
        }
        if (checkType === 'httpGet') {
          // @ts-ignore
          const { httpHeaders = [] } = values;
          // const _httpHeaders = [];
          // httpHeaders && httpHeaders.forEach(item => {
          //   const { name, value } = item || {};
          //   if (name && value) {
          //     _httpHeaders.push({ name, value });
          //   }
          // });
          Object.assign(_livenessProbe, {
            probeHandler: {
              httpGet: {
                path,
                port,
                httpHeaders
              }
            }
          })
        }
        _healthCheck = {
          ...healthCheck,
          livenessProbe: _livenessProbe,
        }
      }
      onChange(_healthCheck);
      this.setState({
        healthCheck: _healthCheck
      });
    });
  }


  render() {
    // @ts-ignore
    const { startupValues, livenessValues } = this.state;
    return (
      <>
        <Form field={this.startupField}>
          <StartupFormItem 
            // @ts-ignore
            value={startupValues}
            field={this.startupField}
          />
        </Form>
        {/* <div className='border-t mt-l mb-l' />
        <Form field={this.livenessField}>
          <LivenessFormItem
            // @ts-ignore
            value={livenessValues}
            field={this.livenessField}
          />
        </Form> */}
      </>
    );
  }

}

export default HealthCheck;

