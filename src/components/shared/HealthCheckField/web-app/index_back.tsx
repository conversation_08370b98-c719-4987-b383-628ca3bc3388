import React from 'react';
import BaseField from '@ali/deep-form-helper';
import HealthCheck from './HealthCheck_back';
import { ConfigProvider } from '@ali/deep';

class HealthCheckField extends BaseField {
  props;
  static displayName = 'HealthCheckField';

  getProps() {
    return {
      ...this.props
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...fieldProps,
      ...this.props,
    };

    return <HealthCheck {...newProps} />;
  }
}

export default ConfigProvider.config(HealthCheckField as any)