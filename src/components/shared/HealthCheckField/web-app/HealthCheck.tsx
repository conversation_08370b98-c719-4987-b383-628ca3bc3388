import React, { Component } from 'react';
import Form from '@ali/deep-form';
import { intl } from '@ali/xconsole';
import { TextField, SwitchField, NumberField } from '@ali/deep';
import cls from 'classnames';
import _ from 'lodash';
import { ColProps } from '@ali/deep/types/grid';

type Align = 'left' | 'top' | 'inset';
type TextAlign = 'left' | 'right';
type ColAlign = 'top'| 'center'| 'bottom'| 'baseline'| 'stretch';

const fieldLayout = {
  labelAlign: 'left' as Align,
  labelTextAlign: 'left' as TextAlign,
  wrapperCol: { span: 17, align: 'center' as Align } as ColProps,
  labelCol: { span: 7, style: { width: 170, maxWidth: 170 } },
};

interface IHealthCheckProps {
  value?: {
    enableHealthCheck: boolean;
    httpGetUrl?: string;
    initialDelaySeconds?: number;
    periodSeconds?: number;
    timeoutSeconds?: number;
    failureThreshold?: number;
    successThreshold?: number;
  };
  onChange?: Function;
}

interface IHealthCheckState {
  // enableHealthCheck: boolean;
  refreshIndex: number;
}

class AppHealthCheck extends Component<IHealthCheckProps, IHealthCheckState> {
  private refField = React.createRef() as any;
  constructor(props) {
    super(props);
    this.state = {
      refreshIndex: 0,
    };
  }

  componentDidMount(): void {
    const { value = {} as any } = this.props;
    this.refField.setValue(value);

    //enableHealthCheck被set为true后，刷新视图显示其他表单项
    setTimeout(() => {
      this.setState({
        refreshIndex: this.state.refreshIndex + 1,
      });
    }, 100);
  }

  componentWillReceiveProps(nextProps: Readonly<IHealthCheckProps>, nextContext: any): void {
    const { value = {} } = nextProps;
    this.refField.setValue(value);
  }

  onHealthChange = () => {
    const { onChange } = this.props;
    const value = this.refField.getValue();
    if (value.enableHealthCheck) {
      onChange?.(value);
    } else {
      onChange?.({
        enableHealthCheck: false,
        httpGetUrl: '',
        initialDelaySeconds: null,
        periodSeconds: null,
        timeoutSeconds: null,
        failureThreshold: null,
        successThreshold: null,
      });
    }
  };

  validate = () => {
    let validateRes = false;
    this.refField.validate((error, value) => {
      validateRes = error ? false : true;
    });
    return validateRes;
  };

  render() {
    const { enableHealthCheck = false } = this.refField.getValue?.() || {};

    return (
      <Form
        ref={(c) => {
          if (c) {
            this.refField = c.getInstance();
          }
        }}
      >
        <SwitchField
          name="enableHealthCheck"
          label={intl('function.detail.healthcheck.label')}
          {...fieldLayout}
          onChange={this.onHealthChange}
        />

        {enableHealthCheck ? (
          <>
            <TextField
              required
              name="httpGetUrl"
              label={intl('function.detail.healthcheck.httpGetURL.label')}
              {...fieldLayout}
              placeholder={intl('function.detail.healthcheck.httpGetURL.placeholder')}
              onChange={this.onHealthChange}
              validation={[
                {
                  type: 'required',
                  message: intl('saenext.HealthCheckField.web-app.HealthCheck.EnterTheRequestPath'),
                },
              ]}
            />

            <NumberField
              required
              step={5}
              min={0}
              max={120}
              className={cls({ 'full24-width': true })}
              name="initialDelaySeconds"
              onChange={this.onHealthChange}
              label={intl('function.detail.healthcheck.initialDelaySeconds.label')}
              {...fieldLayout}
              innerAfter={
                <span>{intl('saenext.HealthCheckField.web-app.HealthCheck.Seconds')}</span>
              }
              placeholder={
                intl('general.placeholder', {
                  name: intl('function.detail.healthcheck.initialDelaySeconds.label'),
                }) as string
              }
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.HealthCheckField.web-app.HealthCheck.PleaseEnterTheFirstDetection',
                  ),
                },
              ]}
            />

            <NumberField
              required
              step={5}
              min={1}
              max={120}
              name="periodSeconds"
              onChange={this.onHealthChange}
              className={cls({ 'full24-width': true })}
              label={intl('function.detail.healthcheck.periodSeconds.label')}
              {...fieldLayout}
              innerAfter={
                <span>{intl('saenext.HealthCheckField.web-app.HealthCheck.Seconds')}</span>
              }
              placeholder={
                intl('general.placeholder', {
                  name: intl('function.detail.healthcheck.periodSeconds.label'),
                }) as string
              }
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.HealthCheckField.web-app.HealthCheck.PleaseEnterTheDetectionInterval',
                  ),
                },
              ]}
            />

            <NumberField
              required
              max={3}
              min={1}
              name="timeoutSeconds"
              onChange={this.onHealthChange}
              className={cls({ 'full24-width': true })}
              label={intl('function.detail.healthcheck.timeoutSeconds.label')}
              {...fieldLayout}
              innerAfter={
                <span>{intl('saenext.HealthCheckField.web-app.HealthCheck.Seconds')}</span>
              }
              placeholder={
                intl('general.placeholder', {
                  name: intl('function.detail.healthcheck.timeoutSeconds.label'),
                }) as string
              }
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.HealthCheckField.web-app.HealthCheck.EnterTheTimeoutPeriodFor',
                  ),
                },
              ]}
            />

            <NumberField
              required
              step={5}
              min={1}
              max={120}
              name="failureThreshold"
              onChange={this.onHealthChange}
              className={cls({ 'full24-width': true })}
              label={intl('function.detail.healthcheck.failureThreshold.label')}
              {...fieldLayout}
              innerAfter={<span>{intl('saenext.HealthCheckField.web-app.HealthCheck.Times')}</span>}
              placeholder={
                intl('general.placeholder', {
                  name: intl('function.detail.healthcheck.failureThreshold.label'),
                }) as string
              }
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.HealthCheckField.web-app.HealthCheck.PleaseEnterTheMaximumNumber',
                  ),
                },
              ]}
            />

            <NumberField
              required
              step={5}
              min={1}
              max={120}
              name="successThreshold"
              onChange={this.onHealthChange}
              className={cls({ 'full24-width': true })}
              label={intl('function.detail.healthcheck.successThreshold.label')}
              {...fieldLayout}
              innerAfter={<span>{intl('saenext.HealthCheckField.web-app.HealthCheck.Times')}</span>}
              placeholder={
                intl('general.required', {
                  name: intl('function.detail.healthcheck.successThreshold.label'),
                }) as string
              }
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.HealthCheckField.web-app.HealthCheck.EnterTheThresholdForSuccessful',
                  ),
                },
              ]}
            />
          </>
        ) : null}
      </Form>
    );
  }
}

export default AppHealthCheck;
