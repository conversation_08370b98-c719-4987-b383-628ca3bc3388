import { intl } from '@ali/cnd';
import { Tab } from '@ali/cnd';
import React from 'react';

const ExampleCommand = () => {
  return (
    <div>
      <h3 style={{ margin: '0 10px' }}>
        {intl('saenext.HealthCheckField.micro-app.ExampleCommand.ExampleOfExecutingACommand')}
      </h3>
      <Tab shape="wrapped">
        <Tab.Item title={intl('saenext.HealthCheckField.micro-app.ExampleCommand.BashMode')}>
          <div style={{ padding: '10px 10px 5px', backgroundColor: '#fff', display: 'flex' }}>
            <div style={{ minWidth: 80 }}>
              <p>{intl('saenext.HealthCheckField.micro-app.ExampleCommand.ExecutableCommands')}</p>
              <p>{intl('saenext.HealthCheckField.micro-app.ExampleCommand.Parameters')}</p>
              <p>{intl('saenext.HealthCheckField.micro-app.ExampleCommand.Parameters')}</p>
            </div>
            <div
              style={{
                border: '1px solid #ccc',
                padding: '0 16px 16px 16px',
                marginLeft: 8,
                minWidth: 400,
              }}
            >
              <p>/bin/sh</p>
              <p>-c</p>
              <p>cat /tmp/test.log</p>
            </div>
          </div>
        </Tab.Item>
        <Tab.Item title={intl('saenext.HealthCheckField.micro-app.ExampleCommand.BinaryMode')}>
          <div style={{ padding: '10px 10px 5px', backgroundColor: '#fff', display: 'flex' }}>
            <div style={{ minWidth: 80 }}>
              <p>{intl('saenext.HealthCheckField.micro-app.ExampleCommand.ExecutableCommands')}</p>
              <p>{intl('saenext.HealthCheckField.micro-app.ExampleCommand.Parameters')}</p>
            </div>
            <div
              style={{
                border: '1px solid #ccc',
                padding: '0 16px 16px 16px',
                marginLeft: 8,
                minWidth: 400,
              }}
            >
              <p>/run/start</p>
              <p>--port=8080</p>
            </div>
          </div>
        </Tab.Item>
      </Tab>
      <p style={{ paddingLeft: 8 }}>
        {intl('saenext.HealthCheckField.micro-app.ExampleCommand.TheFirstBehaviorCommandThe')}
      </p>
    </div>
  );
};

export default ExampleCommand;
