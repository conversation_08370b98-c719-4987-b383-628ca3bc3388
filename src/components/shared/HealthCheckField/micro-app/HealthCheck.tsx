import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import {
  Copy,
  Field,
  Form,
  Icon,
  Input,
  NumberPicker,
  Radio,
  Select,
  Switch,
  Button,
  ToolTipCondition,
  CndSwitch,
  Message,
} from '@ali/cnd';
import If from '~/components/shared/If';
import _, { isEmpty, isEqual } from 'lodash';
import SimpleCollapse from '../../SimpleCollapse';
import CommandShell from '../../CommandShell';
import CachedData from '~/cache/common';

const { Group: RadioGroup } = Radio;

const formItemLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const httpTypeList = [
  {
    value: 'HTTP',
    label: 'HTTP',
  },

  {
    value: 'HTTPS',
    label: 'HTTPS',
  },
];

const healthText = {
  Liveness: {
    label: intl(
      'saenext.HealthCheckField.micro-app.HealthCheck.EnableApplicationInstanceSurvivalCheck',
    ),

    help: intl('saenext.HealthCheckField.micro-app.HealthCheck.WhenEnabledSaeChecksWhether'),
    delayHelp: intl('saenext.HealthCheckField.micro-app.HealthCheck.TheDelayTimeMustBe'),
  },

  Readiness: {
    label: intl(
      'saenext.HealthCheckField.micro-app.HealthCheck.EnableApplicationReadinessCheckReadiness',
    ),

    help: intl('saenext.HealthCheckField.micro-app.HealthCheck.WhenEnabledSaeChecksWhether.1'),
    delayHelp: intl('saenext.HealthCheckField.micro-app.HealthCheck.TheDelayTimeMustBe.2'),
  },

  StartupProbe: {
    label: intl(
      'saenext.HealthCheckField.micro-app.HealthCheck.EnableApplicationStartupDetectionStartupprobe',
    ),
    help: '',
    delayHelp: intl('saenext.HealthCheckField.micro-app.HealthCheck.TheDelayTimeMustBe.3'),
  },
};

interface IProps {
  value?: Object;
  onChange?: Function;
  activeKey: string;
  NewSaeVersion: string;
  microserviceEngineConfig?: string;
  isPreview?: boolean;
  hasMicroService: boolean;
  appConfig?: Object;
  clearReadinessConfirm?: boolean;
}

interface IState {
  useHealth: boolean;
  checkType: string;
  showSeniorOption: boolean;
  disabled: boolean;
  delayTime: number;
  pathOrPortDisabled: boolean;
}

class HealthCheck extends Component<IProps, IState> {
  private field = React.createRef() as any;
  constructor(props) {
    super(props);
    this.state = {
      useHealth: false,
      checkType: 'httpGet',
      showSeniorOption: false,
      disabled: false,
      delayTime: 0,
      pathOrPortDisabled: false,
    };

    this.field = new Field(this, {
      onChange: this.handleFormChange,
      parseName: true,
    });
  }

  componentDidMount() {
    const { value = {}, NewSaeVersion, microserviceEngineConfig, hasMicroService } = this.props;
    if (hasMicroService && microserviceEngineConfig) {
      this.handleMicroServiceInfo(microserviceEngineConfig);
    }
    this.initData(value);
  }

  componentWillReceiveProps(nextProps, nextState: any): void {
    const { NewSaeVersion, microserviceEngineConfig, hasMicroService } = nextProps;
    if (
      !isEqual(NewSaeVersion, this.props.NewSaeVersion) ||
      !isEqual(microserviceEngineConfig, this.props.microserviceEngineConfig)
    ) {
      if (hasMicroService) {
        this.handleMicroServiceInfo(microserviceEngineConfig);
      } else {
        this.setState({
          disabled: false,
          pathOrPortDisabled: false,
        });
      }
    }
    if (!isEqual(nextProps.value, this.props.value)) {
      this.initData(nextProps.value);
    }
  }

  handleMicroServiceInfo = (info) => {
    const { activeKey, appConfig, clearReadinessConfirm } = this.props;
    const config = (info && JSON.parse(info)) || {};
    if (
      _.get(config, 'Enable', false) &&
      _.get(config, 'MseLosslessRule.enable', false) &&
      (_.get(config, 'delayEnable', false) || !_.has(config, 'delayEnable')) // 兼容数据回显时被覆盖问题
    ) {
      if (_.get(config, 'MseLosslessRule.delayTime', 0) > 0) {
        // 延时注册时长大于0
        if (activeKey === 'Readiness') {
          this.setState(
            {
              disabled: true,
              pathOrPortDisabled:
                this.field.getValue('httpGet.path') && this.field.getValue('httpGet.port')
                  ? true
                  : false,
              useHealth: true,
              delayTime: _.get(config, 'MseLosslessRule.delayTime', 0),
            },
            () => {
              this.field.validate();
            },
          );
        }
        if (activeKey === 'Liveness') {
          this.setState(
            {
              delayTime: _.get(config, 'MseLosslessRule.delayTime', 0),
            },
            () => {
              this.field.validate();
            },
          );
        }
      }
    } else {
      if (activeKey === 'Readiness') {
        this.setState(
          {
            disabled: false,
            pathOrPortDisabled: false,
            delayTime: 0,
            // @ts-ignore
            useHealth: clearReadinessConfirm ? false : appConfig?.Readiness ? true : false,
          },
          () => {
            this.field.validate();
          },
        );
      }
      if (activeKey === 'Liveness') {
        this.setState(
          {
            delayTime: 0,
          },
          () => {
            this.field.validate();
          },
        );
      }
    }
  };

  initData = (value) => {
    if (isEmpty(value)) return;

    const checkType = _.get(value, 'httpGet')
      ? 'httpGet'
      : _.get(value, 'tcpSocket')
      ? 'tcpSocket'
      : 'exec';

    this.setState({
      useHealth: true,
      checkType,
    });

    const keyWord = _.get(value, 'httpGet.keyWord');
    if (keyWord) {
      this.setState({
        showSeniorOption: true,
      });
    }
    this.field.setValues(value);
  };

  handleSwitchChange = (val) => {
    const { onChange } = this.props;
    this.setState(
      {
        useHealth: val,
      },
      () => {
        !val && onChange({});
      },
    );
  };

  handleFormChange = () => {
    const { onChange } = this.props;
    const { getValues } = this.field;
    const params = getValues();
    onChange && onChange(params);
  };

  handleCheckTypeChange = (value) => {
    this.setState(
      {
        checkType: value,
      },

      () => {
        if (value !== 'exec') {
          this.field.remove(['exec.command', 'exec']);
        }
        this.field.reset();
      },
    );
  };

  validate = () => {
    const { useHealth } = this.state;
    return new Promise((resolve, reject) => {
      if (!useHealth) {
        resolve(true);
        return;
      }
      this.field.validate((error, value) => {
        error
          ? resolve(
              intl(
                'saenext.HealthCheckField.micro-app.HealthCheck.CompleteTheHealthCheckConfiguration',
              ),
            )
          : resolve(true);
      });
    });
  };

  render() {
    const { activeKey, NewSaeVersion = '', isPreview = false, hasMicroService } = this.props;
    const {
      useHealth = false,
      checkType,
      showSeniorOption,
      disabled,
      delayTime,
      pathOrPortDisabled,
    } = this.state;
    const { exec = {}, httpGet = {} } = this.field.getValues();
    const { command = '' } = exec;
    const { init } = this.field;
    const tipText = httpGet.isContainKeyWord
      ? intl('saenext.HealthCheckField.micro-app.HealthCheck.Include')
      : intl('saenext.HealthCheckField.micro-app.HealthCheck.NotIncluded');
    return (
      <div className="pt-l">
        <Form.Item
          label={healthText[activeKey].label}
          help={healthText[activeKey].help}
          {...(formItemLayout as any)}
        >
          {!isPreview ? (
            <ToolTipCondition
              show={disabled}
              tip={intl(
                'saenext.HealthCheckField.micro-app.HealthCheck.LosslessLaunchDelayedRegistrationIs',
              )}
            >
              <CndSwitch
                checked={useHealth}
                onChange={this.handleSwitchChange}
                disabled={disabled}
              />
            </ToolTipCondition>
          ) : useHealth ? (
            intl('saenext.HealthCheckField.micro-app.HealthCheck.Enabled')
          ) : (
            intl('saenext.HealthCheckField.micro-app.HealthCheck.NotEnabled')
          )}
        </Form.Item>
        <If condition={useHealth}>
          {activeKey === 'Readiness' && delayTime > 0 && (
            <Message type="notice">
              {intl(
                'saenext.HealthCheckField.micro-app.HealthCheck.IfTheRegistrationDurationOf.new',
              )}
            </Message>
          )}
          <div className="flex flex-start">
            <div className="mr-xl">
              <Form {...(formItemLayout as any)} field={this.field}>
                <Form.Item
                  label={intl('saenext.HealthCheckField.micro-app.HealthCheck.InspectionMethod')}
                  className="full24-width"
                >
                  <Radio.Group
                    value={checkType || 'httpGet'}
                    onChange={this.handleCheckTypeChange}
                    disabled={disabled}
                  >
                    <Radio
                      id="httpGet"
                      value="httpGet"
                      label={intl(
                        'saenext.HealthCheckField.micro-app.HealthCheck.HttpRequestCheck',
                      )}
                    />

                    <ToolTipCondition
                      show={activeKey === 'Readiness' && disabled}
                      tip={intl('saenext.HealthCheckField.micro-app.HealthCheck.ToUseTcpPortCheck')}
                      className="mr-xl"
                    >
                      <Radio
                        id="tcpSocket"
                        value="tcpSocket"
                        label={
                          intl('saenext.HealthCheckField.micro-app.HealthCheck.TcpPortCheck') +
                          (activeKey === 'Liveness'
                            ? intl('saenext.HealthCheckField.micro-app.HealthCheck.Recommend')
                            : '')
                        }
                      />
                    </ToolTipCondition>

                    <ToolTipCondition
                      show={activeKey === 'Readiness' && disabled}
                      tip={intl(
                        'saenext.HealthCheckField.micro-app.HealthCheck.ToRunCommandChecksYou',
                      )}
                    >
                      <Radio
                        id="exec"
                        value="exec"
                        label={intl(
                          'saenext.HealthCheckField.micro-app.HealthCheck.RunCommandCheck',
                        )}
                      />
                    </ToolTipCondition>
                  </Radio.Group>
                </Form.Item>
                <If condition={checkType === 'httpGet'}>
                  <Form.Item
                    label={intl('saenext.HealthCheckField.micro-app.HealthCheck.Path')}
                    required
                    requiredTrigger="submit"
                    requiredMessage={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.PleaseFillInThePath',
                    )}
                    className="full24-width"
                    {...(formItemLayout as any)}
                    validator={(rule, value, callback: any) => {
                      if (activeKey === 'Liveness' && hasMicroService && delayTime > 0) {
                        const port = this.field.getValues().httpGet?.port;
                        if (value === '/readiness' && port === 54199) {
                          callback(
                            intl(
                              'saenext.HealthCheckField.micro-app.HealthCheck.LosslessLaunchDelayedRegistrationIs.2.new',
                            ),
                          );
                        } else {
                          callback();
                        }
                      } else {
                        callback();
                      }
                    }}
                  >
                    <Input
                      name="httpGet.path"
                      placeholder={intl(
                        'saenext.HealthCheckField.micro-app.HealthCheck.EnterTheUrlPathOf',
                      )}
                      disabled={pathOrPortDisabled}
                    />
                  </Form.Item>
                  <Form.Item
                    label={intl('saenext.HealthCheckField.micro-app.HealthCheck.Port')}
                    required
                    requiredTrigger="submit"
                    requiredMessage={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.EnterThePort',
                    )}
                    className="full24-width"
                    {...(formItemLayout as any)}
                    validator={(rule, value, callback: any) => {
                      if (activeKey === 'Liveness' && hasMicroService && delayTime > 0) {
                        const path = this.field.getValues().httpGet?.path;
                        if (path === '/readiness' && value === 54199) {
                          callback(
                            intl(
                              'saenext.HealthCheckField.micro-app.HealthCheck.LosslessLaunchDelayedRegistrationIs.2.new',
                            ),
                          );
                        } else {
                          callback();
                        }
                      } else {
                        callback();
                      }
                    }}
                  >
                    <NumberPicker
                      // @ts-ignore
                      name="httpGet.port"
                      min={0}
                      placeholder={intl(
                        'saenext.HealthCheckField.micro-app.HealthCheck.EnterTheRequestedPort',
                      )}
                      disabled={pathOrPortDisabled}
                    />
                  </Form.Item>

                  <SimpleCollapse
                    text={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.HttpAdvancedSettings',
                    )}
                    defaultOpen={showSeniorOption}
                    lazyLoad={false}
                  >
                    <Form.Item
                      label={intl(
                        'saenext.HealthCheckField.micro-app.HealthCheck.ReturnContentKeywords',
                      )}
                      className="full24-width"
                      {...(formItemLayout as any)}
                    >
                      <div className="flex">
                        <Form.Item>
                          <Select hasClear name="httpGet.isContainKeyWord" style={{ width: 140 }}>
                            <Select.Option value={true}>
                              {intl('saenext.HealthCheckField.micro-app.HealthCheck.Include')}
                            </Select.Option>
                            <Select.Option value={false}>
                              {intl('saenext.HealthCheckField.micro-app.HealthCheck.NotIncluded')}
                            </Select.Option>
                          </Select>
                        </Form.Item>
                        <Form.Item style={{ flex: 1 }}>
                          <Input
                            name="httpGet.keyWord"
                            defaultValue=""
                            placeholder={
                              intl(
                                'saenext.HealthCheckField.micro-app.HealthCheck.EnterTheKeywordOfThe',
                                { tipText: tipText },
                              ) as string
                            }
                          />
                        </Form.Item>
                      </div>
                    </Form.Item>
                  </SimpleCollapse>

                  <Form.Item
                    className="full24-width"
                    label={intl('saenext.HealthCheckField.micro-app.HealthCheck.Agreement')}
                    {...(formItemLayout as any)}
                  >
                    <RadioGroup
                      name="httpGet.scheme"
                      dataSource={httpTypeList}
                      defaultValue={'HTTP'}
                    />
                  </Form.Item>
                </If>
                <If condition={checkType === 'tcpSocket'}>
                  <Form.Item
                    label={intl('saenext.HealthCheckField.micro-app.HealthCheck.TcpPort')}
                    required
                    requiredTrigger="submit"
                    requiredMessage={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.EnterTheTcpPort',
                    )}
                    className="full24-width"
                    {...(formItemLayout as any)}
                  >
                    <NumberPicker
                      // @ts-ignore
                      name="tcpSocket.port"
                      placeholder={intl(
                        'saenext.HealthCheckField.micro-app.HealthCheck.EnterTheRequestedPort',
                      )}
                    />
                  </Form.Item>
                </If>
                <Form.Item
                  className="full24-width"
                  label={intl('saenext.HealthCheckField.micro-app.HealthCheck.DelayTimeSeconds')}
                  required
                  requiredMessage={intl(
                    'saenext.HealthCheckField.micro-app.HealthCheck.TheDelayTimeHasNot',
                  )}
                  requiredTrigger="submit"
                  extra={
                    <>
                      {/* {intl('saenext.HealthCheckField.micro-app.HealthCheck.TheDelayTimeMustBe')} */}
                      {activeKey === 'Readiness' && hasMicroService
                        ? intl(
                            'saenext.HealthCheckField.micro-app.HealthCheck.IfYouHaveConfiguredDelayed',
                          )
                        : healthText[activeKey].delayHelp}
                      <a
                        href={CachedData.confLink('help:sae:best-practices-for-health-checks')}
                        target="_blank"
                      >
                        {intl(
                          'saenext.HealthCheckField.micro-app.HealthCheck.BestPracticesForHealthCheck',
                        )}
                      </a>
                    </>
                  }
                  validator={(rule, value: number, callback: any) => {
                    if (disabled && value <= this.state.delayTime) {
                      callback(
                        intl('saenext.HealthCheckField.micro-app.HealthCheck.TheDelayTimeMustBe.1'),
                      );
                    } else {
                      callback();
                    }
                  }}
                >
                  <NumberPicker
                    // @ts-ignore
                    name="initialDelaySeconds"
                    min={0}
                    placeholder={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.IndicatesHowLongTheApplication',
                    )}
                  />
                </Form.Item>
                <Form.Item
                  className="full24-width"
                  label={intl(
                    'saenext.HealthCheckField.micro-app.HealthCheck.TimeoutPeriodSeconds',
                  )}
                >
                  <NumberPicker
                    // @ts-ignore
                    name="timeoutSeconds"
                    min={0}
                    placeholder={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.IndicatesTheTimeoutPeriodOf',
                    )}
                  />
                </Form.Item>
                <Form.Item
                  className="full24-width"
                  label={intl('saenext.HealthCheckField.micro-app.HealthCheck.CheckPeriodSeconds')}
                >
                  <NumberPicker
                    // @ts-ignore
                    name="periodSeconds"
                    min={0}
                    placeholder={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.SetTheHealthCheckCycle',
                    )}
                  />
                </Form.Item>
                <Form.Item
                  className="full24-width"
                  label={intl(
                    'saenext.HealthCheckField.micro-app.HealthCheck.HealthThresholdTimes',
                  )}
                >
                  <NumberPicker
                    // @ts-ignore
                    name="successThreshold"
                    defaultValue={1}
                    min={0}
                    disabled={activeKey === 'Liveness' || activeKey === 'StartupProbe'}
                    placeholder={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.AfterTheDetectionFailsThe',
                    )}
                  />
                </Form.Item>
                <Form.Item
                  className="full24-width"
                  label={intl(
                    'saenext.HealthCheckField.micro-app.HealthCheck.UnhealthyThresholdTimes',
                  )}
                >
                  <NumberPicker
                    // @ts-ignore
                    name="failureThreshold"
                    defaultValue={activeKey === 'Liveness' ? 3 : 1}
                    min={0}
                    placeholder={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.AfterTheDetectionIsSuccessful',
                    )}
                  />
                </Form.Item>
                <If condition={checkType === 'exec'}>
                  <Form.Item
                    label={intl('saenext.HealthCheckField.micro-app.HealthCheck.RunTheCommand')}
                    required
                    requiredMessage={intl(
                      'saenext.HealthCheckField.micro-app.HealthCheck.PleaseFillInTheExecution',
                    )}
                    className="full24-width"
                    {...(formItemLayout as any)}
                  >
                    <CommandShell name="exec.command" />

                    <div>
                      <Copy text={command} showIcon>
                        {intl('saenext.HealthCheckField.micro-app.HealthCheck.OneClickCopy')}
                      </Copy>
                      <span className="pl-s">
                        {intl(
                          'saenext.HealthCheckField.micro-app.HealthCheck.ExecuteTheCurrentCommandIf',
                        )}
                      </span>
                    </div>
                  </Form.Item>
                </If>
              </Form>
            </div>
          </div>
        </If>
      </div>
    );
  }
}

export default HealthCheck;
