import React from 'react';
import BaseField from '@ali/deep-form-helper';
import HealthCheck from './HealthCheck';
import { ConfigProvider } from '@ali/deep';

class HealthCheckField extends BaseField {
  props;
  static displayName = 'HealthCheckField';

  formatValueIn(value, props) {
    try {
      return JSON.parse(value);
    } catch (e) {
      return value;
    }
  }

  formatValueOut(value = []) {
    return JSON.stringify(value);
  }

  getProps() {
    return {
      ...this.props
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...fieldProps,
      ...this.props,
    };

    return (
      <HealthCheck
        {...newProps}
      />
    )
  }
}

export default ConfigProvider.config(HealthCheckField as any)
