import React, { useState } from 'react';// 假设 Icon 组件是从其他地方导入的
import { Icon } from '@ali/cnd';

const SwitchIcon = (props) => {
  const {
    onClick = () => {},
    ...restProps
  } = props;

  const [iconType, setIconType] = useState<'up' | 'down'>('down');

  const toggleIconType = () => {
    setIconType(prevType => (prevType === 'down' ? 'up' : 'down'));
    onClick();
  };

  return (
    <Icon {...restProps} onClick={toggleIconType} type={iconType}/>
  );
};

export default SwitchIcon;