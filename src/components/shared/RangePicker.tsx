import React, { useEffect, useState } from "react";
import { has } from "lodash";
import { NumberPicker, Range } from "@ali/cnd";
import If from "./If";

const RangePicker = props => {
  const {
    defaultValue,
    value,
    onChange,
    marks = 10,
    marksPosition = 'below',
    type = 'inline',
    unit,
    style,
    ...rest
  } = props;
  const [cuttentValue, setCurrentValue] = useState(0);

  useEffect(() => {
    setCurrentValue(has(props, 'value') ? value : defaultValue);
  }, [value]);

  const handleChange = val => {
    if (!has(props, 'value')) {
      setCurrentValue(val);
    }
    onChange && onChange(val);
  };

  return (
    <div className="flex" style={style}>
      <Range
        value={cuttentValue}
        marksPosition={marksPosition}
        marks={marks}
        onChange={val => handleChange(val)}
        onProcess={val => handleChange(val)}
        style={{ padding: '8px 32px 0 8px' }}
        {...rest}
      />
      <NumberPicker
        value={cuttentValue}
        type={type}
        onChange={val => handleChange(val)}
        {...rest}
      />
      <If condition={unit}>
        <span style={{ marginLeft: 4 }}>{unit}</span>
      </If>
    </div>
  );
};

export default RangePicker;