import React from 'react';
import _ from 'lodash';

type Props = {
  if: boolean;
  children?: React.ReactNode;
  style?: object;
}

class ComShow extends React.PureComponent<Props> {
  render() {
    const { if: vif = false, children = null, style = {}, ...others } = this.props;
    const curStyle = _.assign({ display: vif ? 'block' : 'none' }, style);
    return (
      <div style={curStyle} {...others}>
        {children}
      </div>
    );
  }
}
export default ComShow;
