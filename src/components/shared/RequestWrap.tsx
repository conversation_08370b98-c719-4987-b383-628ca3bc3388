import React, { useState } from 'react'
import AddPropsWrap from './AddPropsWrap';

const RequestWrap = (props) => {
  const {
    children,
    ...restProps
  } = props;

  const {
    onClick = () => {},
  } = children.props;

  const [loading, setLoading] = useState(false);

  const requestClick = async () => {
    if (loading) {
      return;
    }
    setLoading(true);
    try {
      await onClick();
    } catch (error) {
      setLoading(false);
    }
    setLoading(false);
  }

  return (
    <AddPropsWrap
      {...restProps}
      loading={loading}
      onClick={requestClick}
      merge={false}
    >
      {children}
    </AddPropsWrap>
  )
}

export default RequestWrap;