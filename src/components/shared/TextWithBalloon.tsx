import React, { memo, ReactNode } from 'react';
import { Balloon, Icon } from '@ali/cnd';

const TextWithBalloon = ({ text, tips = '' as string | ReactNode, children = '' as string | ReactNode, color = '', iconColor = '', align = 't', cache = true, ballonProps = {} }) => {
  return (
    <>
      <span className={`mr-s ${color}`}>{text}</span>
      <Balloon
        cache={cache}
        align={
          align as 't' | 'r' | 'b' | 'l' | 'tl' | 'tr' | 'bl' | 'br' | 'lt' | 'lb' | 'rt' | 'rb'
        }
        trigger={<Icon size="xs" type="help" className={`pointer ${iconColor}` } />}
        closable={false}
        {...ballonProps}
      >
        <span>{tips || children}</span>
      </Balloon>
    </>
  );
};

export default memo(TextWithBalloon);
