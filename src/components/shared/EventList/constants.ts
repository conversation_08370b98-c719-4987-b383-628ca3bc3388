import { intl } from '@ali/cnd';
import CachedData from '~/cache/common';

export const TipsMap = {
  FailedCreate: {
    text: intl('saenext.shared.EventList.constants.CheckWhetherTheImageAddress'),
  },
  FailedGetExternalMetric: {
    text: intl('saenext.shared.EventList.constants.ElasticScalingExceptionPleaseAdd'),
  },
  FailedComputeMetricsReplicas: {
    text: intl('saenext.shared.EventList.constants.ElasticScalingExceptionPleaseAdd'),
  },
  ConfigDefaultBackendServerGroupFailed: {
    text: intl('saenext.shared.EventList.constants.CheckWhetherTheSlbUsed'),
  },
  BackOff: {
    text: intl('saenext.shared.EventList.constants.ApplicationStartupFailedCheckWhether'),
  },
  FailedPreStopHook: {
    text: intl('saenext.shared.EventList.constants.PrestopExecutionFailedMakeSure'),
    link: `${CachedData.confLink('help:sae:configure-lifecycle-management')}?spm=5176.12834076.0.dexternal.494e6a68aHfHmN`,
  },
  FailedPostStartHook: {
    text: intl('saenext.shared.EventList.constants.PoststartExecutionFailedMakeSure'),
    link: `${CachedData.confLink('help:sae:configure-lifecycle-management')}?spm=5176.12834076.0.dexternal.494e6a68aHfHmN`,
  },
  Failed: {
    list: [
      {
        message: 'Failed to pull image,Error: ImagePullBackOff,Error: ErrImagePull',
        text: intl('saenext.shared.EventList.constants.FailedToPullTheImage'),
      },
      {
        message: 'Error: failed to start container',
        text: intl('saenext.shared.EventList.constants.ApplicationStartupFailedCheckWhether'),
      },
    ],
  },
  Unhealthy: {
    text: intl('saenext.shared.EventList.constants.HealthCheckLivenessAndReadiness'),
    instructLink: CachedData.confLink('help:sae:a-configured-health-check-fails'),
    practiceLink: CachedData.confLink('help:sae:best-practices-for-health-checks'),
  },
  CreatingLoadBalancerFailed: {
    list: [
      {
        message: 'not able to find loadbalancer',
        text: intl('saenext.shared.EventList.constants.FailedToCreateAnSlb'),
      },
      {
        message: 'There is conflict listener port exists',
        text: intl('saenext.shared.EventList.constants.FailedToCreateAnSlb.1'),
      },
      {
        message: 'The specified CertificateId does not exist',
        text: intl('saenext.shared.EventList.constants.TheCertificateDoesNotExist'),
      },
    ],
  },
  OOMKilled: {
    text: intl('saenext.shared.EventList.constants.ExcessiveServicePressureCheckThe'),
    link: CachedData.confLink('help:sae:best-practices-for-jvm-heap-size-configuration'),
  },
};
