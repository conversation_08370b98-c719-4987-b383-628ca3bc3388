import React, { useEffect, useState } from 'react';
import { DateTime, Message, Tag, intl } from '@ali/cnd';
import CndTable from '@ali/cnd-table';
import services from '~/services';
import { find, forEach, includes, map } from 'lodash';
import { getParams, removeParams } from '~/utils/global';
import { setSearchParams } from '~/utils/global';
import If from '../If';
import { TipsMap } from './constants';

const EventList = (props) => {
  const { regionId, namespaceId, appId, filters } = props;

  const [totalSize, setTotalSize] = useState(0);
  const [openRowKeys, setOpenRowKeys] = useState([]);

  useEffect(() => {
    return () => {
      removeParams(['ObjectName', 'Reason', 'ObjectKind', 'EventType']);
    };
  }, []);

  const filterParams = filters || {
    ObjectName: getParams('ObjectName'),
    Reason: getParams('Reason'),
    ObjectKind: getParams('ObjectKind'),
    EventType: getParams('EventType'),
  };

  const fetchData = async (params) => {
    if (!namespaceId) return;
    const { current, pageSize, ...restParams } = params;
    setSearchParams(restParams);
    const {
      Data: { AppEventEntity, TotalSize },
    } = await services.listAppEvents({
      CurrentPage: current,
      PageSize: pageSize,
      AppId: appId,
      RegionId: regionId,
      Namespace: namespaceId,
      ...restParams,
    });
    if (!totalSize) {
      setTotalSize(TotalSize);
    }
    initOpenRowKeys(AppEventEntity);
    return {
      data: map(AppEventEntity, (item, index) => {
        item.primaryKey = `id${index}`;
        return item;
      }),
      total: TotalSize,
    };
  };

  const initOpenRowKeys = (data) => {
    const openRowKeys = [];
    forEach(data, (item, index) => {
      if (item.EventType === 'Warning') {
        openRowKeys.push(`id${index}`);
      }
    });
    setOpenRowKeys(openRowKeys);
  };

  const expandedRowRender = (record) => {
    const { Reason, Message } = record;
    const TipData = TipsMap[Reason] || {};
    if (TipData.list) {
      forEach(TipData.list, (item) => {
        const messages = item.message.split(',');
        if (find(messages, (mes) => includes(Message, mes))) {
          TipData.text = item.text || '';
          TipData.link = item.link || '';
        }
      });
    }
    const { text, link, instructLink, practiceLink } = TipData;
    return (
      <div className="pd-card">
        <div>{record.Message}</div>
        <If condition={text}>
          <div className="mt-s">
            {intl('saenext.shared.EventList.SelfTroubleshootingSuggestions')}
            {text}
            <If condition={link}>
              <a href={link} target="_blank" className="ml-s">
                {intl('saenext.shared.EventList.ClickView')}
              </a>
            </If>
            <If condition={instructLink}>
              <a href={instructLink} target="_blank" className="ml-s">
                {intl('saenext.shared.EventList.TroubleshootingGuide')}
              </a>
            </If>
            <If condition={practiceLink}>
              <a href={practiceLink} target="_blank" className="ml-s">
                {intl('saenext.shared.EventList.BestPractices')}
              </a>
            </If>
          </div>
        </If>
      </div>
    );
  };

  const columns = [
    {
      key: 'EventType',
      title: intl('saenext.shared.EventList.EventLevel'),
      dataIndex: 'EventType',
      width: 150,
      cell: (value, index, record) => {
        if (value === 'Warning') {
          return <Tag style={{ background: '#F7EDED', color: '#996262' }}>{value}</Tag>;
        }
        return <Tag style={{ background: '#EDF7ED', color: '#629962' }}>{value}</Tag>;
      },
    },
    {
      key: 'Reason',
      title: intl('saenext.shared.EventList.CauseOfTheEvent'),
      dataIndex: 'Reason',
      width: 200,
    },
    {
      key: 'ObjectKind',
      title: intl('saenext.shared.EventList.SourceType'),
      dataIndex: 'ObjectKind',
      width: 100,
    },
    {
      key: 'ObjectName',
      title: intl('saenext.shared.EventList.SourceName'),
      dataIndex: 'ObjectName',
    },
    {
      key: 'FirstTimestamp',
      title: intl('saenext.shared.EventList.FirstOccurrenceTime'),
      dataIndex: 'FirstTimestamp',
      width: 180,
      cell: (value, index, record) => <DateTime value={value} />,
    },
    {
      key: 'LastTimestamp',
      title: intl('saenext.shared.EventList.RecentOccurrenceTime'),
      dataIndex: 'LastTimestamp',
      width: 180,
      cell: (value, index, record) => <DateTime value={value} />,
    },
  ];

  const search = {
    defaultDataIndex: 'ObjectName',
    defaultSelectedDataIndex: 'ObjectName',
    options: [
      {
        label: intl('saenext.shared.EventList.SourceName'),
        dataIndex: 'ObjectName',
        defaultValue: filterParams.ObjectName,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.shared.EventList.EnterASourceNameSuch'),
        },
      },
      {
        label: intl('saenext.shared.EventList.CauseOfTheEvent'),
        dataIndex: 'Reason',
        defaultValue: filterParams.Reason,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.shared.EventList.EnterAnEventReason'),
        },
      },
      {
        label: intl('saenext.shared.EventList.SourceType'),
        dataIndex: 'ObjectKind',
        defaultValue: filterParams.ObjectKind,
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.shared.EventList.SelectASourceType'),
          dataSource: [
            {
              label: intl('saenext.shared.EventList.ApplicationDeployment'),
              value: 'Deployment',
            },
            {
              label: intl('saenext.shared.EventList.ApplicationInstancePod'),
              value: 'Pod',
            },
            {
              label: 'SLB(Service)',
              value: 'Service',
            },
            {
              label: intl('saenext.shared.EventList.AutomaticElasticityHorizontalpodautoscaler'),
              value: 'HorizontalPodAutoscaler',
            },
            {
              label: intl('saenext.shared.EventList.GatewayRoutingIngress'),
              value: 'Ingress',
            },
            {
              label: intl('saenext.shared.EventList.ApplicationCloneset'),
              value: 'CloneSet',
            },
            {
              label: intl('saenext.shared.EventList.ApplicationStatefulset'),
              value: 'StatefulSet',
            },
          ],
        },
      },
      {
        label: intl('saenext.shared.EventList.EventLevel'),
        dataIndex: 'EventType',
        defaultValue: filterParams.EventType,
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.shared.EventList.SelectAnEventLevel'),
          dataSource: [
            {
              label: 'Warning',
              value: 'Warning',
            },
            {
              label: 'Normal',
              value: 'Normal',
            },
          ],
        },
      },
    ],
  };

  return (
    <>
      <Message type="notice" className="mb-s">
        {intl('saenext.shared.EventList.ATotalOfTotalsizeRecords', { totalSize: totalSize })}
      </Message>
      <CndTable
        key={namespaceId}
        fetchData={fetchData}
        columns={columns}
        showRefreshButton
        primaryKey="primaryKey"
        expandedRowIndent={[1, 1]}
        expandedRowRender={expandedRowRender}
        openRowKeys={openRowKeys}
        onRowOpen={setOpenRowKeys}
        search={search}
      />
    </>
  );
};

export default EventList;
