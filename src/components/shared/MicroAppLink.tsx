import React from 'react';
import { Link } from '@ali/cnd';
import { microAppLink } from '~/utils/global';

const MicroAppLink = (props) => {
  const { appId, appName, regionId } = props;
  const { v1Link, v2Link, enable } = microAppLink(appId, regionId);
  return (
    <Link
      href={!enable && v1Link}
      to={enable && v2Link}
      // @ts-ignore
      target={!enable && '_blank'}
    >
      {appName}
    </Link>
  )
}

export default MicroAppLink;