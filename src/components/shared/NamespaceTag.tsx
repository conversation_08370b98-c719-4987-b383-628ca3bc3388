import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { Actions, Icon } from '@ali/cnd';
import services from '~/services';
import _ from 'lodash';
import { getParams, setSearchParams } from '~/utils/global';

type Props = {
  value?: string;
  defaultValue?: string;
  onChange?: (val: string) => void;
  setNamespaceData?: (arr) => void;
};

const DEFAULT_ALL = [
  {
    NamespaceId: 'all',
    NamespaceName: intl('saenext.components.shared.NamespaceTag.AllNamespaces'),
  },
];

const DEFAULT_NONE = {
  NamespaceId: 'none',
  NamespaceName: intl('saenext.components.shared.NamespaceTag.Unknown'),
};

const { LinkButton } = Actions;

const NamespaceTag = (props: Props, ref) => {
  const defaultNamespaceId = getParams('namespaceId') || 'all';
  const { value: propsValue = defaultNamespaceId, onChange, setNamespaceData } = props;

  const [isLoading, setIsLoading] = useState(false);
  const [value, setValue] = useState(propsValue);
  const namespaces = useRef([]);
  const [namespaceList, setNamespaceList] = useState(DEFAULT_ALL);

  useEffect(() => {
    fetchData();
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      pushNoneNamespace: () => {
        // @ts-ignore
        const enabledNone = namespaceList.find(
          (ns) =>
            ns.NamespaceId === 'none' &&
            ns.NamespaceName === intl('saenext.components.shared.NamespaceTag.Unknown'),
        );
        const _enabledNone = !!enabledNone;
        if (_enabledNone) return;
        let _namespaceList = [...namespaceList, DEFAULT_NONE];
        setNamespaceList(_namespaceList);
      },
    }),
    [namespaceList],
  );

  const fetchData = async () => {
    setIsLoading(true);
    const res = await services.getNamespaceListV2({
      params: {
        CurrentPage: 1,
        PageSize: 100,
      },
      customErrorHandle: (error, data) => {
        setIsLoading(false);
        // @ts-ignore
        setNamespaceList(DEFAULT_ALL);
        return error;
      },
    });
    const _namespaceList = _.get(res, 'Data.Namespaces');
    namespaces.current = _namespaceList;
    setIsLoading(false);
    setNamespaceList(_.concat(DEFAULT_ALL, _namespaceList));
    setNamespaceData?.(_namespaceList);
  };

  const handleChange = (val) => {
    setSearchParams({ namespaceId: val });
    setValue(val);
    onChange(val);
  };

  return (
    <div
      className="pl-l pr-l mb-s"
      style={{ background: '#eff3f8', paddingTop: 12, paddingBottom: 12 }}
    >
      {isLoading ? (
        <div className="flex justify-between">
          <LinkButton>{intl('saenext.components.shared.NamespaceTag.AllNamespaces')}</LinkButton>
          {/* <div className='mr ml' style={{ width: 1, height: 12, background: '#d8d8d8' }} /> */}
          <Icon size="xs" type="loading" />
        </div>
      ) : (
        <Actions wrap={true} threshold={200}>
          {_.map(namespaceList, (item) => {
            // @ts-ignore
            const { NamespaceId, NamespaceName } = item || {};
            return (
              <LinkButton
                key={NamespaceId}
                style={{
                  color: value === NamespaceId ? '#0070cc' : '#999',
                }}
                onClick={() => {
                  handleChange(NamespaceId);
                }}
              >
                {NamespaceName || NamespaceId}
              </LinkButton>
            );
          })}
        </Actions>
      )}
    </div>
  );
};

export default forwardRef(NamespaceTag);
