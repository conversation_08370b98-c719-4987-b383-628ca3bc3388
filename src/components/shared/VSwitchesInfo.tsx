import React, { useMemo } from 'react';
import { Balloon, Copy, Icon, intl, ToolTipCondition } from '@ali/cnd';
import { last, map, toUpper } from 'lodash';
import { useCheckVSwitchZone } from '../app-detail/micro-app/utils/useCheckVSwitchZone';
import If from './If';
import CachedData from '~/cache/common';

const VSwitchesInfo = (props) => {
  const { regionId, vSwitchIds = '', vSwitchList, replicas, setZoneWarning } = props;

  const vSwitches = vSwitchIds.split(',');

  if (!vSwitches.length) {
    return <></>;
  }

  const {
    curVSwitchList,
    ipCountSum,
    zoneList,
  } = useMemo(() => {

    let ipCountSum = 0;
    const zoneList = [];

    const curVSwitchList = map(vSwitches, (vSwitch) => {
      const targetVSwitch = vSwitchList.find((v) => v.VSwitchId === vSwitch);
      if (targetVSwitch) {
        const zoneName = toUpper(last(targetVSwitch.ZoneId as string));
        const label = intl(
          'saenext.micro-app.basic-info.AppBaseInfo.ZoneZonenameTargetvswitchvswitchname',
          { zoneName: zoneName, targetVSwitchVSwitchName: targetVSwitch.VSwitchName },
        );

        const { AvailableIpAddressCount } = targetVSwitch;
        if (AvailableIpAddressCount) {
          ipCountSum += AvailableIpAddressCount;
        }

        zoneList.push(targetVSwitch.ZoneId);

        return {
          label,
          value: vSwitch,
          zoneId: targetVSwitch.ZoneId,
        };
      }

      return {
        label: '',
        value: vSwitch,
        noExist: vSwitchList.length > 0,
      };
    });

    return {
      curVSwitchList,
      ipCountSum,
      zoneList,
    };
  }, [vSwitchList, vSwitchIds]);

  const { inRecommend, recommendZones } = useCheckVSwitchZone({ zoneList, regionId });

  const zoneWarning = !inRecommend && recommendZones.length > 0;
  setZoneWarning?.(zoneWarning);

  return (
    <>
      {map(curVSwitchList, (v) => {
        const { label, value, noExist, zoneId } = v;
        return (
          <div key={value} style={{ marginBottom: 12 }}>
            <p>
              <If condition={!!label}>
                <div>
                  <span className="text-description">
                    {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
                  </span>
                  {label}
                </div>
              </If>
              <div>
                <span className="text-description">ID: </span>
                <Copy text={value}>
                  <a
                    href={CachedData.confLink('feature:vpc:switches:url', {
                      regionId: regionId,
                      switchId: value
                    })}
                    target="_blank"
                  >
                    {value}
                  </a>
                </Copy>

                <If condition={noExist}>
                  <Balloon.Tooltip
                    trigger={
                      <Icon type="warning" size="xs" className="text-warning ml-s" />
                    }
                  >
                    {intl(
                      'saenext.micro-app.basic-info.AppBaseInfo.ThisSwitchHasBeenDeleted',
                    )}
                  </Balloon.Tooltip>
                </If>

              </div>
              {zoneId && zoneId === 'cn-shenzhen-a' && (
                <div className="text-warning">
                  {intl(
                    'saenext.micro-app.basic-info.AppBaseInfo.ShenzhenZoneAIsAbout',
                  )}
                </div>
              )}
            </p>
          </div>
        );
      })}

      <div
        className={
          ipCountSum > replicas || vSwitchList.length === 0
            ? 'text-description'
            : 'text-warning'
        }
      >
        {intl(
          'saenext.micro-app.basic-info.AppBaseInfo.AvailableIpAddressesIpcountsum',
          { ipCountSum: ipCountSum },
        )}
        <If
          condition={ipCountSum <= replicas && replicas > 0 && vSwitchList.length > 0}
        >
          <Balloon.Tooltip
            trigger={<Icon type="warning" size="xs" className="text-warning ml" />}
          >
            <If condition={ipCountSum === 0}>
              {intl('saenext.micro-app.basic-info.AppBaseInfo.TheNumberOfAvailableIp')}
            </If>
            <If condition={ipCountSum !== 0}>
              {intl('saenext.micro-app.basic-info.AppBaseInfo.TheNumberOfIpAddresses')}
            </If>
          </Balloon.Tooltip>
        </If>
      </div>
    </>
  );
};

export default VSwitchesInfo;