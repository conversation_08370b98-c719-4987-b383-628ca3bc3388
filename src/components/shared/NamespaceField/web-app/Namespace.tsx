import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Select, ConsoleContext, Icon, SlidePanel, Form, Field, Input } from '@ali/cnd';
import services from '~/services';
import _ from 'lodash';
import TextRefreshButton from '~/components/shared/TextRefreshButton';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/cache/common';

type Props = {
  value?: string;
  defaultValue?: string;
  onChange?: (val: string) => void;
  onNamespaceChange?: (val: string) => void;
  setVpcId: (vpcId: string) => void;
};

type State = {
  loading: boolean;
  isShowing: boolean;
  value: string;
  isLinkVpc: boolean;
  namespaceList: any[];
  vpcLoading: boolean;
  vpcList: any[];
  namespaceId: string;
  namespaceName: string;
  isProcessing: boolean;
};

class NameSpace extends Component<Props, State> {
  private namespaceVpcs = new Map();
  field = new Field(this);

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      isShowing: false,
      loading: false,
      isLinkVpc: true,
      namespaceList: [],
      vpcLoading: false,
      vpcList: [],
      namespaceId: '',
      namespaceName: '',
      isProcessing: false,
    };
    //@ts-ignore
    this.regionId = ConsoleContext._currentValue.region.getCurrentRegionId();
  }

  componentDidMount(): void {
    this.getNamespaceList(this.initStates);
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.value !== this.props.value) {
      this.updateStates();
    }
  }

  initStates = () => {
    // initState
    const { defaultValue, onChange, setVpcId } = this.props;
    if (defaultValue) {
      onChange && onChange(defaultValue);
      let vpc = this.namespaceVpcs.get(defaultValue);

      let _isLinkVpc = false;
      if (vpc?.VpcId) {
        _isLinkVpc = true;
      }
      setVpcId?.(vpc?.VpcId);
      this.setState({
        value: defaultValue,
        isLinkVpc: _isLinkVpc,
        namespaceId: defaultValue,
        namespaceName: vpc?.NamespaceName,
      });
    }
  };

  updateStates = () => {
    const { value, onChange, setVpcId } = this.props;
    if (value) {
      onChange && onChange(value);
      let vpc = this.namespaceVpcs.get(value);

      let _isLinkVpc = false;
      if (vpc?.VpcId) {
        _isLinkVpc = true;
      }
      setVpcId?.(vpc?.VpcId);
      setVpcId?.(vpc?.VpcId);
      this.setState({
        value,
        isLinkVpc: _isLinkVpc,
        namespaceId: value,
        namespaceName: vpc?.NamespaceName,
      });
    }
  };

  getNamespaceList = async (callback?: () => void) => {
    this.setState({
      loading: true,
    });
    const res = await services.getNamespaceListV2({
      params: {
        CurrentPage: 1,
        PageSize: 100,
      },
    });

    const _Namespaces = _.get(res, 'Data.Namespaces');
    const _namespaceList = _.map(_Namespaces, (item) => {
      const { NamespaceName, NamespaceId, VpcId, VpcName } = item || {};

      item.label =
        VpcId && VpcName ? (
          <>
            <span className="mr-l">{NamespaceName || NamespaceId}</span>
            <span className="text-description">{`vpc (${VpcName || VpcId})`}</span>
          </>
        ) : (
          <span className="mr-l">{NamespaceName || NamespaceId}</span>
        );

      item.value = NamespaceId;
      this.namespaceVpcs.set(NamespaceId, {
        VpcName,
        VpcId,
        NamespaceName,
      });
      return item;
    });
    this.setState(
      {
        loading: false,
        namespaceList: _namespaceList,
      },
      () => {
        callback && callback();
      },
    );
  };

  getVpcs = async () => {
    this.setState({
      vpcLoading: true,
    });
    const { Vpcs = {} } = await services.DescribeVpcs({
      params: {
        // @ts-ignore
        RegionId: this.regionId,
        PageNumber: 1,
        PageSize: 50,
      },
      customErrorHandle: (error, data) => {
        return error;
      },
    });
    const { Vpc = [] } = Vpcs;
    const vpcList = _.map(Vpc, ({ VpcName, VpcId }) => ({
      label: (
        <>
          <span className="mr-l">{VpcName || VpcId}</span>
          <span className="text-description">{VpcId}</span>
        </>
      ),

      value: VpcId,
    }));
    this.setState({
      vpcList,
      vpcLoading: false,
    });
  };

  handleChange = (val) => {
    const { onChange, onNamespaceChange, setVpcId } = this.props;
    this.setState(
      {
        value: val,
      },
      () => {
        onChange && onChange(val);
        onNamespaceChange?.(val);
        let vpc = this.namespaceVpcs.get(val);

        let _isLinkVpc = false;
        if (vpc?.VpcId) {
          _isLinkVpc = true;
        }
        setVpcId?.(vpc?.VpcId);
        this.setState({
          isLinkVpc: _isLinkVpc,
          namespaceId: val,
          namespaceName: vpc?.NamespaceName,
        });
      },
    );
  };

  showSlide = () => {
    this.setState({ isShowing: true });
    this.getVpcs();
  };

  closeSlide = () => {
    this.setState({ isShowing: false });
  };

  handleUpdateVpc = async () => {
    this.field.validate((errors, values) => {
      if (errors) {
        return;
      }
      const { namespaceId } = this.state;
      // @ts-ignore
      const VpcId = this.field.getValue('VpcId');

      this.setState({ isProcessing: true });
      services
        .updateNamespaceVpc({
          VpcId: VpcId as string,
          NamespaceId: namespaceId,
          // @ts-ignore
          RegionId: this.regionId,
        })
        .then((res) => {
          this.setState({ isProcessing: false });
          this.closeSlide();
          this.getNamespaceList(() => this.handleChange(namespaceId));
        })
        .catch(() => {
          this.setState({ isProcessing: false });
        });
    });
  };

  render(): React.ReactNode {
    const { init } = this.field;
    const {
      loading,
      value,
      isLinkVpc,
      namespaceList,
      isShowing,
      vpcList,
      vpcLoading,
      isProcessing,
    } = this.state;
    return (
      <>
        <Select
          dataSource={namespaceList}
          value={value}
          onChange={this.handleChange}
          style={{ width: '100%' }}
          state={loading ? 'loading' : null}
        />

        <div className="flex">
          <TextRefreshButton onClick={() => this.getNamespaceList()} />
          <ExternalLink
            className="ml-l"
            label={intl('saenext.NamespaceField.web-app.Namespace.CreateANamespace')}
            // @ts-ignore
            url={`${window.location.origin}/${this.regionId}/namespace`}
          />

          {isLinkVpc ? (
            <div style={{ cursor: 'not-allowed' }} className="ml-l color-text-description">
              <span style={{ wordBreak: 'break-all' }}>
                {intl('saenext.NamespaceField.web-app.Namespace.BindVpc')}
              </span>
              <span className="ml-s">
                <Icon type="edit" size="xs" />
              </span>
            </div>
          ) : (
            <div onClick={this.showSlide} className="ml-l color-primary pointer">
              <span style={{ wordBreak: 'break-all' }}>
                {intl('saenext.NamespaceField.web-app.Namespace.BindVpc')}
              </span>
              <span className="ml-s">
                <Icon type="edit" size="xs" />
              </span>
              <span className="ml-s text-warning">
                {intl('saenext.NamespaceField.web-app.Namespace.CurrentlyYourNamespaceIsNot')}
              </span>
            </div>
          )}
        </div>
        <SlidePanel
          title={intl('saenext.NamespaceField.web-app.Namespace.BindVpc')}
          isShowing={isShowing}
          width="large"
          isProcessing={isProcessing}
          onClose={this.closeSlide}
          onCancel={this.closeSlide}
          onOk={this.handleUpdateVpc}
        >
          <Form field={this.field}>
            <Form.Item required label={intl('saenext.NamespaceField.web-app.Namespace.Namespace')}>
              <Input readOnly value={this.state.namespaceName} />
            </Form.Item>
            <Form.Item required label={intl('saenext.NamespaceField.web-app.Namespace.Vpc')}>
              <Select
                {...init('VpcId', {
                  rules: [
                    {
                      required: true,
                      message: intl('saenext.NamespaceField.web-app.Namespace.SelectAVpc'),
                    },
                  ],
                })}
                dataSource={vpcList}
                state={vpcLoading ? 'loading' : null}
                style={{ width: '100%' }}
              />

              <div className="flex">
                <TextRefreshButton onClick={() => this.getVpcs()} />
                <ExternalLink
                  className="ml-l"
                  label={intl('saenext.NamespaceField.web-app.Namespace.CreateAVpc')}
                  // @ts-ignore
                  url={`${CachedData.confLink('feature:vpc:url')}/vpc/${this.regionId}/vpcs`}
                />
              </div>
            </Form.Item>
          </Form>
        </SlidePanel>
      </>
    );
  }
}

export default NameSpace;
