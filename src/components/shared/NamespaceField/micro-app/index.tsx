import React from 'react';
import BaseField from '@ali/deep-form-helper';
import Namespace from './Namespace';
import { ConfigProvider } from '@ali/deep';

class NamespaceField extends BaseField {
  props;
  static displayName = 'NamespaceField';

  static propTypes = {
    ...BaseField.propTypes,
  };

  static defaultProps = {
    ...BaseField.defaultProps,
  };

  getProps() {
    return {
      ...this.props
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...fieldProps,
      ...this.props,
    };

    return <Namespace {...newProps} />;
  }
}


export default ConfigProvider.config(NamespaceField as any)