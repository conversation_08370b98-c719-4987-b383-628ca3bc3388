import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import {
  Form,
  Radio,
  Select,
  Field,
  ConsoleContext,
  Icon,
  SlidePanel,
  Input,
  ToolTipCondition,
  CndTag,
} from '@ali/cnd';
import services from '~/services';
import _ from 'lodash';
import TextRefreshButton from '~/components/shared/TextRefreshButton';
import ExternalLink from '~/components/shared/ExternalLink';
import { isForbidden } from '~/utils/authUtils';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';
import CachedData from '~/cache/common';

type Props = {
  value?: any;
  disabled?: boolean;
  defaultValue?: object;
  onChange?: (val: object) => void;
  onNamespaceChange?: () => void;
  isSupportHygon?: boolean;
  hygonSupportInfo: { regions: string[]; zones: string[] };
  updateVswitchList?: (v) => {};
};

type State = {
  autoConfig: boolean;
  vpcId: string;
  namespaceList: any[];
  vpcList: any[];
  vswitchLoading: boolean;
  vswitchList: any[];
  securityGroupsLoading: boolean;
  securityGroupsList: any[];
  isLinkVpc: boolean;
  isShowing: boolean;
  isProcessing: boolean;
  // slide panel
  nsLoading: boolean;
  vpcLoading: boolean;
  bindVpcList: any[];
  namespaceId: string;
  namespaceName: string;
  authedVPC: boolean;
  vSwitchIdMap: any;
};

const RadioGroup = Radio.Group;

class NameSpace extends Component<Props, State> {
  private regionId: string;
  private namespaceVpcs = new Map();
  field = new Field(this, {
    onChange: (name, value) => {
      this.changeNamespaceConfig(name);
    },
  });

  slideField = new Field(this);

  constructor(props) {
    super(props);
    const { value } = props;
    this.state = {
      autoConfig: true,
      vpcId: '',
      namespaceList: [],
      vpcList: [],
      vswitchLoading: false,
      vswitchList: [],
      securityGroupsLoading: false,
      securityGroupsList: [],
      isLinkVpc: true,
      isShowing: false,
      isProcessing: false,
      bindVpcList: [],
      vpcLoading: false,
      nsLoading: false,
      namespaceId: '',
      namespaceName: '',
      authedVPC: true,
      vSwitchIdMap: {},
    };
    //@ts-ignore
    this.regionId = ConsoleContext._currentValue.region.getCurrentRegionId();
    if (_.isEmpty(value)) this.initStates();
  }

  componentDidMount(): void {
    this.getNamespaceList(this.updateStates);
  }

  componentDidUpdate(prevProps: Readonly<Props>, prevState: Readonly<State>, snapshot?: any): void {
    if (prevProps.value !== this.props.value) {
      this.updateStates();
    }
  }

  initStates() {
    const { setValue } = this.field;
    const { onChange } = this.props;
    setValue('AutoConfig', true);
    onChange({
      AutoConfig: true,
    });
  }

  updateStates = () => {
    const { value, onChange } = this.props;
    if (!_.isEmpty(value)) {
      onChange && onChange(value);
      const defaultVpcId = this.namespaceVpcs.get(value.NamespaceId)?.VpcId;
      const {
        AutoConfig = false,
        NamespaceId,
        VpcId = defaultVpcId,
        VSwitchId,
        SecurityGroupId,
      } = value;
      this.setState({
        autoConfig: AutoConfig,
        vpcId: VpcId,
      });
      this.field.setValues({
        AutoConfig,
        NamespaceId,
        VpcId,
        VSwitchId: VSwitchId ? VSwitchId?.split(',') : [],
        SecurityGroupId,
      });
      setTimeout(() => {
        if (!AutoConfig && NamespaceId && VpcId) {
          this.field.validate();
        }
      });
      this.getVpcList(NamespaceId);
      this.getVswitches(VpcId);
      this.getSecurityGrouponList(VpcId);
    }
  };

  changeNamespaceConfig(name) {
    const { getValues } = this.field;
    const { onChange, onNamespaceChange } = this.props;

    const params = getValues([
      'AutoConfig',
      'NamespaceId',
      'VpcId',
      'VSwitchId',
      'SecurityGroupId',
    ]) as any;
    if (params.AutoConfig) {
      onChange({ AutoConfig: true });
      return;
    }
    params.VSwitchId = params.VSwitchId?.join(',');
    onChange(params);
    if (name === 'NamespaceId') {
      onNamespaceChange?.();
    }
  }

  getNamespaceList = async (callback?: () => void) => {
    this.setState({
      nsLoading: true,
    });
    const res = await services.getNamespaceListV2({
      params: {
        CurrentPage: 1,
        PageSize: 100,
      },
    });
    const _Namespaces = _.get(res, 'Data.Namespaces');
    const _namespaceList = _.map(_Namespaces, (item) => {
      item.label = item.NamespaceName || item.NamespaceId;
      item.value = item.NamespaceId;
      this.namespaceVpcs.set(item.NamespaceId, item);
      return item;
    });
    this.setState(
      {
        nsLoading: false,
        namespaceList: _namespaceList,
      },
      () => {
        callback && callback();
      },
    );
  };

  getVpcList = (namespaceId) => {
    const _namespace = this.namespaceVpcs.get(namespaceId) || {};
    const { VpcId, VpcName } = _namespace;
    const vpcList = [
      {
        label: (
          <>
            <span className="mr-l">{VpcName || VpcId}</span>
            <span className="text-description">{VpcId}</span>
          </>
        ),

        value: VpcId,
      },
    ];

    this.setState({
      vpcList,
    });
  };

  namespaceChanged = async (val) => {
    const { setValue, setValues } = this.field;

    setValues({
      VpcId: '',
      VSwitchId: [],
      SecurityGroupId: '',
    });

    const _namespace = this.namespaceVpcs.get(val) || {};
    const { NamespaceName, NamespaceId, VpcId, VpcName } = _namespace;
    const vpcList = [
      {
        label: (
          <>
            <span className="mr-l">{VpcName || VpcId}</span>
            <span className="text-description">{VpcId}</span>
          </>
        ),

        value: VpcId,
      },
    ];

    this.setState({
      vpcId: VpcId,
      vpcList,
      isLinkVpc: !!VpcId,
      namespaceId: NamespaceId,
      namespaceName: NamespaceName,
    });
    setValue('VpcId', VpcId);
    this.getVswitches(VpcId);
    this.getSecurityGrouponList(VpcId);
  };

  getVswitches = async (vpcId, vSwitchId?) => {
    if (!vpcId) return;

    this.setState({
      vswitchLoading: true,
    });

    const { VSwitches = {} } = await services.DescribeVSwitches({
      params: {
        RegionId: this.regionId,
        VSwitchId: vSwitchId || undefined,
        VpcId: vpcId,
        PageNumber: 1,
        PageSize: 50,
      },
      customErrorHandle: (error, _p, cb) => {
        if (isForbidden(error.code)) {
          this.setState({ authedVPC: false });
        } else {
          cb?.();
          this.setState({ authedVPC: true });
        }
      },
    });
    if (!VSwitches?.VSwitches?.length && vSwitchId && this.state.vswitchList) {
      // 搜索场景，且未搜索到
      const filterList = this.state.vswitchList.filter((item) => {
        if (item.searchKey.includes(vSwitchId)) {
          return item;
        }
      });
      this.setState({
        vswitchList: filterList,
        vswitchLoading: false,
      });
      return;
    }
    let _vSwitchIdMap = {};
    const vswitchList = _.map(VSwitches.VSwitch, (item) => {
      _.set(_vSwitchIdMap, item.VSwitchId, item);
      const zoneName = _.toUpper(_.last(item.ZoneId as string));
      const { AvailableIpAddressCount } = item;
      return {
        disabled: item.ZoneId === 'cn-shenzhen-a',
        ...item,
        value: item.VSwitchId,
        label: (
          <ToolTipCondition
            show={item.ZoneId === 'cn-shenzhen-a'}
            tip={intl('saenext.NamespaceField.micro-app.Namespace.ShenzhenZoneAIsAbout')}
            align="r"
          >
            <>
              <span className="mr-l">{item.VSwitchName}</span>
              <span className="text-description mr-s">{item.VSwitchId}</span>
              <span className="text-description">
                {intl('saenext.NamespaceField.micro-app.Namespace.ZoneZonename', {
                  zoneName: zoneName,
                })}
              </span>
              <span className="text-description ml-s">
                {intl(
                  'saenext.NamespaceField.micro-app.Namespace.AvailableIpAddressesAvailableipaddresscount',
                  { AvailableIpAddressCount: AvailableIpAddressCount },
                )}
              </span>
              {this.props.isSupportHygon &&
                _.includes(this.props.hygonSupportInfo.zones, item.ZoneId) && (
                  <CndTag
                    tagText={intl('saenext.NamespaceField.micro-app.Namespace.SupportSeaLight')}
                    tagType="safe"
                    style={{ marginLeft: 8 }}
                  />
                )}
            </>
          </ToolTipCondition>
        ),

        searchKey: item.VSwitchName + ' ' + item.VSwitchId,
      };
    });
    this.setState(
      {
        vswitchList,
        vSwitchIdMap: _vSwitchIdMap,
        vswitchLoading: false,
      },
      () => {
        if (this.props?.isSupportHygon) {
          this.props?.updateVswitchList(vswitchList);
        }
      },
    );
  };

  getSecurityGrouponList = async (vpcId, searchVal?) => {
    if (!vpcId) return;
    this.setState({
      securityGroupsLoading: true,
    });
    const {
      SecurityGroups: { SecurityGroup },
    } = await services.DescribeSecurityGroups({
      // @ts-ignore
      RegionId: this.regionId,
      VpcId: vpcId,
      PageSize: 99,
      PageNumber: 1,
      SecurityGroupId: searchVal,
    });

    const securityGroupValid = _.filter(SecurityGroup, (v) => !v.ServiceManaged);

    if (!securityGroupValid.length && searchVal && this.state.securityGroupsList) {
      // 搜索场景，且未搜索到
      const filterList = this.state.securityGroupsList.filter((item) => {
        if (item.searchKey.includes(searchVal)) {
          return item;
        }
      });
      this.setState({
        securityGroupsList: filterList,
        securityGroupsLoading: false,
      });
      return;
    }

    const securityGroupsList = _.map(securityGroupValid, (v) => {
      return {
        value: v.SecurityGroupId,
        label: (
          <>
            <span className="mr-l">{v.SecurityGroupName}</span>
            <span className="text-description">{v.SecurityGroupId}</span>
          </>
        ),

        searchKey: v.SecurityGroupName + ' ' + v.SecurityGroupId,
      };
    });
    this.setState({
      securityGroupsList,
      securityGroupsLoading: false,
    });
  };

  getVpcs = async () => {
    this.setState({
      vpcLoading: true,
    });
    const { Vpcs = {} } = await services.DescribeVpcs({
      params: {
        // @ts-ignore
        RegionId: this.regionId,
        PageNumber: 1,
        PageSize: 50,
      },
      customErrorHandle: (error, data) => {
        return error;
      },
    });
    const { Vpc = [] } = Vpcs;
    const _vpcList = _.map(Vpc, ({ VpcName, VpcId }) => ({
      label: (
        <>
          <span className="mr-l">{VpcName || VpcId}</span>
          <span className="text-description">{VpcId}</span>
        </>
      ),

      value: VpcId,
    }));
    this.setState({
      vpcLoading: false,
      bindVpcList: _vpcList,
    });
  };

  showSlide = () => {
    this.setState({ isShowing: true });
    this.getVpcs();
  };

  closeSlide = () => {
    this.setState({ isShowing: false });
  };

  handleUpdateVpc = async () => {
    this.slideField.validate((errors, values) => {
      if (errors) {
        return;
      }
      const { namespaceId } = this.state;
      // @ts-ignore
      const VpcId = this.slideField.getValue('VpcId');

      this.setState({ isProcessing: true });
      services
        .updateNamespaceVpc({
          VpcId: VpcId as string,
          NamespaceId: namespaceId,
          // @ts-ignore
          RegionId: this.regionId,
        })
        .then((res) => {
          this.setState({ isProcessing: false });
          this.closeSlide();
          this.getNamespaceList(() => this.namespaceChanged(namespaceId));
        })
        .catch(() => {
          this.setState({ isProcessing: false });
        });
    });
  };

  render(): React.ReactNode {
    const { init } = this.field;
    const {
      onNamespaceChange,
      onChange,
      isSupportHygon = false,
      hygonSupportInfo = { regions: [], zones: [] },
    } = this.props;
    const {
      autoConfig,
      vpcId,
      vpcList,
      namespaceList,
      vswitchList,
      vswitchLoading,
      securityGroupsLoading,
      securityGroupsList,
      isLinkVpc,
      nsLoading,
      isShowing,
      isProcessing,
      bindVpcList,
      vpcLoading,
      vSwitchIdMap,
    } = this.state;

    const handleVswitchOnlyOneTip = () => {
      const vSwitchIds: string[] = this.field.getValue('VSwitchId');
      if (vSwitchIds.length === 1) {
        return (
          <span style={{ color: '#FFA003' }}>
            {intl('saenext.NamespaceField.micro-app.Namespace.CurrentlyOnlyOneSwitchVswitch')}
          </span>
        );
      }
      if (vSwitchIds.length > 1) {
        const _vSwitchIdZone = vSwitchIds.map((item) => {
          return _.get(vSwitchIdMap, `${item}.ZoneId`);
        });
        if (_.uniq(_vSwitchIdZone).length === 1) {
          return (
            <span style={{ color: '#FFA003' }}>
              {intl('saenext.NamespaceField.micro-app.Namespace.WeRecommendThatYouSelect.1')}
            </span>
          );
        }
        return null;
      }
    };

    return (
      <>
        <RadioGroup
          name="type"
          disabled={this.props.disabled}
          {...init('AutoConfig', {
            initValue: autoConfig,
            props: {
              onChange: (val: boolean) => {
                this.setState({ autoConfig: val });
                onChange?.({ AutoConfig: val });
                onNamespaceChange?.();
              },
            },
          })}
        >
          <Radio className="radio-item-120" value={true}>
            {intl('saenext.NamespaceField.micro-app.Namespace.SystemCreation')}
          </Radio>
          <Radio className="radio-item-120" value={false}>
            {intl('saenext.NamespaceField.micro-app.Namespace.SelectAnExistingNamespace')}
          </Radio>
        </RadioGroup>
        {autoConfig ? null : (
          <div className="w-100 mt-l">
            <Form field={this.field} labelAlign="top">
              <div>
                <Form.Item
                  required
                  label={intl('saenext.NamespaceField.micro-app.Namespace.Namespace')}
                  className="w-50"
                >
                  <Select
                    {...init('NamespaceId', {
                      // initValue: this.props.defaultValue,
                      props: {
                        onChange: (value) => {
                          this.namespaceChanged(value);
                        },
                      },
                    })}
                    disabled={this.props.disabled}
                    dataSource={namespaceList}
                    state={nsLoading ? 'loading' : null}
                    style={{ width: '100%' }}
                    showSearch
                  />

                  <div className="flex">
                    <TextRefreshButton onClick={() => this.getNamespaceList()} />
                    <ExternalLink
                      className="ml-l"
                      label={intl('saenext.NamespaceField.micro-app.Namespace.CreateANamespace')}
                      url={`${window.location.origin}/${this.regionId}/namespace`}
                    />
                  </div>
                </Form.Item>
                <Form.Item
                  required
                  label={intl('saenext.NamespaceField.micro-app.Namespace.Vpc')}
                  className="w-50"
                >
                  <Select
                    disabled
                    dataSource={vpcList}
                    {...init('VpcId')}
                    style={{ width: '100%' }}
                  />

                  {isLinkVpc ? (
                    <div style={{ cursor: 'not-allowed' }} className="color-text-description">
                      <span style={{ wordBreak: 'break-all' }}>
                        {intl('saenext.NamespaceField.micro-app.Namespace.BindVpc')}
                      </span>
                      <span className="ml-s">
                        <Icon type="edit" size="xs" />
                      </span>
                    </div>
                  ) : (
                    <div onClick={this.showSlide} className="color-primary pointer">
                      <span style={{ wordBreak: 'break-all' }}>
                        {intl('saenext.NamespaceField.micro-app.Namespace.BindVpc')}
                      </span>
                      <span className="ml-s">
                        <Icon type="edit" size="xs" />
                      </span>
                    </div>
                  )}
                </Form.Item>
              </div>
              <div>
                <Form.Item
                  required
                  requiredMessage={intl(
                    'saenext.NamespaceField.micro-app.Namespace.SelectSwitchVswitch',
                  )}
                  label={
                    <UnAuthedLabel
                      text={intl('saenext.NamespaceField.micro-app.Namespace.VSwitch')}
                      authed={this.state.authedVPC}
                      authKey="AliyunVPCReadOnlyAccess"
                    />
                  }
                  className="w-50"
                  extra={handleVswitchOnlyOneTip()}
                  validator={(rule, value: any[], callback: any) => {
                    if (value.length === 0) {
                      callback(
                        intl('saenext.NamespaceField.micro-app.Namespace.SelectSwitchVswitch'),
                      );
                    } else if (value.length > 5) {
                      callback(intl('saenext.NamespaceField.micro-app.Namespace.YouCanSelectUpTo'));
                    } else {
                      callback();
                    }
                  }}
                >
                  <Select
                    mode="multiple"
                    dataSource={vswitchList}
                    itemRender={(item) => {
                      return (
                        <div>
                          <span>{item.label}</span>
                          <ExternalLink
                            className="ml-s"
                            url={`${CachedData.confLink('feature:vpc:url')}/vpc/${
                              this.regionId
                            }/switches/${item.value}`}
                          />
                        </div>
                      );
                    }}
                    filterLocal={false}
                    showSearch
                    onSearch={_.debounce((val) => this.getVswitches(vpcId, val))}
                    onBlur={() => this.getVswitches(vpcId)}
                    state={vswitchLoading ? 'loading' : null}
                    name="VSwitchId"
                    style={{ width: '100%' }}
                    placeholder={intl(
                      'saenext.NamespaceField.micro-app.Namespace.WeRecommendThatYouSelect',
                    )}
                  />

                  <div className="flex">
                    <TextRefreshButton onClick={() => this.getVswitches(vpcId)} />
                    <ExternalLink
                      className="ml-l"
                      label={intl('saenext.NamespaceField.micro-app.Namespace.CreateASwitch')}
                      // @ts-ignore
                      url={`${CachedData.confLink('feature:vpc:url')}/vpc/${
                        this.regionId
                      }/switches`}
                    />
                  </div>
                </Form.Item>
                <Form.Item
                  required
                  requiredMessage={intl(
                    'saenext.NamespaceField.micro-app.Namespace.SelectASecurityGroup',
                  )}
                  label={intl('saenext.NamespaceField.micro-app.Namespace.SecurityGroup')}
                  className="w-50"
                >
                  <Select
                    dataSource={securityGroupsList}
                    itemRender={(item) => (
                      <div>
                        <span>{item.label}</span>
                        <ExternalLink
                          className="ml-s"
                          url={`${CachedData.confLink(
                            'feature:ecs:url',
                          )}/securityGroupDetail/region/${this.regionId}/groupId/${
                            item.value
                          }/detail`}
                        />
                      </div>
                    )}
                    showSearch
                    state={securityGroupsLoading ? 'loading' : null}
                    onBlur={() => this.getSecurityGrouponList(vpcId)}
                    onSearch={_.debounce((val) => {
                      this.getSecurityGrouponList(vpcId, val);
                    }, 300)}
                    filterLocal={false}
                    name="SecurityGroupId"
                    style={{ width: '100%' }}
                  />

                  <div className="flex">
                    <TextRefreshButton onClick={() => this.getSecurityGrouponList(vpcId)} />
                    <ExternalLink
                      className="ml-l"
                      label={intl(
                        'saenext.NamespaceField.micro-app.Namespace.CreateASecurityGroup',
                      )}
                      url={`${CachedData.confLink('feature:ecs:url')}/#/securityGroup/region/${
                        this.regionId
                      }/create`}
                    />
                  </div>
                </Form.Item>
              </div>
            </Form>
          </div>
        )}

        <SlidePanel
          title={intl('saenext.NamespaceField.micro-app.Namespace.BindVpc')}
          isShowing={isShowing}
          width="large"
          isProcessing={isProcessing}
          onClose={this.closeSlide}
          onCancel={this.closeSlide}
          onOk={this.handleUpdateVpc}
        >
          <Form field={this.slideField}>
            <Form.Item
              required
              label={intl('saenext.NamespaceField.micro-app.Namespace.Namespace')}
            >
              <Input readOnly value={this.state.namespaceName} />
            </Form.Item>
            <Form.Item required label="VPC">
              <Select
                {...this.slideField.init('VpcId', {
                  rules: [
                    {
                      required: true,
                      message: intl('saenext.NamespaceField.micro-app.Namespace.SelectVpc'),
                    },
                  ],
                })}
                showSearch
                dataSource={bindVpcList}
                state={vpcLoading ? 'loading' : null}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Form>
        </SlidePanel>
      </>
    );
  }
}

export default NameSpace;
