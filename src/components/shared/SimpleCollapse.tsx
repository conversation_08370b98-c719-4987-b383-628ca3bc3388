import { Icon } from '@ali/cnd';
import React, { useState } from 'react'

const SimpleCollapse = (props) => {
  const {
    text = '',
    defaultOpen = false,
    lazyLoad = true,
    children,
    type = ''
  } = props;

  const [open, setOpen] = useState(defaultOpen);

  return (
    <>
      <div
        className={`pointer ${type === 'primary' ? 'color-primary' : ''}`} 
        onClick={() => setOpen(!open)}
      >
        {text}
        <span
          className='ml-s'
        >
          {
            open ?
              <Icon
                type="arrow-up"
                size='xs'
              />
              :
              <Icon
                type="arrow-down"
                size='xs'
              />
          }
        </span>
      </div>
      {lazyLoad && open && children}
      {!lazyLoad && <>
        <div className={open ? 'block' :'none'}>
          {children}
        </div>
      </>}
    </>
  )
}

export default SimpleCollapse