import React, { useState } from 'react';

const TextSnippet = ({ text, maxLength }) => {
  const [isHovered, setIsHovered] = useState(false);

  const truncatedText = text.length > maxLength ? text.slice(0, maxLength) + '...' : text;

  return (
    <div
      style={{ cursor: 'pointer' }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {isHovered ? text : truncatedText}
    </div>
  );
};

export default TextSnippet;