import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Form, Switch } from '@ali/cnd';
import services from '~/services';
import C from '~/constants/common';
import If from '~/components/shared/If';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';
import CachedData from '~/cache/common';

const AhasRateLimit = (props) => {
  const {
    appId,
    regionId,
    defaultEnable,
    value,
  } = props;

  const [existsSdkData, setExistsSdkData] = useState(false);
  const ahasOpen = CachedData.getOpenStatus('ahas') || CachedData.getOpenStatus('ahaspro');

  useEffect(() => {
    getAppSdkData();
  }, []);

  const getAppSdkData = async () => {
    const res = await services.SentinelListTopNAppsSummaryMetricOfAppType({
      params: {
        searchKey: appId,
        appTypes: [0, 1, 11, 12],
        ahasRegionId: regionId,
        namespace: 'default',
        pageIndex: 1,
        pageSize: 6,
      },
      ignoreError: true,
    });
    if (res?.Data?.totalCount > 0) {
      setExistsSdkData(true);
    }
  };

  return (
    <>
      <If condition={!ahasOpen}>
        <OpenStatusMessage product="ahas" />
      </If>
      <Form.Item
        {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        label={intl('saenext.shared.AhasRateLimit.EnableThrottlingAndDegradation')}
        size="small"
        help={
          <>
            <If condition={!defaultEnable && !existsSdkData}>
              {intl('saenext.shared.AhasRateLimit.WhenEnabledSaeAutomaticallyHas')}
            </If>
            <If condition={!value && existsSdkData}>
              {intl('saenext.shared.AhasRateLimit.TheApplicationHasBeenConnected')}
            </If>
            <If condition={defaultEnable && value}>
              {intl('saenext.shared.AhasRateLimit.WhenThrottlingDegradationIsDisabled')}
            </If>
          </>
        }
      >
        <Switch name="EnableAhas" disabled={(!value && existsSdkData) || !ahasOpen} />
      </Form.Item>
    </>
  );
};

export default AhasRateLimit;
