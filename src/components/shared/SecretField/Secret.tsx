import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import { Button, Field, Form, Icon, intl } from '@ali/cnd';
import { map, get, isEmpty, noop, find, keys, debounce, forEach } from 'lodash';
import * as services from '~/services';
import { TableField, TextField, SelectField } from '@ali/deep';
import RefreshButton from '../RefreshButton';
import ExternalLink from '../ExternalLink';
import SecretEditSlideButton from '../../namespace/secret/components/SecretEditSlideButton';
import { ESecretType, SECRET_TYPE_MAP } from '../../namespace/secret/constants';
import { jsonParse } from '~/utils/transfer-data';

type IListProps = {
  namespaceId: string;
  onChange?: Function;
  value: any;
  isPreview?: boolean;
};

// 环境变量配置列表
const Secret = (props: IListProps, ref) => {
  const { namespaceId, onChange = noop, value, isPreview } = props;
  const [secretList, setSecretList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [secretMap, setSecretMap] = useState({});
  const tableRef = useRef(null);

  const [regionId] = namespaceId.split(':');

  const field = Field.useField({
    onChange: () => {
      debounce(fieldValueChange, 500)();
    },
  });

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [value],
  );

  useEffect(() => {
    getSecretList();
  }, []);

  useEffect(() => {
    if (!isEmpty(secretMap)) {
      initValue();
    }
  }, [value, secretMap]);

  const fieldValueChange = () => {
    const { getValues } = field;
    const { secretList } = getValues() as any;
    const _secretList = map(secretList, (item) => {
      const { SecretId, Key, MountPath } = item;
      return {
        SecretId,
        SecretName: secretMap[SecretId]?.SecretName,
        Key,
        MountPath,
      };
    });
    onChange(JSON.stringify(_secretList));
  };

  const initValue = () => {
    let _secretList = [];
    if (value) {
      if (typeof value === 'string') {
        _secretList = jsonParse(value);
      } else {
        _secretList = value;
      }
    }
    const secretList = map(_secretList, (item) => ({
      ...item,
      SecretType: SECRET_TYPE_MAP[secretMap[item.SecretId]?.SecretType],
      readOnly: intl('saenext.shared.ConfigManageField.ConfigManage.ReadOnly'),
    }));
    field.setValues({ secretList });
    setTimeout(() => {
      const formGroupIds = tableRef.current.getItems?.();
      forEach(formGroupIds, (formGroupId) => {
        const itemField = tableRef.current.getField(formGroupId);
        const SecretId = itemField.getValue(`${formGroupId}.SecretId`);
        getSecretDetail(formGroupId, SecretId);
      });
    });
  };

  const getSecretList = async () => {
    setLoading(true);
    const { Data } =
      (await services.ListSecrets({
        NamespaceId: namespaceId,
      })) || {};
    const { Secrets = [] } = Data;
    let _secretMap = {};
    forEach(Secrets, (secret) => {
      _secretMap[secret.SecretId] = secret;
    });
    setSecretMap(_secretMap);
    setSecretList(Secrets);
    setLoading(false);
  };

  const handleSecretIdChange = ({ formGroupId, value }) => {
    // 清空 键
    tableRef.current.getComponent(formGroupId, 'Key').setValue('');
    tableRef.current
      .getComponent(formGroupId, 'SecretType')
      .setValue(SECRET_TYPE_MAP[secretMap[value]?.SecretType]);
    getSecretDetail(formGroupId, value);
  };

  const getSecretDetail = async (formGroupId, SecretId: string) => {
    if (!SecretId) return;
    tableRef.current.setComponentProps(formGroupId, 'Key', {
      state: 'loading',
    });
    const { Data = {} } = await services.DescribeSecret({
      NamespaceId: namespaceId,
      SecretId,
    });
    const { SecretData, SecretType } = Data;
    const dataSource = handleKeySource(SecretData, SecretType);
    tableRef.current.setComponentProps(formGroupId, 'Key', {
      state: null,
      dataSource: [
        {
          label: intl('saenext.shared.ConfigManageField.ConfigManage.All'),
          value: 'sae-sys-secret-all',
        },
        ...dataSource,
      ],
    });
  };

  const handleKeySource = (data, type) => {
    let dataSource = [];
    switch (type) {
      case ESecretType.Opaque:
        const opaque_keys = keys(data) || [];
        dataSource = map(opaque_keys, (key) => ({
          label: key,
          value: key,
        }));
        break;
      case ESecretType.DockerConfigJson:
        dataSource = [
          {
            label: '.dockerconfigjson',
            value: '.dockerconfigjson',
          },
        ];

        break;
      case ESecretType.TLS:
        dataSource = [
          {
            label: 'tls.crt',
            value: 'tls.crt',
          },
          {
            label: 'tls.key',
            value: 'tls.key',
          },
        ];

        break;
      default:
        break;
    }
    return dataSource;
  };

  const tableValidation = (value = []) => {
    for (let i = 0; i < value.length; i++) {
      const item = value[i];
      if (!item) {
        return intl('saenext.shared.SecretField.Secret.IncompleteDataConfiguration');
      }
      if (!item.SecretId || !item.Key || !item.MountPath) {
        return intl('saenext.shared.SecretField.Secret.IncompleteDataConfiguration');
      }
    }
    return true;
  };

  // 校验
  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((error, value) => {
        if (error) {
          resolve(' ');
          return;
        }
        resolve(true);
      });
    });
  };

  return (
    <Form field={field} labelAlign="inset">
      <div className="mb-l">
        <SecretEditSlideButton
          buttonText={intl('saenext.shared.SecretField.Secret.CreateASecret')}
          buttonType="secondary"
          disabled={secretList?.length >= 20 || isPreview}
          type="create"
          onSuccess={getSecretList}
          namespaceId={namespaceId}
        />

        <a className="ml-s" target="_blank" href={`/${regionId}/config-management/secret?namespaceId=${namespaceId}`}>
          <Button type="secondary">
            {intl('saenext.shared.SecretField.Secret.ManageConfidentialDictionaries')}
          </Button>
        </a>
      </div>
      <TableField
        name="secretList"
        className="full24-table"
        layout="TABLER"
        showIndex={false}
        showTableHead={true}
        showSortable={false}
        minItems={0}
        addButtonText={intl('saenext.shared.SecretField.Secret.Add')}
        showDeleteConfirm={false}
        actionsColumnWidth={80}
        delButtonText={<Icon type="delete" />}
        ref={(c) => (tableRef.current = c?.getInstance?.() || c)}
        validation={[
          {
            type: 'customValidate',
            param: tableValidation,
          },
        ]}
      >
        <SelectField
          name="SecretId"
          label={
            <div className="flex">
              <span>{intl('saenext.shared.SecretField.Secret.SecretDictionaryName')}</span>
              <RefreshButton className="ml-s" handler={getSecretList} />
            </div>
          }
          dataSource={map(secretList, (obj) => ({
            label: obj.SecretName,
            value: obj.SecretId,
          }))}
          itemRender={(item) => {
            return (
              <div>
                <span>{item.label}</span>
                <ExternalLink
                  className="ml-s"
                  url={`/${regionId}/config-management/secret?namespaceId=${namespaceId}`}
                />
              </div>
            );
          }}
          state={loading ? 'loading' : null}
          onChange={(item) => handleSecretIdChange(item)}
        />

        <SelectField label={intl('saenext.shared.ConfigManageField.ConfigManage.Key')} name="Key" />
        <TextField
          name="MountPath"
          label={intl('saenext.shared.ConfigManageField.ConfigManage.MountPath')}
          placeholder={intl('saenext.shared.ConfigManageField.ConfigManage.ExampleOrXxXxx')}
        />

        <TextField
          label={intl('saenext.shared.SecretField.Secret.SecretDictionaryType')}
          behavior="READONLY"
          name="SecretType"
        />
        <TextField
          label={intl('saenext.shared.ConfigManageField.ConfigManage.Permission')}
          value={intl('saenext.shared.ConfigManageField.ConfigManage.ReadOnly')}
          behavior="READONLY"
          name="readOnly"
        />
      </TableField>
    </Form>
  );
};

export default forwardRef(Secret);
