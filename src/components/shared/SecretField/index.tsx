import React, { Component } from 'react';
import Secret from './Secret';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';

type Props = {
  value: Object;
  onChange: Function;
  namespaceId: string;
};

class SecretFieldClass extends Component<Props> {
  private secretRef = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return this.secretRef.validate();
  };

  render() {
    return <Secret {...this.props} ref={ref => (this.secretRef = ref)} />;
  }
}

class SecretField extends BaseField {
  props;
  static displayName = 'SecretField';

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <SecretFieldClass {...newProps} />;
  }
}

export default ConfigProvider.config(SecretField as any);
