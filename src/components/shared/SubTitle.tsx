import React, { memo } from 'react';
import { Icon } from '@ali/cnd';

const SubTitle = ({
  title = '',
  subTitle = '',
  linkText = '',
  linkUrl = '',
  id = ''
}) => {

  const handleClick = (event) => {
    event.stopPropagation();
  };

  return (
    <div>
      <span id={id} className="text-bold">
        {title}
      </span>
      {subTitle &&
        <span className="text-description ml-l">{subTitle}</span>
      }
      {linkText &&
        <a href={linkUrl} onClick={handleClick} target="_blank" className="ml-l">
          {/* <Icon type="connection" size="small" className='mr-s'/> */}
          {linkText}
        </a>
      }
    </div>
  );
};

export default memo(SubTitle);