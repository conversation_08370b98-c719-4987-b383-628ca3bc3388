import { intl } from '@ali/cnd';
/**
 * 异常页面
 *
 * 1、公测白名单功能 非白名单用户阻止访问
 * 2、浏览器插件
 * 3、网络问题
 */

import React, { PureComponent } from 'react';
import { Card, Grid } from '@ali/cnd';
import { get } from 'lodash';
import CachedData from '~/cache/common';
import { trackWhiteScreenErr } from '~/tracker';

const { Row, Col } = Grid;

type Props = {
  showNothing?: boolean;
  children?: any;
};

type State = {
  hasError: boolean;
  isNotOpen: boolean;
};

class ErrorBoundary extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      isNotOpen: false,
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  static getCurrentUserId() {
    return (
      get(window, `ALIYUN_CONSOLE_CONFIG.CURRENT_PK`) ||
      get(window, `ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK`)
    );
  }

  componentDidMount(): void {
    const betaUsers = get(window, 'ALIYUN_CONSOLE_GLOBAL.greyUid', []);
    const currentUser = CachedData.getCurrentUserId();
    // 如果 greyUid 不设置 或 betaUsers 为 []，则认为不使用白名单功能
    if (!betaUsers.length) return;
    if (!currentUser) return;
    if (!betaUsers.includes(currentUser)) {
      this.setState({
        isNotOpen: true,
      });
    }
  }

  componentDidCatch(error, errorInfo) {
    const { showNothing = false } = this.props;

    trackWhiteScreenErr({
      pathname: window.location.pathname,
      search: window.location.search,
      error: error?.toString(),
      extra: JSON.stringify(errorInfo),
    })

    if (showNothing) return;
    // 关注错误日志
  }

  render() {
    const { hasError, isNotOpen } = this.state;
    const { showNothing = false } = this.props;
    if ((hasError || isNotOpen) && !showNothing) {
      let title = intl('saenext.components.shared.ErrorBoundary.PageExceptionsMayBeCaused');
      let describe = intl(
        'saenext.components.shared.ErrorBoundary.PageLoadingExceptionTryRefreshing',
      );
      if (isNotOpen) {
        title = intl('saenext.components.shared.ErrorBoundary.TheProductHasNotBeen');
        describe = intl(
          'saenext.components.shared.ErrorBoundary.ServerlessApplicationEngineSaeServerless',
        );
      }
      return (
        <div className="p-50">
          <Card contentHeight="auto">
            <Row className="mt-xl">
              <Col span="24">
                <div className="boundary-container">
                  <div className="boundary-image-container">
                    <div className="boundary-image" />
                  </div>
                  <div className="boundary-text">
                    <div
                      className="text-large mb-xl text-bold"
                      dangerouslySetInnerHTML={{ __html: title }}
                    ></div>
                    <div className="text-description mb-xl" style={{ minHeight: 50 }}>
                      {describe}
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
