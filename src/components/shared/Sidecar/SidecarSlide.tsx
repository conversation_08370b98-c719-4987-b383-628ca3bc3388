import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import AddPropsWrap from '../AddPropsWrap';
import { Field, SlidePanel } from '@ali/cnd';
import If from '../If';
import SidecarForm from './SidecarForm';
import { cloneDeep } from 'lodash';

const SidecarSlide = (props) => {
  const { children, value, onChange, appConfig, sidecarNames, isPreview } = props;
  const [show, setShow] = useState(false);

  const field = Field.useField({
    parseName: true,
  });

  useEffect(() => {
    if (show) {
      field.setValues(cloneDeep(value));
    } else {
      field.resetToDefault();
    }
  }, [show]);

  const onSubmit = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) return;

    onChange(cloneDeep(values));
    setShow(false);
  };

  const toggleVisible = () => {
    setShow(!show);
  };

  return (
    <>
      <AddPropsWrap onClick={toggleVisible}>{children}</AddPropsWrap>
      <SlidePanel
        title={intl('saenext.shared.Sidecar.SidecarSlide.SidecarContainer')}
        isShowing={show}
        width={1000}
        onMaskClick={toggleVisible}
        onClose={toggleVisible}
        onCancel={toggleVisible}
        onOk={onSubmit}
        okProps={{
          className: isPreview ? 'none' : '',
        }}
        cancelProps={{
          disabled: false,
        }}
      >
        <If condition={show}>
          <SidecarForm
            field={field}
            value={value}
            appConfig={appConfig}
            sidecarNames={sidecarNames}
            isPreview={isPreview}
          />
        </If>
      </SlidePanel>
    </>
  );
};

export default SidecarSlide;
