import { intl } from '@ali/cnd';
import React from 'react';
import ValueTable from '../ValueTable';
import { Button, Form, Icon, Input } from '@ali/cnd';
import { cloneDeep } from 'lodash';

const SharedStorage = (props) => {
  const { field, name } = props;

  const value = field.getValue(name);

  const columns = ({ onDelete, onItemChange }) => [
    {
      dataIndex: 'Name',
      title: intl('saenext.shared.Sidecar.SharedStorage.TemporaryStorageName'),
      width: 180,
      cell: (val, idx, record) => (
        <Form.Item
          required
          requiredMessage={intl('saenext.shared.Sidecar.SharedStorage.EnterATemporaryStorageName')}
        >
          <Input className="full-width" name={`${name}.${idx}.Name`} />
        </Form.Item>
      ),
    },
    {
      dataIndex: 'MountPath',
      title: intl('saenext.shared.Sidecar.SharedStorage.ContainerPath'),
      width: 180,
      cell: (val, idx, record) => (
        <Form.Item
          required
          requiredMessage={intl('saenext.shared.Sidecar.SharedStorage.EnterTheContainerPath')}
        >
          <Input className="full-width" name={`${name}.${idx}.MountPath`} />
        </Form.Item>
      ),
    },
    {
      title: intl('saenext.shared.Sidecar.SharedStorage.Operation'),
      width: 40,
      cell: (val, idx, record) => {
        return (
          <Button
            text
            className="scale-medium"
            onClick={() => {
              onDelete(idx);
            }}
          >
            <Icon type="delete" />
          </Button>
        );
      },
    },
  ];

  return <ValueTable field={field} name={name} value={value} maxLength={20} columns={columns} />;
};

export default SharedStorage;
