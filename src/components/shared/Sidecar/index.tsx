import React from 'react';
import { Actions, Button, Form, Icon, Input, intl, LinkButton } from '@ali/cnd';
import ValueTable from '../ValueTable';
import SidecarSlide from './SidecarSlide';
import DecimalNumberPicker from './DecimalNumberPicker';
import Truncate from '@alicloud/console-components-truncate';

const Sidecar = (props) => {
  const { field, name, value, onChange, appConfig, isPreview } = props;

  const sidecarNames = value.map((item) => item.Name) as any[];

  const columns = ({ onDelete, onItemChange }) => [
    {
      dataIndex: 'Name',
      title: intl('saenext.shared.Sidecar.ContainerName'),
      width: 190,
    },
    {
      dataIndex: 'ImageUrl',
      title: intl('saenext.shared.Sidecar.Image'),
      width: 515,
      cell: (val, idx, record) => (
        <Truncate type="length" threshold={80}>
          {val}
        </Truncate>
      ),
    },
    {
      dataIndex: 'Cpu',
      width: 140,
      title: intl('saenext.shared.Sidecar.MaximumCpuResources'),
      cell: (val, idx, record) => (
        <div className="flex">
          <DecimalNumberPicker value={val} ratio={1000} precision={2} isPreview />

          <span className="ml-xs">{intl('saenext.shared.Sidecar.Nuclear')}</span>
        </div>
      ),
    },
    {
      dataIndex: 'Memory',
      width: 140,
      title: intl('saenext.shared.Sidecar.MaximumMemoryResources'),
      cell: (val, idx, record) => (
        <div className="flex">
          <DecimalNumberPicker
            value={val}
            ratio={1024}
            precision={2}
            addonTextAfter={'GB'}
            isPreview
          />

          <span className="ml-xs">GB</span>
        </div>
      ),
    },
    {
      title: intl('saenext.shared.Sidecar.Operation'),
      width: 100,
      cell: (val, idx, record) => {
        return (
          <Actions>
            <SidecarSlide
              value={record}
              onChange={(val) => onItemChange(idx, val)}
              appConfig={appConfig}
              sidecarNames={sidecarNames.filter((_, index) => index !== idx)}
              isPreview={isPreview}
            >
              <LinkButton>
                {isPreview
                  ? intl('saenext.shared.Sidecar.View')
                  : intl('saenext.shared.Sidecar.Edit')}
              </LinkButton>
            </SidecarSlide>
            <LinkButton
              className={isPreview ? 'none' : ''}
              onClick={() => {
                onDelete(idx);
              }}
            >
              {intl('saenext.shared.Sidecar.Delete')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];

  const addRender = (onAdd) => {
    return (
      <SidecarSlide value={{}} onChange={onAdd} appConfig={appConfig} sidecarNames={sidecarNames}>
        <Button className="mt-s">
          <Icon type="add" />
          <span>{intl('saenext.shared.ValueTable.Add')}</span>
        </Button>
      </SidecarSlide>
    );
  };

  return (
    <ValueTable
      value={value}
      onChange={onChange}
      maxLength={20}
      columns={columns}
      addRender={addRender}
    />
  );
};

export default Sidecar;
