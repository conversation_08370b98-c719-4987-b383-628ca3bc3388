import { NumberPicker } from '@ali/cnd';
import React from 'react';

const DecimalNumberPicker = (props) => {
  const { value, onChange, precision = 2, ratio, ...rest } = props;

  const scale = Math.pow(10, precision);

  const decimalValue = value === undefined ? undefined : Math.round(value / ratio * scale) / scale;

  const onDecimalChange = (val) => {
    const intVal = val === undefined ? undefined : Math.floor(val * ratio);
    onChange(intVal);
  }

  return (
    <NumberPicker
      {...rest}
      value={decimalValue}
      onChange={onDecimalChange}
      precision={precision}
    />
  )
}

export default DecimalNumberPicker;