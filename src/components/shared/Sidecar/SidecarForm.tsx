import React, { useRef } from 'react';
import { Form, Input, intl, Message } from '@ali/cnd';
import C from '~/constants/common';
import SimpleCollapse from '../SimpleCollapse';
import CollapseField from '../CollapseField';
import VariableField from '~/components/shared/VariableField/VariableField';
import ConfigManageField from '~/components/shared/ConfigManageField/index_back';
import DecimalNumberPicker from './DecimalNumberPicker';
import SharedStorage from './SharedStorage';
import CommandArgsForm from '../CommandArgsForm';
import { ImageConfigField } from './ImageConfig';
import { Type } from '~/components/shared/ImageSelectorField/utils';
import CachedData from '~/cache/common';

const SidecarForm = (props) => {
  const { field, value, appConfig, sidecarNames, isPreview } = props;

  const variableRef = useRef(null);
  const configRef = useRef(null);

  const nameValidator = (rule, value, callback) => {
    const isExist = sidecarNames.includes(value);
    if (isExist) {
      callback('duplicate');
    } else {
      callback();
    }
  };

  const nameErr = field.getError('Name');

  return (
    <Form field={field} {...C.FORM_LAYOUT_LEFT} useLabelForErrorMessage>
      <Form.Item
        label={intl('saenext.shared.Sidecar.SidecarForm.ContainerName')}
        labelAlign="top"
        required
        pattern={/^[a-z]([a-z0-9\-]{0,31})$/}
        validator={nameValidator}
        help={
          JSON.stringify(nameErr) === '["duplicate"]'
            ? intl('saenext.shared.Sidecar.SidecarForm.TheContainerNameCannotBe')
            : intl('saenext.shared.Sidecar.SidecarForm.ItMustStartWithA')
        }
      >
        <Input name="Name" />
      </Form.Item>
      <Form.Item wrapperCol={{ span: 24 }}>
        <ImageConfigField field={field} isPreview={isPreview} appConfig={appConfig} hideSecret supportType={[Type.cr, Type.demo]} />
      </Form.Item>
      <Form.Item
        labelAlign="top"
        label={intl('saenext.shared.Sidecar.SidecarForm.ContainerResourceSettings')}
        help={
          <Message type="notice">
            {intl('saenext.shared.Sidecar.SidecarForm.SidecarTheContainerSharesCpu')}
          </Message>
        }
      ></Form.Item>
      <Form.Item required label={intl('saenext.shared.Sidecar.SidecarForm.MaximumCpuResources')}>
        <DecimalNumberPicker
          name="Cpu"
          defaultValue={appConfig.Cpu / 2}
          ratio={1000}
          min={0.01}
          max={appConfig.Cpu / 1000}
          precision={2}
          innerAfter={intl('saenext.shared.Sidecar.SidecarForm.Nuclear')}
          hasTrigger={false}
        />
      </Form.Item>
      <Form.Item required label={intl('saenext.shared.Sidecar.SidecarForm.MaximumMemoryResources')}>
        <DecimalNumberPicker
          name="Memory"
          defaultValue={appConfig.Memory / 2}
          ratio={1024}
          min={0.01}
          max={appConfig.Memory / 1024}
          precision={2}
          innerAfter={'GB'}
          hasTrigger={false}
        />
      </Form.Item>

      <SimpleCollapse
        text={intl('saenext.shared.Sidecar.SidecarForm.AdvancedSettings')}
        defaultOpen={false}
        lazyLoad={false}
      >
        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.StartCommand')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.SetCommandsRequiredForContainer',
          )}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetStartupCommands')}
          linkHref={CachedData.confLink('help:sae:set-startup-command-2-0')}
          className="mt-l ml-l mr-l"
        >
          <CommandArgsForm
            field={field}
          />
        </CollapseField>

        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.EnvironmentVariables')}
          subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.SetSomeVariablesInThe')}
          linkText={intl(
            'saenext.app-create.micro-app.AdvanceCreator.HowToSetEnvironmentVariables',
          )}
          linkHref={CachedData.confLink('help:sae:configure-environment-variables-2-0')}
          className="mt-l ml-l mr-l"
        >
          <VariableField
            name="Envs"
            defaultValue={'[]'}
            namespaceId={appConfig.NamespaceId}
            hideSecret={true}
            ref={variableRef}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return variableRef.current?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
            operateType="edit"
            // isPreview={isPreview}
          />
        </CollapseField>

        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.ConfigurationManagement')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.InjectConfigurationInformationIntoThe',
          )}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToManageConfigurations')}
          linkHref={CachedData.confLink('help:sae:injection-configuration-information')}
          className="mt-l ml-l mr-l"
        >
          <ConfigManageField
            name="ConfigMapMountDesc"
            namespaceId={appConfig.NamespaceId}
            ref={configRef}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return configRef.current?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        </CollapseField>

        <CollapseField
          title={intl('saenext.shared.Sidecar.SidecarForm.SharedTemporaryStorage')}
          subTitle={intl('saenext.shared.Sidecar.SidecarForm.SetTheTemporaryStorageDirectory')}
          className="mt-l ml-l mr-l"
        >
          <SharedStorage field={field} name={'EmptyDirDesc'} />
        </CollapseField>
      </SimpleCollapse>
    </Form>
  );
};

export default SidecarForm;
