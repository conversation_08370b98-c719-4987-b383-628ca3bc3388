import React, { useEffect, useRef, useState } from 'react';
import { Form, Input, intl, Message, Tab } from '@ali/cnd';
import { isEmpty } from 'lodash'
import Cr from '../ImageSelectorField/cr';
import parseUrl, { Type } from '../ImageSelectorField/utils';
import Custom from '../ImageSelectorField/custom';
import useRegion from '~/hooks/useRegion';
import flatFieldValue from '~/hoc/flatFieldValue';
import Demo from '../ImageSelectorField/demo';
import OtherAccount from '../ImageSelectorField/OtherAccount';

const ImageConfig = props => {
  const {
    value = {},
    onChange,
    appConfig,
    isPreview,
    hideSecret,
    supportType = [],
    appType = 'micro',
    namespaceId = '',
    defaultValue = {}
  } = props;
  const { VpcId } = appConfig;

  const [initImageConfig, setInitImageConfig] = useState<any>({});
  const [imageConfig, setImageConfig] = useState<any>({});
  const [type, setType] = useState<Type>(supportType[0] || Type.cr);

  const regionId = useRegion();

  const ref = useRef(null);

  useEffect(() => {
    if (!isEmpty(defaultValue)) {
      handleValue(true,defaultValue);
    }
  }, [JSON.stringify(defaultValue)]);

  useEffect(() => {
    handleValue(false,value);
  }, [JSON.stringify(value)]);

  const handleValue = (isInit, info) => {
    let { ImageUrl = '', type, AcrAssumeRoleArn, ImagePullSecrets, AcrInstanceId } = info;
    if (!ImageUrl) {
      setImageConfig({});
      return;
    }

    const result = parseUrl(regionId, ImageUrl);
    let activeType = type || result.type || Type.cr;
    let imageConfig = {};
    if (activeType === Type.cr || activeType === Type.demo) {
      imageConfig = result.parseResult;
    } else if (activeType === Type.otherAccount) {
      imageConfig = {
        image: ImageUrl,
        acrAssumeRoleArn: AcrAssumeRoleArn,
        imagePullSecrets: ImagePullSecrets,
        instanceId: AcrInstanceId,
      };
    } else {
      imageConfig = {
        image: ImageUrl,
      };
    }
    if (isInit) {
      setInitImageConfig(imageConfig);
      setImageConfig(imageConfig);
    } else {
      setImageConfig(imageConfig);
    }
    setType(activeType);
  };

  const onTabChange = (key: Type) => {
    setType(key);
    onChange({
      type: key,
      ImageUrl: (key === Type.custom|| key === Type.otherAccount) ? value.ImageUrl : undefined,
    });
  };
  const onImageChange = async val => {
    if (val?.error) {
      Message.error(val?.error);
      return;
    }
    const { image,enableImageAccl, instanceId,acrAssumeRoleArn,imagePullSecrets} = val;

    const params = {
      ImageUrl: image,
      AcrInstanceId: instanceId,
      AcrAssumeRoleArn: acrAssumeRoleArn,
      ImagePullSecrets: imagePullSecrets,
      EnableImageAccl: enableImageAccl,
      type,
    };

    onChange(params);
  };

  if (isPreview) {
    return (
      <Form.Item label={intl('saenext.shared.Sidecar.ImageConfig.ImageAddress')}>
        <Input value={value.ImageUrl} />
      </Form.Item>
    );
  }

  return (
    <>
      <Tab
        shape="wrapped"
        activeKey={type}
        onChange={onTabChange}
        unmountInactiveTabs
        contentClassName="mt"
      >
        {supportType.includes(Type.cr) && (
          <Tab.Item
            title={intl('saenext.shared.DeploySelectorField.DeploySelector.MyAlibabaCloudImage')}
            key={Type.cr}
          >
            <Cr
              ref={ref}
              initValue={initImageConfig}
              appType={appType}
              hideAcreeAccelerate
              vpcId={VpcId}
              value={imageConfig}
              onChange={onImageChange}
            />
          </Tab.Item>
        )}
        {supportType.includes(Type.demo) && (
          <Tab.Item
            title={intl('saenext.shared.DeploySelectorField.DeploySelector.DemoImage')}
            key={Type.demo}
          >
            <Demo
              ref={ref}
              initValue={initImageConfig}
              appType={appType}
              value={imageConfig}
              onChange={onImageChange}
            />
          </Tab.Item>
        )}
        {supportType.includes(Type.custom) && (
          <Tab.Item
            title={intl('saenext.shared.DeploySelectorField.DeploySelector.CustomImage')}
            key={Type.custom}
          >
            <Custom
              ref={ref}
              initValue={initImageConfig}
              appType={appType}
              value={imageConfig}
              onChange={onImageChange}
              showOtherAccountTip
              hideSecret={hideSecret}
            />
          </Tab.Item>
        )}
        {supportType.includes(Type.otherAccount) && (
          <Tab.Item
            title={intl(
              'saenext.shared.DeploySelectorField.DeploySelector.PrivateImagesOfOtherAlibaba',
            )}
            key={Type.otherAccount}
          >
            <OtherAccount
              ref={ref}
              initValue={initImageConfig}
              appType={appType}
              value={imageConfig}
              onChange={onImageChange}
              namespaceId={namespaceId}
            />
          </Tab.Item>
        )}
      </Tab>
    </>
  );
};

export default ImageConfig;

export const ImageConfigField = flatFieldValue({
  names: ['ImageUrl', 'AcrInstanceId', 'type', 'AcrAssumeRoleArn','ImagePullSecrets','EnableImageAccl' ],
  nameOptions: value => {
    const { type } = value || {};
    return {
      ImageUrl: {
        rules: [
          {
            required: true,
            message:
              type === Type.custom || type === Type.otherAccount
                ? intl('saenext.shared.Sidecar.ImageConfig.EnterAnImage')
                : intl('saenext.shared.Sidecar.ImageConfig.SelectAnImage'),
          },
        ],
      },
    };
  },
})(ImageConfig);
