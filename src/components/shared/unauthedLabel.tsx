import { intl } from '@ali/cnd';
import React from 'react';
import { Icon, Balloon, Message } from '@ali/cnd';

interface UnAuthedLabelProps {
  text: React.ReactNode;
  authed: boolean;
  unauthedText?: string;
  authKey: string;
}

export const UnAuthedLabel = (props: UnAuthedLabelProps) => {
  const {
    text,
    authKey,
    authed,
    unauthedText = intl('saenext.components.shared.unauthedLabel.Unauthorized'),
  } = props;
  return (
    <span>
      {text}
      {!authed && <span className="text-error ml-s">{unauthedText}</span>}
      {!authed && (
        <Balloon.Tooltip
          align="t"
          trigger={<Icon className="ml-xs text-error" size="small" type="help" />}
        >
          <Message type="warning">
            {intl('saenext.components.shared.unauthedLabel.FailedToObtainData')}
          </Message>
          <p className="mt-s">
            {intl('saenext.components.shared.unauthedLabel.Missing')}
            <span className="text-error">{authKey}</span>
            {intl('saenext.components.shared.unauthedLabel.PolicyPermissionYouMustGrant')}
          </p>
        </Balloon.Tooltip>
      )}
    </span>
  );
};
