import React, { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import { Form, Input, intl, Select, Message } from '@ali/cnd';
import StartCommandArgs from './StartCommandArgs';

const CommandArgsForm = (props) => {
  const { field, commandName = 'Command', argsName = 'CommandArgs', required } = props;

  const { validate } = field || {};

  const [value, setValue] = useState({
    [commandName]: '',
    [argsName]: '',
  });

  const command = field?.getValue(commandName);
  const commandArgs = field?.getValue(argsName);

  useEffect(() => {
    setValue({
      [commandName]: command,
      [argsName]: commandArgs,
    });
    validate?.([commandName, argsName]);
  }, [command, commandArgs]);

  const commandValidator = (rule, val, callback) => {
    if (!val && !!commandArgs) {
      return callback(intl('saenext.components.shared.CommandArgsField.EnterTheStartupCommand'));
    }
    return callback();
  };

  return (
    <>
      <Message type="warning" className="mb-l">
        {intl('saenext.components.shared.CommandArgsForm.BinBashOrBinSh')}
      </Message>
      <StartCommandArgs
        value={value}
        onChange={(val) => {
          field.setValue(commandName, val[commandName]);
          field.setValue(argsName, val[argsName]);
        }}
      />

      <Form.Item validator={commandValidator} style={{ marginBottom: 0 }}>
        <Input name={commandName} className="field-hide" />
      </Form.Item>
      <Form.Item
        style={{ marginBottom: 0 }}
        required={required}
        requiredMessage={intl('saenext.components.shared.CommandArgsField.EnterTheStartupCommand')}
      >
        <Select name={argsName} className="field-hide" />
      </Form.Item>
    </>
  );
};

export default CommandArgsForm;
