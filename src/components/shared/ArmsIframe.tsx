import React from 'react';
import { Loading } from "@ali/cnd";
import _ from 'lodash';
import { getArmsDomain } from '../../utils/global';
import CachedData from '../../cache/common';

type Props = {
  url?: string;
  regionId: string;
  applicationId?: string;
  pageName?: string;
};

type State = {
  loading: boolean;
};

const ConsoleNavHeight = 50;
const BreadcrumbHeight = 48;
const AppPageNavHeight = 56;
const ReservedSpaceHeight = 16;

class ArmsIframe extends React.Component<Props, State> {
  private height: number;
  private iframeRef = React.createRef() as any;
  private armsUrl: string;

  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };

    const { regionId } = this.props;
    this.armsUrl = getArmsDomain(regionId) + '/apm?iframeMode=edas&iframeModeApp=sae';

    // 默认高度
    this.height =  (
      document.documentElement.clientHeight -
      ConsoleNavHeight -
      BreadcrumbHeight -
      AppPageNavHeight -
      ReservedSpaceHeight
    );
  }

  componentDidMount(): void {
    this.setState({
      loading: true,
    });
    window.addEventListener("message", this.onMessage);
  }

  onMessage = (e) => {
    const { type, payload } = e.data || {};
    if (type === 'iframeHeight') {
      const _height = parseInt(payload, 10);
      const iframeHeight = Math.max(this.height, _height);
      // @ts-ignore
      this.iframeRef.style.height = `${iframeHeight}px`;
    }
    if (type === 'iframeLoaded') {
      this.setState({
        loading: false,
      });
    }
  };

  // iframe自带的onload做兜底
  iframeLoaded = () => {
    this.setState({loading: false});
  }

  getIframeSrc = () => {
    let url = this.props.url;
    if (!_.isEmpty(url)) {
      return url;
    }
    const { regionId, applicationId, pageName } = this.props;
    const isChinese = CachedData.isChinese();
    const lang = isChinese ? 'zh' : 'en';
    const params = { lang, pid: applicationId, regionId: regionId };

    const baseUrl = this.armsUrl;

    const paramsOptions = _.map(_.keys(params), key => `&${key}=${params[key]}`).join('');

    return `${baseUrl}${paramsOptions}#/${applicationId}/${pageName}`;
  };

  componentWillUnmount(): void {
    window.removeEventListener("message", this.onMessage);
  }

  render() {
    const { loading } = this.state;
    return (
      <Loading
        visible={loading}
        style={{ width: "100%", minHeight: this.height, position: "relative" }}
      >
        <iframe
          width="100%"
          height={this.height}
          scrolling="no"
          frameBorder={0}
          onLoad={this.iframeLoaded}
          src={this.getIframeSrc()}
          ref={(ref) => this.iframeRef = ref}
        />
      </Loading>
    );
  }
}
export default ArmsIframe;