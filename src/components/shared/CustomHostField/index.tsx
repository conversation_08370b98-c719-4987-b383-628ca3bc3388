import React, { Component } from 'react';
import CustomHost from './CustomHost';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';

type Props = {
  value?: Object;
  onChange?: Function;
};

class CustomHostFieldClass extends Component<Props> {
  private ref = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.ref.validate();
  }

  render() {

    return (
      <CustomHost
        {...this.props}
        ref={(ref) => this.ref = ref}
      />
    )
  }
}

class CustomHostField extends BaseField {
  props;
  static displayName = 'CustomHostField';

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return (
      <CustomHostFieldClass
        {...newProps}
      />
    );
  }
}

export default ConfigProvider.config(CustomHostField as any)
