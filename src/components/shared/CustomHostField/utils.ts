import { intl } from '@ali/cnd';
import _ from 'lodash';
import { jsonParse } from '~/utils/transfer-data';

const formatHosts = (data: string = '') => {
  const dataArr = _.split(data, /(?=\r|\n)\r?\n?/g).filter(Boolean);

  const res = [];
  _.forEach(dataArr, (item = '') => {
    if (item.startsWith('#')) return;
    const [ip, hostName] = _.split(item, /\s+/).filter(Boolean);

    if (ip && hostName) {
      res.push({
        ip,
        hostName,
      });
    }
  });

  return res;
};

const parseHosts = (data: string = '') => {
  if (!data) return;

  const dataArr = jsonParse(data) || [];
  const res = [];
  _.forEach(dataArr, (item) => {
    res.push(`${item.ip} ${item.hostName}`);
  });
  return res.join('\n');
};

// {
//   "HostAliases": [
//     {
//       "Ip": "*************",
//       "Hostnames": [
//         "sae.cn-hangzhou.aliyuncs.com",
//         "sae.cn-hangzhou.alicontainer.com",
//       ]
//     }
//   ]
// }
const formatHostsWeb = (data: string = '') => {
  const dataArr = _.split(data, /(?=\r|\n)\r?\n?/g).filter(Boolean);

  const HostAliases = [];
  _.forEach(dataArr, (item = '') => {
    if (item.startsWith('#')) return;
    const [ip, ...hostnames] = _.split(item, /\s+/).filter(Boolean);
    if (ip && hostnames.length) {
      HostAliases.push({
        ip,
        hostnames,
      });
    }
  });
  return {
    HostAliases,
  };
};

const parseHostsWeb = (data) => {
  const { hostAliases = [] } = data || {};
  const res = [];
  _.forEach(hostAliases, (item) => {
    const { ip, hostnames } = item || {};
    res.push(`${ip} ${hostnames.join(' ')}`);
  });
  return res.join('\n');
};

const validateHostsWeb = (data: string = '') => {
  const dataArr = _.split(data, /(?=\r|\n)\r?\n?/g).filter(Boolean);

  const valid = _.every(dataArr, (item = '') => {
    if (item.startsWith('#')) return true;

    const [ip, ...hostnames] = _.split(item, /\s+/).filter(Boolean);

    const ipRex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipRex.test(ip)) {
      return false;
    }

    if (ip && hostnames.length) {
      return true;
    } else {
      return false;
    }
  });

  if (valid) {
    return true;
  } else {
    return intl('saenext.shared.CustomHostField.utils.CheckTheHostsFormat');
  }
};

export { formatHosts, parseHosts, formatHostsWeb, parseHostsWeb, validateHostsWeb };
