import React, { Component } from 'react';
import { Collapse } from '@ali/cnd';
import SubTitle from '~/components/shared/SubTitle';
import { every, get, isEmpty, some } from 'lodash';
import './index.less';
import { jsonParse } from '~/utils/transfer-data';

const Panel = Collapse.Panel;
type Props = {
  title: string;
  subTitle?: string;
  linkText?: string;
  linkHref?: string;
  className?: string;
  collapsed?: boolean; // 默认打开
};
type State = {
  names: string[];
  // isnormal: boolean;
  expandedKeys?: string[];
};

class CollapseField extends Component<Props, State> {
  private field = null;
  constructor(props) {
    super(props);
    const { collapsed = false } = this.props;
    this.state = {
      names: [],
      // isnormal: true,
      expandedKeys: collapsed ? [] : ['0'],  // 默认打开
    };
    this.onClick = this.onClick.bind(this);
  }


  componentDidMount(): void {
    this.field = this.returnRootField();
    const names = [];
    React.Children.map(this.props.children, (item) => {
      // @ts-ignore
      const { props: reactElementProps } = item || {};
      const { required, name } = reactElementProps || {};
      if (name) {
        names.push(name);
      }
    });
    
    const _setValues = this.field.setValues;
    const setValues = (...args) => {
      _setValues.apply(this.field, args);
      this.setValues(args, names);
    }
    this.field.setValues = setValues;

    const _validate = this.field.validate;
    const validate = async (...args) => {
      const errors = await this.field?.getErrors(names)
      this.validate(errors);
      _validate.apply(this.field, args);
    }
    this.field.validate = validate;
    
    this.setState({
      names,
    });

  }

  returnRootField() {
    let _fieldFiber = get(this, '_reactInternalFiber');
    while (!get(_fieldFiber, 'stateNode._formField')) {
      // @ts-ignore
      _fieldFiber = get(_fieldFiber, 'return');
    }
    return get(_fieldFiber, 'stateNode._formField');
  }

  setValues(values, keys) {
    const isNotDefault = some(keys, (key) => !this.isDefaultValue(get(values[0], key)));
    if (isNotDefault) {
      this.setState({
        expandedKeys: ['0'],
      });
    }
  }

  validate(errors) {
    const isnormal = !some(errors, (value) => !!value);
    this.setState({
      // isnormal: isnormal,
      expandedKeys: isnormal ? this.state.expandedKeys : ['0'],
    });
  }

  isDefaultValue(value) {
    if (isEmpty(value)) {
      return true;
    }
    if (typeof value === 'object' && every(value, (val) => !val)) {
      return true;
    }
    if (typeof value === 'string' && isEmpty(jsonParse(value) || value)) {
      return true;
    }
    
    return false;
  }

  onClick() {
    const { expandedKeys } = this.state;
    this.setState({
      expandedKeys: expandedKeys.length ? [] : ['0']
    })
  }


  render() {
    const { names, expandedKeys } = this.state;
    
    const errors = this.field?.getErrors(names) || {};
    const _isnormal = !some(errors, (value) => !!value);

    const { title, subTitle, linkText, linkHref, className } = this.props;

    return (
      <Collapse 
        expandedKeys={expandedKeys} 
        style={{ border: 'none' }}
        className={className ? `collapse-field ${className}` : `collapse-field`}
      >
        <Panel
          title={
            <SubTitle
              title={title}
              subTitle={subTitle}
              linkText={linkText} 
              linkUrl={linkHref}
            />
          }
          onClick={this.onClick}
          className={ (_isnormal) ? 'normal' : 'error'}
        >
          {this.props.children}
        </Panel>
      </Collapse>
    );
  }
};

export default CollapseField;