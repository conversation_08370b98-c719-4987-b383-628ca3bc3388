import React, { useState, useRef, useEffect, memo } from 'react';
import { Icon } from '@ali/cnd';
import './index.less';

interface MultiLinesCollapseProps {
  children: React.ReactNode;
  maxLines?: number; // 可配置最大行数
  lineHeight?: number; // 每一行的高度
}

const MultiLinesCollapse: React.FC<MultiLinesCollapseProps> = (props) => {
  const {
    children,
    maxLines = 1,
    lineHeight = 18,
  } = props;
  
  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflow, setIsOverflow] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (contentRef.current) {
      const element = contentRef.current;
      // 检查内容是否溢出
      setIsOverflow(
          element.scrollHeight > maxLines * lineHeight
      );
    }
  }, [children, maxLines]);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="multilines-collapse">
      <div
        ref={contentRef}
        className={`multilines-collapse__content ${
          !isExpanded ? 'multilines-collapse__content--collapsed' : ''
        }`}
        style={{ '--max-lines': maxLines } as React.CSSProperties}
      >
        {children}
      </div>
      {isOverflow && (
        <div className="multilines-collapse__action" onClick={toggleExpand}>
          <Icon 
            type={isExpanded ? "arrow-up" : "arrow-down"} 
            size="xs" 
          />
        </div>
      )}
    </div>
  );
};

export default memo(MultiLinesCollapse);