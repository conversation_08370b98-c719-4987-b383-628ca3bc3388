import { intl } from '@ali/cnd';
import { Form, Input, Select } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { X_MULTI_VERSION_ARR } from '~/constants';

export const MULTI_VERSION_KEY = 'x-multi-version';
export const MultiVersionSelector = () => {
  const [currentEnv, setCurrentEnv] = useState(sessionStorage.getItem(MULTI_VERSION_KEY));

  const handleChangeEnv = (val) => {
    setCurrentEnv(val);
    sessionStorage.setItem(MULTI_VERSION_KEY, val);
    setTimeout(() => {
      window.location.reload();
    }, 300);
  };

  return (
    <div
      className="multi-version-selector"
      style={{
        position: 'fixed',
        zIndex: 999,
        left: 20,
        bottom: 50,
      }}
    >
      <Form labelAlign="inset">
        <Form.Item label={intl('saenext.shared.MultiVersionSelector.FrontEndVersion')}>
          <Select
            style={{ minWidth: 120 }}
            showSearch
            dataSource={X_MULTI_VERSION_ARR}
            value={currentEnv}
            onChange={handleChangeEnv}
          />
        </Form.Item>
      </Form>
    </div>
  );
};
