import React, { useState } from 'react';
import { Button, Icon } from '@ali/cnd';

const RefreshButton = (props) => {
  const {
    disabled = false,
    type = 'primary',
    handler,
    label = '',
    text = true,
    size = "medium",
    iconSize = 'xs',
    className = '',
    style = {},
  } = props;
  const [loading, setLoading] = useState(false);
  const hanleClick = async () => {
    if (loading) {
      return;
    }
    setLoading(true);
    await handler();
    setLoading(false);
  }
  return (
    <Button
      type={type}
      text={text}
      size={size}
      iconSize={iconSize}
      disabled={loading || disabled}
      onClick={hanleClick}
      className={className}
      style={style}
    >
      {!!label && (label)}
      <Icon type={loading ? "loading" : 'refresh'} />
    </Button>
  );
};

export default RefreshButton;
