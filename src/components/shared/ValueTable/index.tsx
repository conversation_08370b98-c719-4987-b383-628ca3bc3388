import { intl } from '@ali/cnd';
import { Button, Icon, CndTable } from '@ali/cnd';
import { cloneDeep, forEach } from 'lodash';
import React from 'react';

const ValueTable = (props) => {
  const { field, name, value = [], onChange = () => {}, maxLength, columns = () => [], addRender, ...restProps } = props;

  const onAdd = (val = {}) => {
    if (field && name) {
      if (!field.getValue(name)) {
        field.setValue(name, []);
      }
      const length = field.getValue(name)?.length || 0;
      field.addArrayValue(name, length, val);
      onChange(field.getValue(name));
    } else {
      value.push(val);
      onChange(value);
    }
  };

  const onDelete = (index) => {
    if (field && name) {
      field.deleteArrayValue(name, index);
      onChange(field.getValue(name));
    } else {
      value.splice(index, 1);
      onChange(value);
    }
  };

  const onItemChange = (index, val) => {
    if (field && name) {
      forEach(val, (v, k) => {
        field.setValue(`${name}.${index}.${k}`, v);
      });
      onChange(field.getValue(name));
    } else {
      value[index] = {
        ...value[index],
        ...val,
      };
      onChange(value);
    }
  };

  return (
    <>
      <CndTable
        {...restProps}
        // key={value.length}
        dataSource={value}
        columns={columns({
          onDelete,
          onItemChange,
        })}
      />

      {!(maxLength && value.length >= maxLength) && (
        addRender ?
          addRender(onAdd)
          :
          <Button className="mt-s" onClick={() => onAdd()}>
            <Icon type="add" />
            <span>{intl('saenext.shared.ValueTable.Add')}</span>
          </Button>
      )}
    </>
  );
};

export default ValueTable;
