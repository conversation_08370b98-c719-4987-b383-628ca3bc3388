import React, { useEffect, useMemo, useState } from 'react';
import { intl, Select } from '@ali/cnd';
import If from '../If';
import { find, get, isEmpty, map } from 'lodash';
import services from '~/services';
import PodIndicator from '~/components/app-detail/micro-app/log-control/log-control/PodIndicator';

const InstanceContainerSelect = (props) => {
  const { appId, groupId, instanceDisabled, value = {}, onChange } = props;

  const { instanceId: podInstanceId, sidecarId = '' } = value;

  const [loading, setLoading] = useState(false);
  const [podInstances, setPodInstances] = useState([]);

  useEffect(() => {
    getInstanceData(groupId);
  }, [groupId]);

  const getInstanceData = async (groupId) => {
    setLoading(true);
    const pods = await getInstanceList(groupId);
    const data = map(pods, (v) => ({
      ...v,
      label: v.InstanceId,
      value: v.InstanceId,
    }));
    setPodInstances(data);
    setLoading(false);
  };

  const getInstanceList = async (groupId) => {
    const filters = {
      InstanceId: instanceDisabled && podInstanceId ? podInstanceId : undefined,
    };

    const res = await services.DescribeApplicationInstances({
      AppId: appId,
      GroupId: groupId,
      PageSize: 999,
      CurrentPage: 1,
      ...filters,
    });
    const pods = get(res, 'Data.Instances', []);
    return pods;
  };

  const sidecarSource = useMemo(() => {
    const curPod = find(podInstances, { value: podInstanceId });
    const { SidecarContainersStatus } = curPod || {};

    if (isEmpty(SidecarContainersStatus)) return [];

    const sidecars = map(SidecarContainersStatus, (v) => ({
      label: intl('saenext.shared.InstanceContainerSelect.tsx.SidecarContainerVcontainerid', {
        vContainerId: v.ContainerId,
      }),
      value: v.ContainerId,
    }));
    return [
      {
        label: intl('saenext.shared.InstanceContainerSelect.tsx.MainApplicationContainer'),
        value: '',
      },
      ...sidecars,
    ];
  }, [podInstances, podInstanceId]);

  const renderInstance = (pod) => {
    return (
      <>
        <PodIndicator value={pod.InstanceContainerStatus} />
        <span style={{ marginLeft: 10, color: '#555' }}>{pod.InstanceContainerIp}</span>
        <span style={{ marginLeft: 10 }}>{pod.InstanceId}</span>
      </>
    );
  };

  const onSelect = (obj) => {
    onChange({
      ...value,
      ...obj,
    });
  };

  return (
    <div className="flex full-width">
      <Select
        label={intl('saenext.components.log-control.RealtimeLog.PodName')}
        className="mr-s mb-s"
        style={{ minWidth: 520 }}
        disabled={instanceDisabled}
        value={podInstanceId}
        state={loading ? 'loading' : undefined}
        dataSource={podInstances}
        valueRender={renderInstance}
        onChange={(instanceId: string) => {
          onSelect({
            instanceId,
          });
        }}
      />

      <If condition={sidecarSource.length > 0}>
        <Select
          className="mr-s mb-s"
          style={{ minWidth: 200 }}
          value={sidecarId}
          state={loading ? 'loading' : undefined}
          dataSource={sidecarSource}
          onChange={(sidecarId: string) => {
            onSelect({
              sidecarId,
            });
          }}
        />
      </If>
    </div>
  );
};

export default InstanceContainerSelect;
