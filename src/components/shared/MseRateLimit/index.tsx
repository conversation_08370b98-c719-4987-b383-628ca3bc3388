import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Form, Switch } from '@ali/cnd';
import C from '~/constants/common';
import If from '~/components/shared/If';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';
import CachedData from '~/cache/common';
import ExternalLink from '~/components/shared/ExternalLink';

const MseRateLimit = (props) => {
  const {
    value
  } = props;

  const { Version } = CachedData.mseStatus;
  // MSE 开通企业版
  const mscOpen = Version === 2;


  return (
    <>
      <OpenStatusMessage product="mse2" />

      <Form.Item
        {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        label={intl('saenext.shared.MseRateLimit.EnableThrottlingAndDegradation')}
        size="small"
        help={
          <>
            <If condition={value}>
              {intl('saenext.shared.MseRateLimit.AfterTheThrottlingDegradationFeature')}
            </If>
            <If condition={!value}>
              <div>
                {intl('saenext.shared.MseRateLimit.SaeInheritsTheMseThrottling')}

                <ExternalLink
                  url={CachedData.confLink('help:mse:billing-overview')}
                />

                <br />
                {intl('saenext.shared.MseRateLimit.AfterTheThrottlingAndDegradation')}
              </div>
            </If>
          </>
        }
      >
        <Switch name="EnableAhas" disabled={!mscOpen} />
      </Form.Item>
    </>
  );
};

export default MseRateLimit;
