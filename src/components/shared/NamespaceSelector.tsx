import React, { useContext, useEffect, useMemo, useState } from 'react';
import { ConsoleContext, Icon, intl, Select } from '@ali/cnd';
import { getParams, setSearchParams } from '~/utils/global';
import { concat, get } from 'lodash';
import services from '~/services';

const DEFAULT_ALL = [
  {
    NamespaceId: 'all',
    NamespaceName: intl('saenext.components.shared.NamespaceTag.AllNamespaces'),
  },
];

export const NAMESPACE_KEY = 'namespaceId';

const NamespaceSelector = (props) => {
  const defaultNamespaceId = getParams('namespaceId') || 'all';
  const { value: propsValue = defaultNamespaceId, onChange, setNamespaceData, disabledAll=false } = props;

  const [isLoading, setIsLoading] = useState(false);
  const [value, setValue] = useState(propsValue);
  const [namespaceList, setNamespaceList] = useState(DEFAULT_ALL);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const isInitRef = React.useRef(true);

  const namespaceListData = useMemo(() => {
    return namespaceList.map(item => ({
      label: item.NamespaceName,
      value: item.NamespaceId,
      disabled: item.NamespaceId === 'all' && disabledAll,
    }));
  }, [namespaceList]);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    sessionStorage.setItem(NAMESPACE_KEY, value);
  }, [value]);

  useEffect(() => {
    // 首次渲染不执行
    if (isInitRef.current) {
      isInitRef.current = false;
      return;
    }
    handleChange(disabledAll ? regionId : 'all');
  }, [regionId]);

  const fetchData = async () => {
    setIsLoading(true);
    const res = await services.getNamespaceListV2({
      params: {
        CurrentPage: 1,
        PageSize: 100,
      },
      customErrorHandle: (error, data) => {
        setIsLoading(false);
        return error;
      },
    });
    const _namespaceList = get(res, 'Data.Namespaces') || [];
    setIsLoading(false);
    setNamespaceList(concat(DEFAULT_ALL, _namespaceList));
    setNamespaceData?.(_namespaceList);
  };

  const handleChange = (val) => {
    setSearchParams({ namespaceId: val });
    setValue(val);
    onChange(val);
  };

  return (
    <Select
      {...props}
      label={intl('saenext.components.shared.NamespaceSelector.Namespace')}
      innerAfter={<Icon type="search" className="mr-s" />}
      showSearch
      dataSource={namespaceListData}
      value={value}
      onChange={handleChange}
      state={isLoading ? 'loading' : undefined}
    />
  );
};

export default NamespaceSelector;
