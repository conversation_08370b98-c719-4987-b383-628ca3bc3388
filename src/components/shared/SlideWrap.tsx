import React, { useEffect, useState } from 'react'
import AddPropsWrap from './AddPropsWrap';
import { SlidePanel } from '@ali/cnd';
import { isUndefined } from 'lodash';
import { getParams, setSearchParams } from '~/utils/global';
import If from './If';

const SlideWrap = (props) => {
  const {
    children,
    content,
    openParamKey,
    onMaskClick,
    onClose,
    onCancel,
    onOk,
    customFooter,
    lazyLoad = true,
    ...rest
  } = props;

  const [slideVisible, setSlideVisible] = useState(false);

  const openParam = getParams(openParamKey);

  useEffect(() => {
    if (openParamKey && !openParam) {
      setSlideVisible(false);
    }
  }, [openParam])

  const toggleVisible = () => {
    if (openParamKey) {
      setSearchParams({
        [openParamKey]: slideVisible ? '' : 'true'
      })
    }
    setSlideVisible(!slideVisible);
  }

  const toggleAfter = async (fun) => {
    const res = await fun();
    if (isUndefined(res) || res) {
      toggleVisible();
    }
  }

  return (
    <>
      <AddPropsWrap onClick={toggleVisible}>{children}</AddPropsWrap>
      <SlidePanel
        {...rest}
        isShowing={slideVisible}
        onMaskClick={onMaskClick && (() => toggleAfter(onMaskClick))}
        onClose={onClose && (() => toggleAfter(onClose))}
        onCancel={onCancel && (() => toggleAfter(onCancel))}
        onOk={onOk && (() => toggleAfter(onOk))}
        customFooter={customFooter && (customFooter(toggleVisible))}
      >
        <AddPropsWrap closeSlide={toggleVisible}>
          <If condition={!lazyLoad || slideVisible}>
            {content}
          </If>
        </AddPropsWrap>
      </SlidePanel>
    </>
  )
}

export default SlideWrap;