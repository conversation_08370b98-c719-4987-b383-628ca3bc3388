import { intl } from '@ali/cnd';
import React, { PureComponent } from 'react';
import { Loading, Message } from '@ali/cnd';
import { XTerm } from 'xterm-for-react';
import services from '~/services';
import { getParams } from '~/utils/global';
import { FitAddon } from 'xterm-addon-fit';
import ResizeObserver from 'react-resize-observer';
import { AES_CONSTANT, trackOpt } from '~/tracker';

function getDefaultCmd(cols, rows) {
  return [
    '/bin/sh',
    '-c',
    `cd /code > /dev/null 2>&1 || true && COLUMNS=${cols}; export COLUMNS; LINES=${rows}; export LINES; TERM=xterm-256color; export TERM; [ -x /bin/bash ] && ([ -x /usr/bin/script ] && /usr/bin/script -q -c "/bin/bash" /dev/null || exec /bin/bash) || exec /bin/sh`,
  ];
}

type Props = {
  versionId: string;
  instanceId: string;
  applicationId: string;
};

type State = {
  disconnected: boolean;
  connecting: boolean;
};

export default class Shell extends PureComponent<Props, State> {
  ref: any;
  ws: any;
  fitAddons: any;

  constructor(props) {
    super(props);
    this.state = {
      connecting: false,
      disconnected: false,
    };
    this.fitAddons = new FitAddon();
    this.ref = React.createRef();
    this.onData = this.onData.bind(this);
  }

  componentDidMount() {
    this.connect();
    this.fitAddons.fit();
  }

  async connect() {
    const { instanceId, applicationId } = this.props;
    this.setState({ connecting: true });
    const applicationName = getParams('name');
    try {
      const { cols = 20, rows = 50 } = this.fitAddons.proposeDimensions() || {};
      const { endpoint, authorization, date } = await services.getInstanceExecAuthorization(
        applicationName,
        instanceId,
        applicationId,
        {
          stdin: true,
          stderr: true,
          stdout: true,
          tty: true,
          command: getDefaultCmd(cols, rows),
        },
      );

      this.setState({ connecting: true });

      const url = `${endpoint}&Authorization=${encodeURIComponent(
        authorization,
      )}&Date=${encodeURIComponent(date)}`;
      const newUrl = url.replace(/ws:/g, 'wss:');

      const ws = new WebSocket(newUrl);

      ws.onopen = () => {
        this.setState({ connecting: false });
        if (this.ref.current) {
          this.ref.current.terminal.focus();
        }
      };

      ws.onmessage = async (message) => {
        if (this.ref.current) {
          let text;
          if (typeof message.data === 'object') {
            text = await message.data.text();
          } else {
            text = message.data;
          }
          this.ref.current.terminal.write(text);
        }
      };

      ws.onerror = async (e) => {
        this.setState({
          connecting: false,
          disconnected: true,
        });
      };

      ws.onclose = (e) => {
        this.setState({
          connecting: false,
          disconnected: true,
        });
      };

      this.ws = ws;
      trackOpt({
        behavior: AES_CONSTANT.OPT_BEHAVIOR_TYPE.WEB_SHELL,
        stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
      });
    } catch (e) {
      trackOpt({
        behavior: AES_CONSTANT.OPT_BEHAVIOR_TYPE.WEB_SHELL,
        stage: AES_CONSTANT.AES_STAGE_TYPE.FAIL,
        extra: e?.message,
      });
      this.setState({
        connecting: false,
        disconnected: true,
      });
    }
  }

  onData = (data) => {
    if (this.ws) {
      this.ws.send(data);
    }
  };

  componentWillUnmount() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  render(): React.ReactNode {
    const { connecting, disconnected } = this.state;
    return (
      <Loading visible={connecting} className="full-width">
        {disconnected && (
          <Message
            className="mb-s"
            type="error"
            title={intl('saenext.components.shared.Shell.TheCurrentConnectionIsDisconnected')}
          >
            {intl('saenext.components.shared.Shell.TheLogonConnectionOfThe')}
          </Message>
        )}

        <ResizeObserver
          onResize={() => {
            this.fitAddons.fit();
          }}
        />

        <div className="shell-inner">
          <XTerm
            addons={[this.fitAddons]}
            ref={this.ref}
            onData={this.onData}
            options={{
              fontSize: 14,
              lineHeight: 1.1,
              fontFamily: '"Lucida Console", Monaco, "Courier New", monospace',
              theme: {
                background: '#2d2e2c',
              },
              cursorBlink: true,
            }}
          />
        </div>
      </Loading>
    );
  }
}
