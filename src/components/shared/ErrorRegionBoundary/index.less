.region-empty {
  width: 100%;
  height: 100%;
  display: flex;
  background-size: cover;

  background-image: url(https://img.alicdn.com/imgextra/i4/O1CN01Hxb2jt1m701P56rfp_!!6000000004906-2-tps-1315-800.png);
  background-repeat: no-repeat;
  background-position: right bottom;

  .region-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .xconsole-rc-region-guidance {
      font-size: 14px;
      line-height: 2;
      color: #000;
      display: flex;
      margin-top: -36px;
    }

    .xconsole-rc-region-count {
      display: none;
    }
    
    a {
      color: #0070cc;
    }
  }
}

.xconsole-rc-region-item-count {
  background-color: rgba(255, 255, 255, 0.5) !important;
}