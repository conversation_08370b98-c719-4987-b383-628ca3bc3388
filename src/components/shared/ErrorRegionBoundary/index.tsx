import React, { useContext } from 'react';
import { find, forEach, get } from 'lodash';
import { intl, ConsoleContext, RegionGuidance } from '@ali/cnd';
import './index.less';

const ErrorRegionBoundary = (props) => {
  const { regionGreyKey, changeRegion = () => {}, children } = props;

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const regionOpenList = get(window, ['ALIYUN_CONSOLE_GLOBAL', regionGreyKey]) || [];
  const regionOpen = find(regionOpenList, { id: regionId });

  if (regionOpen) {
    return children;
  }

  forEach(regionOpenList, (item) => {
    item.count = ' ';
  });

  return (
    <div className="region-empty">
      <div className="region-content">
        <img
          src="https://gw.alicdn.com/imgextra/i2/O1CN01YTGrWJ1QFJiOe9esc_!!6000000001946-2-tps-624-438.png"
          alt=""
        />
        <RegionGuidance
          currentRegion={regionId}
          onRegionClick={changeRegion}
          regionList={regionOpenList}
        >
          <span>{intl('saenext.shared.ErrorRegionBoundary.ThisFeatureIsNotSupported')}</span>
        </RegionGuidance>
      </div>
    </div>
  );
};

export default ErrorRegionBoundary;
