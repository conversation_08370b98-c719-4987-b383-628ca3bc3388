import { intl } from '@ali/cnd';
import { Select } from '@ali/cnd';
import { map } from 'lodash';
import React, { useEffect } from 'react';
import { getTimes } from '~/components/shared/TimeRangeSelector';
import InstanceStatus from '~/components/version-detail/instance-list/InstanceStatus';
import services from '~/services';

const InstanceSelect = (props) => {
  const {
    applicationID,
    applicationName,
    versionId,
    value: instanceId,
    onChange: onInstanceChange,
    ...restProps
  } = props;

  const [instancesData, setInstancesData] = React.useState([]);

  useEffect(() => {
    getInstanceList();
  }, []);

  const getInstanceList = async () => {
    const [startTime, endTime] = getTimes('last_15_minutes');

    const data = await services.getAppVersionInstances({
      applicationID,
      applicationName,
      startTime,
      endTime,
      qualifier: versionId,
    });

    const { instances = [] } = data;
    const _instancesData = map(instances, (instance) => ({
      value: instance.instanceId,
      status: instance.status,
      label: (
        <>
          <InstanceStatus value={instance.status} />
          <span className="ml">{instance.instanceId}</span>
        </>
      ),
    }));
    setInstancesData(_instancesData);
  };

  return (
    <Select
      label={intl('saenext.shared.InstanceSelect.VersionInstance')}
      style={{ minWidth: 400 }}
      value={instanceId}
      dataSource={instancesData}
      onChange={onInstanceChange}
      {...restProps}
    />
  );
};

export default InstanceSelect;
