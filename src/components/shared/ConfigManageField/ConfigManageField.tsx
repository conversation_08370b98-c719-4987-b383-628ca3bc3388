import React, { Component } from 'react';
import ConfigManage from './index';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';

type Props = {
  value: Object;
  onChange: Function;
  namespaceId: string;
};

class ConfigManageFieldClass extends Component<Props> {
  private configRef = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return this.configRef.validate();
  };

  render() {
    return <ConfigManage {...this.props} ref={ref => (this.configRef = ref)} />;
  }
}

class ConfigManageField extends BaseField {
  props;
  static displayName = 'ConfigManageField';

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <ConfigManageFieldClass {...newProps} />;
  }
}

export default ConfigProvider.config(ConfigManageField as any);
