import { intl } from '@ali/cnd';
import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Button, Field, Form, Grid, Icon } from '@ali/cnd';
import {
  map,
  get,
  uniqueId,
  concat,
  isEmpty,
  mapValues,
  noop,
  forEach,
  cloneDeep,
  size,
  filter,
} from 'lodash';
import ConfigMapSlide from '../ConfigMapSlide';
import services from '~/services';
import ConfigItem from './ConfigItem';
const { Row, Col } = Grid;
type IListProps = {
  namespaceId: string;
  onChange?: Function;
  value: any;
};

// 环境变量配置列表
const ConfigManage = (props: IListProps, ref) => {
  const { namespaceId, onChange = noop, value } = props;
  const field = Field.useField({
    onChange: () => {
      let newList: any[] = [];
      const itemList = get(getValues(), 'form-item-manage', []);
      const arrayItemList = Array.isArray(itemList) ? itemList : [];
      mapValues(arrayItemList, (item) => {
        if (!isEmpty(item)) {
          newList.push(item);
        }
      });
      onSubmit(newList);
    },
    parseName: true,
  });
  const getInitValue = () => {
    return isEmpty(value ? JSON.parse(value) : value)
      ? [{ id: uniqueId() }]
      : map(JSON.parse(value), (item) => ({
          id: uniqueId(),
          ConfigMapId: item?.configMapId,
          configMapKey: item?.key,
          mountPath: item?.mountPath,
        }));
  };
  const [configMaps, setConfigMaps] = useState([]);
  const [list, setList] = useState(getInitValue());
  const [loadingConfigMap, setLoadingConfigMap] = useState(false);
  const { getValues, setValue, validate } = field;

  const configMapNames = map(configMaps, 'Name');

  useImperativeHandle(ref, () => ({
    validate: manageValidate,
  }));

  useEffect(() => {
    if (isEmpty(namespaceId)) return;
    fetchListNamespacedConfigMaps();
  }, [namespaceId]);

  useEffect(() => {
    forEach(getInitValue(), (item: any, index) => {
      setValue(`form-item-manage[${index}].ConfigMapId`, item.ConfigMapId);
      setValue(`form-item-manage[${index}].configMapKey`, item.configMapKey);
      setValue(`form-item-manage[${index}].mountPath`, item.mountPath);
    });
  }, [value]);

  const fetchListNamespacedConfigMaps = async () => {
    setLoadingConfigMap(true);
    const { Data } = await services.ListNamespacedConfigMaps({
      NamespaceId: namespaceId,
    });
    setConfigMaps(get(Data, 'ConfigMaps', []));
    setLoadingConfigMap(false);
  };

  const handleAdd = () => {
    const newList = concat(list, { id: uniqueId() });
    setList(newList);
  };

  const handleDelete = (index) => {
    const itemList = get(getValues(), 'form-item-manage', []);
    const arrayItemList = Array.isArray(itemList) ? itemList : [];
    const values = cloneDeep(arrayItemList);
    values.splice(index, 1);
    const newValue = filter(values, (item) => !isEmpty(item));
    setList([...newValue]);
    onSubmit([...newValue]);
  };

  const onSubmit = (values) => {
    const postData = map(values, (item) => ({
      configMapId: item.ConfigMapId,
      key: item.configMapKey,
      mountPath: item.mountPath,
    }));
    onChange && onChange(JSON.stringify(postData));
  };

  const manageValidate = () => {
    return new Promise((resolve) => {
      if (!value) {
        resolve(true);
        return;
      }
      try {
        const data = JSON.parse(value);
        if (
          size(data) === 1 &&
          !get(data, '[0].key') &&
          !get(data, '[0].configMapId') &&
          !get(data, '[0].mountPath')
        ) {
          resolve(true);
          return;
        }
      } catch (error) {}
      validate((error) => {
        error
          ? resolve(
              intl('saenext.shared.ConfigManageField.IncompleteConfigurationManagementInformation'),
            )
          : resolve(true);
      });
    });
  };

  return (
    <Form field={field} labelAlign="inset">
      <ConfigMapSlide
        linkButton
        type="create"
        reload={fetchListNamespacedConfigMaps}
        namespaceId={namespaceId}
        nameList={configMapNames}
        className="mb-l"
      />

      <Row style={{ marginBottom: 16 }}>
        <Col span="6">
          <span>
            {intl('saenext.shared.ConfigManageField.ConfigurationItemName')}

            <span
              className={loadingConfigMap ? 'ib arrow-animation' : 'ib'}
              style={{ marginLeft: 4 }}
            >
              <Icon
                type="refresh"
                size="small"
                style={{ cursor: 'pointer' }}
                onClick={fetchListNamespacedConfigMaps as any}
              />
            </span>
          </span>
        </Col>
        <Col span="6">{intl('saenext.shared.ConfigManageField.Key')}</Col>
        <Col span="7">{intl('saenext.shared.ConfigManageField.MountPath')}</Col>
        <Col span="3" style={{ textAlign: 'center' }}>
          {intl('saenext.shared.ConfigManageField.Permission')}
        </Col>
        <Col span="2">{intl('saenext.shared.ConfigManageField.Operation')}</Col>
      </Row>
      {map(list, (item, index) => (
        <ConfigItem
          field={field}
          configMaps={configMaps}
          index={index}
          handleDelete={handleDelete}
          loadingConfigMap={loadingConfigMap}
          onRefresh={fetchListNamespacedConfigMaps}
        />
      ))}
      <Button onClick={handleAdd} style={{ marginTop: 8 }}>
        <Icon type="add" />
        {intl('saenext.shared.ConfigManageField.Add')}
      </Button>
    </Form>
  );
};

export default forwardRef(ConfigManage);
