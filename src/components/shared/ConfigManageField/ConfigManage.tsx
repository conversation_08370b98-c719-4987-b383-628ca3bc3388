import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import { Button, Field, Form, Icon, intl } from '@ali/cnd';
import { map, get, isEmpty, noop, find, keys, debounce, forEach } from 'lodash';
import ConfigMapSlide from '../ConfigMapSlide';
import services from '~/services';
import { TableField, TextField, SelectField } from '@ali/deep';
import RefreshButton from '../RefreshButton';
import { jsonParse } from '~/utils/transfer-data';
import ExternalLink from '../ExternalLink';

type IListProps = {
  namespaceId: string;
  onChange?: Function;
  value: any;
};

// 环境变量配置列表
const ConfigManage = (props: IListProps, ref) => {
  const { namespaceId, onChange = noop, value } = props;
  const [configMaps, setConfigMaps] = useState([]);
  const [loadingConfigMap, setLoadingConfigMap] = useState(false);
  const tableRef = useRef(null);

  const [regionId] = namespaceId.split(':');

  const configMapNames = map(configMaps, 'Name');

  const field = Field.useField({
    onChange: () => {
      debounce(fieldValueChange, 500)();
    },
  });

  useEffect(() => {
    initValue();
  }, [value]);

  useEffect(() => {
    allKeyItemAddSource();
  }, [configMaps]);

  useEffect(() => {
    if (isEmpty(namespaceId)) return;
    fetchListNamespacedConfigMaps();
  }, [namespaceId]);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [value],
  );

  const initValue = () => {
    let _configList = [];
    if (value) {
      if (typeof value === 'string') {
        _configList = jsonParse(value);
      } else {
        _configList = value;
      }
    }

    const configList = map(_configList, (item) => ({
      ...item,
      readOnly: intl('saenext.shared.ConfigManageField.ConfigManage.ReadOnly'),
    }));

    field.setValues({ configList });
  };

  const allKeyItemAddSource = () => {
    const formGroupIds = tableRef.current.getItems?.();
    forEach(formGroupIds, (formGroupId) => {
      const itemField = tableRef.current.getField(formGroupId);
      const ConfigMapId = itemField.getValue(`${formGroupId}.ConfigMapId`);
      keyItemAddSource(formGroupId, ConfigMapId);
    });
  };

  const fetchListNamespacedConfigMaps = async () => {
    setLoadingConfigMap(true);
    const { Data } = await services.ListNamespacedConfigMaps({
      NamespaceId: namespaceId,
    });
    setConfigMaps(get(Data, 'ConfigMaps', []));
    setLoadingConfigMap(false);
  };

  // 校验
  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((error, value) => {
        if (error) {
          resolve(' ');
          return;
        }
        resolve(true);
      });
    });
  };

  const fieldValueChange = () => {
    const { getValues } = field;

    const { configList } = getValues() as any;
    const _configList = map(configList, (item) => {
      const { ConfigMapId, Key, MountPath } = item;
      return {
        ConfigMapId,
        Key,
        MountPath,
      };
    });
    onChange(JSON.stringify(_configList));
  };

  const handleConfigIdChange = ({ formGroupId, value }) => {
    const keyChild = tableRef.current.getComponent(formGroupId, 'Key');
    // 清空 键
    keyChild.setValue('');
    keyItemAddSource(formGroupId, value);
  };

  const keyItemAddSource = (formGroupId, ConfigMapId) => {
    tableRef.current.setComponentProps(formGroupId, 'Key', {
      state: 'loading',
    });

    const option = find(configMaps, { ConfigMapId }) || {};
    const dataSource = map(keys(option.Data), (item) => ({
      value: item,
      label: item,
    }));

    tableRef.current.setComponentProps(formGroupId, 'Key', {
      state: null,
      dataSource: [
        {
          label: intl('saenext.shared.ConfigManageField.ConfigManage.All'),
          value: 'sae-sys-configmap-all',
        },
        ...dataSource,
      ],
    });
  };

  const tableValidation = (value = []) => {
    for (let i = 0; i < value.length; i++) {
      const item = value[i];
      if (!item) {
        return intl('saenext.shared.ConfigManageField.ConfigManage.IncompleteDataConfiguration');
      }
      if (!item.ConfigMapId || !item.Key || !item.MountPath) {
        return intl('saenext.shared.ConfigManageField.ConfigManage.IncompleteDataConfiguration');
      }
      if (item.MountPath && item.MountPath.endsWith('/')) {
        return intl('saenext.shared.ConfigManageField.ConfigManage.ThePathCannotEnd');
      }
    }
    return true;
  };

  const configItemRender = (item) => {
    return (
      <div>
        <span>{item.label}</span>
        <ExternalLink className="ml-s" url={`/${regionId}/config-management/config-map?namespaceId=${namespaceId}`} />
      </div>
    );
  };

  return (
    <Form field={field} labelAlign="inset">
      <div className="mb-l">
        <ConfigMapSlide
          buttonType="secondary"
          type="create"
          reload={fetchListNamespacedConfigMaps}
          namespaceId={namespaceId}
          nameList={configMapNames}
        />

        <a className="ml-s" target="_blank" href={`/${regionId}/config-management/config-map?namespaceId=${namespaceId}`}>
          <Button type="secondary">
            {intl('saenext.shared.ConfigManageField.ConfigManage.ManageConfigurationItems')}
          </Button>
        </a>
      </div>

      <TableField
        name="configList"
        className="full24-table"
        layout="TABLER"
        showIndex={false}
        showTableHead={true}
        showSortable={false}
        minItems={0}
        addButtonText={intl('saenext.shared.ConfigManageField.ConfigManage.Add')}
        showDeleteConfirm={false}
        actionsColumnWidth={80}
        delButtonText={<Icon type="delete" />}
        ref={(c) => (tableRef.current = c?.getInstance?.() || c)}
        validation={[
          {
            type: 'customValidate',
            param: tableValidation,
          },
        ]}
      >
        <SelectField
          name="ConfigMapId"
          label={
            <div className="flex">
              <span>
                {intl('saenext.shared.ConfigManageField.ConfigManage.ConfigurationItemName')}
              </span>
              <RefreshButton className="ml-s" handler={fetchListNamespacedConfigMaps} />
            </div>
          }
          dataSource={map(configMaps, (obj) => ({
            label: obj.Name,
            value: obj.ConfigMapId,
          }))}
          itemRender={configItemRender}
          state={loadingConfigMap ? 'loading' : null}
          onChange={(item) => handleConfigIdChange(item)}
        />

        <SelectField label={intl('saenext.shared.ConfigManageField.ConfigManage.Key')} name="Key" />

        <TextField
          name="MountPath"
          label={intl('saenext.shared.ConfigManageField.ConfigManage.MountPath')}
          placeholder={intl('saenext.shared.ConfigManageField.ConfigManage.ExampleOrXxXxx')}
        />

        <TextField
          label={intl('saenext.shared.ConfigManageField.ConfigManage.Permission')}
          value={intl('saenext.shared.ConfigManageField.ConfigManage.ReadOnly')}
          behavior="READONLY"
          name="readOnly"
        />
      </TableField>
    </Form>
  );
};

export default forwardRef(ConfigManage);
