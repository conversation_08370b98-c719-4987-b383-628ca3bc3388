import { intl } from '@ali/cnd';
import React from 'react';
import { Select, Form, Input, Icon, Grid } from '@ali/xconsole';
import { map, find, keys } from 'lodash';
const { Row, Col } = Grid;

type IItemProps = {
  field: any;
  configMaps: any[];
  index: Number;
  handleDelete?: Function;
  onRefresh: Function;
  loadingConfigMap: boolean;
};

// 环境变量 配置项
const ConfigList = (props: IItemProps) => {
  const { field, configMaps, index, handleDelete } = props;
  const { init, getValue } = field;

  const getKeys = (ConfigMapId) => {
    const option = find(configMaps, (item) => item.ConfigMapId === ConfigMapId) || {};
    const dataSource = map(keys(option.Data), (item) => ({
      value: item,
      label: item,
    }));
    return [
      {
        label: intl('saenext.shared.ConfigManageField.ConfigItem.All'),
        value: 'sae-sys-configmap-all',
      },
      ...dataSource,
    ];
  };

  return (
    <Row style={{ marginBottom: 0, display: 'flex', alignItems: 'center' }} gutter={8}>
      <Col span="6">
        <Form.Item className="full-width">
          <Select
            {...init(`form-item-manage[${index}].ConfigMapId`, {
              rules: [{ required: true, message: ' ' }],
            })}
            hasClear
            className="full-width"
            dataSource={map(configMaps, (obj) => ({
              label: obj.Name,
              value: obj.ConfigMapId,
            }))}
          />
        </Form.Item>
      </Col>
      <Col span="6">
        <Form.Item className="full-width">
          <Select
            {...init(`form-item-manage[${index}].configMapKey`, {
              rules: [{ required: true, message: ' ' }],
            })}
            hasClear
            className="full-width"
            dataSource={getKeys(getValue(`form-item-manage[${index}].ConfigMapId`))}
          />
        </Form.Item>
      </Col>
      <Col span="7">
        <Form.Item className="full-width">
          <Input
            {...init(`form-item-manage[${index}].mountPath`, {
              rules: [{ required: true, message: ' ' }],
            })}
            className="full-width"
          />
        </Form.Item>
      </Col>
      <Col span="3">
        <Form.Item className="full-width" style={{ textAlign: 'center' }}>
          {intl('saenext.shared.ConfigManageField.ConfigItem.ReadOnly')}
        </Form.Item>
      </Col>
      <Col span="2">
        <Form.Item className="full-width">
          <Icon
            style={{ cursor: 'pointer', fontSize: 16 }}
            type="delete"
            onClick={() => handleDelete(index)}
          />
        </Form.Item>
      </Col>
    </Row>
  );
};

export default ConfigList;
