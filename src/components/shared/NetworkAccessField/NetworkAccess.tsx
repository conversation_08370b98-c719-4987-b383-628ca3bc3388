import { intl } from '@ali/cnd';
import React, { useContext } from 'react';
import { Balloon, Dialog, Icon, Message, Switch } from '@ali/cnd';
import './NetworkAccess.less';
import MicroAppContext from '~/utils/microAppContext';
import CachedData from '~/cache/common';

const NetworkAccess = (props) => {
  const { value: associateEip, onChange, scalingRuleEnbaled = false } = props;

  const { scaleRuleEnabled, scaleRef } = useContext(MicroAppContext);

  const handleSwitchChange = (checked) => {
    if (checked === false) {
      Dialog.confirm({
        title: intl('saenext.shared.NetworkAccessField.NetworkAccess.UnbindAnEip'),
        content: intl('saenext.shared.NetworkAccessField.NetworkAccess.AfterClosingTheEipIs'),
        onOk: () => onChange(checked),
        onCancel: () => {},
      });
      return;
    } else {
      if (scaleRuleEnabled) {
        Message.error(
          intl('saenext.shared.NetworkAccessField.NetworkAccess.TheApplicationHasSelectedThe'),
        );
        return;
      }
    }
    onChange(checked);
  };

  return (
    <>
      <p>{intl('saenext.shared.NetworkAccessField.NetworkAccess.YouCanUseTheFollowing')}</p>
      <div className="network-access">
        <div>
          <div className="network-access-title">
            {intl('saenext.shared.NetworkAccessField.NetworkAccess.SolutionNatGatewayEip')}
          </div>
          <div className="network-access-content">
            <p>{intl('saenext.shared.NetworkAccessField.NetworkAccess.ApplicableScenarios')}</p>
            <p className="ml-s">
              {intl(
                'saenext.shared.NetworkAccessField.NetworkAccess.ApplicationsWithAutomaticElasticityRequirements',
              )}
            </p>
            <p className="ml-s">
              {intl('saenext.shared.NetworkAccessField.NetworkAccess.YouNeedToFixThe')}
            </p>
          </div>
          <div className="mt-s">
            {intl(
              'saenext.shared.NetworkAccessField.NetworkAccess.ForOneConfigurationAllApplication',
            )}

            <a href={CachedData.confLink('help:sae:configure-a-nat-gateway-2-0')} target="_blank">
              {intl('saenext.shared.NetworkAccessField.NetworkAccess.Document')}
            </a>
            <Icon style={{ color: '#0070cc', marginLeft: 2 }} type="external-link-alt" size="xs" />
          </div>
        </div>
        <div>
          <div className="network-access-title">
            {intl('saenext.shared.NetworkAccessField.NetworkAccess.SolutionBindAnEip')}

            <Balloon
              trigger={
                <Icon
                  size="xs"
                  style={{ marginLeft: 4, lineHeight: '12px', color: '#333', fontSize: 12 }}
                  type="help"
                />
              }
              closable={false}
              align="t"
            >
              {intl('saenext.shared.NetworkAccessField.NetworkAccess.WhenEnabledAnEipIs')}
            </Balloon>
          </div>
          <div className="network-access-content">
            <p>{intl('saenext.shared.NetworkAccessField.NetworkAccess.ApplicableScenarios')}</p>
            <p className="ml-s">
              {intl('saenext.shared.NetworkAccessField.NetworkAccess.ApplyToThePublicNetwork')}
            </p>
            <p className="ml-s">
              {intl(
                'saenext.shared.NetworkAccessField.NetworkAccess.TheApplicationEntersThePublic',
              )}
            </p>
          </div>
          <div className="mt-s flex">
            <Switch
              checked={associateEip}
              size="small"
              onChange={handleSwitchChange}
              style={{ marginRight: 8 }}
            />

            <span>
              {intl('saenext.shared.NetworkAccessField.NetworkAccess.WhenAnApplicationIsCreated')}
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default NetworkAccess;
