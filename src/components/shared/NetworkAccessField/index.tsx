import React, { Component } from 'react';
import NetworkAccess from './NetworkAccess';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';

type Props = {
  value?: Object;
  onChange?: Function;
};

class NetworkAccessFieldClass extends Component<Props> {
  private ref = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.ref.validate();
  }

  render() {

    return (
      <NetworkAccess
        {...this.props}
        ref={(ref) => this.ref = ref}
      />
    )
  }
}

class NetworkAccessField extends BaseField {
  props;
  static displayName = 'NetworkAccessField';

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return (
      <NetworkAccessFieldClass
        {...newProps}
      />
    );
  }
}

export default ConfigProvider.config(NetworkAccessField as any)
