import { intl } from '@ali/cnd';
import React from 'react';
import { Button, Loading, Dialog } from '@ali/cnd';

type Props = {
  visible: boolean;
  title: string;
  loading?: boolean;
  onOk: () => void;
  onClose: () => void;
  confirmDisable?: boolean;
  cancelButtonText?: string;
  requestParams?: any;
};

type State = {
  isLoading: boolean;
};

class ComDialog extends React.Component<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
    };
  }

  renderFooter() {
    const {
      loading = false,
      onOk,
      onClose,
      confirmDisable = false,
      cancelButtonText = intl('saenext.components.shared.ComDialog.Cancel'),
    } = this.props;
    return (
      <div>
        {onOk ? (
          <Button type="primary" loading={loading} onClick={onOk} disabled={confirmDisable}>
            {intl('saenext.components.shared.ComDialog.Confirm')}
          </Button>
        ) : null}

        {onClose ? (
          <Button disabled={loading} onClick={onClose} style={{ marginLeft: '8px' }}>
            {cancelButtonText}
          </Button>
        ) : null}
      </div>
    );
  }

  fetchDataSource() {
    // const { onSuccess = $.noop, onError = $.noop } = this.props;
    // const { url, data, type } = this.props.requestParams || {};
    // if (!url) return;
    // this.setState({ isLoading: true });
    // saeFetch({ url, data, type })
    //   .then((res) => this.setState({ isLoading: false }, () => onSuccess(res)))
    //   .catch((res) => this.setState({ isLoading: false }, () => onError(res)));
  }
  componentDidMount() {
    this.fetchDataSource();
  }
  render() {
    const { loading = false, ...others } = this.props;
    const { isLoading } = this.state;
    return (
      <Dialog footer={this.renderFooter()} {...others}>
        <Loading style={{ width: '100%', height: '100%' }} visible={loading || isLoading}>
          {/* @ts-ignore */}
          {this.props.children}
        </Loading>
      </Dialog>
    );
  }
}
export default ComDialog;
