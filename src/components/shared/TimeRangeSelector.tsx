import { intl } from '@ali/cnd';
import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Field, Select } from '@ali/cnd';
import moment from 'moment';
import styled from 'styled-components';
import TimeContainer from './TimeContainer';
import { isJSON } from '~/utils/global';

export const LOCALSTORAGE_KEY = 'TIME_RANGE_PRE_FOR_MONITORING';

export const RECENT_OPTION = [
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastMinutes'),
    value: 'last_15_minutes',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastMinutes.1'),
    value: 'last_half_hour',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastHour'),
    value: 'last_hour',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastHours'),
    value: 'last_3_hours',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastHours.1'),
    value: 'last_6_hours',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastHours.2'),
    value: 'half_one_day',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastDay'),
    value: 'last_24_hours',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastDays'),
    value: 'last_three_days',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeSelector.LastWeek'),
    value: 'half_7_day',
    type: false,
  },
  // { label: '最近14天', value: 'last_two_weeks', type: false },
];

const RECENT_TIMES = {
  last_5_minutes: () => [moment().valueOf() - 5 * 60 * 1000, moment().valueOf()],
  last_10_minutes: () => [moment().valueOf() - 10 * 60 * 1000, moment().valueOf()],
  last_15_minutes: () => [moment().valueOf() - 15 * 60 * 1000, moment().valueOf()],
  last_half_hour: () => [moment().valueOf() - 30 * 60 * 1000, moment().valueOf()],
  last_hour: () => [moment().valueOf() - 60 * 60 * 1000, moment().valueOf()],
  last_3_hours: () => [moment().valueOf() - 3 * 60 * 60 * 1000, moment().valueOf()],
  last_6_hours: () => [moment().valueOf() - 6 * 60 * 60 * 1000, moment().valueOf()],
  last_24_hours: () => [moment().valueOf() - 24 * 60 * 60 * 1000, moment().valueOf()],
  half_one_day: () => [moment().valueOf() - 12 * 60 * 60 * 1000, moment().valueOf()],
  half_7_day: () => [
    moment().subtract(7, 'days').valueOf(),
    moment().valueOf(),

    ,
    moment().valueOf(),
  ],

  today: () => [moment().startOf('day').valueOf(), moment().valueOf()],
  last_three_days: () => [moment().valueOf() - 3 * 24 * 60 * 60 * 1000, moment().valueOf()],
  this_week: () => [moment().startOf('week').add(1, 'd').valueOf(), moment().valueOf()],
  last_two_weeks: () => [moment().valueOf() - 2 * 7 * 24 * 60 * 60 * 1000, moment().valueOf()],
  last_three_weeks: () => [moment().valueOf() - 3 * 7 * 24 * 60 * 60 * 1000, moment().valueOf()],
  this_month: () => [moment().startOf('month').valueOf(), moment().valueOf()],
  last_two_months: () => [moment().valueOf() - 2 * 30 * 24 * 60 * 60 * 1000, moment().valueOf()],
};

function isNumber(value) {
  return typeof value === 'number' && isFinite(value);
}

export function toSecond(timestamp) {
  if (timestamp === -1) {
    return -1;
  } else if (isNumber(timestamp)) {
    return Math.floor(timestamp / 1000) * 1000;
  } else {
    return timestamp;
  }
}

export function getTimes(key) {
  const getTimes = RECENT_TIMES[key];
  if (getTimes) {
    const [start, end] = getTimes();
    return [toSecond(start), toSecond(end)];
  } else {
    return [];
  }
}

export function getStorage() {
  let localStorage = window.localStorage.getItem(LOCALSTORAGE_KEY);
  return isJSON(localStorage) && localStorage !== '{}' ? JSON.parse(localStorage) : '';
}

interface TimeRangeProps {
  onTimeChanged: Function;
  localStorageKey?: string;
  secMetricsStatus?: boolean;
  periodInitValue?: number;
  timeInitValue?: any;
  onRef?: any;
  defaultTime?: boolean;
  options?: {
    label: string;
    value: string;
    type: boolean;
  }[];
  width?: any;
  disabledDate?: [number, string];
  showSecondGranularity?: boolean;
}

const TimeRangeSelector: React.FC<TimeRangeProps> = ({
  onRef,
  onTimeChanged,
  localStorageKey = LOCALSTORAGE_KEY,
  secMetricsStatus = false,
  periodInitValue = undefined,
  timeInitValue = undefined,
  defaultTime = true,
  options,
  width,
  disabledDate = [1, 'months'],
  showSecondGranularity = true,
}) => {
  const [periodList, setPeriodList] = useState(options || RECENT_OPTION);
  const [period, setPeriod]: [number, any] = useState(periodInitValue);
  const [timeValue, setTimeValue] = useState([]);
  const [initiLoading, setInitiLoading] = useState(false);
  const field = Field.useField();
  const { setValue } = field;

  useEffect(() => {
    let storage = getLocalStorage();
    if (storage && defaultTime) {
      getPeriod(storage.recentTiem, '', false);
      setValue('time', [moment(storage.start), moment(storage.end)]);
      setInitiLoading(true);
      setPeriod(storage.period);
      onTimeChanged({
        start: toSecond(storage.start),
        end: toSecond(storage.end),
        key: storage.recentTiem,
        period: storage.period,
      });
    } else {
      const [start, end] = getTimes(timeInitValue || 'last_6_hours');
      setLocalStorage(start, end, period || 60, timeInitValue || 'last_6_hours');
      setInitiLoading(true);
      getPeriod(timeInitValue || 'last_6_hours', [start, end]);
      (secMetricsStatus || !timeInitValue) && setTimeValue([start, end]);
    }
  }, []);

  useEffect(() => {
    let storage = getLocalStorage();
    let dataList = setDataSourceList([storage.start, storage.end]);
    let periodList = dataList.filter((item) => !item.disabled);
    if (!secMetricsStatus && storage.period == 10) {
      // setPeriod(periodList[0].value);
      setLocalStorage(storage.start, storage.end, periodList[0].value, storage.recentTiem);
    } else {
      setPeriod(storage.period);
    }

    if (storage.period === 10 && !showSecondGranularity) {
      handleChange(60);
    }

    // setDataSource([...dataList]);
  }, [secMetricsStatus, showSecondGranularity]);

  useEffect(() => {
    if (initiLoading) {
      getPeriodSelect(timeValue, true);
    }
  }, [timeValue]);

  const getPeriodSelect = (timeValue, isCall) => {
    let dataList = setDataSourceList(timeValue);
    let periodList = dataList.filter((item) => !item.disabled);
    let storage = getLocalStorage();
    if (periodList.length) {
      let key = storage.recentTiem;
      const [start, end] = timeValue;
      setPeriod(isCall ? periodList[0].value : storage.period);
      setValue('time', [moment(start), moment(end)]);
      setLocalStorage(start, end, isCall ? periodList[0].value : storage.period, key);
      isCall &&
        onTimeChanged({
          start: toSecond(start),
          end: toSecond(end),
          key,
          period: isCall ? periodList[0].value : storage.period,
        });
    }
    // setDataSource([...dataList]);
  };

  const setDataSourceList = (timeValue) => {
    const timeInterval = parseInt(timeValue[1]) - parseInt(timeValue[0]);
    const disabledH = isNaN(timeInterval) || timeInterval <= 3601000;
    const disabledMin = isNaN(timeInterval) || timeInterval > 86500000;
    const dataSource = [
      {
        value: 60,
        label: intl('saenext.components.shared.TimeRangeSelector.Minute'),
        disabled: disabledMin,
      },
      {
        value: 300,
        label: intl('saenext.components.shared.TimeRangeSelector.Minutes'),
        disabled: isNaN(timeInterval) || timeInterval > 5 * 86500000,
      },
      {
        value: 900,
        label: intl('saenext.components.shared.TimeRangeSelector.Minutes.1'),
        disabled: isNaN(timeInterval) || timeInterval > 15 * 86500000,
      },
      {
        value: 1800,
        label: intl('saenext.components.shared.TimeRangeSelector.Minutes.2'),
        disabled: isNaN(timeInterval) || timeInterval > 30 * 86500000,
      },
      {
        value: 3600,
        label: intl('saenext.components.shared.TimeRangeSelector.Hour'),
        disabled: disabledH,
      },
      {
        value: 86400,
        label: intl('saenext.components.shared.TimeRangeSelector.Day'),
        disabled: isNaN(timeInterval) || timeInterval <= 86401000,
      },
    ];

    return dataSource;
  };

  const handleChange = (period) => {
    const storage = getLocalStorage();
    const key = storage.recentTiem;
    let start = timeValue[0] || storage.start;
    let end = timeValue[1] || storage.end;
    setPeriod(period);
    setLocalStorage(start, end, period, key);
    onTimeChanged({ start: toSecond(start), end: toSecond(end), key, period });
  };

  const setLocalStorage = (start, end, period, recentTiem) => {
    window.localStorage.setItem(
      localStorageKey,
      JSON.stringify({ start, end, period, recentTiem }),
    );
  };

  const getLocalStorage = () => {
    let localStorage = window.localStorage.getItem(localStorageKey);
    return isJSON(localStorage) && localStorage !== '{}' ? JSON.parse(localStorage) : '';
  };

  const getPeriod = (value, isCall = undefined, isTimeValue = true) => {
    periodList.map((item) => {
      if (item.value == value) {
        item.type = true;
        isTimeValue && setTimeValue(isCall ? isCall : getTimes(item.value));
      } else {
        item.type = false;
      }
    });
    setPeriodList([...periodList]);
  };

  const getFaPeriod = (times) => {
    setTimeValue(times);
  };

  const periodChange = (record) => {
    if (record.type) return false;
    let nextPeriod = record.value;
    const [start, end] = RECENT_TIMES[nextPeriod]();
    getPeriod(nextPeriod);
    setLocalStorage(start, end, period, nextPeriod);
    if (!RECENT_TIMES[nextPeriod]) return false;
    setValue('time', [moment(start), moment(end)]);
    setTimeValue([start, end]);
  };

  useImperativeHandle(onRef, () => ({
    onChild: (times) => {
      getPeriodSelect(times, false);
    },
    onRangeChanged: (times) => {
      let dataList = setDataSourceList(times);
      let periodList = dataList.filter((item) => !item.disabled);
      const period = periodList[0].value;
      setPeriod(period);
      window.localStorage.setItem(
        localStorageKey,
        JSON.stringify({
          start: times[0],
          end: times[1],
          // period: period,
          recentTiem: 'custom',
        }),
      );
      getPeriodSelect(times, false);
      getPeriod('custom');
      return period;
    },
    refresh: () => {
      const { recentTiem } = getLocalStorage();
      if (recentTiem !== 'custom') periodChange({ type: false, value: recentTiem });
    },
    field: field,
  }));

  return (
    <TimeRange>
      <Select
        defaultValue={timeInitValue}
        onChange={(value, actionType, item) => periodChange(item)}
        dataSource={periodList}
      />

      <TimeContainer
        field={field}
        timeInitValue={timeInitValue}
        initValue={getTimes(timeInitValue || getLocalStorage().recentTiem)}
        getFaPeriod={getFaPeriod}
        periodChange={getPeriod}
        getLocalStorage={getLocalStorage}
        period={period}
        localStorageKey={localStorageKey}
        onTimeChanged={onTimeChanged}
        width={width}
        disabledDate={disabledDate}
      />
    </TimeRange>
  );
};

export default TimeRangeSelector;

const TimeRange = styled.div`
  display: flex;
  align-items: center;
  .next-select-inner {
    border-right: none;
    border-radius: 0px !important;
    width: 120px !important;
    min-width: 120px !important;
  }
  .next-range-picker-trigger {
    border-radius: 0px !important;
  }
`;
