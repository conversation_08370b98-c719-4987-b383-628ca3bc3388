import { intl } from '@ali/cnd';
import React from 'react';
import { Form, SwitchField, TextField } from '@ali/deep';
import { Dialog } from '@ali/cnd';
import If from '~/components/shared/If';

const formItemLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const PhpArmsMonitor = (props) => {
  const { value = '', onChange = () => {} } = props;

  const onSwitchChange = ({ value }) => {
    if (!value) {
      Dialog.confirm({
        content: (
          <>{intl('saenext.shared.PhpArmsMonitorField.PhpArmsMonitor.AfterYouDisableTheBasic')}</>
        ),
        onOk: () => {
          onChange('');
        },
      });
    } else {
      onChange('/usr/local/etc/php/conf.d/arms.ini');
    }
  };

  return (
    <Form>
      <SwitchField
        value={!!value}
        onChange={onSwitchChange}
        label={intl(
          'saenext.shared.PhpArmsMonitorField.PhpArmsMonitor.EnableApplicationMonitoring',
        )}
        help={intl('saenext.shared.PhpArmsMonitorField.PhpArmsMonitor.ToUseTheBuiltIn')}
        {...formItemLayout}
      />

      <If condition={!!value}>
        <TextField
          label={intl('saenext.shared.PhpArmsMonitorField.PhpArmsMonitor.MountDirectory')}
          value={value}
          disabled
          {...formItemLayout}
        />
      </If>
    </Form>
  );
};

export default PhpArmsMonitor;
