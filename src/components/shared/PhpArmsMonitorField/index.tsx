import React from 'react';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';
import PhpArmsMonitor from './PhpArmsMonitor';

class PhpArmsMonitorField extends BaseField {
  props;
  static displayName = 'PhpArmsMonitorField';

  getProps() {
    return {
      ...this.props
    };
  }


  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <PhpArmsMonitor {...newProps} />;
  }
}

export default ConfigProvider.config(PhpArmsMonitorField as any)
