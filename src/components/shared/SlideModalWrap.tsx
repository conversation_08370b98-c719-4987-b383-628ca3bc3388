import React, { useEffect, useState } from 'react'
import AddPropsWrap from './AddPropsWrap';
import { SlidePanel } from '@ali/cnd';
import { isUndefined } from 'lodash';
import { getParams, setSearchParams } from '~/utils/global';
import ModalWrap from './ModalWrap';
import modelDecorator from './Modal/decorator';

const SlideModalWrap = (props) => {
  const {
    toggleModal,
    children,
    content,
    onMaskClick,
    onClose,
    onCancel,
    onOk,
    ...rest
  } = props;

  const toggleVisible = () => {
    toggleModal({
      visible: false,
    });
  }

  const toggleAfter = async (fun) => {
    const res = await fun();
    if (isUndefined(res) || res) {
      toggleVisible();
    }
  }

  return (
    <>
      <ModalWrap
        {...rest}
        type="slide"
        trigger={children}
        onMaskClick={onMaskClick && (() => toggleAfter(onMaskClick))}
        onClose={onClose && (() => toggleAfter(onClose))}
        onCancel={onCancel && (() => toggleAfter(onCancel))}
        onOk={onOk && (() => toggleAfter(onOk))}
      >
        {content}
      </ModalWrap>
    </>
  )
}

export default modelDecorator(SlideModalWrap);