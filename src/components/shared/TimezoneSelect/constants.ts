export const TimeZones = [
  {
    "value": "+00:00",
    "label": "UTC (GMT+0:00)",
    "offset": 0,
  },
  {
    "value": "+01:00",
    "label": "Europe/Czech (GMT+1:00)",
    "offset": 1,
  },
  {
    "value": "+02:00",
    "label": "Europe/Egypt (GMT+2:00)",
    "offset": 2,
  },
  {
    "value": "+03:00",
    "label": "Africa/Ethiopia (GMT+3:00)",
    "offset": 3,
  },
  {
    "value": "+03:30",
    "label": "Africa/Iran (GMT+3:30)",
    "offset": 3.5,
  },
  {
    "value": "+04:00",
    "label": "Asia/Azerbaijan (GMT+4:00)",
    "offset": 4,
  },
  {
    "value": "+04:30",
    "label": "Asia/Afghanistan (GMT+4:30)",
    "offset": 4.5,
  },
  {
    "value": "+05:00",
    "label": "Asia/Pakistan (GMT+5:00)",
    "offset": 5,
  },
  {
    "value": "+05:30",
    "label": "Asia/India (GMT+5:30)",
    "offset": 5.5,
  },
  {
    "value": "+06:00",
    "label": "Asia/Bangladesh (GMT+6:00)",
    "offset": 6,
  },
  {
    "value": "+06:30",
    "label": "Asia/Myanmar (GMT+6:30)",
    "offset": 6.5,
  },
  {
    "value": "+07:00",
    "label": "Asia/Indonesia (GMT+7:00)",
    "offset": 7,
  },
  {
    "value": "+08:00",
    "label": "Asia/Shanghai (GMT+8:00)",
    "offset": 8,
  },
  {
    "value": "+08:30",
    "label": "Asia/Long White Time Zone (GMT+8:30)",
    "offset": 8.5,
  },
  {
    "value": "+09:00",
    "label": "Asia/Japan (GMT+9:00)",
    "offset": 9
  },
  {
    "value": "+10:00",
    "label": "Oceania/Australia (GMT+10:00)",
    "offset": 10
  },
  {
    "value": "+11:00",
    "label": "Oceania/Solomon Islands (GMT+11:00)",
    "offset": 11
  },
  {
    "value": "+12:00",
    "label": "Oceania/Fiji (GMT+12:00)",
    "offset": 12
  },
  {
    "value": "-01:00",
    "label": "Africa/Cape Verde (GMT-1:00)",
    "offset": -1
  },
  {
    "value": "-02:00",
    "label": " America/South Georgia (GMT-2:00)",
    "offset": -2
  },
  {
    "value": "-03:00",
    "label": " America/Brazil (GMT-3:00)",
    "offset": -3
  },
  {
    "value": "-04:00",
    "label": " America/Bolivia (GMT-4:00)",
    "offset": -4
  },
  {
    "value": "-04:30",
    "label": " America/Venezuela (GMT-4:30)",
    "offset": -4.5
  },
  {
    "value": "-05:00",
    "label": " America/NewYork (GMT-5:00)",
    "offset": -5
  },
  {
    "value": "-06:00",
    "label": " America/Mexico (GMT-6:00)",
    "offset": -6
  },
  {
    "value": "-07:00",
    "label": " America/Arizona (GMT-7:00)",
    "offset": -7
  },
  {
    "value": "-08:00",
    "label": " America/Los Angeles (GMT-8:00)",
    "offset": -8
  },
  {
    "value": "-09:00",
    "label": "America/Alaska (GMT-9:00)",
    "offset": -9
  },
  {
    "value": "-10:00",
    "label": "America/Cook Islands (GMT-10:00)",
    "offset": -10
  },
  {
    "value": "-11:00",
    "label": "Oceania/Samoa (GMT-11:00)",
    "offset": -11
  },
  {
    "value": "-12:00",
    "label": "West of International Date Lin (GMT-12:00)",
    "offset": -12
  }
]

export const TimeZoneIds = [
  {
    label: '(UTC-12:00) Etc/GMT+12',
    value: 'Etc/GMT+12',
  },
  {
    label: '(UTC-11:00) Pacific/Pago_Pago',
    value: 'Pacific/Pago_Pago',
  },
  {
    label: '(UTC-10:00) Pacific/Honolulu',
    value: 'Pacific/Honolulu',
  },
  {
    label: '(UTC-09:00) America/Anchorage',
    value: 'America/Anchorage',
  },
  {
    label: '(UTC-08:00) America/Los_Angeles',
    value: 'America/Los_Angeles',
  },
  {
    label: '(UTC-07:00) America/Denver',
    value: 'America/Denver',
  },
  {
    label: '(UTC-06:00) America/Chicago',
    value: 'America/Chicago',
  },
  {
    label: '(UTC-05:00) America/New_York',
    value: 'America/New_York',
  },
  {
    label: '(UTC-04:00) America/Barbados',
    value: 'America/Barbados',
  },
  {
    label: '(UTC-03:00) America/Argentina/Buenos_Aires',
    value: 'America/Argentina/Buenos_Aires',
  },
  {
    label: '(UTC-02:00) America/Noronha',
    value: 'America/Noronha',
  },
  {
    label: '(UTC-01:00) Atlantic/Cape_Verde',
    value: 'Atlantic/Cape_Verde',
  },
  {
    label: '(UTC+00:00) UTC',
    value: 'UTC',
  },
  {
    label: '(UTC+01:00) Europe/Paris',
    value: 'Europe/Paris',
  },
  {
    label: '(UTC+02:00) Africa/Cairo',
    value: 'Africa/Cairo',
  },
  {
    label: '(UTC+03:00) Europe/Moscow',
    value: 'Europe/Moscow',
  },
  {
    label: '(UTC+04:00) Asia/Dubai',
    value: 'Asia/Dubai',
  },
  {
    label: '(UTC+05:00) Asia/Karachi',
    value: 'Asia/Karachi',
  },
  {
    label: '(UTC+05:30) Asia/Kolkata',
    value: 'Asia/Kolkata',
  },
  {
    label: '(UTC+06:00) Asia/Dhaka',
    value: 'Asia/Dhaka',
  },
  {
    label: '(UTC+07:00) Asia/Bangkok',
    value: 'Asia/Bangkok',
  },
  {
    label: '(UTC+08:00) Asia/Shanghai',
    value: 'Asia/Shanghai',
  },
  {
    label: '(UTC+09:00) Asia/Tokyo',
    value: 'Asia/Tokyo',
  },
  {
    label: '(UTC+10:00) Australia/Sydney',
    value: 'Australia/Sydney',
  },
  {
    label: '(UTC+11:00) Pacific/Guadalcanal',
    value: 'Pacific/Guadalcanal',
  },
  {
    label: '(UTC+12:00) Pacific/Auckland',
    value: 'Pacific/Auckland',
  },
  {
    label: '(UTC+13:00) Pacific/Apia',
    value: 'Pacific/Apia',
  },
  {
    label: '(UTC+14:00) Pacific/Kiritimati',
    value: 'Pacific/Kiritimati',
  }
]