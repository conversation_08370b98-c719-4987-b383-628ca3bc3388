import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Field, Form, Message, Select, Switch } from '@ali/cnd';
import { Truncate } from '@ali/xconsole';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';
import RefreshButton from '~/components/shared/RefreshButton';
import C from '~/constants/common';
import services from '~/services';
import { get } from 'lodash';
import useRegion from '~/hooks/useRegion';
import CachedData from '~/cache/common';

const OidcRole = (props) => {
  const { field, name } = props;

  const { getValue, setValue, setError } = field || {};

  const defaultValue = getValue?.(name);

  const [roleList, setRoleList] = useState([]);
  const [authed, setAuthed] = useState(true);

  const openField = Field.useField();

  const oidcOpen = openField.getValue('oidcOpen') as boolean;

  const regionId = useRegion();

  useEffect(() => {
    if (defaultValue) {
      getRoleList();
      openField.setValue('oidcOpen', true);
    }
  }, [defaultValue]);

  const onOpen = (value) => {
    if (value) {
      createOidc();
    } else {
      setValue(name, '');
      setError(name, '');
    }
  };

  const createOidc = async () => {
    const data = await services.createOIDCProvider({
      params: {
        RegionId: regionId,
      },
    });

    if (!data) {
      openField.setValue('oidcOpen', false);
      openField.setError('oidcOpen', intl('saenext.shared.OidcRole.FailedToEnablePleaseTry'));
    } else {
      openField.setError('oidcOpen', '');
      getRoleList();
    }
  };

  const getRoleList = async () => {
    const res = await services.ListRoles({
      params: {
        MaxItems: 1000,
      },
      customErrorHandle: (err, data, callback) => {
        if (err.code === 'NoPermission') {
          setAuthed(false);
          return;
        }
        callback();
      },
    });

    if (res) {
      setAuthed(true);
    }

    const data = get(res, 'Roles.Role', []);
    const dataSource = data.map((item) => ({
      ...item,
      label: item.RoleName,
      value: item.RoleName,
    }));
    setRoleList(dataSource);
  };

  const itemRender = (item) => {
    return (
      <div className="flex" style={{ width: 600 }}>
        <span>{item.RoleName}</span>
        <span className="color-gray ml" style={{ flexGrow: 1, width: 0 }}>
          <Truncate type="width" threshold="auto" align="t" style={{ width: '100%' }}>
            {item.Description}
          </Truncate>
        </span>
      </div>
    );
  };

  return (
    <>
      <Form field={openField}>
        <Form.Item label={intl('saenext.shared.OidcRole.EnableRrsaOidc')} {...C.FORM_LAYOUT_LEFT}>
          <Switch name="oidcOpen" onChange={onOpen} />
        </Form.Item>
      </Form>
      <Message type="warning" className={oidcOpen ? 'mb' : 'none'}>
        {intl('saenext.shared.OidcRole.WhenCreatingARamRole')}
      </Message>
      <Form.Item
        label={
          <UnAuthedLabel
            authed={authed}
            text={intl('saenext.shared.OidcRole.RamRole')}
            authKey="ram:ListRoles"
          />
        }
        required={oidcOpen}
        requiredMessage={intl('saenext.shared.OidcRole.SelectARamRole')}
        asterisk={false}
        {...C.FORM_LAYOUT_LEFT}
        className={oidcOpen ? '' : 'none'}
      >
        <Select
          name={name}
          dataSource={roleList}
          showSearch
          itemRender={itemRender}
          style={{ width: 500 }}
        />

        <RefreshButton handler={getRoleList} className="ml" />

        <a href={`${CachedData.confLink('feature:ram:url')}/roles`} target="_blank" className="ml">
          {intl('saenext.shared.OidcRole.CreateARamRole')}
        </a>
      </Form.Item>
    </>
  );
};

export default OidcRole;
