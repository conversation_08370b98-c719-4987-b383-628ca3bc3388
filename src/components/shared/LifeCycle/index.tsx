import React, { useState, useEffect, useContext } from 'react';
import { Form, Field, intl, Message, NumberPicker, ShellEditor, Tab } from '@ali/cnd';
import TextWithBalloon from '../TextWithBalloon';
import C from '~/constants/common';
import ExecCmdShell from './ExecCmdShell';
import FeatureContext from '~/utils/featureContext';

const LifeCycle = (props) => {
  const { field, NewSaeVersion = '', appConfig, microserviceEngineConfig, hasMicroService } = props;
  const [microServiceEnable, setMicroServiceEnable] = useState(false);
  const [activeKey, setActiveKey] = useState('PostStart');
  const { inDebtData } = useContext(FeatureContext);
  const MaxPreStopTime = inDebtData?.MaxPreStopTime || 300;

  useEffect(() => {
    checkMicroServiceEnable();
  }, [NewSaeVersion, appConfig, microserviceEngineConfig, hasMicroService]);

  useEffect(() => {
    // 根据是否开通了微服务治理设置默认值
    if (!appConfig?.TerminationGracePeriodSeconds) {
      field?.setValue('TerminationGracePeriodSeconds', microServiceEnable ? 60 : 30);
    }
  }, [microServiceEnable, activeKey]);

  // 判断是否开启了微服务治理
  const checkMicroServiceEnable = () => {
    if (hasMicroService) {
      if (microserviceEngineConfig) {
        setMicroServiceEnable(true);
        const enable = JSON.parse(microserviceEngineConfig)?.Enable;
        setMicroServiceEnable(enable);
      } else {
        setMicroServiceEnable(false);
      }
    } else {
      if (appConfig?.SaeVersion === 'v2' && appConfig?.MseApplicationId) {
        setMicroServiceEnable(true);
      } else {
        setMicroServiceEnable(false);
      }
    }
  };

  const commandLendthValidator = (rule, value, callback) => {
    if (value && value.length > 1024) {
      callback(intl('saenext.shared.LifeCycle.TheLengthCannotExceed'));
    } else {
      callback();
    }
  };

  return (
    <Tab lazyLoad={false} activeKey={activeKey} onChange={setActiveKey}>
      <Tab.Item
        key="PostStart"
        title={intl('saenext.shared.LifeCycleField.LifeCycleConf.AfterStartupPoststartSettings')}
      >
        <div className="mt-l">
          <p style={{ color: '#999' }}>
            {intl('saenext.shared.LifeCycleField.LifeCycleConf.AfterTheApplicationContainerIs')}
          </p>
          <Form.Item validator={commandLendthValidator}>
            <ExecCmdShell name="PostStart" />
          </Form.Item>
        </div>
      </Tab.Item>
      <Tab.Item
        key="PreStop"
        title={intl('saenext.shared.LifeCycleField.LifeCycleConf.StopPreProcessingPrestopSettings')}
      >
        <div className="mt-l">
          <p style={{ color: '#999' }}>
            {intl('saenext.shared.LifeCycleField.LifeCycleConf.TasksBeforeTheApplicationContainer')}
          </p>
          <Form.Item validator={commandLendthValidator}>
            <ExecCmdShell name="PreStop" />
          </Form.Item>
        </div>
      </Tab.Item>
      <Tab.Item
        key="TerminationGracePeriodSeconds"
        title={intl(
          'saenext.shared.LifeCycleField.LifeCycleConf.ElegantOfflineTimeoutSettingTerminationgraceperiodseconds',
        )}
      >
        <div className="mt-l">
          <p style={{ color: '#999' }}>
            {intl('saenext.shared.LifeCycleField.LifeCycleConf.SetTheTimeoutPeriodFor')}
          </p>
          <Form.Item
            label={
              <TextWithBalloon
                text={intl('saenext.shared.LifeCycleField.LifeCycleConf.ElegantOfflineTimeout')}
                tips={intl('saenext.shared.LifeCycleField.LifeCycleConf.ThatIsHowLongDoes')}
              />
            }
            extra={intl('saenext.shared.LifeCycleField.LifeCycleConf.MaximumKesheSeconds.1',{max:MaxPreStopTime})}
            {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
            validator={(rule, value: number, callback: any) => {
              if (microServiceEnable && value < 60) {
                setActiveKey('TerminationGracePeriodSeconds');
                callback(intl('saenext.shared.LifeCycle.MicroserviceGovernanceIsEnabledThe'));
              } else {
                callback();
              }
            }}
          >
            <NumberPicker
              // @ts-ignore
              name="TerminationGracePeriodSeconds"
              defaultValue={30}
              style={{ width: 200 }}
              min={microServiceEnable ? 60 : 30}
              max={MaxPreStopTime}
            />
          </Form.Item>
        </div>
      </Tab.Item>
    </Tab>
  );
};

export default LifeCycle;
