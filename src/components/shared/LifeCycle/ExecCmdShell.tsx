import React, { useMemo } from 'react';
import { Input } from '@ali/cnd';
import { get, isEmpty } from 'lodash';
import { jsonParse, jsonStringify } from '~/utils/transfer-data';
import CommandShell from '../CommandShell';

const ExecCmdShell = (props) => {
  const { value, onChange } = props;

  const cmdArgsArr = useMemo(() => {
    if (isEmpty(value)) return [];
    
    const valueObj = jsonParse(value);
    const cmdArgsArray = get(valueObj, 'exec.command', []);
    return cmdArgsArray;
  }, [value]);

  const onCmdChange = (v: string[]) => {
    if (isEmpty(v)) {
      onChange('');
    } else {
      const valueObj = {
        exec: {
          command: v,
        }
      }
      onChange(jsonStringify(valueObj))
    }
  };

  return (
    <CommandShell
      value={cmdArgsArr}
      onChange={onCmdChange}
    />
  )
}

export default ExecCmdShell;