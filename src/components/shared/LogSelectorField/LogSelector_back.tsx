import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Form, Field, Message } from '@ali/cnd';
import { isEmpty, map, get, debounce } from 'lodash';
import CachedData from '~/cache/common';
import LogFormItem from './LogFormItem';
import { DEFAULTTAIL } from './Constant';

type Props = {
  value: any;
  isUpdate?: boolean;
  rootLogEnabled?: boolean;
  onChange: (value: any) => void;
  showSlsConfig: boolean;
};

type State = {
  logSelector: {
    slsConfig: any;
    logConfig: any;
  };
  logValues: {};
};

type LogSelectorValue = {
  logEnabled: boolean;
  metricsEnabled: boolean;
  useNewResouces: boolean;
  projectName?: string;
  logstoreName?: string;
  collectConfigs: {
    logType: 'file' | 'stdout';
    logstoreName?: string;
    logtailName?: string;
    logPath: string;
  }[];
};

class LogSelector extends Component<Props, State> {
  field = new Field(this, {
    onChange: () => {
      debounce(this.fieldValueChange, 500).call(this);
    },
  });

  constructor(props) {
    super(props);
    const { value, isUpdate, onChange } = props;
    this.state = {
      logSelector: {
        slsConfig: isUpdate ? {} : null,
        logConfig: isUpdate ? {} : null,
      },
      logValues: {},
    };
    onChange && onChange(this.state.logSelector);
    // 如果不为空
    if (!isEmpty(value)) this.initStates();
  }

  componentDidUpdate(prevProps, prevState) {
    const { value } = this.props;
    if (prevProps.value !== value) {
      // 重新赋值
      this.initStates();
    }
  }

  initStates() {
    const { value = {} } = this.props;
    const { logConfig, slsConfig } = value;
    if (!logConfig && !slsConfig) return;
    if (!logConfig.project && logConfig.pushToUserSLS) return;

    let _logEnabled = !!Object.keys(logConfig || {}).length;
    const _slsEnabled = !!Object.keys(slsConfig || {}).length;

    const { collectConfigs = [] } = slsConfig || {};
    // 检查 collectConfigs 是否有 projectName 这里是兼容没有开启推送 但是选择了已有资源
    let project = null;
    if (collectConfigs && collectConfigs.length) {
      const [rlue] = collectConfigs;
      project = rlue.projectName;
    }

    // 新老日志 都有 logConfig
    if (logConfig) {
      _logEnabled = !!logConfig.project;

      // collectConfigs 和 logConfig 任取其一
      project = project || logConfig.project;
    }

    this.setState({
      logValues: {
        logEnabled: _logEnabled || _slsEnabled,
        ...logConfig,
        ...slsConfig,
        // 这里要考虑有规则收集 但没打开监控推送的可能
        project,
      },
    });
  }

  fieldValueChange() {
    const { validate, getValue } = this.field;
    const { isUpdate, onChange } = this.props;

    const defaultLogConfig = {
      project: null,
      logstore: null,
      pushToUserSLS: false,
    };
    let _logSelector = {
      // 更新状态下不能传null 关闭需要传{}
      slsConfig: isUpdate ? {} : null,
      logConfig: isUpdate ? defaultLogConfig : null,
    };
    // 当开关关闭的时候 不会触发下validate
    const _logEnabled = getValue('logEnabled');
    if (!_logEnabled) {
      onChange(_logSelector);
      return;
    }

    validate(async (error, values) => {
      if (error) return;
      const {
        useNewResouces = true,
        metricsEnabled = false,
        collectConfigs = [],
      } = values as unknown as LogSelectorValue;
      // 更新状态下关闭需要传{}
      let slsConfig = isUpdate ? {} : null;
      let logConfig = defaultLogConfig;

      // 处理 logConfig
      if (metricsEnabled) {
        Reflect.set(logConfig, 'pushToUserSLS', true);
        if (!useNewResouces) {
          const { projectName = null, logstoreName = null } = values as unknown as LogSelectorValue;
          Reflect.set(logConfig, 'project', projectName);
          Reflect.set(logConfig, 'logstore', logstoreName);
        }
      }

      // 处理 slsConfig
      let rules = [];
      const length = collectConfigs.length;
      for (let i = 0; i < length; i++) {
        const rule = collectConfigs[i] || {};
        const is = get(rule, 'logType', '');
        if (!is) break;
        rules.push(rule);
      }

      if (rules.length) {
        // 配置了采集日志
        slsConfig = {};
        let collectConfigs = [];
        if (useNewResouces) {
          collectConfigs = map(rules, (rule) => ({
            logType: rule.logType,
            logPath: rule.logPath,
            projectName: null,
            logstoreName: null,
          }));
        } else {
          // @ts-ignore
          const { projectName } = values;
          collectConfigs = map(rules, (rule) => {
            const { logtailName } = rule;
            const isTail = logtailName && logtailName !== DEFAULTTAIL;
            const config = {
              logType: rule.logType,
              projectName,
              logstoreName: rule.logstoreName,
            };
            if (isTail) {
              Reflect.set(config, 'logtailName', logtailName);
            } else {
              Reflect.set(config, 'logPath', rule.logPath);
            }
            return config;
          });
        }
        Reflect.set(slsConfig, 'collectConfigs', collectConfigs);
      }

      Object.assign(_logSelector, {
        slsConfig,
        logConfig,
      });
      console.log('logSelector: ', _logSelector);
      onChange(_logSelector);
    });
  }

  validate = () => {
    return new Promise((resolve, reject) => {
      this.field.validate((error, value) => {
        if (error) {
          resolve(false);
          return;
        }
        resolve(true);
      });
    });
  };

  render() {
    const { logValues } = this.state;
    const { rootLogEnabled, showSlsConfig } = this.props;
    return (
      <>
        {CachedData.getOpenStatus('logStatus') ? null : (
          <Message type="warning" className="mb-l">
            {intl('saenext.shared.LogSelectorField.LogSelector_back.YouHaveNotActivatedLog')}

            <a
              href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=sls`}
              target="_blank"
              className="ml-s"
            >
              {intl('saenext.shared.LogSelectorField.LogSelector_back.ActivateNow')}
            </a>
          </Message>
        )}

        <Form field={this.field}>
          <LogFormItem
            // @ts-ignore
            value={logValues}
            field={this.field}
            isUpdate={this.props.isUpdate}
            rootLogEnabled={rootLogEnabled}
            showSlsConfig={showSlsConfig}
          />
        </Form>
      </>
    );
  }
}

export default LogSelector;
