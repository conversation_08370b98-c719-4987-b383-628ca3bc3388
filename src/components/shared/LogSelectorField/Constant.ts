import { intl } from '@ali/cnd';
export const USE_CREATING_RESOURCES = 'useCreatingResouces';
export const PROJECT = 'project';
export const LOG_STORE = 'logstore';
export const REQUEST_METRICS = 'enableRequestMetrics';
export const INSTANCE_METRICS = 'enableInstanceMetrics';
export const LOGGING_ENABLED = 'loggingEnabled';
export const LOGINBEGINRULE = 'logBeginRule';
export const FILTER_LOGSTORE = ['internal-operation_log', 'internal-diagnostic_log'];
export const DEFAULTTAIL = intl('saenext.shared.LogSelectorField.Constant.CreateLogtail');
