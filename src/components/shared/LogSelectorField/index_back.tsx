import React from 'react';
import BaseField from '@ali/deep-form-helper';
import LogSelector from './LogSelector_back';
import { ConfigProvider } from '@ali/deep';

class LogSelectorField extends BaseField {
  props;
  static displayName = 'LogSelectorField';

  getProps() {
    return {
      ...this.props
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...fieldProps,
      ...this.props,
    };

    return <LogSelector {...newProps} />;
  }
}

export default ConfigProvider.config(LogSelectorField as any);