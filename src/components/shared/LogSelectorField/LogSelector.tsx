import { intl } from '@ali/cnd';
import { Select, Form, Radio, Balloon, Message, Field, Switch } from '@ali/xconsole';
import React, { Component } from 'react';
import * as LogService from '~/services/logging';
import {
  PROJECT,
  LOG_STORE,
  // REQUEST_METRICS,
  // INSTANCE_METRICS,
  LOGGING_ENABLED,
  // LOGINBEGINRULE,
  FILTER_LOGSTORE,
  USE_CREATING_RESOURCES,
} from './Constant';
import { get, map, isEmpty } from 'lodash';
// import TextWithBalloon from '~/components/TextWithBalloon';
import ExternalLink from '~/components/shared/ExternalLink';
import TextRefreshButton from '~/components/shared/TextRefreshButton';
import CachedData from '~/cache/common';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';

const { Option } = Select;
const RadioGroup = Radio.Group;
const Tooltip = Balloon.Tooltip;

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 17, align: 'center' },
  labelCol: {
    span: 7,
    style: {
      width: 170,
      maxWidth: 170,
    },
  },
};

type Props = {
  value: any;
  regionId: string;
  onChange: (value: any) => void;
  showCreate?: boolean;
};

type State = {
  fetchingProjects: boolean;
  fetchingLogStores: boolean;
  projects: any[];
  logStores: any[];
  selectedProject: string;
  logEnabled: boolean;
  useCreatingResouces: boolean;
  // loginBeginRule: string;
  projectNotExisited: boolean;
  isVirtualService: boolean;
  validate: any;
  logRamWarn: boolean;
};

class Logging extends Component<Props, State> {
  field = new Field(this, {
    onChange: () => {
      this.changeLogConfig();
    },
  });

  constructor(props) {
    super(props);
    const { value, showCreate = false } = props;
    this.state = {
      projects: [],
      logStores: [],
      fetchingProjects: false,
      fetchingLogStores: false,
      projectNotExisited: false,
      useCreatingResouces: showCreate,
      logEnabled: !!get(value, 'project'),
      isVirtualService: window.location.host.indexOf('4service') > -1,
      validate: this.validate.bind(this),
      logRamWarn: false,
    } as any;
    if (!isEmpty(value)) this.updateStates();
  }

  updateStates(isBackfill = false) {
    const { value } = this.props;
    const { setValue } = this.field;

    // 判断是否为系统自建 如果有 project， sae-开头的 为系统自建
    let _useCreatingResouces = true;
    if (!!get(value, 'project')) {
      const project = get(value, 'project');
      _useCreatingResouces = project.startsWith('sae-');
    }
    this.setState({
      ...this.state,
      selectedProject: get(value, 'project'),
      logEnabled: !!get(value, 'project'),
      useCreatingResouces: _useCreatingResouces,
      // loginBeginRule: get(value, 'logBeginRule') || 'None',
    });

    // setValue(REQUEST_METRICS, get(value, 'enableRequestMetrics'));
    // setValue(INSTANCE_METRICS, get(value, 'enableInstanceMetrics'));
    // setValue(LOGINBEGINRULE, get(value, 'logBeginRule') || 'None');
    setValue(LOG_STORE, get(value, 'logstore'));
    setValue(PROJECT, get(value, 'project'));
    setValue(LOGGING_ENABLED, !!get(value, 'project'));

    if (isEmpty(this.state.projects) && !!get(value, 'project')) {
      this.fetchProjects();
      this.fetchLogStores(get(value, 'project'));
    }
    !isBackfill && this.changeLogConfig();
  }

  componentDidUpdate(prevProps) {
    const { value } = this.props;
    if (get(value, 'project') !== get(prevProps.value, 'project')) {
      if (!isEmpty(value) && get(value, 'project')) this.updateStates(true);
    }
  }

  onProjectChanged(value) {
    const { setValue } = this.field;

    this.setState({
      selectedProject: value,
    });
    setValue(PROJECT, value);
    setValue(LOG_STORE, '');
    if (value) {
      this.fetchLogStores(value);
    } else {
      this.setState({ logStores: [] });
      this.changeLogConfig();
    }
  }

  async fetchLogStores(project) {
    if (!project) {
      return;
    }
    this.setState({ fetchingLogStores: true, projectNotExisited: false });
    let out = [];
    try {
      const { logstores = [] } = await LogService.ListStores({params:{ ProjectName: project} });

      out = logstores;
      this.setState({ logStores: out });
    } catch (e) {}
    this.setState({ fetchingLogStores: false });
  }

  async fetchProjects() {
    this.setState({ fetchingProjects: true });
    let out = [];
    try {
      // @ts-ignore
      const data = await LogService.ListProjects({
        customErrorHandle: (error, data) => {
          if (error.code === 'Unauthorized') {
            this.setState({
              logRamWarn: true,
            });
            return {};
          }
          return error;
        },
      });
      const projects = get(data, 'projects', []);
      out = map(projects, (project) => project.projectName);
      this.setState({ projects: out });
    } catch (e) {}
    this.setState({ fetchingProjects: false });
  }

  async beforeLogEnabledChanged(value) {
    this.onLogEnabledChanged(value);
    // if (!value) {
    //   this.onLogEnabledChanged(value);
    // } else {
    //   try {
    //     const {
    //       Statuses: { Status = [] },
    //     } = await LogService.DescribeUserBusinessStatus({
    //       ServiceCode: 'sls',
    //     });

    //     const enabled = Status.find(s => s.StatusKey === 'enabled' && s.StatusValue !== 'false');

    //     if (!enabled) {
    //       Dialog.alert({
    //         title: '日志服务 SLS 尚未开通',
    //         content: <span>您还没有开通云产品“日志服务 SLS”。如果您要在SAE 中使用“日志”功能，请先点击下方的“立即开通”按钮，跳转到开通界面。</span>,
    //         okProps: { children: '立即开通' },
    //         onOk: () => {
    //           window.open(
    //             `https://sls${this.state.isVirtualService ? '4service' : ''}.console.aliyun.com`,
    //           );
    //         },
    //         cancelProps: { children: '取消' },
    //       });
    //     } else {
    //       this.onLogEnabledChanged(value);
    //     }
    //   } catch (e) { }
    // }
  }

  onLogEnabledChanged(value) {
    this.setState({ logEnabled: value }, () => {
      if (value) {
        this.fetchProjects();
        this.changeLogConfig();
      } else {
        this.onProjectChanged('');
        this.setState({
          logRamWarn: false,
        });
      }
    });
    // loginBeginRule: value ? 'DefaultRegex' : 'None'
    // setValue(REQUEST_METRICS, value ? true : false);
    // setValue(INSTANCE_METRICS, value ? true : false);
    // setValue(LOGINBEGINRULE, value ? 'DefaultRegex' : 'None');
  }

  componentDidMount() {
    const { value } = this.props;
    const { logEnabled } = this.state;
    if (logEnabled) {
      this.fetchProjects();
      this.fetchLogStores(get(value, 'project'));
    }
  }

  itemRender(item) {
    if (item.disabled) {
      return (
        <Tooltip trigger={<li>{item.label}</li>}>
          {intl('saenext.shared.LogSelectorField.LogSelector.TheLogstoreIsCreatedBy')}
        </Tooltip>
      );
    } else {
      return <li>{item.label}</li>;
    }
  }

  validate = () => {
    const { validate } = this.field;
    return new Promise((resolve, reject) => {
      validate((error, value) => {
        if (error) {
          resolve(false);
          return;
        }
        resolve(true);
      });
    });
  };

  changeLogConfig() {
    const { getValues } = this.field;
    const { onChange } = this.props;
    const { logEnabled } = this.state;
    //  REQUEST_METRICS, INSTANCE_METRICS, LOGINBEGINRULE,
    const params = getValues([LOG_STORE, PROJECT, USE_CREATING_RESOURCES]) as any;
    onChange({
      ...params,
      enableRequestMetrics: logEnabled,
      enableInstanceMetrics: logEnabled,
      logBeginRule: 'None',
    });
  }

  render() {
    const { showCreate = false } = this.props;
    const field = this.field;
    const {
      selectedProject,
      fetchingProjects,
      projects,
      fetchingLogStores,
      logStores,
      logEnabled,
      useCreatingResouces,
      // loginBeginRule = 'None',
      projectNotExisited,
      isVirtualService,
      logRamWarn,
    } = this.state;
    return (
      <>
        {/* {CachedData.getOpenStatus('logStatus') ? null : (
          <Message type="warning" className="mb-l">
            {intl('saenext.shared.LogSelectorField.LogSelector.YouHaveNotActivatedLog')}

            <a
              href="https://common-buy.aliyun.com/?commodityCode=sls"
              target="_blank"
              className="ml-s"
            >
              {intl('saenext.shared.LogSelectorField.LogSelector.ActivateNow')}
            </a>
          </Message>
        )} */}

        <OpenStatusMessage
          product="sls"
        />

        <Form field={field} {...(fieldLayout as any)}>
          <Form.Item
            label={intl('saenext.shared.LogSelectorField.LogSelector.LogFunction')}
            help={intl('saenext.shared.LogSelectorField.LogSelector.WhenEnabledYouCanView')}
          >
            <Switch
              checked={logEnabled}
              disabled={!CachedData.getOpenStatus('logStatus')}
              onChange={(value) => this.beforeLogEnabledChanged(value)}
            />
          </Form.Item>

          {logEnabled && CachedData.isSubAccount() && logRamWarn ? (
            <Message type="warning" className="mb-l">
              {intl('saenext.shared.LogSelectorField.LogSelector.CurrentlyYourAccountHasNot')}

              <a
                href={`${CachedData.confLink('feature:ram:url')}/#/role/authorize`}
                target="_blank"
                className="ml-s mr-s"
              >
                {intl('saenext.shared.LogSelectorField.LogSelector.RamAccessControl')}
              </a>
              {intl('saenext.shared.LogSelectorField.LogSelector.UseTheProductAfterAuthorization')}
            </Message>
          ) : null}

          {logEnabled && showCreate && (
            <Form.Item label={' '}>
              <RadioGroup
                name={USE_CREATING_RESOURCES}
                value={useCreatingResouces}
                onChange={(value) =>
                  this.setState({
                    useCreatingResouces: Boolean(value),
                  })
                }
              >
                <Radio value={true}>
                  {intl('saenext.shared.LogSelectorField.LogSelector.CreateAnSlsResource')}
                </Radio>
                <Radio value={false}>
                  {intl('saenext.shared.LogSelectorField.LogSelector.UseExistingSlsResources')}
                </Radio>
              </RadioGroup>
            </Form.Item>
          )}

          {logEnabled && !useCreatingResouces && (
            <Form.Item
              required
              label={intl('saenext.shared.LogSelectorField.LogSelector.LogItems')}
              requiredMessage={intl(
                'saenext.shared.LogSelectorField.LogSelector.LogEntryIsRequired',
              )}
            >
              <Select
                disabled={fetchingProjects}
                name={PROJECT}
                className="full-width"
                showSearch
                value={selectedProject}
                onChange={(value) => this.onProjectChanged(value)}
                placeholder={intl('saenext.shared.LogSelectorField.LogSelector.SelectALogItem')}
              >
                {projects.map((project) => (
                  <Option disabled={project.startsWith('sae-')} key={project} value={project}>
                    {project.startsWith('sae-') ? (
                      <Balloon trigger={project} closable={false}>
                        {intl(
                          'saenext.shared.LogSelectorField.LogSelector.ProjectAutomaticallyCreatedByThe',
                        )}
                      </Balloon>
                    ) : (
                      project
                    )}
                  </Option>
                ))}
              </Select>
              <div className="flex">
                <TextRefreshButton onClick={() => this.fetchProjects()} className="mr-l" />
                <ExternalLink
                  label={intl('saenext.shared.LogSelectorField.LogSelector.CreateANewLogProject')}
                  url={`${CachedData.confLink('feature:sls:url')}/lognext/profile`}
                />
              </div>
            </Form.Item>
          )}

          {logEnabled && !useCreatingResouces && (
            <Form.Item
              required
              label={intl('saenext.shared.LogSelectorField.LogSelector.Logstore')}
              requiredMessage={intl(
                'saenext.shared.LogSelectorField.LogSelector.LogstoreIsRequired',
              )}
              extra={
                projectNotExisited ? (
                  <Message className="mt-s" type="warning">
                    <span>
                      {intl('saenext.shared.LogSelectorField.LogSelector.TheLogServiceProjectYou')}
                      <a
                        href={`${CachedData.confLink('feature:sls:url')}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {intl('saenext.shared.LogSelectorField.LogSelector.LogServiceConsole')}
                      </a>
                      {intl('saenext.shared.LogSelectorField.LogSelector.View')}
                    </span>
                  </Message>
                ) : (
                  <Message className="mt-s" type="help">
                    <ExternalLink
                      url={CachedData.confLink('help:sls:how-do-i-delete-logs')}
                      label={intl('saenext.shared.LogSelectorField.LogSelector.HowCanIAdjustThe')}
                    />
                  </Message>
                )
              }
            >
              <Select
                disabled={fetchingLogStores}
                name={LOG_STORE}
                className="full-width"
                showSearch
                placeholder={intl('saenext.shared.LogSelectorField.LogSelector.SelectALogstore')}
                dataSource={logStores.map((logStore) => {
                  return {
                    key: logStore,
                    value: logStore,
                    disabled: FILTER_LOGSTORE.includes(logStore),
                  };
                })}
                itemRender={this.itemRender}
              />

              <div className="flex">
                <TextRefreshButton
                  onClick={() => this.fetchLogStores(selectedProject)}
                  className="mr-l"
                />

                {selectedProject && (
                  <ExternalLink
                    label={intl('saenext.shared.LogSelectorField.LogSelector.CreateANewLogstore')}
                    url={`${CachedData.confLink('feature:sls:url')}/lognext/project/${selectedProject}/overview`}
                  />
                )}
              </div>
            </Form.Item>
          )}

          {/* {logEnabled && (
             <Form.Item
               label={
                 <TextWithBalloon
                   color="color-light-black"
                   align="tr"
                   text={'日志分割规则'}
                   tips={'开启日志分割后，将按照日期格式分割长日志内容，如包含多个"2021-11-11"内容的单条日志将被默认分割为多行，当前日期以及下一个日期前的所有内容将作为一条独立日志写入SLS'}
                 />
               }
             >
               <RadioGroup
                 name={LOGINBEGINRULE}
                 value={loginBeginRule}
                 onChange={v => this.setState({ loginBeginRule: v as string })}
                 shape="button"
                 size="medium"
               >
                 <Radio value="DefaultRegex">启用</Radio>
                 <Radio value="None">禁用</Radio>
               </RadioGroup>
             </Form.Item>
            )}
            {logEnabled && (
             <Form.Item
               label={'请求级别指标'}
             >
               <RadioGroup name={REQUEST_METRICS} shape="button" size="medium">
                 <Radio value={true}>启用</Radio>
                 <Radio value={false}>禁用</Radio>
               </RadioGroup>
             </Form.Item>
            )}
            {logEnabled && (
             <Form.Item
               label={'实例级别指标'}
             >
               <RadioGroup name={INSTANCE_METRICS} shape="button" size="medium">
                 <Radio value={true}>启用</Radio>
                 <Radio value={false}>禁用</Radio>
               </RadioGroup>
             </Form.Item>
            )} */}
        </Form>
      </>
    );
  }
}

export default Logging;
