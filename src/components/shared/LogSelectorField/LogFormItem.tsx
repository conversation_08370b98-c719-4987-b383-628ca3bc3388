import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Form, Field, Switch, Button, Message, Radio, Icon, Select, Balloon } from '@ali/cnd';
import CachedData from '~/cache/common';
import { TableField, TextField, SelectField } from '@ali/deep';
import * as LogService from '~/services/logging';
import { get, map, some, uniq } from 'lodash';
import TextRefreshButton from '~/components/shared/TextRefreshButton';
import ExternalLink from '~/components/shared/ExternalLink';
import { FILTER_LOGSTORE, DEFAULTTAIL } from './Constant';

type Props = {
  field: Field;
  value: {
    logEnabled: boolean;
    project?: string;
    logstore?: string;
    pushToUserSLS?: boolean;
    collectConfigs?: {
      logType: 'stdout' | 'file';
      logPath?: string;
      projectName?: string;
      logstoreName?: string;
      logtailName?: string;
    }[];
  };
  isUpdate: boolean;
  // 标记原始开启状态
  rootLogEnabled: boolean;
  showSlsConfig: boolean;
};

type State = {
  logEnabled: boolean;
  showSeniorOption: boolean;
  metricsEnabled: boolean;
  useNewResouces: boolean;
  logWarnText: string[];
  logRamWarn: boolean;
  projectLoading: boolean;
  logProjects: string[];
  selectedProject: string;
  storeLoading: boolean;
  logStores: string[];
};

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const LogWarnText = [
  intl('saenext.shared.LogSelectorField.LogFormItem.TheCreatedSlsProjectLogstore'),
  intl('saenext.shared.LogSelectorField.LogFormItem.TheDirectoryWhereTheLog'),
  intl('saenext.shared.LogSelectorField.LogFormItem.DoNotStoreOtherImportant'),
  intl('saenext.shared.LogSelectorField.LogFormItem.ToReuseLogtailThePath'),
];

class LogFormItem extends Component<Props, State> {
  tableField = null;
  isVirtualService = window.location.host.indexOf('4service') > -1; // 是否是虚商服务

  constructor(props) {
    super(props);
    this.state = {
      logEnabled: false,
      showSeniorOption: false,
      metricsEnabled: false,
      useNewResouces: true,
      logWarnText: LogWarnText.slice(1),
      logRamWarn: false,
      projectLoading: false,
      logProjects: [],
      selectedProject: '',
      storeLoading: false,
      logStores: [],
    };
  }

  componentDidMount(): void {
    const { showSlsConfig } = this.props;
    if (!showSlsConfig) {
      this.setState(
        {
          showSeniorOption: true,
          metricsEnabled: true,
        }
      )
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { isUpdate = false, field, value, showSlsConfig } = this.props;

    if (prevProps.value?.logEnabled !== value.logEnabled) {
      const {
        logEnabled,
        project = '',
        pushToUserSLS = false,
        logstore = '',
        collectConfigs = [],
      } = value;

      this.setState({
        logEnabled,
      });
      if (!logEnabled || !project) return;

      if (logEnabled) {
        this.getLogProjects();
      }

      // 从这里开始要区分 版本 和 应用复制

      const isSameProject = isUpdate;

      let _useNewResouces = project && project.startsWith('sae-');
      if (isSameProject) {
        // 版本不允许使用新的资源 一个应用只能有一个project
        _useNewResouces = false;
      }

      // 处理slsConfig
      // 这里涉及系统自建的资源 需要切换回系统自建
      let _collectConfigs = collectConfigs.map((item) => {
        // 默认处理系统自建的 只需要logType 和 logPath
        const { logType, logPath } = item;

        // 使用已有的项目
        if (!_useNewResouces) {
          const { logtailName, logPath, ...others } = item;
          let _logtailName =
            !logtailName || logtailName.startsWith('sae-') ? DEFAULTTAIL : logtailName;
          if (isSameProject) {
            _logtailName = !logtailName ? DEFAULTTAIL : logtailName;
          }
          let _logPath = logPath || '';
          return {
            ...others,
            logPath: _logPath,
            logtailName: _logtailName,
          };
        }

        return { logType, logPath };
      });

      // 处理logConfig
      let _showSeniorOption = false;
      let _metricsEnabled = false;
      // pushToUserSLS 是新的日志 logstore可以标志旧日志是否打开
      if (pushToUserSLS || logstore || !showSlsConfig) {
        // 说明打开的监控推送
        _showSeniorOption = true;
        _metricsEnabled = true;
      }

      this.setState(
        {
          useNewResouces: _useNewResouces,
          selectedProject: _useNewResouces ? '' : project,
          showSeniorOption: _showSeniorOption,
          metricsEnabled: _metricsEnabled,
        },
        () => {
          if (!_useNewResouces) {
            // 使用已有资源
            this.getLogStores(project);
          }
        },
      );

      const _values = {
        logEnabled,
        useNewResouces: _useNewResouces,
        projectName: _useNewResouces ? '' : project,
        collectConfigs: _collectConfigs,
        metricsEnabled: _metricsEnabled,
        logstoreName: _useNewResouces ? '' : logstore,
      };
      field.setValues(_values);
    }
  }

  async getLogProjects() {
    this.setState({ projectLoading: true });
    let out = [];
    try {
      // @ts-ignore
      const data = await LogService.ListProjects({
        customErrorHandle: (error, data) => {
          if (error.code === 'Unauthorized') {
            this.setState({
              logRamWarn: true,
            });
            return {};
          }
          return error;
        },
      });
      const projects = get(data, 'projects', []);
      out = map(projects, (project) => project.projectName);
      this.setState({ logProjects: out });
    } catch (e) {}
    this.setState({ projectLoading: false });
  }

  async getLogStores(project) {
    if (!project) {
      return;
    }
    this.setState({ storeLoading: true });
    let out = [];
    try {
      const { logstores = [] } = await LogService.ListStores({ params:{ProjectName: project} });

      out = logstores;
      this.setState({ logStores: out });
    } catch (e) {}
    this.setState({ storeLoading: false });
  }

  async getLogtail(store = '') {
    const { selectedProject } = this.state;
    if (!selectedProject || !store) {
      return;
    }
    const { Data } = await LogService.ListLogtails({
      params: {
        PageSize: 99,
        CurrentPage: 1,
        ProjectName: selectedProject,
        LogstoreName: store,
      },
    });
    const logConfigs = get(Data, 'LogConfigs', []);
    const logtails = map(logConfigs, (val) => {
      const { ConfigName, LogDir, SlsLogStore } = val || {};
      return {
        key: ConfigName,
        value: ConfigName,
        disabled: ConfigName?.startsWith('sae-'),
        LogDir,
        SlsLogStore,
      };
    });
    return logtails;
  }

  onLogTypeChange({ value, formGroupId }) {
    const pathChild = this.tableField.getComponent(formGroupId, 'logPath');
    if (value === 'stdout') {
      pathChild.setValue('stdout.log');
      pathChild.setBehavior('DISABLED');
    } else {
      pathChild.setValue('');
      pathChild.setBehavior('NORMAL');
    }
  }

  async onStoreChange({ value, formGroupId }) {
    if (this.state.useNewResouces) return;
    const tailChild = this.tableField.getComponent(formGroupId, 'logtailName');
    // 清空 logtailName
    tailChild.setValue('');
    this.tableField.setComponentProps(formGroupId, 'logtailName', {
      state: 'loading',
    });
    const logtails = await this.getLogtail(value);
    this.tableField.setComponentProps(formGroupId, 'logtailName', {
      state: null,
      dataSource: [{ key: DEFAULTTAIL, value: DEFAULTTAIL, label: DEFAULTTAIL }, ...logtails],
    });
  }

  onTailChange({ value, formGroupId }) {
    if (this.state.useNewResouces) return;
    const typeChild = this.tableField.getComponent(formGroupId, 'logType');
    const _logType = typeChild.getValue();
    const pathChild = this.tableField.getComponent(formGroupId, 'logPath');
    if (value !== DEFAULTTAIL || _logType === 'stdout') {
      pathChild.setBehavior('DISABLED');
    } else {
      pathChild.setBehavior('NORMAL');
    }
  }

  storeItemRender = (item) => {
    if (item.disabled) {
      return (
        <Balloon.Tooltip trigger={<li>{item.label}</li>}>
          {intl('saenext.shared.LogSelectorField.LogFormItem.TheLogstoreIsCreatedBy')}
        </Balloon.Tooltip>
      );
    } else {
      return <li>{item.label}</li>;
    }
  };

  async tableFieldBehavior({ groupId, itemValue }) {
    // 回显时一些数据的控制
    const { isUpdate, rootLogEnabled } = this.props;
    const { logType, logPath, logtailName } = itemValue;
    // logPath 为空说明是新增的
    if (!logType || !logPath) return;
    const pathChild = this.tableField?.getComponent(groupId, 'logPath');
    // 版本编辑时 将logPath不可编辑 并且是当collectConfigs有值时 不可编辑
    if (isUpdate && rootLogEnabled && logPath) {
      pathChild?.setBehavior('DISABLED');
    }
    if (logType !== 'stdout' && logtailName === DEFAULTTAIL) {
      pathChild?.setBehavior('NORMAL');
    }
  }

  tableChildrenRender = () => {
    const { logStores, useNewResouces, storeLoading } = this.state;
    const { field } = this.props;
    const collectConfigs = (field.getValue('collectConfigs') || []) as { logType: string }[];
    const _disabled = some(collectConfigs, (item) => {
      return item?.logType === 'stdout';
    });

    const children = [
      <SelectField
        name="logType"
        label={intl('saenext.shared.LogSelectorField.LogFormItem.LogType')}
        dataSource={[
          {
            label: intl('saenext.shared.LogSelectorField.LogFormItem.FileCollectionLog'),
            value: 'file',
          },
          {
            label: intl('saenext.shared.LogSelectorField.LogFormItem.StandardOutputLog'),
            value: 'stdout',
            disabled: _disabled,
          },
        ]}
        onChange={(item) => this.onLogTypeChange(item)}
      />,

      <TextField
        name="logPath"
        label={intl('saenext.shared.LogSelectorField.LogFormItem.LogSource')}
        placeholder={intl('saenext.shared.LogSelectorField.LogFormItem.EnterTheFileDirectoryIn')}
      />,
    ];

    if (!useNewResouces) {
      children.splice(
        1,
        0,
        <SelectField
          label="Logstore"
          name="logstoreName"
          state={storeLoading ? 'loading' : null}
          dataSource={logStores.map((logStore) => {
            return {
              key: logStore,
              value: logStore,
              disabled: FILTER_LOGSTORE.includes(logStore),
            };
          })}
          itemRender={this.storeItemRender}
          onChange={(item) => this.onStoreChange(item)}
        />,

        <SelectField
          label="Logtail"
          name="logtailName"
          onChange={(item) => this.onTailChange(item)}
          itemRender={this.storeItemRender}
        />,
      );
    }
    return children;
  };

  validation = (value = []) => {
    const { useNewResouces, metricsEnabled } = this.state;
    if (!metricsEnabled && !value.length) {
      return intl('saenext.shared.LogSelectorField.LogFormItem.WhenMonitoringIsPushedTo');
    }
    const collectConfigs = [];
    for (let i = 0; i < value.length; i++) {
      const item = value[i];
      if (!item) {
        return intl(
          'saenext.shared.LogSelectorField.LogFormItem.IncompleteCollectionRuleConfiguration',
        );
      }
      if (!item.logType) {
        return intl('saenext.shared.LogSelectorField.LogFormItem.TheCollectionRuleConfigurationIs');
      }
      if (useNewResouces && !item.logPath) {
        return intl(
          'saenext.shared.LogSelectorField.LogFormItem.TheCollectionRuleConfigurationIs.1',
        );
      }

      if (!useNewResouces && !item.logstoreName) {
        return intl(
          'saenext.shared.LogSelectorField.LogFormItem.TheCollectionRuleConfigurationIs.2',
        );
      }
      if (!useNewResouces) {
        if (item.logtailName === DEFAULTTAIL && !item.logPath) {
          return intl(
            'saenext.shared.LogSelectorField.LogFormItem.TheCollectionRuleConfigurationIs.3',
          );
        }
        if (!item.logtailName && !item.logPath) {
          return intl(
            'saenext.shared.LogSelectorField.LogFormItem.TheCollectionRuleConfigurationIs.4',
          );
        }
      }

      // 校验logPath 绝对路径
      if (item.logType !== 'stdout' && item.logPath) {
        const absolutePathRegex = /^(?:\/|[a-z]+:\/\/)[a-zA-Z0-9_\-\/.%\*\?]+$/;
        const isValid = absolutePathRegex.test(item.logPath);
        if (!isValid) {
          return intl('saenext.shared.LogSelectorField.LogFormItem.TheCollectionRuleIsIncorrectly');
        }
      }

      if (useNewResouces) {
        collectConfigs.push(`${item.logType}-${item.logPath}`);
      } else {
        collectConfigs.push(
          `${item.logType}-${item.logstoreName}-${item.logtailName}-${item.logPath}`,
        );
      }
    }

    if (uniq(collectConfigs).length !== collectConfigs.length) {
      return intl('saenext.shared.LogSelectorField.LogFormItem.ThereAreDuplicateCollectionRules');
    }
    return true;
  };

  render() {
    const {
      logEnabled,
      showSeniorOption,
      metricsEnabled,
      useNewResouces,
      logWarnText,
      logRamWarn,
      projectLoading,
      logProjects,
      selectedProject,
      storeLoading,
      logStores,
    } = this.state;
    const { field, isUpdate = false, rootLogEnabled, showSlsConfig } = this.props;
    const { init, validate } = field;
    return (
      <>
        <Form.Item
          label={showSlsConfig ? intl('saenext.shared.LogSelectorField.LogFormItem.LogCollectionToSlsLog') : intl('saenext.shared.LogSelectorField.LogFormItem.MonitoringPushedToSlsLog')}
          className="full24-width"
          {...(fieldLayout as any)}
          help={showSlsConfig ? intl('saenext.shared.LogSelectorField.LogFormItem.WhenEnabledYouCanView') : intl('saenext.shared.LogSelectorField.LogFormItem.AfterActivationYouCanView')}
        >
          <Switch
            checked={logEnabled}
            disabled={!CachedData.getOpenStatus('logStatus')}
            {...init('logEnabled', {
              initValue: logEnabled,
              props: {
                onChange: (value) => {
                  this.setState({ logEnabled: value });
                  this.getLogProjects();
                },
              },
            })}
          />
        </Form.Item>
        {logEnabled && CachedData.isSubAccount() && logRamWarn ? (
          <Message type="warning" className="mb-l">
            {intl('saenext.shared.LogSelectorField.LogFormItem.CurrentlyYourAccountHasNot')}

            <a
              href={`${CachedData.confLink('feature:ram:url')}/#/role/authorize`}
              target="_blank"
              className="ml-s mr-s"
            >
              {intl('saenext.shared.LogSelectorField.LogFormItem.RamAccessControl')}
            </a>
            {intl('saenext.shared.LogSelectorField.LogFormItem.UseTheProductAfterAuthorization')}
          </Message>
        ) : null}

        {logEnabled ? (
          <>
            <Form.Item label=" " {...(fieldLayout as any)} className="full24-width">
              <Radio.Group
                {...init('useNewResouces', {
                  initValue: useNewResouces,
                  props: {
                    onChange: (value: boolean) => {
                      this.setState(
                        {
                          useNewResouces: value,
                          logWarnText: value ? LogWarnText.slice(1) : LogWarnText,
                        },
                        () => {
                          field.setValues({ collectConfigs: [] });
                          if (!value) {
                            validate(['projectName'], () => {});
                          }
                        },
                      );
                    },
                  },
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.shared.LogSelectorField.LogFormItem.SelectUseSlsResourceType',
                      ),
                    },
                  ],
                })}
              >
                <Radio value={true} disabled={isUpdate && rootLogEnabled}>
                  {intl('saenext.shared.LogSelectorField.LogFormItem.CreateAnSlsResource')}
                </Radio>
                <Radio value={false}>
                  {intl('saenext.shared.LogSelectorField.LogFormItem.UseExistingSlsResources')}
                </Radio>
              </Radio.Group>
            </Form.Item>
            <Message
              title={intl('saenext.shared.LogSelectorField.LogFormItem.Precautions')}
              type="warning"
              className={"mb-l" + (!showSlsConfig ? ' none' : '')}
            >
              {logWarnText.map((item, index) => (
                <div className="text-line" key={index}>{`${index + 1}. ${item}`}</div>
              ))}
            </Message>
            {!useNewResouces ? (
              <Form.Item
                required
                label={intl('saenext.shared.LogSelectorField.LogFormItem.LogItems')}
                {...(fieldLayout as any)}
                className="full24-width"
              >
                <Select
                  {...init('projectName', {
                    props: {
                      onChange: (value: string) => {
                        this.getLogStores(value);
                        this.setState({ selectedProject: value });
                        // 清空 logstoreName 及 收集规则的 Logstore	Logtail
                        const collectConfigs = field.getValue('collectConfigs') || [];
                        // @ts-ignore
                        const _collectConfigs = map(collectConfigs, (rule) => ({
                          // @ts-ignore
                          ...rule,
                          logstoreName: '',
                          logtailName: '',
                        }));
                        field.setValues({
                          logstoreName: '',
                          collectConfigs: _collectConfigs,
                        });
                      },
                    },
                    rules: [
                      {
                        required: true,
                        message: intl('saenext.shared.LogSelectorField.LogFormItem.SelectALogItem'),
                      },
                    ],
                  })}
                  style={{ width: '70%' }}
                  placeholder={intl('saenext.shared.LogSelectorField.LogFormItem.SelectALogItem')}
                  state={projectLoading ? 'loading' : null}
                  disabled={isUpdate && rootLogEnabled}
                >
                  {logProjects.map((project) => (
                    <Select.Option
                      disabled={project.startsWith('sae-')}
                      key={project}
                      value={project}
                    >
                      {project.startsWith('sae-') ? (
                        <Balloon trigger={project} closable={false}>
                          {intl(
                            'saenext.shared.LogSelectorField.LogFormItem.ProjectAutomaticallyCreatedByThe',
                          )}
                        </Balloon>
                      ) : (
                        project
                      )}
                    </Select.Option>
                  ))}
                </Select>
                <div className="flex line18">
                  <TextRefreshButton onClick={() => this.getLogProjects()} />
                  <ExternalLink
                    className="ml-l"
                    label={intl('saenext.shared.LogSelectorField.LogFormItem.CreateANewLogProject')}
                    // @ts-ignore
                    url={`${CachedData.confLink('feature:sls:url')}/lognext/profile`}
                  />
                </div>
              </Form.Item>
            ) : null}

            <Form.Item
              label={intl(
                'saenext.shared.LogSelectorField.LogFormItem.CollectionRuleConfiguration',
              )}
              {...(fieldLayout as any)}
              className={"full24-width" + (!showSlsConfig ? ' none' : '')}
            ></Form.Item>
            {useNewResouces && showSlsConfig ? (
              <Message title="" type="notice" className="mb-l">
                <div className="text-line">
                  {intl('saenext.shared.LogSelectorField.LogFormItem.WhenYouUseSlsLog')}
                </div>
                <div className="text-line">
                  {intl('saenext.shared.LogSelectorField.LogFormItem.SlsIsBilledBasedOn')}
                </div>
              </Message>
            ) : null}

            <TableField
              name="collectConfigs"
              className={"full24-table" + (!showSlsConfig ? ' none' : '')}
              layout="TABLER"
              showIndex={false}
              showTableHead={true}
              showSortable={false}
              minItems={0}
              addButtonText={intl('saenext.shared.LogSelectorField.LogFormItem.Add')}
              showDeleteConfirm={false}
              actionsColumnWidth={80}
              delButtonText={<Icon type="delete" />}
              ref={(c) => (this.tableField = c?.getInstance?.() || c)}
              fieldBehavior={(value) => this.tableFieldBehavior(value)}
              children={this.tableChildrenRender()}
              validation={[
                {
                  type: 'customValidate',
                  param: this.validation,
                },
              ]}
            />

            <Form.Item
              label={intl('saenext.shared.LogSelectorField.LogFormItem.PushRuleConfiguration')}
              {...(fieldLayout as any)}
              className={"full24-width" + (!showSlsConfig ? ' none' : '')}
            >
              <Button
                text
                type="primary"
                style={{ height: 30, marginTop: 4 }}
                disabled={!CachedData.getOpenStatus('logStatus')}
                onClick={() => {
                  this.setState({
                    showSeniorOption: !showSeniorOption,
                  });
                }}
              >
                {showSeniorOption
                  ? intl('saenext.shared.LogSelectorField.LogFormItem.HideAdvancedSettings')
                  : intl('saenext.shared.LogSelectorField.LogFormItem.ShowAdvancedSettings')}
              </Button>
            </Form.Item>
          </>
        ) : null}

        {logEnabled && showSeniorOption ? (
          <>
            <Form.Item
              label={intl('saenext.shared.LogSelectorField.LogFormItem.MonitoringPushedToSlsLog')}
              className={"full24-width" + (!showSlsConfig ? ' none' : '')}
              {...(fieldLayout as any)}
              help={intl('saenext.shared.LogSelectorField.LogFormItem.AfterActivationYouCanView')}
            >
              <Switch
                checked={metricsEnabled}
                {...init('metricsEnabled', {
                  initValue: metricsEnabled,
                  // rules: [{
                  //   type: 'customValidate',
                  //   param: (value) => {
                  //     const collectConfigs = (field.getValue('collectConfigs') || []) as any[];
                  //     if (!value && !collectConfigs.length) {
                  //       return '监控推送规则、收集规则不能同时为空';
                  //     }
                  //   },
                  // }],
                  props: {
                    onChange: (value) => {
                      this.setState(
                        {
                          metricsEnabled: value,
                        },
                        () => {
                          if (!useNewResouces && value) {
                            validate(['logstoreName'], () => {});
                          }
                        },
                      );
                    },
                  },
                })}
              />
            </Form.Item>
            {metricsEnabled && !useNewResouces ? (
              <>
                <Message title="" type="warning" className="mb-l">
                  {intl('saenext.shared.LogSelectorField.LogFormItem.NoteTheDataFormatOf')}
                </Message>
                <Form.Item
                  required
                  label={intl('saenext.shared.LogSelectorField.LogFormItem.MonitorPushLogLibrary')}
                  {...(fieldLayout as any)}
                  className="full24-width"
                >
                  <Select
                    {...init('logstoreName', {
                      rules: [
                        {
                          required: true,
                          message: intl(
                            'saenext.shared.LogSelectorField.LogFormItem.SelectMonitoringPushLogLibrary',
                          ),
                        },
                      ],
                    })}
                    style={{ width: '70%' }}
                    placeholder={intl(
                      'saenext.shared.LogSelectorField.LogFormItem.SelectMonitoringPushLogLibrary',
                    )}
                    state={storeLoading ? 'loading' : null}
                    dataSource={logStores.map((logStore) => {
                      return {
                        key: logStore,
                        value: logStore,
                        disabled: FILTER_LOGSTORE.includes(logStore),
                      };
                    })}
                    itemRender={this.storeItemRender}
                  />

                  <div className="flex line18">
                    <TextRefreshButton
                      onClick={() => this.getLogStores(selectedProject)}
                      className="mr-l"
                    />

                    {selectedProject ? (
                      <ExternalLink
                        className="mr-l"
                        label={intl(
                          'saenext.shared.LogSelectorField.LogFormItem.CreateANewLogstore',
                        )}
                        url={`${CachedData.confLink('feature:sls:url')}/lognext/project/${selectedProject}/overview`}
                      />
                    ) : null}

                    <ExternalLink
                      url={CachedData.confLink('help:sls:how-do-i-delete-logs')}
                      label={intl('saenext.shared.LogSelectorField.LogFormItem.HowCanIAdjustThe')}
                    />
                  </div>
                </Form.Item>
              </>
            ) : null}
          </>
        ) : null}
      </>
    );
  }
}

export default LogFormItem;
