import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button, Icon, Input, Tag } from '@ali/cnd';
import { map, filter, indexOf } from 'lodash';

const { Group: TagGroup } = Tag;

const TagsGroup = (props:any) => {
  const { value = [], onChange, maxLimitHint = 10, onlyAllowMod = 'string' } = props;
  const [isEdit, setIsEdit] = useState(false);
  const [currentTag, setCurrentTag] = useState('');

  const toAddTag = () => setIsEdit(true);

  const updateTag = (v) => setCurrentTag(v);

  const cancelTag = (from, txt) => {
    const _tagsGroup = filter(value, (item) => item !== txt);
    onChange(_tagsGroup);
    return true;
  };

  const onTagChange = () => {
    let _tagsGroup = [...value];
    if (currentTag) {
      const _tag = currentTag.replace(/(^\s*)|(\s*$)/g, '');
      const index = indexOf(value, _tag);
      if (_tag && _tag.length && index === -1) {
        if (onlyAllowMod === 'number') {
          const pattern = /^[1-5][0-9]{2}$/;
          if (pattern.test(_tag)) {
            _tagsGroup = [...value, _tag];
          }
        } else {
          _tagsGroup = [...value, _tag];
        }
      }
    }
    setIsEdit(false);
    onChange(_tagsGroup);
  };

  return (
    <div
      className="common-box"
      style={{
        // paddingBottom: 8,
        display: 'flex',
        minHeight: 32,
        alignItems: 'center',
      }}
    >
      <TagGroup>
        {map(value, (txt, index) => (
          <Tag.Closeable
            key={`${txt}-${index}`}
            type="primary"
            onClose={(from) => cancelTag(from, txt)}
          >
            {txt}
          </Tag.Closeable>
        ))}
        {
          (!isEdit && value.length < maxLimitHint) && (
            <Button type="normal" size="small" style={{ marginTop: -8 }} onClick={toAddTag}>
            <Icon type="add" />
          </Button>
          )
        }
        {
          isEdit && (
            <Input
            autoFocus
            size="small"
            showLimitHint
            maxLength={onlyAllowMod === 'number' ? 3 : 63}
            style={{ marginTop: -8 }}
            onBlur={onTagChange}
            onChange={updateTag}
            onPressEnter={onTagChange}
          />
          )
        }
      </TagGroup>
    </div>
  );
};

TagsGroup.propTypes = {
  value: PropTypes.arrayOf(PropTypes.any),
  onChange: PropTypes.func,
  maxLimitHint: PropTypes.number,
  onlyAllowMod: PropTypes.string,
};

export default TagsGroup;
