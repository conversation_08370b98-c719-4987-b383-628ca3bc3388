import React, { useEffect, useMemo, useState } from 'react';
import MessengerRegion from '@alicloud/console-base-rc-messenger-region';
import { useLocation } from 'react-router-dom';
import services from '~/services';

const MessengerRegionBar = (props) => {
    const { setFeature, setWebFeature, setInDebtData } = props;
    const [regionId, setRegionId] = useState(window.curRegionId);
    const [disable, setDisable] = useState(false);
    const location = useLocation();
    const pathname = location.pathname;

    const allRegions = useMemo(() => {
        return (window.ALIYUN_CONSOLE_CONFIG.REGIONS).map((item) => {
            return {
                id: item.regionId,
                name: item.name
            }
        })
    }, [])

    useEffect(() => {
        changeRegionVisible();
    }, [location]);
    const changeRegionVisible = () => {
        setRegionId(window.curRegionId);
        //网关详情页和API详情页禁用
        if (
            pathname.includes('/web-app') ||
            pathname.includes('/micro-app') ||
            (pathname.includes('/namespace') && !pathname.endsWith('/namespace')) ||
            (pathname.includes('/scene-market') && !pathname.endsWith('/scene-market')) ||
            pathname.endsWith('/create-job')
        ) {
            setDisable(true);
        } else {
            setDisable(false);
        }
    };

    const onRegionChange = async (regionId) => {
        console.log("🚀 ~ onRegionChange ~ regionId:", regionId)
        if (window.curRegionId === regionId) return;
        window.curRegionId = regionId;
        setRegionId(regionId)

        // 切换 region 后重新查Web应用的配置
        const res = await services.getWebFeatureConfig({
            region: regionId,
        });
        const data = res?.Data || {}
        setWebFeature(data)

        const featureConfig = await services.CheckFeatureConfig({}, { ignoreError: true });
        if (featureConfig?.Data) {
            setFeature(featureConfig.Data);
        }

        const checkInDebtRes = await services.checkInDebt({}, { ignoreError: true });
        const checkInDebtData = checkInDebtRes?.Data || {};
        setInDebtData(checkInDebtData);
    };

    return (
        <>
            <MessengerRegion
                regions={allRegions}
                disabled={disable}
                regionId={regionId}
                onChange={onRegionChange}
            />
        </>
    );
};

export default MessengerRegionBar;