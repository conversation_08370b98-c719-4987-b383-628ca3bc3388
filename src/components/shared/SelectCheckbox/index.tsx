import React, { useEffect, useState } from 'react';
import { Balloon, Select, Icon, Checkbox, Form, Grid, LinkButton } from '@ali/cnd';
import { intl } from '@ali/cnd';
import './index.less';

const SelectCheckbox = (props) => {
  const {
    value,
    onChange,
    dataSource,
    buttonClick,
    optionValue,
    optionLabel,
    buttonLabel,
    width,
    handleRefresh,
    refreshLoading,
    manualClose = false,
    itemRender,
    footerType,
    buttonContent,
    type,
    popupClassName = 'select-popup-checkbox',
  } = props;

  const [visible, setVisible] = useState(false);

  const handleItemRender = (item) => {
    return (
      <div>
        <Checkbox checked={value?.indexOf(item.value) > -1} />
        {item.label}
      </div>
    );
  };

  const handleMenuProps = {
    footer: (
      <div style={{ padding: '0px 16px', borderTop: '1px solid #eee', display: 'flex' }}>
        {footerType && type !== 'route-domain' && buttonContent}
        {!footerType && type !== 'route-domain' && (
          <LinkButton type="primary" onClick={buttonClick}>
            {buttonLabel}
          </LinkButton>
        )}
        {handleRefresh && type !== 'route-domain' && (
          <LinkButton style={{ marginLeft: 10 }} onClick={handleRefresh}>
            {!refreshLoading && <Icon type="refresh" size="xs" />}
            {refreshLoading && <Icon type="loading" size="xs" />}
            <span style={{ marginLeft: 2 }}>{intl('mse.common.refresh')}</span>
          </LinkButton>
        )}
        {manualClose && (
          <LinkButton
            type="primary"
            onClick={() => {
              setVisible(false);
            }}
            style={{ flex: 1, justifyContent: 'flex-end' }}
          >
            {intl('mse.tag.dialog.close')}
          </LinkButton>
        )}
      </div>
    ),
  };

  return (
    <Select
      {...props}
      style={{ width: width || '100%' }}
      mode="multiple"
      itemRender={itemRender || handleItemRender}
      menuProps={buttonLabel ? handleMenuProps : false}
      onChange={(value) => onChange(value)}
      value={value}
      popupClassName={popupClassName}
      visible={visible}
      onVisibleChange={(v) => {
        setVisible(v);
      }}
    >
      {dataSource?.map((item) => (
        <Select.Option value={item[optionValue || 'value']} key={item[optionValue || 'value']}>
          {item?.tip && (
            <Balloon.Tooltip align="r" trigger={item[optionLabel || 'label']}>
              {item?.tip}
            </Balloon.Tooltip>
          )}
          {!item?.tip && item[optionLabel || 'label']}
        </Select.Option>
      ))}
    </Select>
  );
};

export default SelectCheckbox;
