import React from 'react';
import { Loading } from "@ali/cnd";

type Props = {
  uri: string;
  regionId: string;
  applicationId: string;
};

type State = {
  loading: boolean;
};

const ConsoleNavHeight = 50;
const BreadcrumbHeight = 48;
const AppPageNavHeight = 56;
const ReservedSpaceHeight = 16;

class ComIframe extends React.Component<Props, State> {
  private height: number;
  private iframeRef = React.createRef() as any;

  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };

    // 默认高度
    this.height =  (
      document.documentElement.clientHeight - 
      ConsoleNavHeight - 
      BreadcrumbHeight - 
      AppPageNavHeight - 
      ReservedSpaceHeight
    );
  }


  componentDidMount(): void {
    this.setState({
      loading: true,
    });
    window.addEventListener("message", this.onMessage);
  }

  onMessage = (e) => {
    const { type, payload } = e.data || {};
    if (type === 'iframeHeight') {
      const _height = parseInt(payload, 10);
      const iframeHeight = Math.max(this.height, _height);
      // @ts-ignore
      this.iframeRef.style.height = `${iframeHeight}px`;
    }
    if (type === 'iframeLoaded') {
      this.setState({
        loading: false,
      });
    }
  };

  // iframe自带的onload做兜底
  iframeLoaded = () => {
    this.setState({loading: false});
  }

  componentWillUnmount(): void {
    window.removeEventListener("message", this.onMessage);
  }

  render() {
    const { uri } = this.props;
    const { loading } = this.state;
    return (
      <Loading
        visible={loading}
        style={{ width: "100%", minHeight: this.height, position: "relative" }}
      >
        <iframe
          frameBorder={0}
          width="100%"
          height={this.height}
          scrolling="no"
          onLoad={this.iframeLoaded}
          src={uri}
          ref={(ref) => this.iframeRef = ref}
        />
      </Loading>
    );
  }
}
export default ComIframe;