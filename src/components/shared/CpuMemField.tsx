import React, { useEffect, useState } from 'react';
import { CpuMemSelector, Input } from '@ali/cnd';
import { hygonSpecs } from '../app-create/constants';
import services from '~/services';
import { filter, find, isEmpty } from 'lodash';

export const ResourceSpec = {
  default: {
    cpu: 1,
    memory: 2,
  },
  haiguang: {
    cpu: 2,
    memory: 8,
  },
}

const CpuMemField = (props) => {
  const {
    field,
    cpuName,
    memoryName,
    resourceType = 'default',
  } = props;

  const defaultValue = ResourceSpec[resourceType];
  
  const [cpuMemData, setCpuMemData] = useState([]);
  const [value, setValue] = useState(defaultValue);

  const specDataMap = {
    default: cpuMemData,
    haiguang: hygonSpecs,
  } 

  const {
    init,
    getValue,
    getError,
  } = field;

  const cpu = getValue(cpuName);
  const memory = getValue(memoryName);

  useEffect(() => {
    if (!cpu && !memory) {
      return;
    }
    setValue({
      cpu,
      memory,
    });
  }, [cpu, memory])

  useEffect(() => {
    querySpec();
  }, []);
  
  const querySpec = async () => {
    const res = await services.DescribeInstanceSpecifications();
    const { Data = [] } = res || {};
    if (isEmpty(Data)) return;
    Data.sort((a, b) => a.Memory - b.Memory).sort((a, b) => a.Cpu - b.Cpu);
    const specEnable = filter(Data, (item) => item.Enable);

    const cpuMemData = [];
    specEnable.forEach((spec) => {
      const { Cpu, Memory } = spec;

      const cpu = Cpu / 1000;
      const mem = Memory / 1024;

      const cpuItem = find(cpuMemData, { value: cpu });
      if (cpuItem) {
        cpuItem.children.push({
          label: `${mem}`,
          value: mem,
        });
      } else {
        cpuMemData.push({
          label: `${cpu}`,
          value: cpu,
          children: [
            {
              label: `${mem}`,
              value: mem,
            },
          ],
        });
      }
    });
    setCpuMemData(cpuMemData);
  };

  return (
    <>
      <CpuMemSelector
        dataSource={specDataMap[resourceType]}
        unit="GB"
        value={value}
        onChange={(v) => {
          field.setValue(cpuName, v.cpu);
          field.setValue(memoryName, v.memory);
        }}
      />
      <div className="none">
        <Input {...init(cpuName, {
          initValue: defaultValue.cpu,
          rules: [
            {
              required: true,
            },
          ],
        })} />
        <Input {...init(memoryName, {
          initValue: defaultValue.memory,
          rules: [
            {
              required: true,
            },
          ],
        })} />
      </div>
    </>
  )
}

export default CpuMemField;