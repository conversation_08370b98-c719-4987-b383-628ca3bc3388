import React, { useEffect, useRef, useState } from "react";
import { Announcement } from "@ali/cnd";
import _ from "lodash";
import If from '~/components/shared/If';
import parseEnv from "@alicloud/console-base-conf-parse-env";

const autoplaySpeed = 6000;

const AnnouncementBar = (props: { className?: string }) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [activeIndex, setActiveIndex] = useState(0);

  const timerRef = useRef<number>();

  const startPlay = (count: number) => {
    timerRef.current = window.setInterval(() => {
      setActiveIndex((currentIndex) => {
        return (currentIndex + 1) % count;
      });
    }, autoplaySpeed);
  }


  useEffect(() => {
    fetchAnnouncement();
    () => {
      clearInterval(timerRef.current);
    }
  }, []);

  const isPre = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';
  const fetchAnnouncement = async () => {
    const { FECS_HOST } = parseEnv();
    const resJson = await fetch(`https://${FECS_HOST}/api/wind/announcement?id=saenext`, {
      credentials: 'include'
    })
    const res = await resJson.json();
    const data = _.get(res, 'data.notice.announcement.data', []);
    const dataSource = _.map(data, (item) => ({
      title: <div dangerouslySetInnerHTML={{ __html: item.content }} />
    }))

    if (dataSource.length > 0) {
      startPlay(dataSource.length);
    }
    setDataSource(dataSource);
  }
  return (
    <If condition={dataSource.length > 0}>
      <div
        onMouseEnter={() => {
          clearInterval(timerRef.current);
        }}
        onMouseLeave={() => {
          startPlay(dataSource.length);
        }}
      >
        <Announcement
          {...props}
          type="notice"
          dataSource={dataSource}
          sliderOptions={{
            autoplay: false,
            // 由于 alicloud-components-announcement 组件未支持 pauseOnHover,
            // 自行实现 pauseOnHover
            // pauseOnHover: true,
            activeIndex: activeIndex,
            onChange: setActiveIndex,
          }}
        />
      </div>
    </If>
  )
}

export default AnnouncementBar;
