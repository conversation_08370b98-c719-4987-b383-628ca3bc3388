import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Form, Select } from '@ali/cnd';
import { defaultDevEnv, DEV_ENV_LIST } from '~/constants';
import { setCookieByKeyName } from '~/utils/global';
export const DevEnvSelector = () => {
  const [curBackendEnv, setCurBackendEnv] = useState(defaultDevEnv);
  const handleBackendEnvChange = (val) => {
    setCurBackendEnv(val);
    setCookieByKeyName('saeEnvRedirect', val);
    setTimeout(() => {
      window.location.reload();
    }, 300);
  };

  return (
    <div
      style={{
        position: 'fixed',
        zIndex: 999,
        left: 20,
        bottom: 90,
      }}
    >
      <Form labelAlign="inset">
        <Form.Item label={intl('saenext.components.shared.DevEnvSelector.BackendEnvironment')}>
          <Select
            style={{ minWidth: 120 }}
            showSearch
            dataSource={DEV_ENV_LIST}
            value={curBackendEnv}
            onChange={handleBackendEnvChange}
          />
        </Form.Item>
      </Form>
    </div>
  );
};
