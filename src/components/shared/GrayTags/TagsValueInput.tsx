import React from 'react';
import { GRAY_TAGS_KEY } from './constants';
import { Input } from '@ali/cnd';

const TagsValueInput = (props) => {
  const {
    value,
    onChange,
    name,
    ...restProps
  } = props;

  const tagsVal = value?.[GRAY_TAGS_KEY];
  
  const onValChange = (val) => {
    onChange({
      ...value,
      [GRAY_TAGS_KEY]: val,
    });
  }

  return (
    <Input
      {...restProps}
      value={tagsVal}
      onChange={onValChange}
    />
  )
}

export default TagsValueInput;