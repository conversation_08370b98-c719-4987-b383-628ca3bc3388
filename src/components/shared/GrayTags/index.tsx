import { intl } from '@ali/cnd';
import React from 'react';
import { Form, Input, Message } from '@ali/cnd';
import { GRAY_TAGS_KEY } from './constants';
import TagsValueInput from './TagsValueInput';

const GrayTags = () => {
  const serviceTagsValidator = (rule, value, callback) => {
    const val = value?.[GRAY_TAGS_KEY];
    if (!val) {
      callback(intl('saenext.shared.GrayTags.TheTagValueCannotBe'));
    }
    if (!/^[a-zA-Z0-9-]+$/.test(val)) {
      callback(intl('saenext.shared.GrayTags.TagValuesCanOnlyConsist'));
    }
    callback();
  };

  return (
    <>
      <Message type="notice" className="mb-l" size="medium">
        {intl('saenext.shared.GrayTags.TheFullLinkGrayScale')}
      </Message>
      <div className="next-form next-inline next-medium">
        <Form.Item label={intl('saenext.shared.GrayTags.Name')} required disabled className="w-50">
          <Input readOnly value={GRAY_TAGS_KEY} />
        </Form.Item>
        <Form.Item
          label={intl('saenext.shared.GrayTags.Value')}
          validator={serviceTagsValidator}
          required
          extra={
            <div className="text-description mt-s">
              {intl('saenext.shared.GrayTags.TheTagValueIsTo')}
            </div>
          }
          className="w-50
          mr-none"
        >
          <TagsValueInput
            name="ServiceTags"
            maxLength={20}
            showLimitHint
            placeholder={intl('saenext.shared.GrayTags.EnterATagValue')}
          />
        </Form.Item>
      </div>
    </>
  );
};

export default GrayTags;
