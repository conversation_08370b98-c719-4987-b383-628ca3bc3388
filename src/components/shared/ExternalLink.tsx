import { Icon } from '@ali/cnd';
import React, { memo } from 'react';

const ExternalLink = ({
  label = '',
  url,
  icon = true,
  className = '',
  self = false,
  checker = false,
  noSpm = false,
  handelClick = () => { },
}) => {
  const attributes = noSpm ? { 'data-spm-protocol': 'i' } : {};
  return (
    <a
      className={className}
      {...attributes}
      href={url}
      onClick={(e) => {
        e.stopPropagation();
        return handelClick();
      }}
      target={self ? '_self' : '_blank'}
      rel="noopener noreferrer"
    >
      <span style={{ wordBreak: 'break-all' }}>{label}</span>
      {icon && (
        <span className="ml-s">
          <Icon type="external_link" size="xs" />
        </span>
      )}
    </a>
  );
};

export default memo(ExternalLink);
