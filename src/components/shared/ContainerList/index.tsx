import {
  Actions,
  Balloon,
  CndTable,
  Copy,
  intl,
  LinkButton,
  Tag,
  Truncate,
} from '@ali/cnd';
import { includes, isEmpty, map } from 'lodash';
import React, { useContext, useState } from 'react';
import services from '~/services';
import MicroAppContext from '~/utils/microAppContext';
import { EInsTab } from '~/components/app-detail/micro-app/basic-info/InstanceDetail/constants';
import InstanceStatus from '~/components/app-detail/micro-app/basic-info/InstanceGroup/InstanceStatus';

const { Tooltip } = Balloon;

const ContainerList = (props) => {
  const { regionId, appId, groupId, instanceId, setCreateTimeStamp } = props;

  const [refreshIndex, setRefreshIndex] = useState(0);

  const { appConfig } = useContext(MicroAppContext);

  const { PackageType, AssociateEip } = appConfig;

  const refresh = () => {
    setRefreshIndex((refreshIndex) => refreshIndex + 1);
  };

  const fetchData = async (params) => {
    const _params = {
      AppId: appId,
      GroupId: groupId,
      InstanceId: instanceId,
      CurrentPage: 1,
      PageSize: 1,
    };
    const { Data: { Instances = [] } = {} } =
      (await services.DescribeApplicationInstances(_params, true)) || {};

    const data = [];

    const item = Instances[0] || {};
    const { SidecarContainersStatus, MainContainerStatus, ...instanceRest } = item;

    setCreateTimeStamp?.(item.Timestamp);

    if (MainContainerStatus) {
      data.push({
        ...instanceRest,
        type: 'main',
        id: item.InstanceId + ':main',
        InstanceContainerStatus: MainContainerStatus,
      });
    }

    if (!isEmpty(SidecarContainersStatus)) {
      const sideContainers = map(item.SidecarContainersStatus, (sidecarItem) => {
        const { ContainerId, ContainerStatus } = sidecarItem;
        return {
          ...instanceRest,
          ...sidecarItem,
          type: 'sidecar',
          id: item.InstanceId + ':' + ContainerId,
          InstanceContainerStatus: ContainerStatus,
        };
      });

      data.push(...sideContainers);
    }

    return {
      data,
      total: data.length,
    };
  };

  const CONTAINER_TYPE = {
    main: {
      name: intl('saenext.shared.ContainerList.MainApplicationContainer'),
      color: 'blue',
    },
    sidecar: {
      name: intl('saenext.shared.ContainerList.SidecarContainer'),
      color: 'green',
    },
  };

  const onTabChange = (key, paramsObj = {}) => {
    const searchStr = new URLSearchParams(paramsObj).toString();
    window.xconsoleHistory.push(
      `/${regionId}/app-list/${appId}/micro-app/instance?groupId=${groupId}&instanceId=${instanceId}&section=${key}&${searchStr}`,
    );
  };

  return (
    <>
      <CndTable
        fetchData={fetchData}
        primaryKey="id"
        refreshIndex={refreshIndex}
        // showRefreshButton
        recordCurrent
        pagination={{ pageSizeList: [10, 20, 50, 100] }}
        loop={{ enable: true, time: 10000, showLoading: false }}
      >
        <CndTable.Column
          width={300}
          title={intl('saenext.basic-info.InstanceGroup.InstanceList.InstanceId')}
          lock
          dataIndex="InstanceId"
          cell={(val, idx, record) => {
            const podName = record.ContainerId || val;
            return (
              <>
                <Truncate
                  style={{ display: 'inline-block' }}
                  position="middle"
                  threshold={300}
                  type="width"
                >
                  {podName}
                </Truncate>
                <Copy text={podName} showIcon></Copy>
              </>
            );
          }}
        />

        <CndTable.Column
          width={120}
          title={intl('saenext.shared.ContainerList.Type')}
          dataIndex="type"
          cell={(val) =>
            val && (
              <Tag color={CONTAINER_TYPE[val]?.color} style={{ minWidth: 86, textAlign: 'center' }}>
                {CONTAINER_TYPE[val]?.name}
              </Tag>
            )
          }
        />

        <CndTable.Column
          width={200}
          title={intl('saenext.basic-info.InstanceGroup.InstanceList.RunningStatus')}
          sortable
          sortDirections={['desc', 'asc', 'default']}
          dataIndex="InstanceContainerStatus"
          cell={(val, index, record) =>
            <InstanceStatus
              appId={appId}
              associateEip={AssociateEip}
              groupId={groupId}
              refresh={refresh}
              record={record}
              showHealthTip={false}
              showRestart={false}
            />
          }
        />

        {PackageType === 'Image' || includes(PackageType, 'IMAGE_PHP') ? (
          <CndTable.Column
            width={250}
            title={intl('saenext.basic-info.InstanceGroup.InstanceList.Image')}
            dataIndex="ImageUrl"
            cell={(val = '') => {
              const reg = /\/.+\/.+$/;
              const url = val.match(reg);
              return <Tooltip trigger={`...${url || val}`}>{val}</Tooltip>;
            }}
          />
        ) : (
          <CndTable.Column
            width={200}
            title={intl('saenext.basic-info.InstanceGroup.InstanceList.Version')}
            dataIndex="PackageVersion"
          />
        )}

        {/* <CndTable.Column
           width={100}
           title={intl('saenext.basic-info.InstanceGroup.InstanceList.Runtime')}
           dataIndex="CreateTimeStamp"
           cell={(val) => !val ? '--' : moment(val).fromNow(true)}
          /> */}

        <CndTable.Column
          width={140}
          lock="right"
          title={intl('saenext.basic-info.InstanceGroup.InstanceList.Operation')}
          cell={(value, index, record) => {
            const { InstanceContainerStatus, InstanceId, ContainerId } = record;
            return (
              <Actions threshold={6}>
                <LinkButton
                  onClick={() =>
                    onTabChange(EInsTab.LOG, {
                      container: ContainerId || 'main',
                    })
                  }
                >
                  {intl('saenext.basic-info.InstanceGroup.InstanceList.RealTimeLog')}
                </LinkButton>
                <LinkButton
                  onClick={() =>
                    onTabChange(EInsTab.WEBSHELL, {
                      container: ContainerId || 'main',
                    })
                  }
                  disabled={
                    !(
                      InstanceContainerStatus === 'Running' ||
                      InstanceContainerStatus === 'Terminating'
                    )
                  }
                >
                  Webshell
                </LinkButton>
              </Actions>
            );
          }}
        />
      </CndTable>
    </>
  );
};

export default ContainerList;
