import { Checkbox, intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import { Field, Form, Input } from '@ali/cnd';
import { PACKAGE_TYPE } from '~/components/shared/DeploySelectorField/constant';
import If from '~/components/shared/If';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import { includes, mapValues } from 'lodash';
import CachedData from '~/cache/common';

const FORM_ITEM_LAYOUT = {
  labelTextAlign: 'left' as 'left',
  labelCol: {
    fixedSpan: 6,
  },
  wrapperCol: {
    span: 16,
  },
};

const JavaPackageStartCmd = (props) => {
  const { jdk, packageType, value = {}, onChange = () => {} } = props;

  const pattern = /dragonwell/i;
  const isDragonwell = pattern.test(jdk);

  const field = Field.useField();
  const optionField = Field.useField();

  const { isAppCDS } = optionField.getValues() as any;

  useEffect(() => {
    field.setValues(value);
  }, [JSON.stringify(value)]);

  useEffect(() => {
    field.reset([StartOptions, 'JarStartOptions']);
  }, [packageType]);

  useEffect(() => {
    initOptions();
  }, [JSON.stringify(value), jdk, packageType]);

  const initOptions = () => {
    const optionsNames = {
      isUseWisp2: WISP2,
      isElaticHeap: G1,
      isAppCDS: AppCDS,
      path: PathValReg,
    };

    const startOptions = value[StartOptions];

    const optionsValue = mapValues(optionsNames, (optionStr) => {
      if (optionStr instanceof RegExp) {
        const match = startOptions?.match(optionStr);
        return match ? match[1] : '';
      }
      return includes(startOptions, optionStr);
    });
    optionField.setValues(optionsValue);
  };

  const handleOptionsChange = (val) => {
    const options = filterNewLine(val);
    onChange({
      ...value,
      [StartOptions]: options,
    });
  };

  const handleArgsChange = (val) => {
    const JarStartArgs = filterNewLine(val);
    onChange({
      ...value,
      JarStartArgs,
    });
  };

  const filterNewLine = (str) => {
    return str.replace(/[\r\n]/g, ' ');
  };

  const getCheckedOptions = (
    options: string,
    isAdd: boolean,
    key: string | RegExp,
    replaceStr = '',
  ) => {
    if (isAdd) {
      const newOptionsArr = options.split(' ').filter((item) => item !== '');
      if (typeof key === 'string' && !includes(options, key)) {
        newOptionsArr.push(key);
      }
      return newOptionsArr.join(' ');
    } else {
      return options.replace(key, replaceStr);
    }
  };

  const onOptionsChecked = (isAdd, key, replaceStr = '') => {
    const options = (field.getValue(StartOptions) as string) || '';
    let newOptions = getCheckedOptions(options, isAdd, key, replaceStr);

    // 取消启动加速时，移除持久化目录 -Xquickstart:path
    if (key === AppCDS && isAdd === false) {
      newOptions = getCheckedOptions(newOptions, false, PathRegex);
    }
    onChange({
      ...value,
      [StartOptions]: newOptions,
    });
  };

  const onPathChange = (val) => {
    const options = field.getValue(StartOptions) as string;

    if (!val) {
      onOptionsChecked(false, PathRegex);
      return;
    }

    if (PathRegex.test(options)) {
      onOptionsChecked(false, PathRegex, `${PATH}${val}`);
    } else {
      onOptionsChecked(true, `${PATH}${val}`);
    }
  };

  const nameMap = {
    [PACKAGE_TYPE.JAR]: 'JarStartOptions',
    [PACKAGE_TYPE.WAR]: 'WarStartOptions',
  };

  const StartOptions = nameMap[packageType];

  const cmdMap = {
    [PACKAGE_TYPE.JAR]: '$JAVA_HOME/bin/java $Options -jar $CATALINA_OPTS "$package_path" $args',
    [PACKAGE_TYPE.WAR]: 'CATALINA_OPTS="$CATALINA_OPTS $Options" catalina.sh run',
  };

  const cmdTipsMap = {
    [PACKAGE_TYPE.JAR]: (
      <>
        {intl(
          'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.StartCommandFormatDescriptionJava',
        )}
        <span className="color-primary">[-Options]</span> -jar jarfile
        <span className="color-primary">[arg...]</span>
      </>
    ),

    [PACKAGE_TYPE.WAR]: (
      <>
        {intl(
          'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.DescriptionOfTheStartupCommand',
        )}
        <span className="color-primary">[-Options]</span> org.apache.catalina.startup.Bootstrap "$@"
        start
      </>
    ),
  };

  const WISP2 = '-XX:+UnlockExperimentalVMOptions -XX:+UseWisp2 -Dio.netty.transport.noNative=true';
  const G1 = '-XX:+UseG1GC';
  const AppCDS = '-Xquickstart:verbose';
  const PATH = '-Xquickstart:path=';
  const PathRegex = /-Xquickstart:path=([^ ]+)/;
  const PathValReg = /Xquickstart:path=(\S+)/;

  return (
    <>
      <Form field={field}>
        <Form.Item
          label={intl(
            'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.SystemDefaultStartupCommand',
          )}
          {...FORM_ITEM_LAYOUT}
          help={cmdTipsMap[packageType]}
        >
          <Input readOnly value={cmdMap[packageType]} style={{ width: 500, background: '#eee' }} />
        </Form.Item>
        <Form.Item
          label={intl('saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.OptionsSettings')}
          className="relative"
          {...FORM_ITEM_LAYOUT}
          help={
            <>
              {intl(
                'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.ToAvoidOomInApplications',
              )}

              <a href={CachedData.confLink('help:sae:best-practices-for-jvm-heap-size-configuration')} target="_blank">
                {intl(
                  'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.JvmConfigurationBestPractices',
                )}
              </a>
            </>
          }
        >
          <Input.TextArea
            name={StartOptions}
            onChange={handleOptionsChange}
            style={{ width: 500 }}
          />

          <If condition={isDragonwell}>
            <a
              className="form-extra ml-l"
              href={CachedData.confLink('help:sae:jvm-options')}
              target="_blank"
            >
              {intl('saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.JvmParameterSettings')}
            </a>
          </If>
        </Form.Item>
        <If condition={packageType === PACKAGE_TYPE.JAR}>
          <Form.Item
            label={intl('saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.ArgsSettings')}
            {...FORM_ITEM_LAYOUT}
          >
            <Input.TextArea
              name="JarStartArgs"
              onChange={handleArgsChange}
              style={{ width: 500 }}
            />
          </Form.Item>
        </If>

        <If condition={isDragonwell}>
          <Form field={optionField}>
            <Form.Item
              label={intl(
                'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.OptionsShortcutSettings',
              )}
            ></Form.Item>
            <If condition={jdk !== 'Dragonwell 17' && jdk !== 'Dragonwell 21'}>
              <Form.Item label="">
                <Checkbox
                  label={
                    <TextWithBalloon
                      text={intl(
                        'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.EnableMicroservicePerformanceImprovementWisp',
                      )}
                      align="t"
                    >
                      {intl(
                        'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.WispImprovesMicroservicesAndThread',
                      )}
                    </TextWithBalloon>
                  }
                  name={'isUseWisp2'}
                  onChange={(checked) => onOptionsChecked(checked, WISP2)}
                />
              </Form.Item>
            </If>
            <Form.Item label="">
              <Checkbox
                label={
                  <TextWithBalloon
                    text={intl(
                      'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.EnableApplicationMemoryOptimizationG',
                    )}
                    align="t"
                  >
                    {intl(
                      'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.GGcOptimizationToImprove',
                    )}
                  </TextWithBalloon>
                }
                name={'isElaticHeap'}
                onChange={(checked) => onOptionsChecked(checked, G1)}
              />
            </Form.Item>
            <If condition={includes(jdk, '11')}>
              <Form.Item label="">
                <Checkbox
                  label={
                    <TextWithBalloon
                      text={intl(
                        'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.EnableApplicationStartupAccelerationQuickstart',
                      )}
                      align="t"
                    >
                      {intl(
                        'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.EagerappcdsAppcdsUpgradeToImprove',
                      )}
                    </TextWithBalloon>
                  }
                  name={'isAppCDS'}
                  onChange={(checked) => onOptionsChecked(checked, AppCDS)}
                />
              </Form.Item>
              <If condition={isAppCDS}>
                <Form.Item
                  label={intl(
                    'saenext.shared.JavaPackageStartCmd.JavaPackageStartCmd.PersistentDirectory',
                  )}
                  {...FORM_ITEM_LAYOUT}
                >
                  <Input name="path" onChange={onPathChange} style={{ width: 500 }} />
                </Form.Item>
              </If>
            </If>
          </Form>
        </If>
      </Form>
    </>
  );
};

export default JavaPackageStartCmd;
