import React from 'react';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';
import JavaPackageStartCmd from './JavaPackageStartCmd';

class JavaPackageStartCmdField extends BaseField {
  props;
  static displayName = 'JavaPackageStartCmdField';

  getProps() {
    return {
      ...this.props
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <JavaPackageStartCmd {...newProps} />;
  }
}

export default ConfigProvider.config(JavaPackageStartCmdField as any)
