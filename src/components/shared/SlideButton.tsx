import { <PERSON><PERSON>, <PERSON>Button, Slide<PERSON><PERSON>l, intl, Badge, Copy } from '@ali/cnd';
import React, { PureComponent, ReactNode } from 'react';
import { isNumber } from '~/utils/global';

type Props = {
  buttonText: string | ReactNode;
  slideTitle: string;
  slideContent: ReactNode;
  buttonType?: 'primary' | 'secondary' | 'normal';
  slideSize?: string | number;
  submit?: Function;
  linkButton?: boolean;
  invisibleButton?: boolean;
  className?: string;
  disabled?: boolean;
  autoClose?: boolean;
  setVisiable?: number;
  customFooter?: any;
  onClick?: any;
  onClose?: any;
  onCancel?: any;
  showNew?: boolean;
  buttonCopy?: boolean;
  textButton?: boolean;
  textButtonClass?: string;
  buttonId?: string;
  notCloseAfterSubmit?: boolean;
};

type State = {
  active: boolean;
  isProcessing: boolean;
};

class SlideButton extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      active: false,
      isProcessing: false,
    };
  }

  setActive(active) {
    const { onClick = () => {}, onClose = () => {}, onCancel = () => {} } = this.props;
    this.setState({ active });
    if (active) {
      onClick && onClick();
    }
    if (!active) {
      this.setState({ isProcessing: false });
      onClose();
      onCancel();
    }
  }

  submit() {
    const { submit, notCloseAfterSubmit = false } = this.props;
    if (submit) {
      this.setState({
        isProcessing: true,
      });
      submit()
        .then(() => {
          this.setState({
            active: notCloseAfterSubmit ? true : false,
            isProcessing: false,
          });
        })
        .catch(() => {
          this.setState({
            isProcessing: false,
          });
        }).finally(() => {
          this.setState({
            isProcessing: false,
          });
        });
    } else {
      return true;
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.setVisiable !== this.props.setVisiable) {
      this.setState({
        active: false,
        isProcessing: false,
      });
    }
  }

  render() {
    const { active, isProcessing } = this.state;
    const {
      buttonText,
      slideContent,
      buttonType = 'primary',
      slideSize = 'medium',
      slideTitle,
      submit,
      className,
      linkButton = false,
      // disabled = false,
      autoClose = true,
      invisibleButton = false,
      customFooter,
      showNew = false,
      buttonCopy = false,
      textButton = false,
      textButtonClass = 'cursor-pointer',
      buttonId = '',
      ...restProps
    } = this.props;

    const LinkBtn = (
      <LinkButton className={className} {...restProps} onClick={() => this.setActive(true)}>
        {buttonText}
      </LinkButton>
    );

    return (
      <>
        {textButton && (
          <span
            className={textButtonClass}
            onClick={() => {
              this.setActive(true);
            }}
          >
            {buttonText}
          </span>
        )}
        {linkButton && !textButton && (
          // @ts-ignore
          <>{buttonCopy ? <Copy text={buttonText}>{LinkBtn}</Copy> : LinkBtn}</>
        )}

        {!linkButton && !invisibleButton && (
          <Button
            id={buttonId}
            {...restProps}
            className={className}
            type={buttonType}
            onClick={() => this.setActive(true)}
          >
            {showNew && (
              <Badge
                content="New"
                style={{
                  backgroundColor: '#f54743',
                  color: '#fff',
                  borderRadius: 10,
                  marginLeft: 8,
                  marginTop: -13,
                }}
              >
                {buttonText}
              </Badge>
            )}
            {!showNew && buttonText}
          </Button>
        )}
        {active && (
          <SlidePanel
            isProcessing={isProcessing}
            title={slideTitle}
            isShowing={active}
            width={
              isNumber(slideSize) && window.innerWidth < Number(slideSize)
                ? window.innerWidth
                : slideSize
            }
            onMaskClick={() => {
              if (autoClose) {
                this.setActive(false);
              }
            }}
            onClose={() => {
              this.setActive(false);
            }}
            customFooter={customFooter}
            onOk={submit ? this.submit.bind(this) : undefined}
            onCancel={submit ? () => this.setActive(false) : undefined}
            okText={intl('button.ok')}
            cancelText={intl('button.cancel')}
            processingText={intl('button.processing')}
          >
            {slideContent}
          </SlidePanel>
        )}
      </>
    );
  }
}

export default SlideButton;
