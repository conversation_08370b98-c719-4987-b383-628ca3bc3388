import React, { useEffect, forwardRef, useImperativeHandle } from 'react';
import { Form, Field, Select, intl } from '@ali/cnd';
import AppIdsSelect, { AppTypes } from '../enterprise/event-center/AppIdsSelect';
import { useRegionNamespace } from '../enterprise/event-center/custom-hooks';
import { get, map } from 'lodash';
const FormItem = Form.Item;

const Regions = get(window, 'ALIYUN_CONSOLE_CONFIG.REGIONS', []);
const RegionIds = map(Regions, (item) => ({
  label: item.name,
  value: item.regionId,
}));

const RegionNamespaceAppIdForm = (props, ref) => {
  const {
    value,
    onChange,
    appTypes,
    appUseDetailValue = false,
    allowAllRegions = false,
    allowAllNamespaces = false,
  } = props;
  const field = Field.useField({
    onChange: (name, val) => {
      if (name === 'region' && val === '*') {
        setErrors({
          namespaceId: '',
          appjob: '',
        });

        onChange({
          region: '*',
          namespaceId: '*',
          appjob: [
            {
              value: 'allApps',
            },
          ],
        });

        return;
      }
      if (name === 'namespaceId' && val?.endsWith('*')) {
        setErrors({
          appjob: '',
        });

        onChange({
          region: getValue('region'),
          namespaceId: val,
          appjob: [
            {
              value: 'allApps',
            },
          ],
        });

        return;
      }
      if (name === 'region') {
        reset(['namespaceId', 'appjob']);
      }
      if (name === 'namespaceId') {
        reset(['appjob']);
      }
      onChange(getValues());
    },
  });

  const { init, getValue, getValues, setValues, setErrors, reset } = field;
  const appIdsSelectRef = React.useRef(null);
  const { nameSpacesDataSource } = useRegionNamespace({
    getValue,
    allowAllNamespaces,
  });

  useEffect(() => {
    setValues(value || {});
  }, [JSON.stringify(value)]);

  useImperativeHandle(ref, () => ({
    getAppIdsList,
    validatePromise: field.validatePromise,
  }));

  const getAppIdsList = () => {
    return appIdsSelectRef?.current;
  };

  const regions = allowAllRegions
    ? [
        {
          label: intl('saenext.components.shared.RegionNamespaceAppIdForm.AllRegions'),
          value: '*',
        },

        ...RegionIds,
      ]
    : RegionIds;

  return (
    <Form field={field} inline>
      <FormItem required>
        <Select
          {...init('region', {
            rules: [
              {
                required: true,
                message: intl('saenext.event-center.subscribe-rule.EventRuleStep.SelectARegion'),
              },
            ],
          })}
          placeholder={intl('saenext.event-center.subscribe-rule.EventRuleStep.SelectARegion')}
          style={{ width: 300 }}
          dataSource={regions}
        />
      </FormItem>
      <FormItem required style={{ marginRight: 0 }}>
        <Select
          {...init('namespaceId', {
            rules: [
              {
                required: true,
                message: intl('saenext.event-center.subscribe-rule.EventRuleStep.SelectANamespace'),
              },
            ],
          })}
          placeholder={intl('saenext.event-center.subscribe-rule.EventRuleStep.SelectANamespace')}
          style={{ width: 300 }}
          dataSource={nameSpacesDataSource}
          disabled={getValue('region') === '*'}
        />
      </FormItem>
      <FormItem required>
        <AppIdsSelect
          {...init('appjob', {
            rules: [
              {
                required: true,
                message: intl(
                  'saenext.event-center.subscribe-rule.EventRuleStep.SelectAnApplicationOrTask',
                ),
              },
            ],
          })}
          region={getValue('region')}
          namespaceId={getValue('namespaceId')}
          ref={appIdsSelectRef}
          appTypes={appTypes as AppTypes[]}
          appUseDetailValue={appUseDetailValue}
          multiple
          style={{ width: 620 }}
          disabled={(getValue('namespaceId') as string)?.endsWith('*')}
        />
      </FormItem>
    </Form>
  );
};
export default forwardRef(RegionNamespaceAppIdForm);
