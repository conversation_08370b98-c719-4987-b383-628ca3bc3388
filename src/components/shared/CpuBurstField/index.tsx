import React from 'react';
import <PERSON><PERSON>Burst from './CpuBurst';
import <PERSON>Field from '@ali/deep-form-helper';

class CpuBurstField extends BaseField {
  props;
  static displayName = 'CpuBurstField';


  static propTypes = {
    ...BaseField.propTypes,
  };

  static defaultProps = {
    ...BaseField.defaultProps,
  };

  getProps() {
    return {
      ...this.props
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...fieldProps,
      ...this.props,
    };

    return <CpuBurst {...newProps} />;
  }
}

export default CpuBurstField;
