import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Form, Field, Switch, Message, Input,ToolTipCondition } from '@ali/cnd';
import { isEmpty } from 'lodash';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

type Props = {
  disabled: boolean;
  value: any;
  onChange: (value: any) => void;
};
type State = {
  burstEnabled: boolean;
};

class CpuBurst extends Component<Props, State> {
  field = new Field(this, {
    onChange: (value) => {
      this.changeArmsConfig();
    },
  });
  constructor(props) {
    super(props);
    const { value } = props;
    this.state = {
      burstEnabled: false,
    };

    // 初始化赋值
    if (isEmpty(value)) this.initStates();
  }

  componentDidUpdate(prevProps, prevState) {
    const { value } = this.props;
    if (prevProps.value !== value) {
      // 重新赋值
      this.updateStates();
    }
  }

  initStates() {
    const { setValue } = this.field;
    const { onChange } = this.props;
    setValue('enableCpuBurst', false);
    onChange({
      enableCpuBurst: false,
    });
  }

  updateStates() {
    const { value } = this.props;
    const { enableCpuBurst } = value;
    const { setValue } = this.field;
    setValue('enableCpuBurst', enableCpuBurst);
    this.setState({
      burstEnabled: enableCpuBurst,
    });
  }

  changeArmsConfig() {
    const { getValues } = this.field;
    const { onChange } = this.props;

    const params = getValues(['enableCpuBurst']) as any;
    if (params.enableCpuBurst) {
      onChange({ ...params });
      return;
    }
    onChange({ enableCpuBurst: false });
  }

  onArmsEnabledChanged(value) {
    this.setState({ burstEnabled: value });
  }

  render() {
    const { burstEnabled } = this.state;
    const { init } = this.field;
    return (
      <>
        <Message type="warning" className="mb-l">
          <div className="text-line">
            {intl('saenext.shared.CpuBurstField.CpuBurst.TheCpuBurstFunctionOnly')}
          </div>
        </Message>

        <Form field={this.field} {...(fieldLayout as any)}>
          <Form.Item
            label={intl('saenext.shared.CpuBurstField.CpuBurst.EnableCpuBurst')}
            help={intl('saenext.shared.CpuBurstField.CpuBurst.ItIsApplicableToScenarios')}
          >
            <Switch
              name="enableCpuBurst"
              checked={burstEnabled}
              {...init('enableCpuBurst', {
                initValue: burstEnabled,
                props: {
                  onChange: (value) => {
                    this.onArmsEnabledChanged(value);
                  },
                },
              })}
              disabled={this.props.disabled}
            />
          </Form.Item>
          {/* {burstEnabled ? (
            <>
              <Form.Item
                label={intl('saenext.shared.CpuBurstField.CpuBurst.CpuBurstMagnification')}
              >
                <ToolTipCondition show={true} tip="暂不支持修改" align="r">
                  <Input
                    disabled
                    value={2}
                    addonTextAfter={intl('saenext.shared.CpuBurstField.CpuBurst.Times')}
                    style={{ width: 260 }}
                  />
                </ToolTipCondition>
              </Form.Item>
              <Form.Item label={intl('saenext.shared.CpuBurstField.CpuBurst.CpuBurstDuration')}>
                <ToolTipCondition show={true} tip="暂不支持修改" align='r'>
                  <Input
                    disabled
                    value={300}
                    addonTextAfter={intl('saenext.shared.CpuBurstField.CpuBurst.Seconds')}
                    style={{ width: 260 }}
                  />
                </ToolTipCondition>
              </Form.Item>
            </>
          ) : null} */}
        </Form>
      </>
    );
  }
}

export default CpuBurst;
