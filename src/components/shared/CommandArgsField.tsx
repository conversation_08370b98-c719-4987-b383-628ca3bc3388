import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import { DockerCommandEditor } from '@ali/cnd';
import { Form, Input, Select } from '@ali/cnd';

const CommandArgsField = (props) => {
  const { field, commandName = 'Command', argsName = 'CommandArgs' } = props;

  const { validate } = field;

  const [value, setValue] = useState({
    [commandName]: '',
    [argsName]: [],
  });

  const command = field.getValue(commandName);
  const commandArgs = field.getValue(argsName);

  useEffect(() => {
    if (!command && isEmpty(commandArgs)) {
      return;
    }
    setValue({
      [commandName]: command,
      [argsName]: commandArgs,
    });
    validate([commandName, argsName]);
  }, [command, commandArgs]);

  const argsValidator = (rule, value, callback) => {
    if (!(value?.length >= 2)) {
      callback(intl('saenext.components.shared.CommandArgsField.EnterTheStartupCommand'));
      return;
    }
    callback();
  };

  return (
    <>
      <DockerCommandEditor
        value={value}
        onChange={(val) => {
          field.setValue(commandName, val[commandName]);
          field.setValue(argsName, val[argsName]);
        }}
        popupStyle={{ marginLeft: -16, paddingRight: 16, maxWidth: 1000 }}
      />

      <Form.Item>
        <Input name={commandName} className="none" />
      </Form.Item>
      <Form.Item validator={argsValidator}>
        <Select name={argsName} defaultValue={[]} className="none" />
      </Form.Item>
    </>
  );
};

export default CommandArgsField;
