import { intl } from '@ali/cnd';
import React, { useEffect, useState, forwardRef, useImperativeHandle, useContext } from 'react';
import { Form, Field, Icon, Message, Switch, ConsoleContext } from '@ali/cnd';
import { TableField, TextField, SelectField } from '@ali/deep';
import services from '~/services';
import _ from 'lodash';
import RefreshButton from '../../RefreshButton';
import CachedData from '~/cache/common';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const NasSelector = (props, ref) => {
  const { vpcId, value, onChange = () => {}, NewSaeVersion = '', enableCustomNasMountOptions = false } = props;
  const [useNas, setUseNas] = useState(false);
  const [fileSystemList, setFileSystemList] = useState([]);
  const [fileSystemLoading, setFileSystemLoading] = useState(false);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  let tableRef = React.createRef() as any;
  const field = Field.useField({
    onChange: (name, value) => {
      if (name === 'nasEnabled' && !!value) {
        // 启用时不 onChange 不会触发校验
        return;
      }
      _.debounce(fieldValueChange, 500)();
    },
  });
  const { init } = field;

  useEffect(() => {
    initValue();
  }, [value, fileSystemList, vpcId]);

  useEffect(() => {
    useNas && getFileSystemList();
  }, [useNas]);

  useEffect(() => {
    if (!useNas) {
      onChange([]);
    }
  }, [useNas]);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [useNas],
  );

  const initValue = () => {
    if (value && value.length) {
      setUseNas(true);

      const nasList = value.map((item) => {
        const { nasId = '', mountDomain = '' } = item;
        const { mountTarget = [] } =
          _.find(fileSystemList, (item) => item.value.includes(nasId)) || {};
        const mountTargetFilter = mountTarget.filter((item) => item.VpcId === vpcId);
        const mountTargetFormat = mountTargetFilter.map((item) => ({
          label: item.MountTargetDomain,
          value: item.MountTargetDomain,
        }));

        return {
          ...item,
          mountDomain: mountTargetFormat.length ? mountDomain : '',
          mountTarget: mountTargetFormat,
        };
      });
      field.setValues({ nasEnabled: true, nasList });
    }
  };

  const getFileSystemList = async () => {
    setFileSystemLoading(true);
    const res = await services.DescribeFileSystems({
      params: {
        PageSize: 100,
        PageNumber: 1,
      },
      customErrorHandle: (error, data) => {},
    });

    const { FileSystems = {} } = res;
    const { FileSystem = [] } = FileSystems;

    const fileSystemListFormat = FileSystem.map((item) => ({
      label: (
        <>
          <span className="mr-l">{item.Description || item.FileSystemId}</span>
          <span className="text-description">{item.FileSystemId}</span>
        </>
      ),

      value: item.FileSystemId,
      fileSystemType: item.FileSystemType,
      mountTarget: item?.MountTargets?.MountTarget || [],
    }));
    setFileSystemList(fileSystemListFormat);
    setFileSystemLoading(false);
  };

  // 校验
  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((error, value) => {
        if (error) {
          resolve(' ');
          return;
        }
        resolve(true);
      });
    });
  };

  const fieldValueChange = () => {
    const { getValues } = field;

    const { nasEnabled, nasList } = getValues() as any;
    if (!nasEnabled) {
      onChange([]);
      return;
    }
    onChange(nasList);
  };

  const handleNasIdChange = ({ formGroupId, item, value: nasId }) => {
    const pathChild = tableRef.getComponent(formGroupId, 'mountDomain');
    // 清空 挂载源
    pathChild.setValue('');

    tableRef.setComponentProps(formGroupId, 'mountDomain', {
      state: 'loading',
    });

    const { mountTarget } = item;
    const mountTargetFilter = mountTarget.filter((item) => item.VpcId === vpcId);
    const mountTargetFormat = mountTargetFilter.map((item) => ({
      label: item.MountTargetDomain,
      value: item.MountTargetDomain,
    }));
    tableRef.setComponentProps(formGroupId, 'mountDomain', {
      state: null,
      dataSource: mountTargetFormat,
      notFoundContent: (
        <>
          {intl('saenext.NasSelectorField.micro-app.NasSelector_back.TheCurrentNasFileSystem')}
          <a
            href={`${CachedData.confLink('feature:nasnext:url')}/${regionId}/filesystem/${nasId}/mount`}
            target="_blank"
          >
            {intl('saenext.NasSelectorField.micro-app.NasSelector_back.CreateAMountPoint')}
          </a>
        </>
      ),
    });
  };

  const tableValidation = (value = []) => {
    if (!vpcId) {
      return intl('saenext.NasSelectorField.micro-app.NasSelector_back.TheNasDataConfigurationIs');
    }
    const mountDirs = [];
    for (let i = 0; i < value.length; i++) {
      const item = value[i];
      if (!item) {
        return intl(
          'saenext.NasSelectorField.micro-app.NasSelector_back.TheNasDataConfigurationIs',
        );
      }
      if (
        !item.nasId ||
        !item.mountDomain ||
        !item.nasPath ||
        !item.mountPath ||
        item.readOnly === undefined
      ) {
        return intl(
          'saenext.NasSelectorField.micro-app.NasSelector_back.TheNasDataConfigurationIs',
        );
      }
      const nasPathRegex = /^\/|[a-zA-Z0-9._-]+(\/[a-zA-Z0-9._-]+)*$/;
      const mountPathRegex = /^\/|(\/[a-zA-Z0-9._-]+)+$/;
      if (item.nasPath) {
        if (!nasPathRegex.test(item.nasPath)) {
          return intl(
            'saenext.NasSelectorField.micro-app.NasSelector_back.EnterTheCorrectMountDirectory',
          );
        }
      }
      if (item.mountPath) {
        if (NewSaeVersion === 'pro' && item.mountPath === '/tmp') {
          return intl(
            'saenext.NasSelectorField.micro-app.NasSelector_back.ForProfessionalApplicationsTheTmp',
          );
        }
        if (!mountPathRegex.test(item.mountPath)) {
          return intl(
            'saenext.NasSelectorField.micro-app.NasSelector_back.EnterTheCorrectContainerPath',
          );
        }
      }
      mountDirs.push(item.mountPath);
    }
    if (_.uniq(mountDirs).length !== mountDirs.length) {
      return intl(
        'saenext.NasSelectorField.micro-app.NasSelector_back.TheContainerPathIsDuplicate',
      );
    }
    if (mountDirs.length > 1) {
      if (checkOverlap(mountDirs)) {
        return intl(
          'saenext.NasSelectorField.micro-app.NasSelector_back.ContainerPathsCannotOverlap',
        );
      }
    }

    return true;
  };

  const checkOverlap = (paths) => {
    for (let i = 0; i < paths.length; i++) {
      for (let j = i + 1; j < paths.length; j++) {
        if (isSubPath(paths[i], paths[j])) {
          console.log(`Paths ${paths[i]} and ${paths[j]} overlap.`);
          return true;
        }
      }
    }
    return false;
  };
  const isSubPath = (path1, path2) => {
    // 分割路径为数组
    const parts1 = path1.split('/').filter(Boolean); // 过滤掉空字符串
    const parts2 = path2.split('/').filter(Boolean);

    // 确保parts1是更长的那个数组
    if (parts1.length < parts2.length) return isSubPath(path2, path1);

    // 检查parts2是否是parts1的前缀
    for (let i = 0; i < parts2.length; i++) {
      if (parts1[i] !== parts2[i]) return false;
    }

    return true;
  };

  return (
    <>
      <Form field={field}>
        <Form.Item
          label={intl('saenext.NasSelectorField.micro-app.NasSelector_back.EnableNasFileStorage')}
          {...(fieldLayout as any)}
          help={
            <div>
              <span>
                {intl(
                  'saenext.NasSelectorField.micro-app.NasSelector_back.NasFileStorageHasObvious',
                )}
              </span>
              <span className="color-text-description">
                {intl('saenext.NasSelectorField.micro-app.NasSelector_back.NasIsNotRecommendedAs')}
                <a
                  href={CachedData.confLink('help:sae:how-to-prevent-exceptions')}
                  target="_blank"
                >
                  {intl(
                    'saenext.NasSelectorField.micro-app.NasSelector_back.MultiClientConcurrentWriteConflicts',
                  )}
                </a>
                {intl('saenext.NasSelectorField.micro-app.NasSelector_back.TheProblem')}
                <a
                  href={CachedData.confLink('help:sae:set-log-collection-to-sls')}
                  target="_blank"
                >
                  {intl(
                    'saenext.NasSelectorField.micro-app.NasSelector_back.HowToConfigureLogCollection',
                  )}
                </a>
              </span>
            </div>
          }
        >
          <Switch
            checked={useNas}
            {...init('nasEnabled', {
              initValue: useNas,
              props: {
                onChange: (value) => setUseNas(value),
              },
            })}
          />
        </Form.Item>
        {useNas && (
          <>
            {!vpcId && (
              <Message type="warning" className="mb-l">
                {intl(
                  'saenext.NasSelectorField.micro-app.NasSelector_back.TheApplicationDoesNotSpecify',
                )}
              </Message>
            )}

            <TableField
              name="nasList"
              className="full24-table"
              layout="TABLER"
              showIndex={false}
              showTableHead={true}
              showSortable={false}
              minItems={1}
              addButtonText={intl('saenext.NasSelectorField.micro-app.NasSelector_back.Add')}
              showDeleteConfirm={false}
              actionsColumnWidth={80}
              delButtonText={<Icon type="delete" />}
              ref={(c) => (tableRef = c?.getInstance?.() || c)}
              validation={[
                {
                  type: 'customValidate',
                  param: tableValidation,
                },
              ]}
            >
              <SelectField
                name="nasId"
                label={
                  <div className="flex">
                    <span>
                      {intl('saenext.NasSelectorField.micro-app.NasSelector_back.NasFileSystem')}
                    </span>
                    <RefreshButton handler={getFileSystemList} className="ml-s" />
                  </div>
                }
                dataSource={fileSystemList}
                state={fileSystemLoading ? 'loading' : null}
                onChange={(item) => handleNasIdChange(item)}
                showSearch
                notFoundContent={
                  <>
                    {intl(
                      'saenext.NasSelectorField.micro-app.NasSelector_back.NasIsNotAvailableIn',
                    )}

                    <a href={CachedData.confLink('feature:nas:url')} target="_blank">
                      {intl('saenext.NasSelectorField.micro-app.NasSelector_back.NasConsole')}
                    </a>
                    {intl('saenext.NasSelectorField.micro-app.NasSelector_back.Create')}
                  </>
                }
              />

              <SelectField
                name="mountDomain"
                label={intl('saenext.NasSelectorField.micro-app.NasSelector_back.MountSource')}
                showSearch
                disabled={!vpcId}
                notFoundContent={
                  <>
                    {intl(
                      'saenext.NasSelectorField.micro-app.NasSelector_back.TheCurrentNasFileSystem',
                    )}
                    <a href={CachedData.confLink('feature:nas:url')} target="_blank">
                      {intl(
                        'saenext.NasSelectorField.micro-app.NasSelector_back.CreateAMountPoint',
                      )}
                    </a>
                  </>
                }
              />

              <TextField
                name="nasPath"
                label={intl('saenext.NasSelectorField.micro-app.NasSelector_back.MountDirectory')}
                placeholder={intl(
                  'saenext.NasSelectorField.micro-app.NasSelector_back.ExampleOrXxXxx',
                )}
                renderView={(val) => <span className="break-all">{val}</span>}
              />

              <TextField
                name="mountPath"
                label={intl('saenext.NasSelectorField.micro-app.NasSelector_back.ContainerPath')}
                placeholder={intl(
                  'saenext.NasSelectorField.micro-app.NasSelector_back.ExampleTmpNas',
                )}
                renderView={(val) => <span className="break-all">{val}</span>}
              />

              <SelectField
                name="readOnly"
                label={intl('saenext.NasSelectorField.micro-app.NasSelector_back.Permission')}
                initialValue={true}
                dataSource={[
                  {
                    label: intl('saenext.NasSelectorField.micro-app.NasSelector_back.ReadOnly'),
                    value: true,
                  },
                  {
                    label: intl('saenext.NasSelectorField.micro-app.NasSelector_back.ReadWrite'),
                    value: false,
                  },
                ]}
              />

              {enableCustomNasMountOptions?  (
                <TextField
                  name="mountOptions"
                  label={intl(
                    'saenext.NasSelectorField.micro-app.NasSelector_back.MountParameters',
                  )}
                />
              ): <TextField style={{ width: 0 }} behavior="HIDDEN" />}
            </TableField>
          </>
        )}
      </Form>
    </>
  );
};

export default forwardRef(NasSelector);
