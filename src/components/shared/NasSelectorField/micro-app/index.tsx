import React, { Component } from 'react';
import NasSelector from './NasSelector_back';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';
import _ from 'lodash';

type Props = {
  value?: Object;
  onChange?: Function;
};

class NasSelectorFieldClass extends Component<Props> {
  private nasRef = React.createRef() as any;

  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.nasRef.validate();
  }

  render() {

    return (
      <NasSelector
        {...this.props}
        ref={(ref) => this.nasRef = ref}
      />
    )
  }
}

class NasSelectorField extends BaseField {
  props;
  static displayName = 'NasSelectorField';

  // static propTypes = {
  //   ...BaseField.propTypes,
  // };

  // static defaultProps = {
  //   ...BaseField.defaultProps,
  // };

  getProps() {
    return {
      ...this.props,
    };
  }

  formatValueIn(value, props) {
    if (Array.isArray(value)) {
      return value;
    }
    
    const nasConfigs = JSON.parse(value);
    return nasConfigs;
  }

  formatValueOut(value = []) {
    if (!Array.isArray(value)) {
      return value;
    }
    const nasConfigs = _.map(value, (item = {}) => {
      const { nasId, mountDomain, nasPath, mountPath, readOnly, mountOptions } = item;
      return {
        nasId,
        mountDomain,
        nasPath,
        mountPath,
        readOnly,
        mountOptions,
      }
    })
    
    return JSON.stringify(nasConfigs);
  }

  renderControl(fieldProps) {

    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return (
      <NasSelectorFieldClass
        {...newProps}
      />
    );
  }
}

export default ConfigProvider.config(NasSelectorField as any)
