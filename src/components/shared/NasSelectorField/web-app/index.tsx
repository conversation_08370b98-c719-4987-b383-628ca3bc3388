import React, { Component } from 'react';
import NasSelector from './NasSelector_back';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';
import _ from 'lodash';

type Props = {
  value?: Object;
  onChange?: Function;
};

class NasSelectorFieldClass extends Component<Props> {
  
  private nasRef = React.createRef as any;
  constructor(props) {
    super(props);
  }

  validate = () => {
    return this.nasRef.validate();
  }

  render() {

    return (
      <NasSelector
        {...this.props}
        ref={(ref) => this.nasRef = ref}
      />
    )
  }
}

class NasSelectorField extends BaseField {
  props;
  static displayName = 'NasSelectorField';


  getProps() {
    return {
      ...this.props,
    };
  }

  formatValueIn(value, props) {
    if (Array.isArray(value)) {
      return value;
    }
    
    const { mountPoints = [] } = value || {};

    const nasConfigs = _.map(mountPoints, (item) => {
      const { mountDir = '', serverAddr = '' } = item;
      const [ mountDomain = '', nasPath = ''] = serverAddr.split(':');
      const nasId = mountDomain.replace(/^extreme-/, '').match(/^[^-]*/)[0];
  
      return {
        nasId,
        mountDomain: (nasId === mountDomain.replace(/^extreme-/, '')) ? '' : mountDomain,
        nasPath,
        mountDir,
      }
    })

    return nasConfigs;
  }

  formatValueOut(nasConfigs = []) {
    const mountPoints = _.map(nasConfigs, (item) => {
      const { nasId, mountDomain, nasPath, mountDir } = item;
      return {
        mountDir,
        serverAddr: `${mountDomain || nasId}:${nasPath}`,
      }
    })
    
    return {
      mountPoints,
    }
  }

  renderControl(fieldProps) {

    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return (
      <NasSelectorFieldClass
        {...newProps}
      />
    );
  }
}

export default ConfigProvider.config(NasSelectorField as any)
