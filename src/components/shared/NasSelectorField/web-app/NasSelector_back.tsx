import { intl } from '@ali/cnd';
import React, { useEffect, useState, forwardRef, useImperativeHandle, useContext } from 'react';
import { Form, Icon, Message, Switch, Field, ConsoleContext } from '@ali/cnd';
import { TableField, TextField, SelectField } from '@ali/deep';
import services from '~/services';
import _ from 'lodash';
import CachedData from '~/cache/common';
import RefreshButton from '../../RefreshButton';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 16, align: 'center' },
  labelCol: { span: 8, style: { width: 220, maxWidth: 220 } },
};

const NasSelector = (props, ref) => {
  const { type, formField, value, onChange = () => {} } = props;
  const { vpcId } = formField?.getValue('vpcConfig') || {};

  const [useNas, setUseNas] = useState(false);
  const [fileSystemList, setFileSystemList] = useState([]);
  const [fileSystemLoading, setFileSystemLoading] = useState(false);
  const [nasRamWarn, setNasRamWarn] = useState(false);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  let tableRef = React.createRef() as any;
  const field = Field.useField({
    onChange: () => {
      _.debounce(fieldValueChange, 500)();
    },
  });
  const { init } = field;
  const nasEnabled = CachedData.getOpenStatus('nasStatus');

  useEffect(() => {
    initValue();
  }, [value.length, fileSystemList, vpcId]);

  useEffect(() => {
    useNas && getFileSystemList();
  }, [useNas]);

  useImperativeHandle(
    ref,
    () => ({
      useNas,
      validate,
    }),
    [useNas],
  );

  const initValue = () => {
    if (value && value.length) {
      setUseNas(true);

      const nasList = value.map((item) => {
        const { nasId = '', mountDomain = '' } = item;
        const { mountTarget = [] } =
          _.find(fileSystemList, (item) => item.value.includes(nasId)) || {};
        const mountTargetFilter = mountTarget.filter((item) => item.VpcId === vpcId);
        const mountTargetFormat = mountTargetFilter.map((item) => ({
          label: item.MountTargetDomain,
          value: item.MountTargetDomain,
        }));

        return {
          ...item,
          mountDomain: mountTargetFormat.length ? mountDomain : '',
          mountTarget: mountTargetFormat,
        };
      });
      field.setValues({ nasEnabled: true, nasList });
    }
  };

  const getFileSystemList = async () => {
    setFileSystemLoading(true);
    const res = await services.DescribeFileSystems({
      params: {
        PageSize: 100,
        PageNumber: 1,
      },
      customErrorHandle: (error, data) => {
        if (error.code === 'Forbbiden.Ram') {
          setNasRamWarn(true);
          return {};
        }
        return error;
      },
    });

    const { FileSystems = {} } = res;
    const { FileSystem = [] } = FileSystems;

    const fileSystemListFormat = FileSystem.map((item) => ({
      label: (
        <>
          <span className="mr-l">{item.Description || item.FileSystemId}</span>
          <span className="text-description">{item.FileSystemId}</span>
        </>
      ),

      value: item.FileSystemId,
      fileSystemType: item.FileSystemType,
      mountTarget: item?.MountTargets?.MountTarget || [],
    }));
    setFileSystemList(fileSystemListFormat);
    setFileSystemLoading(false);
  };

  // 校验
  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((error, value) => {
        if (error) {
          resolve(false);
          return;
        }
        resolve(true);
      });
    });
  };

  const fieldValueChange = () => {
    const { validate, getValue } = field;

    let _nasConfigs = [];

    // 当开关关闭的时候 不会触发下validate
    const _nasEnabled = getValue('nasEnabled');
    if (!_nasEnabled) {
      onChange(_nasConfigs);
      return;
    }
    validate(async (error, values: any) => {
      if (error) return;
      // @ts-ignore
      const { nasList } = values;
      _nasConfigs = _.map(nasList, (item) => {
        const { nasId, mountDomain, nasPath, mountDir } = item;
        return {
          nasId,
          mountDir,
          mountDomain,
          nasPath,
        };
      });
      onChange(_nasConfigs);
    });
  };

  const handleNasIdChange = ({ formGroupId, item, value: nasId }) => {
    const pathChild = tableRef.getComponent(formGroupId, 'mountDomain');
    // 清空 挂载源
    pathChild.setValue('');

    tableRef.setComponentProps(formGroupId, 'mountDomain', {
      state: 'loading',
    });

    const { mountTarget } = item;
    const mountTargetFilter = mountTarget.filter((item) => item.VpcId === vpcId);
    const mountTargetFormat = mountTargetFilter.map((item) => ({
      label: item.MountTargetDomain,
      value: item.MountTargetDomain,
    }));
    tableRef.setComponentProps(formGroupId, 'mountDomain', {
      state: null,
      dataSource: mountTargetFormat,
      notFoundContent: (
        <>
          {intl('saenext.NasSelectorField.web-app.NasSelector_back.TheCurrentNasFileSystem')}
          <a
            href={`${CachedData.confLink('feature:nasnext:url')}/${regionId}/filesystem/${nasId}/mount`}
            target="_blank"
          >
            {intl('saenext.NasSelectorField.web-app.NasSelector_back.CreateAMountPoint')}
          </a>
        </>
      ),
    });
  };

  const tableValidation = (value = []) => {
    if (!vpcId) {
      return intl('saenext.NasSelectorField.web-app.NasSelector_back.TheNasDataConfigurationIs');
    }
    const mountDirs = [];
    for (let i = 0; i < value.length; i++) {
      const item = value[i];
      if (!item) {
        return intl('saenext.NasSelectorField.web-app.NasSelector_back.TheNasDataConfigurationIs');
      }
      if (!item.nasId || !item.mountDomain || !item.nasPath || !item.mountDir) {
        return intl('saenext.NasSelectorField.web-app.NasSelector_back.TheNasDataConfigurationIs');
      }
      mountDirs.push(item.mountDir);
    }
    if (_.uniq(mountDirs).length !== mountDirs.length) {
      return intl('saenext.NasSelectorField.web-app.NasSelector_back.TheContainerPathIsDuplicate');
    }
    return true;
  };

  return (
    <>
      {/* {nasEnabled ? null : (
        <Message type="warning" className="mb-l">
          {intl('saenext.NasSelectorField.web-app.NasSelector_back.YouHaveNotActivatedFile')}

          <a
            href="https://common-buy.aliyun.com/?commodityCode=naspost"
            target="_blank"
            className="ml-s"
          >
            {intl('saenext.NasSelectorField.web-app.NasSelector_back.ActivateNow')}
          </a>
        </Message>
      )} */}

      <OpenStatusMessage
        product="nas"
      />

      <Form field={field}>
        <Form.Item
          label={intl('saenext.NasSelectorField.web-app.NasSelector_back.EnableNasFileStorage')}
          {...(fieldLayout as any)}
          help={
            <div>
              <span>
                {intl('saenext.NasSelectorField.web-app.NasSelector_back.NasFileStorageHasObvious')}
              </span>
              <span className="color-text-description">
                {intl('saenext.NasSelectorField.web-app.NasSelector_back.NasIsNotRecommendedAs')}
                <a href={CachedData.confLink('help:sae:how-to-prevent-exceptions-1-0')} target="_blank">
                  {intl(
                    'saenext.NasSelectorField.web-app.NasSelector_back.MultiClientConcurrentWriteConflicts',
                  )}
                </a>
                {intl('saenext.NasSelectorField.web-app.NasSelector_back.TheProblem')}
                <a href={CachedData.confLink('help:sae:log-management-sls')} target="_blank">
                  {intl(
                    'saenext.NasSelectorField.web-app.NasSelector_back.HowToConfigureLogCollection',
                  )}
                </a>
              </span>
            </div>
          }
        >
          <Switch
            checked={useNas}
            disabled={!nasEnabled}
            {...init('nasEnabled', {
              initValue: useNas,
              props: {
                onChange: (value) => {
                  setUseNas(value);
                  if (!value) {
                    setNasRamWarn(false);
                  }
                },
              },
            })}
          />
        </Form.Item>
        {useNas && (
          <>
            {CachedData.isSubAccount() && nasRamWarn ? (
              <Message type="warning" className="mb-l">
                {intl(
                  'saenext.NasSelectorField.web-app.NasSelector_back.CurrentlyYourAccountHasNot',
                )}

                <a
                  href={`${CachedData.confLink('feature:ram:url')}/#/role/authorize`}
                  target="_blank"
                  className="ml-s mr-s"
                >
                  {intl('saenext.NasSelectorField.web-app.NasSelector_back.RamAccessControl')}
                </a>
                {intl(
                  'saenext.NasSelectorField.web-app.NasSelector_back.UseTheProductAfterAuthorization',
                )}
              </Message>
            ) : null}

            {!vpcId && (
              <Message type="warning" className="mb-l">
                {type === 'version'
                  ? intl(
                      'saenext.NasSelectorField.web-app.NasSelector_back.TheApplicationDoesNotSpecify',
                    )
                  : intl(
                      'saenext.NasSelectorField.web-app.NasSelector_back.TheApplicationDoesNotSpecify.1',
                    )}
              </Message>
            )}

            <TableField
              name="nasList"
              className="full24-table"
              layout="TABLER"
              showIndex={false}
              showTableHead={true}
              showSortable={false}
              minItems={1}
              addButtonText={intl('saenext.NasSelectorField.web-app.NasSelector_back.Add')}
              showDeleteConfirm={false}
              actionsColumnWidth={80}
              delButtonText={<Icon type="delete" />}
              ref={(c) => (tableRef = c?.getInstance?.() || c)}
              validation={[
                {
                  type: 'customValidate',
                  param: tableValidation,
                },
              ]}
            >
              <SelectField
                name="nasId"
                label={
                  <div className="flex">
                    <span>
                      {intl('saenext.NasSelectorField.web-app.NasSelector_back.NasFileSystem')}
                    </span>
                    <RefreshButton handler={getFileSystemList} className="ml-s" />
                  </div>
                }
                dataSource={fileSystemList}
                state={fileSystemLoading ? 'loading' : null}
                onChange={(item) => handleNasIdChange(item)}
                showSearch
                notFoundContent={
                  <>
                    {intl('saenext.NasSelectorField.web-app.NasSelector_back.NasIsNotAvailableIn')}

                    <a href={CachedData.confLink('feature:nas:url')} target="_blank">
                      {intl('saenext.NasSelectorField.web-app.NasSelector_back.NasConsole')}
                    </a>
                    {intl('saenext.NasSelectorField.web-app.NasSelector_back.Create')}
                  </>
                }
              />

              <SelectField
                name="mountDomain"
                label={intl('saenext.NasSelectorField.web-app.NasSelector_back.MountSource')}
                showSearch
                disabled={!vpcId}
                notFoundContent={
                  <>
                    {intl(
                      'saenext.NasSelectorField.web-app.NasSelector_back.TheCurrentNasFileSystem',
                    )}
                    <a href={CachedData.confLink('feature:nas:url')} target="_blank">
                      {intl('saenext.NasSelectorField.web-app.NasSelector_back.CreateAMountPoint')}
                    </a>
                  </>
                }
              />

              <TextField
                name="nasPath"
                label={intl('saenext.NasSelectorField.web-app.NasSelector_back.MountDirectory')}
                placeholder={intl(
                  'saenext.NasSelectorField.web-app.NasSelector_back.EnterTheMountDirectorySuch',
                )}
              />

              <TextField
                name="mountDir"
                label={intl('saenext.NasSelectorField.web-app.NasSelector_back.ContainerPath')}
                placeholder={intl(
                  'saenext.NasSelectorField.web-app.NasSelector_back.EnterTheContainerPathSuch',
                )}
              />
            </TableField>
          </>
        )}
      </Form>
    </>
  );
};

export default forwardRef(NasSelector);
