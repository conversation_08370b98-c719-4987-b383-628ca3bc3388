import { intl } from '@ali/cnd';
import React, { useEffect, useState, forwardRef, useImperativeHandle, useContext } from 'react';
import {
  Button,
  ConsoleContext,
  Form,
  Icon,
  Input,
  Message,
  Select,
  Switch,
  CndTable,
} from '@ali/cnd';
import services from '~/services';
import _ from 'lodash';
import RefreshButton from '../../RefreshButton';
import CachedData from '~/cache/common';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 16, align: 'center' },
  labelCol: { span: 8, style: { width: 220, maxWidth: 220 } },
};

const NasSelector = (props, ref) => {
  const { type, formField, value, nasEnabled, onChange = () => {} } = props;
  const { vpcId } = formField?.getValue('vpcConfig') || {};

  const [useNas, setUseNas] = useState(false);
  const [nasList, setNasList] = useState<any>([{}]);
  const [fileSystemList, setFileSystemList] = useState([]);
  const [fileSystemLoading, setFileSystemLoading] = useState(false);
  const [nasRamWarn, setNasRamWarn] = useState(false);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  useEffect(() => {
    initValue();
  }, [value, fileSystemList, vpcId]);

  useEffect(() => {
    useNas && queryFileSystemList();
  }, [useNas]);

  useImperativeHandle(
    ref,
    () => ({
      useNas,
      getValues,
    }),
    [useNas, nasList],
  );

  const initValue = () => {
    if (value && value.length) {
      setUseNas(true);

      const nasList = value.map((item) => {
        const { nasId = '', mountDomain = '' } = item;
        const { mountTarget = [] } =
          _.find(fileSystemList, (item) => item.value.includes(nasId)) || {};
        const mountTargetFilter = mountTarget.filter((item) => item.VpcId === vpcId);
        const mountTargetFormat = mountTargetFilter.map((item) => item.MountTargetDomain);

        return {
          ...item,
          mountDomain: mountTargetFormat.length ? mountDomain : '',
          mountTarget: mountTargetFormat,
        };
      });
      setNasList([...nasList]);
    }
  };

  const getValues = () => {
    if (!useNas) {
      return [];
    }
    const value = nasList.map((item) => {
      const { mountDomain, nasPath, mountDir } = item;
      const serverAddr = `${mountDomain}:${nasPath}`;
      return {
        mountDir,
        serverAddr,
      };
    });
    return value;
  };

  const queryFileSystemList = async () => {
    setFileSystemLoading(true);
    const res = await services.DescribeFileSystems({
      params: {
        PageSize: 100,
        PageNumber: 1,
      },
      customErrorHandle: (error, data) => {
        if (error.code === 'Forbbiden.Ram') {
          setNasRamWarn(true);
          return {};
        }
        return error;
      },
    });

    const { FileSystems = {} } = res;
    const { FileSystem = [] } = FileSystems;

    const fileSystemListFormat = FileSystem.map((item) => ({
      label: (
        <>
          <span className="mr-l">{item.Description || item.FileSystemId}</span>
          <span className="text-description">{item.FileSystemId}</span>
        </>
      ),

      value: item.FileSystemId,
      fileSystemType: item.FileSystemType,
      mountTarget: item?.MountTargets?.MountTarget || [],
    }));
    setFileSystemList(fileSystemListFormat);
    setFileSystemLoading(false);
  };

  const handleSwitchChange = (val) => {
    setUseNas(val);
    if (val) {
      onChange([
        {
          nasId: '',
          mountDomain: '',
          nasPath: '',
          mountDir: '',
        },
      ]);
    } else {
      setNasRamWarn(false);
      onChange([]);
    }
  };

  const handleNasIdChange = async (idx, val, item) => {
    nasList[idx].nasId = val;
    nasList[idx].mountDomain = '';

    const { mountTarget } = item;
    const mountTargetFilter = mountTarget.filter((item) => item.VpcId === vpcId);
    const mountTargetFormat = mountTargetFilter.map((item) => item.MountTargetDomain);
    nasList[idx].mountTarget = mountTargetFormat;
    setNasList(nasList);
    onChange(nasList);
  };

  const handleNasConfigChange = (idx, val, key) => {
    nasList[idx][key] = val;
    setNasList(nasList);
    onChange(nasList);
  };

  const handleAddNas = () => {
    nasList.push({
      nasId: '',
      mountDomain: '',
      nasPath: '',
      mountDir: '',
    });
    setNasList(nasList);
    onChange(nasList);
    // setRefreshIndex(refreshIndex + 1);
  };

  const handleDeleteNas = (idx) => {
    if (nasList.length === 1) {
      Message.warning(intl('saenext.NasSelectorField.web-app.NasSelector.KeepAtLeastOneRecord'));
      return;
    }

    nasList.splice(idx, 1);
    setNasList(nasList);
    onChange(nasList);
    // setRefreshIndex(refreshIndex + 1);
  };

  return (
    <>
      {CachedData.getOpenStatus(`nasStatus`) ? null : (
        <Message type="warning" className="mb-l">
          {intl('saenext.NasSelectorField.web-app.NasSelector.YouHaveNotActivatedFile')}

          <a
            href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=naspost`}
            target="_blank"
            className="ml-s"
          >
            {intl('saenext.NasSelectorField.web-app.NasSelector.ActivateNow')}
          </a>
        </Message>
      )}

      <Form {...(fieldLayout as any)}>
        <Form.Item
          label={intl('saenext.NasSelectorField.web-app.NasSelector.EnableNasFileStorage')}
          help={
            <div>
              <span>
                {intl('saenext.NasSelectorField.web-app.NasSelector.NasFileStorageHasObvious')}
              </span>
              <span className="color-text-description">
                {intl('saenext.NasSelectorField.web-app.NasSelector.NasIsNotRecommendedAs')}
                <a href={CachedData.confLink('help:sae:how-to-prevent-exceptions-1-0')} target="_blank">
                  {intl(
                    'saenext.NasSelectorField.web-app.NasSelector.MultiClientConcurrentWriteConflicts',
                  )}
                </a>
                {intl('saenext.NasSelectorField.web-app.NasSelector.TheProblem')}
                <a href={CachedData.confLink('help:sae:log-management-sls')} target="_blank">
                  {intl('saenext.NasSelectorField.web-app.NasSelector.HowToConfigureLogCollection')}
                </a>
              </span>
            </div>
          }
        >
          <Switch checked={useNas} disabled={!nasEnabled} onChange={handleSwitchChange} />
        </Form.Item>
      </Form>

      {useNas && (
        <>
          <>
            {CachedData.isSubAccount() && nasRamWarn ? (
              <Message type="warning" className="mb-l">
                {intl('saenext.NasSelectorField.web-app.NasSelector.CurrentlyYourAccountHasNot')}

                <a
                  href={`${CachedData.confLink('feature:ram:url')}/#/role/authorize`}
                  target="_blank"
                  className="ml-s mr-s"
                >
                  {intl('saenext.NasSelectorField.web-app.NasSelector.RamAccessControl')}
                </a>
                {intl(
                  'saenext.NasSelectorField.web-app.NasSelector.UseTheProductAfterAuthorization',
                )}
              </Message>
            ) : null}

            {!vpcId && (
              <Message type="warning" className="mb-l">
                {type === 'version'
                  ? intl('saenext.NasSelectorField.web-app.NasSelector.TheCurrentVersionDoesNot')
                  : intl(
                      'saenext.NasSelectorField.web-app.NasSelector.TheApplicationDoesNotSpecify',
                    )}
              </Message>
            )}

            <CndTable
              dataSource={nasList}
              primaryKey="key"
              style={{ width: '100%' }}
              hasBorder={false}
            >
              <CndTable.Column
                title={
                  <span>
                    <span>
                      {intl('saenext.NasSelectorField.web-app.NasSelector.NasFileSystem')}
                    </span>
                  </span>
                }
                width="20%"
                dataIndex="nasId"
                // @ts-ignore
                cell={(nasId, idx, record) => (
                  <div className="flex" style={{ maxWidth: 340 }}>
                    <Select
                      value={nasId}
                      dataSource={fileSystemList}
                      state={fileSystemLoading ? 'loading' : record.nasState}
                      onChange={(val, actionType, item) => handleNasIdChange(idx, val, item)}
                      style={{ flex: 1 }}
                      showSearch
                      notFoundContent={
                        <>
                          {intl('saenext.NasSelectorField.web-app.NasSelector.NasIsNotAvailableIn')}

                          <a href={CachedData.confLink('feature:nas:url')} target="_blank">
                            {intl('saenext.NasSelectorField.web-app.NasSelector.NasConsole')}
                          </a>
                          {intl('saenext.NasSelectorField.web-app.NasSelector.Create')}
                        </>
                      }
                    />

                    <RefreshButton handler={queryFileSystemList} className="ml-l" />
                  </div>
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>{intl('saenext.NasSelectorField.web-app.NasSelector.MountSource')}</span>
                  </span>
                }
                width="20%"
                dataIndex="mountDomain"
                // @ts-ignore
                cell={(mountDomain, idx, record) => (
                  <Select
                    value={mountDomain}
                    disabled={!vpcId || !record.nasId}
                    notFoundContent={
                      <>
                        {intl(
                          'saenext.NasSelectorField.web-app.NasSelector.TheCurrentNasFileSystem',
                        )}
                        <a
                          href={`${CachedData.confLink('feature:nasnext:url')}/${regionId}/filesystem/${record.nasId}/mount`}
                          target="_blank"
                        >
                          {intl('saenext.NasSelectorField.web-app.NasSelector.CreateAMountPoint')}
                        </a>
                      </>
                    }
                    dataSource={record.mountTarget}
                    onChange={(val) => handleNasConfigChange(idx, val, 'mountDomain')}
                    showSearch
                    style={{ width: '100%' }}
                  />
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>
                      {intl('saenext.NasSelectorField.web-app.NasSelector.MountDirectory')}
                    </span>
                  </span>
                }
                width="25%"
                dataIndex="nasPath"
                // @ts-ignore
                cell={(nasPath, idx) => (
                  <Input
                    value={nasPath}
                    placeholder={intl(
                      'saenext.NasSelectorField.web-app.NasSelector.ExampleOrXxXxx',
                    )}
                    style={{ width: '100%' }}
                    onChange={(val) => handleNasConfigChange(idx, val, 'nasPath')}
                    trim={true}
                  />
                )}
              />

              <CndTable.Column
                title={
                  <span>
                    <span>
                      {intl('saenext.NasSelectorField.web-app.NasSelector.ContainerPath')}
                    </span>
                  </span>
                }
                width="25%"
                dataIndex="mountDir"
                // @ts-ignore
                cell={(val, idx, record) => (
                  <Input
                    value={val}
                    placeholder={intl('saenext.NasSelectorField.web-app.NasSelector.ExampleTmpNas')}
                    style={{ width: '100%' }}
                    onChange={(val) => handleNasConfigChange(idx, val, 'mountDir')}
                    trim={true}
                  />
                )}
              />

              <CndTable.Column
                title={intl('saenext.NasSelectorField.web-app.NasSelector.Operation')}
                width="10%"
                // @ts-ignore
                cell={(val, idx, record) => (
                  <Button text className="scale-medium" onClick={() => handleDeleteNas(idx)}>
                    <Icon type="delete" />
                  </Button>
                )}
              />
            </CndTable>
            {nasList.length < 5 && (
              <Button style={{ marginTop: 16 }} onClick={handleAddNas}>
                <Icon type="add" />
                {intl('saenext.NasSelectorField.web-app.NasSelector.Add')}
              </Button>
            )}
          </>
        </>
      )}
    </>
  );
};

export default forwardRef(NasSelector);
