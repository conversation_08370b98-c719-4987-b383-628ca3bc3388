import { intl } from '@ali/cnd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { Field, Form, Input, Message } from '@ali/cnd';
import { replace } from 'lodash';
import C from '~/constants/common';
import services from '~/services';
import CachedData from '~/cache/common';

const ShellDownload = (props, ref) => {
  const { appId, instanceId, fileSizeLimit } = props;

  const fileSizeLimitMb = fileSizeLimit / 1024;

  const field = Field.useField();

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  const onOk = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) {
      return;
    }
    const { Localpath } = values;

    const { Data: downloadUrl } =
      (await services.downloadFiles(
        {
          AppId: appId,
          InstanceId: instanceId,
          Localpath,
        },
        true,
      )) || {};
    if (!downloadUrl) {
      Message.error(intl('saenext.shared.Webshell.ShellDownload.DownloadFailed'));
      return;
    }
    const httpsUrl = replace(downloadUrl, 'http://', 'https://');
    window.open(httpsUrl);
    return true;
  };

  const checkPath = (rule, value, callback) => {
    const reg = new RegExp('[\\:*?"<>()|]');
    if (reg.test(value)) {
      callback(intl('saenext.shared.Webshell.ShellDownload.TheFilePathCannotContain'));
    } else {
      callback();
    }
  };

  return (
    <Form {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT} field={field} style={{ width: 640 }}>
      <Form.Item
        label={intl('saenext.shared.Webshell.ShellDownload.DestinationPath')}
        required
        requiredMessage={intl('saenext.shared.Webshell.ShellDownload.EnterTheDestinationPath')}
        extra={
          <>
            {intl('saenext.shared.Webshell.ShellDownload.TheDownloadedFileCannotBe')}
            {fileSizeLimitMb}
            {intl('saenext.shared.Webshell.ShellDownload.MbExceeding')}
            {fileSizeLimitMb}
            {intl('saenext.shared.Webshell.ShellDownload.MbFilePlease')}
            <a
              href={CachedData.confLink('help:sae:use-the-webshell-check-the-health')}
              target="_blank"
            >
              {intl('saenext.shared.Webshell.ShellDownload.ReferenceDocumentation')}
            </a>
          </>
        }
        validator={checkPath}
      >
        <Input name="Localpath" required placeholder="" width={'100%'} />
      </Form.Item>
    </Form>
  );
};

export default forwardRef(ShellDownload);
