import { intl } from '@ali/cnd';
import { ShellEditor } from '@ali/cnd';
import { Checkbox, Form, Message } from '@ali/cnd';
import { isEmpty } from 'lodash';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import C from '~/constants/common';

const ShellInstall = (props, ref) => {
  const { regionId } = props;

  const [command, setCommand] = useState('');

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  const onOk = async () => {
    await navigator.clipboard.writeText(command);
    Message.success(intl('saenext.shared.Webshell.ShellInstall.CopiedTheInstallationCommandTo'));
    return true;
  };

  const onChange = (selected) => {
    if (isEmpty(selected)) {
      setCommand('');
      return;
    }

    const cmd =
      `/home/<USER>/.tools/curl -L http://sae-agent-${regionId}.oss-${regionId}-internal.aliyuncs.com/updateRepository.sh -o /home/<USER>/.tools/updateRepository.sh && chmod 700 /home/<USER>/.tools/updateRepository.sh && /home/<USER>/.tools/updateRepository.sh ` +
      selected.join(' ');
    setCommand(cmd);
  };

  const list = [
    {
      value: 'openssh-server',
      label: intl('saenext.shared.Webshell.ShellInstall.InstallOpenssh'),
    },
    {
      value: 'net-tools',
      label: intl('saenext.shared.Webshell.ShellInstall.InstallNetToolsNetstat'),
    },
    {
      value: 'iproute iproute2',
      label: intl('saenext.shared.Webshell.ShellInstall.InstallIprouteSs'),
    },
    {
      value: 'procps',
      label: intl('saenext.shared.Webshell.ShellInstall.InstallProcpsPsTopFree'),
    },
  ];

  return (
    <Form {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT} style={{ width: 640 }}>
      <Form.Item label={intl('saenext.shared.Webshell.ShellInstall.SelectInstallComponent')}>
        <Checkbox.Group defaultValue={[]} dataSource={list} onChange={onChange} />
      </Form.Item>
      <Form.Item label={intl('saenext.shared.Webshell.ShellInstall.PreviewCommand')}>
        <ShellEditor value={command} />
      </Form.Item>
    </Form>
  );
};

export default forwardRef(ShellInstall);
