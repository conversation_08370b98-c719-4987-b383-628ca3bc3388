import { intl, StatusType } from '@ali/cnd';

export enum TERMINAL_STATUS {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
}

export const TERMINAL_STATUS_MAP = {
  [TERMINAL_STATUS.CONNECTING]: {
    type: 'loading' as StatusType,
    color: '#FFC440',
  },
  [TERMINAL_STATUS.CONNECTED]: {
    type: 'success' as StatusType,
    color: '#1E8E3E',
  },
  [TERMINAL_STATUS.DISCONNECTED]: {
    type: 'error' as StatusType,
    color: '#D93026',
  },
};

export enum ACTION_TYPE {
  INSTALL = 'install',
  DOWNLOAD = 'download',
  UPLOAD = 'upload',
}

export const ACTION_TYPE_MAP = {
  [ACTION_TYPE.INSTALL]: {
    title: intl('saenext.shared.Webshell.constants.OneClickCopyInstallationCommand'),
    text: intl('saenext.shared.Webshell.constants.Copy'),
  },
  [ACTION_TYPE.DOWNLOAD]: {
    title: intl('saenext.shared.Webshell.constants.DownloadFiles'),
    text: intl('saenext.shared.Webshell.constants.Download'),
  },
  [ACTION_TYPE.UPLOAD]: {
    title: intl('saenext.shared.Webshell.constants.UploadFiles'),
    text: intl('saenext.shared.Webshell.constants.Upload'),
  },
};
