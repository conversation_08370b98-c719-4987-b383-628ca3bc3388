import { intl } from '@ali/cnd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { Field, Form, Input, Message } from '@ali/cnd';
import C from '~/constants/common';
import services from '~/services';
import PackageUploader from '../DeploySelectorField/PackageUploader';

const ShellUpload = (props, ref) => {
  const { appId, instanceId, fileSizeLimit } = props;

  const fileSizeLimitMb = fileSizeLimit / 1024;

  const field = Field.useField();

  useImperativeHandle(ref, () => ({
    onOk,
  }));

  const onOk = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) {
      return;
    }

    const { Data, Success } =
      (await services.uploadFiles(
        {
          AppId: appId,
          InstanceId: instanceId,
          ...values,
        },
      )) || {};
    if (!Success || (Data && !Data.includes('Succeed'))) {
      Message.error(intl('saenext.shared.Webshell.ShellUpload.UploadFailed'));
      return;
    }
    Message.success(intl('saenext.shared.Webshell.ShellUpload.UploadedSuccessfully'));
    return true;
  };

  const checkPath = (rule, value, callback) => {
    const reg = new RegExp('[\\:*?"<>()|]');
    if (reg.test(value)) {
      callback(intl('saenext.shared.Webshell.ShellUpload.TheFilePathCannotContain'));
    } else {
      callback();
    }
  };

  return (
    <Form {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT} field={field} style={{ width: 640 }}>
      <Form.Item
        label={intl('saenext.shared.Webshell.ShellUpload.FileUpload')}
        required
        requiredMessage={intl('saenext.shared.Webshell.ShellUpload.PleaseUploadTheFile')}
      >
        <PackageUploader
          name="CloudUrl"
          tools
          appId={appId}
          fileSizeLimit={fileSizeLimit * 1024}
          className="full-width"
        />
      </Form.Item>
      <Form.Item
        label={intl('saenext.shared.Webshell.ShellUpload.DestinationPath')}
        required
        requiredMessage={intl('saenext.shared.Webshell.ShellUpload.EnterTheDestinationPath')}
        extra={
          <>
            {intl('saenext.shared.Webshell.ShellUpload.TheUploadedFileCannotBe')}
            {fileSizeLimitMb}MB
          </>
        }
        validator={checkPath}
      >
        <Input name="Localpath" required placeholder="" width={'100%'} />
      </Form.Item>
    </Form>
  );
};

export default forwardRef(ShellUpload);
