import React, { useState } from 'react';
import './index.less';
import { StatusIndicator, Truncate } from '@ali/cnd';
import Terminal from './Terminal';
import services from '~/services';
import { TERMINAL_STATUS, TERMINAL_STATUS_MAP } from './constants';
import ShellMenu from './ShellMenu';
import CachedData from '~/cache/common';
import cls from 'classnames';

const US_REGION_IDS = ['us-west-1', 'us-east-1'];

const Webshell = (props) => {
  const {
    isFullSceen,
    regionId,
    appId,
    instanceId,
    containerId,
    tokenId,
    onClose,
    fullName,
    hideMenu,
    hideClose,
  } = props;

  const [refreshIndex, setRefreshIndex] = useState(0);
  const [status, setStatus] = useState(TERMINAL_STATUS.DISCONNECTED);
  const curStatus = TERMINAL_STATUS_MAP[status];

  const isPre = CachedData.isPre();
  const isIntl = CachedData.isSinSite();

  const onCloseClick = () => {
    onClose();
  }

  const getWsUrl = async ({ cols, rows }) => {
    if (tokenId) {
      const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
      const hostName = isIntl ?
        'sae-webshell-intl.console.alibabacloud.com' :
        (isPre ? 'pre-shanghai-' : '') +
          (US_REGION_IDS.includes(regionId) ?
          'saenext-webshell-us-west-1.console.aliyun.com' :
          'sae-webshell.console.aliyun.com');
      return `${protocol}${hostName}/websocket/eamWebshell?tokenId=${tokenId}&region=${regionId}`;
    }
    const { Data: { Token = '' } = {} } = await services.getWebshellToken({
      AppId: appId,
      PodName: instanceId,
      ContainerName: containerId,
      Lines: rows,
      Columns: cols,
    }) || {};

    if (!Token) {
      return '';
    }

    const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
    const hostName = isIntl ?
        'sae-webshell-intl.console.alibabacloud.com' :
        (isPre ? 'pre-shanghai-' : '') +
          (US_REGION_IDS.includes(regionId) ?
          'saenext-webshell-us-west-1.console.aliyun.com' :
          'sae-webshell.console.aliyun.com');

    let envParam = '';
    if (window.ALIYUN_CONSOLE_CONFIG?.fEnv === 'pre') {
    }

    const socketURL = `${protocol}${hostName}/websocket/eamWebshell?tokenId=${Token}&region=${regionId}${envParam}`;
    console.log("🚀 ~ socketURL:", socketURL)
    return socketURL;
  }

  const onOpenNewTab = () => {
    const containerStr = containerId ? `?container=${containerId}` : '';
    window.open(`/${regionId}/app-list/${appId}/micro-app/shell/${instanceId}${containerStr}`);
    onCloseClick();
  }

  return (
    <div className='terminal-wrapper'>
      <div className="terminal-menu">
        <div className="terminal-title">
          <Truncate style={{ display: 'inline-block' }} position="middle" threshold={fullName ? 800 : 200} type="width">
            {containerId || instanceId}
          </Truncate>
          <StatusIndicator
            shape="dot"
            type={curStatus['type']}
            className='ml'
            style={{ color: curStatus['color'] }}
          >
            <span>{status}</span>
          </StatusIndicator>
        </div>
        <div className={cls("flex", { none: hideMenu })}>
          <ShellMenu
            isFullSceen={isFullSceen}
            regionId={regionId}
            appId={appId}
            instanceId={instanceId}
            onOpenNewTab={onOpenNewTab}
            onRefresh={() => setRefreshIndex(refreshIndex + 1)}
            onClose={onCloseClick}
            hideClose={hideClose}
          />
        </div>
      </div>
      <div className="terminal-content">
        <Terminal
          refreshIndex={refreshIndex}
          getWsUrl={getWsUrl}
          onStatusChange={setStatus}
        />
      </div>
    </div>
  )
}

export default Webshell;