import React, { Component, createRef } from 'react';
import { XTerm } from 'xterm-for-react';
import { FitAddon } from 'xterm-addon-fit';
import ResizeObserver from 'react-resize-observer';
import { TERMINAL_STATUS } from './constants';

type Props = {
  getWsUrl: (params: { cols: number; rows: number }) => Promise<string>;
  onStatusChange?: (status: TERMINAL_STATUS) => void;
  refreshIndex?: number;
};

class Terminal extends Component<Props> {
  ref: any;
  ws: any;
  fitAddons: any;

  constructor(props) {
    super(props);
    this.fitAddons = new FitAddon();
    this.ref = createRef();
  }

  componentDidMount() {
    this.connect();
    this.fitAddons.fit();
  }

  componentDidUpdate(prevProps) {
    if (this.props.refreshIndex !== prevProps.refreshIndex) {
      this.ref.current.terminal.write('\n\r');
      this.connect();
    }
  }

  componentWillUnmount() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  async connect() {
    const { getWsUrl, onStatusChange } = this.props;
    const { cols = 20, rows = 50 } = this.fitAddons.proposeDimensions() || {};
    const wsUrl = await getWsUrl({
      cols,
      rows,
    });

    onStatusChange?.(TERMINAL_STATUS.CONNECTING);

    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      onStatusChange?.(TERMINAL_STATUS.CONNECTED);
      if (this.ref.current) {
        this.ref.current.terminal.focus();
      }
    };

    ws.onmessage = async (message) => {
      if (this.ref.current) {
        let text;
        if (typeof message.data === 'object') {
          text = await message.data.text();
        } else {
          text = message.data;
        }
        this.ref.current.terminal.write(text);
      }
    };

    ws.onerror = async (e) => {
      onStatusChange?.(TERMINAL_STATUS.DISCONNECTED);
    };

    ws.onclose = (e) => {
      onStatusChange?.(TERMINAL_STATUS.DISCONNECTED);
    };

    this.ws = ws;
  }

  onData = (data) => {
    if (this.ws) {
      this.ws.send(data);
    }
  };

  render() {
    return (
      <>
        <XTerm
          addons={[this.fitAddons]}
          ref={this.ref}
          onData={this.onData}
          options={{
            fontSize: 14,
            lineHeight: 1.1,
            fontFamily: '"Lucida Console", Monaco, "Courier New", monospace',
            theme: {
              background: '#2d2e2c',
            },
            cursorBlink: true,
          }}
        />
        <ResizeObserver
          onResize={() => {
            this.fitAddons.fit();
          }}
        />
      </>
    )
  }
}

export default Terminal