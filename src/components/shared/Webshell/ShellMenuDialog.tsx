import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Dialog } from '@ali/cnd';
import If from '~/components/shared/If';
import { ACTION_TYPE, ACTION_TYPE_MAP } from './constants';
import { delay } from '~/utils/global';
import ShellInstall from './ShellInstall';
import ShellDownload from './ShellDownload';
import ShellUpload from './ShellUpload';

const ShellMenuDialog = (props) => {
  const { type, setType, fileSizeLimit, regionId, appId, instanceId } = props;
  const curType = ACTION_TYPE_MAP[type] || {};

  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const ref = useRef(null);

  useEffect(() => {
    type && setVisible(true);
  }, [type]);

  const onOk = async () => {
    setLoading(true);
    const res = await ref.current?.onOk();
    setLoading(false);
    
    res && onClose();
  }

  const onClose = async () => {
    setVisible(false);
    await delay(1000);
    setType('');
  }

  return (
    <Dialog
      visible={visible}
      title={curType.title}
      onOk={onOk}
      onCancel={onClose}
      onClose={onClose}
      okProps={{ 
        loading,
        children: curType.text,
       }}
    >
      <If condition={type === ACTION_TYPE.INSTALL}>
        <ShellInstall
          ref={ref}
          regionId={regionId}
        />
      </If>
      <If condition={type === ACTION_TYPE.DOWNLOAD}>
        <ShellDownload
          ref={ref}
          appId={appId}
          instanceId={instanceId}
          fileSizeLimit={fileSizeLimit}
        />
      </If>
      <If condition={type === ACTION_TYPE.UPLOAD}>
        <ShellUpload
          ref={ref}
          appId={appId}
          instanceId={instanceId}
          fileSizeLimit={fileSizeLimit}
        />
      </If>
    </Dialog>
  )
}

export default ShellMenuDialog;