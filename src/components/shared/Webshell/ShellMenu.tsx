import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useState } from 'react';
import { Balloon, Button, Icon, Message } from '@ali/cnd';
import If from '~/components/shared/If';
import ShellMenuDialog from './ShellMenuDialog';
import { ACTION_TYPE } from './constants';
import services from '~/services';
import { get } from 'lodash';
import FeatureContext from '~/utils/featureContext';

const ShellMenu = (props) => {
  const { regionId, appId, instanceId, isFullSceen, onOpenNewTab, onRefresh, onClose, hideClose } = props;

  const [type, setType] = useState('');
  const [appStatus, setAppStatus] = useState<any>({});

  const { feature } = useContext(FeatureContext);
  const { saeAgent } = feature;

  useEffect(() => {
    getMicroAppStatus();
  }, []);

  const getMicroAppStatus = async () => {
    const data = await services.describeMicroApplicationStatus(
      {
        AppId: appId,
      },
      true,
    );
    const _appStatus = get(data, 'Data', {});
    setAppStatus(_appStatus);
  };

  const onOpenDialog = async (type: ACTION_TYPE) => {
    const { EnableAgent: enableAgent } = appStatus;
    if (!enableAgent) {
      Message.error(intl('saenext.shared.Webshell.ShellMenu.RestartTheApplicationAndTry'));
      return;
    }

    setType(type);
  };

  return (
    <>
      <If condition={saeAgent}>
        <Balloon
          trigger={
            <Button text iconSize="small">
              <Icon type="clouddownload" />
            </Button>
          }
          closable={false}
          align={isFullSceen ? 'b' : 't'}
        >
          <ul>
            <li>
              <Button
                type="normal"
                text
                style={{ display: 'block', width: 40, height: 32 }}
                onClick={() => onOpenDialog(ACTION_TYPE.UPLOAD)}
              >
                {intl('saenext.shared.Webshell.ShellMenu.Upload')}
              </Button>
            </li>
            <li>
              <Button
                type="normal"
                text
                style={{ display: 'block', width: 40, height: 32 }}
                onClick={() => onOpenDialog(ACTION_TYPE.DOWNLOAD)}
              >
                {intl('saenext.shared.Webshell.ShellMenu.Download')}
              </Button>
            </li>
          </ul>
        </Balloon>
        <Button text iconSize="small" onClick={() => onOpenDialog(ACTION_TYPE.INSTALL)}>
          <Icon type="inbox" />
        </Button>
      </If>
      <If condition={!isFullSceen}>
        <Button text iconSize="small" onClick={onOpenNewTab}>
          <Icon type="external_link" />
        </Button>
      </If>
      <Button text iconSize="small" onClick={onRefresh}>
        <Icon type="refresh" />
      </Button>
      <Button text iconSize="small" className={hideClose ? 'none' : ''} onClick={onClose}>
        <Icon type="close" />
      </Button>

      <ShellMenuDialog
        type={type}
        setType={setType}
        fileSizeLimit={appStatus.FileSizeLimit}
        regionId={regionId}
        appId={appId}
        instanceId={instanceId}
      />
    </>
  );
};

export default ShellMenu;
