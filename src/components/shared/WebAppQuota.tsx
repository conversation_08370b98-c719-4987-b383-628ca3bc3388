import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import services from '~/services';
import _ from 'lodash';
import { Message } from '@ali/cnd';

const WebAppQuota = (props) => {
  const { className = '', isCreate = false } = props;

  const [quota, setQuota] = useState<any>({});
  const { instanceLimit = 0, instanceUsed = 0 } = quota;

  useEffect(() => {
    getWebQuota();
  }, []);

  const getWebQuota = async () => {
    const data = ({} = await services.GetWebQuota({
      ignoreError: true,
    }));
    data && setQuota(data);
  };

  return (
    <div className={className}>
      {instanceLimit > instanceUsed && (
        <Message type="notice">
          {intl('saenext.components.shared.WebAppQuota.TheNumberOfRemainingInstances')}
          {instanceLimit - instanceUsed}
          {intl('saenext.components.shared.WebAppQuota.UsedInstances')}
          {instanceUsed}{intl('general.unit.count')}
          {intl("saenext.common.full-stop")}
        </Message>
      )}

      {!_.isEmpty(quota) && instanceLimit <= instanceUsed && (
        <Message type="warning">
          {intl('saenext.components.shared.WebAppQuota.TheNumberOfInstancesYou')}
          {instanceLimit}
          {intl('saenext.components.shared.WebAppQuota.PleaseJoinDingtalkGroupTo')}
          {isCreate && intl('saenext.components.shared.WebAppQuota.IfYouContinueToCreate')}
        </Message>
      )}
    </div>
  );
};

export default WebAppQuota;
