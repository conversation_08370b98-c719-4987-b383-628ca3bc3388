import { intl } from '@ali/cnd';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo } from 'react';
import { Checkbox, Field, NumberPicker, Radio } from '@ali/cnd';
import If from '~/components/shared/If';
import _, { isNil } from 'lodash';

const MinReadyInstance = (props, ref) => {
  const { value = {}, onChange = () => {}, appConfig = {}, appStatus = {} } = props;

  const field = Field.useField();
  const { init, getValues } = field;

  const { minInstancesType, minReadyInstanceRatio, useDefault } = getValues() as any;

  const minInstancesNum = Math.ceil(appConfig.Replicas * (minReadyInstanceRatio / 100));

  useEffect(() => {
    initValue();
  }, [value]);

  const isValid = useMemo(() => {
    const { MinReadyInstances, MinReadyInstanceRatio } = value;
    return _.isNumber(MinReadyInstances) && _.isNumber(MinReadyInstanceRatio);
  }, [value]);

  useImperativeHandle(ref, () => ({
    validate,
  }));

  const initValue = () => {
    const { MinReadyInstances, MinReadyInstanceRatio } = value;
    let fieldValues = {};
    if (MinReadyInstances === -1 && MinReadyInstanceRatio === -1) {
      fieldValues = {
        minInstancesType: 'byRatio',
        minReadyInstances: null,
        minReadyInstanceRatio: 50,
        useDefault: true,
      };
    } else {
      const minInstancesType = MinReadyInstanceRatio !== -1 ? 'byRatio' : 'byNum';
      fieldValues = {
        minInstancesType,
        minReadyInstances: minInstancesType === 'byNum' ? MinReadyInstances : null,
        minReadyInstanceRatio: minInstancesType === 'byRatio' ? MinReadyInstanceRatio : null,
        useDefault: false,
      };
    }
    field.setValues(fieldValues);
  };

  const handleTypeChange = (value) => {
    if (value === 'byNum') {
      const MinReadyInstances = Math.ceil((appConfig.Replicas * 50) / 100);
      onChange({
        MinReadyInstances,
        MinReadyInstanceRatio: -1,
      });
    } else {
      onChange({
        MinReadyInstances: -1,
        MinReadyInstanceRatio: -1,
      });
    }
  };

  const handleNumChange = (value) => {
    onChange({
      MinReadyInstances: isNil(value) ? null : Number(value),
      MinReadyInstanceRatio: -1,
    });
  };

  const handleRatioChange = (value) => {
    onChange({
      MinReadyInstances: -1,
      MinReadyInstanceRatio: isNil(value) ? null : Number(value),
    });
  };

  const handleDefaultChange = (value) => {
    if (value) {
      onChange({
        MinReadyInstances: -1,
        MinReadyInstanceRatio: -1,
      });
    } else {
      onChange({
        MinReadyInstances: -1,
        MinReadyInstanceRatio: 50,
      });
    }
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((errors, values) => {
        if (errors) {
          reject(errors);
        } else {
          resolve(true);
        }
      });
    });
  };

  return (
    <>
      <div className="flex" style={{ height: 70 }}>
        <Radio.Group {...init('minInstancesType')} itemDirection="ver" onChange={handleTypeChange}>
          <Radio id="byNum" value="byNum" style={{ margin: '0px 0px 8px', lineHeight: '32px' }}>
            {intl('saenext.shared.MinReadyInstance.ByNumber')}
          </Radio>
          <Radio id="byRatio" value="byRatio" style={{ margin: 0, lineHeight: '32px' }}>
            {intl('saenext.shared.MinReadyInstance.Proportional')}
          </Radio>
        </Radio.Group>

        <div className="ml-xl">
          <NumberPicker
            {...init('minReadyInstances', {
              rules: [
                {
                  required: minInstancesType === 'byNum',
                  message: intl('saenext.shared.MinReadyInstance.EnterTheMinimumNumberOf'),
                },
              ],
            })}
            min={0}
            max={appStatus.RunningInstances}
            disabled={useDefault || minInstancesType === 'byRatio'}
            // @ts-ignore
            addonTextAfter={intl("general.unit.count")}
            style={{ width: 150 }}
            onChange={handleNumChange}
          />

          <br />

          <NumberPicker
            name="minReadyInstanceRatio"
            {...init('minReadyInstanceRatio', {
              rules: [
                {
                  required: minInstancesType === 'byRatio',
                  message: intl('saenext.shared.MinReadyInstance.EnterTheMinimumNumberOf'),
                },
              ],
            })}
            min={0}
            max={100}
            disabled={useDefault || minInstancesType === 'byNum'}
            // @ts-ignore
            addonTextAfter="%"
            style={{ width: 150, marginTop: 8 }}
            onChange={handleRatioChange}
          />

          {/* @ts-ignore */}
          <Checkbox
            // name="useDefault"
            {...init('useDefault', {
              valueName: 'checked',
            })}
            onChange={handleDefaultChange}
            className="ml-l"
            disabled={minInstancesType === 'byNum'}
          >
            {intl('saenext.shared.MinReadyInstance.UseTheSystemRecommendValue')}
          </Checkbox>

          <If condition={minInstancesType === 'byRatio' && !!minInstancesNum}>
            <span className="ml-l">
              {minInstancesNum}
              {intl('saenext.shared.MinReadyInstance.Instances')}
            </span>
          </If>
        </div>
      </div>

      <If condition={!isValid}>
        <span className="text-warning">
          {intl('saenext.shared.MinReadyInstance.EnterTheMinimumNumberOf')}
        </span>
      </If>
    </>
  );
};

export default forwardRef(MinReadyInstance);
