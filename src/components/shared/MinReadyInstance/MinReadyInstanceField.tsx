import React, { useContext, useEffect, useState } from 'react';
import { Input } from '@ali/cnd';
import MinReadyInstance from '.';
import MicroAppContext from '~/utils/microAppContext';
import If from '~/components/shared/If';


const MinReadyInstanceField = (props) => {
  const {
    field,
    countName = 'MinReadyInstances',
    ratioName = 'MinReadyInstanceRatio',
  } = props;

  const { appConfig, appStatus } = useContext(MicroAppContext);
  const {
    MinReadyInstances,
    MinReadyInstanceRatio,
  } = appConfig;

  const {
    init,
    getError,
  } = field;

  const [value, setValue] = useState({
    MinReadyInstances,
    MinReadyInstanceRatio,
  });

  const minReadyInstances = field.getValue(countName);
  const minReadyInstanceRatio = field.getValue(ratioName);

  useEffect(() => {
    if (!minReadyInstances && !minReadyInstanceRatio) {
      return;
    }
    setValue({
      MinReadyInstances: minReadyInstances,
      MinReadyInstanceRatio: minReadyInstanceRatio,
    });
  }, [minReadyInstances, minReadyInstanceRatio])

  const hasErrors = getError(countName) || getError(ratioName);

  return (
    <>
      <MinReadyInstance
        value={value}
        onChange={(val) => {
          field.setValue(countName, val.MinReadyInstances);
          field.setValue(ratioName, val.MinReadyInstanceRatio);
          field.validate('ScalingRuleMetric.minReplicas');
        }}
        appConfig={appConfig}
        appStatus={appStatus}
      />
      <div className="none">
        <Input {...init(countName, {
          initValue: MinReadyInstances,
          rules: [
            {
              required: true,
            },
          ],
        })} />
        <Input {...init(ratioName, {
          initValue: MinReadyInstanceRatio,
          rules: [
            {
              required: true,
            },
          ],
        })} />
      </div>
    </>
  )
}

export default MinReadyInstanceField;