import React, { useState, useEffect, useImperativeHandle, useMemo, forwardRef } from 'react';
import {
  intl,
  Form,
  Field,
  Checkbox,
  Balloon,
  Icon,
  NumberPicker,
  Dialog,
  ToolTipCondition,
  CndSwitch,
} from '@ali/cnd';
import { has, isEmpty, get, set } from 'lodash';
import services from '~/services';
import CachedData from '~/cache/common';
import { compareVersions } from '~/utils/global';
import { confFeature } from '@alicloud/console-one-conf';

const formItemLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const mseVersionMap = new Map()
  .set(0, intl('saenext.components.msc.FetchMseUse.BasicEdition'))
  .set(1, intl('saenext.components.msc.FetchMseUse.ProfessionalEdition'))
  .set(2, intl('saenext.components.msc.FetchMseUse.EnterpriseEdition'))
  .set(3, intl('saenext.components.msc.FetchMseUse.NotActivated'))
  .set(4, intl('saenext.components.msc.FetchMseUse.TrialVersion'))
  .set(5, intl('saenext.components.msc.FetchMseUse.TrialVersion'));

const MicroServceConfig = (props, ref) => {
  const {
    appConfig,
    value,
    onChange,
    isPreview,
    baseAppId,
    hasGrayApp,
    NewSaeVersion,
    hasMicroService,
    agentVersion,
  } = props;
  const field = Field.useField();
  const [microServiceEnable, setMicroServiceEnable] = useState(false);
  const [mseLosslessRule, setMseLosslessRule] = useState<any>({
    enable: false,
    notice: false,
    delayTime: 0,
    warmupTime: 120,
  });
  const [delayEnable, setDelayEnable] = useState(false); // 延时注册开启（从0到大于0）关闭（从大于0到0），这两种情况需用户弹窗确认
  const [preDelayTime, setPreDelayTime] = useState(0); // 延时注册开启/关闭 用户确认时 点击取消时需恢复至上一个值

  const [mseUserStatus, setMseUserStatus] = useState<any>({});
  const [mscVersion, setMscVersion] = useState<number>();

  const userId =
    get(window, `ALIYUN_CONSOLE_CONFIG.CURRENT_PK`) ||
    get(window, `ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK`);
  const isIntl = CachedData.isSinSite();

  const ignoreAgentVersionValidate = confFeature('ignoreAgentVersionValidate');

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [value],
  );

  useEffect(() => {
    getMseUserStatus();
  }, []);

  useEffect(() => {
    // 数据回显
    if (appConfig?.MicroserviceEngineConfig) {
      initConfig();
    }
  }, [appConfig?.MicroserviceEngineConfig]);

  useEffect(() => {
    onChange({
      microServiceEnable: microServiceEnable,
      mseLosslessRule: mseLosslessRule,
      delayEnable: delayEnable,
    });
  }, [microServiceEnable, mseLosslessRule, delayEnable]);

  const initConfig = () => {
    const { enable = false, mseLosslessRule = {} } =
      JSON.parse(appConfig?.MicroserviceEngineConfig) || {};
    const MseLosslessRule = mseLosslessRule || {};
    setMicroServiceEnable(enable);
    setMseLosslessRule({ ...MseLosslessRule });
    setDelayEnable(MseLosslessRule?.delayTime > 0);
    setPreDelayTime(MseLosslessRule.delayTime);
  };

  const getMseUserStatus = async () => {
    const { Data } =
      (await services.getMseUserStatus({
        ignoreError: true,
      })) || {};
    // Status    是否开通  1 未开通、2 开通
    // Version   开通版本  0 基础版、 1 专业版、 2 企业版
    // FreeVersion  是否开通试用版  0 未开通、1 开通试用版、 2 试用版到期
    const { Status = 1, Version = 0, FreeVersion = 0 } = Data;
    let version = 3;
    if (Status === 2 && FreeVersion !== 1) {
      version = Version;
    } else {
      version = FreeVersion === 0 ? 3 : FreeVersion + 3;
    }
    setMscVersion(version);
    setMseUserStatus(Data);
  };

  const handleDelayTimeBlur = () => {
    let msg = '';
    let _delayEnable = false;
    const readinessFieldValues =
      (props.readinessFieldValues && JSON.parse(props.readinessFieldValues)) || {};
    let new_readiness = isEmpty(readinessFieldValues) ? {} : readinessFieldValues;
    if (preDelayTime === 0 && mseLosslessRule?.delayTime > 0 && !delayEnable) {
      msg = intl(
        'saenext.shared.MicroServiceConfigField.MicroServiceConfig.IfTheDelayRegistrationPeriod.new',
      );
      _delayEnable = true;
      set(new_readiness, 'httpGet.path', '/readiness');
      set(new_readiness, 'httpGet.port', 54199);
    }
    if (preDelayTime > 0 && mseLosslessRule?.delayTime === 0 && delayEnable) {
      msg = intl(
        'saenext.shared.MicroServiceConfigField.MicroServiceConfig.WhenTheDelayRegistrationPeriod',
      );
      _delayEnable = false;
      new_readiness = {};
    }
    if (msg) {
      Dialog.confirm({
        title: intl(
          'saenext.shared.MicroServiceConfigField.MicroServiceConfig.ConfigureDelayedRegistrationDuration',
        ),
        content: msg,
        onOk: () => {
          setPreDelayTime(mseLosslessRule?.delayTime);
          setDelayEnable(_delayEnable);
          props?.onChangeReadniess(JSON.stringify(new_readiness));
        },
        onCancel: () => {
          setMseLosslessRule({
            ...mseLosslessRule,
            delayTime: preDelayTime,
          });
        },
        closeable: false,
      });
    } else {
      setPreDelayTime(mseLosslessRule?.delayTime);
    }
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((error, value) => {
        // 非专业版应用
        if (
          microServiceEnable &&
          hasMicroService &&
          NewSaeVersion !== 'pro' &&
          mscVersion !== undefined
        ) {
          if (mscVersion !== 1 && mscVersion !== 2 && mscVersion !== 4) {
            resolve(' ');
          }
        }
        error ? resolve(' ') : resolve(true);
      });
    });
  };

  const mseUserStatusInfo = useMemo(() => {
    const { Status = '', Version = '', FreeVersion = '' } = mseUserStatus;
    let versionLabel = '';
    if (Status === 2 && Version !== 2) {
      if (Version === 0) {
        versionLabel = intl(
          'saenext.shared.MicroServiceConfigField.MicroServiceConfig.BasicEdition',
        );
      } else if (Version === 1) {
        versionLabel = intl(
          'saenext.shared.MicroServiceConfigField.MicroServiceConfig.ProfessionalEdition',
        );
      } else {
        if (FreeVersion !== 0) {
          versionLabel = intl(
            'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TrialVersion',
          );
        }
      }
    }
    const uri = `${CachedData.confLink('feature:common-buy')}/?commodityCode=${
      isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'
    }&orderType=UPGRADE&instanceId=synthetic_post_${userId}`;

    return {
      msg: (
        <div>
          <div>
            {intl(
              'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheCurrentMseMicroserviceGovernance',
            )}
            {versionLabel}
            {intl(
              'saenext.shared.MicroServiceConfigField.MicroServiceConfig.MseMicroserviceGovernanceIsRequired',
            )}
            <a href={uri} target="_blank">
              {intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.Upgrade')}
            </a>
            {intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.ItCanOnlyBeEnabled')}
          </div>
          <div style={{ color: '#FFA003' }}>
            {Version === 1
              ? intl(
                  'saenext.shared.MicroServiceConfigField.MicroServiceConfig.NoteAfterUpgradingToEnterprise',
                )
              : intl(
                  'saenext.shared.MicroServiceConfigField.MicroServiceConfig.NoteAfterUpgradingToEnterprise.1',
                )}
          </div>
        </div>
      ),

      disabled: versionLabel ? true : false,
    };
  }, [mseUserStatus]);

  const handleDisableMsg = () => {
    if (baseAppId) {
      return intl(
        'saenext.shared.MicroServiceConfigField.MicroServiceConfig.GrayscaleApplicationsCannotBeModified',
      );
    }
    if (hasGrayApp && microServiceEnable) {
      return intl(
        'saenext.shared.MicroServiceConfigField.MicroServiceConfig.GrayscaleApplicationsExistAndCannot',
      );
    }
    if (NewSaeVersion === 'pro' && !ignoreAgentVersionValidate && agentVersion && compareVersions(agentVersion, '4.3.0') < 0) {
      return (
        <>
          {/* {intl(
            'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheCurrentAgentVersionDoes',
          )}{' '}
          <a
            href={`${CachedData.confLink('feature:smartservice:url')}/service/create-ticket`}
            target="_blank"
          >
            {intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.SubmitATicket')}
          </a>{' '}
          {intl(
            'saenext.shared.MicroServiceConfigField.MicroServiceConfig.ContactTheServerlessApplicationEngine',
          )} */}
          {intl.html('saenext.shared.MicroServiceConfigField.MicroServiceConfig.AgentVersion.tips',{
            href: `${CachedData.confLink('feature:smartservice:url')}/service/create-ticket`,
          })}
        </>
      );
    }
    return mseUserStatusInfo.msg;
  };

  const handleStdMscVersionValidate = () => {
    if (mscVersion !== 1 && mscVersion !== 2 && mscVersion !== 4) {
      return (
        <div style={{ color: 'red' }}>
          {mscVersion === 3 && (
            <div>
              {intl(
                'saenext.shared.MicroServiceConfigField.MicroServiceConfig.CurrentlyMseMicroserviceGovernanceIs',
              )}

              <a
                href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=${
                  isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'
                }`}
                target="_blank"
                className="mr-xs  ml-xs"
              >
                {intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.Activate')}
              </a>
              {intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.GoToTheMsePurchase')}
            </div>
          )}
          {mscVersion === 0 && (
            <div>
              {intl(
                'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheCurrentMseMicroserviceGovernance',
              )}
              {mseVersionMap.get(mscVersion)}
              {intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.PleaseClick')}
              <a
                href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=${
                  isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'
                }&orderType=UPGRADE&instanceId=synthetic_post_${userId}`}
                target="_blank"
                className="mr-xs  ml-xs"
              >
                {intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.Upgrade')}
              </a>
              {intl(
                'saenext.shared.MicroServiceConfigField.MicroServiceConfig.GoToTheMsePurchase.1',
              )}
            </div>
          )}
          {mscVersion === 5 && (
            <div>
              {intl(
                'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheCurrentMseTrialVersion',
              )}

              <a
                href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=${
                  isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'
                }`}
                target="_blank"
                className="mr-xs  ml-xs"
              >
                {intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.Activate')}
              </a>
              {intl(
                'saenext.shared.MicroServiceConfigField.MicroServiceConfig.GoToTheMsePurchase.2',
              )}
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <Form {...(formItemLayout as any)} field={field} id="micro-service-config">
      <Form.Item
        label={intl(
          'saenext.shared.MicroServiceConfigField.MicroServiceConfig.MicroserviceGovernance',
        )}
        help={
          microServiceEnable
            ? NewSaeVersion === 'pro'
              ? intl(
                  'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheLosslessOfflineAndOffline',
                )
              : null
            : intl(
                'saenext.shared.MicroServiceConfigField.MicroServiceConfig.WhenEnabledTheSystemInjects',
              )
        }
      >
        {!isPreview ? (
          <ToolTipCondition
            show={
              (hasGrayApp && microServiceEnable) ||
              baseAppId ||
              (NewSaeVersion === 'pro' &&
                (mseUserStatusInfo.disabled ||
                  (!ignoreAgentVersionValidate && agentVersion && compareVersions(agentVersion, '4.3.0') < 0)))
            }
            tip={handleDisableMsg()}
            align="t"
            popupStyle={{ maxWidth: 480 }}
          >
            <CndSwitch
              checked={microServiceEnable}
              disabled={
                (hasGrayApp && microServiceEnable) ||
                baseAppId ||
                (NewSaeVersion === 'pro' &&
                  (mseUserStatusInfo.disabled ||
                    (!ignoreAgentVersionValidate && agentVersion && compareVersions(agentVersion, '4.3.0') < 0)))
              }
              onChange={(val) => {
                setMicroServiceEnable(val);
                if (!val) {
                  setMseLosslessRule({
                    enable: false,
                    notice: false,
                    delayTime: 0,
                    warmupTime: 120,
                  });
                  setDelayEnable(false);
                  setPreDelayTime(0);
                  props?.onChangeReadniess('{}');
                }
              }}
              // suffix={
              //   microServiceEnable
              //     ? NewSaeVersion === 'pro'
              //       ? intl(
              //           'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheLosslessOfflineAndOffline',
              //         )
              //       : null
              //     : intl(
              //         'saenext.shared.MicroServiceConfigField.MicroServiceConfig.WhenEnabledTheSystemInjects',
              //       )
              // }
            />
          </ToolTipCondition>
        ) : microServiceEnable ? (
          intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.Enabled')
        ) : (
          intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.NotEnabled')
        )}
      </Form.Item>
      {microServiceEnable && hasMicroService && NewSaeVersion !== 'pro' && (
        <Form.Item
          label={intl(
            'saenext.shared.MicroServiceConfigField.MicroServiceConfig.MseMicroserviceGovernanceVersion',
          )}
          extra={handleStdMscVersionValidate()}
        >
          <div>{mseVersionMap.get(mscVersion)}</div>
        </Form.Item>
      )}
      {microServiceEnable && (
        <Form.Item label=" ">
          {!isPreview && hasMicroService && NewSaeVersion !== 'pro' && (
            <div className="mb-s">
              {intl(
                'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheLosslessOfflineAndOffline',
              )}
            </div>
          )}
          <div style={{ background: '#f1f1f1', padding: 8 }}>
            <Form.Item
              label={
                <>
                  {intl(
                    'saenext.shared.MicroServiceConfigField.MicroServiceConfig.LosslessOffline',
                  )}

                  <Balloon
                    align="t"
                    trigger={
                      <Icon size="xs" type="info" className="ml-xs" style={{ color: '#888' }} />
                    }
                    closable={false}
                  >
                    {intl(
                      'saenext.shared.MicroServiceConfigField.MicroServiceConfig.InScenariosSuchAsService',
                    )}
                  </Balloon>
                </>
              }
              labelCol={{ span: 3 }}
              labelTextAlign="left"
            >
              {!isPreview ? (
                <div>
                  <div style={{ lineHeight: '32px' }}>
                    {intl(
                      'saenext.shared.MicroServiceConfigField.MicroServiceConfig.ValidByDefault',
                    )}
                  </div>
                  <div>
                    <Checkbox
                      checked={mseLosslessRule?.notice}
                      onChange={(checked) => {
                        setMseLosslessRule({
                          ...mseLosslessRule,
                          notice: checked,
                        });
                      }}
                      disabled={baseAppId}
                    >
                      {intl(
                        'saenext.shared.MicroServiceConfigField.MicroServiceConfig.SendNotification',
                      )}

                      <Balloon
                        align="r"
                        trigger={
                          <Icon size="xs" type="info" className="ml-xs" style={{ color: '#888' }} />
                        }
                        closable={false}
                      >
                        {intl(
                          'saenext.shared.MicroServiceConfigField.MicroServiceConfig.AfterTheNodeIsEnabled',
                        )}

                        <a
                          target="_blank"
                          href={`${CachedData.confLink(
                            'help:mse:lossless-start-and-shutdown-faq',
                          )}?spm=a2c4g.11186623.0.0.5509439fKCH9R7#16494d99fe1qj`}
                        >
                          {intl(
                            'saenext.shared.MicroServiceConfigField.MicroServiceConfig.LearnMore',
                          )}
                        </a>
                      </Balloon>
                    </Checkbox>
                  </div>
                </div>
              ) : (
                <div>
                  <div>
                    {intl(
                      'saenext.shared.MicroServiceConfigField.MicroServiceConfig.ValidByDefault',
                    )}
                  </div>
                  <div>{`${intl(
                    'saenext.shared.MicroServiceConfigField.MicroServiceConfig.notice.label',
                  )} ${
                    mseLosslessRule?.notice
                      ? intl(
                          'saenext.shared.MicroServiceConfigField.MicroServiceConfig.notice.enable',
                        )
                      : intl(
                          'saenext.shared.MicroServiceConfigField.MicroServiceConfig.notice.disable',
                        )
                  }`}</div>
                </div>
              )}
            </Form.Item>
            <Form.Item
              label={intl(
                'saenext.shared.MicroServiceConfigField.MicroServiceConfig.LosslessLaunch',
              )}
              labelCol={{ span: 3 }}
              labelTextAlign="left"
              validator={(rule, value, callback: any) => {
                if (
                  mseLosslessRule?.enable &&
                  !(mseLosslessRule?.warmupTime || mseLosslessRule?.warmupTime === 0)
                ) {
                  callback(
                    intl(
                      'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheWarmUpPeriodFor',
                    ),
                  );
                }
                if (
                  mseLosslessRule?.enable &&
                  !(mseLosslessRule?.delayTime || mseLosslessRule?.delayTime === 0)
                ) {
                  callback(
                    intl(
                      'saenext.shared.MicroServiceConfigField.MicroServiceConfig.TheDelayRegistrationPeriodCannot',
                    ),
                  );
                } else {
                  callback();
                }
              }}
            >
              {!isPreview ? (
                <CndSwitch
                  name="mseLosslessRule.enable"
                  checked={mseLosslessRule?.enable}
                  disabled={baseAppId}
                  onChange={(v) => {
                    setMseLosslessRule({
                      ...mseLosslessRule,
                      enable: v,
                      warmupTime: 120,
                      delayTime: 0,
                    });
                    if (!v) {
                      setDelayEnable(false);
                      setPreDelayTime(0);
                      props?.onChangeReadniess('{}');
                    }
                  }}
                  suffix={
                    <div>
                      {intl(
                        'saenext.shared.MicroServiceConfigField.MicroServiceConfig.AfterTheApplicationIsEnabled',
                      )}

                      <a
                        href={CachedData.confLink('help:sae:configure-graceful-start-and-shutdown')}
                        target="_blank"
                      >
                        {intl(
                          'saenext.shared.MicroServiceConfigField.MicroServiceConfig.ViewConfigurationInstructions',
                        )}
                      </a>
                    </div>
                  }
                />
              ) : mseLosslessRule?.enable ? (
                intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.Enabled')
              ) : (
                intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.NotEnabled')
              )}
              {mseLosslessRule?.enable && (
                <div>
                  {!isPreview ? (
                    <NumberPicker
                      // @ts-ignore
                      name="warmupTime"
                      value={mseLosslessRule?.warmupTime}
                      innerBefore={
                        <span style={{ paddingLeft: 8 }}>
                          {intl(
                            'saenext.shared.MicroServiceConfigField.MicroServiceConfig.SmallFlowPreheatingDuration',
                          )}
                        </span>
                      }
                      innerAfter={intl(
                        'saenext.shared.MicroServiceConfigField.MicroServiceConfig.Seconds',
                      )}
                      style={{ width: 220, marginTop: 8, marginRight: 8 }}
                      min={0}
                      max={24 * 60 * 60}
                      disabled={baseAppId}
                      onChange={(v: number) => {
                        setMseLosslessRule({
                          ...mseLosslessRule,
                          warmupTime: v,
                        });
                      }}
                      state={
                        mseLosslessRule?.warmupTime || mseLosslessRule?.warmupTime === 0
                          ? 'success'
                          : 'error'
                      }
                    />
                  ) : (
                    <span className="mr-xl">{`${intl(
                      'saenext.shared.MicroServiceConfigField.MicroServiceConfig.warmupTime.label',
                    )}${mseLosslessRule?.warmupTime}${intl('saenext.common.unit.second')}`}</span>
                  )}
                  {!isPreview ? (
                    <NumberPicker
                      // @ts-ignore
                      name="delayTime"
                      value={mseLosslessRule?.delayTime}
                      innerBefore={
                        <span style={{ paddingLeft: 8 }}>
                          {intl(
                            'saenext.shared.MicroServiceConfigField.MicroServiceConfig.DelayedRegistration',
                          )}
                        </span>
                      }
                      innerAfter={intl(
                        'saenext.shared.MicroServiceConfigField.MicroServiceConfig.Seconds',
                      )}
                      style={{ width: 220, marginTop: 8 }}
                      min={0}
                      max={1800}
                      disabled={baseAppId}
                      onChange={(v: number) => {
                        setMseLosslessRule({
                          ...mseLosslessRule,
                          delayTime: v,
                        });
                      }}
                      onBlur={handleDelayTimeBlur}
                      state={
                        mseLosslessRule?.delayTime || mseLosslessRule?.delayTime === 0
                          ? 'success'
                          : 'error'
                      }
                    />
                  ) : (
                    `${intl(
                      'saenext.shared.MicroServiceConfigField.MicroServiceConfig.delayTime.label',
                    )}${mseLosslessRule?.delayTime}${intl('saenext.common.unit.second')}`
                  )}
                </div>
              )}
            </Form.Item>
          </div>
        </Form.Item>
      )}
    </Form>
  );
};

export default forwardRef(MicroServceConfig);
