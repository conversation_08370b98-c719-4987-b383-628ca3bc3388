import React, { Component } from 'react';
import MicroServiceConfig from './MicroServiceConfig';
import <PERSON>Field from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';
import _, { isObject } from 'lodash';
import { jsonParse } from '~/utils/transfer-data';

type Props = {
  value?: Object;
  onChange?: Function;
};

class MicroServiceConfigFieldClass extends Component<Props> {
  private ref = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.ref.validate();
  };

  render() {
    return <MicroServiceConfig {...this.props} ref={ref => (this.ref = ref)} />;
  }
}

class MicroServiceConfigField extends BaseField {
  props;
  static displayName = 'MicroServiceConfigField';

  formatValueIn(value, props) {
    if (isObject(value)) {
      return value;
    }
    const valueObj = jsonParse(value) || {};
    return valueObj;
  }

  formatValueOut(value = {} as any, props) {
    const { microServiceEnable = false, mseLosslessRule={}, ...restValue } = value;
    const result = {
      Enable: microServiceEnable,
      MseLosslessRule:{
        enable: false,
        notice: false,
        delayTime: 0,
        warmupTime: 120,
        funcType: 2,
        aligned: false,
        related: false,
        ...mseLosslessRule,
      },
      ...restValue,
    };
    return JSON.stringify(result);
  }

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <MicroServiceConfigFieldClass {...newProps} />;
  }
}

export default ConfigProvider.config(MicroServiceConfigField as any);
