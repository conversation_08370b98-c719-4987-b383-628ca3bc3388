import React, { Component } from 'react';
import PrometheusMonitorConfig from './PrometheusMonitorConfig';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';
import _, { isObject } from 'lodash';
import { jsonParse } from '~/utils/transfer-data';

type Props = {
  value?: Object;
  onChange?: Function;
};

class PrometheusMonitorFieldClass extends Component<Props> {
  private ref = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.ref.validate();
  };

  render() {
    return <PrometheusMonitorConfig {...this.props} ref={ref => (this.ref = ref)} />;
  }
}

class PrometheusMonitorField extends BaseField {
  props;
  static displayName = 'PrometheusMonitorField';

  formatValueIn(value, props) {
    if (isObject(value)) {
      return value;
    }
    const valueObj = jsonParse(value) || {};
    return valueObj;
  }

  formatValueOut(value: Record<string, any> | undefined = {}, props): Record<string, any> {
    if (value === undefined || value === null) {
      return {};
    }
    return value;
  }

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <PrometheusMonitorFieldClass {...newProps} />;
  }
}

export default ConfigProvider.config(PrometheusMonitorField as any);
