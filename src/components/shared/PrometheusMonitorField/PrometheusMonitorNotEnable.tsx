import React from 'react';
import { intl, Button } from '@ali/cnd';
import PrometheusMonitorDialog from '~/components/shared/PrometheusMonitorField/PrometheusMonitorDialog';
const PrometheusMonitorNotEnable = props => {
  const { appConfig, appAddonReleaseInfo, refresh, regionId, history, hasCmsPermission } = props;

  return (
    <div
      style={{
        overflow: 'auto',
      }}
    >
      <div style={{ fontSize: '14px', fontWeight: 'bold', lineHeight: '24px', marginBottom: 10 }}>
        {intl(
          'saenext.shared.PrometheusMonitorField.PrometheusMonitorNotEnable.WelcomeToPrometheusMonitoring',
        )}
      </div>
      <div style={{ lineHeight: '20px' }}>
        {intl(
          'saenext.shared.PrometheusMonitorField.PrometheusMonitorNotEnable.ByDefaultServerlessApplicationEngine',
        )}
      </div>
      <div className="mt-l mb-l">
        <PrometheusMonitorDialog
          appConfig={appConfig}
          appAddonReleaseInfo={appAddonReleaseInfo}
          refresh={refresh}
          fromPage="prometheus"
          regionId={regionId}
          history={history}
          hasCmsPermission={hasCmsPermission}
        >
          <Button type="primary">
            {intl(
              'saenext.shared.PrometheusMonitorField.PrometheusMonitorNotEnable.ConfigurePrometheusMonitoring',
            )}
          </Button>
        </PrometheusMonitorDialog>
      </div>
      <div>
        <div
          style={{
            width: '100%',
            paddingTop: '58.42%', // 假设图片比例为 16:9，可以根据实际需求调整
            border: '1px solid #e5e5e5',
            borderRadius: '4px',
            backgroundImage: `url(https://img.alicdn.com/imgextra/i1/O1CN01y6DEab1c3CjFjsOtm_!!6000000003544-2-tps-2054-1200.png)`,
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
          }}
        ></div>
      </div>
    </div>
  );
};

export default PrometheusMonitorNotEnable;
