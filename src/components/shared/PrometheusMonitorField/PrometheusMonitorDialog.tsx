import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { intl, Dialog, Form, Field, Message } from '@ali/cnd';
import services from '~/services';
import { get, isEmpty } from 'lodash';
import CachedData from '~/cache/common';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import PrometheusMonitorField from '~/components/shared/PrometheusMonitorField';

// 定义 Prometheus 配置接口
interface PrometheusConfig {
  EnablePrometheus?: boolean;
  interval?: string;
  port?: string;
  metricPath?: string;
}
const PrometheusMonitorDialog = (props, ref) => {
  const {
    children,
    appConfig,
    appAddonReleaseInfo,
    refresh = () => {},
    fromPage = '',
    regionId,
    history,
    hasCmsPermission,
  } = props;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const field = Field.useField();
  const prometheusMonitorRef = useRef(null);

  useEffect(() => {
    if (
      !isEmpty(appAddonReleaseInfo) &&
      appAddonReleaseInfo?.enablePrometheus &&
      !isEmpty(appAddonReleaseInfo.config)
    ) {
      field.setValues({
        PrometheusMonitorConfig: {
          EnablePrometheus: appAddonReleaseInfo?.enablePrometheus,
          ...appAddonReleaseInfo.config,
        },
      });
    }
  }, [JSON.stringify(appAddonReleaseInfo)]);

  useImperativeHandle(ref, () => ({
    toogleVisible,
  }));
  const handleSettingPrometheusConfig = () => {
    field.validate(async (error, values) => {
      if (!error) {
        // 使用类型断言来明确配置结构，并添加默认空对象防止解构错误
        const config = (values?.PrometheusMonitorConfig as PrometheusConfig) || {};
        const { EnablePrometheus = false, interval = '', port = '', metricPath = '' } = config;
        const _enable = EnablePrometheus;
        const _config = {
          interval,
          port,
          metricPath,
        };
        setLoading(true);
        try {
          await handlePrometheusMonitor(_enable, _config);
          if (fromPage === 'prometheus') {
            const search = window?.location?.search;
            history.push(`/${regionId}/app-list/${appConfig?.AppId}/micro-app/base${search}`);
            setTimeout(async () => {
              const policyId = get(appAddonReleaseInfo, 'policyId', '');
              const storageRes = await services.ListIntegrationPolicyStorageRequirements({
                params: {
                  policyId,
                  addonName: 'cloud-sae-custom',
                  addonReleaseName: `sae-custom-${appConfig?.AppId}`,
                },
              });
              const clusterId = get(storageRes, 'storageRequirements[0].status.instanceId', '');
              if (clusterId) {
                const prometheusUrl = `${CachedData.confLink(
                  'feature:cms:url',
                )}/prom/instances/details?clusterId=${clusterId}&regionId=${
                  props.regionId
                }&path=metric-explorer`;

                window.open(prometheusUrl, '_blank');
              }
            }, 1000);
          }
          setLoading(false);
          toogleVisible();
          refresh();
        } catch (error) {
          console.error('Error handling Prometheus monitor:', error);
        }
      }
    });
  };
  // prometheus监控采集指标处理
  const handlePrometheusMonitor = async (enable, config) => {
    const policyId = get(appAddonReleaseInfo, 'policyId', '');
    const addonVersion = get(appAddonReleaseInfo, 'addonVersion', '');
    const originEnable = get(appAddonReleaseInfo, 'enablePrometheus', false);
    // 关闭->开启
    if (!originEnable && enable) {
      await services.CreateAddonRelease({
        params: {
          policyId,
        },
        content: {
          addonName: 'cloud-sae-custom',
          envType: 'ECS',
          releaseName: `sae-custom-${appConfig?.AppId}`,
          version: '*',
          values: JSON.stringify({
            _entity: {
              instance_id: appConfig?.AppId,
              vpc_id: appConfig?.VpcId,
            },
            ...config,
          }),
        },
      });
      Message.success(
        intl(
          'saenext.shared.PrometheusMonitorField.PrometheusMonitorDialog.PrometheusMonitoringEnabled',
        ),
      );
      return;
    }
    // 开启->关闭
    if (originEnable && !enable) {
      await services.DeleteAddonRelease({
        params: {
          policyId,
          releaseName: `sae-custom-${appConfig?.AppId}`,
        },
      });
      Message.success(
        intl(
          'saenext.shared.PrometheusMonitorField.PrometheusMonitorDialog.PrometheusMonitoringClosedSuccessfully',
        ),
      );
      return;
    }
    // 采集指标编辑
    if (originEnable && enable && appAddonReleaseInfo?.config) {
      if (
        config.interval !== appAddonReleaseInfo?.config?.interval ||
        config.port !== appAddonReleaseInfo?.config?.port ||
        config.metricPath !== appAddonReleaseInfo?.config?.metricPath
      ) {
        await services.UpdateAddonRelease({
          params: {
            policyId,
            releaseName: `sae-custom-${appConfig?.AppId}`,
          },
          content: {
            addonVersion,
            values: JSON.stringify({
              _entity: {
                instance_id: appConfig?.AppId,
                vpc_id: appConfig?.VpcId,
              },
              ...config,
            }),
          },
        });
        Message.success(
          intl(
            'saenext.shared.PrometheusMonitorField.PrometheusMonitorDialog.PrometheusMonitoringAndCollectionIndicators',
          ),
        );
      }
    }
  };
  const toogleVisible = () => {
    setVisible(!visible);
  };

  return (
    <>
      <AddPropsWrap
        onClick={() => {
          toogleVisible();
        }}
      >
        {children}
      </AddPropsWrap>
      <Dialog
        title={intl(
          'saenext.shared.PrometheusMonitorField.PrometheusMonitorNotEnable.ConfigurePrometheusMonitoring',
        )}
        visible={visible}
        onOk={handleSettingPrometheusConfig}
        onClose={toogleVisible}
        onCancel={toogleVisible}
        okProps={{ loading }}
        style={{ width: 800 }}
      >
        <Form field={field}>
          <PrometheusMonitorField
            name="PrometheusMonitorConfig"
            appAddonReleaseInfo={appAddonReleaseInfo}
            ref={prometheusMonitorRef}
            hasCmsPermission={hasCmsPermission}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return prometheusMonitorRef?.current?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        </Form>
      </Dialog>
    </>
  );
};
export default forwardRef(PrometheusMonitorDialog);
