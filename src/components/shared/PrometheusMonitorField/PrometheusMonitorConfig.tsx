import { intl } from '@ali/cnd';
import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useContext,
  useMemo,
} from 'react';
import {
  Form,
  Field,
  Switch,
  ToolTipCondition,
  Input,
  NumberPicker,
  ConsoleContext,
  CndStatus,
  Balloon,
  Icon,
} from '@ali/cnd';
import { isEmpty } from 'lodash';
import services from '~/services';

import { debounce, get } from 'lodash';
import C from '~/constants/common';
import CachedData from '~/cache/common';

const statusMap = {
  UnInstalled: {
    type: 'stop',
    label: intl('saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.Uninstalled'),
  },
  UnInstalling: {
    type: 'loading',
    label: intl('saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.Unmounting'),
  },
  Success: {
    type: 'success',
    label: intl('saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.AccessSuccessful'),
  },
  Failed: {
    type: 'fail',
    label: intl('saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.AccessFailed'),
  },
  Connecting: {
    type: 'loading',
    label: intl('saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.Accessing'),
  },
};
const PrometheusMonitorConfig = (props, ref) => {
  const { value = {}, onChange, appAddonReleaseInfo, hasCmsPermission = true, type = '' } = props;
  const field = Field.useField();
  const [enable, setEnable] = useState(value?.enable);
  const [isOpenPrometheus, setIsOpenPrometheus] = useState(true);
  const [addonStatus, setAddonStatus] = useState('');
  const isIntl = CachedData.isSinSite();
  const { region } = useContext(ConsoleContext);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [value],
  );

  useEffect(() => {
    GetCmsService();
  }, []);

  useEffect(() => {
    if (
      !isEmpty(appAddonReleaseInfo) &&
      appAddonReleaseInfo?.enablePrometheus &&
      !isEmpty(appAddonReleaseInfo.config)
    ) {
      field.setValues({ ...appAddonReleaseInfo.config });
      const conditions = get(appAddonReleaseInfo, 'conditions', []);
      setEnable(appAddonReleaseInfo.enablePrometheus);
      if (conditions.length > 0) {
        // 状态判断顺序 已卸载>卸载中>成功>失败>接入中
        if (conditions.some((item) => item.type === 'UnInstalled' && item.status === 'True')) {
          setAddonStatus('UnInstalled');
        } else if (
          conditions.some((item) => item.type === 'UnInstalled' && item.status === 'Unknow')
        ) {
          setAddonStatus('UnInstalling');
        } else if (conditions.some((item) => item.type === 'Ready' && item.status === 'True')) {
          setAddonStatus('Success');
        } else if (conditions.some((item) => item.status === 'False')) {
          setAddonStatus('Failed');
        } else {
          setAddonStatus('Connecting');
        }
      }
    }
  }, [isOpenPrometheus, JSON.stringify(appAddonReleaseInfo)]);

  const GetCmsService = async () => {
    const res = await services.GetCmsService({
      params: {
        product: 'prometheus',
      },
    });
    const enabled = get(res, 'enabled', false);
    setIsOpenPrometheus(enabled);
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      field.validate((error, value) => {
        error ? resolve(' ') : resolve(true);
      });
    });
  };
  const onFieldChange = (values, item) => {
    onChange?.(values);
  };

  const renderEnableTip = useMemo(() => {
    if (!isOpenPrometheus) {
      return (
        <>
          {intl(
            'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.CurrentlyPrometheusMonitoringIsNot',
          )}

          <a
            href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=${
              isIntl ? 'prometheus_serverless_public_intl' : 'prometheus_serverless_public_cn'
            }`}
          >
            {intl('saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.Activate')}
          </a>
          {intl(
            'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.ThenTurnOnThePrometheus',
          )}
        </>
      );
    }
    // 应用详情cms:CreateIntegrationPolicy无权限或者未正常获取到deployId时禁止操作
    if (type !== 'create') {
      if (!hasCmsPermission) {
        return intl(
          'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.TheCurrentUserDoesNot',
        );
      }
      if (!appAddonReleaseInfo?.policyId) {
        return intl(
          'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.CmsTheCreateintegrationpolicyInterfaceDoes',
        );
      }
    }
  }, [isOpenPrometheus, hasCmsPermission, type, appAddonReleaseInfo?.policyId]);

  return (
    <Form
      field={field}
      onChange={debounce(onFieldChange, 500)}
      {...C.FORM_LAYOUT_LEFT}
      id="prometheus-monitor"
    >
      <Form.Item
        label={intl(
          'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.PrometheusMonitoring',
        )}
        help={
          addonStatus ? (
            <CndStatus type={statusMap[addonStatus].type} label={statusMap[addonStatus].label} />
          ) : (
            ''
          )
        }
      >
        {isOpenPrometheus &&
        (type === 'create' ||
          (type !== 'create' && hasCmsPermission && appAddonReleaseInfo?.policyId)) ? (
          <Switch
            name="EnablePrometheus"
            checked={enable}
            onChange={v => {
              setEnable(v);
              setAddonStatus('');
            }}
          />
        ) : (
          <ToolTipCondition align="r" show={true} tip={renderEnableTip}>
            <Switch name="enable" disabled={true} />
          </ToolTipCondition>
        )}
      </Form.Item>
      {isOpenPrometheus && enable && (
        <>
          <Form.Item
            label={
              <>
                <span>
                  {intl(
                    'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.CollectionIntervalSeconds',
                  )}
                </span>
                <Balloon align="t" trigger={<Icon size="xs" type="help" />} closable={false}>
                  {intl(
                    'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.TheIntervalBetweenMetricsCollected',
                  )}
                </Balloon>
              </>
            }
            required
            requiredMessage={intl(
              'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.PleaseFillInTheCollection',
            )}
            style={{ marginBottom: 16 }}
          >
            {/* @ts-ignore */}
            <NumberPicker name="interval" min={1} style={{ width: '80%' }} />
          </Form.Item>
          <Form.Item
            label={
              <>
                <span className="mr-xs">
                  {intl(
                    'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.DestinationPort',
                  )}
                </span>
                <Balloon align="t" trigger={<Icon size="xs" type="help" />} closable={false}>
                  {intl(
                    'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.TheListeningPortOfThe',
                  )}
                </Balloon>
              </>
            }
            required
            requiredMessage={intl(
              'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.EnterTheDestinationPort',
            )}
            style={{ marginBottom: 16 }}
          >
            {/* @ts-ignore */}
            <NumberPicker name="port" min={0} max={65535} style={{ width: '80%' }} />
          </Form.Item>
          <Form.Item
            label={
              <>
                <span className="mr-xs">
                  {intl(
                    'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.IndicatingPath',
                  )}
                </span>
                <Balloon align="t" trigger={<Icon size="xs" type="help" />} closable={false}>
                  {intl(
                    'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.HttpPathToExposeMetric',
                  )}
                </Balloon>
              </>
            }
            required
            requiredMessage={intl(
              'saenext.shared.PrometheusMonitorField.PrometheusMonitorConfig.PleaseFillInTheInstruction',
            )}
          >
            <Input name="metricPath" style={{ width: '80%' }} />
          </Form.Item>
        </>
      )}
    </Form>
  );
};

export default forwardRef(PrometheusMonitorConfig);
