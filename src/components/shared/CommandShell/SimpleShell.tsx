import React from 'react';
import ResizeWrap from '../ResizeWrap';
import { intl, ShellEditor } from '@ali/cnd';

const SimpleShell = (props) => {
  const {
    value = [],
    onChange = () => {},
    ...restProps
  } = props;

  const valStr = Array.isArray(value) ? value?.join('\n') : value;

  const onShellChange = (v) => {
    if (!v) {
      onChange();
      return;
    }
    onChange(v.split(/(?=\r|\n)\r?\n?/g));
  }

  return (
    <>
      <p>{intl('saenext.shared.LifeCycleField.LifeCycleConf.TheFirstBehaviorCommandThe')}</p>
      <ResizeWrap>
        <ShellEditor
          value={valStr}
          onChange={onShellChange}
          {...restProps}
        />
      </ResizeWrap>
    </>
  )
}

export default SimpleShell;