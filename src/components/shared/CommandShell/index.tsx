import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { isEmpty, isUndefined } from 'lodash';
import { jsonStringify } from '~/utils/transfer-data';
import BinShell, { COMMAND, COMMAND_LIST } from './BinShell';
import SimpleShell from './SimpleShell';
import If from '../If';
import { Button, Message } from '@ali/cnd';

const CommandShell = (props) => {
  const { value = [], onChange } = props;

  const [isBinShell, setIsBinShell] = useState<boolean | undefined>();

  useEffect(() => {
    if (isEmpty(value)) return;
    const [command, dashC] = value;
    if (COMMAND_LIST.includes(command) && dashC === '-c') {
      intBinShellType(true);
    } else {
      intBinShellType(false);
    }
  }, [jsonStringify(value)]);

  const intBinShellType = (v) => {
    // 只在第一次有值回显时判断，避免编辑时类型变化
    if (!isUndefined(isBinShell)) return;
    setIsBinShell(v);
  };

  const transIntoBinShell = () => {
    setIsBinShell(true);

    onChange([COMMAND.SHELL, '-c', value.join('\n')]);
  };

  return (
    <>
      <If condition={isBinShell === false}>
        <Message type="notice" className="mb-s">
          {intl('saenext.shared.CommandShell.WhetherToConvertToA')}

          <Button text type="primary" className="ml-s" onClick={transIntoBinShell}>
            {intl('saenext.shared.CommandShell.Conversion')}
          </Button>
        </Message>
      </If>
      {isBinShell !== false ? <BinShell {...props} /> : <SimpleShell {...props} />}
    </>
  );
};

export default CommandShell;
