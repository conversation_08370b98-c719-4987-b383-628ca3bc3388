import React, { useEffect, useState } from 'react';
import { intl, Radio, ShellEditor, Message } from '@ali/cnd';
import { jsonParse, jsonStringify } from '~/utils/transfer-data';
import { isEmpty } from 'lodash';
import ResizeWrap from '../ResizeWrap';

export enum COMMAND {
  SHELL = '/bin/sh',
  BASH = '/bin/bash',
}

export const COMMAND_LIST = [COMMAND.SHELL, COMMAND.BASH];

const BinShell = (props) => {
  const {
    value = [],
    onChange,
    ...restProps
  } = props;

  const defaultCommand = COMMAND.SHELL;

  const [command, setCommand] = useState<string>(defaultCommand);
  const [args, setArgs] = useState('');

  useEffect(() => {
    parseValue(value)
  }, [jsonStringify(value)])

  const parseValue = (v) => {
    if (isEmpty(v)) {
      setCommand(defaultCommand);
      setArgs('');
      return;
    }
    const [ command, dashC, ...args ] = v;
    setCommand(command);
    // 兼容多个命令行参数，换行
    const argsStr = args.join('\n');
    setArgs(argsStr);
  }

  const onCmdChange = (v) => {
    setCommand(v);
    if (!isEmpty(args)) {
      const val = [v, '-c', args];
      onChange(val);
    }
  }

  const onArgsChange = (v) => {
    setArgs(v);
    if (!isEmpty(v)) {
      const val = [command, '-c', v];
      onChange(val);
    } else {
      onChange([]);
    }
  }

  return (
    <>
      <Message type="warning" className="mb-s">
        {intl('saenext.components.shared.CommandArgsForm.BinBashOrBinSh')}
      </Message>
      <Radio.Group className="mb" value={command} onChange={onCmdChange}>
        <Radio value={COMMAND.SHELL}>{` >_ /bin/sh`}</Radio>
        <Radio value={COMMAND.BASH}>{`>_ /bin/bash`}</Radio>
      </Radio.Group>
      <ResizeWrap>
        <ShellEditor value={args} onChange={onArgsChange} {...restProps} />
      </ResizeWrap>
    </>
  );
}

export default BinShell;