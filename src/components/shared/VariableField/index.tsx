import { intl } from '@ali/cnd';
import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import { Button, Field, Form, Icon } from '@ali/xconsole';
import {
  map,
  find,
  keys,
  filter,
  get,
  concat,
  isEmpty,
  mapValues,
  noop,
  forEach,
  has,
  cloneDeep,
  size,
} from 'lodash';
import ConfigMapSlide from '../ConfigMapSlide';
import services from '~/services';
import * as _services from '~/services';
import VariableItem from './VariableItem';
import SecretEditSlideButton from '../../namespace/secret/components/SecretEditSlideButton';

type IListProps = {
  namespaceId: string;
  onChange?: Function;
  value: any;
  operateType?: string;
  hideSecret?: boolean;
  isPreview?: boolean;
};

// 环境变量配置列表
const VariableList = (props: IListProps, ref) => {
  const { namespaceId, onChange = noop, value, operateType = 'create', isPreview, hideSecret } = props;
  const field = Field.useField({
    parseName: true,
  });
  const { getValues, setValue, validatePromise } = field;
  const getInitValue = () => {
    return isEmpty(value ? JSON.parse(value) : value)
      ? []
      : map(JSON.parse(value), (item: any) => {
          if (has(item, 'valueFrom.configMapRef')) {
            return {
              type: 'config',
              key: item?.name?.indexOf('sae-sys-configmap-all') !== 0 ? item?.name : '',
              ConfigMapId: get(item, 'valueFrom.configMapRef.configMapId'),
              configMapKey:
                item?.name?.indexOf('sae-sys-configmap-all') === 0
                  ? 'allKey'
                  : get(item, 'valueFrom.configMapRef.key'),
            };
          }
          if (has(item, 'valueFrom.secretRef')) {
            return {
              type: 'secret',
              // @ts-ignore
              key: item?.name?.indexOf('sae-sys-secret-all') !== 0 ? item?.name : '',
              secretId: get(item, 'valueFrom.secretRef.secretId'),
              secretKey:
                // @ts-ignore
                item?.name?.indexOf('sae-sys-secret-all') === 0
                  ? 'allKey'
                  : get(item, 'valueFrom.secretRef.key'),
            };
          }

          return {
            type: 'custom',
            key: get(item, 'name'),
            value: get(item, 'value'),
          };
        });
  };

  const [configMaps, setConfigMaps] = useState([]);
  const [list, setList] = useState<any[]>([]);
  const [loadingConfigMap, setLoadingConfigMap] = useState(false);
  const [secretList, setSecretList] = useState([]);

  const configMapNames = map(configMaps, 'Name');

  const getKeys = (ConfigMapId) => {
    const option = find(configMaps, (item) => item.ConfigMapId === ConfigMapId) || {};
    const keysDatasource = map(keys(option.Data), (item) => ({
      label: item,
      value: item,
    }));
    // 已经选择的 configMap 不能再全选 key
    const isConfigSelected = filter(list, { ConfigMapId })?.length > 1;
    return [
      {
        label: intl('saenext.shared.VariableField.AllKeys'),
        value: 'allKey',
        disabled: isConfigSelected,
      },
      ...keysDatasource,
    ];
  };

  useImperativeHandle(ref, () => ({
    validate: variableValidate,
  }));

  useEffect(() => {
    if (operateType === 'edit' && !value) {
      return;
    }
    setList(getInitValue());
  }, [JSON.stringify(value)]);

  useEffect(() => {
    if (isEmpty(namespaceId)) return;
    fetchListNamespacedConfigMaps();
    getSecretList();
  }, [namespaceId]);

  useEffect(() => {
    let newList: any[] = [];
    const formItem = get(getValues(), 'form-item', []);
    const formItemArray = Array.isArray(formItem) ? formItem : [];
    mapValues(formItemArray, (item) => {
      if (!isEmpty(item) && item.type) {
        newList.push(item);
      }
    });
    onSubmit(newList);
  }, [JSON.stringify(getValues()), configMaps, secretList]);

  useEffect(() => {
    if (!value) return;
    forEach(getInitValue(), (item: any, index) => {
      setValue(`form-item[${index}].type`, item.type);
      setValue(`form-item[${index}].key`, item.key);
      setValue(`form-item[${index}].value`, item.value);
      setValue(`form-item[${index}].ConfigMapId`, item.ConfigMapId);
      setValue(`form-item[${index}].configMapKey`, item.configMapKey);
      setValue(`form-item[${index}].secretId`, item.secretId);
      setValue(`form-item[${index}].secretKey`, item.secretKey);
    });
  }, [JSON.stringify(value)]);

  const fetchListNamespacedConfigMaps = async () => {
    setLoadingConfigMap(true);
    const { Data } = await services.ListNamespacedConfigMaps({
      NamespaceId: namespaceId,
    });
    setConfigMaps(get(Data, 'ConfigMaps', []));
    setLoadingConfigMap(false);
  };

  const getSecretList = async () => {
    const { Data } =
      (await _services.ListSecrets({
        NamespaceId: namespaceId,
      })) || {};
    const { Secrets = [] } = Data;
    setSecretList(Secrets);
  };

  const handleAdd = () => {
    const newList = concat(list, { type: 'custom' });
    onSubmit([...newList]);
  };

  const handleDelete = (index) => {
    const formItem = get(getValues(), 'form-item', []);
    const formItemArray = Array.isArray(formItem) ? formItem : [];
    const values = cloneDeep(formItemArray);
    values.splice(index, 1);
    const newValue = filter(values, (item) => !isEmpty(item) && !!item.type);
    onSubmit([...newValue]);
  };

  const onSubmit = (values) => {
    const postData = map(values, (item) => {
      if (item.type === 'custom') {
        return {
          name: item.key,
          value: item.value,
        };
      } else if (item.type === 'config') {
        if (item.configMapKey === 'allKey') {
          return {
            name:
              'sae-sys-configmap-all' +
              '-' +
              find(configMaps, { ConfigMapId: item.ConfigMapId })?.Name,
            valueFrom: {
              configMapRef: {
                configMapId: item.ConfigMapId,
                key: '',
              },
            },
          };
        }
        return {
          name: item.key,
          valueFrom: {
            configMapRef: {
              configMapId: item.ConfigMapId,
              key: item.configMapKey,
            },
          },
        };
      } else if (item.type === 'secret') {
        if (item.secretKey === 'allKey') {
          return {
            name:
              'sae-sys-secret-all' +
              '-' +
              find(secretList, { SecretId: item.secretId })?.SecretName,
            valueFrom: {
              secretRef: {
                secretId: item.secretId,
                key: '',
              },
            },
          };
        }
        return {
          name: item.key,
          valueFrom: {
            secretRef: {
              secretId: item.secretId,
              key: item.secretKey,
            },
          },
        };
      }
    });
    onChange && onChange(JSON.stringify(postData));
    if (operateType === 'edit' && isEmpty(postData)) {
      setList([]);
    }
  };

  const variableValidate = async () => {
    const { errors } = await validatePromise();
    if (errors) {
      return intl('saenext.shared.VariableField.IncompleteEnvironmentVariableInformation');
    } else {
      return true;
    }
  };

  return (
    <Form field={field}>
      <div className="mb-l">
        <ConfigMapSlide
          buttonType="secondary"
          type="create"
          reload={fetchListNamespacedConfigMaps}
          namespaceId={namespaceId}
          nameList={configMapNames}
          className="mr-s"
        />

        {!hideSecret &&
          <SecretEditSlideButton
          buttonText={intl('saenext.shared.VariableField.CreateASecret')}
          buttonType="secondary"
          disabled={secretList?.length >= 20 || isPreview}
          type="create"
          onSuccess={getSecretList}
          namespaceId={namespaceId}
        />
        }
      </div>

      {map(list, (item, index) => {
        return (
          <VariableItem
            {...item}
            key={index}
            field={field}
            index={index}
            configMaps={configMaps}
            getKeys={getKeys}
            list={list as any}
            handleDelete={handleDelete}
            loadingConfigMap={loadingConfigMap}
            onRefresh={fetchListNamespacedConfigMaps}
            isPreview={isPreview}
            hideSecret={hideSecret}
            secretList={secretList}
            namespaceId={namespaceId}
          />
        );
      })}
      <Button onClick={handleAdd} style={{ marginTop: 8, display: 'block' }}>
        <Icon type="add" />
        {intl('saenext.shared.VariableField.Add')}
      </Button>
    </Form>
  );
};

export default forwardRef(VariableList);
