import React, { Component } from 'react';
import VariableSelector from './index';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';

type Props = {
  value: Object;
  onChange: Function;
  namespaceId: string;
};

class VariableSelectorFieldClass extends Component<Props> {
  private variableRef = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return this.variableRef.validate();
  };

  render() {
    return <VariableSelector {...this.props} ref={ref => (this.variableRef = ref)} />;
  }
}

class VariableSelectorField extends BaseField {
  props;
  static displayName = 'VariableSelectorField';

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <VariableSelectorFieldClass {...newProps} />;
  }
}

export default ConfigProvider.config(VariableSelectorField as any);
