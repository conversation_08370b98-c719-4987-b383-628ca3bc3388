import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Select, Grid, Form, Input, Icon, Truncate } from '@ali/cnd';
import { map, findIndex, get, filter, includes, keys } from 'lodash';
import * as _services from '~/services';
import { ESecretType } from '../../namespace/secret/constants';

const { Row, Col } = Grid;

const envKeyReserved = [
  'PS1',
  'POD_IP',
  'EDAS_APP_ID',
  'EDAS_PROJECT_NAME',
  'EDAS_NAMESPACE',
  'SAE_INSTANCE_REGION_ID',
  'EDAS_ECC_ID',
];

type IItemProps = {
  namespaceId: string;
  id: string;
  type?: string;
  index: number;
  key?: string;
  configMapKey?: string;
  ConfigMapId?: string;
  value?: string;
  field: any;
  configMaps: any[];
  getKeys: Function;
  list: IItemProps[];
  handleDelete: Function;
  loadingConfigMap: boolean;
  onRefresh: Function;
  isPreview?: boolean;
  hideSecret?: boolean;
  secretList: any[];
};
// 环境变量 配置项
const VariableItem = (props: IItemProps) => {
  const {
    namespaceId,
    type,
    field,
    index,
    key,
    configMapKey,
    value,
    ConfigMapId,
    configMaps,
    getKeys,
    list,
    handleDelete,
    onRefresh,
    loadingConfigMap,
    isPreview,
    secretList,
    hideSecret,
  } = props;
  const { init, getValue, getValues, setValue } = field;
  const [keyDisable, setKeyDisable] = useState(false);
  const [secretKeys, setSecretKeys] = useState([]);

  useEffect(() => {
    const type = getValue(`form-item[${index}].type`);
    const configMapKey = getValue(`form-item[${index}].configMapKey`);
    const secretKey = getValue(`form-item[${index}].secretKey`);
    if (type === 'custom') {
      setKeyDisable(false);
    }
    if (type === 'config') {
      setKeyDisable(configMapKey === 'allKey');
    }
    if (type === 'secret') {
      setKeyDisable(secretKey === 'allKey');
    }
  }, [
    getValue(`form-item[${index}].configMapKey`),
    getValue(`form-item[${index}].secretKey`),
    getValue(`form-item[${index}].type`),
  ]);

  useEffect(() => {
    if (getValue(`form-item[${index}].type`) === 'secret') {
      const secretId = getValue(`form-item[${index}].secretId`);
      // @ts-ignore
      if (secretId && secretId === list[index]?.secretId) {
        getSecretKeys(secretId);
      }
    }
  }, [
    getValue(`form-item[${index}].secretId`),
    getValue(`form-item[${index}].type`),
    JSON.stringify(list),
  ]);

  const getSecretKeys = async (id) => {
    if (!id) return;
    setSecretKeys([]);
    const { Data = {} } = await _services.DescribeSecret({
      NamespaceId: namespaceId,
      SecretId: id,
    });
    const { SecretData, SecretType } = Data;
    const dataSource = handleKeySource(SecretData, SecretType);
    const isSelected = filter(list, { secretId: id })?.length > 1;
    setSecretKeys([
      {
        label: intl('saenext.shared.VariableField.AllKeys'),
        value: 'allKey',
        disabled: isSelected,
      },
      ...dataSource,
    ]);
  };

  const handleKeySource = (data, type) => {
    let dataSource = [];
    switch (type) {
      case ESecretType.Opaque:
        const opaque_keys = keys(data) || [];
        dataSource = map(opaque_keys, (key) => ({
          label: key,
          value: key,
        }));
        break;
      case ESecretType.DockerConfigJson:
        dataSource = [
          {
            label: '.dockerconfigjson',
            value: '.dockerconfigjson',
          },
        ];

        break;
      case ESecretType.TLS:
        dataSource = [
          {
            label: 'tls.crt',
            value: 'tls.crt',
          },
          {
            label: 'tls.key',
            value: 'tls.key',
          },
        ];

        break;
      default:
        break;
    }
    return dataSource;
  };

  return (
    <Row style={{ marginBottom: 0, display: 'flex', alignItems: 'center' }} gutter={8}>
      <Col span="4" style={{ position: 'relative' }}>
        <Form.Item
          label={intl('saenext.shared.VariableField.VariableItem.Type')}
          className="full-width"
          labelAlign="inset"
        >
          <Select
            {...init(`form-item[${index}].type`, {
              initValue: type,
            })}
            className="full-width"
            dataSource={[
              { label: intl('saenext.shared.VariableField.VariableItem.Custom'), value: 'custom' },
              {
                label: intl(
                  'saenext.shared.VariableField.VariableItem.ReferenceConfigurationItems',
                ),
                value: 'config',
              },
              {
                label: intl(
                  'saenext.shared.VariableField.VariableItem.ReferenceConfidentialDictionary',
                ),
                value: 'secret',
                show: !hideSecret,
              },
            ].filter(item => item.show !== false)}
          />
        </Form.Item>
      </Col>
      <Col span="8">
        <Form.Item
          label={intl('saenext.shared.VariableField.VariableItem.VariableName')}
          className="full-width"
          labelAlign="inset"
          required={!keyDisable}
          isPreview={isPreview}
          renderPreview={(val) => {
            return (
              <div style={{ height: 32, lineHeight: '40px' }}>
                <Truncate
                  type="width"
                  threshold={350}
                  popupStyle={{ wordBreak: 'break-all' }}
                  align="t"
                >
                  {`${intl('saenext.shared.VariableField.VariableItem.VariableName')} ${val}`}
                </Truncate>
              </div>
            );
          }}
        >
          <Input
            {...init(`form-item[${index}].key`, {
              initValue: key,
              rules: [
                {
                  validator: (rule, value, callback) => {
                    const values = get(getValues(), 'form-item', []);
                    const existedOption = filter(
                      values,
                      (obj) => obj.key === value && !!value && !!obj.key,
                    );
                    const regx = /^[-._a-zA-Z][-._a-zA-Z0-9]*$/;

                    if (existedOption.length > 1)
                      callback(
                        intl(
                          'saenext.shared.VariableField.VariableItem.ThisValueAlreadyExistsPlease',
                        ),
                      );
                    else if (includes(envKeyReserved, value))
                      callback(
                        intl('saenext.shared.VariableField.VariableItem.ThisVariableNameHasBeen'),
                      );
                    else if (!regx.test(value) && !keyDisable)
                      callback(
                        intl(
                          'saenext.shared.VariableField.VariableItem.VariableNamesCanOnlyContain',
                        ),
                      );
                    else callback();
                  },
                },
                {
                  required: !keyDisable,
                  message: ' ',
                },
              ],
            })}
            className="full-width"
            disabled={keyDisable}
          />
        </Form.Item>
      </Col>
      <Col span="1">
        <div style={{ textAlign: 'center', lineHeight: '32px', height: 32, marginBottom: 8 }}>
          =
        </div>
      </Col>
      {getValue(`form-item[${index}].type`) === 'custom' && (
        <Col span="10">
          <Form.Item
            label={intl('saenext.shared.VariableField.VariableItem.VariableValue')}
            className="full-width"
            labelAlign="inset"
            isPreview={isPreview}
            renderPreview={(val) => {
              return (
                <div style={{ height: 32, lineHeight: '40px' }}>
                  <Truncate
                    type="width"
                    threshold={450}
                    popupStyle={{ wordBreak: 'break-all' }}
                    align="t"
                  >
                    {`${intl('saenext.shared.VariableField.VariableItem.VariableValue')} ${val}`}
                  </Truncate>
                </div>
              );
            }}
          >
            <Input
              {...init(`form-item[${index}].value`, {
                initValue: value,
              })}
              className="full-width"
            />
          </Form.Item>
        </Col>
      )}
      {getValue(`form-item[${index}].type`) === 'config' && (
        <Col span="10">
          <Row gutter={8}>
            <Col span="13">
              <Form.Item
                label={intl('saenext.shared.VariableField.VariableItem.ConfigurationItemName')}
                className="full-width"
                labelAlign="inset"
                required
                isPreview={isPreview}
                renderPreview={(val: any) => {
                  return (
                    <div style={{ height: 32, lineHeight: '40px' }}>
                      <Truncate
                        type="width"
                        threshold={240}
                        popupStyle={{ wordBreak: 'break-all' }}
                        align="t"
                      >
                        {`${intl(
                          'saenext.shared.VariableField.VariableItem.ConfigurationItemName',
                        )} ${val?.label}`}
                      </Truncate>
                    </div>
                  );
                }}
              >
                <Select
                  className="full-width"
                  {...init(`form-item[${index}].ConfigMapId`, {
                    initValue: ConfigMapId,
                    rules: [{ required: true, message: ' ' }],
                  })}
                  hasClear
                  placeholder={intl(
                    'saenext.shared.VariableField.VariableItem.SelectAConfigurationItemName',
                  )}
                  onChange={(val) => {
                    setValue(`form-item[${index}].ConfigMapId`, val);
                    setValue(`form-item[${index}].configMapKey`, '');
                  }}
                  dataSource={map(configMaps, (obj) => ({
                    label: obj.Name,
                    value: obj.ConfigMapId,
                    disabled:
                      findIndex(list as any, {
                        type: 'config',
                        ConfigMapId: obj.ConfigMapId,
                        configMapKey: 'allKey',
                      }) > -1,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span="10">
              <Form.Item
                label={intl('saenext.shared.VariableField.VariableItem.Key')}
                className="full-width"
                labelAlign="inset"
                required
                isPreview={isPreview}
                renderPreview={(val: any) => {
                  return (
                    <div style={{ height: 32, lineHeight: '40px' }}>
                      <Truncate
                        type="width"
                        threshold={180}
                        popupStyle={{ wordBreak: 'break-all' }}
                        align="t"
                      >
                        {`${intl('saenext.shared.VariableField.VariableItem.Key')} ${val?.label}`}
                      </Truncate>
                    </div>
                  );
                }}
              >
                <Select
                  className="full-width"
                  {...init(`form-item[${index}].configMapKey`, {
                    initValue: configMapKey,
                    rules: [{ required: true, message: ' ' }],
                  })}
                  hasClear
                  placeholder={intl('saenext.shared.VariableField.VariableItem.PleaseSelectAKey')}
                  dataSource={getKeys(getValue(`form-item[${index}].ConfigMapId`))}
                />
              </Form.Item>
            </Col>
            {!isPreview && (
              <Col span="1" align="center">
                <span className={loadingConfigMap ? 'ib arrow-animation' : 'ib'}>
                  <Icon
                    type="refresh"
                    size="small"
                    style={{ cursor: 'pointer', lineHeight: '32px', height: 32, marginBottom: 8 }}
                    onClick={onRefresh as any}
                  />
                </span>
              </Col>
            )}
          </Row>
        </Col>
      )}
      {getValue(`form-item[${index}].type`) === 'secret' && (
        <Col span="10">
          <Row gutter={8}>
            <Col span="13">
              <Form.Item
                label={intl('saenext.shared.VariableField.VariableItem.SecretDictionaryName')}
                className="full-width"
                labelAlign="inset"
                required
                isPreview={isPreview}
                renderPreview={(val: any) => {
                  return (
                    <div style={{ height: 32, lineHeight: '40px' }}>
                      <Truncate
                        type="width"
                        threshold={240}
                        popupStyle={{ wordBreak: 'break-all' }}
                        align="t"
                      >
                        {`${intl("saenext.shared.VariableField.VariableItem.SecretDictionaryName")} ${val?.label}`}
                      </Truncate>
                    </div>
                  );
                }}
              >
                <Select
                  className="full-width"
                  {...init(`form-item[${index}].secretId`, {
                    rules: [{ required: true, message: ' ' }],
                  })}
                  hasClear
                  placeholder={intl(
                    'saenext.shared.VariableField.VariableItem.SelectASecretDictionaryName',
                  )}
                  onChange={(val) => {
                    setValue(`form-item[${index}].secretId`, val);
                    setValue(`form-item[${index}].secretKey`, '');
                  }}
                  dataSource={map(secretList, (obj) => ({
                    label: obj.SecretName,
                    value: obj.SecretId,
                    disabled:
                      findIndex(list as any, {
                        type: 'secret',
                        secretId: obj.SecretId,
                        secretKey: 'allKey',
                      }) > -1,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span="10">
              <Form.Item
                label={intl('saenext.shared.VariableField.VariableItem.Key')}
                className="full-width"
                labelAlign="inset"
                required
                isPreview={isPreview}
                renderPreview={(val: any) => {
                  return (
                    <div style={{ height: 32, lineHeight: '40px' }}>
                      <Truncate
                        type="width"
                        threshold={180}
                        popupStyle={{ wordBreak: 'break-all' }}
                        align="t"
                      >
                        {`${intl('saenext.shared.VariableField.VariableItem.Key')} ${val?.label}`}
                      </Truncate>
                    </div>
                  );
                }}
              >
                <Select
                  className="full-width"
                  {...init(`form-item[${index}].secretKey`, {
                    rules: [{ required: true, message: ' ' }],
                  })}
                  hasClear
                  placeholder={intl('saenext.shared.VariableField.VariableItem.PleaseSelectAKey')}
                  dataSource={secretKeys}
                />
              </Form.Item>
            </Col>
          </Row>
        </Col>
      )}
      {!isPreview && (
        <Col span="1">
          <Icon
            onClick={() => handleDelete(index)}
            style={{
              marginLeft: 8,
              cursor: 'pointer',
              lineHeight: '32px',
              height: 32,
              marginBottom: 8,
            }}
            type="delete"
          />
        </Col>
      )}
    </Row>
  );
};

export default VariableItem;
