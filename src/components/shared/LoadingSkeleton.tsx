import React, { memo } from 'react';
import { Loading, Skeleton } from '@ali/cnd';

const LoadingSkeleton = ({ showLoading = false, isSkeleton = false, ...other }) => {
  if (showLoading) {
    if (isSkeleton) {
      return (
        <div {...other}>
          <div>
            <Skeleton style={{ width: '70%' }} />
          </div>
          <div>
            <Skeleton style={{ width: '90%' }} />
          </div>
          <div>
            <Skeleton style={{ width: '60%' }} />
          </div>
          <div>
            <Skeleton style={{ width: '50%' }} />
          </div>
          <div>
            <Skeleton style={{ width: '30%' }} />
          </div>
          <div>
            <Skeleton style={{ width: '20%' }} />
          </div>
        </div>
      );
    }
    return <Loading fullScreen />;
  }
  return null;
};

export default memo(LoadingSkeleton);