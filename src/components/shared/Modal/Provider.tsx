/* eslint-disable react/no-unused-state */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import ModalContext, { ModalContextType } from './context';
import { assign, mapValues } from 'lodash';

type Props = {
  children: React.ReactNode;
};

class ModalProvider extends Component<Props, ModalContextType> {
  static propTypes = {
    children: PropTypes.node,
  };

  constructor(props) {
    super(props);
    this.toggleModal = this.toggleModal.bind(this);
    this.state = {
      title: null,
      visible: false,
      type: '',
      component: null,
      toggleModal: this.toggleModal,
    };
  }

  toggleModal(args = {} as any) {
    const curState = args.visible ?
      assign(this.state, args) :
      assign(
        mapValues(this.state, () => undefined),
        {
          toggleModal: this.toggleModal,
        }
      );
    this.setState(curState);
  }

  render() {
    return <ModalContext.Provider value={this.state}>{this.props.children}</ModalContext.Provider>;
  }
}

export default ModalProvider;
