import React from 'react';

export interface ModalContextType {
  title?: string | React.ReactNode;
  size?: 'xs' | 'small' | 'medium' | 'large' | 'xl';
  visible: boolean;
  type: 'slide' | 'dialog' | '';
  component?: React.ReactNode;
  content?: React.ReactNode;
  style?: React.CSSProperties;
  width?: number | string;
  toggleModal: (args?: Record<string, any>) => void;
  childProps?: Record<string, any>;
}

const initialState: ModalContextType = {
  title: null,
  size: 'medium',
  visible: false,
  type: 'slide',
  component: null,
  content: null,
  style: {},
  width: null,
  toggleModal: (args = {}) => {},
  childProps: {},
};

export default React.createContext(initialState);
