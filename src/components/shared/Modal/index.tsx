import React, { useContext } from 'react';
import ModalContext from './context';
import { Dialog, SlidePanel } from '@ali/cnd';
import If from '~/components/shared/If';
import AddPropsWrap from '~/components/shared/AddPropsWrap';

const Modal = () => {
  const context = useContext(ModalContext);
  const { childProps, type, visible, title, size = 'medium', toggleModal, content, component, style, width, ...rest } = context;

  const widthMap = {
    xs: 320,
    small: 480,
    medium: 640,
    large: 800,
    xl: 960,
  };

  return (
    <>
      <If condition={type === 'slide'}>
        <SlidePanel
          width={width || widthMap[size]}
          onClose={() => toggleModal({ visible: false })}
          onCancel={() => toggleModal({ visible: false })}
          title={title}
          isShowing={visible}
          {...rest}
        >
          {component || content}
        </SlidePanel>
      </If>
      <If condition={type === 'dialog'}>
        <Dialog
          onClose={() => toggleModal({ visible: false })}
          onCancel={() => toggleModal({ visible: false })}
          title={title}
          visible={visible}
          style={{ width: width || widthMap[size], ...style }}
          shouldUpdatePosition
          {...rest}
        >
          <AddPropsWrap {...childProps}>
            {component || content}
          </AddPropsWrap>
        </Dialog>
      </If>
    </>
  );
};

export default Modal;
