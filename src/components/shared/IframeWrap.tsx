import React, { useRef, useState } from 'react';
import { Loading } from '@ali/cnd';

const IframeWrap = (props) => {
  const {
    src,
    style = {}
  } = props;

  const [loading, setLoading] = useState(true);
  const iframeRef = useRef(null);

  const iframeLoaded = () => {
    setLoading(false);
  }

  return (
    <>
      <Loading
        visible={loading}
        style={{ width: "100%", height: "100%", position: "relative", display: loading ?  "block" : "none" }}
      />
      <iframe
        style={{
          display: loading ? 'none' : 'block',
          ...style
        }}
        width="100%"
        height="100%"
        scrolling="no"
        frameBorder={0}
        onLoad={iframeLoaded}
        src={src}
        ref={iframeRef}
      />
    </>
  );
}

export default IframeWrap;