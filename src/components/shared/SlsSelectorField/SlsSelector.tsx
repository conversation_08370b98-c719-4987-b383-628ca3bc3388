import { intl, Overlay, ToolTipCondition } from '@ali/cnd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import CachedData from '~/cache/common';
import If from '../If';
import { Form, Message, Switch, Icon, Radio } from '@ali/cnd';
import { Form as FormDeep, SelectField, TableField, TextField } from '@ali/deep';
import services from '~/services';
import _, { get } from 'lodash';
import TextRefreshButton from '../TextRefreshButton';
import ExternalLink from '../ExternalLink';
import ComShow from '../ComShow';
import { getProductOpenStatus } from '~/utils/openStatus';
import RefreshButton from '../RefreshButton';

const Size = 99;
const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const SlsSelector = (props, ref) => {
  const { value, onChange = () => {} } = props;
  const [useSls, setUseSls] = useState(false);
  const [useExistProject, setUseExistProject] = useState(false);
  const [projectList, setProjectList] = useState<any>([{}]);
  const [projectListLoading, setProjectListLoading] = useState(false);
  const [logstoreList, setLogstoreList] = useState([]);
  const [openStatus, setOpenStatus] = useState(CachedData.getOpenStatus('logStatus'));
  const [refresh, setRefresh] = useState({});
  const [showDirHint, setShowDirHint] = useState(false);
  const [projectNotExist, setProjectNotExist] = useState(false);

  const formField = useRef(null);
  const tableField = useRef(null);
  const ballonTriggerRef = useRef(null);
  const logtailMapRef = useRef({}); // map 映射 logstoreName --> logtailData
  // const openStatus = CachedData.getOpenStatus('logStatus');

  const { projectName, slsConfigs = [] } = formField.current?.getValue() || {};

  const logTypeList = useMemo(() => {
    // stdout最多选一次
    const disabledStd = _.some(slsConfigs, (item) => {
      return item?.logType === 'stdout';
    });

    return [
      {
        label: intl('saenext.shared.SlsSelectorField.SlsSelector.FileLogLogPathIn'),
        value: 'file_log',
      },
      {
        label: intl('saenext.shared.SlsSelectorField.SlsSelector.ContainerStandardOutputLogs'),
        value: 'stdout',
        disabled: disabledStd,
      },
    ];
  }, [slsConfigs]);

  useEffect(() => {
    initValue();
  }, [JSON.stringify(value)]);

  useEffect(() => {
    useSls && getProjectList();
  }, [useSls]);

  useEffect(() => {
    if (!useSls) {
      onChange('');
    }
  }, [useSls]);

  useEffect(() => {
    projectName && getListLogstore();
  }, [projectName]);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [useSls, projectName, slsConfigs],
  );

  const initValue = () => {
    if (value?.length) {
      setUseSls(true);

      const projectName = _.get(value, '0.projectName');
      const isExistProject = projectName !== undefined;
      setUseExistProject(isExistProject);

      formField.current?.setValue({
        projectName,
        slsConfigs: value,
      });
      setTimeout(() => {
        setRefresh({});
        setComtData();
      }, 1000);
    }
  };

  const setComtData = () => {
    const formGroupIds = tableField.current.getItems?.();
    _.forEach(formGroupIds, (formGroupId) => {
      const itemField = tableField.current.getField(formGroupId);
      const logType = itemField.getValue(`${formGroupId}.logType`);
      const logDirCom = tableField.current.getComponent(formGroupId, 'logDir');
      if (logType === 'stdout') {
        logDirCom.setValue('stdout.log');
        logDirCom.setBehavior('READONLY');
      } else {
        logDirCom.setBehavior('NORMAL');
      }

      const logstoreName = itemField.getValue(`${formGroupId}.logstoreName`);
      if (logstoreName) {
        onLogStoreChange({ value: logstoreName, formGroupId });
      }
    });
  };

  const getLogtailData = async (logstoreName = '') => {
    if (!logstoreName) {
      return;
    }

    const _logtailData = logtailMapRef.current[logstoreName];

    if (!_logtailData) {
      const data = await getListLogtail(logstoreName);
      logtailMapRef.current[logstoreName] = data;
    }

    const logtailData = logtailMapRef.current[logstoreName];
    return logtailData;
  };

  const getProjectList = async (offset = 0, projectList = []) => {
    setProjectListLoading(true);
    const res = await services.ListProjects({
      params: {
        size: Size,
        offset,
      },
      customErrorHandle: (error, values,callback) => {
        setProjectListLoading(false);
        callback && callback()
      },
    });
    const projects = _.get(res, 'projects', []);
    const total = _.get(res, 'total', 0);
    const saeCreatedRegex = /(?=.*auto\screate)(?=.*sae)/i;

    const projectsData = _.map(projects, (val) => {
      const disabled = saeCreatedRegex.test(val.description);

      return {
        ...val,
        key: val.projectName,
        value: val.projectName,
        disabled,
        label: (
          <ToolTipCondition
            show={disabled}
            tip={intl('saenext.shared.SlsSelectorField.SlsSelector.ThisSlsProjectIsA')}
          >
            {val.projectName}
          </ToolTipCondition>
        ),
      };
    });
    const allProjects = projectList.concat(projectsData);
    setProjectListLoading(false);
    setProjectList(allProjects);

    if (total > Size + offset) {
      offset += Size;
      getProjectList(offset, allProjects);
    }
  };

  const getListLogstore = async () => {
    setProjectNotExist(false);
    const { logstores } = await services.ListStores({
      params: { ProjectName: projectName },
      customErrorHandle: (err, data, callback) => {
        if (err?.code === 'ProjectNotExist' || err?.code === 'ProjectForbidden') {
          setProjectNotExist(true);
        } else {
          callback();
        }
      },
    });

    const logstoresData = _.map(logstores, (val) => {
      return {
        key: val,
        value: val,
      };
    });
    setLogstoreList(logstoresData);
  };

  const getListLogtail = async (logstoreName = '') => {
    const _projectName = projectName || get(value, '0.projectName');
    if (!_projectName || !logstoreName) {
      return;
    }
    const { Data } = await services.ListLogtails({
      params: {
        ProjectName: _projectName,
        PageSize: Size,
        CurrentPage: 1,
        LogstoreName: logstoreName,
      },
    });
    const logConfigs = _.get(Data, 'LogConfigs', []);

    const logtailData = _.map(logConfigs, (val) => {
      const { ConfigName, LogDir, SlsLogStore } = val || {};
      return {
        key: ConfigName,
        value: ConfigName,
        disabled: ConfigName?.startsWith('sae-'),
        LogDir,
        SlsLogStore,
      };
    });
    return logtailData;
    // logtailMap[logstoreName] = logtailData;
    // setLogtailMap(logtailMap);
  };

  const handleSwitchChange = (val) => {
    setUseSls(val);
  };

  const handleUseExistSlsChange = (val) => {
    tableField.current.field.reset();
    setUseExistProject(val);
    onChange([
      {
        projectName: val ? '' : undefined,
      },
    ]);
  };

  const handleProjectChange = ({ value, item }) => {
    _.forEach(slsConfigs, (item = {}) => {
      item.projectName = value;
      item.logstoreName = '';
      item.logtailName = '';
    });
    setLogstoreList([]);
    logtailMapRef.current = {};
    onChange(slsConfigs);
  };

  const onConfigChange = ({ value }) => {
    _.forEach(value, (item = {}, index) => {
      value[index] = {
        ...item,
        projectName,
      };
    });
    onChange(value);
  };

  const onTypeChange = ({ value, formGroupId }) => {
    const logDirCom = tableField.current.getComponent(formGroupId, 'logDir');
    if (value === 'stdout') {
      logDirCom.setValue('stdout.log');
      logDirCom.setBehavior('READONLY');
    } else {
      logDirCom.setValue('');
      logDirCom.setBehavior('NORMAL');
    }
  };

  const onDirHintShow = (e) => {
    if (e.target?.value?.endsWith('.log')) {
      ballonTriggerRef.current = e.target;
      setShowDirHint(true);
    } else {
      setShowDirHint(false);
    }
  };

  const onLogStoreChange = async ({ value, formGroupId }) => {
    // const logtailData = await getListLogtail(value);
    const logtailData = await getLogtailData(value);
    tableField.current.setComponentProps(formGroupId, 'logtailName', {
      dataSource: [
        {
          key: intl('saenext.shared.SlsSelectorField.SlsSelector.CreateLogtail'),
          value: intl('saenext.shared.SlsSelectorField.SlsSelector.CreateLogtail'),
          label: intl('saenext.shared.SlsSelectorField.SlsSelector.CreateLogtail'),
          LogDir: '',
        },
        ...logtailData,
      ],
    });
  };

  const errorRender = (val) => {
    return intl('saenext.shared.SlsSelectorField.SlsSelector.CompleteTheSlsConfiguration');
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      if (!useSls) {
        resolve(true);
        return;
      }
      if (useExistProject && projectNotExist) {
        resolve(' ');
        return;
      }

      formField.current.validate((error, value) => {
        error ? resolve(' ') : resolve(true);
      });
    });
  };

  const getKafkaOpenStatus = async () => {
    const enabled = await getProductOpenStatus('sls');
    setOpenStatus(enabled);
  };

  return (
    <>
      <If condition={!openStatus}>
        <Message type="warning" className="mb-l">
          {intl('saenext.shared.SlsSelectorField.SlsSelector.YouHaveNotActivatedLog')}
          <a
            href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=sls`}
            target="_blank"
            className="ml-s"
          >
            {intl('saenext.shared.SlsSelectorField.SlsSelector.ActivateNow')}
          </a>
          {intl('saenext.common.full-stop')}
          <br />
          {intl('saenext.shared.SlsSelectorField.SlsSelector.IfTheActivationIsSuccessful')}
          <RefreshButton
            label={intl('saenext.shared.SlsSelectorField.SlsSelector.Refresh')}
            handler={getKafkaOpenStatus}
          />
        </Message>
      </If>

      <Form {...(fieldLayout as any)}>
        <Form.Item label={intl('saenext.shared.SlsSelectorField.SlsSelector.EnableSlsLogService')}>
          <Switch disabled={!openStatus} checked={useSls} onChange={handleSwitchChange} />
        </Form.Item>
        <ComShow if={useSls}>
          <Form.Item label={' '} {...(fieldLayout as any)}>
            <Radio.Group value={useExistProject} onChange={handleUseExistSlsChange}>
              <Radio
                value={false}
                label={intl('saenext.shared.SlsSelectorField.SlsSelector.CreateAnSlsResource')}
              />

              <Radio
                value={true}
                label={intl('saenext.shared.SlsSelectorField.SlsSelector.UseExistingSlsResources')}
              />
            </Radio.Group>
          </Form.Item>
        </ComShow>
      </Form>

      <ComShow if={useSls}>
        <Message
          title={intl('saenext.shared.SlsSelectorField.SlsSelector.Precautions')}
          type="warning"
          className="mb-l"
        >
          <div className="text-line">
            {intl('saenext.shared.SlsSelectorField.SlsSelector.TheLogSourceStorageDirectory')}
          </div>
          <div className="text-line">
            {intl('saenext.shared.SlsSelectorField.SlsSelector.DoNotStoreOtherImportant')}
          </div>
        </Message>
        <FormDeep ref={(c) => (formField.current = c?.getInstance?.() || c)}>
          <SelectField
            {...fieldLayout}
            name="projectName"
            label={intl('saenext.shared.SlsSelectorField.SlsSelector.UseExistingSlsProject')}
            showSearch
            placeholder={intl(
              'saenext.shared.SlsSelectorField.SlsSelector.SelectAnExistingSlsProject',
            )}
            dataSource={projectList}
            onChange={handleProjectChange}
            state={projectListLoading ? 'loading' : projectNotExist ? 'error' : undefined}
            className="full24-width"
            behavior={useExistProject ? 'NORMAL' : 'HIDDEN'}
            notFoundContent={
              <>
                {intl('saenext.shared.SlsSelectorField.SlsSelector.ThereIsNoSlsProject')}

                <a
                  href={`${CachedData.confLink('feature:sls:url')}/lognext/profile`}
                  target="_blank"
                >
                  {intl('saenext.shared.SlsSelectorField.SlsSelector.SlsConsole')}
                </a>
                {intl('saenext.shared.SlsSelectorField.SlsSelector.Purchase')}
              </>
            }
            validation={[
              {
                type: 'required',
                message: intl('saenext.shared.SlsSelectorField.SlsSelector.SelectSlsProject'),
              },
            ]}
            extra={
              <>
                <TextRefreshButton onClick={() => getProjectList()} />
                <ExternalLink
                  className="ml-l"
                  label={intl('saenext.shared.SlsSelectorField.SlsSelector.CreateANewSlsProject')}
                  url={`${CachedData.confLink('feature:sls:url')}/lognext/profile`}
                />

                {projectName && projectNotExist && (
                  <div style={{ color: 'red' }}>
                    {intl(
                      'saenext.shared.SlsSelectorField.SlsSelector.ProjectnameHasBeenDeletedPlease',
                      { projectName: projectName },
                    )}
                  </div>
                )}
              </>
            }
          />

          <TableField
            ref={(c) => (tableField.current = c?.getInstance?.() || c)}
            name="slsConfigs"
            label=""
            addButtonText={intl('saenext.shared.SlsSelectorField.SlsSelector.Add')}
            className="full24-table"
            layout="TABLER"
            required
            errorRender={errorRender}
            showIndex={false}
            showDeleteConfirm={false}
            actionsColumnWidth={80}
            delButtonText={<Icon type="delete" />}
            onChange={onConfigChange}
          >
            <SelectField
              name="logType"
              label={intl('saenext.shared.SlsSelectorField.SlsSelector.CollectionLogType')}
              required
              dataSource={logTypeList}
              onChange={onTypeChange}
            />

            <TextField
              name="logDir"
              label={intl('saenext.shared.SlsSelectorField.SlsSelector.LogSource')}
              required
              onChange={(_, e) => onDirHintShow(e)}
              onFocus={onDirHintShow}
              onBlur={() => setShowDirHint(false)}
            />

            {useExistProject ? (
              <SelectField
                name="logstoreName"
                label="logstoreName"
                dataSource={logstoreList}
                onChange={onLogStoreChange}
                required
              />
            ) : (
              <TextField style={{ width: 0 }} behavior="HIDDEN" />
            )}

            {useExistProject ? (
              <SelectField name="logtailName" label="logtail" required />
            ) : (
              <TextField style={{ width: 0 }} behavior="HIDDEN" />
            )}
          </TableField>
        </FormDeep>

        <Overlay visible={showDirHint} target={ballonTriggerRef.current}>
          <div className="next-balloon next-balloon-medium next-balloon-normal next-balloon-top-left mt-s">
            <div className="next-balloon-title">
              {intl('saenext.shared.SlsSelectorField.SlsSelector.Precautions')}
            </div>
            <div className="next-balloon-content">
              {intl('saenext.shared.SlsSelectorField.SlsSelector.DoNotDirectlyConfigureLog')}
              <br />
              {intl('saenext.shared.SlsSelectorField.SlsSelector.MakeSureThatTheDirectory')}
            </div>
          </div>
        </Overlay>
      </ComShow>
    </>
  );
};

export default forwardRef(SlsSelector);
