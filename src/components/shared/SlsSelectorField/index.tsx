import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import SlsSelector from './SlsSelector';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';
import _ from 'lodash';

type Props = {
  value?: Object;
  onChange?: Function;
};

class SlsSelectorFieldClass extends Component<Props> {
  private slsRef = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.slsRef.validate();
  };

  render() {
    return <SlsSelector {...this.props} ref={(ref) => (this.slsRef = ref)} />;
  }
}

class SlsSelectorField extends BaseField {
  props;
  static displayName = 'SlsSelectorField';

  formatValueIn(value, props) {
    if (!value) {
      return [];
    }

    if (Array.isArray(value)) {
      return value;
    }

    try {
      const dataSource = JSON.parse(value);
      _.forEach(dataSource, (item = {} as any) => {
        if (item.logtailName === '') {
          item.logtailName = intl('saenext.shared.SlsSelectorField.CreateLogtail');
        }
        if (!item.logType) {
          item.logType = 'file_log';
        }
        if (item.logType === 'stdout') {
          item.logDir = 'stdout.log';
        }
      });
      return dataSource;
    } catch (e) {
      return [];
    }
  }

  formatValueOut(value) {
    if (!value) {
      return '';
    }
    if (!Array.isArray(value)) {
      return value;
    }
    _.forEach(value, (item = {} as any) => {
      if (item.logtailName === intl('saenext.shared.SlsSelectorField.CreateLogtail')) {
        item.logtailName = '';
      }
      if (item.logType === 'file_log') {
        item.logType = '';
      }
      if (item.logType === 'stdout') {
        item.logDir = '';
      }
    });
    return JSON.stringify(value);
  }

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <SlsSelectorFieldClass {...newProps} />;
  }
}

export default ConfigProvider.config(SlsSelectorField as any);
