import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Copy, Dialog, intl } from '@ali/cnd';
import { Form, NumberField, SelectField, SwitchField, TableField, TextField } from '@ali/deep';
import { Icon, Message } from '@ali/cnd';
import { debounce } from 'lodash';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';

const formItemLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const K8sRegistration = (props, ref) => {
  const { value = {}, onChange, appName, namespaceId } = props;
  const { serviceId, serviceName } = value;
  const [regionId, namespaceSuffix = 'default'] = namespaceId.split(':');

  const [enable, setEnable] = useState(value?.enable);

  const formField = useRef(null);

  useEffect(() => {
    const { enable = false } = value;
    setEnable(enable);
    formField.current?.setValue?.(value);
  }, [JSON.stringify(value)]);

  useEffect(() => {
    initServiceName();
  }, [appName]);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [value],
  );

  const serviceNameData = useMemo(() => {
    // 新版包含 .{namespaceSuffix} 信息
    if (serviceName?.includes('.')) {
      return serviceName?.split('.')[0];
    } else {
      return `${serviceName}.svc.cluster.local.${regionId}`;
    }
  }, [serviceName]);


  const initServiceName = () => {
    formField.current?.setValue?.({ serviceName: appName.toLocaleLowerCase() });
  };

  const onFieldChange = (values, item) => {
    onChange?.(values);
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      formField.current.validate((error, value) => {
        error ? resolve(' ') : resolve(true);
      });
    });
  };

  const errorRender = (val) => {
    return intl('saenext.shared.K8sRegistrationField.K8sRegistration.CompleteThePortAndProtocol');
  };


  return (
    <>
      <Form
        ref={c => (formField.current = c?.getInstance?.() || c)}
        onChange={debounce(onFieldChange, 500)}
      >
        <SwitchField
          className="inline-extra"
          name="enable"
          label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.KSServiceBasedService')}
          {...formItemLayout}
          extra={
            <a
              href={CachedData.confLink('help:sae:microservices-configure-application-access-based-on-k8s')}
              target="_blank"
              className="ml-l"
            >
              <Icon type="connection" size="small" className="mr-s" />
              {intl(
                'saenext.shared.K8sRegistrationField.K8sRegistration.KSServiceRegistrationDiscovery',
              )}
            </a>
          }
          value={enable}
          onChange={({ value }) => {
            if (!value && serviceId) {
              Dialog.confirm({
                title: intl('saenext.shared.K8sRegistrationField.K8sRegistration.Prompt'),
                content: intl(
                  'saenext.shared.K8sRegistrationField.K8sRegistration.EnsureThatOtherServicesUsing',
                ),
                footerActions: ['ok'],
              });
            }
            setEnable(value);
          }}
        />

        {enable && (
          <>
            <Message title="" type="warning" className="mt mb">
              <ol className="list-decimal pl-l">
                <li>
                  {intl(
                    'saenext.shared.K8sRegistrationField.K8sRegistration.NoteThatTheCustomService',
                  )}
                </li>
                <li>
                  {intl('saenext.shared.K8sRegistrationField.K8sRegistration.EtcResolvConfWillBe')}
                </li>
              </ol>
            </Message>

            <If condition={serviceId}>
              <Form.Item
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.ServiceName')}
                {...formItemLayout}
              >
                <Copy text={serviceNameData}>{serviceNameData}</Copy>
              </Form.Item>
            </If>

            <If condition={!serviceId}>
              <TextField
                required
                name="serviceName"
                behavior="NORMAL"
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.ServiceName')}
                placeholder={intl(
                  'saenext.shared.K8sRegistrationField.K8sRegistration.EnterAServiceName',
                )}
                requiredMessage={intl(
                  'saenext.shared.K8sRegistrationField.K8sRegistration.EnterAServiceName',
                )}
                {...formItemLayout}
                validation={[
                  {
                    type: 'required',
                    message: intl(
                      'saenext.shared.K8sRegistrationField.K8sRegistration.EnterAServiceName',
                    ),
                  },
                  {
                    type: 'customValidate',
                    param: value => {
                      const regex = /^[a-z0-9]+(-[a-z0-9]+)*$/;
                      if (value.length > 63) {
                        return intl(
                          'saenext.shared.K8sRegistrationField.K8sRegistration.TheMaximumLengthOfThe',
                        );
                      }
                      if (!regex.test(value)) {
                        return intl(
                          'saenext.shared.K8sRegistrationField.K8sRegistration.TheServiceNameConsistsOf',
                        );
                      }
                      return true;
                    },
                  },
                ]}
              />
            </If>

            <TableField
              name="portProtocols"
              label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.PortsAndProtocols')}
              addButtonText={intl('saenext.shared.K8sRegistrationField.K8sRegistration.Add')}
              className="full24-table"
              layout="TABLER"
              required
              errorRender={errorRender}
              showIndex={false}
              showDeleteConfirm={false}
              actionsColumnWidth={80}
              delButtonText={<Icon type="delete" />}
              maxItems={10}
              {...formItemLayout}
            >
              <NumberField
                name="port"
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.Port')}
                required
                min={1}
                max={65535}
              />

              <NumberField
                name="targetPort"
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.ContainerPort')}
                required
                min={1}
                max={65535}
              />

              <SelectField
                name="protocol"
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.Agreement')}
                dataSource={[
                  {
                    value: 'TCP',
                    label: 'TCP',
                  },
                  {
                    value: 'UDP',
                    label: 'UDP',
                  },
                ]}
                required
              />
            </TableField>
          </>
        )}
      </Form>
    </>
  );
};

export default forwardRef(K8sRegistration);
