import { jsonParse } from "~/utils/transfer-data";

const formatPvtz = (objStr: string, update: boolean = false) => {
  const obj = jsonParse(objStr);

  if (!obj) {
    return;
  }

  // 复制应用时删除多余字段
  Reflect.deleteProperty(obj, 'portAndProtocol');
  Reflect.deleteProperty(obj, 'pvtzDiscoveryName');
  !update && Reflect.deleteProperty(obj, 'serviceId');

  return JSON.stringify(obj);
}

export {
  formatPvtz,
}