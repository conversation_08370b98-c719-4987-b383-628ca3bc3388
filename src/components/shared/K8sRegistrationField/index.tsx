import React, { Component } from 'react';
import K8sRegistration from './K8sRegistration';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';
import _, { isObject } from 'lodash';
import { jsonParse } from '~/utils/transfer-data';

type Props = {
  value?: Object;
  onChange?: Function;
};

class K8sRegistrationFieldClass extends Component<Props> {
  private ref = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.ref.validate();
  }

  render() {

    return (
      <K8sRegistration
        {...this.props}
        ref={(ref) => this.ref = ref}
      />
    )
  }
}

class K8sRegistrationField extends BaseField {
  props;
  static displayName = 'K8sRegistrationField';

  formatValueIn(value, props) {
    if (isObject(value)) {
      return value;
    }
    const valueObj = jsonParse(value) || {};
    valueObj.enable && (valueObj.enable = jsonParse(valueObj.enable));
    return valueObj;
  }

  formatValueOut(value = {} as any, props) {
    if (!value?.enable && !value?.serviceId) {
      return undefined;
    }
    
    const { namespaceId } = props;
    const result = {
      ...value,
      namespaceId,
    }
    return JSON.stringify(result);
  }

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return (
      <K8sRegistrationFieldClass
        {...newProps}
      />
    );
  }
}

export default ConfigProvider.config(K8sRegistrationField as any)
