import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { DatePicker } from '@ali/cnd';
import moment from 'moment';

const { RangePicker } = DatePicker;

interface Props {
  field: any;
  period: number;
  initValue: any;
  timeInitValue: any;
  localStorageKey: string;
  onTimeChanged: Function;
  periodChange: Function;
  getFaPeriod: Function;
  getLocalStorage: Function;
  width?: any;
  disabledDate?: [number, string];
}
interface State {}

export default class TimeContainer extends Component<Props, State> {
  state = {};

  disabledDate = (date) => {
    const { disabledDate = [1, 'months'] } = this.props;
    const [amount = 1, unit = 'months'] = disabledDate;
    return (
      // @ts-ignore
      date.valueOf() > moment().valueOf() || date.valueOf() < moment().subtract(amount, unit).valueOf()
    );
  };

  onRangeOk = (value) => {
    const { field, periodChange, getFaPeriod, localStorageKey, getLocalStorage } = this.props;
    const [start, end] = value;
    periodChange('custom');
    getFaPeriod([start.valueOf(), end.valueOf()]);
    field.setValue('tiem', value);
    let storag = getLocalStorage();
    window.localStorage.setItem(
      localStorageKey,
      JSON.stringify({
        start: start.valueOf(),
        end: end.valueOf(),
        period: storag.period,
        recentTiem: 'custom',
      }),
    );
  };

  render() {
    const { field, initValue, width = 490 } = this.props;
    const { init } = field;
    return (
      <RangePicker
        label={intl('saenext.components.shared.TimeContainer.TimeRange')}
        style={{ width, height: 32 }}
        {...init('time', {
          initValue: [moment(initValue[0]), moment(initValue[1])],
        })}
        showTime={{ format: 'HH:mm:ss' }}
        disabledDate={this.disabledDate}
        onOk={this.onRangeOk}
      />
    );
  }
}
