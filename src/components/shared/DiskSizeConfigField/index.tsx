import React, { useEffect, useState } from 'react';
import { Form, intl, Message, NumberPicker, Balloon, Icon } from '@ali/cnd';
import C from '~/constants/common';
import CachedData from '~/cache/common';

const DiskSizeConfigField = (props) => {
  const { name } = props;
  return (
    <div>
      <Message type="notice" className="mb-l">
        {intl('saenext.shared.DiskSizeConfigField.SaeProvidesGibForEach')}

        {/* <a href="" target="_blank">
           查看计费
          </a> */}
      </Message>
      <Form.Item
        label={
          <>
            <span>{intl('saenext.shared.DiskSizeConfigField.TemporaryDiskSize')}</span>
            <Balloon
              trigger={<Icon size="xs" type="help_fill" style={{ color: '#888', marginLeft: 4 }} />}
              closable={false}
              align="r"
            >
              {intl('saenext.shared.DiskSizeConfigField.TheDiskUsedToTemporarily')}
            </Balloon>
          </>
        }
        {...C.FORM_LAYOUT_LEFT}
        help={intl('saenext.shared.DiskSizeConfigField.ValueRangeTo')}
        validator={(rule, value: number, callback) => {
          if (value && value > 500) {
            callback(' ');
          } else {
            callback();
          }
        }}
      >
        {/* @ts-ignore */}
        <NumberPicker name={name} defaultValue={20} min={20} innerAfter="GiB" />
      </Form.Item>
    </div>
  );
};

export default DiskSizeConfigField;
