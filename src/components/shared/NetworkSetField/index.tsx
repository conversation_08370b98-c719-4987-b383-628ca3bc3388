import React from 'react';
import BaseField from '@ali/deep-form-helper';
import NetworkSet from './NetworkSet';
import { ConfigProvider } from '@ali/deep';

class NetworkSetField extends BaseField {
  props;
  static displayName = 'NetworkSetField';

  getProps() {
    return {
      ...this.props
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return <NetworkSet {...newProps} />;
  }
}

export default ConfigProvider.config(NetworkSetField as any)
