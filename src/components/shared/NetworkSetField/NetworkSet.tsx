import { Dialog, intl } from '@ali/cnd';
import React, { Component } from 'react';
import Form from '@ali/deep-form';
import { SwitchField, SelectField, RadioField } from '@ali/deep';
import cls from 'classnames';
import { ConsoleContext, Message } from '@ali/cnd';
import _ from 'lodash';
import services from '~/services';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import TextRefreshButton from '~/components/shared/TextRefreshButton';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/cache/common';
import { isForbidden } from '~/utils/authUtils';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

interface INetworkSetProps {
  value?: {
    enableVpc: boolean;
    vpcId?: string;
    vSwitchIds?: string[];
    securityGroupId?: string;
  };
  disabled?: boolean;
  defaultVpcId: string;
  onChange?: Function;
  showStaticIpOptions?: boolean;
}

interface INetworkSetState {
  enableVpc: boolean;
  vpcId: string;
  vpcList: { label: any; value: string }[];
  vswitchList: { label: any; value: string }[];
  vswitchListAvailable: { label: any; value: string }[];
  securityGroupsList: { label: any; value: string }[];
  availableVswitch: string[];
  isStaticIp: boolean;
  vpcLoading: boolean;
  vswitchLoading: boolean;
  securityGroupsLoading: boolean;
  vpcRamWarn: boolean;
  authedVPC: boolean;
}

class NetworkSet extends Component<INetworkSetProps, INetworkSetState> {
  private regionId: string;
  private refField = React.createRef() as any;
  constructor(props) {
    super(props);
    this.state = {
      enableVpc: false,
      vpcId: '',
      vpcList: [],
      vswitchList: [],
      vswitchListAvailable: [],
      securityGroupsList: [],
      availableVswitch: [],
      isStaticIp: false,
      vpcLoading: false,
      vswitchLoading: false,
      securityGroupsLoading: false,
      vpcRamWarn: false,
      authedVPC: true,
    };
    this.getVpcs = this.getVpcs.bind(this);
    //@ts-ignore
    this.regionId = ConsoleContext._currentValue.region.getCurrentRegionId();
  }

  componentDidMount(): void {
    const { value = { enableVpc: false } } = this.props;
    this.refField.setValue(value);
    this.initSetValue();
  }

  componentWillReceiveProps(nextProps, nextContext: any): void {
    const { value = { enableVpc: false } } = nextProps;
    this.refField.setValue(value);
  }

  componentDidUpdate(prevProps, prevState) {
    const { availableVswitch, vswitchList } = this.state;
    const { value, defaultVpcId, onChange } = this.props;
    const { enableVpc = false, vpcId = '' } = value || {};

    if (prevProps?.value?.enableVpc !== enableVpc) {
      if (enableVpc) {
        this.getVpcs();
        this.getAvailableVswitch();
      }
    }

    if (prevProps?.defaultVpcId !== defaultVpcId) {
      if (enableVpc && defaultVpcId) {
        onChange({
          enableVpc: true,
          vpcId: defaultVpcId,
          vSwitchIds: [],
          securityGroupId: '',
        });
      }
    }

    if (prevProps?.value?.vpcId !== vpcId) {
      if (vpcId) {
        this.getVswitches(vpcId);
        this.getSecurityGrouponList(vpcId);
      }
    }

    if (prevState.availableVswitch !== availableVswitch || prevState.vswitchList !== vswitchList) {
      this.updateAvailableVswitch();
    }
  }

  initSetValue = () => {
    const { value } = this.props;
    const { enableVpc = false, vpcId = '' } = value || {};

    if (enableVpc) {
      this.getVpcs();
      this.getAvailableVswitch();
    }
    if (vpcId) {
      this.getAvailableVswitch();
      this.getVswitches(vpcId);
      this.getSecurityGrouponList(vpcId);
    }
  };

  onNetworkChange = () => {
    const { onChange, defaultVpcId } = this.props;
    const value = this.refField.getValue();
    if (value.enableVpc) {
      if (defaultVpcId) {
        value.vpcId = defaultVpcId;
      }
      onChange?.(value);
    } else {
      onChange?.({
        ...value,
        enableVpc: false,
        vpcId: '',
        securityGroupId: '',
        vSwitchIds: [],
        internetAccess: true,
      });
    }
  };

  validate = () => {
    let validateRes = false;
    this.refField.validate((error, value) => {
      validateRes = error ? false : true;
    });
    return validateRes;
  };

  fixedIpNotice = () => {
    Dialog.confirm({
      title: intl('saenext.shared.NetworkSetField.NetworkSet.FixedPublicIpAddressConfiguration'),
      content: intl('saenext.shared.NetworkSetField.NetworkSet.IfYouSelectAFixed'),
      closeable: false,
      footerActions: ['ok'],
    });
  };

  getAvailableVswitch = async () => {
    const { availableAZs } = await services.GetAccountSettings();
    this.setState({
      availableVswitch: availableAZs,
    });
  };

  updateAvailableVswitch = () => {
    const { vswitchList, availableVswitch } = this.state;
    if (!availableVswitch.length) return;

    _.forEach(vswitchList, (item) => {
      // @ts-ignore
      const disabled = !_.includes(availableVswitch, item.ZoneId);
      // @ts-ignore
      item.disabled = disabled;
    });

    this.setState({
      vswitchListAvailable: vswitchList,
    });
  };

  getVpcs = async () => {
    this.setState({
      vpcLoading: true,
    });
    const { Vpcs = {} } = await services.DescribeVpcs({
      params: {
        RegionId: this.regionId,
        PageNumber: 1,
        PageSize: 50,
      },
      customErrorHandle: (error, data) => {
        if (error.code === 'Forbidden.RAM') {
          this.setState({
            vpcRamWarn: true,
          });
          return {};
        }
        return error;
      },
    });
    const { Vpc = [] } = Vpcs;
    const vpcList = _.map(Vpc, ({ VpcName, VpcId }) => ({
      label: (
        <>
          <span className="mr-l">{VpcName || VpcId}</span>
          <span className="text-description">{VpcId}</span>
        </>
      ),

      value: VpcId,
    }));
    this.setState({
      vpcList,
      vpcLoading: false,
    });
  };

  getVswitches = async (vpcId, vSwitchId?) => {
    // const { availableVswitch } = this.state;

    if (!vpcId) return;

    this.setState({
      vswitchLoading: true,
    });

    const { VSwitches = {} } = await services.DescribeVSwitches({
      params: {
        RegionId: this.regionId,
        VpcId: vpcId,
        VSwitchId: vSwitchId || undefined,
        PageNumber: 1,
        PageSize: 50,
      },
      customErrorHandle: (error, _p, cb) => {
        if (isForbidden(error.code)) {
          this.setState({ authedVPC: false });
        } else {
          this.setState({ authedVPC: true });
          cb?.();
        }
      }
    });

    const vswitchList = _.map(VSwitches.VSwitch, (item) => {
      const zoneName = _.toUpper(_.last(item.ZoneId as string));
      // const disabled = !_.includes(availableVswitch, item.ZoneId);
      return {
        // disabled,
        ...item,
        value: item.VSwitchId,
        label: (
          <>
            <span className="mr-l">{item.VSwitchName}</span>
            <span className="text-description mr-s">{item.VSwitchId}</span>
            <span className="text-description">
              {intl('saenext.shared.NetworkSetField.NetworkSet.ZoneZonename', {
                zoneName: zoneName,
              })}
            </span>
          </>
        ),
      };
    });
    this.setState({
      vpcId,
      vswitchList,
      vswitchLoading: false,
    });
  };

  getSecurityGrouponList = async (vpcId) => {
    if (!vpcId) return;
    this.setState({
      securityGroupsLoading: true,
    });
    const {
      SecurityGroups: { SecurityGroup },
    } = await services.DescribeSecurityGroups({
      RegionId: this.regionId,
      VpcId: vpcId,
      PageSize: 99,
      PageNumber: 1,
    });

    const securityGroupValid = _.filter(SecurityGroup, (v) => !v.ServiceManaged);
    const securityGroupsList = _.map(securityGroupValid, (v) => {
      return {
        value: v.SecurityGroupId,
        label: (
          <>
            <span className="mr-l">{v.SecurityGroupName}</span>
            <span className="text-description">{v.SecurityGroupId}</span>
          </>
        ),
      };
    });
    this.setState({
      securityGroupsList,
      securityGroupsLoading: false,
    });
  };

  render() {
    const { disabled, showStaticIpOptions = false } = this.props;
    const {
      vpcList,
      vswitchListAvailable,
      securityGroupsList,
      // isStaticIp,
      availableVswitch,
      vpcLoading,
      vswitchLoading,
      securityGroupsLoading,
      vpcRamWarn,
    } = this.state;
    const {
      enableVpc = false,
      vpcId = '',
      internetAccess = true,
    } = this.refField?.getValue?.() || {};

    const availableVswitchStr = _.map(availableVswitch, (item) => {
      const zoneName = _.toUpper(_.last(item));
      return intl('saenext.shared.NetworkSetField.NetworkSet.ZoneZonename', { zoneName: zoneName });
    }).join(', ');

    return (
      <Form
        ref={(c) => {
          if (c) {
            this.refField = c.getInstance();
          }
        }}
      >
        <SwitchField
          name="enableVpc"
          label={intl('saenext.shared.NetworkSetField.NetworkSet.AllowApplicationsToAccessVpc')}
          {...fieldLayout}
          onChange={({ value }) => {
            this.onNetworkChange();
            if (!value) {
              this.setState({
                vpcRamWarn: false,
              });
            }
          }}
        />

        {enableVpc ? (
          <>
            {CachedData.isSubAccount() && vpcRamWarn ? (
              <Message type="warning" className="mb-l">
                {intl('saenext.shared.NetworkSetField.NetworkSet.CurrentlyYourAccountHasNot')}

                <a
                  href={`${CachedData.confLink('feature:ram:url')}/#/role/authorize`}
                  target="_blank"
                  className="ml-s mr-s"
                >
                  {intl('saenext.shared.NetworkSetField.NetworkSet.RamAccessControl')}
                </a>
                {intl('saenext.shared.NetworkSetField.NetworkSet.UseTheProductAfterAuthorization')}
              </Message>
            ) : null}

            <SelectField
              required
              name="vpcId"
              label={intl('saenext.shared.NetworkSetField.NetworkSet.Vpc')}
              showSearch
              {...fieldLayout}
              dataSource={vpcList}
              disabled={vpcLoading || disabled}
              state={vpcLoading ? 'loading' : null}
              className={cls({ 'full24-width': true })}
              validation={[
                {
                  type: 'required',
                  message: intl('saenext.shared.NetworkSetField.NetworkSet.SelectVpc'),
                },
              ]}
              onChange={({ value }) => {
                this.props.onChange({
                  enableVpc: true,
                  vpcId: value,
                  vSwitchIds: [],
                  securityGroupId: '',
                });
              }}
            />

            {disabled ? null : (
              <div className="field-item-extra" style={{ marginLeft: 220 }}>
                <TextRefreshButton onClick={this.getVpcs} />
                <ExternalLink
                  className="ml-l"
                  label={intl('saenext.shared.NetworkSetField.NetworkSet.CreateAVpc')}
                  url={`${CachedData.confLink('feature:vpc:url')}/vpc/${this.regionId}/vpcs`}
                />
              </div>
            )}

            <SelectField
              required
              name="vSwitchIds"
              label={
                <UnAuthedLabel
                  text={intl('saenext.shared.NetworkSetField.NetworkSet.SwitchVswitch')}
                  authed={this.state.authedVPC}
                  authKey='AliyunVPCReadOnlyAccess'
                />
              }
              {...fieldLayout}
              mode="multiple"
              help={intl('saenext.shared.NetworkSetField.NetworkSet.SelectTheVswitchOfThe', {
                availableVswitchStr: availableVswitchStr,
              })}
              placeholder={intl('saenext.shared.NetworkSetField.NetworkSet.SelectASwitchOrEnter')}
              filterLocal={false}
              showSearch
              onSearch={_.debounce((val) => this.getVswitches(vpcId, val), 500)}
              onBlur={() => this.getVswitches(vpcId)}
              onChange={this.onNetworkChange}
              dataSource={vswitchListAvailable}
              itemRender={(item) => (
                <div>
                  <span>{item.label}</span>
                  <ExternalLink
                    className="ml-s"
                    url={`${CachedData.confLink('feature:vpc:url')}/vpc/${this.regionId}/switches/${item.value}`}
                  />
                </div>
              )}
              disabled={vswitchLoading}
              state={vswitchLoading ? 'loading' : null}
              className={cls({ 'full24-width': true })}
              validation={[
                {
                  type: 'required',
                  message: intl('saenext.shared.NetworkSetField.NetworkSet.SelectVswitch'),
                },
              ]}
            />

            <div className="field-item-extra" style={{ marginLeft: 220 }}>
              <TextRefreshButton onClick={() => this.getVswitches(vpcId)} />
              <ExternalLink
                className="ml-l"
                label={intl('saenext.shared.NetworkSetField.NetworkSet.CreateASwitch')}
                url={`${CachedData.confLink('feature:vpc:url')}/vpc/${this.regionId}/switches`}
              />
            </div>

            <SelectField
              required
              name="securityGroupId"
              label={intl('saenext.shared.NetworkSetField.NetworkSet.SecurityGroup')}
              extra={
                <Message type="notice" className="mt-s">
                  {intl(
                    'saenext.shared.NetworkSetField.NetworkSet.InWebApplicationScenariosTraffic',
                  )}
                </Message>
              }
              {...fieldLayout}
              onChange={this.onNetworkChange}
              dataSource={securityGroupsList}
              disabled={securityGroupsLoading}
              itemRender={(item) => (
                <div>
                  <span>{item.label}</span>
                  <ExternalLink
                    className="ml-s"
                    url={`${CachedData.confLink('feature:ecs:url')}/securityGroupDetail/region/${this.regionId}/groupId/${item.value}/detail`}
                  />
                </div>
              )}
              state={securityGroupsLoading ? 'loading' : null}
              className={cls({ 'full24-width': true })}
              validation={[
                {
                  type: 'required',
                  message: intl('saenext.shared.NetworkSetField.NetworkSet.SelectASecurityGroup'),
                },
              ]}
            />

            <div className="field-item-extra" style={{ marginLeft: 220 }}>
              <TextRefreshButton onClick={() => this.getSecurityGrouponList(vpcId)} />
              <ExternalLink
                className="ml-l"
                label={intl('saenext.shared.NetworkSetField.NetworkSet.CreateASecurityGroup')}
                url={`${CachedData.confLink('feature:ecs:url')}/#/securityGroup/region/${this.regionId}/create`}
              />
            </div>
          </>
        ) : null}
        <RadioField
          className="nat-radio-field"
          name="internetAccess"
          defaultValue={true}
          label={
            <TextWithBalloon
              text={intl('saenext.shared.NetworkSetField.NetworkSet.ApplicationAccessToThePublic')}
              tips={intl(
                'saenext.shared.NetworkSetField.NetworkSet.ConfigureWhetherTheApplicationCan',
              )}
            />
          }
          help={
            !internetAccess
              ? intl.html('saenext.shared.NetworkSetField.NetworkSet.NatGatewayAHrefHttps.new',{
                href:`${CachedData.confLink('feature:vpc:url')}/nat/${this.regionId}/nats`
              })
              : null
          }
          {...fieldLayout}
          onChange={({ value }) => {
            this.onNetworkChange();
            if (!value && !enableVpc) {
              this.fixedIpNotice();
            }
          }}
          // value={isStaticIp}
          // onChange={val => this.setState({ isStaticIp: val.value })}
          dataSource={[
            {
              value: true,
              text: (
                <span className="radio-item-120">
                  {intl('saenext.shared.NetworkSetField.NetworkSet.NonFixedPublicIpAddress')}
                </span>
              ),
            },
            {
              value: false,
              text: (
                <TextWithBalloon
                  text={intl('saenext.shared.NetworkSetField.NetworkSet.FixedPublicIpAddress')}
                  tips={intl.html(
                    'saenext.shared.NetworkSetField.NetworkSet.ItIsApplicableToMultiple.new',
                    {
                      href:CachedData.confLink('help:nat-gateway:create-and-manage-nat-gateways')
                    }
                  )}
                />
              ),
            },
          ]}
        />
      </Form>
    );
  }
}

export default NetworkSet;
