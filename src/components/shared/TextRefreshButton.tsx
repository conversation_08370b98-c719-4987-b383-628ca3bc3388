import { intl } from '@ali/cnd';
import { Icon } from '@ali/cnd';
import React, { memo } from 'react';

const TextRefreshButton = ({
  label = intl('saenext.components.shared.TextRefreshButton.Refresh'),
  onClick,
  icon = true,
  className = '',
}) => {
  return (
    <a href="javascript:void(0)" onClick={onClick} className={className}>
      {label}
      {icon && (
        <span className="ml-s">
          <Icon type="refresh" size="xs" />
        </span>
      )}
    </a>
  );
};

export default memo(TextRefreshButton);
