import { intl } from '@ali/cnd';
import React, { memo, useEffect, useMemo } from 'react';
import CachedData from '~/cache/common';
import * as ubsmsService from '~/services/ubsms';
import * as roleService from '~/services/role';
import { get, isEmpty } from 'lodash';
import { Dialog, Message } from '@ali/cnd';

const PRODUCTS = ['oss', 'acr', 'arms', 'sls', 'ahas', 'oos', 'eventbridge', 'ahaspro', 'nas'];

type Props = {
  // 是否开通 true 开通 false 未开通
  saeEanble: boolean;
  // 欠费状态 true 已欠费  false 未欠费
  saeInDebt: boolean;
  // 欠费超期状态 true 已超期  false 未超期
  saeInDebtOverdue: boolean;
  // 所有产品开通状态
  setOpenStatus: (value: any) => void,
};

const FetchUbsms = (props: Props) => {
  const { saeEanble, saeInDebt, saeInDebtOverdue, setOpenStatus } = props;

  useEffect(() => {
    getDependProductStatus();
  }, [JSON.stringify(saeEanble), JSON.stringify(saeInDebt), JSON.stringify(saeInDebtOverdue)]);

  const getDependProductStatus = useMemo(() => {
    if (!saeEanble) return () => {};

    // 优先拿viper配置
    const openStatus = get(window, 'ALIYUN_CONSOLE_CONFIG.OPEN_STATUS') || {};
    if (!isEmpty(openStatus)) {
      return () => {
        const out = {};
        const results = openStatus;
        const products = Reflect.ownKeys(results) as string[];
        products.forEach((product) => {
          const result = results[product];
          const { enabled } = result;
          const _enabled = enabled == 'true';
          out[product] = !!_enabled;
        });
        Reflect.set(out, 'sae', saeEanble);
        Reflect.set(out, 'inDebt', saeInDebt);
        Reflect.set(out, 'inDebtOverdue', saeInDebtOverdue);
        console.log('out: ', out);
        if (out['sae']) {
          // 开通之后判断 AliyunServiceRoleForSAE
          readAliyunServiceRole2sae();
        }

        CachedData.setOpenStatus(out);
        setOpenStatus(out);
      };
    }

    // 如果viper没有 则用接口配置
    return async () => {
      Promise.all(
        PRODUCTS.map((product) =>
          ubsmsService.DescribeUserBusinessStatus({
            ServiceCode: product,
          }),
        ),
      ).then((results) => {
        const out = {};
        results.forEach((result, index) => {
          const {
            Statuses: { Status = [] },
          } = result;
          const enabled = Status.find(
            (s) => s.StatusKey === 'enabled' && s.StatusValue !== 'false',
          );

          out[PRODUCTS[index]] = !!enabled;
        });
        Reflect.set(out, 'sae', saeEanble);
        Reflect.set(out, 'inDebt', saeInDebt);
        Reflect.set(out, 'inDebtOverdue', saeInDebtOverdue);
        console.log('out: ', out);
        if (out['sae']) {
          // 开通之后判断 AliyunServiceRoleForSAE
          readAliyunServiceRole2sae();
        }
        CachedData.setOpenStatus(out);
        setOpenStatus(out);
      });
    };
  }, [JSON.stringify(saeEanble), JSON.stringify(saeInDebt), JSON.stringify(saeInDebtOverdue)]);

  const readAliyunServiceRole2sae = async () => {
    // 判断主账号是否有角色 AliyunServiceRolePolicyForSAE，子账号不需要 RAM 权限也能调用此接口 CheckServiceLinkedRoleExistence
    const roleResult = await roleService.CheckServiceLinkedRoleExistence({
      params: {
        ServiceName: 'sae.aliyuncs.com',
      }
    });

    if (roleResult && !roleResult?.Exists) {
      const dialog = Dialog.show({
        title: intl('saenext.components.shared.FetchUbsms.WelcomeToServerlessApplicationEngine'),
        content: (
          <div style={{ width: 600, fontSize: 12, lineHeight: '24px', marginTop: 8 }}>
            <p>{intl('saenext.components.shared.FetchUbsms.IfYouPerformThisOperation')}</p>
            <p>{intl('saenext.components.shared.FetchUbsms.RoleNameAliyunserviceroleforsae')}</p>
            <p>
              {intl(
                'saenext.components.shared.FetchUbsms.RolePermissionPolicyAliyunservicerolepolicyforsae',
              )}
            </p>
            <p>{intl('saenext.components.shared.FetchUbsms.PermissionDescriptionSaeIsAllowed')}</p>
            <p>
              {intl('saenext.components.shared.FetchUbsms.DocumentLink')}

              <a href={CachedData.confLink('help:sae:service-linked-role')} target="_blank">
                {intl('saenext.components.shared.FetchUbsms.CreateAServiceAssociatedRole')}
              </a>
            </p>
          </div>
        ),

        okProps: {
          children: intl('saenext.components.shared.FetchUbsms.ConfirmCreation'),
        },
        onCancel: () => {
          dialog.hide();
        },
        onOk: () => {
          return new Promise(async (resolve, reject) => {
            //调用 ram ResourceManager 同名接口，增加参数
            const res = await roleService.CreateServiceLinkedRole({
              params: {
                ServiceName: 'sae.aliyuncs.com',
              },
              customErrorHandle: (err, data, callback) => {
                if (err.code === 'EntityAlreadyExists.Role') {
                  Message.error(intl('saenext.components.shared.FetchUbsms.TheRoleAlreadyExists'));
                  setTimeout(() => {
                    window.location.reload();
                  }, 1000);
                  reject();
                }
                if (err.code === 'NoPermission') {
                  Dialog.show({
                    title: intl('saenext.components.shared.FetchUbsms.SaeServiceAssociatedRole'),
                    content: (
                      <div style={{ width: 600, fontSize: 12, lineHeight: '24px', marginTop: 8 }}>
                        <p>{intl('saenext.components.shared.FetchUbsms.TheUserDoesNotHave')}</p>
                        <p>
                          {intl(
                            'saenext.components.shared.FetchUbsms.AliyunsaefullaccessOrCreateACustom',
                          )}
                        </p>
                        <p>Service Name: sae.aliyuncs.com</p>
                        <p>
                          {intl(
                            'saenext.components.shared.FetchUbsms.ServiceAssociatedRoleNameAliyunserviceroleforsae',
                          )}
                        </p>
                        <p>
                          {intl(
                            'saenext.components.shared.FetchUbsms.UserPermissionsRequiredToPerform',
                          )}
                        </p>
                      </div>
                    ),
                  });
                  reject();
                }
                reject();
              },
            });
            if (res.Role) {
              resolve(res);
              Message.success(intl('saenext.components.shared.FetchUbsms.TheRoleIsCreatedAnd'));
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            }
          });
        },
        footerActions: ['ok'],
        closeMode: [],
        className: 'sae-ram-dialog',
      });
    }
  };

  return null;
};

export default memo(FetchUbsms);
