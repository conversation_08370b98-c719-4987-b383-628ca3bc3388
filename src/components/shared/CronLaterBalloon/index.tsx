import { intl } from '@ali/cnd';
import React from 'react';
import { parseExpression } from 'cron-parser';
import moment from 'moment';
import { Balloon, DateTime, Icon } from '@ali/cnd';
import { map } from 'lodash';

const CronLaterBalloon = (props) => {
  const { trigger } = props;
  const interval = parseExpression(trigger);
  const results = [];
  for (let i = 0; i < 5; i++) {
    results.push(interval.next().toDate());
  }

  return (
    <Balloon align="t" closable={false} trigger={<Icon className="ml-s" type="help" size="xs" />}>
      <div className="mb-s text-bold">
        {intl('saenext.shared.CronLaterBalloon.TheLastFiveExecutionTimes')}
      </div>
      {map(results, (item) => (
        <div>
          <DateTime
            className="block"
            key={item}
            value={item}
            format={{
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            }}
          />
        </div>
      ))}
    </Balloon>
  );
};

export default CronLaterBalloon;