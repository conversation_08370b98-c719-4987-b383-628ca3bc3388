import { CascaderSelect } from '@ali/cnd';
import { map } from 'lodash';
import React from 'react';

const CascaderSelectDetail = (props) => {
  const {
    useDetailValue = false,
    value,
    onChange = () => { },
    multiple = false,
    ...otherProps
  } = props;

  const detailValue = multiple ?
    map(value, (item) => {
      return item.value
    }) : value?.value;

  const onChangeDetail = (val, data) => {
    if (!val) {
      onChange();
      return;
    }
    if (multiple) {
      const newValue = data.map((item) => {
        return {
          label: item.label,
          value: item.value,
          ...item,
        }
      })
      onChange(newValue);
    } else {
      onChange(data);
    }
  }

  return (
    useDetailValue ?
    <CascaderSelect
      value={detailValue}
      onChange={onChangeDetail}
      multiple={multiple}
      {...otherProps}
    /> :
    <CascaderSelect
      {...props}
    />
  )
}

export default CascaderSelectDetail;