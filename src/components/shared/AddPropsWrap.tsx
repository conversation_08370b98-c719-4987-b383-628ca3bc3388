import React from 'react'

const AddPropsWrap = (props) => {
  const {
    merge = true,
    children,
    ...restProps
  } = props;

  if (!children) {
    return null;
  }

  const combineOnClicks = (original?: () => void, additional?: () => void) => {
    return () => {
      original?.();
      additional?.();
    };
  }

  return React.Children.map(children, child => {
    if (merge) {
      const childClick = child.props.onClick;
      const addPropsClick = restProps.onClick;
      restProps.onClick = combineOnClicks(childClick, addPropsClick);
    }

    return React.cloneElement(child, { ...restProps })
  });
}

export default AddPropsWrap;