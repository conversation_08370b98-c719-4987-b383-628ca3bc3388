import { intl } from '@ali/cnd';
import React, { useContext, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { CndRcSelect, Select, CndTable, Search, ConsoleContext } from '@ali/cnd';
import { isEmpty, find, get } from 'lodash';
import {
  ListNamespace,
  ListRepoByNamespace,
  ListTagRepos,
  GetUserInfo,
  listRepoTag,
} from '~/services/acr';
import CachedData from '~/cache/common';

interface IProps {
  initValue: Record<string, string>;
  instanceId: string;
  onChange?: (value: any) => void;
  appType?: string
}

export default forwardRef(({ initValue, instanceId, onChange, appType }: IProps, ref) => {
  const [key, setKey] = useState(0); // 为了 listTag 强制刷新渲染一次
  const [needOpenCr, setNeedOpenCr] = useState(false);
  const [loading, setLoading] = useState(false);
  const [namespaceList, setNamespaceList] = useState([]);
  const [repoResult, setRepoResult] = useState({ repos: [], total: 0 });
  const [repoParams, setRepoParams] = useState({ Page: 1, PageSize: 10, RepoNamePrefix: initValue?.repoName });
  const [useTag, setUseTag] = useState(
    isEmpty(initValue) ? { repoNamespace: '', repoName: '', tag: '' } : initValue,
  );

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  useEffect(() => {
    const _useTag = isEmpty(initValue) ? { repoNamespace: '', repoName: '', tag: '' } : initValue;
    setUseTag(_useTag);
    getNamespace();
  }, [initValue]);

  useImperativeHandle(ref, () => ({
    submit: () => {
      if (!useTag.repoNamespace) {
        return { error: intl('saenext.ImageSelectorField.cr.acr.SelectANamespace') };
      } else if (!useTag.tag) {
        return { error: intl('saenext.ImageSelectorField.cr.acr.SelectAnImageVersion') };
      }
      let image = '';
      if (instanceId) {
        image = `${instanceId}.${regionId}.personal.cr.aliyuncs.com/${useTag.repoNamespace}/${useTag.repoName}:${useTag.tag}`
      } else {
        image =
          appType === 'job'
            ? `registry-vpc.${regionId}.aliyuncs.com/${useTag.repoNamespace}/${useTag.repoName}:${useTag.tag}`
            : `registry.${regionId}.aliyuncs.com/${useTag.repoNamespace}/${useTag.repoName}:${useTag.tag}`;
      }

      return { image, imageType: 'acr' };
    },
  }));

  const getNamespace = async () => {
    try {
      await GetUserInfo();
    } catch (ex) {
      if (ex.message === 'user is not registered.') {
        setNeedOpenCr(true);
        return;
      }
    }
    const { data } = await ListNamespace();
    setNamespaceList(data.namespaces);

    // 处理回显
    const findNamespaceResult = find(
      data.namespaces,
      (item) => item.namespace === initValue.repoNamespace,
    );
    if (findNamespaceResult && initValue.tag) {
      // setLoading(true);
      const { data: listRepo } = await ListRepoByNamespace({
        ...repoParams,
        RepoNamespace: initValue.repoNamespace,
      });
      const findResult = find(listRepo.repos, (item) => item.repoName === initValue.repoName);

      if (!findResult) {
        const { data: findRepoList } = await ListRepoByNamespace({
          ...repoParams,
          RepoNamePrefix: initValue.repoName,
          RepoNamespace: initValue.repoNamespace,
        });
        const findRepo = find(findRepoList?.repos, (item) => item.repoName === initValue.repoName);
        if (findRepo) {
          listRepo.repos.unshift(findRepo);
        }
      }

      setRepoResult(listRepo);
      // setLoading(false);
    }
  };

  const getListRepo = async (params, repoNamespace?: string) => {
    setRepoResult({ repos: [], total: 0 });
    await setLoading(true);
    const { data } = await ListRepoByNamespace({
      ...params,
      RepoNamespace: repoNamespace || useTag.repoNamespace,
    });
    setRepoResult(data);
    setLoading(false);
    return data;
  };

  const getListTags = async (index, { repoNamespace, repoName }) => {
    const result = await ListTagRepos({ RepoNamespace: repoNamespace, RepoName: repoName });
    repoResult.repos[index].tags = result;
    setRepoResult(repoResult);
    setKey(key + 1);
  };

  const fetchTagData = async (search = '', page = 1, record) => {
    const { repoNamespace: RepoNamespace, repoName: RepoName } = record;
    const result = await listRepoTag({
      RepoNamespace,
      RepoName,
      Page: page,
      PageSize: 100,
    });

    const { tags = [], total = 0 } = get(result, 'data', {});
    return {
      data: tags?.map((item) => ({
        ...item,
        value: item.tag,
        label: item.tag,
      })),
      total,
    };
  };

  const onTagChange = (v, repoName) => {
    setUseTag({ repoNamespace: useTag.repoNamespace, repoName, tag: v });

    let image = '';
    if (instanceId) {
      image = `${instanceId}.${regionId}.personal.cr.aliyuncs.com/${useTag.repoNamespace}/${repoName}:${v}`;
    } else {
      image =
        appType === 'job'
          ? `registry-vpc.${regionId}.aliyuncs.com/${useTag.repoNamespace}/${repoName}:${v}`
          : `registry.${regionId}.aliyuncs.com/${useTag.repoNamespace}/${repoName}:${v}`;
    }

    onChange?.({ image });
  };

  if (needOpenCr) {
    return (
      <div>
        {intl('saenext.ImageSelectorField.cr.acr.CurrentlyYourAccountHasNot')}

        <a href={CachedData.confLink('feature:cr:url')} target="_blank" className="ml-s">
          {intl('saenext.ImageSelectorField.cr.acr.ActivateAcr')}
        </a>
      </div>
    );
  }

  return (
    <>
      <div className="flex">
        <Select
          key={key}
          value={useTag.repoNamespace}
          label={intl('saenext.ImageSelectorField.cr.acr.ImageRepositoryNamespace')}
          style={{ width: 260 }}
          disabled={!namespaceList.length || loading}
          dataSource={namespaceList.map((item) => item.namespace)}
          onChange={async (v: string) => {
            const p = { ...repoParams, Page: 1, RepoNamePrefix: '' };
            setUseTag({ repoNamespace: v, repoName: '', tag: '' });
            setRepoParams(p);
            getListRepo(p, v);
          }}
        />

        <Search
          style={{ width: 250 }}
          label={intl('saenext.ImageSelectorField.cr.acr.ImageRepositoryName')}
          className="ml-s"
          placeholder={intl('saenext.ImageSelectorField.cr.acr.EnterTheImageRepositoryName')}
          disabled={!useTag.repoNamespace}
          value={repoParams.RepoNamePrefix}
          onChange={(v) => {
            const p = { ...repoParams, Page: 1, RepoNamePrefix: v };
            setRepoParams(p);
            getListRepo(p);
          }}
          hasClear
        />
      </div>

      <CndTable
        loading={loading}
        dataSource={repoResult.repos}
        className="image-table mt"
        primaryKey="repoName"
        pagination={
          !!repoResult.total && {
            current: repoParams.Page,
            total: repoResult.total,
            pageSize: repoParams.PageSize,
            onPageSizeChange: (v) => {
              const p = { ...repoParams, Page: 1, PageSize: v };
              setRepoParams(p);
              getListRepo(p);
            },
            onChange: (v) => {
              const p = { ...repoParams, Page: v };
              setRepoParams(p);
              getListRepo(p);
            },
          }
        }
        // @ts-ignore
        columns={[
          {
            title: intl('saenext.ImageSelectorField.cr.acr.ImageRepositoryName'),
            dataIndex: 'repoName',
          },
          {
            title: intl('saenext.ImageSelectorField.cr.acr.Type'),
            dataIndex: 'repoType',
          },
          {
            title: intl('saenext.ImageSelectorField.cr.acr.Source'),
            dataIndex: 'repoOriginType',
          },
          {
            title: intl('saenext.ImageSelectorField.cr.acr.SelectImageVersion'),
            dataIndex: 'repoName',
            width: 300,
            cell: (value, index, record) => {
              return (
                <CndRcSelect
                  value={value === useTag.repoName ? useTag.tag : undefined}
                  onChange={(v: string) => onTagChange(v, value)}
                  fetchData={(...params) => fetchTagData(...params, record)}
                  mode="single"
                  style={{ width: 300 }}
                  pagination={{
                    pageNumber: 1,
                    pageSize: 100,
                  }}
                  notFoundContent={
                    <p>{intl('saenext.ImageSelectorField.cr.acr.TheRelevantImageVersionDoes')}</p>
                  }
                  followTrigger={false}
                  // dataSource={record.tags?.map((item) => item.tag)}
                  // onVisibleChange={(visible) => visible && getListTags(index, record)}
                />
              );
            },
          },
        ]}
      />
    </>
  );
});
