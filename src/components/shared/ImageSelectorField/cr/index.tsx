import { Balloon, intl, ToolTipCondition } from '@ali/cnd';
import React, { useState, forwardRef, useEffect, useMemo, useContext } from 'react';
import { Radio, Message } from '@ali/cnd';
import Acr from './acr';
import Cr from './cr';
import CachedData from '~/cache/common';
import ExternalLink from '../../ExternalLink';
import If from '~/components/shared/If';
import FeatureContext from '~/utils/featureContext';
import services from '~/services';
import { find, get } from 'lodash';
import useRegion from '~/hooks/useRegion';

const { Group: RadioGroup } = Radio;

interface IProps {
  initValue: Record<string, string>;
  appType?: 'web' | 'micro' | 'job';
  vpcId?: string;
  value?: any;
  onChange?: (value: any) => void;
  hideAcreeAccelerate?: boolean;
}

export default forwardRef((props: IProps, ref) => {
  const { appType = 'web', vpcId, initValue = {}, hideAcreeAccelerate = false } = props;
  const [radio, setRadio] = useState(initValue?.type || 'acr');

  const [showPersonal, setShowPersonal] = useState(true);
  const [personalInstance, setPersonalInstance] = useState<Record<string, string>>({});

  const regionId = useRegion();

  const { feature } = useContext(FeatureContext);
  const { sae2_new_custom_container } = feature;

  useEffect(() => {
    setRadio(initValue?.type || 'acr');
  }, [initValue]);

  useEffect(() => {
    checkCrType();
  }, []);

  const checkCrType = async () => {
    const res = await services.GetInstance();
    const data = get(res, 'data', {});
    setPersonalInstance(data);

    if (appType === 'web' && data?.instanceId?.startsWith('crpi-')) {
      setShowPersonal(false);
      setRadio('cr');
    }
  };

  const onChange = (v: 'acr' | 'cr') => setRadio(v);

  const personalDisabled = useMemo(() => {
    const disabled =
      personalInstance.instanceId?.startsWith('crpi-') &&
      personalInstance.instanceRegionId !== regionId;
    if (disabled) {
      setRadio('cr');
    }
    return disabled;
  }, [personalInstance, regionId]);

  return (
    <>
      <div>
        <RadioGroup value={radio} onChange={onChange}>
          <If condition={(appType === 'web' && showPersonal) || appType === 'micro' || appType === 'job'}>
            <ToolTipCondition
              show={personalDisabled}
              tip={intl('saenext.ImageSelectorField.cr.TheCurrentRegionIsNot')}
            >
              <Radio value={'acr'} disabled={personalDisabled}>
                {intl('saenext.ImageSelectorField.cr.ImageServicePersonalEdition')}
              </Radio>
            </ToolTipCondition>
          </If>
          <If condition={(appType === 'web' && sae2_new_custom_container) || appType === 'micro' || appType === 'job'}>
            <Radio value={'cr'} className="ml">
              {intl('saenext.ImageSelectorField.cr.EnterpriseEdition')}
            </Radio>
          </If>
        </RadioGroup>

        <Message type="notice" className="mt mb">
          {intl('saenext.ImageSelectorField.cr.ThePersonalEditionOfAlibaba')}

          <ExternalLink url={CachedData.confLink('help:acr:/create-a-personal-edition-instance')} />

          <br />
          {intl('saenext.ImageSelectorField.cr.BecauseTheImagePullPolicy')}
        </Message>
        {radio === 'acr' ? (
          <Acr ref={ref} {...props} instanceId={personalInstance.instanceId} onChange={props.onChange} />
        ) : CachedData.getOpenStatus('acrStatus') ? (
          <Cr ref={ref} {...props} vpcId={vpcId} hideAcreeAccelerate={hideAcreeAccelerate} onChange={props.onChange} />
        ) : (
          <Message type="warning" className="mb-l">
            {intl('saenext.ImageSelectorField.cr.YouHaveNotActivatedContainer')}

            <a href={CachedData.confLink('feature:cr:url')} target="_blank" className="ml-s">
              {intl('saenext.ImageSelectorField.cr.ActivateNow')}
            </a>
          </Message>
        )}
      </div>
    </>
  );
});
