import { ConsoleContext, intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle, useContext } from 'react';
import { Select, CndTable, Search, Message, Balloon } from '@ali/cnd';
import {
  ListInstance,
  ListRepository,
  ListRepoTag,
  CrListNamespace,
  ListInstanceEndpoint,
} from '~/services/acr';
import { filter, find, isEmpty, map } from 'lodash';
import FeatureContext from '~/utils/featureContext';
import ExternalLink from '~/components/shared/ExternalLink';
import If from '~/components/shared/If';
import ImageAccelerate from '../ImageAccelerate';
import CachedData from '~/cache/common';

interface IProps {
  appType?: string;
  initValue: Record<string, string>;
  vpcId?: string;
  onChange?: (value: any) => void;
  hideAcreeAccelerate?: boolean;
}

export default forwardRef(({ initValue, vpcId, appType, onChange, hideAcreeAccelerate }: IProps, ref) => {
  const { registry: defaultDomain = ''} = initValue || {}
  const [key, setKey] = useState(0); // 强制刷新
  const [loading, setLoading] = useState(false);

  const [showInstanceError, setInstanceError] = useState<any>('');
  const [instanceEndpoint, setInstanceEndpoint] = useState({ vpc: '', domain: defaultDomain });

  const [instances, setInstances] = useState([]);
  const [namespaceList, setNamespaceList] = useState([]);
  const [repoResult, setRepoResult] = useState({ repos: [], total: 0 });
  const [repoParams, setRepoParams] = useState({
    PageNo: 1,
    PageSize: 10,
    RepoName: initValue?.repoName,
    RepoStatus: 'NORMAL',
  });
  const [useTag, setUseTag] = useState(
    isEmpty(initValue) ? { instanceId: '', namespace: '', repoName: '', tag: '' } : initValue,
  );
  // /fc-accl-images-dev-cluster-5/1799010890651873_fc-test_busybox-digest-change:fc-v20201008-8aa9f9ed111ad05073140986bfc85111c880fb2b5ddcc53ca2a8a45acccfbae5_containerd_accelerated
  const [enableAccelerate, setEnableAccelerate] = useState(!!initValue.enableImageAccl);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const { feature } = useContext(FeatureContext);
  const acclEnabled = !!feature.user_acree_image_accl;

  useEffect(() => {
    getInstanceList();
  }, [initValue]);

  useImperativeHandle(ref, () => ({
    submit: () => {
      if (!useTag.instanceId) {
        return { error: intl('saenext.ImageSelectorField.cr.cr.SelectAnEnterpriseInstance') };
      } else if (!useTag.namespace) {
        return { error: intl('saenext.ImageSelectorField.cr.cr.SelectANamespace') };
      } else if (!useTag.tag) {
        return { error: intl('saenext.ImageSelectorField.cr.cr.SelectAnImageVersion') };
      }

      const enableImageAccl =
        (appType === 'micro'|| appType === 'job') && useTag.instanceId ? enableAccelerate : undefined;

      return {
        vpc: instanceEndpoint.vpc,
        instanceId: useTag.instanceId,
        image: `${instanceEndpoint.domain}/${useTag.namespace}/${useTag.repoName}:${useTag.tag}`,
        enableImageAccl,
      };
    },
  }));

  const getInstanceList = async () => {
    const result = await ListInstance();
    setInstances(result);

    if (result.length === 0) {
      setInstanceError(
        <>
          {intl('saenext.ImageSelectorField.cr.cr.YouHaveNotPurchasedAn')}

          <ExternalLink
            label={intl('saenext.ImageSelectorField.cr.cr.BuyNow')}
            url={`${CachedData.confLink('feature:common-buy')}/?commodityCode=acr_ee_public_cn&regionId=${regionId}`}
          />

          <ExternalLink
            className="ml-s"
            label={intl('saenext.ImageSelectorField.cr.cr.ProductDetails')}
            url={CachedData.confLink('help:acr:what-is-container-registry')}
          />
        </>,
      );
      return;
    }

    // 处理回显
    const findInstanceIdResult = find(
      result,
      (instance) => instance.InstanceName === initValue.instanceName,
    );
    if (findInstanceIdResult && initValue.tag) {
      const instanceId = findInstanceIdResult.InstanceId;
      setUseTag({ ...useTag, instanceId });
      const namespaceResult = await CrListNamespace({ InstanceId: instanceId });
      const findNamespace = find(
        namespaceResult,
        (item) => item.NamespaceName === initValue.namespace,
      );
      if (findNamespace) {
        const { Endpoints: endpoints, Message: message } = await ListInstanceEndpoint({
          InstanceId: instanceId,
        });
        if (message) {
          setInstanceError(message);
          return;
        }

        const vpcResult = find(endpoints, (endpoint) => endpoint.EndpointType === 'vpc') || {};
        if (vpcResult?.LinkedVpcs && vpcResult?.LinkedVpcs?.length === 0) {
          setInstanceError(intl('saenext.ImageSelectorField.cr.cr.TheEnterpriseInstanceIsNot'));
          return;
        }

        if (appType === 'micro' && !find(vpcResult?.LinkedVpcs, { VpcId: vpcId })) {
          setInstanceError(intl('saenext.ImageSelectorField.cr.cr.NoNamespaceIsSelectedOr'));
          return;
        }

        const listRepo = await ListRepository({
          ...repoParams,
          InstanceId: instanceId,
          RepoNamespaceName: initValue.namespace,
        });

        const findResult = find(
          listRepo.Repositories,
          (item) => item.RepoName === initValue.repoName,
        );

        if (!findResult) {
          const findRepoList = await ListRepository({
            ...repoParams,
            InstanceId: instanceId,
            RepoNamespaceName: initValue.namespace,
            RepoName: initValue.repoName,
          });
          const findRepo = find(
            findRepoList?.Repositories,
            (item) => item.RepoName === initValue.repoName,
          );
          if (findRepo) {
            listRepo.Repositories.unshift(findRepo);
          }
        }

        setRepoResult({
          total: listRepo.TotalCount,
          repos: listRepo.Repositories,
        });
        setInstanceEndpoint({
          domain: vpcResult.Domains[0].Domain,
          vpc: map(vpcResult.LinkedVpcs, 'VpcId')?.join(','),
        });
      }

      setNamespaceList(namespaceResult);
    }
  };

  const getListRepo = async (params, namespace?: string) => {
    await setLoading(true);
    const result = await ListRepository({
      ...params,
      InstanceId: useTag.instanceId,
      RepoNamespaceName: namespace || useTag.namespace,
    });
    setRepoResult({ total: result.TotalCount, repos: result.Repositories });

    setLoading(false);
  };

  const onInstanceChange = async (instanceId: string) => {
    const init = (error, result, endpoint) => {
      setRepoResult({ repos: [], total: 0 });
      setRepoParams({ ...repoParams, PageNo: 1, RepoName: '' });
      setUseTag({ instanceId, namespace: '', repoName: '', tag: '' });

      setInstanceEndpoint(endpoint);
      setInstanceError(error);
      setNamespaceList(result);
    };

    const { Endpoints: endpoints, Message: message } = await ListInstanceEndpoint({
      InstanceId: instanceId,
    });
    if (message) {
      init(message, [], {});
      return;
    }

    const vpcResult = find(endpoints, (endpoint) => endpoint.EndpointType === 'vpc') || {};
    if (vpcResult?.LinkedVpcs && vpcResult?.LinkedVpcs?.length === 0) {
      init(intl('saenext.ImageSelectorField.cr.cr.TheEnterpriseInstanceIsNot'), [], {});
      return;
    }

    if (appType === 'micro' && !find(vpcResult?.LinkedVpcs, { VpcId: vpcId })) {
      setInstanceError(intl('saenext.ImageSelectorField.cr.cr.NoNamespaceIsSelectedOr'));
      return;
    }

    const result = await CrListNamespace({ InstanceId: instanceId });
    init('', result, {
      domain: vpcResult.Domains[0].Domain,
      vpc: map(vpcResult.LinkedVpcs, 'VpcId')?.join(','),
    });
  };

  const onNamespaceChange = async (namespace: string) => {
    const u = { instanceId: useTag.instanceId, namespace, repoName: '', tag: '' };
    const p = { ...repoParams, PageNo: 1, RepoName: '' };
    getListRepo(p, namespace);

    setUseTag(u);
    setRepoParams(p);
  };

  const onSearchChange = async (v: string) => {
    const p = { ...repoParams, PageNo: 1, RepoName: v };
    getListRepo(p);
    setRepoParams(p);
  };

  const getListTags = async (index, { InstanceId, RepoId }) => {
    const result = await ListRepoTag({ InstanceId, RepoId });
    const resultFilter = filter(
      result,
      (item) =>
        !item.Tag?.startsWith('__ACR_EE_BUILD_SERVICE_INTERNAL_CACHE') &&
        !item.Tag?.startsWith('__ACR_EE_BUILD_SERVICE_INTERNAL_IMAGE_CACHE'),
    );
    const tags = resultFilter?.map((item) => ({
      value: item.Tag,
      label: item.Tag,
      ...item,
      disabled:
        (item.Tag?.endsWith('_accelerated') && !acclEnabled) ||
        item.Tag?.endsWith('_containerd_accelerated'),
    }));
    repoResult.repos[index].tags = tags;
    setRepoResult(repoResult);
    setKey(key + 1);
  };

  const tagItemRender = (item) => {
    if (item.disabled) {
      return (
        <Balloon.Tooltip trigger={item.label}>
          <span>
            {appType === 'micro'
              ? intl('saenext.ImageSelectorField.cr.cr.InGrayScalePleaseSubmit')
              : intl('saenext.ImageSelectorField.cr.cr.AcceleratedImagesAreNotSupported')}
          </span>
        </Balloon.Tooltip>
      );
    }
    return item.label;
  };

  const onPageSizeChange = (v) => {
    const p = { ...repoParams, PageNo: 1, PageSize: v };
    getListRepo(p);
    setRepoParams(p);
  };

  const onPageNoChange = (v) => {
    const p = { ...repoParams, PageNo: v };
    setRepoParams(p);
    getListRepo(p);
  };

  return (
    <>
      <div className="flex" style={{ paddingRight: 2 }}>
        <Select
          key={key}
          style={{ minWidth: 260 }}
          value={useTag.instanceId}
          label={
            intl('saenext.ImageSelectorField.cr.cr.EnterpriseInstanceList')
          }
          disabled={!instances.length || loading}
          dataSource={instances.map((item) => ({
            value: item.InstanceId,
            label: item.InstanceName,
          }))}
          onChange={onInstanceChange}
        />

        <Select
          style={{ minWidth: 260 }}
          value={useTag.namespace}
          label={
            intl('saenext.ImageSelectorField.cr.cr.ImageRepositoryNamespace')
          }
          className="ml-s"
          disabled={!namespaceList.length || loading}
          dataSource={namespaceList.map((item) => item.NamespaceName)}
          onChange={onNamespaceChange}
        />

        <Search
          style={{ flex: 1 }}
          label={intl('saenext.ImageSelectorField.cr.cr.ImageRepositoryName')}
          className="ml-s"
          placeholder={intl('saenext.ImageSelectorField.cr.cr.EnterTheImageRepositoryName')}
          disabled={!useTag.namespace}
          value={repoParams.RepoName}
          onChange={onSearchChange}
          hasClear
        />
      </div>
      {showInstanceError && (
        <Message className="mt" type="warning">
          {showInstanceError}
        </Message>
      )}

      <CndTable
        loading={loading}
        dataSource={repoResult.repos}
        className="mt"
        primaryKey="RepoName"
        pagination={{
          current: repoParams.PageNo,
          pageSize: repoParams.PageSize,
          total: repoResult.total,
          onChange: onPageNoChange,
          onPageSizeChange,
        }}
        // @ts-ignore
        columns={[
          {
            title: intl('saenext.ImageSelectorField.cr.cr.ImageRepositoryName'),
            dataIndex: 'RepoName',
          },
          {
            title: intl('saenext.ImageSelectorField.cr.cr.Type'),
            dataIndex: 'RepoType',
          },
          {
            title: intl('saenext.ImageSelectorField.cr.cr.WarehouseStatus'),
            dataIndex: 'RepoStatus',
          },
          {
            title: intl('saenext.ImageSelectorField.cr.cr.SelectImageVersion'),
            dataIndex: 'RepoName',
            // align: 'right',
            cell: (value, index, record) => {
              return (
                <Select
                  style={{ width: 350 }}
                  value={value === useTag.repoName ? useTag.tag : ''}
                  dataSource={record.tags}
                  itemRender={tagItemRender}
                  onVisibleChange={(visible) => visible && getListTags(index, record)}
                  onChange={(v:any) => {
                    setUseTag({
                      instanceId: useTag.instanceId,
                      namespace: useTag.namespace,
                      repoName: value,
                      tag: v,
                      repoId: record.RepoId
                    });
                    onChange?.({
                      instanceId: useTag.instanceId,
                      image: `${instanceEndpoint.domain}/${useTag.namespace}/${value}:${v}`,
                      enableImageAccl:(appType === 'micro'|| appType === 'job') && useTag.instanceId ? enableAccelerate : undefined
                    })
                    if (v.endsWith('_accelerated') && !hideAcreeAccelerate) {
                      setEnableAccelerate(true);
                    }
                  }}
                />
              );
            },
          },
        ]}
      />

      <If condition={appType === 'micro' && !hideAcreeAccelerate}>
        <ImageAccelerate
          value={enableAccelerate}
          onChange={setEnableAccelerate}
          acclEnabled={acclEnabled}
          imageInfo={useTag}
        />
      </If>
    </>
  );
});
