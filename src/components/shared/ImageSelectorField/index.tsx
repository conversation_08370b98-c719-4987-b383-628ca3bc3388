import { intl } from '@ali/cnd';
import React from 'react';
import { ConfigProvider } from '@ali/deep';
import ImageSelector from './entry';
import BaseField from '@ali/deep-form-helper';

class ImageSelectorField extends BaseField {
  props;
  static displayName = 'ImageSelectorField';

  static propTypes = {
    ...BaseField.propTypes,
  };

  static defaultProps = {
    ...BaseField.defaultProps,
  };

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...fieldProps,
      placeholder:
        this.props.placeholder || intl('saenext.shared.ImageSelectorField.SelectAnImage'),
    };

    return <ImageSelector {...newProps} />;
  }
}

export default ConfigProvider.config(ImageSelectorField as any);
