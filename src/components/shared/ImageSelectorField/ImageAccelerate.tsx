import React, { useEffect, useState } from 'react';
import { Collapse, intl, Message, Switch, ToolTipCondition } from '@ali/cnd';
import SubTitle from '~/components/shared/SubTitle';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';
import services from '~/services';

const ImageAccelerate = (props) => {
  const { value, onChange, acclEnabled, imageInfo } = props;

  const [imageIndexOnly, setImageIndexOnly] = useState(false);

  useEffect(() => {
    if (value && imageInfo.tag) {
      handleRepoAccelerate();
    }
  }, [JSON.stringify(imageInfo), value]);

  const handleRepoAccelerate = async () => {
    const res = await getRepoAccelerateStatus();

    if (res?.Code !== 'success') return;

    if (res?.Parameters?.ImageIndexOnly === true) {
      setImageIndexOnly(true);
      onChange(false);
      return;
    }

    if (res?.ArtifactType === 'ACCELERATED_IMAGE') return;

    const enableAccelerate = await enableRepoAccelerate();
    if (!enableAccelerate) {
      onChange(false);
    }
  };

  const getRepoAccelerateStatus = async () => {
    const res = await services.GetArtifactBuildRule({
      InstanceId: imageInfo.instanceId,
      ScopeType: 'REPOSITORY',
      ScopeId: imageInfo.repoId,
      ArtifactType: 'ACCELERATED_IMAGE',
    });

    return res;
  };

  const enableRepoAccelerate = async () => {
    const res = await services.CreateArtifactBuildRule({
      InstanceId: imageInfo.instanceId,
      ScopeType: 'REPOSITORY',
      ScopeId: imageInfo.repoId,
      ArtifactType: 'ACCELERATED_IMAGE',
    });
    if (res?.Code === 'success') {
      return true;
    } else {
      Message.error(
        intl(
          'saenext.shared.ImageSelectorField.ImageAccelerate.ImageRepositoryImageAccelerationFailed',
        ),
      );
      return false;
    }
  };

  return (
    <Collapse defaultExpandedKeys={['0']}>
      <Collapse.Panel
        title={
          <SubTitle
            title={intl('saenext.shared.ImageSelectorField.ImageAccelerate.ImageAcceleration')}
            subTitle={intl(
              'saenext.shared.ImageSelectorField.ImageAccelerate.ThroughImageAccelerationTheStartup',
            )}
          />
        }
      >
        <p>
          {intl('saenext.shared.ImageSelectorField.ImageAccelerate.ByUsingOnDemandLoading')}

          <a href={CachedData.confLink('help:sae:how-to-run-images-in-sae-choice')} target="_blank">
            {intl('saenext.shared.ImageSelectorField.ImageAccelerate.HowToUseTheEnterprise')}
          </a>
        </p>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {intl('saenext.shared.ImageSelectorField.ImageAccelerate.EnableImageAcceleration')}

          <ToolTipCondition
            show={imageIndexOnly}
            tip={intl('saenext.shared.ImageSelectorField.ImageAccelerate.TheImageRepositoryIsIn')}
          >
            <Switch checked={value} onChange={onChange} disabled={!acclEnabled || imageIndexOnly} />
          </ToolTipCondition>

          <If condition={!acclEnabled}>
            <span style={{ marginLeft: 16, color: '#9c9d9c' }}>
              {intl('saenext.shared.ImageSelectorField.ImageAccelerate.InGrayScalePleaseSubmit')}
            </span>
          </If>
        </div>
      </Collapse.Panel>
    </Collapse>
  );
};

export default ImageAccelerate;
