import { intl } from '@ali/cnd';
import _ from 'lodash';
import { LANGUAGE_TYPE } from '../DeploySelectorField/constant';

export enum Type {
  cr = 'cr',
  demo = 'demo',
  custom = 'custom',
  otherAccount = 'otherAccount',
  private = 'private',
}

/**
 * 个人版实例
 * @param image e.g.: registry.cn-shanghai.aliyuncs.com/fc-demo2/test:v1
 */
export const isAcrRegistry = (regionId: string, image: string = ''): boolean => {
  const [registry] = image.split('/');
  const url = `registry.${regionId}.aliyuncs.com`;
  const urlVpc = `registry-vpc.${regionId}.aliyuncs.com`;

  const personalRegString = `^crpi-(\\w+)(-vpc)?\\.${regionId}\\.(?:personal\\.cr\\.aliyuncs\\.com)$`;
  const personalReg = new RegExp(personalRegString);

  return registry === url || registry === urlVpc || personalReg.test(registry);
};
export const parsingAcr = (regionId: string, image: string) => {
  if (!isAcrRegistry(regionId, image)) {
    return {};
  }
  const [, repoNamespace, repoNameAngTag = ''] = image.split('/');
  const [repoName = '', tag = ''] = repoNameAngTag.split(':');
  return { repoNamespace, repoName, tag };
};

/**
 * 企业版实例
 * @param image e.g.: fc-ci-acr-ee-registry-vpc.cn-shanghai.cr.aliyuncs.com/fc-test-accl-images/cuda-samples:v1
 */
export const isAcreeRegistry = (regionId: string, image: string = ''): boolean => {
  const [registry] = image.split('/');
  const url = `-registry.${regionId}.cr.aliyuncs.com`;
  const urlVpc = `-registry-vpc.${regionId}.cr.aliyuncs.com`;

  return registry.endsWith(url) || registry.endsWith(urlVpc);
};
export const parsingAcree = (regionId: string, image: string = '') => {
  if (!isAcreeRegistry(regionId, image)) {
    return {};
  }

  const [registry, namespace, ...repoNameAngTag] = image.split('/');
  const [repoName = '', tag = ''] = repoNameAngTag.join('/').split(':');

  let [instanceName] = registry.split('.');
  const sliceLength = instanceName.endsWith('-registry-vpc') ? -13 : -9;

  return {
    registry,
    instanceName: instanceName.slice(0, sliceLength),
    namespace,
    repoName,
    tag,
    type: 'cr',
  };
};

const palworldImageTags = _.get(window, 'ALIYUN_CONSOLE_GLOBAL.palworldImageTags', []);

// Web应用 Dome 示例
export const DOME_IMAGE = [
  // {
  //   port: 9000,
  //   tag: 'custom-container-http-examples:springboot',
  //   description: 'SpringBoot Web 应用示例镜像', // intl('image.demo.http.springboot'),
  //   gitLink:
  //     'https://github.com/devsapp/start-fc/tree/master/custom-container-function/fc-custom-container-http-springboot/src/code',
  // },
  // {
  //   port: 80,
  //   tag: 'custom-container-http-examples:aspdotnetcore',
  //   description: 'ASP.NET Web 应用示例镜像', // intl('image.demo.http.aspdotnetcore'),
  //   gitLink:
  //     'https://github.com/devsapp/start-fc/tree/master/custom-container-function/fc-custom-container-http-aspdotnetcore/src/code',
  // },
  {
    type: 'web',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: intl('saenext.shared.ImageSelectorField.utils.ExpressSampleImage'),
    port: 8080,
    tags: ['web-express-helloworld-v1.0', 'web-express-helloworld-blue-v1.0'],
  },
  {
    type: 'web',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: 'SpringBoot',
    port: 8080,
    tags: ['web-springboot-helloworld-v1.0'],
  },
  {
    type: 'web',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: 'ThinkPHP',
    port: 8080,
    tags: ['web-php-tinkphp-v1.0'],
  },
  {
    type: 'web',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: 'PHP Laravel',
    port: 8080,
    tags: ['web-php-laravel-v1.0'],
  },
  {
    type: 'web',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: 'Python Flask',
    port: 8080,
    tags: ['web-python-flask-v1.0'],
  },
  {
    type: 'web',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: 'Python django',
    port: 8080,
    tags: ['web-python-django-v1.0'],
  },
  {
    type: 'web',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: 'Golang Gin',
    port: 8080,
    tags: ['web-golang-gin-v1.0'],
  },
  {
    type: 'web',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: intl('saenext.shared.ImageSelectorField.utils.AiIdPhoto'),
    port: 7860,
    tags: ['web-HivisionIDPhotos-v1.0'],
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: intl('saenext.shared.ImageSelectorField.utils.MicroserviceApplicationProvider'),
    tags: ['microservice-java-provider-v1.0'],
    lang: LANGUAGE_TYPE.JAVA,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: intl('saenext.shared.ImageSelectorField.utils.MicroserviceApplicationsConsumers'),
    tags: ['microservice-java-consumer-v1.0'],
    lang: LANGUAGE_TYPE.JAVA,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: intl('saenext.shared.ImageSelectorField.utils.WebApplicationsBackend'),
    tags: ['web-springboot-hellosae-v1.0'],
    lang: LANGUAGE_TYPE.JAVA,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    tags: ['saa-jmanus-main-abae01eb-06231705'],
    lang: LANGUAGE_TYPE.JAVA,
  },
  // {
  //   type: 'micro',
  //   repoNamespace: 'sae-serverless-demo',
  //   repoName: 'provider',
  //   tags: ['1.0'],
  //   lang: LANGUAGE_TYPE.JAVA,
  // },
  // {
  //   type: 'micro',
  //   repoNamespace: 'sae-demo',
  //   description: intl('saenext.shared.ImageSelectorField.utils.PalworldGameImage'),
  //   repoName: 'palworld',
  //   tags: palworldImageTags,
  //   lang: LANGUAGE_TYPE.JAVA,
  // },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    tags: ['microservice-php-demo-v1.0'],
    description: 'PHP Demo',
    lang: LANGUAGE_TYPE.PHP,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    tags: ['microservice-python-demo-v1.0'],
    description: 'Python Flask',
    lang: LANGUAGE_TYPE.PYTHON,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: intl('saenext.shared.ImageSelectorField.utils.AiIdPhoto'),
    port: 7860,
    tags: ['web-HivisionIDPhotos-v1.0'],
    lang: LANGUAGE_TYPE.PYTHON,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    tags: ['microservice-golang-gin-v1.0'],
    description: 'Golang Gin',
    lang: LANGUAGE_TYPE.GO,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    tags: [
      'microservice-dotnet3.1-hello-v1.0',
      'microservice-dotnet5.0-hello-v1.0',
      'microservice-dotnet6.0-hello-v1.0',
      'microservice-dotnet7.0-hello-v1.0',
      'microservice-dotnet8.0-hello-v1.0',
      'microservice-dotnet9.0-hello-v1.0',
    ],
    description: '.Net HelloWorld',
    lang: LANGUAGE_TYPE.DOTNET,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: intl('saenext.shared.ImageSelectorField.utils.WebApplicationsFrontend'),
    tags: ['web-dashboard-hellosae-v1.0'],
    lang: LANGUAGE_TYPE.OTHER,
  },
  {
    type: 'micro',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    description: intl('saenext.shared.ImageSelectorField.utils.Chatbot'),
    tags: ['lobe-chat-v1.0'],
    lang: LANGUAGE_TYPE.OTHER,
  },
  {
    type: 'job',
    repoNamespace: 'sae-serverless-demo',
    repoName: 'sae-demo',
    tags: ['job-java-demo-v1.0'],
  }
];

// 微服务应用 Dome 示例
export const DOME_IMAGE_MICRO = [
  {
    repoNamespace: 'sae-serverless-demo',
    repoName: 'provider',
    tags: ['1.0'],
  },
  {
    repoNamespace: 'sae-serverless-demo',
    repoName: 'consumer',
    tags: ['1.0'],
  },
];

/**
 * 合成 dome 示例 url
 * @param regionId
 * @param tag
 * @returns
 */
export const syntheticDomeURL = (
  repoNamespace: string,
  regionId: string,
  repoName: string,
  tag: string,
): string => `registry.${regionId}.aliyuncs.com/${repoNamespace}/${repoName}:${tag}`;
/**
 * 解析 dome 示例 url
 * @param regionId
 * @param image
 * @returns
 */
export const parsingDomeURL = (regionId: string, image: string) => {
  if (
    !image.startsWith(`registry.${regionId}.aliyuncs.com/sae-serverless-demo/`) &&
    !image.startsWith(`registry.${regionId}.aliyuncs.com/serverless_devs/`) &&
    !image.startsWith(`registry.${regionId}.aliyuncs.com/sae-demo-image/`) &&
    !image.startsWith(`registry.${regionId}.aliyuncs.com/sae-demo/`) &&
    !image.startsWith(`registry-vpc.${regionId}.aliyuncs.com/sae-serverless-demo/`)
  ) {
    return {};
  }

  const [, repoNamespace, repoNameTag] = image.split('/');
  const [repoName, tag] = repoNameTag.split(':');

  for (const domeImage of DOME_IMAGE) {
    if (
      // 兼容之前的老镜像地址
      (domeImage.repoNamespace === repoNamespace || repoNamespace === 'serverless_devs') &&
      domeImage.repoName === repoName &&
      domeImage.tags.includes(tag)
    ) {
      return { repoNamespace, tag, repoName, port: domeImage.port };
    }
  }
  return {};
};

/**
 * 解析传入的 image 信息
 * @param regionId
 * @param image
 * @returns 和各种信息的 useTag 一致
 */
export const parseUrl = (regionId: string, image: string, registryConfig?: any) => {
  // 需要先判断是否是 demo 示例
  const domeResult = parsingDomeURL(regionId, image);
  if (domeResult.tag) {
    return {
      type: Type.demo,
      parseResult: domeResult,
    };
  }

  if (isAcrRegistry(regionId, image)) {
    return {
      type: Type.cr,
      parseResult: parsingAcr(regionId, image),
    };
  } else if (isAcreeRegistry(regionId, image)) {
    return {
      type: Type.cr,
      parseResult: parsingAcree(regionId, image),
    };
  } else if (registryConfig || !image.includes('aliyuncs.com')) {
    return {
      type: Type.private,
      parseResult: {
        image,
        registryConfig,
      },
    }
  }

  return { type: Type.custom, parseResult: { image } };
};

export default parseUrl;
