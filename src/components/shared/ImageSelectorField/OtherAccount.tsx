import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';
import { ConsoleContext, Field, Form, Input, Message, Radio, Select, intl } from '@ali/cnd';
import RefreshButton from '../RefreshButton';
import * as services from '~/services';
import { filter, map, split } from 'lodash';
import CachedData from '~/cache/common';

const FormItem = Form.Item;

const OtherAccount = (props, ref) => {
  const { initValue, namespaceId, onChange: onImageChange } = props;

  const [secretList, setSecretList] = useState([]);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const field = Field.useField();

  const { isUseOtherAcr, otherAcrType } = field.getValues() as any;

  useEffect(() => {
    const {
      image = '',
      instanceId = '',
      acrAssumeRoleArn = '',
      imagePullSecrets = '',
    } = initValue || {};
    const firstValidSecret = getFirstValidSecret(imagePullSecrets);
    field.setValues({
      isUseOtherAcr: !!instanceId || false,
      instanceId: instanceId,
      otherAcrType: imagePullSecrets ? 'secret' : 'ram',
      acrAssumeRoleArn,
      imagePullSecrets: firstValidSecret,
      image: image,
    });
  }, [initValue]);

  useEffect(()=>{
    const { image, acrAssumeRoleArn, imagePullSecrets, instanceId } = field.getValues();
    onImageChange &&
      onImageChange({
        image,
        acrAssumeRoleArn,
        imagePullSecrets,
        instanceId,
      });
  },[JSON.stringify(field.getValues())])

  const getFirstValidSecret = (imagePullSecrets) => {
    const secretsArr = split(imagePullSecrets, ',');
    const [secretId] = secretsArr.filter(Boolean);
    return secretId;
  };

  useEffect(() => {
    otherAcrType === 'secret' && getSecretList();
  }, [otherAcrType]);

  const validate = () => {
    return new Promise((resolve) => {
      field.validate((error, values) => {
        if (error) {
          resolve(false);
          return;
        }
        resolve(values);
      });
    });
  };

  useImperativeHandle(ref, () => ({
    submit: async () => {
      const values = (await validate()) as any;
      if (!values) {
        return { error: intl('saenext.shared.ImageSelectorField.OtherAccount.Complete') };
      }

      const { instanceId, image, acrAssumeRoleArn, imagePullSecrets } = values;

      return {
        instanceId,
        image,
        acrAssumeRoleArn,
        imagePullSecrets,
      };
    },
  }));

  const getSecretList = async () => {
    const { Data } = await services.ListSecrets({
      NamespaceId: namespaceId,
    });
    const { Secrets = [] } = Data;
    const dockerSecrets = filter(Secrets, { SecretType: 'kubernetes.io/dockerconfigjson' });
    const secretList = map(dockerSecrets, (item) => {
      const { SecretId, SecretName } = item;
      return {
        value: SecretId,
        label: SecretName,
      };
    });
    setSecretList(secretList);
  };

  const onOtherAcrTypeChange = (val) => {
    field.reset(['acrAssumeRoleArn', 'imagePullSecrets']);
  };

  return (
    <>
      <Message type="warning">
        {intl('saenext.shared.ImageSelectorField.OtherAccount.NoteBecauseTheImagePull')}

        <br />
        {isUseOtherAcr
          ? intl(
              'saenext.shared.ImageSelectorField.OtherAccount.FunctionUsageConstraintsAssumerolearnIs',
            )
          : intl('saenext.shared.ImageSelectorField.OtherAccount.FunctionUsageConstraintsAUser')}
      </Message>
      <Form
        field={field}
        labelAlign="left"
        labelCol={{ fixedSpan: 8 }}
        // @ts-ignore
        wrapperCol={{ span: 16, width: 500 }}
        className="mt ml"
      >
        <Form.Item
          label={intl('saenext.shared.ImageSelectorField.OtherAccount.ImageServiceVersion')}
          fullWidth
          required
        >
          <Radio.Group name="isUseOtherAcr">
            <Radio id="false" value={false}>
              {intl('saenext.shared.ImageSelectorField.OtherAccount.ImageServicePersonalEdition')}
            </Radio>
            <Radio id="true" value={true}>
              {intl('saenext.shared.ImageSelectorField.OtherAccount.EnterpriseEdition')}
            </Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label={intl('saenext.shared.ImageSelectorField.OtherAccount.ImageType')}
          required
          fullWidth
        >
          <Radio.Group name="otherAcrType" defaultValue="ram" onChange={onOtherAcrTypeChange}>
            <Radio id="ram" value={'ram'}>
              Ram Role
            </Radio>
            <Radio id="secret" value={'secret'}>
              {intl('saenext.shared.ImageSelectorField.OtherAccount.StaticUsernameAndPassword')}
            </Radio>
          </Radio.Group>
        </Form.Item>
        {isUseOtherAcr && (
          <FormItem
            label={intl('saenext.shared.ImageSelectorField.OtherAccount.TheIdOfTheEnterprise')}
            extra={
              <a href={`${CachedData.confLink('feature:cr:url')}/${regionId}/instances`} target="_blank">
                {intl('saenext.shared.ImageSelectorField.OtherAccount.HowToViewTheEnterprise')}
              </a>
            }
            required
            requiredMessage={intl(
              'saenext.shared.ImageSelectorField.OtherAccount.EnterTheEnterpriseInstanceId',
            )}
          >
            <Input
              name="instanceId"
              placeholder={intl(
                'saenext.shared.ImageSelectorField.OtherAccount.EnterAnEnterpriseInstanceId',
              )}
            />
          </FormItem>
        )}

        {otherAcrType === 'ram' && (
          <FormItem
            label="acrAssumeRoleArn"
            extra={
              <a href={CachedData.confLink('help:sae:pull-alibaba-cloud-images-across-accounts')} target="_blank">
                {intl(
                  'saenext.shared.ImageSelectorField.OtherAccount.HowToObtainAcrassumerolearnIn',
                )}
              </a>
            }
            required
            requiredMessage={intl(
              'saenext.shared.ImageSelectorField.OtherAccount.EnterAcrassumerolearn',
            )}
          >
            <Input name="acrAssumeRoleArn" placeholder="acs:ram::123456:role/acr-pull-role" />
          </FormItem>
        )}

        {otherAcrType === 'secret' && (
          <FormItem
            label={intl(
              'saenext.shared.ImageSelectorField.OtherAccount.AConfidentialDictionaryThatStores',
            )}
            extra={
              <a href={CachedData.confLink('help:sae:manage-a-kubernetes-secret')} target="_blank">
                {intl('saenext.shared.ImageSelectorField.OtherAccount.HowToStoreTheUser')}
              </a>
            }
            fullWidth
          >
            <Select name="imagePullSecrets" dataSource={secretList} />

            <RefreshButton className="ml-l timestamp" handler={getSecretList} />
          </FormItem>
        )}

        <FormItem
          label={intl('saenext.shared.ImageSelectorField.OtherAccount.FullImageRepositoryAddress')}
          required
        >
          <Input
            {...field.init('image', {
              rules: [
                {
                  required: true,
                  message: intl(
                    'saenext.shared.ImageSelectorField.OtherAccount.EnterTheFullImageRepository',
                  ),
                },
                {
                  validator: (_rule, value: string, callback) => {
                    if (/\s/.test(value)) {
                      callback(
                        intl(
                          'saenext.shared.ImageSelectorField.OtherAccount.TheImageAddressCannotContain',
                        ),
                      );
                    } else {
                      callback();
                    }
                  },
                },
              ],
            })}
            placeholder={intl(
              'saenext.shared.ImageSelectorField.OtherAccount.EnterTheFullImageRepository',
            )}
          />
        </FormItem>
      </Form>
    </>
  );
};

export default forwardRef(OtherAccount);
