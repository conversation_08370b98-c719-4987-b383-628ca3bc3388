import { intl } from '@ali/cnd';
import React, { useContext, useState, forwardRef, useImperativeHandle } from 'react';
import { CndTable, ConsoleContext, Select } from '@ali/cnd';
import { filter, isEmpty } from 'lodash';
import { DOME_IMAGE, syntheticDomeURL } from './utils';

interface IProps {
  initValue: Record<string, string>;
  appType?: 'web' | 'micro' | 'job';
  value?: any;
  onChange?: (value: any) => void;
  microImageLanguage?: string;
}

export default forwardRef((props: IProps, ref) => {
  const { initValue, appType = 'web', microImageLanguage, onChange: onImageChange } = props;
  const [useTag, setUseTag] = useState(
    isEmpty(initValue) ? { port: 8080, repoNamespace: '', repoName: '', tag: '' } : initValue,
  );

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  useImperativeHandle(ref, () => ({
    submit: () => {
      if (useTag.tag) {
        return {
          port: useTag.port,
          image: syntheticDomeURL(useTag.repoNamespace, regionId, useTag.repoName, useTag.tag),
          imageType: 'demo',
        };
      }

      return { error: intl('saenext.shared.ImageSelectorField.demo.SelectASampleImage') };
    },
  }));

  const demoTypeFilter = filter(DOME_IMAGE, (item) => item.type === appType && (!microImageLanguage || item.lang === microImageLanguage));

  return (
    <CndTable primaryKey="repoName" dataSource={demoTypeFilter}>
      <CndTable.Column
        title={intl('saenext.shared.ImageSelectorField.demo.SampleImageRepositoryNamespace')}
        width="30%"
        dataIndex="repoNamespace"
      />

      <CndTable.Column
        title={intl('saenext.shared.ImageSelectorField.demo.SampleImageRepository')}
        width="20%"
        dataIndex="repoName"
      />

      <CndTable.Column
        title={intl('saenext.shared.ImageSelectorField.demo.SampleImageDescription')}
        dataIndex="description"
        width="20%"
        cell={(value) => (<>{value || '--'}</>)}
      />

      <CndTable.Column
        title={intl('saenext.shared.ImageSelectorField.demo.SelectImageVersion')}
        dataIndex="repoName"
        width="30%"
        // @ts-ignore
        cell={(value, index, record) => {
          return (
            <Select
              style={{ width: '100%' }}
              dataSource={record.tags}
              value={
                value === useTag.repoName && record.tags?.includes(useTag.tag) ? useTag.tag : ''
              }
              onChange={(v: string) => {
                setUseTag({
                  repoNamespace: record.repoNamespace,
                  repoName: value,
                  tag: v,
                  port: record.port,
                });
                onImageChange &&
                  onImageChange({
                    image:
                      appType === 'job'
                        ? `registry-vpc.${regionId}.aliyuncs.com/${record.repoNamespace}/${value}:${v}`
                        : `registry.${regionId}.aliyuncs.com/${record.repoNamespace}/${value}:${v}`,
                  });
              }}
            />
          );
        }}
      />
    </CndTable>
  );
});
