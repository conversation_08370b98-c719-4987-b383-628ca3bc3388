import { intl } from '@ali/cnd';
import React, { useContext, useState, useRef } from 'react';
import { Input, Button, SlidePanel, Tab, ConsoleContext, Message } from '@ali/cnd';
import { set } from 'lodash';
import parseUrl, { Type } from './utils';
import Cr from './cr';
import Demo from './demo';
import Custom from './custom';
import { GetRepo } from '~/services/acr';

interface ImageSelectorProps {
  onChange: (value) => void;
  value: Record<string, string>;
  placeholder: string;
  size: 'small' | 'medium' | 'large';
}

export default ({ size, placeholder, value, onChange }: ImageSelectorProps) => {
  const ref = useRef(null);
  const [showing, setShowing] = useState(false);
  const [parseConfig, setParseConfig] = useState({ type: Type.cr, parseResult: {} });

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const closeImageSlide = () => setShowing(false);

  const showImageSlide = async () => {
    const { image, instanceId } = value || {};
    if (image) {
      const result = parseUrl(regionId, image);
      if (result.type === Type.cr) {
        if (image.indexOf(`${regionId}.cr.aliyuncs.com`) > -1) {
          // 企业镜像
          // @ts-ignore
          result.parseResult.type = 'cr';
        } else {
          // 个人版镜像
          // @ts-ignore
          result.parseResult.type = 'acr';
        }
        if (instanceId) {
          set(result, 'parseResult.instanceId', instanceId);
        }
        // @ts-ignore: 个人版镜像验证是否存在
        const { repoNamespace, repoName } = result.parseResult;
        if (repoNamespace) {
          try {
            await GetRepo({ RepoNamespace: repoNamespace, RepoName: repoName });
          } catch (_e) {
            // 获取不到信息强制转换到自定义
            result.type = Type.custom;
            result.parseResult = { image };
          }
        }
      }
      setParseConfig(result);
    }
    setShowing(true);
  };

  const onOk = () => {
    const result = ref.current.submit();
    if (result.error) {
      Message.error(result.error);
      return;
    }
    onChange({ ...result, accelerationType: 'Default' });
    setShowing(false);
  };

  const onInputImage = (image) => {
    onChange({ image, accelerationType: 'Default' });
  };

  return (
    <>
      <Input
        size={size}
        placeholder={placeholder}
        value={value?.image}
        onChange={onInputImage}
        className="full-width"
        innerAfter={
          <Button type="primary" text className="mr-s" onClick={showImageSlide}>
            {intl('saenext.shared.ImageSelectorField.entry.Select')}
          </Button>
        }
      />

      <SlidePanel
        title={intl('saenext.shared.ImageSelectorField.entry.ModifyAnImage')}
        isShowing={showing}
        width="large"
        onClose={closeImageSlide}
        onCancel={closeImageSlide}
        onOk={onOk}
      >
        <Tab
          shape="wrapped"
          activeKey={parseConfig.type}
          className="mb"
          onChange={(key: Type) => setParseConfig({ type: key, parseResult: {} })}
        >
          <Tab.Item
            title={intl('saenext.shared.ImageSelectorField.entry.MyAlibabaCloudImage')}
            key={Type.cr}
          ></Tab.Item>
          <Tab.Item
            title={intl('saenext.shared.ImageSelectorField.entry.DemoImage')}
            key={Type.demo}
          ></Tab.Item>
          <Tab.Item
            title={intl('saenext.shared.ImageSelectorField.entry.CustomImage')}
            key={Type.custom}
          ></Tab.Item>
        </Tab>
        {parseConfig.type === Type.cr && <Cr ref={ref} initValue={parseConfig.parseResult} />}
        {parseConfig.type === Type.demo && (
          <Demo ref={ref} initValue={parseConfig.parseResult} appType="web" />
        )}
        {parseConfig.type === Type.custom && (
          <Custom ref={ref} initValue={parseConfig.parseResult} />
        )}
      </SlidePanel>
    </>
  );
};
