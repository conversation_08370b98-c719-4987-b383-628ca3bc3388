import React, { forwardRef, useImperativeHandle } from 'react';
import { Checkbox, Field, Form, Input, intl, Message } from '@ali/cnd';
import { cloneDeep } from 'lodash';

const PrivateRegistry = (props, ref) => {
  const { initValue } = props;

  useImperativeHandle(ref, () => ({
    submit: async () => {
      const { errors, values } = await field.validatePromise();

      if (errors) {
        return { error: intl('saenext.shared.ImageSelectorField.OtherAccount.Complete') };
      }

      return formatValues(values);
    },
  }));

  const parseValues = (values) => {
    const result = cloneDeep(values);
    const rootCaCertBase64 = result?.registryConfig?.certConfig?.rootCaCertBase64;
    if (rootCaCertBase64) {
      result.registryConfig.certConfig.rootCaCertBase64 = atob(rootCaCertBase64);
    }
    return result;
  };

  const formatValues = (values) => {
    const result = cloneDeep(values);
    const rootCaCertBase64 = result?.registryConfig?.certConfig?.rootCaCertBase64;
    if (rootCaCertBase64) {
      result.registryConfig.certConfig.rootCaCertBase64 = btoa(rootCaCertBase64);
    }
    return result;
  };

  const field = Field.useField({
    values: parseValues(initValue),
    parseName: true,
  });
  const { getValues } = field;

  return (
    <>
      <Form
        field={field}
        labelAlign="left"
        labelCol={{ fixedSpan: 8 }}
        // @ts-ignore
        wrapperCol={{ span: 16, width: 500 }}
        className="mt ml"
      >
        <Form.Item
          label={intl(
            'saenext.shared.ImageSelectorField.PrivateRegistry.ThirdPartyPrivateImageRepository',
          )}
          required
          useLabelForErrorMessage
        >
          <Input
            name="image"
            placeholder={intl(
              'saenext.shared.ImageSelectorField.PrivateRegistry.EnterTheAccessAddressOf',
            )}
          />

          <Message type="notice" className="mt">
            {intl('saenext.shared.ImageSelectorField.PrivateRegistry.MakeSureThatYourThird')}
            <br />
            {intl(
              'saenext.shared.ImageSelectorField.PrivateRegistry.CurrentlySupportedImageFormatsDocker',
            )}
          </Message>
        </Form.Item>
        <Form.Item label={intl('saenext.shared.ImageSelectorField.PrivateRegistry.LoginAccount')}>
          <Input name="registryConfig.authConfig.userName" />
        </Form.Item>
        <Form.Item
          label={intl('saenext.shared.ImageSelectorField.PrivateRegistry.AccountPassword')}
        >
          <Input.Password name="registryConfig.authConfig.password" />
        </Form.Item>
        <Form.Item
          label={intl(
            'saenext.shared.ImageSelectorField.PrivateRegistry.WhetherToSkipCertificateVerification',
          )}
        >
          <Checkbox name="registryConfig.certConfig.insecure" defaultChecked={false} />
        </Form.Item>
        <Form.Item
          label={intl(
            'saenext.shared.ImageSelectorField.PrivateRegistry.ImageRepositoryCaCertificate',
          )}
        >
          <Input.TextArea name="registryConfig.certConfig.rootCaCertBase64" />
        </Form.Item>
      </Form>
    </>
  );
};

export default forwardRef(PrivateRegistry);
