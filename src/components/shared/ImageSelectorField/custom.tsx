import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { intl, Input, Message, Form, Field, Radio, Select } from '@ali/cnd';
import RefreshButton from '../RefreshButton';
import * as services from '~/services';
import { filter, map, split } from 'lodash';
import CachedData from '~/cache/common';
const FormItem = Form.Item;

interface IProps {
  initValue: Record<string, string>;
  appType?: string;
  value?: any;
  onChange?: (value: any) => void;
  namespaceId?: string;
  showOtherAccountTip?: boolean;
  hideSecret?: boolean;
}

export default forwardRef(
  (
    {
      initValue,
      appType,
      onChange: onImageChange,
      namespaceId,
      showOtherAccountTip = false,
      hideSecret,
    }: IProps,
    ref,
  ) => {
    const [useTag, setUseTag] = useState({ image: initValue.image, imageType: 'custom' });

    useEffect(() => {
      setUseTag({ image: initValue.image, imageType: 'custom' });
      field.setValues({ ...initValue });
    }, [initValue]);
    const field = Field.useField();
    const [secretList, setSecretList] = useState([]);

    useEffect(() => {
      if (appType === 'micro' && namespaceId) {
        getSecretList();
        field.setValues({ ...initValue });
      }
    }, [appType]);

    useImperativeHandle(ref, () => ({
      submit: async () => {
        if (appType === 'web') {
          if (useTag.image) {
            return useTag;
          }
          return { error: intl('saenext.shared.ImageSelectorField.custom.EnterTheImageAddress') };
        }
        if (appType === 'micro') {
          let validateFlag = true;
          field.validate((error, values) => {
            if (error) {
              validateFlag = false;
            } else {
              validateFlag = true;
            }
          });
          if (validateFlag) {
            const values: any = field.getValues();
            return {
              ...values,
              imagePullSecrets: values.imagePullSecrets || '',
              imageType: 'custom',
            };
          } else {
            return {
              error: intl('saenext.shared.ImageSelectorField.custom.PleaseFillInTheComplete'),
            };
          }
        }
      },
    }));

    const getSecretList = async () => {
      const { Data } = await services.ListSecrets({
        NamespaceId: namespaceId,
      });
      const { Secrets = [] } = Data;
      const dockerSecrets = filter(Secrets, { SecretType: 'kubernetes.io/dockerconfigjson' });
      const secretList = map(dockerSecrets, (item) => {
        const { SecretId, SecretName } = item;
        return {
          value: SecretId,
          label: SecretName,
        };
      });
      setSecretList(secretList);
    };

    const onChange = (image: string) => {
      setUseTag({ ...useTag, image });
      onImageChange?.({ ...useTag, image });
    };

    return (
      <>
        {appType === 'web' && (
          <React.Fragment>
            <Message type="notice" className="mb-l">
              {intl('saenext.shared.ImageSelectorField.custom.CurrentlyOnlyAcrImagesIn')}
            </Message>
            <Input
              className="full-width"
              value={useTag.image}
              onChange={onChange}
              label={intl('saenext.shared.ImageSelectorField.custom.ImageAddress')}
              hasClear
              trim
            />

            <p>
              {intl('saenext.shared.ImageSelectorField.custom.EnterTheImageRepositoryAddress')}
              <a href={CachedData.confLink('help:sae:configure-a-nat-gateway')} target="_blank">
                {intl('saenext.shared.ImageSelectorField.custom.HowToAccessTheInternet')}
              </a>
            </p>
          </React.Fragment>
        )}

        {(appType === 'micro'||appType === 'job') && (
          <React.Fragment>
            <Message type="notice" className="mb-l">
              {intl('saenext.shared.ImageSelectorField.custom.CustomImagesSupportDockerHub')}
              <br />
              {showOtherAccountTip &&
                intl('saenext.shared.ImageSelectorField.custom.IfBothSidecarAndApplication')}
            </Message>
            <Form
              field={field}
              labelAlign="left"
              labelCol={{ fixedSpan: 8 }}
              // @ts-ignore
              wrapperCol={{ span: 16, width: 500 }}
            >
              <FormItem
                label={intl('saenext.shared.ImageSelectorField.custom.ImageNetwork')}
                required
                className={hideSecret ? 'none' : ''}
              >
                <Radio.Group name="customImageNetworkType" defaultValue={'internet'}>
                  <Radio id="internet" value="internet">
                    {intl('saenext.shared.ImageSelectorField.custom.PublicNetwork')}
                  </Radio>
                  <Radio id="intranet" value="intranet">
                    {intl('saenext.shared.ImageSelectorField.custom.PrivateNetwork')}
                  </Radio>
                </Radio.Group>
              </FormItem>
              <FormItem
                label={intl('saenext.shared.ImageSelectorField.custom.UsernameAndPassword')}
                extra={
                  <a href={CachedData.confLink('help:sae:manage-a-kubernetes-secret')} target="_blank">
                    {intl('saenext.shared.ImageSelectorField.OtherAccount.HowToStoreTheUser')}
                  </a>
                }
                fullWidth
                className={hideSecret ? 'none' : ''}
              >
                <Select
                  name="imagePullSecrets"
                  dataSource={secretList}
                  placeholder={intl(
                    'saenext.shared.ImageSelectorField.custom.SelectASecretToStore',
                  )}
                  hasClear
                />

                <RefreshButton className="ml-l timestamp" handler={getSecretList} />
              </FormItem>
              <FormItem
                label={intl('saenext.shared.ImageSelectorField.custom.ImageAddress')}
                extra={
                  <>
                    {field.getValue('customImageNetworkType') === 'internet' ? (
                      <>
                        {intl(
                          'saenext.shared.ImageSelectorField.custom.EnterTheFullImageRepository',
                        )}{' '}
                        <a
                          href={CachedData.confLink('help:sae:configure-a-nat-gateway')}
                          target="_blank"
                        >
                          {intl(
                            'saenext.shared.ImageSelectorField.custom.ApplicationAccessToPublicNetwork',
                          )}
                        </a>
                      </>
                    ) : (
                      intl(
                        'saenext.shared.ImageSelectorField.custom.EnterTheCompleteImageRepository',
                      )
                    )}
                  </>
                }
                required
              >
                <Input
                  {...field.init('image', {
                    props: {
                      onChange: onChange,
                    },
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.shared.ImageSelectorField.custom.EnterTheFullImageAddress',
                        ),
                      },
                      {
                        validator: (_rule, value: string, callback) => {
                          if (/\s/.test(value)) {
                            callback(
                              intl(
                                'saenext.shared.ImageSelectorField.custom.TheImageAddressCannotContain',
                              ),
                            );
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })}
                  placeholder={intl(
                    'saenext.shared.ImageSelectorField.custom.EnterTheFullImageAddress',
                  )}
                />
              </FormItem>
            </Form>
          </React.Fragment>
        )}
      </>
    );
  },
);
