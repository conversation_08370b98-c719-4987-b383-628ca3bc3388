import React from 'react';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import modelDecorator from '~/components/shared/Modal/decorator';

const ModalWrap = (props) => {
  const { toggleModal, trigger, children, ...rest } = props;

  const toogleVisible = () => {
    toggleModal({
      visible: true,
      ...rest,
      content: children,
    });
  }
  
  return (
    <>
      <AddPropsWrap onClick={toogleVisible}>
        {trigger}
      </AddPropsWrap>
    </>
  )
}

export default modelDecorator(ModalWrap);