import React, { useEffect, useState, useContext } from 'react';
import { Form, intl, Message, Switch, ConsoleContext, ToolTipCondition } from '@ali/cnd';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';
import CachedData from '~/cache/common';
import C from '~/constants/common';
import { LANGUAGE_NAME, LANGUAGE_TYPE } from '../DeploySelectorField/constant';
import { isTypeImage } from '~/utils/global';
import services from '~/services';

const ArmsMonitorNew = (props) => {
  const { name, lang, packageType, NewSaeVersion = '', setValue } = props;

  const isImage = isTypeImage(packageType);

  const langName = LANGUAGE_NAME[lang];

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const [armsStatus, setArmsStatus] = useState(false);
  const [armsProStatus, setArmsProStatus] = useState(false);
  const [armsGbStatus, setArmsGbStatus] = useState(false);

  useEffect(() => {
    if (NewSaeVersion === 'pro') {
      handleCheckCommercialStatus();
    }
  }, [NewSaeVersion]);

  useEffect(() => {
    setValue();
  }, [armsStatus, armsProStatus, armsGbStatus]);

  const handleCheckCommercialStatus = async () => {
    // 检查arms用用监控是否开通
    const res = await services.CheckCommercialStatus({
      params: {
        RegionId: regionId,
        Service: 'apm',
      },
    });
    setArmsStatus(res?.Data || false);
    if (res?.Data) {
      // 检查专家版/GB版是否开通
      // arms_app_post --- arms应用监控专家版  arms_serverless_public_cn --- arms应用监控GB版
      const checkVersionRes = await Promise.all([
        checkVersionCommercialStatus('arms_app_post'),
        checkVersionCommercialStatus('arms_serverless_public_cn'),
      ]);
      const arms_pro_status = checkVersionRes[0]?.UserAndCommodityStatus?.Enable;
      const arms_gb_status = checkVersionRes[1]?.UserAndCommodityStatus?.Enable;
      setArmsProStatus(arms_pro_status);
      setArmsGbStatus(arms_gb_status);
    }
  };

  const checkVersionCommercialStatus = async (type) => {
    const res = await services.GetCommercialStatus({
      params: {
        RegionId: regionId,
        CommodityCode: type,
      },
    });
    return res;
  };

  const LangLink = {
    [LANGUAGE_TYPE.GO]: CachedData.confLink('help:sae:installing-probes-for-golang-applications'),
    [LANGUAGE_TYPE.PYTHON]: CachedData.confLink('help:sae:install-the-arms-agent-for-a-python-application'),
  };

  const helpLink = (
    <a href={LangLink[lang]} target="_blank">
      {intl('saenext.micro.golang.AppMonitor.ViewOperationDocuments')}
    </a>
  );

  const helpMsg_pro = {
    [LANGUAGE_TYPE.JAVA]: intl('saenext.shared.MicroServiceConfigField.MicroServiceConfig.WhenEnabledTheSystemInjects'),

    [LANGUAGE_TYPE.PYTHON]: intl(
      'saenext.ArmsMonitorField.micro-app.ArmsMonitor.WhenEnabledSaeCanProvide',
    ),
    [LANGUAGE_TYPE.GO]: intl(
      'saenext.ArmsMonitorField.micro-app.ArmsMonitor.WhenEnabledSaeCanProvide',
    ),
  };

  return (
    <div id="arms-monitor">
      {(!NewSaeVersion || NewSaeVersion === 'std') && (
        <React.Fragment>
          <OpenStatusMessage product="arms" />
          <Message type="warning" className="mb-l">
            {isImage
              ? intl('saenext.ArmsMonitorField.micro-app.ArmsMonitor.IfYouChooseToEnable', {
                  langName,
                  helpLink,
                })
              : intl('saenext.shared.ArmsMonitorNew.IfYouChooseToEnable', { langName: langName })}
          </Message>

          <Form.Item
            label={intl('saenext.ArmsMonitorField.micro-app.ArmsMonitor.ApplicationMonitoring')}
            help={intl.html(
              'saenext.ArmsMonitorField.micro-app.ArmsMonitor.WhenEnabledSaeCanProvide',
            )}
            {...C.FORM_LAYOUT_LEFT}
          >
            <Switch name={name} disabled={!CachedData.getOpenStatus('armsStatus')} />
          </Form.Item>
        </React.Fragment>
      )}
      {NewSaeVersion === 'pro' && (
        <React.Fragment>
          {lang !== LANGUAGE_TYPE.JAVA && (
            <Message type="warning" className="mb-l">
              {isImage ? (
                <>
                  {intl('saenext.shared.ArmsMonitorNew.NoteToEnableApplicationMonitoring', {
                    langName: langName,
                  })}{' '}
                  {helpLink}
                </>
              ) : (
                <>
                  {intl('saenext.shared.ArmsMonitorNew.NoteIfYouChooseTo', { langName: langName })}
                  {helpLink}
                </>
              )}
            </Message>
          )}
          <Form.Item
            label={intl('saenext.ArmsMonitorField.micro-app.ArmsMonitor.ApplicationMonitoring')}
            help={helpMsg_pro[lang]}
            {...C.FORM_LAYOUT_LEFT}
          >
            {armsStatus && !armsProStatus && !armsGbStatus ? (
              <ToolTipCondition
                show={armsStatus && !armsProStatus && !armsGbStatus}
                align="t"
                popupStyle={{ maxWidth: 480 }}
                tip={
                  <div>
                    <div>
                      {intl('saenext.shared.ArmsMonitorNew.CurrentlyArmsApplicationMonitoringIs')}

                      <a href={CachedData.confLink('feature:commonbuy:arms_GB')} target="_blank">
                        {intl('saenext.shared.ArmsMonitorNew.Switch')}
                      </a>
                      {intl('saenext.shared.ArmsMonitorNew.TheBillingByObservableData')}
                    </div>
                    <div style={{ color: '#FFA003' }}>
                      {intl('saenext.shared.ArmsMonitorNew.NoteAfterTheBasicEdition')}

                      <a href={CachedData.confLink('help:arms:product-billing')} target="_blank">
                        {intl('saenext.shared.ArmsMonitorNew.EstimatedBillingByWriteData')}
                      </a>
                    </div>
                  </div>
                }
              >
                <Switch name={name} disabled={armsStatus && !armsProStatus && !armsGbStatus} />
              </ToolTipCondition>
            ) : (
              <Switch name={name} />
            )}
          </Form.Item>
        </React.Fragment>
      )}
    </div>
  );
};

export default ArmsMonitorNew;
