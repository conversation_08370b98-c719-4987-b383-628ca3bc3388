import { intl } from '@ali/cnd';
import React from 'react';
import { SwitchField } from '@ali/deep';

const formItemLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const EbpfMonitorConfig = () => {
  return (
    <>
      <SwitchField
        name="EnableEbpf"
        label={intl('saenext.components.shared.EbpfMonitorConfig.EnableApplicationMonitoring')}
        help={intl(
          'saenext.components.shared.EbpfMonitorConfig.WhenEnabledTheApplicationMonitoring',
        )}
        {...formItemLayout}
      />
    </>
  );
};

export default EbpfMonitorConfig;
