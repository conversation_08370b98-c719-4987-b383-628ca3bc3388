import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState, useMemo } from 'react';
import { Copy, Dialog, intl } from '@ali/cnd';
import { Form, NumberField, SelectField, SwitchField, TableField, TextField } from '@ali/deep';
import { Icon, ToolTipCondition } from '@ali/cnd';
import { debounce, isEmpty } from 'lodash';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';

const formItemLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const SwimK8sRegistration = (props, ref) => {
  const { value = {}, onChange, appName, namespaceId, appConfig, pvtzDiscoverySvc } = props;
  const { serviceName } = value;
  const [regionId, namespaceSuffix = 'default'] = namespaceId.split(':');
  const formField = useRef(null);

  const [enable, setEnable] = useState(value?.enable);
  const [disabled, setDisabled] = useState(false);

  useEffect(() => {
    const { enable = false } = value;
    setEnable(enable);
    formField.current?.setValue?.(value);
  }, [JSON.stringify(value)]);

  useEffect(() => {
    const swimlaneInfo =
      (appConfig?.SwimlanePvtzDiscovery && JSON.parse(appConfig?.SwimlanePvtzDiscovery)) || {};
    setDisabled(swimlaneInfo?.serviceName ? true : false);
  }, [appConfig?.SwimlanePvtzDiscovery]);

  useEffect(() => {
    // 基于k8s 全链路灰度开启时 服务名称初始化值处理 && 端口协议初始化值与基于k8s应用访问保持一致
    if (enable && !appConfig?.SwimlanePvtzDiscovery) {
      let _portProtocols = [];
      const pvtzDiscoverySvcObj = (pvtzDiscoverySvc && JSON.parse(pvtzDiscoverySvc)) || {};
      if (!isEmpty(pvtzDiscoverySvcObj?.portProtocols)) {
        _portProtocols = pvtzDiscoverySvcObj?.portProtocols;
      }
      const portProtocols = _portProtocols.map((item) => {
        return {
          port: item?.port,
          targetPort: item?.targetPort,
          protocol: item?.protocol,
        };
      })
      const defaultServiceName = `${appName}-gray`;
      formField.current?.setValue?.({
        serviceName: defaultServiceName.toLocaleLowerCase(),
        portProtocols,
      });
    }
  }, [enable, appName, namespaceId]);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [value],
  );

   const serviceNameData = useMemo(() => {
      // 新版包含 .{namespaceSuffix} 信息
      if (serviceName?.includes('.')) {
        return serviceName?.split('.')[0];
      } else {
        return `${serviceName}.svc.cluster.local.${regionId}`;
      }
    }, [serviceName]);

  const onFieldChange = (values, item) => {
    onChange?.(formField.current.getValue?.());
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      formField.current.validate((error, value) => {
        error ? resolve(' ') : resolve(true);
      });
    });
  };

  const errorRender = (val) => {
    return intl('saenext.shared.K8sRegistrationField.K8sRegistration.CompleteThePortAndProtocol');
  };

  return (
    <>
      <Form
        ref={c => (formField.current = c?.getInstance?.() || c)}
        onChange={debounce(onFieldChange, 500)}
      >
        <SwitchField
          className="inline-extra"
          name="enable"
          label={intl(
            'saenext.shared.SwimLaneK8sRegistrationField.SwimlaneK8sRegistration.ConfigureFullLinkGrayBased',
          )}
          {...formItemLayout}
          extra={
            <a
              href={CachedData.confLink('help:sae:k8s-based-end-to-end-release')}
              target="_blank"
              className="ml-l"
            >
              <Icon type="connection" size="small" className="mr-s" />
              {intl(
                'saenext.shared.SwimLaneK8sRegistrationField.SwimlaneK8sRegistration.KSServiceFullLink',
              )}
            </a>
          }
          value={enable}
          onChange={({ value }) => {
            setEnable(value);
          }}
        />

        {enable && (
          <>
            <If condition={disabled}>
              <Form.Item
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.ServiceName')}
                {...formItemLayout}
              >
                <Copy truncateProps={{ type: 'width', threshold: 'auto' }} text={serviceNameData}>
                  {serviceNameData}
                </Copy>
              </Form.Item>
            </If>

            <If condition={!disabled}>
              <TextField
                required
                name="serviceName"
                behavior="NORMAL"
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.ServiceName')}
                placeholder={intl(
                  'saenext.shared.K8sRegistrationField.K8sRegistration.EnterAServiceName',
                )}
                requiredMessage={intl(
                  'saenext.shared.K8sRegistrationField.K8sRegistration.EnterAServiceName',
                )}
                {...formItemLayout}
                validation={[
                  {
                    type: 'required',
                    message: intl(
                      'saenext.shared.K8sRegistrationField.K8sRegistration.EnterAServiceName',
                    ),
                  },
                  {
                    type: 'customValidate',
                    param: value => {
                      const regex = /^[a-z0-9]+(-[a-z0-9]+)*$/;
                      if (value.length > 63) {
                        return intl(
                          'saenext.shared.K8sRegistrationField.K8sRegistration.TheMaximumLengthOfThe',
                        );
                      }
                      if (!regex.test(value)) {
                        return intl(
                          'saenext.shared.K8sRegistrationField.K8sRegistration.TheServiceNameConsistsOf',
                        );
                      }
                      return true;
                    },
                  },
                ]}
              />
            </If>

            <TableField
              name="portProtocols"
              label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.PortsAndProtocols')}
              addButtonText={intl('saenext.shared.K8sRegistrationField.K8sRegistration.Add')}
              className="full24-table"
              layout="TABLER"
              required
              errorRender={errorRender}
              showIndex={false}
              showDeleteConfirm={false}
              actionsColumnWidth={80}
              delButtonText={<Icon type="delete" />}
              maxItems={5}
              {...formItemLayout}
            >
              <NumberField
                name="port"
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.Port')}
                required
                min={1}
                max={65535}
              />

              <NumberField
                name="targetPort"
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.ContainerPort')}
                required
                min={1}
                max={65535}
              />

              <SelectField
                name="protocol"
                label={intl('saenext.shared.K8sRegistrationField.K8sRegistration.Agreement')}
                dataSource={[
                  {
                    value: 'TCP',
                    label: 'TCP',
                  },
                  {
                    value: 'UDP',
                    label: 'UDP',
                  },
                ]}
                required
              />
            </TableField>
          </>
        )}
      </Form>
    </>
  );
};

export default forwardRef(SwimK8sRegistration);
