import { intl } from '@ali/cnd';
import React, { useState, useEffect, useImperativeHandle } from 'react';
import { isNaN } from 'lodash';
import { Balloon, Select, Field } from '@ali/cnd';
import TimeContainer from './TimeContainer';
import StatusContainer from './StatusContainer';
import moment from 'moment';
import { isJSON } from '~/utils/global';
const LOCALSTORAGE_KEY = 'TIME_RANGE_PRE_FOR_MONITORING';

export const RECENT_OPTION = [
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Minutes'),
    value: 'last_15_minutes',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.HalfAnHour'),
    value: 'last_half_hour',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Hour'),
    value: 'last_hour',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Hours'),
    value: 'last_3_hours',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Hours.1'),
    value: 'last_6_hours',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Hours.2'),
    value: 'half_one_day',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Day'),
    value: 'last_24_hours',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Days'),
    value: 'last_three_days',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Days.1'),
    value: 'half_7_day',
    type: false,
  },
  {
    label: intl('saenext.components.shared.TimeRangeMonitor.Days.2'),
    value: 'last_two_weeks',
    type: false,
  },
];

const RECENT_TIMES = {
  last_5_minutes: () => [moment().valueOf() - 5 * 60 * 1000, moment().valueOf()],
  last_10_minutes: () => [moment().valueOf() - 10 * 60 * 1000, moment().valueOf()],
  last_15_minutes: () => [moment().valueOf() - 15 * 60 * 1000, moment().valueOf()],
  last_half_hour: () => [moment().valueOf() - 30 * 60 * 1000, moment().valueOf()],
  last_hour: () => [moment().valueOf() - 60 * 60 * 1000, moment().valueOf()],
  last_3_hours: () => [moment().valueOf() - 3 * 60 * 60 * 1000, moment().valueOf()],
  last_6_hours: () => [moment().valueOf() - 6 * 60 * 60 * 1000, moment().valueOf()],
  last_24_hours: () => [moment().valueOf() - 24 * 60 * 60 * 1000, moment().valueOf()],
  half_one_day: () => [moment().valueOf() - 12 * 60 * 60 * 1000, moment().valueOf()],
  half_7_day: () => [
    moment().subtract(7, 'days').valueOf(),
    moment().valueOf(),

    ,
    moment().valueOf(),
  ],

  today: () => [moment().startOf('day').valueOf(), moment().valueOf()],
  last_three_days: () => [moment().valueOf() - 3 * 24 * 60 * 60 * 1000, moment().valueOf()],
  this_week: () => [moment().startOf('week').add(1, 'd').valueOf(), moment().valueOf()],
  last_two_weeks: () => [moment().valueOf() - 2 * 7 * 24 * 60 * 60 * 1000, moment().valueOf()],
  last_three_weeks: () => [moment().valueOf() - 3 * 7 * 24 * 60 * 60 * 1000, moment().valueOf()],
  this_month: () => [moment().startOf('month').valueOf(), moment().valueOf()],
  last_two_months: () => [moment().valueOf() - 2 * 30 * 24 * 60 * 60 * 1000, moment().valueOf()],
};

function isNumber(value) {
  return typeof value === 'number' && isFinite(value);
}

export function toSecond(timestamp) {
  if (timestamp === -1) {
    return -1;
  } else if (isNumber(timestamp)) {
    return Math.floor(timestamp / 1000) * 1000;
  } else {
    return timestamp;
  }
}

export function getTimes(key) {
  const getTimes = RECENT_TIMES[key];
  if (getTimes) {
    const [start, end] = getTimes();
    return [toSecond(start), toSecond(end)];
  } else {
    return [];
  }
}

interface ListFormProps {
  onTimeChanged: Function;
  localStorageKey?: string;
  secMetricsStatus?: boolean;
  timeInitValue?: any;
  periodInitValue?: number;
  onRef?: any;
  defaultTime?: boolean;
  options?: {
    label: string;
    value: string;
    type: boolean;
  }[];
  width?: any;
  showSecondGranularity?: boolean;
}

const TimeRangeMonitor: React.FC<ListFormProps> = ({
  onRef,
  onTimeChanged,
  localStorageKey = LOCALSTORAGE_KEY,
  secMetricsStatus = false,
  timeInitValue = undefined,
  periodInitValue = undefined,
  defaultTime = true,
  options,
  width,
  showSecondGranularity = true,
}) => {
  const [periodList, setPeriodList] = useState(options || RECENT_OPTION);
  const [period, setPeriod]: [number, any] = useState(periodInitValue);
  const [dataSource, setDataSource] = useState([]);
  const [refresh, setRefresh] = useState(true);
  const [timeValue, setTimeValue] = useState([]);
  const [initiLoading, setInitiLoading] = useState(false);
  const field = Field.useField();
  const { setValue } = field;

  useEffect(() => {
    let storage = getLocalStorage();
    if (storage && defaultTime) {
      getPeriod(storage.recentTiem, '', false);
      setValue('time', [moment(storage.start), moment(storage.end)]);
      setInitiLoading(true);
      setPeriod(storage.period);
      onTimeChanged({
        start: toSecond(storage.start),
        end: toSecond(storage.end),
        key: storage.recentTiem,
        period: storage.period,
      });
    } else {
      const [start, end] = getTimes(timeInitValue || 'last_6_hours');
      setLocalStorage(start, end, period || 60, timeInitValue || 'last_6_hours');
      setInitiLoading(true);
      getPeriod(timeInitValue || 'last_6_hours', [start, end]);
      (secMetricsStatus || !timeInitValue) && setTimeValue([start, end]);
    }
  }, []);

  useEffect(() => {
    let storage = getLocalStorage();
    let dataList = setDataSourceList([storage.start, storage.end]);
    let periodList = dataList.filter((item) => !item.disabled);
    if (!secMetricsStatus && storage.period == 10) {
      setPeriod(periodList[0].value);
      setLocalStorage(storage.start, storage.end, periodList[0].value, storage.recentTiem);
    } else {
      setPeriod(storage.period);
    }

    if (storage.period === 10 && !showSecondGranularity) {
      handleChange(60);
    }

    setDataSource([...dataList]);
  }, [secMetricsStatus, showSecondGranularity]);

  useEffect(() => {
    if (initiLoading) {
      getPeriodSelect(timeValue, true);
    }
  }, [timeValue]);

  const getPeriodSelect = (timeValue, isCall) => {
    let dataList = setDataSourceList(timeValue);
    let periodList = dataList.filter((item) => !item.disabled);
    let storage = getLocalStorage();
    if (periodList.length) {
      let key = storage.recentTiem;
      const [start, end] = timeValue;
      setPeriod(isCall ? periodList[0].value : storage.period);
      setValue('time', [moment(start), moment(end)]);
      setLocalStorage(start, end, isCall ? periodList[0].value : storage.period, key);
      isCall &&
        onTimeChanged({
          start: toSecond(start),
          end: toSecond(end),
          key,
          period: isCall ? periodList[0].value : storage.period,
        });
    }
    setDataSource([...dataList]);
  };

  const setDataSourceList = (timeValue) => {
    const timeInterval = parseInt(timeValue[1]) - parseInt(timeValue[0]);
    // 86400000 一天时间   3600000 一小时时间
    // 1天 最小1分钟  // 1440点
    // 3天 最小5分钟  // 864点
    // 7天 最小15分钟 // 672点
    // 14天最小14分钟 // 1344

    const dataSource = [
      {
        value: 60,
        label: intl('saenext.components.shared.TimeRangeMonitor.Minute'),
        disabled: isNaN(timeInterval) || timeInterval > 0.5 * 86400000, // 大于12小时 1分钟不可用
      },
      {
        value: 300,
        label: intl('saenext.components.shared.TimeRangeMonitor.Minutes.1'),
        disabled: isNaN(timeInterval) || timeInterval > 3 * 86400000, // 大于3天 5分钟不可用
      },
      {
        value: 900,
        label: intl('saenext.components.shared.TimeRangeMonitor.Minutes.2'),
        disabled:
          isNaN(timeInterval) ||
          timeInterval >= 14 * 86400000 || // 大于14天 15分钟不可用
          timeInitValue <= 900000, // 小于15分钟 不可用
      },
      {
        value: 1800,
        label: intl('saenext.components.shared.TimeRangeMonitor.Minutes.3'),
        disabled:
          isNaN(timeInterval) ||
          timeInterval >= 30 * 86400000 || // 大于30天 30分钟不可用
          timeInterval <= 1800000, // 小于30分钟 不可用
      },
      {
        value: 3600,
        label: intl('saenext.components.shared.TimeRangeMonitor.Hour.1'),
        disabled: isNaN(timeInterval) || timeInterval <= 3600000,
      },
      {
        value: 86400,
        label: intl('saenext.components.shared.TimeRangeMonitor.Day.1'),
        disabled: isNaN(timeInterval) || timeInterval <= 86400000,
      },
    ];

    return dataSource;
  };

  const setLocalStorage = (start, end, period, recentTiem) => {
    window.localStorage.setItem(
      localStorageKey,
      JSON.stringify({ start, end, period, recentTiem }),
    );
  };

  const getLocalStorage = () => {
    let localStorage = window.localStorage.getItem(localStorageKey);
    return isJSON(localStorage) && localStorage !== '{}' ? JSON.parse(localStorage) : '';
  };

  const getPeriod = (value, isCall = undefined, isTimeValue = true) => {
    periodList.map((item) => {
      if (item.value == value) {
        item.type = true;
        isTimeValue && setTimeValue(isCall ? isCall : getTimes(item.value));
      } else {
        item.type = false;
      }
    });
    setPeriodList([...periodList]);
  };

  const periodChange = (record) => {
    if (record.type) return false;
    let nextPeriod = record.value;
    const [start, end] = RECENT_TIMES[nextPeriod]();
    getPeriod(nextPeriod);
    setLocalStorage(start, end, period, nextPeriod);
    if (!RECENT_TIMES[nextPeriod]) return false;
    setValue('time', [moment(start), moment(end)]);
    setTimeValue([start, end]);
  };

  const balloonChange = () => {
    setRefresh(false);
    setRefresh(true);
  };

  const getBalloon = (value) => {
    const [start, end] = getTimes(value);
    let ballStr = `${moment(start).format('YYYY-MM-DD HH:mm:ss')} ~ ${moment(end).format(
      'YYYY-MM-DD HH:mm:ss',
    )} `;
    return ballStr;
  };

  const handleChange = (period) => {
    const storage = getLocalStorage();
    const key = storage.recentTiem;
    let start = timeValue[0] || storage.start;
    let end = timeValue[1] || storage.end;
    setPeriod(period);
    setLocalStorage(start, end, period, key);
    onTimeChanged({ start: toSecond(start), end: toSecond(end), key, period });
  };

  const getFaPeriod = (times) => {
    setTimeValue(times);
  };

  useImperativeHandle(onRef, () => ({
    onChild: (times) => {
      getPeriodSelect(times, false);
    },
    onRangeChanged: (times) => {
      let dataList = setDataSourceList(times);
      let periodList = dataList.filter((item) => !item.disabled);
      const period = periodList[0].value;
      setPeriod(period);
      window.localStorage.setItem(
        localStorageKey,
        JSON.stringify({
          start: times[0],
          end: times[1],
          period: period,
          recentTiem: 'custom',
        }),
      );
      getPeriodSelect(times, false);
      getPeriod('custom');
      return period;
    },
    refresh: () => {
      const { recentTiem } = getLocalStorage();
      if (recentTiem !== 'custom') periodChange({ type: false, value: recentTiem });
    },
  }));

  return (
    <div className="flex">
      <span style={{ marginRight: 10, minWidth: 475 }}>
        {periodList.map((item, ids) => {
          return (
            <span key={ids} className={item.type ? 'border-left-none' : ''}>
              <Balloon
                trigger={
                  <span onClick={() => periodChange(item)}>
                    <StatusContainer
                      className={
                        item.type
                          ? 'color-primary border-primary'
                          : periodList.length - 1 == ids
                          ? 'border-normal'
                          : 'simple-normal'
                      }
                      type="simple"
                      style={{ height: 32, lineHeight: '32px' }}
                    >
                      {item.label}
                    </StatusContainer>
                  </span>
                }
                afterClose={() => balloonChange()}
                closable={false}
              >
                {refresh && getBalloon(item.value)}
              </Balloon>
            </span>
          );
        })}
      </span>
      <TimeContainer
        field={field}
        timeInitValue={timeInitValue}
        initValue={getTimes(timeInitValue || getLocalStorage().recentTiem)}
        getFaPeriod={getFaPeriod}
        periodChange={getPeriod}
        getLocalStorage={getLocalStorage}
        period={period}
        localStorageKey={localStorageKey}
        onTimeChanged={onTimeChanged}
        width={width}
      />

      <Select
        label={intl('saenext.components.shared.TimeRangeMonitor.Granularity')}
        value={period}
        style={{ marginLeft: 10 }}
        onChange={handleChange}
        dataSource={dataSource}
      />
    </div>
  );
};

export default TimeRangeMonitor;
