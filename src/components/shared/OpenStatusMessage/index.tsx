import { intl } from '@ali/cnd';
import React, { useContext, useEffect } from 'react';
import { Message } from '@ali/cnd';
import If from '~/components/shared/If';
import RefreshButton from '~/components/shared/RefreshButton';
import ExternalLink from '~/components/shared/ExternalLink';
import { getMseOpenStatus, getProductOpenStatus } from '~/utils/openStatus';
import { PRODUCT_CODE, getBuyLink } from '~/constants/productCode';
import FeatureContext from '~/utils/featureContext';
import MicroAppContext from '~/utils/microAppContext';

const OpenStatusMessage = (props) => {
  const { product, refresh } = props;

  const { type } = useContext(MicroAppContext);

  const { openStatus, setOpenStatus } = useContext(FeatureContext);
  const productOpenStatus = openStatus?.[product];
  const { name: productName, feature } = PRODUCT_CODE[product] || {};

  const featureName = feature || productName;

  const isMse = product.startsWith('mse');

  useEffect(() => {
    if (isMse) {
      queryMseStatus();
    }
  }, [product]);

  const queryOpenStatus = async () => {
    if (isMse) {
      queryMseStatus();
      return;
    }

    const enabled = await getProductOpenStatus(product);
    setOpenStatus({
      ...openStatus,
      [product]: enabled,
    });
    refresh?.(enabled);
  };

  const queryMseStatus = async () => {
    const mseStatus = await getMseOpenStatus();
    if (!mseStatus) return;

    const { Status, Version, FreeVersion } = mseStatus;
    setOpenStatus({
      ...openStatus,
      [`mse${Version}`]: Status === 2,
    });
  };

  return (
    <If condition={!productOpenStatus && type !== 'preview'}>
      <Message type="warning" className="mb-l">
        <div>
          {intl('saenext.shared.OpenStatusMessage.YouHaveNotActivatedCloud')}
          {productName}
          {intl('saenext.shared.OpenStatusMessage.IfYouWantToUse')}
          {featureName}
          {intl('saenext.shared.OpenStatusMessage.FunctionPleaseActivateNow')}
          <ExternalLink url={getBuyLink(product)} />
        </div>
        <div>
          {intl('saenext.shared.OpenStatusMessage.IfTheActivationIsSuccessful')}

          <RefreshButton
            label={intl('saenext.shared.OpenStatusMessage.Refresh')}
            handler={queryOpenStatus}
          />
        </div>
      </Message>
    </If>
  );
};

export default OpenStatusMessage;
