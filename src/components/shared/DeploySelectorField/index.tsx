import React from 'react';
import { ConfigProvider } from '@ali/deep';
import DeploySelector from './DeploySelector';
import BaseField from '@ali/deep-form-helper';

class DeploySelectorField extends BaseField {
  props;
  static displayName = 'DeploySelectorField';

  static propTypes = {
    ...BaseField.propTypes,
  };

  static defaultProps = {
    ...BaseField.defaultProps,
  };

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {
    const newProps = {
      ...this.props,
      ...fieldProps,
      // placeholder: this.props.placeholder || '请选择镜像',
    };

    return <DeploySelector {...newProps} />;
  }
}

export default ConfigProvider.config(DeploySelectorField as any);
