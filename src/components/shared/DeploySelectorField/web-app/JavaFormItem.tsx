import { intl } from '@ali/cnd';
import React, { useState, useEffect, useMemo } from 'react';
import { Field, Form, Radio, Input, Select, Button } from '@ali/cnd';
import { UPLOAD_TYPE, JdkList, TomcatList } from '../constant';
import { PACKAGE_TYPE } from '~/constants/application';
import _ from 'lodash';
import * as services from '~/services';
import PackageUploader from '../PackageUploader';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import { ShellEditor } from '@ali/cnd';

interface IProps {
  field: Field;
  javaValues: any;
}

const PackageText = {
  [PACKAGE_TYPE.WAR]: 'WAR',
  [PACKAGE_TYPE.JAR]: 'JAR',
  [PACKAGE_TYPE.PHPZIP]: 'ZIP',
  [PACKAGE_TYPE.PYTHONZIP]: 'ZIP',
};

const FileSuffix = {
  [PACKAGE_TYPE.WAR]: '.war',
  [PACKAGE_TYPE.JAR]: '.jar',
  [PACKAGE_TYPE.PHPZIP]: '.zip',
  [PACKAGE_TYPE.PYTHONZIP]: '.zip',
};

const fieldLayout = {
  labelTextAlign: 'left' as 'left',
  labelCol: { fixedSpan: 6 },
  wrapperCol: { span: 16 },
};

const JavaFormItem = (props: IProps) => {
  const { field, javaValues } = props;
  // @ts-ignore
  const [javaList, setJavaList] = useState([]);
  const [tomcatList, setTomcatList] = useState([]);
  const [edasList, setEdasList] = useState([]);
  const [jdkVersion, setJdkVersion] = useState('openjdk-8');
  const [packageType, setPackageType] = useState(PACKAGE_TYPE.WAR);
  const [uploadType, setUploadType] = useState(UPLOAD_TYPE.UPLOAD);
  const [fileName, setFileName] = useState('');
  const [startCmd, setStartCmd] = useState('');
  const { init, setValue, setValues } = field;

  useEffect(() => {
    // jdk tomcat 版本号暂时前端写死 后端还未兼容1.0的接口配置方式
    // getJavaComponents();
    // getTomcatComponents();
    // getEdasComponents();
  }, []);
  useEffect(() => {
    parseIn();
  }, [javaValues]);
  useEffect(() => {
    setValue('FileName', fileName);
  }, [fileName]);

  const parseIn = () => {
    if (_.isEmpty(javaValues)) return;
    if (_.isEmpty(javaValues.PackageUrl)) return;
    const {
      PackageType = PACKAGE_TYPE.WAR,
      PackageUrl,
      FileName,
      PackageVersion,
      Jdk = 'openjdk-8',
      Container = 'apache-tomcat-7',
      RunCommand = '',
    } = javaValues;

    const values = {
      PackageType,
      PackageUrl,
      PackageVersion,
      Jdk,
      Container,
      RunCommand,
    };

    // @ts-ignore
    setValues(values);
    setValue('FileName', FileName);
    setJdkVersion(Jdk);
    setStartCmd(RunCommand);
    setPackageType(PackageType);
  };

  // @ts-ignore
  const getJavaComponents = async () => {
    const Data = await fetchComponents('JDK');
    const _javaList = _.map(Data, (item) => ({
      label: item.ComponentDescription,
      value: item.ComponentKey,
    }));
    setJavaList(_javaList);
  };

  // @ts-ignore
  const getTomcatComponents = async () => {
    const Data = await fetchComponents('TOMCAT');
    const _tomcatList = _.map(Data, (item) => ({
      label: item.ComponentDescription,
      value: item.ComponentKey,
    }));
    setTomcatList(_tomcatList);
  };

  // @ts-ignore
  const getEdasComponents = async () => {
    const { Data = [] } = (await services.DescribeEdasContainers({})) || {};
    const enableEdasList = _.filter(Data, { Disabled: false });
    const _edasList = _.map(enableEdasList, (item) => ({
      value: item.EdasContainerVersion,
      label: intl(
        'saenext.DeploySelectorField.web-app.JavaFormItem.EdasContainerItemedascontainerversionFatjarDeployment',
        { itemEdasContainerVersion: item.EdasContainerVersion },
      ),
    }));
    _edasList.sort((a, b) => b.value - a.value);
    setEdasList(_edasList);
  };

  const fetchComponents = async (Type) => {
    const { Data = [] } =
      (await services.DescribeComponents({
        params: { Type },
      })) || {};
    return Data;
  };

  // @ts-ignore
  const warContainers = useMemo(() => {
    return [...tomcatList, ...edasList];
  }, [tomcatList, edasList]);

  const exampleLink = useMemo(() => {
    return {
      [PACKAGE_TYPE.WAR]:
        jdkVersion !== 'openjdk-7'
          ? 'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae.war'
          : 'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-1.7.war',
      [PACKAGE_TYPE.JAR]:
        jdkVersion !== 'openjdk-7'
          ? 'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae.jar'
          : 'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-1.7.jar',
    };
  }, [jdkVersion]);

  const demoPkgPortMap = {
    [PACKAGE_TYPE.WAR]: '8080',
    [PACKAGE_TYPE.JAR]: '18091',
  };

  const demoPort = demoPkgPortMap[packageType];
  const pkgText = PackageText[packageType];

  const onChangePackageType = (packageType) => {
    setPackageType(packageType);
  };

  const onChangeUploadType = (uploadType) => {
    setUploadType(uploadType);
  };

  return (
    <>
      <Form.Item
        required
        label={intl('saenext.DeploySelectorField.web-app.JavaFormItem.CodePackageType')}
        {...fieldLayout}
      >
        <Radio.Group
          className="full-width"
          {...init('PackageType', {
            initValue: PACKAGE_TYPE.WAR,
            props: {
              onChange: onChangePackageType,
            },
          })}
        >
          <Radio id="war" value={PACKAGE_TYPE.WAR}>
            <TextWithBalloon
              text={intl('saenext.DeploySelectorField.web-app.JavaFormItem.WarPackageDeployment')}
              tips={
                <>
                  {intl(
                    'saenext.DeploySelectorField.web-app.JavaFormItem.SupportsGeneralWarPackageDeployment',
                  )}
                </>
              }
            />
          </Radio>
          <Radio id="jar" value={PACKAGE_TYPE.JAR}>
            <TextWithBalloon
              text={intl('saenext.DeploySelectorField.web-app.JavaFormItem.JarPackageDeployment')}
              tips={
                <>
                  {intl(
                    'saenext.DeploySelectorField.web-app.JavaFormItem.SupportsGeneralJarPackageDeployment',
                  )}
                </>
              }
            />
          </Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        required
        label={intl('saenext.DeploySelectorField.web-app.JavaFormItem.FileUploadMethod')}
        {...fieldLayout}
      >
        <Radio.Group
          className="full-width"
          {...init('UploadType', {
            initValue: UPLOAD_TYPE.UPLOAD,
            props: {
              onChange: onChangeUploadType,
            },
          })}
        >
          <Radio id="upload" value={UPLOAD_TYPE.UPLOAD}>
            {intl(
              'saenext.DeploySelectorField.web-app.JavaFormItem.UploadPackagetextpackagetypePackages',
              { PackageTextPackageType: PackageText[packageType] },
            )}
          </Radio>
          {/* <Radio id="url" disabled value={UPLOAD_TYPE.URL}>{PackageText[packageType]} 包地址</Radio> */}
        </Radio.Group>
      </Form.Item>
      {uploadType === UPLOAD_TYPE.UPLOAD ? (
        <Form.Item
          required
          {...fieldLayout}
          label={intl(
            'saenext.DeploySelectorField.web-app.JavaFormItem.UploadPackagetextpackagetypePackages',
            { PackageTextPackageType: PackageText[packageType] },
          )}
          help={intl(
            'saenext.DeploySelectorField.web-app.JavaFormItem.PkgtextCodePackageDefaultPort',
            { pkgText: pkgText, demoPort: demoPort },
          )}
        >
          <PackageUploader
            name="PackageUrl"
            className="full-width"
            assignSignatur="v2"
            accept={FileSuffix[packageType]}
            setFileName={setFileName}
          />

          <Button
            text
            type="primary"
            onClick={() => {
              window.open(exampleLink[packageType]);
            }}
          >
            {intl(
              'saenext.DeploySelectorField.web-app.JavaFormItem.DownloadPackagetextpackagetypePackageSample',
              { PackageTextPackageType: PackageText[packageType] },
            )}
          </Button>
        </Form.Item>
      ) : (
        <Form.Item
          required
          {...fieldLayout}
          label={intl(
            'saenext.DeploySelectorField.web-app.JavaFormItem.PackagetextpackagetypePackageAddress',
            { PackageTextPackageType: PackageText[packageType] },
          )}
        >
          <Input
            trim
            name="PackageUrl"
            className="full-width"
            // @ts-ignore
            placeholder={
              intl(
                'saenext.DeploySelectorField.web-app.JavaFormItem.EnterThePackagetextpackagetypePackageAddress',
                {
                  PackageTextPackageType: PackageText[packageType],
                  FileSuffixPackageType: FileSuffix[packageType],
                },
              ) as string
            }
          />
        </Form.Item>
      )}

      <Form.Item
        required
        {...fieldLayout}
        className="relative"
        label={intl(
          'saenext.DeploySelectorField.web-app.JavaFormItem.PackagetextpackagetypePackageVersion',
          { PackageTextPackageType: PackageText[packageType] },
        )}
      >
        <Input
          trim
          hasLimitHint
          maxLength={16}
          className="full-width"
          {...init('PackageVersion', {
            initValue: new Date().getTime(),
          })}
        />

        <Button
          text
          type="primary"
          className="ml-l timestamp"
          onClick={() => {
            setValue('PackageVersion', new Date().getTime());
          }}
        >
          {intl('saenext.DeploySelectorField.web-app.JavaFormItem.UseTimestampAsVersionNumber')}
        </Button>
      </Form.Item>
      {packageType === PACKAGE_TYPE.WAR ? (
        <Form.Item
          required
          label={intl('saenext.DeploySelectorField.web-app.JavaFormItem.TomcatVersion')}
          {...fieldLayout}
        >
          <Select
            showSearch
            dataSource={TomcatList}
            className="full-width"
            placeholder={intl(
              'saenext.DeploySelectorField.web-app.JavaFormItem.SelectTomcatVersion',
            )}
            {...init('Container', {
              initValue: 'apache-tomcat-7',
            })}
          />
        </Form.Item>
      ) : null}

      <Form.Item
        required
        label={intl('saenext.DeploySelectorField.web-app.JavaFormItem.JdkVersion')}
        {...fieldLayout}
      >
        <Select
          showSearch
          dataSource={JdkList}
          className="full-width"
          {...init('Jdk', {
            initValue: jdkVersion,
            props: {
              onChange: (value: string) => setJdkVersion(value),
            },
          })}
        />
      </Form.Item>
      {/* <Form.Item
          required
          label="时区设置"
          {...fieldLayout}
         >
          <Select
            className="full-width"
            {...init("Timezone", {
              initValue: 'Asia/Shanghai',
            })}
          >
            <Select.Option id="shanghai" value="Asia/Shanghai">UTC+8</Select.Option>
            <Select.Option id="universal" value="Universal">UTC+0</Select.Option>
          </Select>
         </Form.Item> */}
      <Form.Item
        label={intl('saenext.DeploySelectorField.web-app.JavaFormItem.StartCommand')}
        {...fieldLayout}
      >
        <ShellEditor
          {...init('RunCommand', {
            initValue: startCmd,
            props: {
              onChange: (value) => setStartCmd(value),
            },
          })}
        />
      </Form.Item>
    </>
  );
};

export default JavaFormItem;
