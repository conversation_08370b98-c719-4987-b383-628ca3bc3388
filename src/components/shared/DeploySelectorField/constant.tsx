import { intl } from '@ali/xconsole';
import React from 'react';
import TextWithBalloon from '../TextWithBalloon';
import CachedData from '~/cache/common';

export enum REPO_TYPE {
  // repository
  GITHUB = 'github',
  GITEE = 'gitee',
  GITLAB = 'gitlab',
  CODEUP = 'codeup',
}

export const C_REPOSITORY = {
  [REPO_TYPE.GITHUB]: {
    // label: intl('applications.repository.github.label'),
    label: 'GitHub',
    svg: (height = 30, width = 24) => (
      <svg
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="2632"
        width={width}
        height={height}
      >
        <path
          d="M64 512c0 195.2 124.8 361.6 300.8 422.4 22.4 6.4 19.2-9.6 19.2-22.4v-76.8c-134.4 16-140.8-73.6-150.4-89.6-19.2-32-60.8-38.4-48-54.4 32-16 64 3.2 99.2 57.6 25.6 38.4 76.8 32 105.6 25.6 6.4-22.4 19.2-44.8 35.2-60.8-144-22.4-201.6-108.8-201.6-211.2 0-48 16-96 48-131.2-22.4-60.8 0-115.2 3.2-121.6 57.6-6.4 118.4 41.6 124.8 44.8 32-9.6 70.4-12.8 112-12.8 41.6 0 80 6.4 112 12.8 12.8-9.6 67.2-48 121.6-44.8 3.2 6.4 25.6 57.6 6.4 118.4 32 38.4 48 83.2 48 131.2 0 102.4-57.6 188.8-201.6 214.4 22.4 22.4 38.4 54.4 38.4 92.8v112c0 9.6 0 19.2 16 19.2C832 876.8 960 710.4 960 512c0-246.4-201.6-448-448-448S64 265.6 64 512z"
          p-id="2633"
        ></path>
      </svg>
    ),

    disabled: false,
    url: 'https://github.com/login/oauth/authorize?client_id=86059a1b2bb20d3e5fc3&scope=repo,repo:status,delete_repo',
  },
  [REPO_TYPE.GITEE]: {
    // label: intl('applications.repository.gitee.label'),
    label: 'Gitee',
    svg: (height = 30, width = 24) => (
      <svg
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="32470"
        width={width}
        height={height}
      >
        <path
          d="M512 1000.12c-268.466 0-488.12-219.654-488.12-488.12S243.533 23.88 512 23.88 1000.12 243.533 1000.12 512 780.467 1000.12 512 1000.12z m247.111-543.034H481.492c-12.203 0-24.406 12.203-24.406 24.406v61.016c0 12.203 12.203 24.406 24.406 24.406h167.792c12.203 0 24.406 12.203 24.406 24.406v12.203c0 39.66-33.558 73.218-73.218 73.218H371.665c-12.203 0-24.406-12.203-24.406-24.406V420.477c0-39.66 33.559-73.218 73.218-73.218h338.634c12.203 0 24.406-12.203 24.406-24.406v-61.015c0-12.203-12.203-24.406-24.406-24.406H420.477c-100.675 0-179.994 82.37-179.994 179.995V756.06c0 12.203 12.203 24.406 24.406 24.406h356.938c88.472 0 161.69-73.218 161.69-161.69V481.492c0-12.203-12.203-24.406-24.406-24.406z"
          fill="#c71d23"
          p-id="32471"
        ></path>
      </svg>
    ),

    disabled: false,
    url: `https://gitee.com/oauth/authorize?client_id=ade27888816a6ee0d451ae34ded436588cebebc320b8c1b8f22bad2d7f29da36&redirect_uri=${CachedData.confLink('feature:fcnext:url')}/connected/gitee&response_type=code`,
  },
  [REPO_TYPE.GITLAB]: {
    // label: intl('application.repository.self.gitlab.label'),
    label: 'GitLab',
    svg: (height = 30, width = 24) => (
      <svg
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="37132"
        width={width}
        height={height}
      >
        <path
          d="M932.317184 567.76704L885.10464 422.46144l-93.57312-287.997952c-4.8128-14.81728-25.776128-14.81728-30.590976 0L667.36128 422.459392H356.62848L263.051264 134.46144c-4.8128-14.81728-25.776128-14.81728-30.593024 0l-93.57312 287.997952-47.210496 145.309696a32.165888 32.165888 0 0 0 11.68384 35.96288l408.6272 296.890368L920.61696 603.734016c11.272192-8.192 15.990784-22.71232 11.68384-35.964928"
          fill="#FC6D26"
          p-id="37133"
        ></path>
        <path
          d="M512.002048 900.62848l155.365376-478.171136H356.634624z"
          fill="#e24328"
          p-id="37134"
          data-spm-anchor-id="a313x.7781069.0.i25"
        ></path>
        <path
          d="M512.004096 900.62848L356.63872 422.47168H138.901504z"
          fill="#fc6d26"
          p-id="37135"
          data-spm-anchor-id="a313x.7781069.0.i28"
        ></path>
        <path
          d="M138.891264 422.465536l-47.214592 145.309696a32.16384 32.16384 0 0 0 11.685888 35.96288L511.991808 900.62848z"
          fill="#FCA326"
          p-id="37136"
        ></path>
        <path
          d="M138.893312 422.459392h217.737216L263.053312 134.46144c-4.8128-14.819328-25.778176-14.819328-30.590976 0z"
          fill="#E24329"
          p-id="37137"
        ></path>
        <path
          d="M512.002048 900.62848l155.365376-478.154752H885.10464z"
          fill="#fc6d26"
          p-id="37138"
          data-spm-anchor-id="a313x.7781069.0.i27"
        ></path>
        <path
          d="M885.11488 422.465536l47.214592 145.309696a32.16384 32.16384 0 0 1-11.685888 35.96288L512.014336 900.62848z"
          fill="#FCA326"
          p-id="37139"
        ></path>
        <path
          d="M885.096448 422.459392H667.36128l93.577216-287.997952c4.814848-14.819328 25.778176-14.819328 30.590976 0z"
          fill="#E24329"
          p-id="37140"
        ></path>
      </svg>
    ),

    disabled: false,
    // balloonText: intl('application.repository.self.gitlab.help'),
  },
  [REPO_TYPE.CODEUP]: {
    // label: intl('application.repository.self.codeup.label'),
    label: 'Codeup',
    svg: (height = 30, width = 24) => (
      <svg
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="37132"
        width={width}
        height={height}
      >
        <defs>
          <linearGradient x1="0%" y1="50%" x2="100.126%" y2="50%" id="a">
            <stop stop-color="#006ad4" stop-opacity=".5" offset="0%" />
            <stop stop-color="#006ad4" stop-opacity=".2" offset="100%" />
          </linearGradient>
          <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="b">
            <stop stop-color="#006ad4" offset="0%" />
            <stop stop-color="#006ad4" stop-opacity=".5" offset="100%" />
          </linearGradient>
        </defs>
        <g transform="translate(36 24) scale(5, 5)" fill="none">
          <path
            d="M94.84 148.36c-22.8 0-42.022-14.923-49.398-35.952-2.235-6.33-4.47-13.566-5.141-25.55-.224-5.427.223-10.628.223-10.628 6.93-19.898 27.717-31.656 51.633-32.56 20.34-1.13 38.445 9.723 47.833 26.23l36.657-22.16h.224C160.107 18.572 128.367-.874 92.38.03 42.76 1.16 2.079 42.087.068 92.284-1.944 147.004 41.195 192 95.063 192c29.951 0 56.774-14.019 74.208-35.952 1.565-2.035 1.341-4.974-.67-6.557l-27.046-21.255c-2.012-1.583-4.694-1.13-6.259.679-9.835 11.757-24.14 19.445-40.457 19.445Z"
            fill="url(#a)"
          />

          <path
            d="m168.6 149.49-27.045-21.254c-2.012-1.583-4.694-1.13-6.259.679-9.835 11.757-24.14 19.445-40.457 19.445-22.798 0-42.021-14.923-49.397-35.952-2.235-6.33-4.47-13.566-5.141-25.55-.224-5.427.223-10.628.223-10.628v-.452c3.8-40.7 37.775-72.13 78.679-72.582A95.87 95.87 0 0 0 92.381.03C42.76 1.16 2.079 42.087.068 92.284-1.944 147.004 41.195 192 95.063 192c29.951 0 56.774-14.019 74.208-35.952 1.565-2.035 1.341-4.974-.67-6.557Z"
            fill="url(#b)"
          />

          <ellipse fill="#006ad4" cx="158.542" cy="59.046" rx="21.458" ry="21.707" />
        </g>
      </svg>
    ),

    disabled: false,
    // balloonText: intl('application.repository.self.codeuo.help'),
  },
};

export enum TRIGGER_TYPE {
  PUSH = 'PUSH',
  NONE = 'NONE',
  TAG = 'TAG',
}

export const C_TRIGGER = {
  [TRIGGER_TYPE.PUSH]: {
    label: intl('saenext.shared.DeploySelectorField.constant.PushToTheSpecifiedBranch'),
    help: intl('saenext.shared.DeploySelectorField.constant.EachTimeANewCode'),
  },
  [TRIGGER_TYPE.NONE]: {
    label: intl('saenext.shared.DeploySelectorField.constant.ManuallyTriggerBuild'),
    help: intl('saenext.shared.DeploySelectorField.constant.WhenYouSelectManualTrigger'),
  },
  [TRIGGER_TYPE.TAG]: {
    label: intl('saenext.shared.DeploySelectorField.constant.TagReleaseEvents'),
    help: intl('saenext.shared.DeploySelectorField.constant.ThisTypeOfEventIs'),
  },
};

export const personalRepoText = {
  default: {
    help: intl('saenext.shared.DeploySelectorField.constant.ByDefaultThePlatformImage'),
  },
};

export enum DEPLOY_TYPE {
  IMAGE = 'image',
  REPOISTORY = 'repoistory',
  MICRO_PACKAGE = 'micro-package',
  WEB_PACKAGE = 'web-package',
}

export enum LANGUAGE_TYPE {
  JAVA = 'java',
  PHP = 'php',
  PYTHON = 'python',
  GO = 'golang',
  DOTNET = 'dotnet',
  OTHER = 'other',
}

export const LANGUAGE_NAME = {
  [LANGUAGE_TYPE.JAVA]: 'Java',
  [LANGUAGE_TYPE.PHP]: 'PHP',
  [LANGUAGE_TYPE.PYTHON]: 'Python',
  [LANGUAGE_TYPE.GO]: 'Golang',
  [LANGUAGE_TYPE.DOTNET]: '.NET',
}

export enum PACKAGE_TYPE {
  WAR = 'War',
  JAR = 'FatJar',
  PHPZIP = 'PhpZip',
  PYTHONZIP = 'PythonZip',
  DOTNETZIP = 'DotnetZip',
}

export enum UPLOAD_TYPE {
  UPLOAD = 'upload',
  URL = 'url',
}

export enum BUILD_TYPE {
  BUILDPACK = 'Buildpack',
  DOCKERFILE = 'Dockerfile',
  STATIC = 'Static',
}

export const repoUrl = {
  github: {
    console: 'https://github.com',
    oauth:
      `https://github.com/login/oauth/authorize?client_id=c2e63d70ef6944eac1c9&redirect_url=${CachedData.confLink('feature:saenext:url')}/connected?provider=github&scope=repo%20admin:repo_hook%20admin:org`,
  },
  gitee: {
    console: 'https://gitee.com',
    oauth:
      `https://gitee.com/oauth/authorize?client_id=a7dfd76d3266c5e36e64c0391e74981515e0bc5f3d94129dcd17df8b7b5e2821&redirect_uri=${CachedData.confLink('feature:saenext:url')}/connected?provider=gitee&response_type=code&scope=user_info%20projects%20hook`,
  },
  gitlab: {
    console: 'https://gitlab.com',
    oauth:
      `https://gitlab.com/oauth/authorize?client_id=4bec5e4593294ff75a42d968b99c67bb79ed360d361f7c632526b8b96a08c847&redirect_uri=${CachedData.confLink('feature:saenext:url')}/connected?provider=gitlab&response_type=code&scope=api+read_user+read_api+read_repository+write_repository`,
  },
  codeup: {
    console: 'https://codeup.aliyun.com',
    oauth:
      `https://codeup.aliyun.com/oauth/authorize?app_id=s0f80e6s00a3e58f7985a&redirect_uri=${CachedData.confLink('feature:saenext:url')}/connected/codeup&state=123`,
  },
};

export const DEFAULT_REPOISTORY_VALUE = {
  type: DEPLOY_TYPE.REPOISTORY,
  CodeConfig: {},
  BuildConfig: {},
};

export const DEFAULT_MICROPACKAGE_VALUE = {
  type: DEPLOY_TYPE.MICRO_PACKAGE,
  PackageUrl: '',
};

export const DEFAULT_WEBPACKAGE_VALUE = {
  type: DEPLOY_TYPE.WEB_PACKAGE,
  PackageUrl: '',
};

export const DEFAULT_ACR_ITEM = {
  value: Date().toString(),
  label: intl('saenext.shared.DeploySelectorField.constant.AutomaticCreation'),
};

export const JdkList = [
  { label: 'Open JDK 8', value: 'openjdk-8' },
  { label: 'Open JDK 11', value: 'openjdk-11' },
  { label: 'Dragonwell 8', value: 'dragonwell-8' },
  { label: 'Dragonwell 11', value: 'dragonwell-11' },
  { label: 'Dragonwell 17', value: 'dragonwell-17' },
];

export const TomcatList = [
  { label: 'apache-tomcat-7', value: 'apache-tomcat-7' },
  { label: 'apache-tomcat-8', value: 'apache-tomcat-8' },
];

export const IMAGE_LANG_ICONS = [
  {
    value: LANGUAGE_TYPE.JAVA,
    label: (
      <>
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="6927"
          width="32"
          height="32"
        >
          <path
            d="M558.08 472.064c48.128 53.248-13.312 103.424-13.312 103.424s119.808-61.44 65.536-139.264c-51.2-71.68-91.136-107.52 122.88-232.448 0 1.024-335.872 86.016-175.104 268.288"
            fill="#FF0000"
            p-id="6928"
          ></path>
          <path
            d="M610.304 5.12s101.376 101.376-96.256 258.048C356.352 389.12 478.208 460.8 514.048 543.744 420.864 459.776 354.304 386.048 399.36 317.44 463.872 216.064 651.264 166.912 610.304 5.12"
            fill="#FF0000"
            p-id="6929"
          ></path>
          <path
            d="M720.896 757.76c183.296-95.232 98.304-188.416 39.936-175.104-15.36 3.072-21.504 5.12-21.504 5.12s5.12-8.192 16.384-11.264c117.76-40.96 207.872 120.832-37.888 186.368-1.024 0 2.048-3.072 3.072-5.12m-337.92 38.912s-37.888 21.504 26.624 29.696c76.8 8.192 117.76 8.192 202.752-8.192 0 0 23.552 15.36 53.248 26.624-191.488 80.896-433.152-5.12-282.624-48.128m-23.552-106.496s-43.008 31.744 23.552 37.888c82.944 8.192 149.504 10.24 261.12-13.312 0 0 16.384 16.384 40.96 24.576-231.424 68.608-490.496 5.12-325.632-49.152"
            fill="#6699FF"
            p-id="6930"
          ></path>
          <path
            d="M811.008 876.544s27.648 23.552-31.744 40.96c-111.616 34.816-460.8 45.056-558.08 2.048-34.816-15.36 31.744-35.84 51.2-40.96 21.504-5.12 34.816-3.072 34.816-3.072-38.912-28.672-251.904 52.224-107.52 75.776 390.144 62.464 712.704-28.672 611.328-74.752M400.384 578.56s-178.176 43.008-63.488 56.32c49.152 6.144 146.432 5.12 235.52-3.072 73.728-6.144 147.456-19.456 147.456-19.456s-26.624 11.264-45.056 24.576c-181.248 48.128-530.432 26.624-430.08-23.552 88.064-39.936 155.648-34.816 155.648-34.816"
            fill="#6699FF"
            p-id="6931"
          ></path>
          <path
            d="M418.816 1015.808c176.128 11.264 446.464-6.144 453.632-90.112 0 0-13.312 31.744-146.432 56.32-150.528 27.648-336.896 24.576-446.464 6.144 2.048 1.024 24.576 20.48 139.264 27.648"
            fill="#6699FF"
            p-id="6932"
          ></path>
        </svg>
        <div className="text-bold mt-s">Java</div>
      </>
    ),
  },
  {
    value: LANGUAGE_TYPE.PHP,
    label: (
      <>
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="9065"
          width="32"
          height="32"
        >
          <path
            d="M306.40355555 441.68533333h-35.38488888L251.33511111 542.72h28.78577778c22.18666667 0 38.68444445-4.096 49.152-12.17422222 10.35377778-7.96444445 17.52177778-21.73155555 21.27644444-41.07377778 3.52711111-17.97688889 2.16177778-30.60622222-3.98222222-37.31911111-6.25777778-6.94044445-19.79733333-10.46755555-40.16355556-10.46755556zM761.51466667 441.68533333h-35.38488889L706.44622222 542.72h28.78577778c22.18666667 0 38.68444445-4.096 49.152-12.17422222 10.35377778-7.96444445 17.52177778-21.73155555 21.27644445-41.07377778 3.52711111-17.97688889 2.16177778-30.60622222-3.98222223-37.31911111-6.25777778-6.94044445-19.79733333-10.46755555-40.16355555-10.46755556z"
            fill="#787db0"
            p-id="9066"
          ></path>
          <path
            d="M512 244.96355555C231.53777778 244.96355555 4.20977778 364.544 4.20977778 512s227.328 267.03644445 507.67644444 267.03644445S1019.79022222 659.456 1019.79022222 512 792.46222222 244.96355555 512 244.96355555zM383.31733333 564.224c-12.17422222 11.37777778-25.94133333 19.56977778-40.84622222 24.46222222-14.67733333 4.77866667-33.56444445 7.168-56.09244444 7.168h-45.39733334l-12.97066666 66.67377778c-0.45511111 2.61688889-2.73066667 4.43733333-5.46133334 4.43733333h-58.25422222c-1.59288889 0-3.18577778-0.68266667-4.20977778-2.048-1.024-1.25155555-1.47911111-2.95822222-1.13777778-4.55111111l51.99644445-267.60533333c0.45511111-2.61688889 2.73066667-4.43733333 5.46133333-4.43733334h112.07111112c35.27111111 0 61.44 9.55733333 77.93777777 28.44444445 16.61155555 19.00088889 21.73155555 45.51111111 15.24622223 78.848-2.61688889 13.53955555-7.168 26.16888889-13.42577778 37.43288889-6.37155555 11.37777778-14.79111111 21.84533333-24.91733334 31.17511111zM551.02577778 593.92c-1.024-1.25155555-1.47911111-2.95822222-1.13777778-4.55111111l22.98311111-118.44266667c2.16177778-11.264 1.59288889-19.34222222-1.47911111-22.75555555-1.93422222-2.048-7.73688889-5.57511111-25.03111111-5.57511112h-41.64266667l-28.89955555 148.93511112c-0.45511111 2.61688889-2.73066667 4.43733333-5.46133334 4.43733333h-57.79911111c-1.59288889 0-3.18577778-0.68266667-4.20977777-2.048-1.024-1.25155555-1.47911111-2.95822222-1.13777778-4.55111111l51.99644444-267.60533334c0.45511111-2.61688889 2.73066667-4.43733333 5.46133334-4.43733333h57.7991111c1.59288889 0 3.18577778 0.68266667 4.20977778 2.048 1.024 1.25155555 1.47911111 2.95822222 1.13777778 4.55111111l-12.51555556 64.62577778h44.82844445c34.13333333 0 57.344 6.03022222 70.88355555 18.432 13.76711111 12.62933333 18.09066667 32.88177778 12.74311112 60.07466667L619.52 591.64444445c-0.45511111 2.61688889-2.73066667 4.43733333-5.46133333 4.43733333h-58.70933334c-1.70666667-0.11377778-3.29955555-0.91022222-4.32355555-2.16177778z m325.632-98.304c-2.61688889 13.53955555-7.168 26.16888889-13.53955556 37.43288889-6.37155555 11.264-14.67733333 21.73155555-24.80355555 31.06133333-12.17422222 11.37777778-25.94133333 19.56977778-40.84622222 24.46222223-14.67733333 4.77866667-33.56444445 7.168-56.09244445 7.168h-45.39733333l-12.97066667 66.67377777c-0.45511111 2.61688889-2.73066667 4.43733333-5.46133333 4.43733333h-58.25422222c-1.59288889 0-3.18577778-0.68266667-4.20977778-2.048-1.024-1.25155555-1.47911111-2.95822222-1.13777778-4.5511111l51.99644444-267.60533334c0.45511111-2.61688889 2.73066667-4.43733333 5.46133334-4.43733333h112.07111111c35.27111111 0 61.44 9.55733333 77.93777777 28.44444444 16.61155555 19.11466667 21.73155555 45.62488889 15.24622223 78.96177778z"
            fill="#787db0"
            p-id="9067"
          ></path>
        </svg>
        <div className="text-bold mt-s">PHP</div>
      </>
    ),
  },
  {
    value: LANGUAGE_TYPE.PYTHON,
    label: (
      <>
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="10186"
          width="32"
          height="32"
        >
          <path
            d="M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z"
            fill="#3C78AA"
            p-id="10187"
          ></path>
          <path
            d="M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z"
            fill="#FDD835"
            p-id="10188"
          ></path>
        </svg>
        <div className="text-bold mt-s">Python</div>
      </>
    ),
  },
  {
    value: LANGUAGE_TYPE.GO,
    label: (
      <>
        <img src="https://img.alicdn.com/imgextra/i2/O1CN01OJylIk1XJcmektkyX_!!6000000002903-55-tps-205-205.svg" width="32" height="32" />
        <div className="text-bold mt-s">Go</div>
      </>
    ),
  },
  {
    value: LANGUAGE_TYPE.DOTNET,
    label: (
      <>
        <img
          src="https://img.alicdn.com/imgextra/i2/O1CN01iAulPh1TuwaFETH1y_!!6000000002443-55-tps-254-254.svg"
          width="32"
          height="32"
        />
        <div className="text-bold mt-s">.NET Core</div>
      </>
    ),
  },
  {
    value: LANGUAGE_TYPE.OTHER,
    label: (
      <>
        <img
          src="https://img.alicdn.com/imgextra/i3/O1CN01cC3PlG1ywY3yJF8L0_!!6000000006643-55-tps-200-200.svg"
          width="32"
          height="32"
        />
        <div className="text-bold mt-s">
          <TextWithBalloon
            text={intl('saenext.shared.DeploySelectorField.constant.OtherLanguages')}
            tips={intl('saenext.shared.DeploySelectorField.constant.ForExampleCNodeJs')}
          />
        </div>
      </>
    ),
  },
];
