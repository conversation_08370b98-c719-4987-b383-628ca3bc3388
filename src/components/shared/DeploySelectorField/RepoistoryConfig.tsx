import { ConsoleContext, intl } from '@ali/cnd';
import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle,
  useContext,
} from 'react';
import { Step, Select, Radio, Icon, Button, Dialog, Message, Checkbox } from '@ali/cnd';
import { TextField } from '@ali/deep';
import Form from '@ali/deep-form';
import services from '~/services';
import ExternalLink from '~/components/shared/ExternalLink';
import {
  C_REPOSITORY,
  REPO_TYPE,
  TRIGGER_TYPE,
  C_TRIGGER,
  personalRepoText,
  repoUrl,
  DEFAULT_REPOISTORY_VALUE,
  DEFAULT_ACR_ITEM,
  BUILD_TYPE,
} from './constant';
import { ListNamespace, ListRepoByNamespace, GetUserInfo } from '~/services/acr';
import { find, isEqual, map, includes } from 'lodash';
import { getRepoFullUrl } from '~/utils/global';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';

const RadioGroup = Radio.Group;
const FinalOrganizationId = '-1';
const fieldLayout = {
  wrapperCol: { align: 'center' },
  labelTextAlign: 'left',
  labelCol: { style: { minWidth: 170, maxWidth: 170, marginRight: 16 } },
};

const standardRepo = {
  [REPO_TYPE.GITHUB]: 'GitHub',
  [REPO_TYPE.GITEE]: 'Gitee',
  [REPO_TYPE.CODEUP]: 'Codeup',
  [REPO_TYPE.GITLAB]: 'GitLab',
};

const repoTypeSource = map(C_REPOSITORY, (value: any, key) => ({
  value: key,
  label: (
    <>
      {value.svg()}
      <div className="text-bold">{value.label}</div>
    </>
  ),
}));

const RepoistoryConfig = forwardRef(({ initValue, style = {} }: any, ref) => {
  let sourceForm = useRef(null);
  // loading status
  const [accountLoading, setAccountLoading] = useState(false);
  const [repoLoading, setRepoLoading] = useState(false);
  const [branchLoading, setBranchLoading] = useState(false);
  const [namespaceLoading, setNamespaceLoading] = useState(false);
  const [imageRepoLoading, setImageRepoLoading] = useState(false);
  // list value
  const [accountDataSource, setAccountDataSource] = useState([]);
  const [repoDataSource, setRepoDataSource] = useState([]);
  const [branchDataSource, setBranchDataSource] = useState([]);
  // code config
  const [provider, setProvider] = useState('');
  const [accountId, setAccountId] = useState('');
  const [organizationId, setOrganizationId] = useState('');
  const [repoId, setRepoId] = useState('');
  const [repoFullName, setRepoFullName] = useState('');
  const [branchName, setBranchName] = useState('');
  // const [branchId, setBranchId] = useState(''); // for CommitId

  // build config
  const [buildType, setBuildType] = useState<BUILD_TYPE>(BUILD_TYPE.BUILDPACK);
  const [triggerType, setTriggerType] = useState<TRIGGER_TYPE>(TRIGGER_TYPE.PUSH);
  const [tagName, setTagName] = useState('');
  const [beforeBuildCommand, setBeforeBuildCommand] = useState('');
  const [runCommand, setRunCommand] = useState('');
  const [workingDir, setWorkingDir] = useState('./');
  const [dockerfilePath, setDockerfilePath] = useState('Dockerfile');
  // acr
  const [isPersonalRepoEnabled, setIsPersonalRepoEnabled] = useState(false);
  const [needOpenCr, setNeedOpenCr] = useState(false);
  const [namespace, setNamespace] = useState('');
  const [repository, setRepository] = useState('');
  const [namespaceList, setNamespaceList] = useState([]);
  const [imageRepoList, setImageRepoList] = useState([]);
  const [updateTraffic, setUpdateTraffic] = useState(false);

  // const [showAdvance, setShowAdvance] = useState(false);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const isGithubOrCodeup = useMemo(() => {
    return provider === REPO_TYPE.GITHUB || provider === REPO_TYPE.CODEUP;
  }, [provider]);

  const organizationDataSource = useMemo(() => {
    if (isGithubOrCodeup) {
      const res = find(accountDataSource, { value: accountId }) || {};
      const { Id, Name, Organizations = [] } = res;
      const dataSource = map(Organizations, (e) => ({
        ...e,
        label: e.Name,
        value: e.Id,
      }));
      if (provider === REPO_TYPE.GITHUB) {
        return [
          {
            label: intl('saenext.shared.DeploySelectorField.RepoistoryConfig.PersonalWarehouse'),
            children: [{ label: Name, value: FinalOrganizationId }],
          },
          {
            label: intl(
              'saenext.shared.DeploySelectorField.RepoistoryConfig.OrganizationWarehouse',
            ),
            children: dataSource,
          },
        ];
      }
      return dataSource;
    } else {
      return [];
    }
  }, [isGithubOrCodeup, accountDataSource, accountId, provider]);

  const handleUnbind = (AccountId) => {
    services
      .unbindSourceCodeAccount({
        Provider: provider,
        AccountId,
      })
      .then((res) => {
        if (res) {
          Message.success(
            intl('saenext.shared.DeploySelectorField.RepoistoryConfig.UnboundSuccessfully'),
          );
          getAccountsList();
          setOrganizationId('');
          setRepoId('');
          setBranchName('');
          setRepoDataSource([]);
          setBranchDataSource([]);
        }
      });
  };

  const getAccountsList = async (Provider = provider) => {
    setAccountLoading(true);
    setAccountId('');
    setOrganizationId('');
    setRepoId('');
    setBranchName('');
    setRepoDataSource([]);
    setBranchDataSource([]);
    const result = await services.listSourceCodeAccounts({
      Provider,
    });
    if (result) {
      const { Accounts } = result;
      const tmp = Accounts.map((e) => {
        return { ...e, value: e.Id };
      });
      setAccountDataSource(tmp);
    }
    setAccountLoading(false);
    return result.Accounts.map((e) => {
      return { ...e, value: e.Id };
    });
  };

  const getRepoList = async (
    Provider = provider,
    AccountId = accountId,
    OrganizationId = organizationId,
  ) => {
    setRepoLoading(true);
    setRepoId('');
    setRepoDataSource([]);
    setBranchName('');
    setBranchDataSource([]);
    const result = await services.listSourceCodeRepositories({
      Provider,
      AccountId,
      OrganizationId: OrganizationId === FinalOrganizationId ? '' : OrganizationId,
    });
    if (result) {
      setRepoDataSource(
        result.Repos.map((e) => {
          return { ...e, value: e.Id, label: provider !== REPO_TYPE.CODEUP ? e.FullName : e.Name };
        }),
      );
    }
    setRepoLoading(false);
  };

  const getBranchList = async (
    Provider = provider,
    AccountId = accountId,
    OrganizationId = organizationId,
    RepoId = repoId,
    RepoFullName = repoFullName,
  ) => {
    setBranchLoading(true);
    setBranchName('');
    setBranchDataSource([]);
    const result = await services.listSourceCodeRepoBranches({
      Provider,
      AccountId,
      OrganizationId,
      RepoId,
      RepoFullName,
    });
    if (result) {
      setBranchDataSource(
        result.Branches.map((e) => {
          return { ...e, value: e.Name, label: e.Name };
        }),
      );
    }
    setBranchLoading(false);
  };

  const getNamespaceList = async () => {
    setNamespaceLoading(true);
    try {
      await GetUserInfo();
    } catch (ex) {
      if (ex.message === 'user is not registered.') {
        setNeedOpenCr(true);
        setNamespaceLoading(false);
        return;
      }
    }
    setNeedOpenCr(false);
    const { data } = await ListNamespace();
    // 命名空间为3个后则不支持自动创建
    if (data.namespaces.length >= 3) {
      setNamespaceList(
        data.namespaces.map((e) => {
          return { ...e, value: e.namespace };
        }),
      );
    } else {
      setNamespaceList([
        DEFAULT_ACR_ITEM,
        ...data.namespaces.map((e) => {
          return { ...e, value: e.namespace };
        }),
      ]);
      setNamespace(DEFAULT_ACR_ITEM.value);
    }
    setImageRepoList([DEFAULT_ACR_ITEM]);
    setRepository(DEFAULT_ACR_ITEM.value);
    setNamespaceLoading(false);
  };

  const getRepoListData = async (RepoNamespace, Page = 1, tmpData = []) => {
    const { data } = await ListRepoByNamespace({
      RepoNamespace,
      Page,
      PageSize: 30,
    });
    tmpData.push(...data.repos);
    if (tmpData.length < data.total) {
      await getRepoListData(RepoNamespace, Page + 1, tmpData);
    }
    return tmpData.map((e) => {
      return { ...e, value: e.repoName };
    });
  };

  const getImageRepoList = async (RepoNamespace = namespace) => {
    if (!RepoNamespace) {
      setImageRepoLoading(true);
    }
    const data = await getRepoListData(RepoNamespace);
    setImageRepoList([DEFAULT_ACR_ITEM, ...data]);
    setImageRepoLoading(false);
  };

  const handleNamespace = async (val) => {
    try {
      setNamespace(val);
      await getImageRepoList(val);
      setRepository(DEFAULT_ACR_ITEM.value);
    } catch (err) {
      return err;
    }
  };

  useImperativeHandle(ref, () => ({
    submit: () => {
      let result;
      sourceForm['field'].validate(async (error, values) => {
        if (!error) {
          values = { ...initValue, ...values };
          const {
            Provider,
            BranchName,
            RepoFullName = repoFullName,
            RepoId,
            BuildType,
            TriggerType,
            UpdateTraffic,
            TagName,
            BeforeBuildCommand,
            RunCommand,
            WorkingDir,
            DockerfilePath,
            Namespace,
            Repository,
            IsPersonalRepoEnabled,
          } = values;
          result = {
            CodeConfig: {
              Provider,
              AccountId: accountId,
              OrganizationId: organizationId,
              BranchName,
              RepoFullName,
              RepoId,
            },
            BuildConfig: {
              BuildType,
              BeforeBuildCommand,
              RunCommand,
              WorkingDir,
              DockerfilePath,
            },
            ImageConfig: {
              Namespace: Namespace === DEFAULT_ACR_ITEM.value ? '' : Namespace,
              Repository: Repository === DEFAULT_ACR_ITEM.value ? '' : Repository,
              InstanceType: IsPersonalRepoEnabled === true ? 'USER_ACR' : 'SHARE_ACREE',
            },
            DeployConfig: {
              UpdateTraffic,
            },
            TriggerConfig: {
              Type: TriggerType, // 本期默认固定push触发
              BranchName: TriggerType === TRIGGER_TYPE.PUSH ? BranchName : undefined,
              TagName,
            },
          };
        } else {
          result = {
            error: intl('saenext.shared.DeploySelectorField.RepoistoryConfig.PleaseReCheckTheForm'),
          };
          const keys = Object.keys(error);
          const step2Keys = ['Namespace', 'Repository'];
          for (let i = 0; i < keys.length; i++) {
            if (step2Keys.indexOf(keys[i]) < 0) {
              return result;
            }
          }
        }
      });
      return result;
    },
  }));

  useEffect(() => {
    if (initValue && !isEqual(initValue, DEFAULT_REPOISTORY_VALUE)) {
      // 回填
      const {
        CodeConfig = {},
        BuildConfig = {},
        ImageConfig = {},
        TriggerConfig = {},
        DeployConfig = {},
      } = initValue;
      const { Provider, AccountId, OrganizationId, RepoId, RepoFullName, BranchName } = CodeConfig;
      const {
        RunCommand,
        BeforeBuildCommand,
        WorkingDir,
        BuildType,
        DockerfilePath = 'Dockerfile',
      } = BuildConfig;
      const { Namespace, Repository } = ImageConfig;
      const { Type, TagName } = TriggerConfig;
      const { UpdateTraffic = false } = DeployConfig;
      setTriggerType(Type);
      setTagName(TagName);
      setRunCommand(RunCommand);
      setBeforeBuildCommand(beforeBuildCommand || BeforeBuildCommand); // todo
      setWorkingDir(WorkingDir);
      setBuildType(BuildType);
      setDockerfilePath(DockerfilePath);
      setUpdateTraffic(UpdateTraffic);

      if (Provider) {
        setProvider(Provider);
        getAccountsList(Provider).then((tmp) => {
          if (!tmp.find((item) => item.value === AccountId)) {
            Message.notice(
              intl('saenext.shared.DeploySelectorField.RepoistoryConfig.TheOriginalAccountHasBeen'),
            );
            return;
          }
          if (AccountId) {
            getRepoList(Provider, AccountId, OrganizationId);
            setAccountId(AccountId);
            setOrganizationId(OrganizationId);
            if (RepoId && RepoFullName) {
              getBranchList(Provider, AccountId, OrganizationId, RepoId, RepoFullName);
              setRepoId(RepoId);
              setRepoFullName(RepoFullName);
              setBranchName(BranchName);
            }
          }
        });
      }
      if (Namespace) {
        setIsPersonalRepoEnabled(true);
        getNamespaceList().then(() => {
          setNamespace(Namespace);
          getImageRepoList(Namespace);
          setRepository(Repository);
        });
      }
    } else {
      setProvider(REPO_TYPE.GITHUB);
      getAccountsList(REPO_TYPE.GITHUB);
    }
  }, [initValue]);

  useEffect(() => {
    // ACR鉴权
    getNamespaceList();
    // 监听git授权
    const handler = (e) => {
      if (e.key === 'authTrigger' && e.newValue) {
        getAccountsList(e.newValue);
        localStorage.removeItem(e.key);
      }
    };
    window.addEventListener('storage', handler);
    return () => {
      window.removeEventListener('storage', handler);
    };
  }, []);

  const gotoAuth = () => {
    services
      .getAuthorizationUrl({
        Provider: provider,
      })
      .then((res) => {
        res.url && open(res.url);
        localStorage.setItem('authRegionId', regionId);
      });
  };

  const accountMenuItemRender = (item: any, showUnbind: boolean) => {
    return (
      item.value && (
        <div className="account-menu-item">
          <span className="flex">
            <img src={item.AvatarUrl} />
            {item.Name}
          </span>
          {showUnbind && (
            <Button
              type="primary"
              text
              onClick={() => {
                Dialog.confirm({
                  title: intl(
                    'saenext.shared.DeploySelectorField.RepoistoryConfig.RevokeAuthorizationFromTheCode',
                  ),
                  content: intl(
                    'saenext.shared.DeploySelectorField.RepoistoryConfig.IfYouUnlockTheApplication',
                    { provider: provider },
                  ),
                  onOk: () => handleUnbind(item.Id),
                });
              }}
            >
              {intl('saenext.shared.DeploySelectorField.RepoistoryConfig.Unbind')}
            </Button>
          )}
        </div>
      )
    );
  };

  const repoMenuItemRender = (item: any) => {
    return (
      item.value && (
        <div className="account-menu-item">
          <span className="flex">{provider !== REPO_TYPE.CODEUP ? item.FullName : item.Name}</span>
          <Button
            type="primary"
            text
            style={{ fontWeight: 400 }}
            onClick={() => {
              open(getRepoFullUrl(provider, item.FullName));
            }}
          >
            {intl('saenext.shared.DeploySelectorField.RepoistoryConfig.ViewWarehouse')}
          </Button>
        </div>
      )
    );
  };

  const renderBuildConfig = useMemo(() => {
    return [
      {
        name: 'DockerfilePath',
        content: (
          <TextField
            required
            name="DockerfilePath"
            value={dockerfilePath}
            onChange={({ value }) => setDockerfilePath(value)}
            label={intl(
              'saenext.shared.DeploySelectorField.RepoistoryConfig.DockerfileDirectoryAndFileName',
            )}
            {...fieldLayout}
            validation={[
              {
                type: 'required',
                message: intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.EnterDockerfileDirectoryAndFile',
                ),
              },
            ]}
          />
        ),

        available: [BUILD_TYPE.DOCKERFILE],
      },
      {
        name: 'WorkingDir',
        content: (
          <TextField
            name="WorkingDir"
            label={intl(
              'saenext.shared.DeploySelectorField.RepoistoryConfig.TheWorkingDirectoryOfThe',
            )}
            help={intl(
              'saenext.shared.DeploySelectorField.RepoistoryConfig.TheWorkingDirectoryBuiltBy',
            )}
            value={workingDir}
            onChange={({ value }) => setWorkingDir(value)}
            {...fieldLayout}
          />
        ),

        available: [BUILD_TYPE.BUILDPACK, BUILD_TYPE.STATIC],
      },
      {
        name: 'RunCommand',
        content: (
          <TextField
            name="RunCommand"
            label={intl('saenext.shared.DeploySelectorField.RepoistoryConfig.StartCommand')}
            help={intl(
              'saenext.shared.DeploySelectorField.RepoistoryConfig.DockerEntrypointStartTheWeb',
            )}
            value={runCommand}
            onChange={({ value }) => setRunCommand(value)}
            {...fieldLayout}
          />
        ),

        available: [BUILD_TYPE.BUILDPACK],
      },
    ].map((e) => e.available.indexOf(buildType) >= 0 && e.content);
  }, [dockerfilePath, workingDir, runCommand, buildType]); // showAdvance

  return (
    <Form
      style={style}
      className="source-config-form"
      ref={(c) => {
        if (c) {
          sourceForm = c.getInstance();
        }
      }}
    >
      <Step stretch direction="ver" labelPlacement="hoz" animation={false}>
        <Step.Item
          title={intl('saenext.shared.DeploySelectorField.RepoistoryConfig.SourceCodeLibrary')}
          status="process"
          content={
            <>
              <Form.Item
                required
                label={intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.CodeRepositoryType',
                )}
                requiredMessage={intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.PleaseSelectAWarehouseType',
                )}
                {...fieldLayout}
              >
                <Radio.Group
                  name="Provider"
                  className="repo-box-group"
                  shape="button"
                  value={provider}
                  onChange={(val: string) => {
                    console.log('val', val);
                    setAccountId('');
                    setOrganizationId('');
                    setRepoId('');
                    setRepoFullName('');
                    setRepoDataSource([]);
                    setBranchName('');
                    setBranchDataSource([]);
                    setProvider(val);
                    getAccountsList(val);
                  }}
                  dataSource={repoTypeSource}
                />
              </Form.Item>
              <Form.Item
                required
                label={intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.WarehouseUsersOrganizations',
                )}
                requiredMessage={intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.SelectARepositoryUser',
                )}
                className="mb-none"
                {...fieldLayout}
              >
                <span className="item-operation item-account">
                  <Select
                    name="AccountId"
                    className={!isGithubOrCodeup ? 'full-width' : 'w-50'}
                    value={accountId}
                    onChange={(value: string) => {
                      setAccountId(value);
                      setOrganizationId('');
                      setRepoId('');
                      setRepoFullName('');
                      setBranchName('');
                      !includes([REPO_TYPE.GITHUB, REPO_TYPE.CODEUP], provider) &&
                        getRepoList(provider, value);
                    }}
                    showSearch
                    state={accountLoading ? 'loading' : undefined}
                    disabled={accountLoading}
                    dataSource={accountDataSource}
                    notFoundContent={
                      <Button
                        type="primary"
                        text
                        style={{ fontWeight: '400' }}
                        onClick={() => {
                          provider && gotoAuth();
                        }}
                      >
                        {intl(
                          'saenext.shared.DeploySelectorField.RepoistoryConfig.NoWarehouseUserIsAuthorized',
                        )}
                      </Button>
                    }
                    itemRender={(item) => accountMenuItemRender(item, true)}
                    valueRender={(item) => accountMenuItemRender(item, false)}
                  />

                  {isGithubOrCodeup && (
                    <Select
                      name="OrganizationId"
                      className={!isGithubOrCodeup ? 'full-width' : 'w-50'}
                      value={organizationId}
                      onChange={(value: string) => {
                        setOrganizationId(value);
                        setRepoId('');
                        setRepoFullName('');
                        setBranchName('');
                        getRepoList(provider, accountId, value);
                      }}
                      showSearch
                      state={accountLoading ? 'loading' : undefined}
                      disabled={accountLoading}
                      dataSource={organizationDataSource}
                    />
                  )}
                </span>
                <Button disabled={!provider} onClick={() => getAccountsList()}>
                  <Icon type="refresh" />
                </Button>
              </Form.Item>
              <Form.Item label=" " {...fieldLayout} className="mb-none">
                <Button
                  className="mr-l"
                  type="primary"
                  text
                  onClick={() => {
                    provider && open(repoUrl[provider].console);
                  }}
                  style={{ fontWeight: 400 }}
                >
                  {intl('saenext.shared.DeploySelectorField.RepoistoryConfig.Go')}
                  {standardRepo[provider]}
                  <Icon type="external-link" size="xs" />
                </Button>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    provider && gotoAuth();
                  }}
                  style={{ fontWeight: 400 }}
                >
                  {intl('saenext.shared.DeploySelectorField.RepoistoryConfig.GoToAuthorization')}
                  <Icon type="external-link" size="xs" />
                </Button>
              </Form.Item>
              <Form.Item
                label={intl('saenext.shared.DeploySelectorField.RepoistoryConfig.WarehouseName')}
                required
                requiredMessage={intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.SelectARepository',
                )}
                help={
                  provider === REPO_TYPE.GITLAB
                    ? intl(
                        'saenext.shared.DeploySelectorField.RepoistoryConfig.OnlyPersonalRepositoriesAreSupported',
                      )
                    : intl(
                        'saenext.shared.DeploySelectorField.RepoistoryConfig.OnlyPersonalOrganizationalRepositoriesWith',
                      )
                }
                {...fieldLayout}
              >
                <Select
                  name="RepoId"
                  className="item-operation"
                  value={repoId}
                  showSearch
                  dataSource={repoDataSource}
                  state={repoLoading ? 'loading' : undefined}
                  disabled={repoLoading}
                  itemRender={(item) => repoMenuItemRender(item)}
                  onChange={(val: string, evt, item: any) => {
                    setRepoId(val);
                    setRepoFullName(item.FullName);
                    setBranchName('');
                    getBranchList(provider, accountId, organizationId, val, item.FullName);
                  }}
                />

                <Button disabled={!provider && !accountId} onClick={() => getRepoList()}>
                  <Icon type="refresh" />
                </Button>
              </Form.Item>
              <Form.Item
                label={intl('saenext.shared.DeploySelectorField.RepoistoryConfig.WarehouseBranch')}
                required
                requiredMessage={intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.SelectABranch',
                )}
                {...fieldLayout}
              >
                <Select
                  name="BranchName"
                  className="item-operation"
                  showSearch
                  value={branchName}
                  onChange={(val, evt, item: any) => {
                    // setBranchId(item.CommitId);
                    setBranchName(item.Name);
                  }}
                  state={branchLoading ? 'loading' : undefined}
                  disabled={branchLoading}
                  dataSource={branchDataSource}
                />

                <Button
                  disabled={!provider && !accountId && !repoId}
                  onClick={() => getBranchList()}
                >
                  <Icon type="refresh" />
                </Button>
              </Form.Item>
            </>
          }
        />

        <Step.Item
          title={intl('saenext.shared.DeploySelectorField.RepoistoryConfig.BuildConfiguration')}
          status="process"
          content={
            <>
              <Form.Item
                required
                label={intl('saenext.shared.DeploySelectorField.RepoistoryConfig.BuildType')}
                {...fieldLayout}
              >
                <RadioGroup
                  className="flex-radio-group"
                  name="BuildType"
                  direction="ver"
                  value={buildType}
                  onChange={(value: BUILD_TYPE) => setBuildType(value)}
                >
                  <Radio value={BUILD_TYPE.BUILDPACK}>
                    {intl(
                      'saenext.shared.DeploySelectorField.RepoistoryConfig.TheSystemAutomaticallyDetectsAnd',
                    )}
                  </Radio>
                  <Radio value={BUILD_TYPE.STATIC}>
                    {intl(
                      'saenext.shared.DeploySelectorField.RepoistoryConfig.PureStaticWebsitesHtmlJs',
                    )}
                  </Radio>
                  <Radio value={BUILD_TYPE.DOCKERFILE}>
                    {intl(
                      'saenext.shared.DeploySelectorField.RepoistoryConfig.BuildBasedOnDockerfile',
                    )}
                  </Radio>
                  {buildType === BUILD_TYPE.BUILDPACK && (
                    <ExternalLink
                      label={intl(
                        'saenext.shared.DeploySelectorField.RepoistoryConfig.ViewSupportedProgrammingLanguagesAnd',
                      )}
                      url={CachedData.confLink('help:sae:foundation-development')}
                    />
                  )}
                </RadioGroup>
              </Form.Item>
              <Form.Item
                required
                label={intl('saenext.shared.DeploySelectorField.RepoistoryConfig.BuildTriggerMode')}
                {...fieldLayout}
              >
                <RadioGroup
                  name="TriggerType"
                  className="trigger-radio-group"
                  value={triggerType}
                  onChange={(value: TRIGGER_TYPE) => {
                    setTriggerType(value);
                    setTagName(undefined);
                  }}
                >
                  {map(C_TRIGGER, (item, index) => (
                    <Radio value={index} key={index} className="full-width">
                      <span>{item.label}</span>
                      <p className="color-text-description">{item.help}</p>
                    </Radio>
                  ))}
                </RadioGroup>
              </Form.Item>
              <If condition={triggerType === TRIGGER_TYPE.TAG}>
                <TextField
                  required
                  name="TagName"
                  className="tag-input"
                  placeholder={intl(
                    'saenext.shared.DeploySelectorField.RepoistoryConfig.SupportsRegularExpressionsSuchAs',
                  )}
                  value={tagName}
                  onChange={({ value }) => {
                    setTagName(value);
                  }}
                  validation={[
                    {
                      type: 'required',
                      message: intl(
                        'saenext.shared.DeploySelectorField.RepoistoryConfig.EnterTagReleaseEvents',
                      ),
                    },
                  ]}
                />
              </If>

              <Form.Item
                required
                label={intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.NewVersionTrafficPolicy',
                )}
                {...fieldLayout}
              >
                <RadioGroup
                  name="UpdateTraffic"
                  className="flex-radio-group"
                  direction="ver"
                  value={updateTraffic}
                  onChange={(value) => {
                    // @ts-ignore
                    setUpdateTraffic(value);
                  }}
                >
                  <Radio value={false} key="false">
                    <span>
                      {intl(
                        'saenext.shared.DeploySelectorField.RepoistoryConfig.AfterTheVersionIsDeployed',
                      )}
                    </span>
                  </Radio>
                  <Radio value={true} key="true">
                    <span>
                      {intl(
                        'saenext.shared.DeploySelectorField.RepoistoryConfig.AfterTheVersionIsDeployed.1',
                      )}
                    </span>
                  </Radio>
                </RadioGroup>
              </Form.Item>

              <Form.Item
                label={intl(
                  'saenext.shared.DeploySelectorField.RepoistoryConfig.TargetImageRepository',
                )}
                help={personalRepoText.default.help}
                {...fieldLayout}
              >
                <Checkbox
                  checked={isPersonalRepoEnabled}
                  name="IsPersonalRepoEnabled"
                  onChange={(e) => {
                    setIsPersonalRepoEnabled(e);
                  }}
                >
                  {intl(
                    'saenext.shared.DeploySelectorField.RepoistoryConfig.CustomTargetImageRepository',
                  )}
                </Checkbox>
              </Form.Item>

              {isPersonalRepoEnabled && (
                <Form.Item
                  label=" "
                  required
                  requiredMessage={intl(
                    'saenext.shared.DeploySelectorField.RepoistoryConfig.SelectANamespace',
                  )}
                  asterisk={false}
                  {...fieldLayout}
                >
                  <Select
                    name="Namespace"
                    showSearch
                    style={{ width: 'calc(50% - 38px)' }}
                    value={namespace}
                    label={intl('saenext.shared.DeploySelectorField.RepoistoryConfig.Namespace')}
                    placeholder={intl(
                      'saenext.shared.DeploySelectorField.RepoistoryConfig.SelectANamespace',
                    )}
                    onChange={handleNamespace}
                    state={namespaceLoading ? 'loading' : undefined}
                    disabled={namespaceLoading && needOpenCr}
                    dataSource={namespaceList}
                  />

                  <Select
                    name="Repository"
                    className="w-50"
                    showSearch
                    value={repository}
                    label={intl(
                      'saenext.shared.DeploySelectorField.RepoistoryConfig.WarehouseName.1',
                    )}
                    placeholder={intl(
                      'saenext.shared.DeploySelectorField.RepoistoryConfig.SelectAnImageRepository',
                    )}
                    onChange={(val: string) => {
                      setRepository(val);
                    }}
                    state={imageRepoLoading ? 'loading' : undefined}
                    disabled={imageRepoLoading && needOpenCr}
                    dataSource={imageRepoList}
                  />

                  <Button
                    disabled={!provider && !accountId && !repoId}
                    onClick={() => {
                      getNamespaceList();
                      setNamespace('');
                      setRepository('');
                    }}
                  >
                    <Icon type="refresh" />
                  </Button>
                </Form.Item>
              )}

              {needOpenCr ? (
                <Form.Item label=" " {...fieldLayout}>
                  <div>
                    {intl(
                      'saenext.shared.DeploySelectorField.RepoistoryConfig.CurrentlyYourAccountHasNot',
                    )}

                    <a href={CachedData.confLink('feature:cr:url')} target="_blank" className="ml-s">
                      {intl('saenext.shared.DeploySelectorField.RepoistoryConfig.ActivateAcr')}
                    </a>
                  </div>
                </Form.Item>
              ) : null}

              {renderBuildConfig}
            </>
          }
        ></Step.Item>
      </Step>
    </Form>
  );
});

RepoistoryConfig.propTypes = {};

export default RepoistoryConfig;
