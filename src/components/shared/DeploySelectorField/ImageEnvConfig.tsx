import React, { useEffect, useState } from 'react';
import { Field, Form, Grid, Radio, Select, intl } from '@ali/cnd';
import If from '~/components/shared/If';
import C from '~/constants/common';
import { IMAGE_LANG_ICONS, LANGUAGE_TYPE } from './constant';
import * as services from '~/services';
import { map } from 'lodash';
import { IMAGE_PHP_ENV } from '~/components/constants';

const ImageEnvConfig = (props) => {
  const { value = {}, onChange, actionType } = props;

  const [javaList, setJavaList] = useState([]);
  const [phpList, setPhpList] = useState(IMAGE_PHP_ENV);

  const field = Field.useField();

  const { getValues, setValues, remove } = field;

  const { ProgrammingLanguage = LANGUAGE_TYPE.JAVA } = getValues() as any;

  useEffect(() => {
    setValues(value);
  }, [value]);

  useEffect(() => {
    if (ProgrammingLanguage === LANGUAGE_TYPE.JAVA) {
      fetchJavaComponents();
    } else if (ProgrammingLanguage === LANGUAGE_TYPE.PHP) {
      // fetchPhpComponents();
    }
  }, [ProgrammingLanguage]);

  const fetchComponents = async (Type) => {
    const { Data = [] } =
      (await services.DescribeComponents({
        params: {
          Type,
        },
      })) || {};
    return Data;
  };

  const fetchJavaComponents = async () => {
    const Data = await fetchComponents('JDK');
    const javaList = map(Data, (item) => ({
      label: item.ComponentDescription,
      value: item.ComponentKey,
    }));
    setJavaList(javaList);
  };

  const fetchPhpComponents = async () => {
    const Data = await fetchComponents('PHP');
    const phpList = map(Data, (item) => {
      const version = item.ComponentKey.split(' ')[1];
      const label = `PHP-${version}`;
      const versionStr = version.replace('.', '_');
      const value = `IMAGE_PHP_${versionStr}`
      return {
        label,
        value,
      }
    });
    setPhpList(phpList);
  };

  const onFormChange = () => {
    setTimeout(() => {
      const values = getValues();
      onChange(values);
    }, 500);
  };

  const onChangeLanguageTypeType = (lang) => {
    if (lang !== LANGUAGE_TYPE.JAVA) {
      remove('Jdk');
    } else if (lang !== LANGUAGE_TYPE.PHP) {
      remove('PackageType');
    }
  };

  return (
    <Form field={field} onChange={onFormChange}>
      <Form.Item
        label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.TechnologyStackLanguage')}
        {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        wrapperCol={{ span: 24 }}
      >
        <Radio.Group
          disabled={actionType === 'deploy'}
          defaultValue={LANGUAGE_TYPE.JAVA}
          onChange={onChangeLanguageTypeType}
          name="ProgrammingLanguage"
          className="full-width package-box-group"
          dataSource={IMAGE_LANG_ICONS}
        >
          {/* <Grid.Row>
            <Grid.Col span="4">
              <Radio value={LANGUAGE_TYPE.JAVA} label="Java" />
            </Grid.Col>
            <Grid.Col span="4">
              <Radio value={LANGUAGE_TYPE.PHP} label="PHP" />
            </Grid.Col>
            <Grid.Col span="4">
              <Radio value={LANGUAGE_TYPE.PYTHON} label="Python" />
            </Grid.Col>
            <Grid.Col span="4">
              <Radio value={LANGUAGE_TYPE.GO} label="Go" />
            </Grid.Col>
            <Grid.Col span="4">
              <Radio value={LANGUAGE_TYPE.DOTNET} label=".NET" />
            </Grid.Col>
            <Grid.Col span="12">
              <Radio
                value={LANGUAGE_TYPE.OTHER}
                label={intl(
                  'saenext.shared.DeploySelectorField.ImageEnvConfig.OtherLanguagesSuchAsC',
                )}
              />
            </Grid.Col>
          </Grid.Row> */}
        </Radio.Group>
      </Form.Item>

      <If condition={ProgrammingLanguage === LANGUAGE_TYPE.JAVA}>
        <Form.Item
          label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.JavaEnvironment')}
          help={intl('saenext.shared.DeploySelectorField.ImageEnvConfig.WeRecommendThatYouSelect')}
          {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        >
          <Select
            defaultValue={javaList[0]?.value || 'Open JDK 8'}
            dataSource={javaList}
            showSearch
            name="Jdk"
            className="full-width"
          />
        </Form.Item>
      </If>
      <If condition={ProgrammingLanguage === LANGUAGE_TYPE.PHP}>
        <Form.Item
          label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.PhpEnvironment')}
          {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        >
          <Select
            defaultValue={'IMAGE_PHP_7_3'}
            dataSource={phpList}
            showSearch
            name="PackageType"
            className="full-width"
          />
        </Form.Item>
      </If>
    </Form>
  );
};

export default ImageEnvConfig;
