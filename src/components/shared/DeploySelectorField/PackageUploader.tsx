import { intl } from '@ali/cnd';
import React, { useMemo } from 'react';
import { Icon, Message, Upload } from '@ali/cnd';
import _ from 'lodash';
import * as services from '~/services';

const PackageUploader = (props) => {
  const {
    appId,
    tools,
    fileSizeLimit,
    accept,
    value: defaultPackageUrl = '',
    onChange,
    className = '',
    assignSignatur,
    setFileName,
    fileNameReg,
    fileNameMessage,
  } = props;

  const value = useMemo(() => {
    if (!defaultPackageUrl) {
      return [];
    } else {
      const name = _.replace(defaultPackageUrl, /.*\//, '');
      return [
        {
          name,
        },
      ];
    }
  }, [defaultPackageUrl]);

  const beforeUpload = async (file, uploadOptions) => {
    if (fileSizeLimit && file.size > fileSizeLimit) {
      return Promise.reject();
    }
    if (fileNameReg && !fileNameReg.test(file.name)) {
      return Promise.reject();
    }
    let res = {};
    if (assignSignatur === 'v2') {
      res =
        (await services.AssignUploadSignatureV2({
          params: {
            PackageVersion: new Date().getTime(),
            FileName: file.name,
            ApplicationId: '',
          },
        })) || {};
    } else {
      res =
        (await services.AssignUploadSignature({
          params: {
            PackageVersion: new Date().getTime(),
            FileName: file.name,
            AppId: appId,
            Tools: tools,
            PackageDescription: '',
          },
        })) || {};
    }
    // @ts-ignore
    const { Data } = res;
    const { BucketName, Url, Key, Policy, Signature, OSSAccessKeyId } = Data;
    file.tempUrl = tools ? `oss://${BucketName}/${Key}` : `${Url}/${Key}`;

    uploadOptions.data = {
      BucketName,
      Name: file.name,
      Key,
      Policy,
      OSSAccessKeyId,
      Signature,
      Success_action_status: 200,
    };

    // uploadOptions.headers = { 'X-Requested-With': null }; // 需要跨域上传的话加这一段
    uploadOptions.action = Url;
    setFileName && setFileName(file.name);
    return uploadOptions;
  };

  const resFormatter = (response, file) => ({
    success: true,
    url: file.tempUrl,
  });

  const onFileChange = (files) => {
    if (!files || files.length === 0) {
      onChange('');
    }
  };

  const onSuccess = (file) => {
    onChange(file.url);
  };

  const onError = (file) => {
    if (fileSizeLimit && file.size > fileSizeLimit) {
      Message.error(
        intl('saenext.shared.DeploySelectorField.PackageUploader.TheFileSizeExceedsThe'),
      );
      return;
    }
    if (fileNameReg && !fileNameReg.test(file.name)) {
      Message.error(fileNameMessage);
      return;
    }

    Message.show({
      type: 'error',
      content: intl('saenext.shared.DeploySelectorField.PackageUploader.UploadFailed'),
      duration: 5000,
    });
  };

  return (
    <Upload.Dragger
      className={className}
      accept={accept}
      limit={1}
      listType="text"
      withCredentials={false}
      value={value}
      onChange={onFileChange}
      beforeUpload={beforeUpload}
      formatter={resFormatter}
      onSuccess={onSuccess}
      onError={onError}
    >
      <div className={!_.isEmpty(value) ? 'next-upload next-disabled' : ''}>
        <div className="next-upload-inner">
          <div className="next-upload-drag">
            <p className="next-upload-drag-icon">
              <Icon type="upload" size="large" />
            </p>
            <p className="next-upload-drag-text">
              {intl('saenext.shared.DeploySelectorField.PackageUploader.ClickOrDragTheFile')}
            </p>
            <p className="next-upload-drag-hint"></p>
          </div>
        </div>
      </div>
    </Upload.Dragger>
  );
};

export default PackageUploader;
