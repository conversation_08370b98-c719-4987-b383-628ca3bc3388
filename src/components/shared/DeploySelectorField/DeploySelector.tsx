import { intl } from '@ali/cnd';
import React, { useContext, useState, useRef, useEffect } from 'react';
import { Input, Button, SlidePanel, Tab, ConsoleContext, Message, Radio } from '@ali/cnd';
import { set } from 'lodash';
import parseUrl, { Type } from '../ImageSelectorField/utils';
import Cr from '../ImageSelectorField/cr';
import Demo from '../ImageSelectorField/demo';
import Custom from '../ImageSelectorField/custom';
import OtherAccount from '../ImageSelectorField/OtherAccount';

import { GetRepo } from '~/services/acr';
import {
  BUILD_TYPE,
  DEFAULT_WEBPACKAGE_VALUE,
  DEFAULT_MICROPACKAGE_VALUE,
  DEFAULT_REPOISTORY_VALUE,
  LANGUAGE_TYPE,
} from './constant';
import { DEPLOY_TYPE } from '~/constants/application';
import { getRepoFullUrl } from '~/utils/global';
import { AES_CONSTANT, trackResource } from '~/tracker';
import RepoistoryConfig from './RepoistoryConfig';
import WebPackageConfig from './web-app/PackageConfig';
import MicroPackageConfig from './micro-app/PackageConfig';
import If from '~/components/shared/If';
import ImageEnvConfig from './ImageEnvConfig';
import PrivateRegistry from '../ImageSelectorField/PrivateRegistry';
import FeatureContext from '~/utils/featureContext';

const RadioGroup = Radio.Group;

interface Props {
  onChange: (value) => void;
  value: { [key: string]: any };
  size: 'small' | 'medium' | 'large';
  allowTypes: Array<string>;
  appType?: 'web' | 'micro';
  vpcId?: string;
  namespaceId?: string;
  actionType?: 'create' | 'deploy';
  isPreview?: boolean;
  preIsTypeImage?:boolean;
  isSupportUpdateDeployType?: boolean;
}

export default ({
  appType,
  vpcId,
  namespaceId,
  allowTypes = ['image', 'source'],
  size,
  value,
  onChange,
  actionType,
  isPreview = false,
  preIsTypeImage = false,
  isSupportUpdateDeployType = false,
}: Props) => {
  const ref = useRef(null);
  const [selectType, setSelectType] = useState(DEPLOY_TYPE.IMAGE);
  const [parseConfig, setParseConfig] = useState({ type: Type.cr, parseResult: {} });
  const [imageShow, setImageShow] = useState(false);
  const [repoistoryShow, setRepoistoryShow] = useState(false);
  const [microPackageShow, setMicroPackageShow] = useState(false);
  const [webPackageShow, setWebPackageShow] = useState(false);
  const [imageValue, setImageValue] = useState<any>({ ...value, type: DEPLOY_TYPE.IMAGE });
  const [repoistoryValue, setRepoistoryValue] = useState<any>(DEFAULT_REPOISTORY_VALUE);
  const [webPackageValue, setWebPackageValue] = useState<any>(DEFAULT_WEBPACKAGE_VALUE);
  const [micorPackageValue, setMicroPackageValue] = useState<any>(DEFAULT_MICROPACKAGE_VALUE);
  const [imageEnvValue, setImageEnvValue] = useState<any>({});

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const existApps = parseInt(window.localStorage.getItem('EXIST_APPS')) || 0;

  const { feature } = useContext(FeatureContext);
  const { customRegistry } = feature;

  useEffect(() => {
    switch (value?.type) {
      case DEPLOY_TYPE.IMAGE:
        setImageValue({ ...value });
        setSelectType(DEPLOY_TYPE.IMAGE);
        return;
      case DEPLOY_TYPE.REPOISTORY:
        setRepoistoryValue({ ...value });
        setSelectType(DEPLOY_TYPE.REPOISTORY);
        return;
      case DEPLOY_TYPE.WEB_PACKAGE:
        setWebPackageValue({ ...value });
        setSelectType(DEPLOY_TYPE.WEB_PACKAGE);
        return;
      case DEPLOY_TYPE.MICRO_PACKAGE:
        setMicroPackageValue({ ...value });
        setSelectType(DEPLOY_TYPE.MICRO_PACKAGE);
        return;
    }
  }, [value]);

  useEffect(() => {
    if (appType === 'micro') {
      value.ProgrammingLanguage
        ? setImageEnvValue({
            ProgrammingLanguage: value.ProgrammingLanguage,
            Jdk: value.Jdk,
            PackageType: value.PackageType,
          })
        : setImageEnvValue({
            ProgrammingLanguage: LANGUAGE_TYPE.JAVA,
            Jdk: 'Open JDK 8',
          });
    } else {
      setImageEnvValue({});
    }
  }, [appType, value]);

  const showImageSlide = async () => {
    const {
      image,
      instanceId,
      acrAssumeRoleArn,
      imagePullSecrets,
      customImageNetworkType,
      enableImageAccl,
      registryConfig,
    } = imageValue || {};
    if (image) {
      const result = parseUrl(regionId, image);
      if(customImageNetworkType){
        result.type = Type.custom;
        result.parseResult = {
          image,
          // @ts-ignore
          customImageNetworkType,
          imagePullSecrets,
        };
      }else if (acrAssumeRoleArn || imagePullSecrets) {
        result.type = Type.otherAccount;
        result.parseResult = {
          image,
          // @ts-ignore
          instanceId,
          acrAssumeRoleArn,
          imagePullSecrets,
        };
      }else if (result.type === Type.cr) {
        if (image.indexOf(`${regionId}.cr.aliyuncs.com`) > -1) {
          // 企业镜像
          // @ts-ignore
          result.parseResult.type = 'cr';
        } else {
          // 个人版镜像
          // @ts-ignore
          result.parseResult.type = 'acr';
        }
        if (instanceId) {
          set(result, 'parseResult.instanceId', instanceId);
          set(result, 'parseResult.enableImageAccl', enableImageAccl);
        }
        // @ts-ignore: 个人版镜像验证是否存在
        const { repoNamespace, repoName } = result.parseResult;
        if (repoNamespace) {
          try {
            await GetRepo({ RepoNamespace: repoNamespace, RepoName: repoName });
          } catch (_e) {
            // 获取不到信息强制转换到自定义
            result.type = Type.custom;
            result.parseResult = { image };
          }
        }
      } else if (registryConfig || (result.type !== Type.demo && !image.includes('aliyuncs.com'))) {
        result.type = Type.private;
        result.parseResult = {
          image,
          // @ts-ignore
          registryConfig,
        };
      }
      setParseConfig(result);
    }
    trackResource({
      behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE_FORM,
      stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
      existApps,
      deployType: DEPLOY_TYPE.IMAGE,
    });
    setImageShow(true);
  };

  const onTypeChange = (selectType) => {
    let newValue = {};
    switch (selectType) {
      case DEPLOY_TYPE.IMAGE:
        newValue = imageValue;
        break;
      case DEPLOY_TYPE.REPOISTORY:
        newValue = repoistoryValue;
        break;
      case DEPLOY_TYPE.WEB_PACKAGE:
        newValue = webPackageValue;
        break;
      case DEPLOY_TYPE.MICRO_PACKAGE:
        newValue = micorPackageValue;
        break;
    }
    if(actionType === 'deploy' && isSupportUpdateDeployType){
      newValue = { ...newValue, ProgrammingLanguage: value?.ProgrammingLanguage };
    }
    onChange(newValue);
  };

  const imageOk = (value) => {
    setImageValue({ ...imageValue, ...value, ...imageEnvValue });
    onChange({ ...value, type: selectType, accelerationType: 'Default', ...imageEnvValue });
    trackResource({
      behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE_FORM,
      stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
      existApps,
      deployType: DEPLOY_TYPE.IMAGE,
    });
    setImageShow(false);
  };

  const repoistoryOk = (value) => {
    setRepoistoryValue({ ...repoistoryValue, ...value });
    // 选择持续部署时，若选择静态网站构建方式则设置端口，否则将端口号置空
    const extraConfig =
      value.BuildConfig.BuildType === BUILD_TYPE.STATIC ? { port: 8080 } : { port: null };
    onChange({ ...value, ...extraConfig, type: selectType });
    trackResource({
      behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE_FORM,
      stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
      existApps,
      deployType: DEPLOY_TYPE.REPOISTORY,
    });
    setRepoistoryShow(false);
  };

  const microPackageOk = (value) => {
    setMicroPackageValue({ ...micorPackageValue, ...value });
    onChange({ ...value, type: selectType });
    trackResource({
      behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE_FORM,
      stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
      existApps,
      deployType: DEPLOY_TYPE.MICRO_PACKAGE,
    });
    setMicroPackageShow(false);
  };

  const webPackageOk = (value) => {
    setWebPackageValue({ ...webPackageValue, ...value });
    onChange({ ...value, type: selectType });
    setWebPackageShow(false);
  };

  const onOk = async () => {
    const result = await ref.current.submit();
    if (result?.error) {
      Message.error(result?.error);
      return;
    }
    if (!selectType) {
      Message.error('no type');
      return;
    }

    const handleOk = {
      [DEPLOY_TYPE.IMAGE]: imageOk,
      [DEPLOY_TYPE.REPOISTORY]: repoistoryOk,
      [DEPLOY_TYPE.MICRO_PACKAGE]: microPackageOk,
      [DEPLOY_TYPE.WEB_PACKAGE]: webPackageOk,
    };
    handleOk[selectType](result);
  };

  const deployTypes = [
    {
      label: intl('saenext.shared.DeploySelectorField.DeploySelector.SelectImageDeployment'),
      key: DEPLOY_TYPE.IMAGE,
      placeholder: intl('saenext.shared.DeploySelectorField.DeploySelector.SelectAnImage'),
      selectText: intl('saenext.shared.DeploySelectorField.DeploySelector.SetImage'),
      showTrigger: showImageSlide,
    },
    {
      label: intl(
        'saenext.shared.DeploySelectorField.DeploySelector.ContinuousDeploymentFromSourceCode',
      ),
      key: DEPLOY_TYPE.REPOISTORY,
      placeholder: intl(
        'saenext.shared.DeploySelectorField.DeploySelector.ConfigureContinuousSourceCodeDeployment',
      ),
      selectText: intl('saenext.shared.DeploySelectorField.DeploySelector.SetContinuousDeployment'),
      showTrigger: () => setRepoistoryShow(true),
    },
    {
      label: intl('saenext.shared.DeploySelectorField.DeploySelector.CodePackageDeployment'),
      key: DEPLOY_TYPE.MICRO_PACKAGE,
      placeholder: intl(
        'saenext.shared.DeploySelectorField.DeploySelector.ConfigureCodePackageDeployment',
      ),
      selectText: intl(
        'saenext.shared.DeploySelectorField.DeploySelector.ConfigureCodePackageDeployment.4',
      ),
      showTrigger: () => setMicroPackageShow(true),
    },
    {
      label: intl('saenext.shared.DeploySelectorField.DeploySelector.CodePackageDeployment'),
      key: DEPLOY_TYPE.WEB_PACKAGE,
      placeholder: intl(
        'saenext.shared.DeploySelectorField.DeploySelector.ConfigureCodePackageDeployment',
      ),
      selectText: intl(
        'saenext.shared.DeploySelectorField.DeploySelector.ConfigureCodePackageDeployment.4',
      ),
      showTrigger: () => setWebPackageShow(true),
    },
  ].filter((item) => allowTypes.includes(item.key));

  return (
    <>
      <RadioGroup
        value={selectType}
        direction="ver"
        style={{ width: '100%' }}
        onChange={(v: any) => {
          setSelectType(v);
          onTypeChange(v);
        }}
      >
        {deployTypes.map((item) => (
          <Radio id={item.key} value={item.key} style={{ marginRight: 0 }}>
            <span className="inline-block" style={{ width: 118 }}>
              {item.label}
            </span>
            <Input
              disabled
              style={{ width: 'calc(100% - 146px)' }}
              size={size}
              placeholder={item.placeholder}
              value={
                item.key === DEPLOY_TYPE.IMAGE
                  ? imageValue.image
                  : item.key === DEPLOY_TYPE.REPOISTORY
                  ? getRepoFullUrl(
                      repoistoryValue.CodeConfig['Provider'],
                      repoistoryValue.CodeConfig['RepoFullName'],
                    )
                  : item.key === DEPLOY_TYPE.MICRO_PACKAGE
                  ? micorPackageValue.PackageUrl
                  : webPackageValue.PackageUrl
              }
              innerAfter={
                !isPreview && (
                  <Button
                    type="primary"
                    text
                    className="mr-s"
                    onClick={() => {
                      setSelectType(item.key);
                      onTypeChange(item.key);
                      item.showTrigger();
                    }}
                  >
                    {item.selectText}
                  </Button>
                )
              }
            />
          </Radio>
        ))}
      </RadioGroup>
      <SlidePanel
        title={intl('saenext.shared.DeploySelectorField.DeploySelector.SetContinuousDeployment')}
        isShowing={repoistoryShow}
        width="large"
        onOk={onOk}
        onClose={() => setRepoistoryShow(false)}
        onCancel={() => setRepoistoryShow(false)}
      >
        <RepoistoryConfig ref={ref} initValue={repoistoryValue} onChange={onChange} />
      </SlidePanel>

      <SlidePanel
        title={intl(
          'saenext.shared.DeploySelectorField.DeploySelector.ConfigureCodePackageDeployment.4',
        )}
        isShowing={microPackageShow}
        width="large"
        onOk={onOk}
        onClose={() => setMicroPackageShow(false)}
        onCancel={() => setMicroPackageShow(false)}
      >
        <MicroPackageConfig
          ref={ref}
          initValue={micorPackageValue}
          actionType={actionType}
          preIsTypeImage={preIsTypeImage}
        />
      </SlidePanel>

      <SlidePanel
        title={intl(
          'saenext.shared.DeploySelectorField.DeploySelector.ConfigureCodePackageDeployment.4',
        )}
        isShowing={webPackageShow}
        width="large"
        onOk={onOk}
        onClose={() => setWebPackageShow(false)}
        onCancel={() => setWebPackageShow(false)}
      >
        <WebPackageConfig ref={ref} initValue={webPackageValue} />
      </SlidePanel>

      <SlidePanel
        title={intl('saenext.shared.DeploySelectorField.DeploySelector.SetImage')}
        isShowing={imageShow}
        width="large"
        onOk={onOk}
        onClose={() => setImageShow(false)}
        onCancel={() => setImageShow(false)}
      >
        <If condition={appType === 'micro'}>
          <ImageEnvConfig
            value={imageEnvValue}
            onChange={setImageEnvValue}
            actionType={actionType}
          />
        </If>
        <Tab
          shape="wrapped"
          activeKey={parseConfig.type}
          onChange={(key: Type) => setParseConfig({ type: key, parseResult: {} })}
          unmountInactiveTabs
          contentClassName="mt"
        >
          <Tab.Item
            title={intl('saenext.shared.DeploySelectorField.DeploySelector.MyAlibabaCloudImage')}
            key={Type.cr}
          >
            <Cr ref={ref} initValue={parseConfig.parseResult} appType={appType} vpcId={vpcId} />
          </Tab.Item>
          <Tab.Item
            title={intl('saenext.shared.DeploySelectorField.DeploySelector.DemoImage')}
            key={Type.demo}
          >
            <Demo
              ref={ref}
              initValue={parseConfig.parseResult}
              appType={appType}
              microImageLanguage={imageEnvValue.ProgrammingLanguage}
            />
          </Tab.Item>
          <Tab.Item
            title={intl('saenext.shared.DeploySelectorField.DeploySelector.CustomImage')}
            key={Type.custom}
          >
            <Custom ref={ref} initValue={parseConfig.parseResult} appType={appType} namespaceId={namespaceId} />
          </Tab.Item>
          {appType === 'micro' && (
            <Tab.Item
              title={intl(
                'saenext.shared.DeploySelectorField.DeploySelector.PrivateImagesOfOtherAlibaba',
              )}
              key={Type.otherAccount}
            >
              <OtherAccount
                ref={ref}
                initValue={parseConfig.parseResult}
                namespaceId={namespaceId}
              />
            </Tab.Item>
          )}
          {appType === 'web' && customRegistry && (
            <Tab.Item
              title={intl(
                'saenext.shared.DeploySelectorField.DeploySelector.ThirdPartyPrivateImageRepository',
              )}
              key={Type.private}
            >
              <PrivateRegistry ref={ref} initValue={parseConfig.parseResult} />
            </Tab.Item>
          )}
        </Tab>
      </SlidePanel>
    </>
  );
};
