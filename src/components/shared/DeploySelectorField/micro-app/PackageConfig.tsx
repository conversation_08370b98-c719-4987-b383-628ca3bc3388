import React, {
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import {
  Balloon,
  Button,
  Checkbox,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  intl,
  Message,
  Radio,
  Select,
} from '@ali/cnd';
import If from '~/components/shared/If';
import { LANGUAGE_TYPE, PACKAGE_TYPE, UPLOAD_TYPE } from '../constant';
import FeatureContext from '~/utils/featureContext';
import MicroAppContext from '~/utils/microAppContext';
import PackageUploader from '../PackageUploader';
import * as services from '~/services';
import _ from 'lodash';
import CustomBuildShell from './CustomBuildShell';
import TomcatConfig from './TomcatConfig';
import PHPIniConfig from './PHPIniConfig';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import CommandArgsForm from '../../CommandArgsForm';
import ComShow from '~/components/shared/ComShow';
import CachedData from '~/cache/common';

interface IProps {
  initValue: any;
  actionType?: 'create' | 'deploy';
  preIsTypeImage?: boolean;
}

const fieldLayout = {
  labelTextAlign: 'left' as 'left',
  labelCol: { fixedSpan: 6 },
  wrapperCol: { span: 16 },
};
const languageTypes = [
  {
    value: LANGUAGE_TYPE.JAVA,
    label: (
      <>
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="6927"
          width="32"
          height="32"
        >
          <path
            d="M558.08 472.064c48.128 53.248-13.312 103.424-13.312 103.424s119.808-61.44 65.536-139.264c-51.2-71.68-91.136-107.52 122.88-232.448 0 1.024-335.872 86.016-175.104 268.288"
            fill="#FF0000"
            p-id="6928"
          ></path>
          <path
            d="M610.304 5.12s101.376 101.376-96.256 258.048C356.352 389.12 478.208 460.8 514.048 543.744 420.864 459.776 354.304 386.048 399.36 317.44 463.872 216.064 651.264 166.912 610.304 5.12"
            fill="#FF0000"
            p-id="6929"
          ></path>
          <path
            d="M720.896 757.76c183.296-95.232 98.304-188.416 39.936-175.104-15.36 3.072-21.504 5.12-21.504 5.12s5.12-8.192 16.384-11.264c117.76-40.96 207.872 120.832-37.888 186.368-1.024 0 2.048-3.072 3.072-5.12m-337.92 38.912s-37.888 21.504 26.624 29.696c76.8 8.192 117.76 8.192 202.752-8.192 0 0 23.552 15.36 53.248 26.624-191.488 80.896-433.152-5.12-282.624-48.128m-23.552-106.496s-43.008 31.744 23.552 37.888c82.944 8.192 149.504 10.24 261.12-13.312 0 0 16.384 16.384 40.96 24.576-231.424 68.608-490.496 5.12-325.632-49.152"
            fill="#6699FF"
            p-id="6930"
          ></path>
          <path
            d="M811.008 876.544s27.648 23.552-31.744 40.96c-111.616 34.816-460.8 45.056-558.08 2.048-34.816-15.36 31.744-35.84 51.2-40.96 21.504-5.12 34.816-3.072 34.816-3.072-38.912-28.672-251.904 52.224-107.52 75.776 390.144 62.464 712.704-28.672 611.328-74.752M400.384 578.56s-178.176 43.008-63.488 56.32c49.152 6.144 146.432 5.12 235.52-3.072 73.728-6.144 147.456-19.456 147.456-19.456s-26.624 11.264-45.056 24.576c-181.248 48.128-530.432 26.624-430.08-23.552 88.064-39.936 155.648-34.816 155.648-34.816"
            fill="#6699FF"
            p-id="6931"
          ></path>
          <path
            d="M418.816 1015.808c176.128 11.264 446.464-6.144 453.632-90.112 0 0-13.312 31.744-146.432 56.32-150.528 27.648-336.896 24.576-446.464 6.144 2.048 1.024 24.576 20.48 139.264 27.648"
            fill="#6699FF"
            p-id="6932"
          ></path>
        </svg>
        <div className="text-bold mt-s">Java</div>
      </>
    ),
  },
  {
    value: LANGUAGE_TYPE.PHP,
    label: (
      <>
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="9065"
          width="32"
          height="32"
        >
          <path
            d="M306.40355555 441.68533333h-35.38488888L251.33511111 542.72h28.78577778c22.18666667 0 38.68444445-4.096 49.152-12.17422222 10.35377778-7.96444445 17.52177778-21.73155555 21.27644444-41.07377778 3.52711111-17.97688889 2.16177778-30.60622222-3.98222222-37.31911111-6.25777778-6.94044445-19.79733333-10.46755555-40.16355556-10.46755556zM761.51466667 441.68533333h-35.38488889L706.44622222 542.72h28.78577778c22.18666667 0 38.68444445-4.096 49.152-12.17422222 10.35377778-7.96444445 17.52177778-21.73155555 21.27644445-41.07377778 3.52711111-17.97688889 2.16177778-30.60622222-3.98222223-37.31911111-6.25777778-6.94044445-19.79733333-10.46755555-40.16355555-10.46755556z"
            fill="#787db0"
            p-id="9066"
          ></path>
          <path
            d="M512 244.96355555C231.53777778 244.96355555 4.20977778 364.544 4.20977778 512s227.328 267.03644445 507.67644444 267.03644445S1019.79022222 659.456 1019.79022222 512 792.46222222 244.96355555 512 244.96355555zM383.31733333 564.224c-12.17422222 11.37777778-25.94133333 19.56977778-40.84622222 24.46222222-14.67733333 4.77866667-33.56444445 7.168-56.09244444 7.168h-45.39733334l-12.97066666 66.67377778c-0.45511111 2.61688889-2.73066667 4.43733333-5.46133334 4.43733333h-58.25422222c-1.59288889 0-3.18577778-0.68266667-4.20977778-2.048-1.024-1.25155555-1.47911111-2.95822222-1.13777778-4.55111111l51.99644445-267.60533333c0.45511111-2.61688889 2.73066667-4.43733333 5.46133333-4.43733334h112.07111112c35.27111111 0 61.44 9.55733333 77.93777777 28.44444445 16.61155555 19.00088889 21.73155555 45.51111111 15.24622223 78.848-2.61688889 13.53955555-7.168 26.16888889-13.42577778 37.43288889-6.37155555 11.37777778-14.79111111 21.84533333-24.91733334 31.17511111zM551.02577778 593.92c-1.024-1.25155555-1.47911111-2.95822222-1.13777778-4.55111111l22.98311111-118.44266667c2.16177778-11.264 1.59288889-19.34222222-1.47911111-22.75555555-1.93422222-2.048-7.73688889-5.57511111-25.03111111-5.57511112h-41.64266667l-28.89955555 148.93511112c-0.45511111 2.61688889-2.73066667 4.43733333-5.46133334 4.43733333h-57.79911111c-1.59288889 0-3.18577778-0.68266667-4.20977777-2.048-1.024-1.25155555-1.47911111-2.95822222-1.13777778-4.55111111l51.99644444-267.60533334c0.45511111-2.61688889 2.73066667-4.43733333 5.46133334-4.43733333h57.7991111c1.59288889 0 3.18577778 0.68266667 4.20977778 2.048 1.024 1.25155555 1.47911111 2.95822222 1.13777778 4.55111111l-12.51555556 64.62577778h44.82844445c34.13333333 0 57.344 6.03022222 70.88355555 18.432 13.76711111 12.62933333 18.09066667 32.88177778 12.74311112 60.07466667L619.52 591.64444445c-0.45511111 2.61688889-2.73066667 4.43733333-5.46133333 4.43733333h-58.70933334c-1.70666667-0.11377778-3.29955555-0.91022222-4.32355555-2.16177778z m325.632-98.304c-2.61688889 13.53955555-7.168 26.16888889-13.53955556 37.43288889-6.37155555 11.264-14.67733333 21.73155555-24.80355555 31.06133333-12.17422222 11.37777778-25.94133333 19.56977778-40.84622222 24.46222223-14.67733333 4.77866667-33.56444445 7.168-56.09244445 7.168h-45.39733333l-12.97066667 66.67377777c-0.45511111 2.61688889-2.73066667 4.43733333-5.46133333 4.43733333h-58.25422222c-1.59288889 0-3.18577778-0.68266667-4.20977778-2.048-1.024-1.25155555-1.47911111-2.95822222-1.13777778-4.5511111l51.99644444-267.60533334c0.45511111-2.61688889 2.73066667-4.43733333 5.46133334-4.43733333h112.07111111c35.27111111 0 61.44 9.55733333 77.93777777 28.44444444 16.61155555 19.11466667 21.73155555 45.62488889 15.24622223 78.96177778z"
            fill="#787db0"
            p-id="9067"
          ></path>
        </svg>
        <div className="text-bold mt-s">PHP</div>
      </>
    ),
  },
  {
    value: LANGUAGE_TYPE.PYTHON,
    label: (
      <>
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="10186"
          width="32"
          height="32"
        >
          <path
            d="M420.693333 85.333333C353.28 85.333333 298.666667 139.946667 298.666667 207.36v71.68h183.04c16.64 0 30.293333 24.32 30.293333 40.96H207.36C139.946667 320 85.333333 374.613333 85.333333 442.026667v161.322666c0 67.413333 54.613333 122.026667 122.026667 122.026667h50.346667v-114.346667c0-67.413333 54.186667-122.026667 121.6-122.026666h224c67.413333 0 122.026667-54.229333 122.026666-121.642667V207.36C725.333333 139.946667 670.72 85.333333 603.306667 85.333333z m-30.72 68.693334c17.066667 0 30.72 5.12 30.72 30.293333s-13.653333 38.016-30.72 38.016c-16.64 0-30.293333-12.8-30.293333-37.973333s13.653333-30.336 30.293333-30.336z"
            fill="#3C78AA"
            p-id="10187"
          ></path>
          <path
            d="M766.250667 298.666667v114.346666a121.6 121.6 0 0 1-121.6 121.984H420.693333A121.6 121.6 0 0 0 298.666667 656.597333v160a122.026667 122.026667 0 0 0 122.026666 122.026667h182.613334A122.026667 122.026667 0 0 0 725.333333 816.64v-71.68h-183.082666c-16.64 0-30.250667-24.32-30.250667-40.96h304.64A122.026667 122.026667 0 0 0 938.666667 581.973333v-161.28a122.026667 122.026667 0 0 0-122.026667-122.026666zM354.986667 491.221333l-0.170667 0.170667c0.512-0.085333 1.066667-0.042667 1.621333-0.170667z m279.04 310.442667c16.64 0 30.293333 12.8 30.293333 37.973333a30.293333 30.293333 0 0 1-30.293333 30.293334c-17.066667 0-30.72-5.12-30.72-30.293334s13.653333-37.973333 30.72-37.973333z"
            fill="#FDD835"
            p-id="10188"
          ></path>
        </svg>
        <div className="text-bold mt-s">Python</div>
      </>
    ),
  },
  {
    value: LANGUAGE_TYPE.DOTNET,
    label: (
      <>
        <img
          src="https://img.alicdn.com/imgextra/i2/O1CN01iAulPh1TuwaFETH1y_!!6000000002443-55-tps-254-254.svg"
          width="32"
          height="32"
        />

        <div className="text-bold mt-s">.NET Core</div>
      </>
    ),
  },
];

const JdkImageMap = {
  'Dragonwell 21': 'CentOS Linux release 7.9.2009',
  'Dragonwell 17': 'CentOS Linux release 7.9.2009',
  'Dragonwell 11': 'CentOS Linux release 7.9.2009',
  'Dragonwell 8': 'CentOS Linux release 7.9.2009',
  'Open JDK 21': 'Debian 11',
  'Open JDK 17': 'Debian 11',
  'Open JDK 11': 'Debian 11',
  'Open JDK 8': 'CentOS Linux release 7.6.1810',
  'Open JDK 7': 'CentOS Linux release 7.6.1810',
  'openjdk-8u191-jdk-alpine3.9': 'alpine 3.9.2',
  'openjdk-7u201-jdk-alpine3.9': 'alpine 3.9.2',
};

const PackageConfig = forwardRef((props: IProps, ref) => {
  const { initValue = {}, actionType, preIsTypeImage } = props;

  const [javaList, setJavaList] = useState([]);
  const [webContainerList, setWebContainerList] = useState([]);
  const [edasContainerList, setEdasContainerList] = useState([]);
  const [phpList, setPhpList] = useState([]);
  const [pythonList, setPythonList] = useState([]);
  const [dontnetList, setDontnetList] = useState([]);
  const [useCustom, setUseCustom] = useState(false);
  const [usePhpCustom, setUsePhpCustom] = useState(false);
  const [pkgUrlLoading, setPkgUrlLoading] = useState(false);

  const { feature } = useContext(FeatureContext);
  const { appConfig } = useContext(MicroAppContext);

  const field = Field.useField();
  const { getValues, setValue, setValues } = field;
  const {
    ProgrammingLanguage = LANGUAGE_TYPE.JAVA,
    PackageType = PACKAGE_TYPE.JAR,
    uploadType = UPLOAD_TYPE.UPLOAD,
    Jdk = 'Open JDK 8',
    Dotnet = '.NET 3.1',
    phpExtensionsStr = '',
    phpExtensPECLStr = '',
    PackageUrl = '',
  } = getValues() as any;

  useEffect(() => {
    parseIn(initValue);
  }, [initValue]);

  useEffect(() => {
    if (ProgrammingLanguage === LANGUAGE_TYPE.JAVA) {
      fetchJavaComponents();
      fetchEdasComponents();
      if (actionType === 'deploy' && preIsTypeImage) {
        setValue('PackageType', PACKAGE_TYPE.JAR);
      }
    } else if (ProgrammingLanguage === LANGUAGE_TYPE.PHP) {
      fetchPhpComponents();
      if (actionType === 'deploy' && preIsTypeImage) {
        setValue('PackageType', PACKAGE_TYPE.PHPZIP);
      }
    } else if (ProgrammingLanguage === LANGUAGE_TYPE.PYTHON) {
      fetchPythonComponents();
      if (actionType === 'deploy' && preIsTypeImage) {
        setValue('PackageType', PACKAGE_TYPE.PYTHONZIP);
      }
    } else if (ProgrammingLanguage === LANGUAGE_TYPE.DOTNET) {
      fetchDotnetComponents();
      if (actionType === 'deploy' && preIsTypeImage) {
        setValue('PackageType', PACKAGE_TYPE.DOTNETZIP);
      }
    }
  }, [ProgrammingLanguage]);

  useEffect(() => {
    if (PackageType === PACKAGE_TYPE.WAR) {
      fetchWebContainersComponents();
    }
  }, [PackageType]);

  useImperativeHandle(ref, () => ({
    submit,
  }));

  const parseIn = (values) => {
    const {
      PackageType = PACKAGE_TYPE.JAR,
      WebContainer,
      EdasContainerVersion,
      uploadType = UPLOAD_TYPE.UPLOAD,
      PhpExtensions = [],
      PhpPECLExtensions = [],
      PythonModules = [],
      PackageRuntimeCustomBuild = '',
      PhpConfig = '',
      PhpConfigLocation = '',
    } = values;

    const defaultContainer = PackageType === PACKAGE_TYPE.WAR ? 'apache-tomcat-8.5.58' : 'default';

    const formValue = {
      ...values,
      container: EdasContainerVersion || WebContainer || defaultContainer,
      uploadType,
    };

    if (PackageRuntimeCustomBuild) {
      setUseCustom(true);
    }
    if (!_.isEmpty(PhpExtensions) || !_.isEmpty(PhpPECLExtensions)) {
      setUseCustom(true);
      formValue.phpExtensionsStr = _.join(PhpExtensions, '\n');
      formValue.phpExtensPECLStr = _.join(PhpPECLExtensions, '\n');
    }
    if (!_.isEmpty(PythonModules)) {
      setUseCustom(true);
      formValue.pythonModulesStr = _.join(PythonModules, '\n');
    }

    if (PhpConfig && PhpConfigLocation) {
      setUsePhpCustom(true);
    }

    setValues(formValue);
  };

  const pasrseOut = (values) => {
    const {
      container,
      phpExtensionsStr,
      phpExtensPECLStr,
      pythonModulesStr,
      PackageRuntimeCustomBuild: customShell = '',
      ...rest
    } = values;

    const PackageRuntimeCustomBuild =
      customShell.trim() === '#!/bin/bash' ? '' : customShell.trim();

    const params = {
      PackageRuntimeCustomBuild,
      ...rest,
    };
    // delete params.uploadType;

    if (ProgrammingLanguage === LANGUAGE_TYPE.JAVA) {
      if (
        webContainerList.some((item) => item.value === container) ||
        container.startsWith('apache-tomcat')
      ) {
        params.WebContainer = container;
        delete params.EdasContainerVersion;
      } else if (container === 'default') {
        // Jar包，选'标准Java应用运行环境'时, WebContainer 参数不传
        delete params.EdasContainerVersion;
        delete params.WebContainer;
      } else {
        params.EdasContainerVersion = container;
        delete params.WebContainer;
      }
    }

    if (ProgrammingLanguage === LANGUAGE_TYPE.PHP) {
      params.PhpExtensions = phpExtensionsStr ? _.split(phpExtensionsStr, /(?=\r|\n)\r?\n?/g) : [];
      params.PhpPECLExtensions = phpExtensPECLStr
        ? _.split(phpExtensPECLStr, /(?=\r|\n)\r?\n?/g)
        : [];
    }
    if (ProgrammingLanguage === LANGUAGE_TYPE.PYTHON) {
      params.PythonModules = pythonModulesStr
        ? _.filter(_.split(pythonModulesStr, /(?=\r|\n)\r?\n?/g), (item) => !!item)
        : [];
    }

    return params;
  };

  const submit = () => {
    return new Promise((resolve, reject) => {
      field.validate((errors, values) => {
        if (errors) {
          resolve({
            error: intl('saenext.DeploySelectorField.micro-app.PackageConfig.PleaseReCheckTheForm'),
          });
        } else {
          resolve(pasrseOut(values));
        }
      });
    });
  };

  const fetchComponents = async (Type) => {
    const { Data = [] } =
      (await services.DescribeComponents({
        params: {
          Type,
        },
      })) || {};
    return Data;
  };

  const fetchJavaComponents = async () => {
    const Data = await fetchComponents('JDK');
    const javaList = _.map(Data, (item) => ({
      label: item.ComponentDescription,
      value: item.ComponentKey,
    }));
    setJavaList(javaList);
  };

  const fetchWebContainersComponents = async () => {
    const Data = await fetchComponents('TOMCAT');
    const webContainerList = _.map(Data, (item) => ({
      label: item.ComponentDescription,
      value: item.ComponentKey,
    }));
    setWebContainerList(webContainerList);
  };

  const fetchEdasComponents = async () => {
    const { Data = [] } = (await services.DescribeEdasContainers({})) || {};
    const enableedasContainerList = _.filter(Data, { Disabled: false });
    const edasContainerList = _.map(enableedasContainerList, (item) => ({
      value: item.EdasContainerVersion,
      label: intl(
        'saenext.DeploySelectorField.micro-app.PackageConfig.EdasContainerItemedascontainerversionFatjarDeployment',
        { itemEdasContainerVersion: item.EdasContainerVersion },
      ),
    }));
    edasContainerList.sort((a, b) => b.value - a.value);
    setEdasContainerList(edasContainerList);
  };

  const fetchPhpComponents = async () => {
    const Data = await fetchComponents('PHP');
    const phpList = _.map(Data, (item) => ({
      label: item.ComponentDescription,
      value: item.ComponentKey,
    }));
    setPhpList(phpList);
  };

  const fetchPythonComponents = async () => {
    const Data = await fetchComponents('PYTHON');
    const pythonList = _.map(Data, (item) => ({
      label: item.ComponentDescription,
      value: item.ComponentKey,
    }));
    setPythonList(pythonList);
  };

  const fetchDotnetComponents = async () => {
    const Data = await fetchComponents('DOTNET');
    const dotnetList = _.map(Data, (item) => ({
      label: item.ComponentDescription,
      value: item.ComponentKey,
    }));
    setDontnetList(dotnetList);
  };

  const onChangeLanguageTypeType = (lang) => {
    field.resetToDefault(['uploadType', 'PackageUrl', 'PackageRuntimeCustomBuild']);
    if (lang === LANGUAGE_TYPE.JAVA) {
      setValue('PackageType', PACKAGE_TYPE.WAR);
    } else if (lang === LANGUAGE_TYPE.PHP) {
      setValue('PackageType', PACKAGE_TYPE.PHPZIP);
    } else if (lang === LANGUAGE_TYPE.PYTHON) {
      setValue('PackageType', PACKAGE_TYPE.PYTHONZIP);
    } else if (lang === LANGUAGE_TYPE.DOTNET) {
      setValue('PackageType', PACKAGE_TYPE.DOTNETZIP);
    }
  };

  const onChangePackageType = (val) => {
    field.resetToDefault(['Jdk', 'uploadType', 'PackageUrl', 'PackageRuntimeCustomBuild']);
    if (val === PACKAGE_TYPE.WAR) {
      setValue('container', warContainers[0]?.value);
    } else if (val === PACKAGE_TYPE.JAR) {
      setValue('container', jarContainers[0]?.value);
    }
  };

  const onChangeUploadType = async (val) => {
    if (actionType !== 'deploy') return;
    if (val === UPLOAD_TYPE.URL) {
      setValue('PackageUrl', '');
      const downloadUrl = await getBuildPackageUrl();
      setValue('PackageUrl', downloadUrl);
    } else {
      const { PackageUrl } = appConfig;
      setValue('PackageUrl', PackageUrl);
    }
  };

  const getBuildPackageUrl = async () => {
    setPkgUrlLoading(true);
    const { AppId, PackageUrl } = appConfig;
    const res = await services.getPackageVersionAccessableUrl(
      {
        AppId,
      },
      true,
    );
    setPkgUrlLoading(false);
    const { Data } = res || {};
    const downloadUrl = Data || PackageUrl;
    return downloadUrl;
  };

  const handleSetPackageVersion = () => {
    setValue('PackageVersion', new Date().getTime());
  };

  const onUseCustomChange = (val) => {
    setUseCustom(val);
    setValue('PackageRuntimeCustomBuild', '');
  };

  const shellValidator = (rule, value = '', callback) => {
    if (value.length >= 4096) {
      callback(
        intl('saenext.DeploySelectorField.micro-app.PackageConfig.ItCannotExceedCharacters'),
      );
      return;
    }
    callback();
  };

  const pkgVersionValidator = (rule, value = '', callback) => {
    const { PackageVersion } = appConfig;
    if (value === PackageVersion) {
      callback(
        intl(
          'saenext.DeploySelectorField.micro-app.PackageConfig.TheVersionNumberPackageversionAlready',
          { PackageVersion: PackageVersion },
        ),
      );
      return;
    }
    callback();
  };

  const setPhpExt = (value) => {
    setValue('phpExtensionsStr', value);
  };

  const setPhpPECL = (value) => {
    setValue('phpExtensPECLStr', value);
  };

  const warContainers = useMemo(() => {
    if (actionType === 'create' || (actionType === 'deploy' && preIsTypeImage)) {
      const containers = [...webContainerList, ...edasContainerList];

      return containers;
    } else if (actionType === 'deploy') {
      if (initValue.EdasContainerVersion) {
        return edasContainerList;
      } else if (initValue.WebContainer) {
        return webContainerList;
      }
    }
  }, [actionType, initValue, webContainerList, edasContainerList]);

  const jarContainers = useMemo(() => {
    const defaultContainer = {
      value: 'default',
      label: intl(
        'saenext.DeploySelectorField.micro-app.PackageConfig.StandardJavaApplicationRuntimeEnvironment',
      ),
    };
    if (actionType === 'create' || (actionType === 'deploy' && preIsTypeImage)) {
      const containers = [defaultContainer, ...edasContainerList];

      return containers;
    } else if (actionType === 'deploy') {
      if (initValue.EdasContainerVersion) {
        return edasContainerList;
      } else {
        return [defaultContainer];
      }
    }
  }, [actionType, initValue, edasContainerList]);

  const packageTextMap = {
    [PACKAGE_TYPE.WAR]: 'WAR',
    [PACKAGE_TYPE.JAR]: 'JAR',
    [PACKAGE_TYPE.PHPZIP]: 'ZIP',
    [PACKAGE_TYPE.PYTHONZIP]: 'ZIP',
    [PACKAGE_TYPE.DOTNETZIP]: 'ZIP',
  };

  const acceptFileMap = {
    [PACKAGE_TYPE.WAR]: '.war',
    [PACKAGE_TYPE.JAR]: '.jar',
    [PACKAGE_TYPE.PHPZIP]: '.zip',
    [PACKAGE_TYPE.PYTHONZIP]: '.zip',
    [PACKAGE_TYPE.DOTNETZIP]: '.zip',
  };

  const dotnetDemoLinkMap = {
    '.NET 3.1':
      'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-dotnet3.1.zip',
    '.NET 5.0':
      'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-dotnet5.0.zip',
    '.NET 6.0':
      'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-dotnet6.0.zip',
    '.NET 7.0':
      'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-dotnet7.0.zip',
    '.NET 8.0':
      'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-dotnet8.0.zip',
    '.NET 9.0':
      'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-dotnet9.0.zip',
  };

  const exampleLinkMap = {
    [PACKAGE_TYPE.WAR]:
      Jdk !== 'Open JDK 7'
        ? 'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae.war'
        : 'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-1.7.war',
    [PACKAGE_TYPE.JAR]:
      Jdk !== 'Open JDK 7'
        ? 'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae.jar'
        : 'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-1.7.jar',
    [PACKAGE_TYPE.PHPZIP]:
      'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-php.zip',
    [PACKAGE_TYPE.PYTHONZIP]:
      'https://sae-demo-cn-shenzhen.oss-cn-shenzhen.aliyuncs.com/demo/1.0/hello-sae-python.zip',
    [PACKAGE_TYPE.DOTNETZIP]: dotnetDemoLinkMap[Dotnet],
  };

  const uploadTextList = [
    {
      label: intl(
        'saenext.DeploySelectorField.micro-app.PackageConfig.UploadPackagetextmappackagetypePackages',
        { packageTextMapPackageType: packageTextMap[PackageType] },
      ),
      value: UPLOAD_TYPE.UPLOAD,
    },
    {
      label: intl(
        'saenext.DeploySelectorField.micro-app.PackageConfig.PackagetextmappackagetypePackageAddress',
        { packageTextMapPackageType: packageTextMap[PackageType] },
      ),
      value: UPLOAD_TYPE.URL,
    },
  ];

  return (
    <Form field={field} {...fieldLayout}>
      <Form.Item
        label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.TechnologyStackLanguage')}
        required
      >
        <Radio.Group
          disabled={actionType === 'deploy'}
          defaultValue={LANGUAGE_TYPE.JAVA}
          onChange={onChangeLanguageTypeType}
          name="ProgrammingLanguage"
          className="full-width package-box-group"
          dataSource={languageTypes}
        >
          {/* <Grid.Row>
              <Grid.Col span="6">
                <Radio value={LANGUAGE_TYPE.JAVA} label="Java" />
              </Grid.Col>
              <Grid.Col span="6">
                <Radio value={LANGUAGE_TYPE.PHP} label="PHP" />
              </Grid.Col>
              <Grid.Col span="6">
                <Radio value={LANGUAGE_TYPE.PYTHON} label="Python" />
              </Grid.Col>
             </Grid.Row> */}
        </Radio.Group>
      </Form.Item>
      <Form.Item
        label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.CodePackageType')}
        required
      >
        <Radio.Group
          // disabled={actionType === 'deploy'}
          defaultValue={PACKAGE_TYPE.JAR}
          name="PackageType"
          onChange={onChangePackageType}
          className="full-width"
        >
          <Grid.Row>
            <If condition={ProgrammingLanguage === LANGUAGE_TYPE.JAVA}>
              <Grid.Col span="6">
                <Radio id="jar" value={PACKAGE_TYPE.JAR}>
                  <TextWithBalloon
                    text={intl(
                      'saenext.DeploySelectorField.micro-app.PackageConfig.JarPackageDeployment',
                    )}
                    tips={
                      <>
                        {intl(
                          'saenext.DeploySelectorField.micro-app.PackageConfig.SupportsGeneralJarPackageDeployment',
                        )}
                      </>
                    }
                  />
                </Radio>
              </Grid.Col>
              <Grid.Col span="6">
                <Radio id="war" value={PACKAGE_TYPE.WAR}>
                  <TextWithBalloon
                    text={intl(
                      'saenext.DeploySelectorField.micro-app.PackageConfig.WarPackageDeployment',
                    )}
                    tips={
                      <>
                        {intl(
                          'saenext.DeploySelectorField.micro-app.PackageConfig.SupportsGeneralWarPackageDeployment',
                        )}
                      </>
                    }
                  />
                </Radio>
              </Grid.Col>
            </If>
            <If condition={ProgrammingLanguage === LANGUAGE_TYPE.PHP}>
              <Grid.Col span="6">
                <Radio
                  value={PACKAGE_TYPE.PHPZIP}
                  label={intl(
                    'saenext.DeploySelectorField.micro-app.PackageConfig.ZipPackageDeployment',
                  )}
                />
              </Grid.Col>
            </If>
            <If condition={ProgrammingLanguage === LANGUAGE_TYPE.PYTHON}>
              <Grid.Col span="6">
                <Radio
                  value={PACKAGE_TYPE.PYTHONZIP}
                  label={intl(
                    'saenext.DeploySelectorField.micro-app.PackageConfig.ZipPackageDeployment',
                  )}
                />
              </Grid.Col>
            </If>
            <If condition={ProgrammingLanguage === LANGUAGE_TYPE.DOTNET}>
              <Grid.Col span="6">
                <Radio
                  value={PACKAGE_TYPE.DOTNETZIP}
                  label={intl(
                    'saenext.DeploySelectorField.micro-app.PackageConfig.ZipPackageDeployment',
                  )}
                />
              </Grid.Col>
            </If>
          </Grid.Row>
        </Radio.Group>
      </Form.Item>

      <h5 className="package-deploy-panel requried">
        {intl(
          'saenext.DeploySelectorField.micro-app.PackageConfig.ConfigurePackagetextmappackagetypePackages',
          { packageTextMapPackageType: packageTextMap[PackageType] },
        )}
      </h5>
      <>
        <If condition={ProgrammingLanguage === LANGUAGE_TYPE.JAVA}>
          <If condition={PackageType === PACKAGE_TYPE.WAR}>
            <Form.Item
              {...fieldLayout}
              label={intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.ApplicationRuntimeEnvironment',
              )}
              required
              requiredMessage={intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.SelectAnApplicationRuntimeEnvironment',
              )}
              help={intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.ForUsersWhoUseSpringboot',
              )}
            >
              <Select
                defaultValue={warContainers?.[0]?.value}
                showSearch
                name="container"
                className="full-width"
                dataSource={warContainers}
              />
            </Form.Item>
          </If>

          <If condition={PackageType === PACKAGE_TYPE.JAR}>
            <Form.Item
              {...fieldLayout}
              label={intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.ApplicationRuntimeEnvironment',
              )}
              required
              requiredMessage={intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.SelectAnApplicationRuntimeEnvironment',
              )}
              help={intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.ForUsersWhoUseSpringboot.1',
              )}
            >
              <Select
                defaultValue={jarContainers[0]?.value}
                showSearch
                name="container"
                className="full-width"
                dataSource={jarContainers}
              />
            </Form.Item>
          </If>

          <Form.Item
            label={
              <TextWithBalloon
                text={intl('saenext.DeploySelectorField.micro-app.PackageConfig.JavaEnvironment')}
                tips={
                  <div>
                    <p>
                      {intl('saenext.DeploySelectorField.micro-app.PackageConfig.OpenJdkXxBasedOn')}
                    </p>
                    <p>
                      {intl(
                        'saenext.DeploySelectorField.micro-app.PackageConfig.OpenjdkXxxxxJdkAlpineBased',
                      )}
                    </p>
                    <p>
                      {intl(
                        'saenext.DeploySelectorField.micro-app.PackageConfig.DragonwellxSupportsTheLatestVersion',
                      )}
                    </p>
                  </div>
                }
              />
            }
            required
            requiredMessage={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.SelectAJavaEnvironment',
            )}
            {...fieldLayout}
            className="relative"
          >
            <Select
              defaultValue={javaList[0]?.value || 'Open JDK 8'}
              dataSource={javaList}
              showSearch
              name="Jdk"
              className="full-width"
            />
          </Form.Item>
        </If>

        <If condition={ProgrammingLanguage === LANGUAGE_TYPE.PHP}>
          <Form.Item
            label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.PhpEnvironment')}
            required
            requiredMessage={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.SelectAPhpEnvironment',
            )}
            {...fieldLayout}
          >
            <Select
              defaultValue={'PHP-FPM 7.3'}
              dataSource={phpList}
              showSearch
              name="Php"
              className="full-width"
            />
          </Form.Item>
        </If>

        <If condition={ProgrammingLanguage === LANGUAGE_TYPE.PYTHON}>
          <Form.Item
            label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.PythonEnvironment')}
            required
            requiredMessage={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.SelectAPythonEnvironment',
            )}
            {...fieldLayout}
          >
            <Select
              defaultValue={'PYTHON 3.9.15'}
              dataSource={pythonList}
              showSearch
              name="Python"
              className="full-width"
            />
          </Form.Item>
        </If>

        <If condition={ProgrammingLanguage === LANGUAGE_TYPE.DOTNET}>
          <Form.Item
            label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.NetCoreEnvironment')}
            required
            requiredMessage={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.SelectNetCoreEnvironment',
            )}
            {...fieldLayout}
          >
            <Select
              defaultValue={'.NET 3.1'}
              dataSource={dontnetList}
              showSearch
              name="Dotnet"
              className="full-width"
            />
          </Form.Item>
        </If>

        <Form.Item
          label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.FileUploadMethod')}
          required
          {...fieldLayout}
        >
          <Radio.Group
            className="full-width"
            name="uploadType"
            defaultValue={UPLOAD_TYPE.UPLOAD}
            dataSource={uploadTextList}
            onChange={onChangeUploadType}
          />
        </Form.Item>

        <ComShow if={uploadType === UPLOAD_TYPE.UPLOAD}>
          <Form.Item
            label={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.UploadPackagetextmappackagetypePackages.1',
              { packageTextMapPackageType: packageTextMap[PackageType] },
            )}
            required
            // @ts-ignore
            requiredMessage={
              intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.PleaseUploadPackagetextmappackagetypePackage',
                { packageTextMapPackageType: packageTextMap[PackageType] },
              ) as string
            }
            {...fieldLayout}
          >
            <PackageUploader
              accept={acceptFileMap[PackageType]}
              fileNameReg={/^[a-zA-Z0-9-_\.]+\.(?:war|jar|zip)$/}
              fileNameMessage={intl(
                'saenext.shared.DeploySelectorField.PackageUploader.TheFileNameConsistsOf',
              )}
              name="PackageUrl"
              appId={appConfig.AppId}
              className="full-width"
            />

            <a href={exampleLinkMap[PackageType]} target="_blank">
              {intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.DownloadPackagetextmappackagetypePackageSample',
                { packageTextMapPackageType: packageTextMap[PackageType] },
              )}
            </a>
            <If condition={PackageType === PACKAGE_TYPE.PYTHONZIP}>
              <Balloon
                align="t"
                trigger={<Icon size="small" type="help_fill" className="color-primary ml-l" />}
                closable={false}
              >
                {intl(
                  'saenext.DeploySelectorField.micro-app.PackageConfig.TheSamplePackageStartupCommand',
                )}
              </Balloon>
            </If>
            <If condition={PackageType === PACKAGE_TYPE.DOTNETZIP}>
              <Balloon
                align="t"
                trigger={<Icon size="small" type="help_fill" className="color-primary ml-l" />}
                closable={false}
              >
                {intl(
                  'saenext.DeploySelectorField.micro-app.PackageConfig.TheSamplePackageStartupCommand.1',
                )}

                <br />
                {intl('saenext.DeploySelectorField.micro-app.PackageConfig.TheSamplePackagePortIs')}
              </Balloon>
            </If>
          </Form.Item>
        </ComShow>
        <ComShow if={uploadType === UPLOAD_TYPE.URL}>
          <Form.Item
            label={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.PackagetextmappackagetypePackageAddress.1',
              { packageTextMapPackageType: packageTextMap[PackageType] },
            )}
            required
            // @ts-ignore
            requiredMessage={
              intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.PleaseFillInPackagetextmappackagetypePackage',
                { packageTextMapPackageType: packageTextMap[PackageType] },
              ) as string
            }
            {...fieldLayout}
          >
            <Input
              trim
              htmlType="text"
              className="full-width"
              name="PackageUrl"
              state={pkgUrlLoading ? 'loading' : undefined}
            />
          </Form.Item>
        </ComShow>

        {actionType !== 'deploy' && (
          <Form.Item
            label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.Version')}
            {...fieldLayout}
            required
            requiredMessage={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.PleaseFillInTheVersion',
            )}
            validator={pkgVersionValidator}
            className="relative"
          >
            <Input
              trim
              hasLimitHint
              maxLength={32}
              defaultValue={new Date().getTime()}
              name="PackageVersion"
              className="full-width"
            />

            <Button
              onClick={handleSetPackageVersion}
              text
              type="primary"
              className="ml-l timestamp"
            >
              {intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.UseTimestampAsVersionNumber',
              )}
            </Button>
          </Form.Item>
        )}

        <Form.Item
          label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.TimeZoneSettings')}
          required
          {...fieldLayout}
        >
          <Select
            className="full-width"
            name="Timezone"
            defaultValue={'Asia/Shanghai'}
            dataSource={[
              {
                label: 'UTC+8',
                value: 'Asia/Shanghai',
              },
              {
                label: 'UTC+0',
                value: 'Universal',
              },
            ]}
          />
        </Form.Item>

        <If
          // condition={PackageType === PACKAGE_TYPE.DOTNETZIP}
          condition={PackageType !== PACKAGE_TYPE.JAR && PackageType !== PACKAGE_TYPE.WAR}
        >
          <Form.Item
            label={intl('saenext.app-create.micro-app.AdvanceCreator.StartCommand')}
            required={
              PackageType === PACKAGE_TYPE.PYTHONZIP || PackageType === PACKAGE_TYPE.DOTNETZIP
            }
            {...fieldLayout}
          >
            <CommandArgsForm
              field={field}
              required={
                PackageType === PACKAGE_TYPE.PYTHONZIP || PackageType === PACKAGE_TYPE.DOTNETZIP
              }
            />
          </Form.Item>
        </If>

        <If condition={feature.runtime_custom_build}>
          <Checkbox
            label={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.CustomizeRuntimeEnvironmentSettingsIncluding',
            )}
            className="m-10"
            checked={useCustom}
            onChange={onUseCustomChange}
          />

          <If condition={useCustom}>
            <If condition={ProgrammingLanguage === LANGUAGE_TYPE.PHP}>
              <Form.Item
                label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.PhpExtension')}
                {...fieldLayout}
              >
                <Input.TextArea
                  name="phpExtensionsStr"
                  placeholder={intl(
                    'saenext.DeploySelectorField.micro-app.PackageConfig.MultipleParametersAreSeparatedBy',
                  )}
                />
              </Form.Item>
              <Form.Item
                label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.PhpPeclExtension')}
                {...fieldLayout}
              >
                <Input.TextArea
                  name="phpExtensPECLStr"
                  placeholder={intl(
                    'saenext.DeploySelectorField.micro-app.PackageConfig.MultipleParametersAreSeparatedBy',
                  )}
                />
              </Form.Item>
            </If>
            <If condition={ProgrammingLanguage === LANGUAGE_TYPE.PYTHON}>
              <Form.Item
                label={intl(
                  'saenext.DeploySelectorField.micro-app.PackageConfig.PythonDependencies',
                )}
                help={intl(
                  'saenext.DeploySelectorField.micro-app.PackageConfig.ByDefaultSaeInstallsDependencies',
                )}
                {...fieldLayout}
              >
                <Input.TextArea
                  name="pythonModulesStr"
                  placeholder={intl(
                    'saenext.DeploySelectorField.micro-app.PackageConfig.TheInputFormatIsThe',
                  )}
                />
              </Form.Item>
            </If>

            <Form.Item validator={shellValidator}>
              <If condition={ProgrammingLanguage === LANGUAGE_TYPE.JAVA && JdkImageMap[Jdk]}>
                <Message type="warning" className="mt-s mb-s">
                  {intl(
                    'saenext.DeploySelectorField.micro-app.PackageConfig.NoteTheCurrentBasicImage.2',
                    { JdkImageMapJdk: JdkImageMap[Jdk] },
                  )}
                  <a href={CachedData.confLink('help:sae:java-runtime-environment-2-0')}>
                    {intl(
                      'saenext.DeploySelectorField.micro-app.PackageConfig.JavaRuntimeEnvironmentDescription',
                    )}
                  </a>
                </Message>
              </If>
              <CustomBuildShell
                name="PackageRuntimeCustomBuild"
                packageType={PackageType}
                phpExtensionsStr={phpExtensionsStr}
                phpExtensPECLStr={phpExtensPECLStr}
                setPhpExt={setPhpExt}
                setPhpPECL={setPhpPECL}
              />
            </Form.Item>
          </If>
        </If>
      </>

      <If condition={PackageType === PACKAGE_TYPE.WAR}>
        <h5 className="package-deploy-panel">
          <span>
            {intl('saenext.DeploySelectorField.micro-app.PackageConfig.JavaTomcatSettings')}
          </span>
          <span className="text-description ml-l">
            {intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.SetPortJavaTomcatApplication',
            )}
          </span>
        </h5>

        <Form.Item wrapperCol={{ span: 24 }}>
          <TomcatConfig name="TomcatConfig" packageUrl={PackageUrl} />
        </Form.Item>
      </If>

      <If condition={ProgrammingLanguage === LANGUAGE_TYPE.PHP}>
        <h5 className="package-deploy-panel">
          <span>
            {intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.PhpIniConfigurationFileModification',
            )}
          </span>
          <span className="text-description ml-l">
            {intl('saenext.DeploySelectorField.micro-app.PackageConfig.UseACustomPhpIni')}
          </span>
        </h5>
        <PHPIniConfig field={field} usePhpCustom={usePhpCustom} setUsePhpCustom={setUsePhpCustom} />
      </If>
    </Form>
  );
});

export default PackageConfig;
