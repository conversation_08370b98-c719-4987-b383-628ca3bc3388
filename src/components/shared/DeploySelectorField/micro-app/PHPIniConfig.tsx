import { intl } from '@ali/cnd';
import React from 'react';
import { Checkbox, Form, Input } from '@ali/cnd';
import { ShellEditor } from '@ali/cnd';
import If from '~/components/shared/If';

const FORM_ITEM_LAYOUT = {
  labelTextAlign: 'left' as 'left',
  labelCol: {
    fixedSpan: 6,
  },
  wrapperCol: {
    span: 16,
  },
};

const PHPIniConfig = (props) => {
  const { field, usePhpCustom, setUsePhpCustom } = props;

  const onUseCustomChange = (checked) => {
    setUsePhpCustom(checked);

    if (!checked) {
      field.reset(['PhpConfig', 'PhpConfigLocation']);
    }
  };

  const iniValidator = (rule, value, callback) => {
    if (value.length >= 5000) {
      callback(intl('saenext.DeploySelectorField.micro-app.PHPIniConfig.ItCannotExceedCharacters'));
      return;
    }
    callback();
  };

  return (
    <>
      <Checkbox
        label={intl('saenext.DeploySelectorField.micro-app.PHPIniConfig.CustomPhpIni')}
        className="m-10"
        checked={usePhpCustom}
        onChange={onUseCustomChange}
      />

      <If condition={usePhpCustom}>
        <Form.Item
          required={usePhpCustom}
          requiredMessage={intl(
            'saenext.DeploySelectorField.micro-app.PHPIniConfig.FillInThePhpIni',
          )}
          extra={intl('saenext.DeploySelectorField.micro-app.PHPIniConfig.EnterTheContentOfThe')}
          validator={iniValidator}
        >
          <ShellEditor
            height={150}
            title={intl('saenext.DeploySelectorField.micro-app.PHPIniConfig.ConfigurationContent')}
            language="ini"
            name="PhpConfig"
          />
        </Form.Item>
        <Form.Item
          label={intl('saenext.DeploySelectorField.micro-app.PHPIniConfig.PhpIniMountPath')}
          required={usePhpCustom}
          requiredMessage={intl(
            'saenext.DeploySelectorField.micro-app.PHPIniConfig.EnterThePhpIniMount',
          )}
          {...FORM_ITEM_LAYOUT}
        >
          <Input name="PhpConfigLocation" className="full-width" />
        </Form.Item>
      </If>
    </>
  );
};

export default PHPIniConfig;
