import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Balloon, Checkbox, Field, Form, Icon, Input, NumberPicker, Radio, Select } from '@ali/cnd';
import If from '~/components/shared/If';
import { jsonParse } from '~/utils/transfer-data';
import ComShow from '~/components/shared/ComShow';
const Option = Select.Option;

const FORM_ITEM_LAYOUT = {
  labelTextAlign: 'left' as 'left',
  labelCol: {
    fixedSpan: 6,
  },
  wrapperCol: {
    span: 16,
  },
};

enum CONTEXTTYPE {
  PACKAGENAME = 1,
  ROOT = 2,
  CUSTOM = 3,
}

const TomcatConfig = (props) => {
  const { value = '', onChange = () => {}, packageUrl = '' } = props;

  const [useCustom, setUseCustom] = useState(false);

  useEffect(() => {
    initData();
  }, [value]);

  useEffect(() => {
    onPackageUrlChange();
  }, [packageUrl]);

  const onFieldChange = () => {
    onChange(JSON.stringify(getValues()));
  };

  const field = Field.useField({
    onChange: onFieldChange,
  });
  const { getValues, setValue, setValues } = field;

  const { contextType } = getValues() as any;

  const initData = () => {
    const valueData = jsonParse(value);
    if (!valueData) {
      setUseCustom(false);
      return;
    }

    setUseCustom(true);

    const { contextType, contextPath } = valueData;
    if (contextType) {
    } else if (contextPath === getFileName(packageUrl)) {
      valueData.contextType = CONTEXTTYPE.PACKAGENAME;
    } else if (contextPath === '/') {
      valueData.contextType = CONTEXTTYPE.ROOT;
    } else {
      valueData.contextType = CONTEXTTYPE.CUSTOM;
    }

    setValues(valueData);
  };

  const onUseCustomChange = (checked) => {
    setUseCustom(checked);
    if (checked) {
      onFieldChange();
    } else {
      onChange('');
    }
  };

  const onContextTypeChange = (value) => {
    if (value === CONTEXTTYPE.PACKAGENAME) {
      const filename = getFileName(packageUrl);
      setValue('contextPath', filename);
    } else if (value === CONTEXTTYPE.ROOT) {
      setValue('contextPath', '/');
    } else if (value === CONTEXTTYPE.CUSTOM) {
      setValue('contextPath', '');
    }
  };

  const onPackageUrlChange = () => {
    if (contextType !== CONTEXTTYPE.PACKAGENAME) return;

    const filename = getFileName(packageUrl);
    setValue('contextPath', filename);
    onFieldChange();
  };

  const getFileName = (url) => {
    const filename = url.split('/').pop().split('?')[0];
    const filenameWithoutExtension = filename.replace(/\.[^.]+$/, '');
    return filenameWithoutExtension;
  };

  return (
    <>
      <Checkbox
        label={intl('saenext.DeploySelectorField.micro-app.TomcatConfig.CustomConfiguration')}
        className="m-10"
        checked={useCustom}
        onChange={onUseCustomChange}
      />

      <ComShow if={useCustom}>
        <Form field={field} {...FORM_ITEM_LAYOUT}>
          <Form.Item
            label={intl('saenext.DeploySelectorField.micro-app.TomcatConfig.ApplicationPort')}
          >
            <NumberPicker
              // @ts-ignore
              name="port"
              defaultValue={8080}
              max={49151}
              min={1024}
              className="full-width"
            />
          </Form.Item>
          <Form.Item label="Tomcat Context:">
            <Radio.Group
              name="contextType"
              defaultValue={CONTEXTTYPE.ROOT}
              onChange={onContextTypeChange}
            >
              <If condition={packageUrl}>
                <Radio value={CONTEXTTYPE.PACKAGENAME}>
                  {intl('saenext.DeploySelectorField.micro-app.TomcatConfig.PackageName')}
                </Radio>
              </If>
              <Radio value={CONTEXTTYPE.ROOT}>
                {intl('saenext.DeploySelectorField.micro-app.TomcatConfig.RootDirectory')}
              </Radio>
              <Radio value={CONTEXTTYPE.CUSTOM}>
                {intl('saenext.DeploySelectorField.micro-app.TomcatConfig.Custom')}
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label={intl('saenext.DeploySelectorField.micro-app.TomcatConfig.CustomPath')}
            {...FORM_ITEM_LAYOUT}
            className={contextType !== CONTEXTTYPE.CUSTOM ? 'none' : ''}
          >
            <Input
              name="contextPath"
              defaultValue={'/'}
              placeholder={intl(
                'saenext.DeploySelectorField.micro-app.TomcatConfig.ItMayContainEnglishCharacters',
              )}
              maxLength={32}
              className="full-width"
            />
          </Form.Item>
          <Form.Item
            label={
              <>
                {intl('saenext.DeploySelectorField.micro-app.TomcatConfig.MaximumNumberOfThreads')}

                <Balloon
                  align="t"
                  trigger={<Icon size="small" type="help_fill" className="color-primary" />}
                  closable={false}
                >
                  {intl(
                    'saenext.DeploySelectorField.micro-app.TomcatConfig.TheMaximumNumberOfThreads',
                  )}
                </Balloon>
              </>
            }
          >
            <NumberPicker
              // @ts-ignore
              name="maxThreads"
              defaultValue={400}
              max={1024}
              min={10}
              className="full-width"
            />
          </Form.Item>
          <Form.Item
            label={
              <>
                {intl('saenext.DeploySelectorField.micro-app.TomcatConfig.TomcatEncoding')}

                <Balloon
                  align="t"
                  trigger={<Icon size="small" type="help_fill" className="color-primary" />}
                  closable={false}
                >
                  {intl(
                    'saenext.DeploySelectorField.micro-app.TomcatConfig.UsebodyencodingforuriDefaultValueIsTrue',
                  )}
                </Balloon>
              </>
            }
            className="relative"
          >
            <Select name="uriEncoding" defaultValue="ISO-8859-1" className="full-width next-col-12">
              <Option value="UTF-8">UTF-8</Option>
              <Option value="ISO-8859-1">ISO-8859-1</Option>
              <Option value="GBK">GBK</Option>
              <Option value="GB2312">GB2312</Option>
            </Select>
            <Checkbox
              name="useBodyEncodingForUri"
              defaultChecked={true}
              label="Use Body Encoding for URL"
              className="ml-l next-col-10"
            />
          </Form.Item>
        </Form>
      </ComShow>
    </>
  );
};

export default TomcatConfig;
