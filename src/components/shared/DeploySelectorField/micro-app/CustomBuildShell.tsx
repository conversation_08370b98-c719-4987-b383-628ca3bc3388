import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import { ShellEditor } from '@ali/cnd';
import { Actions, Copy, Icon, LinkButton } from '@ali/cnd';
import { handleWinLineBreak } from '~/utils/transfer-data';
import { PACKAGE_TYPE } from '../constant';
import _ from 'lodash';
import CachedData from '~/cache/common';

const CustomBuildShell = (props) => {
  const {
    value = '#!/bin/bash',
    onChange,
    packageType,
    phpExtensionsStr = '',
    phpExtensPECLStr = '',
    setPhpExt,
    setPhpPECL,
  } = props;

  const installReg = /^\s{0,1}[a-zA-Z]{1}[a-zA-Z0-9\-\.\_]{0,}/gm;
  const enableReg = /^\s{0,1}[a-zA-Z]{1,}/gm;

  useEffect(() => {
    if (packageType === PACKAGE_TYPE.PHPZIP) {
      extToShell(phpExtensionsStr);
    }
  }, [phpExtensionsStr]);

  useEffect(() => {
    if (packageType === PACKAGE_TYPE.PHPZIP) {
      extPECLToSHell(phpExtensPECLStr);
    }
  }, [phpExtensPECLStr]);

  const extToShell = (phpExtensionsStr) => {
    if (phpExtensionsStr === '') {
      const extCmdReg = /(## PHP Extension)(.|\n)*?(## PHP Extension end)/g;
      const extCmd = value.replace(extCmdReg, '');
      onChange(extCmd);
    }
    const installArr = phpExtensionsStr.match(installReg);
    const enableArr = phpExtensionsStr.match(enableReg);
    if (!installArr || !enableArr) return '';
    let shellExt = '\n';
    _.forEach(installArr, (installItem, index) => {
      const enableItem = enableArr[index];
      if (installItem.indexOf(enableItem) > -1) {
        shellExt +=
          `docker-php-ext-install ${installItem} 1>/dev/null && docker-php-ext-enable ${enableItem} 1>/dev/null` +
          '\n';
      }
    });

    const extCmdReg = /(?:## PHP Extension)(.|\n)*?(?=## PHP Extension end)/; //## PHP Extension 和 ## PHP Extension end 包裹的中间的语句(含## PHP Extension)
    let extCmd = '';
    if (value.match(extCmdReg)) {
      extCmd = value.replace(extCmdReg, '## PHP Extension' + shellExt);
    } else {
      extCmd = value + '\n## PHP Extension' + shellExt + '## PHP Extension end\n';
    }
    onChange(extCmd);
  };

  const extPECLToSHell = (phpExtensPECLStr) => {
    if (phpExtensPECLStr === '') {
      const extCmdReg = /(## PHP PECL)(.|\n)*?(## PHP PECL end)/g;
      const extCmd = value.replace(extCmdReg, '');
      onChange(extCmd);
      return extCmd;
    }
    const installArr = phpExtensPECLStr.match(installReg);
    const enableArr = phpExtensPECLStr.match(enableReg);

    if (!installArr || !enableArr) return '';
    let shellExt = '\n';
    _.forEach(installArr, (installItem, index) => {
      const enableItem = enableArr[index];
      if (installItem.indexOf(enableItem) > -1) {
        shellExt +=
          `yes '' | pecl install -f ${installItem} 1>/dev/null && docker-php-ext-enable ${enableItem} 1>/dev/null` +
          '\n';
      }
    });

    const peclCmdReg = /(?:## PHP PECL)(.|\n)*?(?=## PHP PECL end)/; //## PHP PECL 和 ## PHP PECL end 包裹的中间的语句
    let extCmd = '';
    if (value.match(peclCmdReg)) {
      extCmd = value.replace(peclCmdReg, '## PHP PECL' + shellExt);
    } else {
      extCmd = value + '\n## PHP PECL' + shellExt + '## PHP PECL end\n';
    }
    onChange(extCmd);
  };

  const onShellChange = (val) => {
    const parseVal = handleWinLineBreak(val); //处理Windows换行符

    // setShellCmd(parseVal);
    onChange(parseVal);
    if (packageType === PACKAGE_TYPE.PHPZIP) {
      shellParsePHP(parseVal);
    }
  };

  const shellParsePHP = (val) => {
    const extArr = shellToArr(
      val,
      /(?:docker-php-ext-install )(.)*?(?=\n)/g,
      'docker-php-ext-install ',
    );
    const peclArr = shellToArr(
      val,
      /(?:yes '' \| pecl install )(.)*?(?=\n)/g,
      "yes '' | pecl install ",
    );
    if (extArr.length) {
      setPhpExt(extArr.join('\n'));
    } else {
      setPhpExt('');
    }
    if (peclArr.length) {
      setPhpPECL(peclArr.join('\n'));
    } else {
      setPhpPECL('');
    }
  };

  const shellToArr = (val, reg, head) => {
    const multiLineArr = val.match(reg);
    if (!multiLineArr || multiLineArr.length === 0) return [];
    const resultArr = [];
    const installReg = /^[a-zA-Z]{1}[a-zA-Z0-9\-\.]{1,}$/;
    multiLineArr.forEach((line) => {
      const _line = line.replace(head, '');
      const paramsArr = _line.trim().split(/\s+/);
      try {
        paramsArr.forEach((item) => {
          // if (!item) return;
          if (installReg.test(item)) {
            resultArr.push(item);
            return;
          }
          if (/^\-[a-zA-Z]+$/.test(item)) {
            return;
          } else {
            throw new Error(); //安装语句结束，跳出循环
          }
        });
      } catch (e) {}
    });
    return resultArr;
  };

  return (
    <>
      <ShellEditor
        height={500}
        title={intl('saenext.DeploySelectorField.micro-app.CustomBuildShell.RunTheCommand')}
        language="shell"
        value={value}
        onChange={onShellChange}
      />

      <Actions style={{ marginTop: 4 }}>
        <Copy
          showIcon
          text={value}
          icon={
            <>
              <span className="color-primary">
                {intl('saenext.DeploySelectorField.micro-app.CustomBuildShell.Copy')}
              </span>
              <Icon type="copy" size={'small'} />
            </>
          }
        />

        <LinkButton
          onClick={() => {
            onChange('');
          }}
        >
          {intl('saenext.DeploySelectorField.micro-app.CustomBuildShell.Clear')}
        </LinkButton>
        <a href={CachedData.confLink('help:sae:configure-runtime-environments-for-code-packages')} target="_blank">
          {intl('saenext.DeploySelectorField.micro-app.CustomBuildShell.HowToDebug')}
        </a>
      </Actions>
    </>
  );
};

export default CustomBuildShell;
