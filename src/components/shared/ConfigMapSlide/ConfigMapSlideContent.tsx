import { intl } from '@ali/cnd';
import React from 'react';
import { Form, Input, Field, Message, Loading } from '@ali/xconsole';
import services from '~/services';
import _, { each, keys, map, get } from 'lodash';
import jsyaml from 'js-yaml';
import { jsonParse } from '~/utils/transfer-data';
import { getParams } from '~/utils/global';
import ManualCreated from './ManualCreated';

export const CONFIG_TYPES = [
  {
    value: 'json',
    label: 'JSON',
  },
  {
    value: 'yaml',
    label: 'YAML',
  },
];

export const FORM_ITEM_LAYOUT = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 16,
  },
};

class ConfigMapSlideContent extends React.Component {
  field: Field;
  props: any;
  constructor(props) {
    super(props);
    this.field = new Field(this);
    this.state = {
      errMessage: '',
      loading: false,
    };
  }

  componentDidMount() {
    this.fetchDescribeConfigMap();
  }

  fetchDescribeConfigMap = async () => {
    const { type, ConfigMapId, data } = this.props;
    if (type === 'edit' || type === 'copy' || type === 'preview') {
      const { setValues } = this.field;
      if (data) {
        setValues(data);
        return;
      }
      this.setState({
        loading: true,
      });
      try {
        const { Data } = await services.DescribeConfigMap({ ConfigMapId });
        setValues({
          Name: type === 'edit' ? Data.Name : Data.Name + '-copy',
          Description: Data.Description,
          Data: Data.Data,
        });
        this.setState({
          loading: false,
        });
      } catch (error) {
        this.setState({
          loading: false,
        });
      }
    }
  };
  handleCreateConfirm = async (_values: any) => {
    const { reload } = this.props;
    let params = {
      NamespaceId: _values.NamespaceId,
      Name: _values.Name,
      Description: _values.Description,
    } as any;
    const newData = this.handleConfigData(_values.Data);
    const { Data } = await services.CreateConfigMap({
      content: { Data: JSON.stringify(newData) },
      params,
    });
    reload();
    Message.success(
      intl('saenext.shared.ConfigMapSlide.ConfigMapSlideContent.TheConfigurationItemIsCreated'),
    );
    return Data;
  };

  handleEditConfirm = async (_values) => {
    const { reload, ConfigMapId } = this.props;
    let params = {
      ConfigMapId,
      Description: _values.Description,
    } as any;
    const newData = this.handleConfigData(_values.Data);
    const { Data } = await services.UpdateConfigMap({
      content: { Data: JSON.stringify(newData) },
      params,
    });
    reload();
    Message.success(
      intl('saenext.shared.ConfigMapSlide.ConfigMapSlideContent.TheConfigurationItemHasBeen'),
    );
    return Data;
  };

  handleConfigData=(values)=>{
    // 字符串类型数字或布尔值在json yaml格式展示时会被转换为数值/布尔类型，提交时统一转为string
    const newData = Object.fromEntries(
      Object.keys(values).map(key => [key, String(values[key])])
    );
    return newData
  }

  handleConfirm = async () => {
    const { type } = this.props;
    const { errors, values } = await this.field.validatePromise();
    if (!errors) {
      return type !== 'edit' ? this.handleCreateConfirm(values) : this.handleEditConfirm(values);
    } else {
      return Promise.reject();
    }
  };
  validateName = (rule, value, callback) => {
    const { type, nameList = [] } = this.props;
    if (type === 'edit') {
      callback();
      return {
        isSuccess: true,
      };
    }
    const result = new Promise((resolve) => {
      if (_.includes(nameList, value)) {
        callback(
          intl(
            'saenext.shared.ConfigMapSlide.ConfigMapSlideContent.TheConfigurationItemNameAlready',
          ),
        );
      } else {
        callback();
        return resolve({
          isSuccess: true,
        });
      }
    });

    return result;
  };

  render() {
    const { init } = this.field;
    const { type, namespaceId } = this.props;
    const { loading } = this.state as any;
    return (
      <div style={{ width: 750 }}>
        <Loading visible={loading} style={{ width: '100%' }}>
          <Form {...FORM_ITEM_LAYOUT} field={this.field} labelAlign="top" isPreview={type === 'preview'}>
            <Form.Item
              label={intl('saenext.shared.ConfigMapSlide.ConfigMapSlideContent.Namespace')}
            >
              <Input
                {...init('NamespaceId', {
                  initValue: getParams('namespaceId') || namespaceId,
                })}
                disabled
              />
            </Form.Item>
            <Form.Item
              label={intl(
                'saenext.shared.ConfigMapSlide.ConfigMapSlideContent.ConfigurationItemName',
              )}
              required
            >
              <Input
                {...init('Name', {
                  rules: [
                    {
                      pattern: /^[a-z0-9]([-a-z0-9]{0,40}[a-z0-9])?$/,
                      message: intl(
                        'saenext.shared.ConfigMapSlide.ConfigMapSlideContent.ACombinationOfNumbersLetters',
                      ),
                    },
                    {
                      validator: this.validateName,
                      trigger: ['onBlur', 'onChange'],
                    },
                    {
                      required: true,
                      message: intl(
                        'saenext.shared.ConfigMapSlide.ConfigMapSlideContent.EnterTheNameOfThe',
                      ),
                    },
                  ],
                })}
                disabled={type === 'edit'}
              />
            </Form.Item>
            <ManualCreated field={this.field} isPreview={type === 'preview'} />
          </Form>
        </Loading>
      </div>
    );
  }
}

export default ConfigMapSlideContent;
