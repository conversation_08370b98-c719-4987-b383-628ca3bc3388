import { intl } from '@ali/cnd';
import React, { useRef } from 'react';
import ConfigMapSlideContent from './ConfigMapSlideContent';
import SlideButton from '../SlideButton';

const ConfigMapSlide = (props) => {
  const {
    linkButton,
    buttonText,
    buttonType,
    slideTitle,
    className,
    ...slideprops
  } = props;

  const configMapSlide = useRef(null);
  return (
    <SlideButton
      className={className}
      buttonType={buttonType}
      linkButton={linkButton}
      buttonText={buttonText || intl('saenext.shared.ConfigMapSlide.CreateAConfigurationItemK')}
      slideTitle={slideTitle || intl('saenext.shared.ConfigMapSlide.CreateAConfigurationItem')}
      slideContent={<ConfigMapSlideContent ref={configMapSlide} {...slideprops} />}
      submit={() => configMapSlide?.current?.handleConfirm()}
      slideSize={900}
    />
  );
};

export default ConfigMapSlide;
