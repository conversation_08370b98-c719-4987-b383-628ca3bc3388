import { intl } from '@ali/cnd';
import React, { Fragment, useRef } from 'react';
import { Form, Input } from '@ali/xconsole';
import { EnvEditor } from '@ali/cnd';
import { isEmpty } from 'lodash';
import CachedData from '~/cache/common';
import { delay, strByteSize } from '../../../utils/global';

const ManualCreated = ({ field, isPreview = false }) => {
  const envRef = useRef(null);

  const { init } = field;

  return (
    <Fragment>
      <Form.Item
        label={intl('saenext.shared.ConfigMapSlide.ManualCreated.ConfigurationItemDescription')}
      >
        <Input.TextArea
          {...init('Description')}
          placeholder={intl(
            'saenext.shared.ConfigMapSlide.ManualCreated.EnterADescriptionThatCannot',
          )}
          maxLength={255}
        />
      </Form.Item>
      <Form.Item
        label={intl('saenext.shared.ConfigMapSlide.ManualCreated.ConfigureMapping')}
        required
      >
        <EnvEditor
          isPreview={isPreview}
          {...init('Data', {
            rules: [
              {
                required: true,
                message: intl(
                  'saenext.shared.ConfigMapSlide.ManualCreated.PleaseFillInTheConfiguration',
                ),
              },
              {
                validator: async (rule, value, callback) => {
                  await delay(500);
                  const valid = await envRef.current?.validate();
                  if (!valid) {
                    callback(' ');
                    return;
                  }
                  if (isEmpty(value)) {
                    callback(
                      intl(
                        'saenext.shared.ConfigMapSlide.ManualCreated.PleaseFillInTheConfiguration',
                      ),
                    );
                  } else if (strByteSize(JSON.stringify(value)) > 1024 * 1024) {
                    callback(
                      intl('saenext.shared.ConfigMapSlide.ManualCreated.TheSizeOfTheConfiguration'),
                    );
                  } else {
                    callback();
                  }
                },
              },
            ],
          })}
          ref={envRef}
          isShowYaml
          valueFullScreenEdit
          keyPattern={/^[-0-9a-zA-Z_.]+$/}
          keyPatternMessage={intl(
            'saenext.shared.ConfigMapSlide.ManualCreated.VariableNamesCanOnlyContain',
          )}
          valuePattern={/^[\s\S]+$/}
          valuePatternMessage={intl(
            'saenext.shared.ConfigMapSlide.ManualCreated.InvalidVariableValue',
          )}
          // valueRequired={false}
          hintText={
            (
              <div>
                {intl(
                  'saenext.shared.ConfigMapSlide.ManualCreated.EnterKubernetesConfigmapManifestFile',
                )}

                <a href={CachedData.confLink('help:sae:manage-and-use-configuration-items-k8s-configmap')}>
                  {intl('saenext.shared.ConfigMapSlide.ManualCreated.FileFormatDescription')}
                </a>
              </div>
            ) as any
          }
        />
      </Form.Item>
    </Fragment>
  );
};

export default ManualCreated;
