import { intl } from '@ali/cnd';
import React, {
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import CachedData from '~/cache/common';
import If from '../If';
import { ConsoleContext, Form, Message, Switch, Icon } from '@ali/cnd';
import { Form as FormDeep, SelectField, TableField, TextField } from '@ali/deep';
import services from '~/services';
import _ from 'lodash';
import TextRefreshButton from '../TextRefreshButton';
import ExternalLink from '../ExternalLink';
import ComShow from '../ComShow';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';
import RefreshButton from '~/components/shared/RefreshButton';
import { getProductOpenStatus } from '~/utils/openStatus';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

const KafkaSelector = (props, ref) => {
  const { vpcId, value, onChange = () => {} } = props;
  const [useKafka, setUseKafka] = useState(false);
  const [kafkaList, setKafkaList] = useState<any>([{}]);
  const [kafkaListLoading, setKafkaListLoading] = useState(false);
  const [topicList, setTopicList] = useState([]);
  // const [openStatus, setOpenStatus] = useState(CachedData.getOpenStatus('alikafka'));
  // const [topicLoading, setTopicLoading] = useState(false);

  const formField = useRef(null);
  const tableField = useRef(null);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const openStatus = CachedData.getOpenStatus('alikafka');

  const { kafkaInstanceId = '', kafkaConfigs = [] } = formField.current?.getValue() || {};

  const logTypeList = useMemo(() => {
    // stdout最多选一次
    const disabledStd = _.some(kafkaConfigs, (item) => {
      return item?.logType === 'stdout';
    });

    return [
      {
        label: intl('saenext.shared.KafkaSelectorField.KafkaSelector.FileLogLogPathIn'),
        value: 'file_log',
      },
      {
        label: intl('saenext.shared.KafkaSelectorField.KafkaSelector.ContainerStandardOutputLogs'),
        value: 'stdout',
        disabled: disabledStd,
      },
    ];
  }, [kafkaConfigs]);

  useEffect(() => {
    initValue();
  }, [value]);

  useEffect(() => {
    useKafka && getKafkaList();
  }, [useKafka, vpcId]);

  useEffect(() => {
    if (!useKafka) {
      onChange('');
    }
  }, [useKafka]);

  useEffect(() => {
    kafkaInstanceId && getTopicList();
  }, [kafkaInstanceId]);

  useImperativeHandle(
    ref,
    () => ({
      validate,
    }),
    [useKafka, kafkaInstanceId, kafkaConfigs],
  );

  const initValue = () => {
    if (value?.kafkaConfigs?.length) {
      setUseKafka(true);
      formField.current?.setValue(value);

      setStdDir();
    }
  };

  const setStdDir = () => {
    const formGroupIds = tableField.current.getItems?.();
    _.forEach(formGroupIds, (formGroupId) => {
      const itemField = tableField.current.getField(formGroupId);
      const logType = itemField.getValue(`${formGroupId}.logType`);
      if (logType === 'stdout') {
        const logDirCom = tableField.current.getComponent(formGroupId, 'logDir');
        logDirCom.setValue('stdout.log');
        logDirCom.setBehavior('READONLY');
      }
    });
  };

  const getKafkaList = async () => {
    setKafkaListLoading(true);
    const { kafkaInstanceList } = await services.DescribeKafkaInstance({
      params: {
        VpcId: vpcId,
      },
    });
    setKafkaListLoading(false);

    const kafkaListData = _.map(kafkaInstanceList, (item) => {
      return {
        ...item,
        key: item.InstanceId,
        value: item.InstanceId,
      };
    });
    setKafkaList(kafkaListData);
  };

  const getTopicList = async () => {
    // setTopicLoading(true);
    const { TopicList } = await services.DescribeKafkaTopic({
      params: {
        InstanceId: kafkaInstanceId,
      },
    });
    // setTopicLoading(false);

    const topicListData = _.map(TopicList, (item) => {
      return {
        key: item,
        value: item,
      };
    });
    setTopicList(topicListData);
  };

  const handleSwitchChange = (val) => {
    setUseKafka(val);
  };

  const onValueChange = (val) => {
    onChange({
      ...value,
      ...val,
    });
  };

  const handleInstanceChange = ({ value, item }) => {
    const { InstanceEndPoint } = item;

    _.forEach(kafkaConfigs, (item) => {
      item.kafkaTopic = '';
    });
    setTopicList([]);

    onValueChange({
      kafkaInstanceId: value,
      kafkaEndpoint: InstanceEndPoint,
      kafkaConfigs,
    });
  };

  const onConfigChange = ({ value }) => {
    onValueChange({
      kafkaConfigs: value,
    });
  };

  const onTypeChange = ({ value, formGroupId }) => {
    const logDirCom = tableField.current.getComponent(formGroupId, 'logDir');
    if (value === 'stdout') {
      logDirCom.setValue('stdout.log');
      logDirCom.setBehavior('READONLY');
    } else {
      logDirCom.setValue('');
      logDirCom.setBehavior('NORMAL');
    }
  };

  const errorRender = (val) => {
    return intl('saenext.shared.KafkaSelectorField.KafkaSelector.CompleteKafkaConfiguration');
  };

  const validate = () => {
    return new Promise((resolve, reject) => {
      if (!useKafka) {
        resolve(true);
        return;
      }
      if (!vpcId) {
        resolve(
          intl('saenext.shared.KafkaSelectorField.KafkaSelector.TheApplicationDoesNotSpecify'),
        );
        return;
      }
      formField.current.validate((error, value) => {
        error ? resolve(' ') : resolve(true);
      });
    });
  };

  // const getKafkaOpenStatus = async () => {
  //   const enabled = await getProductOpenStatus('alikafka');
  //   setOpenStatus(enabled);
  // };

  return (
    <>
      {/* <If condition={!openStatus}>
         <Message type="warning" className="mb-s">
           {intl('saenext.shared.KafkaSelectorField.KafkaSelector.YouHaveNotActivatedMessage')}
           <a
             href={`https://common-buy.aliyun.com/?commodityCode=alikafka_post&regionId=${regionId}`}
             target="_blank"
             className="ml-s"
           >
             {intl('saenext.shared.KafkaSelectorField.KafkaSelector.ActivateNow')}
           </a>
           。
           <br />
           {intl('saenext.shared.KafkaSelectorField.KafkaSelector.IfTheActivationIsSuccessful')}
           <RefreshButton
             label={intl('saenext.shared.KafkaSelectorField.KafkaSelector.Refresh')}
             handler={getKafkaOpenStatus}
           />
         </Message>
        </If> */}

      <OpenStatusMessage product="alikafka" />

      <Form {...(fieldLayout as any)}>
        <Form.Item
          label={intl('saenext.shared.KafkaSelectorField.KafkaSelector.EnableKafkaLogService')}
        >
          <Switch disabled={!openStatus} checked={useKafka} onChange={handleSwitchChange} />
        </Form.Item>
      </Form>

      <ComShow if={useKafka}>
        <If condition={!vpcId}>
          <Message type="warning" className="mb-l">
            {intl('saenext.shared.KafkaSelectorField.KafkaSelector.TheApplicationDoesNotSpecify.1')}
          </Message>
        </If>
        <Message
          title={intl('saenext.shared.KafkaSelectorField.KafkaSelector.Precautions')}
          type="warning"
          className="mb-l"
        >
          <div className="text-line">
            {intl('saenext.shared.KafkaSelectorField.KafkaSelector.TheLogSourceStorageDirectory')}
          </div>
          <div className="text-line">
            {intl('saenext.shared.KafkaSelectorField.KafkaSelector.DoNotStoreOtherImportant')}
          </div>
        </Message>
        <FormDeep ref={(c) => (formField.current = c?.getInstance?.() || c)}>
          <SelectField
            {...fieldLayout}
            name="kafkaInstanceId"
            label={intl('saenext.shared.KafkaSelectorField.KafkaSelector.KafkaInstance')}
            showSearch
            placeholder={intl(
              'saenext.shared.KafkaSelectorField.KafkaSelector.SelectAKafkaInstance',
            )}
            dataSource={kafkaList}
            onChange={handleInstanceChange}
            state={kafkaListLoading ? 'loading' : undefined}
            className="full24-width"
            notFoundContent={
              <>
                {intl('saenext.shared.KafkaSelectorField.KafkaSelector.YouDoNotHaveA')}

                <a
                  href={`${CachedData.confLink('feature:kafka:url')}/region/${regionId}/instances`}
                  target="_blank"
                >
                  {intl('saenext.shared.KafkaSelectorField.KafkaSelector.KafkaConsole')}
                </a>
                {intl('saenext.shared.KafkaSelectorField.KafkaSelector.Purchase')}
              </>
            }
            validation={[
              {
                type: 'required',
                message: intl(
                  'saenext.shared.KafkaSelectorField.KafkaSelector.SelectAKafkaInstance',
                ),
              },
              {
                type: 'customValidate',
                param: (value) => {
                  if (vpcId) {
                    return true;
                  } else {
                    return intl(
                      'saenext.shared.KafkaSelectorField.KafkaSelector.TheApplicationDoesNotSpecify.2',
                    );
                  }
                },
              },
            ]}
          />

          <div className="field-item-extra">
            <TextRefreshButton onClick={getKafkaList} />
            <ExternalLink
              className="ml-l"
              label={intl(
                'saenext.shared.KafkaSelectorField.KafkaSelector.CreateANewKafkaInstance',
              )}
              url={`${CachedData.confLink('feature:kafka:url')}/region/${regionId}/instances`}
            />
          </div>
          <TableField
            ref={(c) => (tableField.current = c?.getInstance?.() || c)}
            name="kafkaConfigs"
            label=""
            className="full24-table"
            addButtonText={intl('saenext.shared.KafkaSelectorField.KafkaSelector.Add')}
            layout="TABLER"
            required
            errorRender={errorRender}
            showIndex={false}
            showDeleteConfirm={false}
            actionsColumnWidth={80}
            delButtonText={<Icon type="delete" />}
            onChange={onConfigChange}
            validation={[
              {
                type: 'customValidate',
                param: (value) => {
                  let logDirNameValidate = (value || []).every((item) => {
                    return item?.logDir.endsWith('.log');
                  });
                  if (logDirNameValidate) {
                    let mountPathList = [];
                    (value || []).forEach((item) => {
                      const lastSlashIndex = item?.logDir.lastIndexOf('/');
                      const path = item?.logDir.slice(0, lastSlashIndex);
                      mountPathList.push(path);
                    });
                    if (new Set(mountPathList).size === mountPathList.length) {
                      return true;
                    } else {
                      return intl(
                        'saenext.shared.KafkaSelectorField.KafkaSelector.LogSourcePathDuplicate',
                      );
                    }
                  } else {
                    return intl(
                      'saenext.shared.KafkaSelectorField.KafkaSelector.TheDirectoryWhereTheLog',
                    );
                  }
                },
              },
            ]}
          >
            <SelectField
              name="logType"
              label={intl('saenext.shared.KafkaSelectorField.KafkaSelector.CollectionLogType')}
              required
              dataSource={logTypeList}
              onChange={onTypeChange}
            />

            <TextField
              name="logDir"
              label={intl('saenext.shared.KafkaSelectorField.KafkaSelector.LogSource')}
              required
            />

            <SelectField
              name="kafkaTopic"
              label={intl('saenext.shared.KafkaSelectorField.KafkaSelector.KafkaTopicName')}
              required
              dataSource={topicList}
              // state={topicLoading ? 'loading' : undefined}
            />
          </TableField>
        </FormDeep>
      </ComShow>
    </>
  );
};

export default forwardRef(KafkaSelector);
