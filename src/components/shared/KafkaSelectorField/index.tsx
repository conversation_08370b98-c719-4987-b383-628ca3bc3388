import React, { Component } from 'react';
import KafkaSelector from './KafkaSelector';
import BaseField from '@ali/deep-form-helper';
import { ConfigProvider } from '@ali/deep';

type Props = {
  value?: Object;
  onChange?: Function;
};

class KafkaSelectorFieldClass extends Component<Props> {
  private kafkaRef = React.createRef() as any;
  constructor(props) {
    super(props);
  }

  validate = async () => {
    return await this.kafkaRef.validate();
  }

  render() {

    return (
      <KafkaSelector
        {...this.props}
        ref={(ref) => this.kafkaRef = ref}
      />
    )
  }
}

class KafkaSelectorField extends BaseField {
  props;
  static displayName = 'KafkaSelectorField';

  formatValueIn(value, props) {
    try {
      return JSON.parse(value);
    } catch (e) {
      return value;
    }
  }

  formatValueOut(value) {
    if (!value) {
      return '';
    }
    return JSON.stringify(value);
  }

  getProps() {
    return {
      ...this.props,
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...this.props,
      ...fieldProps,
    };

    return (
      <KafkaSelectorFieldClass
        {...newProps}
      />
    );
  }
}

export default ConfigProvider.config(KafkaSelectorField as any)
