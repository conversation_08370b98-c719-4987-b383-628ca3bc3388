import React from 'react';
import BaseField from '@ali/deep-form-helper';
import LifeCycleConf from './LifeCycleConf';
import { ConfigProvider } from '@ali/deep';
import _ from 'lodash';
import { jsonParse } from '~/utils/transfer-data';
import CachedData from '~/cache/common';


/**
 * 使用示例
 *
 *    getValues() {
        console.log('values: ', this.baseForm.field.getValues())
      }

      setValues() {
        this.baseForm.field.setValues({
          LifeCycle: {
            PostStart: '/PostStart',
            PreStop: '/PreStop',
            TerminationGracePeriodSeconds: 240,
          }
        });
      }

 *    <React.Fragment>
        <Form
          ref={(c) => { if (c) { this.baseForm = c.getInstance(); } }}
        >
          <CollapseField
            title='应用生命周期管理设置'
            subTitle='生命周期脚本定义，管理应用容器在运行前和关闭前的一些动作，如环境准备、优雅下线等'
            linkText='如何设置应用生命周期'
            linkHref={`${CachedData.confLink('help:sae:configure-lifecycle-management')}?spm=5176.12834076.0.dexternal.494e6a68aHfHmN`}
            // collapsed={false}
          >
            <LifeCycleField
              name="LifeCycle"
            />
          </CollapseField>
        </Form>
      </React.Fragment>

 */


class LifeCycleField extends BaseField {
  props;
  static displayName = 'LifeCycleField';

  formatValueIn(value) {
    if (!(value && typeof value === 'object')) {
      return value;
    }

    const {
      PostStart = '',
      PreStop = '',
    } = value;

    const postStartObj = jsonParse(PostStart);
    const postStartArr = _.get(postStartObj, 'exec.command', []);

    const preStopObj = jsonParse(PreStop);
    const preStopArr = _.get(preStopObj, 'exec.command', []);

    const result = {
      ...value,
      PostStart: postStartArr.join('\n') || PostStart,
      PreStop: preStopArr.join('\n') || PreStop,
    }

    return result;
  }

  formatValueOut(value) {
    if (!(value && typeof value === 'object')) {
      return value;
    }

    const {
      PostStart = '',
      PreStop = '',
    } = value;

    const postStartFormat = PostStart ? JSON.stringify({
      exec: {
        command: _.split(PostStart, /(?=\r|\n)\r?\n?/g),
      },
    }) : '';

    const preStopFormat = PreStop ? JSON.stringify({
      exec: {
        command: _.split(PreStop, /(?=\r|\n)\r?\n?/g),
      },
    }) : '';

    const result = {
      ...value,
      PostStart: postStartFormat,
      PreStop: preStopFormat,
    };

    return result;
  }

  getProps() {
    return {
      ...this.props
    };
  }


  renderControl(fieldProps) {
    const newProps = {
      ...fieldProps,
      ...this.props,
    };

    return <LifeCycleConf {...newProps} />;
  }
}


export default ConfigProvider.config(LifeCycleField as any)
