import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Tab, Message, NumberPicker, Balloon, Icon } from '@ali/cnd';
import { ShellEditor } from '@ali/cnd';

type Props = {
  injectField: any;
  value: {
    PostStart: string;
    PreStop: string;
    TerminationGracePeriodSeconds: string;
  };
  onChange: (o) => void;
  command?: string;
};

const LifeCycleConf = (props: Props) => {
  const { value = { PostStart: '', PreStop: '', TerminationGracePeriodSeconds: 30 }, onChange, command } =
    props;
  const [activeKey, setActiveKey] = useState('PostStart');
  const [startWarningVisible, setStartWarningVisible] = useState(false);
  const [stopWarningVisible, setStopWarningVisible] = useState(false);

  const placeholder = (type) => {
    return `# ${command}\n# -c\n# echo "Hello from the ${type} handler"`
  }

  const handleTabChange = (key) => {
    setActiveKey(key);
  };

  const handlePostStartCommandChange = (val) => {
    let _startWarningVisible = false;
    if (val.length > 1024) {
      _startWarningVisible = true;
    } else {
      _startWarningVisible = false;
    }
    setStartWarningVisible(_startWarningVisible);
    onChange &&
      onChange({
        ...value,
        PostStart: val,
      });
  };

  const handlePreStopCommandChange = (val) => {
    let _stopWarningVisible = false;
    if (val.length > 1024) {
      _stopWarningVisible = true;
    } else {
      _stopWarningVisible = false;
    }
    setStopWarningVisible(_stopWarningVisible);
    onChange &&
      onChange({
        ...value,
        PreStop: val,
      });
  };

  return (
    <Tab activeKey={activeKey} onChange={handleTabChange}>
      <Tab.Item
        key="PostStart"
        title={intl('saenext.shared.LifeCycleField.LifeCycleConf.AfterStartupPoststartSettings')}
      >
        <div className="mt-l">
          <p style={{ color: '#999' }}>
            {intl('saenext.shared.LifeCycleField.LifeCycleConf.AfterTheApplicationContainerIs')}
          </p>
          <p>{intl('saenext.shared.LifeCycleField.LifeCycleConf.TheFirstBehaviorCommandThe')}</p>
          <div>
            <ShellEditor value={value?.PostStart || placeholder('PostStart')} onChange={handlePostStartCommandChange} />

            <Message
              type="warning"
              shape="inline"
              visible={startWarningVisible}
              style={{ marginTop: 10, width: 360 }}
            >
              {intl(
                'saenext.shared.LifeCycleField.LifeCycleConf.PoststartCannotExceedCharactersIn',
              )}
            </Message>
          </div>
        </div>
      </Tab.Item>
      <Tab.Item
        key="PreStop"
        title={intl('saenext.shared.LifeCycleField.LifeCycleConf.StopPreProcessingPrestopSettings')}
      >
        <div className="mt-l">
          <p style={{ color: '#999' }}>
            {intl('saenext.shared.LifeCycleField.LifeCycleConf.TasksBeforeTheApplicationContainer')}
          </p>
          <p>{intl('saenext.shared.LifeCycleField.LifeCycleConf.TheFirstBehaviorCommandThe')}</p>
          <div>
            <ShellEditor value={value?.PreStop || placeholder('PreStop')} onChange={handlePreStopCommandChange} />

            <Message
              type="warning"
              shape="inline"
              visible={stopWarningVisible}
              style={{ marginTop: 10, width: 360 }}
            >
              {intl('saenext.shared.LifeCycleField.LifeCycleConf.PrestopCannotExceedCharactersIn')}
            </Message>
          </div>
        </div>
      </Tab.Item>
      <Tab.Item
        key="TerminationGracePeriodSeconds"
        title={intl(
          'saenext.shared.LifeCycleField.LifeCycleConf.ElegantOfflineTimeoutSettingTerminationgraceperiodseconds',
        )}
      >
        <div className="mt-l">
          <p style={{ color: '#999' }}>
            {intl('saenext.shared.LifeCycleField.LifeCycleConf.SetTheTimeoutPeriodFor')}
          </p>
          <div className="flex mt-l">
            <div style={{ color: '#111', paddingRight: 32 }}>
              <span>
                {intl('saenext.shared.LifeCycleField.LifeCycleConf.ElegantOfflineTimeout')}
              </span>
              <Balloon
                align="t"
                trigger={
                  <Icon
                    size="xs"
                    style={{ marginLeft: 4, lineHeight: '12px', color: '#C1C1C1', fontSize: 12 }}
                    type="help"
                  />
                }
                closable={false}
              >
                {intl('saenext.shared.LifeCycleField.LifeCycleConf.ThatIsHowLongDoes')}
              </Balloon>
            </div>
            <div>
              <NumberPicker
                style={{ width: 200 }}
                min={0}
                max={600}
                value={value?.TerminationGracePeriodSeconds}
                onChange={(val) => {
                  onChange &&
                    onChange({
                      ...value,
                      TerminationGracePeriodSeconds: val,
                    });
                }}
              />{' '}
              {/* (最大可设{maxPreStopTime}秒) */}
              {intl('saenext.shared.LifeCycleField.LifeCycleConf.MaximumKesheSeconds')}
            </div>
          </div>
        </div>
      </Tab.Item>
    </Tab>
  );
};

export default LifeCycleConf;
