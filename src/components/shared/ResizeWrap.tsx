import React, { useEffect, useRef, useState } from 'react';

const ResizeWrap = (props) => {
  const {
    children
  } = props;

  const [show, setShow] = useState(false);

  const ref = useRef(null);

  useEffect(() => {
    if (ref.current) {
      const observer = new ResizeObserver(entries => {
        for (let entry of entries) {
          setShow(entry.target.offsetHeight > 0);
        }
      });

      observer.observe(ref.current);

      return () => {
        observer.unobserve(ref.current!);
      };
    }
  }, []);

  return (
    <div id={'resize-wrap'} ref={ref} key={`resize-wrap-${show}`}>
      {children}
    </div>
  )
}

export default ResizeWrap;
