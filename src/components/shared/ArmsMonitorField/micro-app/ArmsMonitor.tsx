import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Form, Field, Switch, Message } from '@ali/cnd';
import { isEmpty } from 'lodash';
import CachedData from '~/cache/common';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';

// const RadioGroup = Radio.Group;
const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};

type Props = {
  value: any;
  onChange: (value: any) => void;
};
type State = {
  armsEnabled: boolean;
};

class ArmsMonitor extends Component<Props, State> {
  field = new Field(this, {
    onChange: (value) => {
      this.changeArmsConfig();
    },
  });
  constructor(props) {
    super(props);
    const { value } = props;
    this.state = {
      armsEnabled: false,
    };

    // 初始化赋值
    if (isEmpty(value)) this.initStates();
  }

  componentDidUpdate(prevProps, prevState) {
    const { value } = this.props;
    if (prevProps.value !== value) {
      // 重新赋值
      this.updateStates();
    }
  }

  initStates() {
    const { setValue } = this.field;
    const { onChange } = this.props;
    setValue('enableNewArms', false);
    onChange({
      enableNewArms: false,
    });
  }

  updateStates() {
    const { value } = this.props;
    const { enableNewArms } = value;
    const { setValue } = this.field;
    setValue('enableNewArms', enableNewArms);
    this.setState({
      armsEnabled: enableNewArms,
    });
  }

  changeArmsConfig() {
    const { getValues } = this.field;
    const { onChange } = this.props;

    const params = getValues(['enableNewArms']) as any;
    if (params.enableNewArms) {
      onChange({ ...params });
      return;
    }
    onChange({ enableNewArms: false });
  }

  onArmsEnabledChanged(value) {
    this.setState({ armsEnabled: value });
  }

  render() {
    const { armsEnabled } = this.state;
    const { init } = this.field;
    return (
      <>
        <OpenStatusMessage product="arms" />
        <Message type="warning" className="mb-l">
          {intl('saenext.ArmsMonitorField.micro-app.ArmsMonitor.IfYouChooseToEnable')}
        </Message>

        <Form field={this.field} {...(fieldLayout as any)}>
          <Form.Item
            label={intl('saenext.ArmsMonitorField.micro-app.ArmsMonitor.ApplicationMonitoring')}
            help={intl('saenext.ArmsMonitorField.micro-app.ArmsMonitor.WhenEnabledSaeCanProvide')}
          >
            <Switch
              name="enableNewArms"
              checked={armsEnabled}
              {...init('enableNewArms', {
                initValue: armsEnabled,
                props: {
                  onChange: (value) => {
                    this.onArmsEnabledChanged(value);
                  },
                },
              })}
              disabled={!CachedData.getOpenStatus('armsStatus')}
            />
          </Form.Item>
        </Form>
      </>
    );
  }
}

export default ArmsMonitor;
