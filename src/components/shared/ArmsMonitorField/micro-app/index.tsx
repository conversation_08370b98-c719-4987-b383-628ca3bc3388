import React from 'react';
import ArmsMonitor from './ArmsMonitor';
import BaseField from '@ali/deep-form-helper';

class ArmsMonitorField extends BaseField {
  props;
  static displayName = 'ArmsMonitorField';


  static propTypes = {
    ...BaseField.propTypes,
  };

  static defaultProps = {
    ...BaseField.defaultProps,
  };

  getProps() {
    return {
      ...this.props
    };
  }

  renderControl(fieldProps) {

    const newProps = {
      ...fieldProps,
      ...this.props,
    };

    return <ArmsMonitor {...newProps} />;
  }
}

export default ArmsMonitorField;