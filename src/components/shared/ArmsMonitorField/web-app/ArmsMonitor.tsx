import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Form, Field, Switch, Radio, Message } from '@ali/cnd';
import { isEmpty } from 'lodash';
import CachedData from '~/cache/common';
import { DEPLOY_TYPE } from '~/constants/application';
import OpenStatusMessage from '~/components/shared/OpenStatusMessage';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18, align: 'center' },
  labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
};
type Props = {
  value: any;
  onChange: (value: any) => void;
  deployType?: string;
};
type State = {
  armsEnabled: boolean;
  programmingLanguage: string;
};

const RadioGroup = Radio.Group;

class ArmsMonitor extends Component<Props, State> {
  field = new Field(this, {
    onChange: (value) => {
      this.changeArmsConfig();
    },
  });
  constructor(props) {
    super(props);
    const { value } = props;
    this.state = {
      armsEnabled: false,
      programmingLanguage: 'java',
    };

    // 初始化赋值
    if (isEmpty(value)) this.initStates();
  }

  componentDidUpdate(prevProps, prevState) {
    const { value } = this.props;
    if (prevProps.value !== value) {
      // 重新赋值
      this.updateStates();
    }
  }

  initStates() {
    const { setValue } = this.field;
    const { onChange } = this.props;
    setValue('enableAppMetric', false);
    setValue('programmingLanguage', 'java');
    onChange({
      enableAppMetric: false,
    });
  }

  updateStates() {
    const { value } = this.props;
    const { enableAppMetric, programmingLanguage } = value;
    const { setValue } = this.field;
    setValue('enableAppMetric', enableAppMetric);
    setValue('programmingLanguage', programmingLanguage || 'java');
    this.setState({
      armsEnabled: enableAppMetric,
      programmingLanguage,
    });
  }

  changeArmsConfig() {
    const { getValues } = this.field;
    const { onChange } = this.props;

    const params = getValues(['enableAppMetric', 'programmingLanguage']) as any;
    if (params.enableAppMetric) {
      onChange({ ...params });
      return;
    }
    onChange({ enableAppMetric: false });
  }

  onArmsEnabledChanged(value) {
    this.setState({ armsEnabled: value });
    if (value) {
      this.field.setValue('programmingLanguage', 'java');
    }
  }

  onLanguageChanged(value) {
    this.setState({ programmingLanguage: value });
    const { onChange } = this.props;
    onChange({ ...this.props.value, programmingLanguage: value });
  }

  render() {
    const { deployType = DEPLOY_TYPE.IMAGE } = this.props;
    const { armsEnabled, programmingLanguage } = this.state;
    const { init } = this.field;
    return (
      <>
        <OpenStatusMessage product="arms" />
        <Message type="notice" className="mb-l">
          {intl(
            'saenext.ArmsMonitorField.web-app.ArmsMonitor.SaeAutomaticallyIntegratesArmsApplication',
          )}
        </Message>
        <Form field={this.field} {...(fieldLayout as any)}>
          <Form.Item
            label={intl('saenext.shared.ArmsMonitorField.ArmsMonitor.ApplicationMonitoring')}
            help={intl('saenext.shared.ArmsMonitorField.ArmsMonitor.WhenEnabledSaeCanProvide')}
          >
            <Switch
              name="enableAppMetric"
              checked={armsEnabled}
              {...init('enableAppMetric', {
                initValue: armsEnabled,
                props: {
                  onChange: (value) => {
                    this.onArmsEnabledChanged(value);
                  },
                },
              })}
              disabled={!CachedData.getOpenStatus('armsStatus')}
            />
          </Form.Item>
          {deployType === DEPLOY_TYPE.IMAGE && armsEnabled ? (
            <Form.Item
              label={intl('saenext.ArmsMonitorField.web-app.ArmsMonitor.LanguageType')}
              help={
                programmingLanguage === 'golang' ? (
                  <>
                    <span>
                      {intl(
                        'saenext.ArmsMonitorField.web-app.ArmsMonitor.SelectGoLanguageApplicationMonitoring',
                      )}
                    </span>
                    <a
                      className="ml-s"
                      href={CachedData.confLink('help:sae:installing-probes-for-golang-applications')}
                      target="_blank"
                    >
                      {intl('saenext.ArmsMonitorField.web-app.ArmsMonitor.OperationDocument')}
                    </a>
                  </>
                ) : null
              }
            >
              <RadioGroup
                {...init('programmingLanguage', {
                  initValue: 'java',
                  props: {
                    onChange: (value) => {
                      this.onLanguageChanged(value);
                    },
                  },
                })}
              >
                <Radio key="java" value="java">
                  Java
                </Radio>
                <Radio key="golang" value="golang">
                  Go
                </Radio>
              </RadioGroup>
            </Form.Item>
          ) : null}
        </Form>
      </>
    );
  }
}

export default ArmsMonitor;
