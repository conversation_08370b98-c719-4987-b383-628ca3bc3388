import { intl } from '@ali/cnd';
import React, { useState, useEffect, useContext, useMemo } from 'react';
import { Message } from '@ali/cnd';
import services from '~/services';
import { createAlfaApp } from '@alicloud/alfa-react';
import { get, isEmpty } from 'lodash';
import CachedData from '~/cache/common';
import useInstances from '~/hooks/useInstances';

type Props = {
  appId: string;
  regionId: string;
};

const CloudMonitor = createAlfaApp({
  name: '@ali/alfa-cloud-cms-widget-home',
});

const BaseMonitor = (props: Props) => {
  const { appId, regionId } = props;
  const [prometheusUrl, setPrometheusUrl] = useState('');
  const { existSidecarContainer } = useInstances({ appId });

  useEffect(() => {
    if (!isEmpty(appId) && !isEmpty(regionId)) {
      getPrometheusUrl();
    }
  }, [appId, regionId]);

  const getPrometheusUrl = async () => {
    const res = await services.getArmsDashboardUrl({
      params: { RegionId: regionId },
      customErrorHandle: (err, data, callback) => {
        callback && callback();
      },
    });
    const _prometheusUrl = get(res, 'Data', '');
    setPrometheusUrl(_prometheusUrl);
  };

  return (
    <>
      {isEmpty(prometheusUrl) ? null : (
        <Message type="notice" style={{ marginBottom: -8 }}>
          <div className="text-line">
            {intl('saenext.app-monitor.micro-app.SaeSupportsMonitoringMetricsSuch')}
          </div>
          <div className="text-line">
            <span className="mr-xs">
              {intl('saenext.app-monitor.micro-app.AddedSupportForPrometheusLarge')}
            </span>
            <a href={`${prometheusUrl}&var-appId=${appId}&var-instanceId=All`} target="_blank">
              {intl('saenext.app-monitor.micro-app.ViewDetails')}
            </a>
          </div>
        </Message>
      )}
      {CachedData.isOdinView() && (
        <Message type="warning" className="mt mb">
          {intl('saenext.app-monitor.micro-app.LogOnToTheBasic')}
          <a
            href="http://cms.alibaba-inc.com/productDetail.html?product=serverless&keyword=instance"
            target="_blank"
          >
            {intl('saenext.app-monitor.micro-app.CloudMonitoringPlatform')}
          </a>
          {intl('saenext.app-monitor.micro-app.View')}
        </Message>
      )}
      <CloudMonitor
        category="serverless"
        dimensions={[{ appId: `${appId}` }]}
        regionId={regionId}
        hideGroupTab={!existSidecarContainer}
        isSortResource
        className="alfa-cloud-cms-widget-home"
      />
    </>
  );
};

export default React.memo(BaseMonitor);
