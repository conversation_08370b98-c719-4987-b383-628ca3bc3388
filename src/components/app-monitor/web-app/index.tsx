/* eslint react/prop-types: 0 */
import React, { useState, useRef } from 'react';
import { Button, Icon } from '@ali/cnd';
import MonitorTimeRange, { getTimes } from '~/components/shared/TimeRangeMonitor';
import APPMetrics from './AppMonitor';

type Props = {
  appData: AppItem,
  applicationID: string,
};

export default (props: Props) => {
  const { appData, applicationID } = props;
  const timeInitValue = 'last_half_hour';
  const initPeriod = 60;
  const [start, end] = getTimes(timeInitValue);
  const [timeTuple, setTimeTuple] = useState({
    start,
    end,
    key: timeInitValue,
    period: initPeriod,
  });
  const [selectedTimeKey, updateSelectedTimeKey] = useState(timeInitValue);
  const child = useRef(null);
  


  const onMonitorTimeChange = value => {
    const { start, end, key, period } = value;
    updateSelectedTimeKey(key);
    setTimeTuple({ start, end, key, period });
  };

  const refreshChart = () => {
    const [start, end] = getTimes(selectedTimeKey);

    let _start = start;
    let _end = end;
    if (selectedTimeKey === 'custom') {
      // 当为自定义时间时 timeTuple 值不变不会引起刷新 
      _start = timeTuple.start;
      _end = timeTuple.end + 1000;
    }
    child?.current.onChild([_start, _end]);
    setTimeTuple({
      start: _start,
      end: _end,
      key: selectedTimeKey,
      period: timeTuple.period,
    });
  };

  return (
    <>
      <div className='flex justify-between'>
        {/* <div className='flex'>
          
          <Message
            type="notice"
            size="medium"
            className='flex ml-s'
            style={{ height: 32 }}
          >
            监控数据采集稍有延迟，请稍后刷新查看最新数据
          </Message>
        </div> */}
        <MonitorTimeRange
          onRef={child}
          defaultTime={false}
          timeInitValue={timeInitValue}
          onTimeChanged={onMonitorTimeChange}
        />
        <Button
          type="normal"
          className='ml-s'
          onClick={refreshChart}
        >
          <Icon type="refresh" style={{ color: '#666' }} />
        </Button>
      </div>
      <APPMetrics
        appData={appData}
        timeTuple={timeTuple}
        applicationID={applicationID}
      />
    </>
  );
};
