import { intl } from '@ali/cnd';
import React, { useEffect, useState, useRef } from 'react';
import '@alife/aisc-widgets/build/index.css';
import { Loading, Grid, Radio, Select } from '@ali/cnd';
import { getParams, unshiftZero } from '~/utils/global';
import services from '~/services';
import { map, filter, orderBy } from 'lodash';
import { Mode, IMetricsProps } from '../../monitor-indicator/Constant';
import CombinationIndicator from '../../monitor-indicator/CombinationIndicator';
import LatencyIndicator from '../../monitor-indicator/LatencyIndicator';
import CpuUtilizationIndicator from '../../monitor-indicator/CpuUtilizationIndicator';
import MemUtilizationIndicator from '../../monitor-indicator/MemUtilizationIndicator';
import SystemLoadIndicator from '../../monitor-indicator/SystemLoadIndicator';
import DiskUtilizationIndicator from '../../monitor-indicator/DiskUtilizationIndicator';
import NetworkPacketIndicator from '../../monitor-indicator/NetworkPacketIndicator';
import HttpStatusIndicator from '../../monitor-indicator/HttpStatusIndicator';
import RequestCountIndicator from '../../monitor-indicator/RequestCountIndicator';
import InstanceIndicator from '../../monitor-indicator/InstanceIndicator';
import StartupLatencyIndicator from '../../monitor-indicator/StartupLatencyIndicator';
import { AES_CONSTANT, trackOpt } from '~/tracker';

const { Row, Col } = Grid;
const RadioGroup = Radio.Group;
type Props = {
  appData: AppItem;
  applicationID: string;
  timeTuple: {
    start: number;
    end: number;
    key: string;
    period: number;
  };
};

export default (props: Props) => {
  const { appData, applicationID, timeTuple } = props;

  const applicationName = getParams('name');
  const [isLoading, setIsLoading] = useState(false);
  const [appVersions, setAppVersions] = useState([]);
  const [data, setData] = useState<{ [key: string]: any[] }>();
  const [chartMode, setChartMode] = useState(Mode.one);
  const [isCompare, setIsCompare] = useState(false);
  const compareRef = useRef({ v1: '', v2: '' });
  const [compareVersions, setCompareVersions] = useState({ v1: '', v2: '' });
  const cacheData = useRef(null);
  const [dataSource, setDataSource] = useState({
    applicationInstances: [],
    applicationRequests: [],
    applicationCPUUsage: [],
  });

  useEffect(() => {
    getApplicationVersions();
    trackOpt({
      behavior: AES_CONSTANT.OPT_BEHAVIOR_TYPE.MONITOR,
      stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
    });
  }, []);

  useEffect(() => {
    // 这个地方要考虑刷新时 是不是对比状态
    if (isCompare && chartMode === Mode.compare) {
      getAppCompareMetrics();
    } else {
      getAppOneMetrics();
    }
  }, [JSON.stringify(timeTuple)]);

  const getApplicationVersions = async () => {
    const data = await services.getListAppVersions({
      applicationID,
      applicationName,
      limit: 100,
    });
    const { versions = [] } = data;
    // 过滤无流量版本
    const activates = orderBy(
      filter(versions, (version) => version.weight),
      ['weight'],
      ['desc'],
    );
    const _activates =
      activates &&
      map(activates, (version) => {
        const { versionId, weight } = version;
        const title = unshiftZero(versionId);
        let _weight = '';
        if (weight) {
          const _value = weight.toFixed(2);
          const [integer, decimals] = _value.split('.');
          if (parseInt(integer, 10)) {
            _weight = '100';
          } else {
            _weight = parseInt(decimals, 10).toFixed();
          }
        }
        return {
          name: title,
          value: versionId,
          weight: `${_weight}%`,
          label: (
            <div className="flex justify-between">
              <span>{title}</span>
              <span style={{ color: '#888' }}>{_weight}%</span>
            </div>
          ),
        };
      });
    setAppVersions(_activates);
  };

  const getAppOneMetrics = async () => {
    setIsLoading(true);
    const res = await theIndicatorPromise([
      // 趋势图 CPU使用量
      'ApplicationCPUUsage',
      // 请求次数
      'ApplicationRequestsV2',
      // 请求响应时间
      'ApplicationRequestsLatencyV2',
      // HTTP 状态码
      'ApplicationHTTPStatus2xxV2',
      'ApplicationHTTPStatus3xxV2',
      'ApplicationHTTPStatus4xxV2',
      'ApplicationHTTPStatus5xxV2',
      // 实例数
      'ApplicationInstances',
      'ApplicationInstancesActive',
      'ApplicationInstancesIdle',
      // CPU使用率
      'ApplicationCPUUtilizations',
      // 内存使用率
      'ApplicationMemoryUtilizations',
      // 系统平均负载
      'ApplicationLoad1m',
      // 磁盘使用率
      'ApplicationDiskUsageUtilizations',
      // 网络收&发数据包
      'ApplicationNetworkRXBytes',
      'ApplicationNetworkTXBytes',
      // 实例创建耗时(含健康检查)
      'ApplicationStartupLatency',
    ]);
    const { metrics = {} } = res;
    const {
      ApplicationCPUUsage = [] as IMetricsProps[],
      ApplicationRequestsV2 = [] as IMetricsProps[],
      ApplicationRequestsLatencyV2 = [] as IMetricsProps[],
      ApplicationHTTPStatus2xxV2 = [] as IMetricsProps[],
      ApplicationHTTPStatus3xxV2 = [] as IMetricsProps[],
      ApplicationHTTPStatus4xxV2 = [] as IMetricsProps[],
      ApplicationHTTPStatus5xxV2 = [] as IMetricsProps[],
      ApplicationInstances = [] as IMetricsProps[],
      ApplicationInstancesActive = [] as IMetricsProps[],
      ApplicationInstancesIdle = [] as IMetricsProps[],
      ApplicationCPUUtilizations = [] as IMetricsProps[],
      ApplicationMemoryUtilizations = [] as IMetricsProps[],
      ApplicationLoad1m = [] as IMetricsProps[],
      ApplicationDiskUsageUtilizations = [] as IMetricsProps[],
      ApplicationNetworkRXBytes = [] as IMetricsProps[],
      ApplicationNetworkTXBytes = [] as IMetricsProps[],
      ApplicationStartupLatency = [] as IMetricsProps[],
    } = metrics;
    const _data = {};
    Object.assign(_data, {
      cpuUsage: ApplicationCPUUsage,
      requests: ApplicationRequestsV2,
      latency: ApplicationRequestsLatencyV2,
      httpStatus: [
        ApplicationHTTPStatus2xxV2,
        ApplicationHTTPStatus3xxV2,
        ApplicationHTTPStatus4xxV2,
        ApplicationHTTPStatus5xxV2,
      ],

      instances: [ApplicationInstances, ApplicationInstancesActive, ApplicationInstancesIdle],

      cpuUtilizations: ApplicationCPUUtilizations,
      memUtilizations: ApplicationMemoryUtilizations,
      systemLoads: ApplicationLoad1m,
      diskUtilizations: ApplicationDiskUsageUtilizations,
      networkPacket: [ApplicationNetworkRXBytes, ApplicationNetworkTXBytes],

      startupLatency: ApplicationStartupLatency,
    });
    setData(_data);
    setDataSource({
      applicationInstances: ApplicationInstances,
      applicationRequests: ApplicationRequestsV2,
      applicationCPUUsage: ApplicationCPUUsage,
    });
    cacheData.current = {
      ...cacheData.current,
      [Mode.one]: _data,
    };
    setIsLoading(false);
  };

  const getAppCompareMetrics = async () => {
    if (!compareRef.current?.v1 || !compareRef.current?.v2) return;
    setIsLoading(true);
    const versions = [compareRef.current?.v1, compareRef.current?.v2];

    const _compareData = {};
    for await (let versionId of versions) {
      const res = await theIndicatorPromise(
        [
          'ApplicationVersionRequestsV2',
          'ApplicationVersionRequestsLatencyV2',
          'ApplicationVersionHTTPStatus2xxV2',
          'ApplicationVersionHTTPStatus3xxV2',
          'ApplicationVersionHTTPStatus4xxV2',
          'ApplicationVersionHTTPStatus5xxV2',
          'ApplicationVersionInstances',
          'ApplicationVersionInstancesActive',
          'ApplicationVersionInstancesIdle',
          'ApplicationVersionCPUUtilizations',
          'ApplicationVersionMemoryUtilizations',
          'ApplicationVersionLoad1m',
          'ApplicationVersionDiskUsageUtilizations',
          'ApplicationVersionNetworkRXBytes',
          'ApplicationVersionNetworkTXBytes',
          'ApplicationVersionStartupLatency',
        ],
        versionId,
      );
      const { metrics = {} } = res;
      const {
        ApplicationVersionRequestsV2 = [] as IMetricsProps[],
        ApplicationVersionRequestsLatencyV2 = [] as IMetricsProps[],
        ApplicationVersionHTTPStatus2xxV2 = [] as IMetricsProps[],
        ApplicationVersionHTTPStatus3xxV2 = [] as IMetricsProps[],
        ApplicationVersionHTTPStatus4xxV2 = [] as IMetricsProps[],
        ApplicationVersionHTTPStatus5xxV2 = [] as IMetricsProps[],
        ApplicationVersionInstances = [] as IMetricsProps[],
        ApplicationVersionInstancesActive = [] as IMetricsProps[],
        ApplicationVersionInstancesIdle = [] as IMetricsProps[],
        ApplicationVersionCPUUtilizations = [] as IMetricsProps[],
        ApplicationVersionMemoryUtilizations = [] as IMetricsProps[],
        ApplicationVersionLoad1m = [] as IMetricsProps[],
        ApplicationVersionDiskUsageUtilizations = [] as IMetricsProps[],
        ApplicationVersionNetworkRXBytes = [] as IMetricsProps[],
        ApplicationVersionNetworkTXBytes = [] as IMetricsProps[],
        ApplicationVersionStartupLatency = [] as IMetricsProps[],
      } = metrics;
      Reflect.set(_compareData, versionId, {
        ApplicationVersionRequestsV2,
        ApplicationVersionRequestsLatencyV2,
        ApplicationVersionHTTPStatus2xxV2,
        ApplicationVersionHTTPStatus3xxV2,
        ApplicationVersionHTTPStatus4xxV2,
        ApplicationVersionHTTPStatus5xxV2,
        ApplicationVersionInstances,
        ApplicationVersionInstancesActive,
        ApplicationVersionInstancesIdle,
        ApplicationVersionCPUUtilizations,
        ApplicationVersionMemoryUtilizations,
        ApplicationVersionLoad1m,
        ApplicationVersionDiskUsageUtilizations,
        ApplicationVersionNetworkRXBytes,
        ApplicationVersionNetworkTXBytes,
        ApplicationVersionStartupLatency,
      });
    }
    const [v1, v2] = versions;
    const v1name = unshiftZero(v1);
    const v2name = unshiftZero(v2);
    const _data = {};
    Object.assign(_data, {
      requests: {
        [v1name]: _compareData[v1]?.ApplicationVersionRequestsV2,
        [v2name]: _compareData[v2]?.ApplicationVersionRequestsV2,
      },
      latency: {
        [v1name]: _compareData[v1]?.ApplicationVersionRequestsLatencyV2,
        [v2name]: _compareData[v2]?.ApplicationVersionRequestsLatencyV2,
      },
      httpStatus: {
        [v1name]: [
          _compareData[v1]?.ApplicationVersionHTTPStatus2xxV2,
          _compareData[v1]?.ApplicationVersionHTTPStatus3xxV2,
          _compareData[v1]?.ApplicationVersionHTTPStatus4xxV2,
          _compareData[v1]?.ApplicationVersionHTTPStatus5xxV2,
        ],

        [v2name]: [
          _compareData[v2]?.ApplicationVersionHTTPStatus2xxV2,
          _compareData[v2]?.ApplicationVersionHTTPStatus3xxV2,
          _compareData[v2]?.ApplicationVersionHTTPStatus4xxV2,
          _compareData[v2]?.ApplicationVersionHTTPStatus5xxV2,
        ],
      },
      instances: {
        [v1name]: [
          _compareData[v1]?.ApplicationVersionInstances,
          _compareData[v1]?.ApplicationVersionInstancesActive,
          _compareData[v1]?.ApplicationVersionInstancesIdle,
        ],

        [v2name]: [
          _compareData[v2]?.ApplicationVersionInstances,
          _compareData[v2]?.ApplicationVersionInstancesActive,
          _compareData[v2]?.ApplicationVersionInstancesIdle,
        ],
      },
      cpuUtilizations: {
        [v1name]: _compareData[v1]?.ApplicationVersionCPUUtilizations,
        [v2name]: _compareData[v2]?.ApplicationVersionCPUUtilizations,
      },
      memUtilizations: {
        [v1name]: _compareData[v1]?.ApplicationVersionMemoryUtilizations,
        [v2name]: _compareData[v2]?.ApplicationVersionMemoryUtilizations,
      },
      systemLoads: {
        [v1name]: _compareData[v1]?.ApplicationVersionLoad1m,
        [v2name]: _compareData[v2]?.ApplicationVersionLoad1m,
      },
      diskUtilizations: {
        [v1name]: _compareData[v1]?.ApplicationVersionDiskUsageUtilizations,
        [v2name]: _compareData[v2]?.ApplicationVersionDiskUsageUtilizations,
      },
      networkPacket: {
        [v1name]: [
          _compareData[v1]?.ApplicationVersionNetworkRXBytes,
          _compareData[v1]?.ApplicationVersionNetworkTXBytes,
        ],

        [v2name]: [
          _compareData[v2]?.ApplicationVersionNetworkRXBytes,
          _compareData[v2]?.ApplicationVersionNetworkTXBytes,
        ],
      },
      startupLatency: {
        [v1name]: _compareData[v1]?.ApplicationVersionStartupLatency,
        [v2name]: _compareData[v2]?.ApplicationVersionStartupLatency,
      },
    });
    setData(_data);
    cacheData.current = {
      ...cacheData.current,
      [Mode.compare]: _data,
    };
    setIsLoading(false);
  };

  const theIndicatorPromise = (indicators: string[], versionId?: string) => {
    const params = {
      startTime: timeTuple.start,
      endTime: timeTuple.end,
      period: timeTuple.period,
      fillZero: true,
    };
    if (versionId) {
      Reflect.set(params, 'versionID', versionId);
    }
    return services.getAppVersionMetrics({
      ...params,
      applicationID,
      metrics: indicators.join(','),
      applicationName: getParams('name'),
    });
  };

  const onCompare = (key, val) => {
    setChartMode(Mode.compare);
    const _compareVersions = { ...compareVersions };
    _compareVersions[key] = val;
    setCompareVersions(_compareVersions);
    compareRef.current = _compareVersions;
    if (!_compareVersions?.v1 || !_compareVersions?.v2) return;
    if (_compareVersions?.v1 === _compareVersions?.v2) return;
    setIsCompare(true);
    getAppCompareMetrics();
  };

  const onChartMode = (v) => {
    setChartMode(v as string);
    if (cacheData?.current) {
      setData(cacheData.current[v]);
    }
  };

  return (
    <Loading visible={isLoading} className="full-width mb-l">
      <CombinationIndicator
        dataSource={dataSource}
        // @ts-ignore
        urlInternet={appData?.urlInternet}
      />

      <div className="layout">
        <RadioGroup value={chartMode} onChange={onChartMode}>
          <Radio
            id="one"
            value={Mode.one}
            label={intl('saenext.app-monitor.web-app.AppMonitor.AllVersions')}
          />
          <Radio id="compare" value={Mode.compare}>
            <div className="compare">
              <span className="mr-s">
                {intl('saenext.app-monitor.web-app.AppMonitor.VersionComparison')}
              </span>
              <Select
                size="small"
                placeholder={intl('saenext.app-monitor.web-app.AppMonitor.VersionV')}
                className="mr-s"
                style={{ width: 140 }}
                dataSource={map(appVersions, (v) => ({
                  ...v,
                  disabled: v.value === compareVersions?.v2,
                }))}
                valueRender={(item) => (
                  <div className="flex justify-between">
                    <span className="mr-xl">{item?.name}</span>
                    <span className="color-text-description">{item?.weight}</span>
                  </div>
                )}
                onChange={(v) => onCompare('v1', v)}
              />

              <Select
                size="small"
                placeholder={intl('saenext.app-monitor.web-app.AppMonitor.VersionV.1')}
                style={{ width: 140 }}
                dataSource={map(appVersions, (v) => ({
                  ...v,
                  disabled: v.value === compareVersions?.v1,
                }))}
                valueRender={(item) => (
                  <div className="flex justify-between">
                    <span className="mr-xl">{item?.name}</span>
                    <span className="color-text-description">{item?.weight}</span>
                  </div>
                )}
                onChange={(v) => onCompare('v2', v)}
              />
            </div>
          </Radio>
        </RadioGroup>
      </div>
      <Row gutter={24} className="mt-s">
        <Col span={8}>
          <RequestCountIndicator data={data?.requests} isCompare={isCompare} />
        </Col>
        <Col span={8}>
          <LatencyIndicator data={data?.latency} isCompare={isCompare} />
        </Col>
        <Col span={8}>
          <HttpStatusIndicator data={data?.httpStatus} isCompare={isCompare} />
        </Col>
      </Row>
      <Row gutter={24} className="mt-xl">
        <Col span={8}>
          <InstanceIndicator data={data?.instances} isCompare={isCompare} />
        </Col>
        <Col span={8}>
          <CpuUtilizationIndicator data={data?.cpuUtilizations} isCompare={isCompare} />
        </Col>
        <Col span={8}>
          <MemUtilizationIndicator data={data?.memUtilizations} isCompare={isCompare} />
        </Col>
      </Row>
      <Row gutter={24} className="mt-xl">
        <Col span={8}>
          <SystemLoadIndicator data={data?.systemLoads} isCompare={isCompare} />
        </Col>
        <Col span={8}>
          <DiskUtilizationIndicator data={data?.diskUtilizations} isCompare={isCompare} />
        </Col>
        <Col span={8}>
          <NetworkPacketIndicator data={data?.networkPacket} isCompare={isCompare} />
        </Col>
      </Row>
      <Row gutter={24} className="mt-xl">
        <Col span={8}>
          <StartupLatencyIndicator data={data?.startupLatency} isCompare={isCompare} />
        </Col>
      </Row>
    </Loading>
  );
};
