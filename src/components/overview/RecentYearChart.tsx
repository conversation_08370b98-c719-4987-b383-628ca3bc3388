import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useRef, useEffect, useContext } from 'react';
import { Select, Tab, Button, Icon, Message, Radio, DatePicker } from '@ali/cnd';
import RecentAppsContent from './RecentAppsContent';
import RecentTasksContent from './RecentTasksContent';
import { REGIONS, SAE_TYPES } from './constants';
import { get, concat, map } from 'lodash';
import services from '~/services';
import moment from 'moment';
import FeatureContext from '~/utils/featureContext';

const RadioGroup = Radio.Group;

type Props = {
  title: string;
  AppVersionOptions: any[];
  enableNewSaeVersion: boolean;
  isMultipleNewAccount: boolean;
  multipleNewAccountStdEnable: boolean;
  diskSizeEnable: boolean;
};

const CHART_TYPES = {
  APPS: 'APPS',
  TASKS: 'TASKS',
};
const TimeoutLabels = {
  'APPS-ALL': intl('saenext.components.overview.RecentYearChart.AllApplicationTypesUsageOverview'),
  'APPS-WEB_REQUEST': intl(
    'saenext.components.overview.RecentYearChart.TheWebApplicationUsageOverview',
  ),
  'APPS-MICRO_SERVICE': intl(
    'saenext.components.overview.RecentYearChart.TheMicroserviceApplicationUsageOverview',
  ),
  TASKS: intl('saenext.components.overview.RecentYearChart.TheTaskUsageOverviewRequest'),
};

const regions = [...REGIONS];
const DEFAULT_ALL = [
  {
    NamespaceId: 'all',
    NamespaceName: intl('saenext.pages.overview.RecentYearChart.AllNamespaces'),
  },
];

const TimeUnitDataSource = [
  { label: intl('saenext.components.overview.RecentYearChart.Days'), value: 'day' },
  { label: intl('saenext.components.overview.RecentYearChart.Month'), value: 'month' },
];

export default (props: Props) => {
  const { title, AppVersionOptions, enableNewSaeVersion, isMultipleNewAccount, multipleNewAccountStdEnable, diskSizeEnable } =
    props;
  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;
  const [regionId, setRegionId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [namespaceId, setNamespaceId] = useState('all');
  const [namespaces, setNamespaces] = useState(DEFAULT_ALL);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [, forceUpdate] = useState(0);
  const [currentChart, setCurrentChart] = useState(CHART_TYPES.APPS);
  const [currentAppType, setCurrentAppType] = useState(SAE_TYPES.ALL);
  const namespaceRef = useRef({});
  const timeoutRef = useRef(new Map());
  const probableTimeoutKey =
    currentChart === CHART_TYPES.TASKS
      ? CHART_TYPES.TASKS
      : `${CHART_TYPES.APPS}-${currentAppType}`;
  const [curQuickTime, setCurQuickTime] = useState('last_1_month');
  const [startTime, setStartTime] = useState(0);
  const [endTime, setEndTime] = useState(0);
  const [timeUnit, setTimeUnit] = useState('day');
  const [curAppVersions, setCurAppVersions] = useState([]);

  useEffect(() => {
    if (curQuickTime) {
      let _startTime = 0;
      const _endTime = moment().endOf('day').valueOf();
      switch (curQuickTime) {
        case 'last_1_month':
          _startTime = moment().subtract(1, 'months').startOf('day').valueOf();
          if (timeUnit !== 'day') {
            setTimeUnit('day');
          }
          break;
        case 'last_6_month':
          _startTime = moment().subtract(6, 'months').startOf('day').valueOf();
          break;
        case 'last_1_year':
          _startTime = moment().subtract(1, 'years').startOf('day').valueOf();
          if (timeUnit !== 'month') {
            setTimeUnit('month');
          }
          break;
      }
      setStartTime(_startTime);
      setEndTime(_endTime);
    }
  }, [curQuickTime]);

  const getNamespaces = async (regionIdParam) => {
    setIsLoading(true);
    if (!regionIdParam) {
      setNamespaces(DEFAULT_ALL);
      setIsLoading(false);
      return;
    }
    if (namespaceRef.current[regionIdParam]) {
      setNamespaces(concat(DEFAULT_ALL, namespaceRef.current[regionIdParam]));
      setIsLoading(false);
      return;
    }
    const res = await services.getNamespaceListV2({
      params: {
        CurrentPage: 1,
        PageSize: 100,
        RegionId: regionIdParam,
      },
      customErrorHandle: (error) => {
        setIsLoading(false);
        // @ts-ignore
        setNamespaceList(DEFAULT_ALL);
        return error;
      },
    });
    const _namespaces = get(res, 'Data.Namespaces');
    namespaceRef.current = _namespaces;
    // 如果请求成功 缓存一下 namespaceList
    Reflect.set(namespaceRef.current, regionIdParam, _namespaces);
    setNamespaces(concat(DEFAULT_ALL, _namespaces));
    setIsLoading(false);
  };

  const timeoutCallback = (timeout) => {
    // {
    //   'APPS-ALL': true,
    //   'APPS-WEB_REQUEST': true,
    //   'APPS-MICRO_SERVICE': true,
    //   'TASKS': true
    // }
    timeoutRef.current = { ...timeoutRef.current, ...timeout };
    forceUpdate((i) => i + 1);
  };

  const appTypeCallback = (type) => {
    setCurrentAppType(type);
    curAppVersions.length > 0 && setCurAppVersions([]);
  };

  return (
    <div className="recent-year w-100">
      <div className="title mb-s">
        <span>{title}</span>
        <Select
          size="small"
          className="ml"
          value={regionId}
          dataSource={regions}
          onChange={(regionIdVal: string) => {
            setRegionId(regionIdVal);
            setNamespaceId('all');
            getNamespaces(regionIdVal);
          }}
        />

        <Select
          size="small"
          className="ml-s"
          value={namespaceId}
          dataSource={map(namespaces, (item) => ({
            label: item.NamespaceName,
            value: item.NamespaceId,
          }))}
          state={isLoading ? 'loading' : null}
          onChange={(namespaceIdVal: string) => {
            setNamespaceId(namespaceIdVal);
          }}
        />

        {(enableNewSaeVersion || isMultipleNewAccount) &&
          currentChart === CHART_TYPES.APPS &&
          (!EnableWebApplication ||
            (EnableWebApplication && currentAppType === SAE_TYPES.MICRO_SERVICE)) && (
            <Select
              size="small"
              className="ml-s select-app-versions"
              mode="tag"
              style={{ minWidth: 160 }}
              placeholder={intl(
                'saenext.components.overview.RecentYearChart.SelectAnApplicationVersion',
              )}
              value={curAppVersions}
              dataSource={
                (enableNewSaeVersion || (isMultipleNewAccount && multipleNewAccountStdEnable))
                  ? AppVersionOptions
                  : AppVersionOptions.filter((item) => item.value !== 'std')
              }
              onChange={(val: []) => {
                setCurAppVersions(val);
              }}
            />
          )}

        <Button
          size="small"
          className="isOnlyIcon ml-s"
          style={{ minWidth: 24 }}
          onClick={() => {
            // 刷新之前要把之前的错误信息清空
            timeoutRef.current = { ...timeoutRef.current, [probableTimeoutKey]: false };
            setRefreshIndex(Date.now());
          }}
        >
          <Icon type={refreshing ? 'loading' : 'refresh'} />
        </Button>
        {get(timeoutRef.current, probableTimeoutKey, false) ? (
          <Message type="error" size="medium" className="timeout-message ml-s">
            {TimeoutLabels[probableTimeoutKey]}
          </Message>
        ) : null}
      </div>
      <div style={{ color: '#888', marginTop: 10 }}>
        {intl('saenext.components.overview.RecentYearChart.NoteHistoricalDataBeforeApril')}
      </div>
      <div className="mt-s mb-s flex justify-start">
        <div className="mr-s">
          <RadioGroup
            shape="button"
            value={curQuickTime}
            onChange={(v: string) => {
              setCurQuickTime(v);
            }}
          >
            <Radio key="last_1_month" value="last_1_month">
              {intl('saenext.components.overview.RecentYearChart.LastMonth')}
            </Radio>
            <Radio key="last_6_month" value="last_6_month">
              {intl('saenext.components.overview.RecentYearChart.InTheLastHalfYear')}
            </Radio>
            <Radio key="last_1_year" value="last_1_year">
              {intl('saenext.components.overview.RecentYearChart.LastYear')}
            </Radio>
          </RadioGroup>
        </div>
        <div className="mr-8 recent-year-range-picker">
          <DatePicker.RangePicker
            label={intl('saenext.components.overview.RecentYearChart.TimeRange')}
            value={[moment(startTime), moment(endTime)]}
            format={'YYYY-MM-DD'}
            hasClear={false}
            onChange={(v) => {
              setCurQuickTime('');
              const _startTime = moment(v[0]).startOf('day').valueOf();
              const _endTime = moment(v[1]).endOf('day').valueOf();
              setStartTime(Number(_startTime));
              setEndTime(Number(_endTime));
            }}
            style={{ width: 360 }}
          />
        </div>
        <div>
          <Select
            label={intl('saenext.components.overview.RecentYearChart.Granularity')}
            dataSource={TimeUnitDataSource}
            value={timeUnit}
            onChange={(v) => {
              setTimeUnit(String(v));
            }}
          />
        </div>
      </div>
      <div className="wrapper w-100">
        <Tab
          shape="pure"
          excessMode="slide"
          activeKey={currentChart}
          onChange={(key) => {
            // @ts-ignore
            setCurrentChart(key);
            curAppVersions.length > 0 && setCurAppVersions([]);
          }}
          unmountInactiveTabs
        >
          <Tab.Item
            title={intl('saenext.pages.overview.RecentYearChart.Application')}
            key={CHART_TYPES.APPS}
          >
            <RecentAppsContent
              regionId={regionId}
              namespaceId={namespaceId}
              refreshIndex={refreshIndex}
              isCurrentChart={currentChart === CHART_TYPES.APPS}
              timeoutCallback={timeoutCallback}
              appTypeCallback={appTypeCallback}
              startTime={startTime}
              endTime={endTime}
              timeUnit={timeUnit}
              appVersions={curAppVersions}
              diskSizeEnable={diskSizeEnable}
            />
          </Tab.Item>
          <Tab.Item
            title={intl('saenext.pages.overview.RecentYearChart.Task')}
            key={CHART_TYPES.TASKS}
          >
            <RecentTasksContent
              regionId={regionId}
              namespaceId={namespaceId}
              refreshIndex={refreshIndex}
              isCurrentChart={currentChart === CHART_TYPES.TASKS}
              timeoutCallback={timeoutCallback}
              startTime={startTime}
              endTime={endTime}
              timeUnit={timeUnit}
            />
          </Tab.Item>
        </Tab>
      </div>
    </div>
  );
};
