/* eslint react/prop-types: 0 */
import React, { useContext } from 'react';
import { Runtime } from './assets';
import FeatureContext from '~/utils/featureContext';
import { ConsoleContext } from '@ali/cnd';

type Props = {
  title: string;
};

const runtimes = [
  {
    language: 'SpringCloud',
    icon: Runtime.springcloud,
    type: 'micro',
  },
  {
    language: 'Dubbo',
    icon: Runtime.dubbo,
    type: 'micro',
  },
  {
    language: 'SpringBoot',
    icon: Runtime.springboot,
    type: 'web',
    imageDemoTag: 'web-springboot-helloworld-v1.0',
  },
  {
    language: 'ThinkPHP',
    icon: Runtime.thinkPHP,
    type: 'web',
    imageDemoTag: 'web-php-tinkphp-v1.0',
  },
  {
    language: 'PHP laravel',
    icon: Runtime.larvavelPHP,
    type: 'web',
    imageDemoTag: 'web-php-laravel-v1.0',
  },
  {
    language: 'Flask',
    icon: Runtime.flask,
    type: 'web',
    imageDemoTag: 'web-python-flask-v1.0',
  },
  {
    language: 'django',
    icon: Runtime.django,
    type: 'web',
    imageDemoTag: 'web-python-django-v1.0',
  },
  {
    language: 'Express.js',
    icon: Runtime.express,
    type: 'web',
    imageDemoTag: 'web-express-helloworld-v1.0',
  },
  {
    language: 'Next.js',
    icon: Runtime.nextjs,
    type: 'web',
  },
  {
    language: 'Gin',
    icon: Runtime.gin,
    type: 'web',
    imageDemoTag: 'web-golang-gin-v1.0',
  },
];

export default (props: Props) => {
  const { title } = props;

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  
  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  const renderHref = (runtime) => {
    if (!EnableWebApplication) {
      return `/${regionId}/create-app/micro`;
    }

    const { type, imageDemoTag } = runtime;
    const search = imageDemoTag ? `?imageDemoTag=${imageDemoTag}` : '';
    return `/${regionId}/create-app/${type}${search}`;
  };

  return (
    <div className="runtime w-100">
      <div className='title mb-s'>{title}</div>
      <div className="wrapper">
        {
          runtimes.map(runtime => (
            <a 
              className="next-runtime" 
              href={renderHref(runtime)}
              target="_blank"
              data-spm-protocol='i'
            >
              <span className='mr'>{runtime.icon}</span>
              <span>{runtime.language}</span>
            </a>
          ))
        }
      </div>
    </div>
  );
};
