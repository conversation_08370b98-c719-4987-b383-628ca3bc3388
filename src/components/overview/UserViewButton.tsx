import { intl } from '@ali/cnd';
import React, { memo } from 'react';
import UserView from './UserView';
import SlideButton from '~/components/shared/SlideButton';

const UserViewButton = () => {
  const ref = React.createRef<UserView>();

  return (
    <SlideButton
      buttonText={intl('saenext.components.overview.UserViewButton.SwitchUserPerspective')}
      slideTitle={intl('saenext.components.overview.UserViewButton.SwitchUserPerspective')}
      slideSize={700}
      slideContent={<UserView ref={ref} />}
      className="full-width"
      submit={() => {
        if (ref.current) {
          return ref.current.submit();
        }
      }}
    />
  );
};

export default memo(UserViewButton);
