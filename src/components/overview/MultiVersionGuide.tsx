import { intl } from '@ali/cnd';
import React, { useContext } from 'react';
import { Icon, Grid, Button, ConsoleContext, useHistory } from '@ali/cnd';
import { AppVersionMap } from '~/constants/application';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/cache/common';

const { Row, Col } = Grid;

const backgroundColorMap = {
  lite: '#F7F7F7',
  std: '#EAF2FF',
  pro: '#F8F4EA',
};

const MultiVersionGuide = (props) => {
  const { multipleNewAccountStdEnable } = props
  const history = useHistory();
  const { region } = useContext(ConsoleContext);
  const currentRegionId = region.getCurrentRegionId();

  const versionsOptions = (multipleNewAccountStdEnable
    ? Object.keys(AppVersionMap)
    : Object.keys(AppVersionMap).filter(key => key !== 'std')
  ).map(key => ({
    ...AppVersionMap[key],
    value: key,
  }));

  return (
    <div className="muliti-version-guide w-100">
      <div className="title mb-s">
        {intl('saenext.components.overview.MultiVersionGuide.WelcomeToServerlessApplicationEngine')}
      </div>
      <div className="desc">
        {intl('saenext.components.overview.MultiVersionGuide.SaeServerlessAppEngineIs')}{' '}
        <a
          href={CachedData.confLink('help:sae:/what-is-serverless-app-engine')}
          target="_blank"
        >
          {intl('saenext.components.overview.MultiVersionGuide.LearnMore')}

          <Icon size="xs" type="external-link" className="ml-xs" />
        </a>
        {intl(
          'saenext.components.overview.MultiVersionGuide.SaeProvidesLightweightAndProfessional',
        )}
      </div>
      <div className="content mb-l"
        // @ts-ignore
        style={{'--grid-template-columns':multipleNewAccountStdEnable?'1fr 1fr 1fr':'1fr 1fr'}}
      >
        {versionsOptions.map((item) => {
          return (
            <div
              className={`version-item ${item.value === 'pro' ? 'pro-item' : ''}`}
              // @ts-ignore
              style={{ '--multi-version-item-backgroundColor': backgroundColorMap[item.value] }}
            >
              <div className="mb-l">
                <div className="item-title mb-l">
                  <img src={item.icon} />
                  <span style={{ fontSize: 14, marginLeft: 4 }}>{item.label}</span>
                </div>
                <div className="item-desc">
                  <div style={{ color: '#333', fontWeight: 500, marginBottom: 12 }}>
                    {item.desc}
                  </div>
                  <div
                    style={{ color: '#555' }}
                    dangerouslySetInnerHTML={{ __html: item.subDesc }}
                  ></div>
                </div>
              </div>
              <div className="item-footer">
                <Button
                  type="primary"
                  onClick={() => {
                    history.push(`/${currentRegionId}/create-app/micro?version=${item.value}`);
                  }}
                >
                  {intl(
                    'saenext.components.overview.MultiVersionGuide.CreateItemlabelApplication',
                    { itemLabel: item.label },
                  )}
                </Button>
              </div>
              {item.value === 'pro' && (
                <div className="recommend-pro">
                  {intl('saenext.components.overview.MultiVersionGuide.Recommend')}
                </div>
              )}
            </div>
          );
        })}
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <ExternalLink
          label={intl(
            'saenext.components.overview.MultiVersionGuide.ViewDetailedVersionComparison',
          )}
          url={CachedData.confLink('help:sae:app-multiple-versions')}
        />
      </div>
    </div>
  );
};

export default MultiVersionGuide;
