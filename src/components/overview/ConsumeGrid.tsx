import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect, useRef, useContext } from 'react';
import { Loading, Balloon, Icon, CndTable, Select, Button, Message } from '@ali/cnd';
import services from '~/services';
import { useNumber } from '~/utils/global';
import { getConsumeData, REGIONS } from './constants';
import { cloneDeep, get, isEmpty, find, forEach, includes, noop } from 'lodash';
import FeatureContext from '~/utils/featureContext';

type Props = {
  title: string;
  updateRegionSum: (value) => void;
  enableNewSaeVersion: boolean;
  isMultipleNewAccount: boolean;
  multipleNewAccountStdEnable: boolean;
  diskSizeEnable: boolean;
};

export default (props: Props) => {
  const { title, updateRegionSum, enableNewSaeVersion, isMultipleNewAccount, multipleNewAccountStdEnable, diskSizeEnable } = props;

  const { feature, webFeature } = useContext(FeatureContext);
  const isAllocateIdle = get(feature, 'idle', false);
  const { EnableWebApplication } = webFeature;

  const [loading, setLoading] = useState(false);
  const [regions, setRegions] = useState([...REGIONS]);
  const [regionId, setRegionId] = useState('');
  const [regionsQuta, setRegionsQuta] = useState({});
  const _data = getConsumeData({isAllocateIdle, EnableWebApplication, enableNewSaeVersion, isMultipleNewAccount, multipleNewAccountStdEnable, diskSizeEnable});
  const [dataSource, setDataSource] = useState(_data);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [regionLabels, setRegionLabels] = useState([]);
  const timeoutRegionsRef = useRef([]);


  useEffect(() => {
    getAppsResourcesRegions();
  }, [refreshIndex, isAllocateIdle, EnableWebApplication]);

  const getAppsResources = (regionId) => {
    const params = { RegionId: regionId };
    const customErrorHandle = (err, data, callback) => {
      const code = get(err, 'code');
      const mseeage = get(err, 'response.data.message');
      const timeoutCodes = ['ServiceUnavailable', 'ApiReadTimeOut'];
      const timeoutMsgs = [
        'The request has failed due to a temporary failure of the server.',
        intl('saenext.components.overview.ConsumeGrid.TheRequestedApiProcessingTimed'),
      ];
      const isTimeout = includes(timeoutCodes, code) && includes(timeoutMsgs, mseeage);
      if (isTimeout) {
        // 刷新需要清空
        timeoutRegionsRef.current = [...timeoutRegionsRef.current, regionId];
        return;
      }
      // RAM 账号未授权报错只弹一次
      if (code === 'AuthenticationFailed' && regionId !== regions?.[1]?.value) return;
      // callback && callback();
    };
    return Promise.all([
      // DescribeUserAppsInfo
      services.getUserAppsInfo(params, customErrorHandle),
      // DescribeResourceQuota
      services.getResourceQuota(params, customErrorHandle),
      // DescribeUserMeasureInfo
      services.getResourceMeasure(params, customErrorHandle),
    ])
      .then((res) => {
        const [userAppsres, resourceQuotares, useMeasureres] = res;
        const { Data: userApps = {} } = userAppsres;
        const { Data: resourceQuota = {} } = resourceQuotares;
        const { Data = {} } = useMeasureres;
        const { Measures: measures = [] } = Data;

        // 处理 measures 转化为对象
        let useMeasure = {};
        for (let i = 0; i < measures.length; i++) {
          const data = measures[i];
          const { AppSource, CpuStrategy } = data;
          const key = CpuStrategy === 'always' ? 'Always' : 'Request';
          switch (AppSource) {
            case 'job':
              Object.assign(useMeasure, {
                JobCpu: data.Cpu || 0,
                JobMemory: data.Memory || 0,
                JobRequest: data.Request || 0,
                JobInternetOutbound: data.InternetOutbound || 0,
              });
              break;
            case 'micro_service':
              Object.assign(useMeasure, {
                MicroServiceCpu: data.Cpu || 0,
                MicroServiceIdleCpu: data.IdleCpu || 0,
                MicroServiceMemory: data.Memory || 0,
                MicroServiceRequest: data.Request || 0,
                MicroServiceInternetOutbound: data.InternetOutbound || 0,
                MicroServiceDiskSize: data.DiskSize || 0,
              });
              break;
            case 'web':
              Object.assign(useMeasure, {
                [`Web${key}Cpu`]: data.Cpu || 0,
                [`Web${key}IdleCpu`]: data.IdleCpu || 0,
                [`Web${key}Memory`]: data.Memory || 0,
                [`Web${key}Request`]: data.Request || 0,
                [`Web${key}InternetOutbound`]: data.InternetOutbound || 0,
              });
              break;
          }
          Object.assign(useMeasure, { ...data });
        }
        ['AppSource', 'CpuStrategy', 'Cpu', 'Memory', 'Request', 'InternetOutbound'].forEach(
          (prop) => Reflect.deleteProperty(useMeasure, prop),
        );
        return [userApps, resourceQuota, useMeasure];
      })
      .then((res) => {
        const [userApps, resourceQuota, useMeasure] = res;
        //本月应用CPU资源使用量
        const microCpu = useNumber(useMeasure, 'MicroServiceCpu');
        const microIdleCpu = useNumber(useMeasure, 'MicroServiceIdleCpu');
        const webRequestCpu = useNumber(useMeasure, 'WebRequestCpu');
        const webRequestIdleCpu = useNumber(useMeasure, 'WebRequestIdleCpu');
        const allocateCpu = microCpu + webRequestCpu;
        const allocateIdleCpu =  microCpu + microIdleCpu + webRequestCpu + webRequestIdleCpu;
        const cpu = isAllocateIdle ? allocateIdleCpu : allocateCpu;

        // 本月应用内存资源使用量
        const microMemory = useNumber(useMeasure, 'MicroServiceMemory');
        const webRequestMemory = useNumber(useMeasure, 'WebRequestMemory');
        const memory = microMemory + webRequestMemory;

        // 本月临时磁盘使用量
        const diskSize = useNumber(useMeasure, 'EphemeralStorageUsed');

        // 应用总数
        const microApps = useNumber(userApps, 'MicroServiceApps');
        const webRequestApps = useNumber(userApps, 'WebRequestApps');
        const appCount = microApps + webRequestApps;

        // 微服务应用对应版本应用数
        const microAppsLite = useNumber(userApps.AppVersionDistribution.lite, 'Apps');
        const microAppsStd = useNumber(userApps.AppVersionDistribution.std, 'Apps');
        const microAppsPro = useNumber(userApps.AppVersionDistribution.pro, 'Apps');

        // 总实例数
        const microInstances = useNumber(userApps, 'MicroServiceInstances');
        const webRequestInstances = useNumber(userApps, 'WebRequestInstances');
        const instances = microInstances + webRequestInstances;

        // 实例上限
        const microMaxInstances = useNumber(resourceQuota, 'MicroServiceInstances');
        const webRequestMaxInstances = useNumber(resourceQuota, 'WebRequestInstances');
        const maxInstances = microMaxInstances + webRequestMaxInstances;

        // 弹性策略
        const microScaleRules = useNumber(userApps, 'MicroServiceScaleRules');
        const scaleRules = microScaleRules;

        // 本月公网出口总量
        const microIObound = useNumber(useMeasure, 'MicroServiceInternetOutbound');
        const webRequestIObound = useNumber(useMeasure, 'WebRequestInternetOutbound');
        const iobound = microIObound + webRequestIObound;

        // 本月请求总次数
        const microRequest = useNumber(useMeasure, 'MicroServiceRequest');
        const webRequestRequest = useNumber(useMeasure, 'WebRequestRequest');
        const request = microRequest + webRequestRequest;

        // 本月任务CPU资源使用量
        const taskCpu = useNumber(useMeasure, 'JobCpu');

        // 本月任务内存资源使用量
        const taskMemory = useNumber(useMeasure, 'JobMemory');

        // 任务模板总数
        const taskTemplates = useNumber(userApps, 'Jobs');

        // 启用任务模板数
        const taskEnabledTemplates = useNumber(userApps, 'JobsEnabled');

        // 运行中任务实例数
        const taskActiveInstances = useNumber(userApps, 'JobActiveInstances');

        // 任务实例上限
        const taskMaxInstances = useNumber(resourceQuota, 'JobInstances');

        // app cu
        // 活跃cpu使用量（核*秒）* 1 + 闲置cpu使用量（核*秒）* 0.2 + 内存（GiB*秒）* 0.25 + Web请求次数(百万次) * 2332.8 + Web公网出口总量(GB) * 15552
        // 活跃cpu使用量后端返回 核*分
        // 闲置cpu使用量后端返回 核*分
        // 内存后端返回 GiB*分
        // Web请求次数后端返回 次
        // Web公网出口总量后端返回 bytes
        let appCu = (
          ((microCpu + webRequestCpu) * 60 *1) +
          ((microIdleCpu + webRequestIdleCpu) * 60 *0.2) +
          (memory * 60 * 0.25) +
          ((webRequestRequest / 1000000) * 2332.8) +
          ((webRequestIObound / 1024 / 1024 / 1024) * 15552)
        );
        appCu = Number(appCu.toFixed(2));

        // task cu
        // cpu资源使用量（核*秒）* 1.2 + 内存资源使用量（GiB*秒）* 0.3
        // cpu使用量后端返回 核*分
        // 内存后端返回 GiB*分
        let taskCu = (
          (taskCpu * 60 * 1.2) +
          (taskMemory * 60 * 0.3)
        );
        taskCu = Number(taskCu.toFixed(2));

        const apps = {
          cu: { count: appCu },
          cpu: { count: cpu, microCpu, microIdleCpu, webRequestCpu, webRequestIdleCpu },
          memory: { count: memory, microMemory, webRequestMemory },
          application: { count: appCount, microApps, webRequestApps, microAppsLite, microAppsStd, microAppsPro },
          instances: { count: instances, microInstances, webRequestInstances },
          maxInstances: { count: maxInstances, microMaxInstances, webRequestMaxInstances },
          scaleRules: { count: scaleRules, microScaleRules },
          iobound: { count: iobound, microIObound, webRequestIObound },
          request: { count: request, microRequest, webRequestRequest },
          diskSize:{ count: diskSize }
        };
        const tasks = {
          taskCu,
          taskCpu,
          taskMemory,
          taskTemplates,
          taskEnabledTemplates,
          taskActiveInstances,
          taskMaxInstances,
        };

        return {
          [regionId]: { apps, tasks },
        };
      })
      .catch(() => {
        return {
          [regionId]: { apps: null, tasks: null },
        };
      });
  };

  const getAppsResourcesRegions = async () => {
    setLoading(true);
    const regionsQuta = [];
    for (let i = 0; i < regions.length; i++) {
      const regionId = regions[i].value;
      if (!regionId) continue;
      regionsQuta.push(getAppsResources(regionId));
    }
    Promise.all(regionsQuta)
      .then((res) => {
        const regionsQuta = {};
        const regionsSum = {
          apps: {
            cu: { count: 0 },
            cpu: { count: 0, microCpu: 0, microIdleCpu: 0, webRequestCpu: 0, webRequestIdleCpu: 0 },
            memory: { count: 0, microMemory: 0, webRequestMemory: 0 },
            application: { count: 0, microApps: 0, webRequestApps: 0,microAppsLite:0, microAppsStd:0, microAppsPro:0 },
            instances: { count: 0, microInstances: 0, webRequestInstances: 0 },
            maxInstances: { count: 0, microMaxInstances: 0, webRequestMaxInstances: 0 },
            scaleRules: { count: 0, microScaleRules: 0 },
            iobound: { count: 0, microIObound: 0, webRequestIObound: 0 },
            request: { count: 0, microRequest: 0, webRequestRequest: 0 },
            diskSize:{ count: 0 }
          },
          tasks: {
            taskCu: 0,
            taskCpu: 0,
            taskMemory: 0,
            taskTemplates: 0,
            taskEnabledTemplates: 0,
            taskActiveInstances: 0,
            taskMaxInstances: 0,
          },
        };
        for (let i = 0; i < res.length; i++) {
          const keys = Object.keys(res[i]);
          const [regionId] = keys;
          const data = res[i][regionId];
          regionsQuta[regionId] = data;
          regionsSum.apps.cu.count += get(data, 'apps.cu.count', 0);
          regionsSum.apps.cpu.count += get(data, 'apps.cpu.count', 0);
          regionsSum.apps.cpu.microCpu += get(data, 'apps.cpu.microCpu', 0);
          regionsSum.apps.cpu.microIdleCpu += get(data, 'apps.cpu.microIdleCpu', 0);
          regionsSum.apps.cpu.webRequestCpu += get(data, 'apps.cpu.webRequestCpu', 0);
          regionsSum.apps.cpu.webRequestIdleCpu += get(data, 'apps.cpu.webRequestIdleCpu', 0);

          regionsSum.apps.memory.count += get(data, 'apps.memory.count', 0);
          regionsSum.apps.memory.microMemory += get(data, 'apps.memory.microMemory', 0);
          regionsSum.apps.memory.webRequestMemory += get(data, 'apps.memory.webRequestMemory', 0);

          regionsSum.apps.diskSize.count += get(data, 'apps.diskSize.count', 0);

          regionsSum.apps.application.count += get(data, 'apps.application.count', 0);
          regionsSum.apps.application.microApps += get(data, 'apps.application.microApps', 0);
          regionsSum.apps.application.webRequestApps += get(
            data,
            'apps.application.webRequestApps',
            0,
          );
          regionsSum.apps.application.microAppsLite += get(data, 'apps.application.microAppsLite', 0);
          regionsSum.apps.application.microAppsStd += get(data, 'apps.application.microAppsStd', 0);
          regionsSum.apps.application.microAppsPro += get(data, 'apps.application.microAppsPro', 0);

          regionsSum.apps.instances.count += get(data, 'apps.instances.count', 0);
          regionsSum.apps.instances.microInstances += get(data, 'apps.instances.microInstances', 0);
          regionsSum.apps.instances.webRequestInstances += get(
            data,
            'apps.instances.webRequestInstances',
            0,
          );

          regionsSum.apps.maxInstances.count += get(data, 'apps.maxInstances.count', 0);
          regionsSum.apps.maxInstances.microMaxInstances += get(
            data,
            'apps.maxInstances.microMaxInstances',
            0,
          );
          regionsSum.apps.maxInstances.webRequestMaxInstances += get(
            data,
            'apps.maxInstances.webRequestMaxInstances',
            0,
          );

          regionsSum.apps.scaleRules.count += get(data, 'apps.scaleRules.count', 0);
          regionsSum.apps.scaleRules.microScaleRules += get(
            data,
            'apps.scaleRules.microScaleRules',
            0,
          );

          regionsSum.apps.iobound.count += get(data, 'apps.iobound.count', 0);
          regionsSum.apps.iobound.microIObound += get(data, 'apps.iobound.microIObound', 0);
          regionsSum.apps.iobound.webRequestIObound += get(
            data,
            'apps.iobound.webRequestIObound',
            0,
          );

          regionsSum.apps.request.count += get(data, 'apps.request.count', 0);
          regionsSum.apps.request.microRequest += get(data, 'apps.request.microRequest', 0);
          regionsSum.apps.request.webRequestRequest += get(
            data,
            'apps.request.webRequestRequest',
            0,
          );

          regionsSum.tasks.taskCu += get(data, 'tasks.taskCu', 0);
          regionsSum.tasks.taskCpu += get(data, 'tasks.taskCpu', 0);
          regionsSum.tasks.taskMemory += get(data, 'tasks.taskMemory', 0);
          regionsSum.tasks.taskTemplates += get(data, 'tasks.taskTemplates', 0);
          regionsSum.tasks.taskEnabledTemplates += get(data, 'tasks.taskEnabledTemplates', 0);
          regionsSum.tasks.taskActiveInstances += get(data, 'tasks.taskActiveInstances', 0);
          regionsSum.tasks.taskMaxInstances += get(data, 'tasks.taskMaxInstances', 0);
        }
        updateRegionSum(regionsSum);
        return {
          ...regionsQuta,
          'regions-sum': regionsSum,
        };
      })
      .then((res) => {
        const _regionId = regionId || 'regions-sum';
        const regionIdQuta = res[_regionId];
        const _dataSource = getConsumeData({isAllocateIdle, value: regionIdQuta, EnableWebApplication, enableNewSaeVersion, isMultipleNewAccount, multipleNewAccountStdEnable, diskSizeEnable});
        setRegionsQuta(res);
        setDataSource(_dataSource);
      })
      .finally(() => {
        let _regions = cloneDeep([...REGIONS]);
        let _regionLabels = [];
        if (!isEmpty(timeoutRegionsRef.current)) {
          const timeoutRegions = Array.from(new Set(timeoutRegionsRef.current));
          timeoutRegions.forEach((regionId) => {
            const _region = find(_regions, { value: regionId });
            if (!isEmpty(_region)) {
              forEach(_regions, (region) => {
                if (region.value === _region.value) {
                  _regionLabels.push(_region.label);
                  Reflect.set(region, 'disabled', true);
                }
              });
            }
          });
        }
        setRegions(_regions);
        setRegionLabels(_regionLabels);
        setLoading(false);
      });
  };

  return (
    <div className="consume w-100">
      <div className="title mb">
        <span style={{ minWidth: 56 }}>{title}</span>
        <Select
          size="small"
          className="ml mr-s"
          value={regionId}
          dataSource={regions}
          onChange={(regionId:string) => {
            setRegionId(regionId);
            const _regionId = regionId || 'regions-sum';
            const regionIdQuta = regionsQuta[_regionId];
            const _dataSource = getConsumeData({isAllocateIdle, value: regionIdQuta, EnableWebApplication,enableNewSaeVersion, isMultipleNewAccount, multipleNewAccountStdEnable, diskSizeEnable});
            setDataSource(_dataSource);
          }}
        />

        <Button
          size="small"
          className="isOnlyIcon"
          style={{ minWidth: 24 }}
          onClick={() => {
            timeoutRegionsRef.current = [];
            setRegionLabels([]);
            setRefreshIndex(Date.now());
          }}
        >
          <Icon type={loading ? 'loading' : 'refresh'} />
        </Button>
        {isEmpty(regionLabels) ? null : (
          <Message type="error" size="medium" className="timeout-message ml-s">
            {regionLabels.length === regions.length - 1 ? '' : regionLabels.join(', ')}
            {intl('saenext.components.overview.ConsumeGrid.TheRegionDataRequestTimed')}
          </Message>
        )}
      </div>
      <Loading visible={loading} className="full-width">
        {Object.keys(dataSource).map((key, index) => {
          return (
            <>
              <div className="wrapper w-100">
                {dataSource[key].map((item) => {
                  return item.visible?<CardOption className="next-consume" {...item} />:null;
                })}
              </div>
              {index !== Object.keys(dataSource).length - 1 && <div className="divider"></div>}
            </>
          );
        })}
      </Loading>
    </div>
  );
};

function CardOption(props: {
  className?: string;
  title: string;
  tips?: string;
  theme?: string;
  count: number | string;
  unit: string;
  detail?: {
    columns?: {
      dataIndex: string;
      title: string;
    }[];
    dataSource?: any[];
    tableWidth?: number;
    cellProps?: (rowIndex: number, colIndex: number) => any;
  };
  detailVisible?: boolean;
  visible: boolean;
}) {
  const { title, tips, detail, detailVisible } = props;
  const showDetail = detail && detail.columns && detail.columns.length && detailVisible !== false;
  const [type, setType] = React.useState('expand');

  return (
    <div className={`${props.className}`}>
      <div className="mb-xs">
        <span className="mr-s" style={{ color: '#696969', fontSize: 12 }}>
          {title}
        </span>
        {tips && (
          <Balloon
            align="t"
            cache={true}
            trigger={
              <Icon size="xs" type="help" className="fc-help-icon" style={{ color: '#696969' }} />
            }
            closable={false}
          >
            <span>{tips}</span>
          </Balloon>
        )}
      </div>
      <div
        style={{
          display: 'flex',
          alignItems: 'baseline',
          color: '#333333',
          flexWrap: 'wrap',
        }}
      >
        <div className="pt-xs pb-xs" style={{ fontSize: 24, lineHeight: '32px' }}>
          {props.count}
        </div>
        <div className="ml-xs" style={{ fontSize: 12, color: '#696969',maxWidth:38 }}>
          {props.unit}
        </div>
        {showDetail && (
          <Balloon
            type="primary"
            closable={false}
            triggerType="hover"
            popupStyle={{
              width: detail.tableWidth ? detail.tableWidth + 24 : 384,
              maxWidth: detail.tableWidth ? detail.tableWidth + 24 : 384,
            }}
            // trigger={<Icon className="ml-xs pointer" type={type} size="small" />}
            trigger={
              <div className="ml-xs pointer">
                <img
                  src="https://img.alicdn.com/imgextra/i4/O1CN01Fd8VXR1IAitJbnvUx_!!6000000000853-55-tps-12-12.svg"
                  style={{marginBottom:'-2px'}}
                />
              </div>
            }
            // onVisibleChange={visible => {
            //   setType(visible ? 'collapse' : 'expand');
            // }}
          >
            <div>
              <CndTable
                hasBorder={true}
                columns={detail.columns}
                dataSource={detail.dataSource}
                tableWidth={detail.tableWidth || 360}
                cellProps={detail.cellProps || noop}
              />
            </div>
          </Balloon>
        )}
      </div>
    </div>
  );
}
