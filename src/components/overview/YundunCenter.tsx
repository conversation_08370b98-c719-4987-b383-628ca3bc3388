import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Loading, Button, Icon, Select, Message, LinkButton, Tag } from '@ali/cnd';
import services from '~/services';
import { REGIONS } from './constants';
import { get, isEmpty, isEqual } from 'lodash';
import { Wpie, Wnumber, COLORS } from '@alife/aisc-widgets';
import CachedData from '~/cache/common';

type Props = {
  title?: string;
};

const regions = [...REGIONS.slice(1)];

export default (props: Props) => {
  const [loading, setLoading] = useState(false);
  const [regionId, setRegionId] = useState(regions[0].value);
  const [regionName, setRegionName] = useState(regions[0].label);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [data, setData] = useState({
    PostPaidModuleSwitch: false,
    IsBind: false,
    TotalBindAppCount: 0,
    TotalUnBindAppCount: 0,
    EventLevels: { Remind: 0, Serious: 0, Suspicious: 0 },
  });
  const [timeoutYundun, setTimeoutYundun] = useState(false);
  // 缓存region的云安全数据
  const yundunRef = React.useRef(new Map());
  const isRefresh = React.useRef(false);

  useEffect(() => {
    getYundunResources(regionId);
  }, [refreshIndex]);

  const getYundunResources = async (_regionId) => {
    setLoading(true);
    const __regionId = _regionId || regionId;
    let _data = yundunRef.current.get(__regionId);
    if (!isEmpty(_data) && !isRefresh.current) {
      setData(_data);
      setLoading(false);
      return;
    }
    const params = { RegionId: __regionId };
    const customErrorHandle = (err, data, callback) => {
      setLoading(false);
      const code = get(err, 'code');
      const mseeage = get(err, 'response.data.message');
      let _timeout = false;
      if (
        code === 'ApiReadTimeOut' &&
        mseeage === intl('saenext.components.overview.YundunCenter.TheRequestedApiProcessingTimed')
      ) {
        _timeout = true;
        setTimeoutYundun(_timeout);
        return;
      }
      callback && callback();
    };
    const res = await services.getEventLevelCount(params, customErrorHandle);
    _data = get(res, 'Data', {});
    yundunRef.current.set(__regionId, _data);
    setData(_data);
    setLoading(false);
  };

  const titleYundun = React.useMemo(() => {
    let title = intl('saenext.components.overview.YundunCenter.SecurityCenterNotActivated');
    const permission = get(data, 'PostPaidModuleSwitch', false);
    if (permission) {
      title = intl('saenext.components.overview.YundunCenter.MicroserviceApplicationsNotBoundTo');
      const totalBindAppCount = get(data, 'TotalBindAppCount', 0);
      const totalUnBindAppCount = get(data, 'TotalUnBindAppCount', 0);
      // @ts-ignore
      const total = parseInt(totalBindAppCount, 10) + parseInt(totalUnBindAppCount, 10);

      if (total && isEqual(get(data, 'TotalUnBindAppCount'), 0)) {
        title = intl('saenext.components.overview.YundunCenter.NumberOfSecurityAlerts');
      }
    }
    return title;
  }, [data]);

  return (
    <>
      <div className="yundun w-100">
        <div className="title mb">
          <span>{titleYundun}</span>
          <Select
            size="small"
            className="ml mr-s"
            value={regionId}
            dataSource={regions}
            onChange={(regionId: string, _, region: any) => {
              isRefresh.current = false;
              setRegionName(region.label);
              setRegionId(regionId);
              getYundunResources(regionId);
            }}
          />

          <Button
            size="small"
            className="isOnlyIcon"
            style={{ minWidth: 24 }}
            onClick={() => {
              isRefresh.current = true;
              setTimeoutYundun(false);
              setRefreshIndex(Date.now());
            }}
          >
            <Icon type={loading ? 'loading' : 'refresh'} />
          </Button>
          {!timeoutYundun ? null : (
            <Message type="error" size="medium" className="timeout-message ml-s">
              {intl('saenext.components.overview.YundunCenter.SecurityCenterDataRequestTimed')}
            </Message>
          )}
        </div>
        <div className="divider"></div>
        <Loading visible={loading} className="full-width">
          <UnBindPermission data={data} />
          <WholeBindsas data={data} />
          <WholeUnBindsas data={data} />
          <HalfWithWpieUnBindsas data={data} />
        </Loading>
      </div>
      <HalfWithWarnUnBindsas loading={loading} data={data} regionName={regionName} />
    </>
  );
};

// Serverless资产未开通 只展示开通按钮
export const UnBindPermission = (props) => {
  const { data = {}, style } = props;
  const permission = get(data, 'PostPaidModuleSwitch', false);
  if (permission) return null;

  let _style = style;
  if (isEmpty(style)) {
    _style = { alignItems: 'center' };
  }

  return (
    <div className="flex" style={_style}>
      <Button
        type="primary"
        className="mr-s"
        onClick={() => {
          const isPre = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';
          const yundun = isPre
            ? `${CachedData.confLink('feature:yundun-pre:url')}/?p=sas_buy&microFrontVersionName=serverless#/buy/cn-hangzhou?fromSAE=true`
            : `${CachedData.confLink('feature:yundun:url')}/?p=sas_buy#/buy/cn-hangzhou?fromSAE=true`;
          window.open(yundun, '_blank');
        }}
      >
        {intl('saenext.components.overview.YundunCenter.ActivateNow')}
      </Button>
      <Message type="warning">
        <span>
          {intl('saenext.components.overview.YundunCenter.CurrentlyAllYourApplicationsHave')}
          <a
            href={`${CachedData.confLink('feature:yundun:url')}/?p=sas#/overview/home/<USER>
            target="_blank"
          >
            {intl('saenext.components.overview.YundunCenter.SecurityCenter')}
          </a>
          {intl('saenext.components.overview.YundunCenter.ForMoreInformationSee')}
        </span>
        <LinkButton
          className="ml-xs"
          onClick={() => {
            window.open(
              CachedData.confLink('help:security-center:billing-overview'),
              '_blank',
            );
          }}
        >
          <span>{intl('saenext.components.overview.YundunCenter.SecurityCenterBillingRules')}</span>
          <Icon type="external_link" size="xs" className="ml-xs" />
        </LinkButton>
      </Message>
    </div>
  );
};

// 全部接入 只展示安全告警数
export const WholeBindsas = (props) => {
  const { data = {}, showWarn = false } = props;
  const _showWarn =
    showWarn ||
    (get(data, 'PostPaidModuleSwitch') &&
      get(data, 'TotalBindAppCount', 0) &&
      isEqual(get(data, 'TotalUnBindAppCount'), 0));

  if (!_showWarn) return null;
  const eventLevels = get(data, 'EventLevels', { Remind: 0, Serious: 0, Suspicious: 0 });

  const toYundunCenter = () => {
    const isPre = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';
    const yundun = isPre
      ? `${CachedData.confLink('feature:yundun-pre:url')}/?p=sas&microFrontVersionName=serverless#/serverless/cn-hangzhou?fromSAE=true`
      : `${CachedData.confLink('feature:yundun:url')}/?p=sas#/serverless/cn-hangzhou?fromSAE=true`;
    window.open(yundun, '_blank');
  };

  return (
    <div className="flex">
      <Tag color="red" className="mr-s" onClick={toYundunCenter}>
        <span>
          {intl('saenext.components.overview.YundunCenter.Urgent')}
          {get(eventLevels, 'Serious', 0)}
        </span>
      </Tag>
      <Tag color="yellow" className="mr-s" onClick={toYundunCenter}>
        <span>
          {intl('saenext.components.overview.YundunCenter.Suspicious')}
          {get(eventLevels, 'Suspicious', 0)}
        </span>
      </Tag>
      <Tag style={{ background: '#f5f5f5', color: '#808080' }} onClick={toYundunCenter}>
        <span>
          {intl('saenext.components.overview.YundunCenter.Reminder')}
          {get(eventLevels, 'Remind', 0)}
        </span>
      </Tag>
    </div>
  );
};

// 全部未接入
export const WholeUnBindsas = (props) => {
  const { data = {}, style, type = 'warning', showInsert = false } = props;
  const _showInsert =
    (get(data, 'PostPaidModuleSwitch') && isEqual(get(data, 'TotalBindAppCount'), 0)) || showInsert;
  if (!_showInsert) return null;

  let _style = style;
  if (isEmpty(style)) {
    _style = { alignItems: 'flex-start' };
  }

  return (
    <div className="flex" style={_style}>
      <Button
        type="primary"
        className="mb-s mr-s"
        style={{ width: 136 }}
        onClick={() => {
          const isPre = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';
          const yundun = isPre
            ? `${CachedData.confLink('feature:yundun-pre:url')}/?p=sas&microFrontVersionName=serverless#/serverless/cn-hangzhou?fromSAE=true`
            : `${CachedData.confLink('feature:yundun:url')}/?p=sas#/serverless/cn-hangzhou?fromSAE=true`;
          window.open(yundun, '_blank');
        }}
      >
        {intl('saenext.components.overview.YundunCenter.CustomOnDemandBinding')}
      </Button>
      <Message type={type}>
        <span>
          {intl('saenext.components.overview.YundunCenter.CurrentYour')}
          {type === 'warning'
            ? intl('saenext.components.overview.YundunCenter.All')
            : intl('saenext.components.overview.YundunCenter.Part')}
          {intl('saenext.components.overview.YundunCenter.TheApplicationHasNotBeen')}
          <a
            href={`${CachedData.confLink('feature:yundun:url')}/?p=sas#/overview/home/<USER>
            target="_blank"
          >
            {intl('saenext.components.overview.YundunCenter.SecurityCenter')}
          </a>
          {intl('saenext.components.overview.YundunCenter.ForMoreInformationSee')}
        </span>
        <LinkButton
          className="ml-xs"
          onClick={() => {
            window.open(
              CachedData.confLink('help:security-center:billing-overview'),
              '_blank',
            );
          }}
        >
          <span>{intl('saenext.components.overview.YundunCenter.SecurityCenterBillingRules')}</span>
          <Icon type="external_link" size="xs" className="ml-xs" />
        </LinkButton>
      </Message>
    </div>
  );
};

// 部分接入 展示绑定按钮和占比
export const HalfWithWpieUnBindsas = (props) => {
  const { data = {} } = props;
  const showWpie =
    get(data, 'PostPaidModuleSwitch') &&
    get(data, 'TotalUnBindAppCount', 0) &&
    get(data, 'TotalBindAppCount', 0);

  if (!showWpie) return null;
  const totalBindAppCount = get(data, 'TotalBindAppCount', 0);
  const totalUnBindAppCount = get(data, 'TotalUnBindAppCount', 0);
  const total = parseInt(totalBindAppCount, 10) + parseInt(totalUnBindAppCount, 10);

  return (
    <>
      <div className="flex">
        <div style={{ width: 300 }}>
          <Wpie
            height="120"
            config={{
              cycle: true,
              innerRadius: 0.9,
              outerRadius: 1,
              legend: {
                visible: true,
                position: 'bottom',
                align: 'center',
                showData: true,
                marker: {
                  symbol: 'hyphen',
                  style(oldStyle) {
                    return {
                      ...oldStyle,
                      r: 3,
                      lineWidth: 6,
                      fill: oldStyle.stroke,
                      lineAppendWidth: 0,
                    };
                  },
                },
                nameFormatter: (name) => {
                  const count =
                    name === intl('saenext.components.overview.YundunCenter.Unbound')
                      ? totalUnBindAppCount
                      : totalBindAppCount;
                  return `${name} ${count}`;
                },
              },
              tooltip: {
                nameFormatter: function (v) {
                  const name =
                    v === intl('saenext.components.overview.YundunCenter.Unbound')
                      ? intl(
                          'saenext.components.overview.YundunCenter.MicroserviceApplicationsNotBoundTo',
                        )
                      : intl(
                          'saenext.components.overview.YundunCenter.MicroserviceApplicationsBoundToSecurity',
                        );
                  return name;
                },
              },
              // @ts-ignore
              colors: [COLORS.widgetsColorGray, COLORS.widgetsColorPurple],
            }}
            data={{
              name: intl(
                'saenext.components.overview.YundunCenter.PercentageOfCloudSecurityCenter',
              ),
              data: [
                [intl('saenext.components.overview.YundunCenter.Unbound'), totalUnBindAppCount],
                [intl('saenext.components.overview.YundunCenter.Bound'), totalBindAppCount],
              ],
            }}
          >
            <Wnumber
              bottomTitle={intl('saenext.components.overview.YundunCenter.TotalApplication')}
              style={{ fontSize: 24 }}
              unit={intl("general.unit.count")}
            >
              {total}
            </Wnumber>
          </Wpie>
        </div>
        <div style={{ flex: 1 }}>
          <WholeUnBindsas
            type="notice"
            showInsert={true}
            style={{ flexDirection: 'column', alignItems: 'flex-start' }}
          />
        </div>
      </div>
    </>
  );
};

// 部分接入 展示安全告警数
export const HalfWithWarnUnBindsas = (props) => {
  const { loading, data = {}, regionName } = props;
  const showWarn =
    get(data, 'PostPaidModuleSwitch') &&
    get(data, 'TotalUnBindAppCount', 0) &&
    get(data, 'TotalBindAppCount', 0);

  if (!showWarn) return null;

  return (
    <div className="yundun w-100">
      <Loading visible={loading} className="full-width">
        <div className="title mb">
          <span>
            {intl(
              'saenext.components.overview.YundunCenter.MicroserviceApplicationsBoundToSecurity',
            )}
          </span>
          <div className="display ml">{regionName}</div>
        </div>
        <div className="divider"></div>
        <div className="flex">
          <span style={{ marginRight: 16 }}>
            {intl('saenext.components.overview.YundunCenter.NumberOfSecurityAlerts')}
          </span>
          <WholeBindsas showWarn={true} data={data} />
        </div>
      </Loading>
    </div>
  );
};
