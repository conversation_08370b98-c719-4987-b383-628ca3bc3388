import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Badge, Balloon, Button, DateTime, Icon, Loading, Progress } from '@ali/cnd';
import RefreshButton from '../../shared/RefreshButton';
import services from "~/services";
import { forEach, findIndex, cloneDeep, isEqual, isEmpty, sortBy } from 'lodash';
import moment from 'moment';
import './index.less';
import { isInteger } from '../../../utils/global';
import { convertToCu, formatNumber } from '../../../utils/global';
import TextWithBalloon from '../../shared/TextWithBalloon';
import CachedData from '../../../cache/common';

type Props = {
  title: string;
};

const PackageTypes = {
  CU: 'sae_cupackage_dp_cn',
  CU_FREE: 'sae_cufreetrial_dp_cn',
  MEM: 'sae_mem_bag',
  CPU: 'sae_cpu_bag',
  MEM_FREE: 'sae_trialmem_dp_cn',
  CPU_FREE: 'sae_trialcpu_dp_cn',
};

const PackageNames = {
  [PackageTypes.CU]: intl('saenext.components.pack-balance.CuResourcePackage'),
  [PackageTypes.CU_FREE]: intl('saenext.components.pack-balance.CuResourcePackageFreeTrial'),
  [PackageTypes.MEM]: intl('saenext.components.pack-balance.CpuAndMemResourcePackages'),
  [PackageTypes.CPU]: intl('saenext.components.pack-balance.CpuAndMemResourcePackages'),
  [PackageTypes.MEM_FREE]: intl('saenext.components.pack-balance.CpuAndMemResourcePackages.2'),
  [PackageTypes.CPU_FREE]: intl('saenext.components.pack-balance.CpuAndMemResourcePackages.2'),
};

const CommonBuyLink = {
  'OFFICIAL': `${CachedData.confLink('feature:common-buy')}/?commodityCode=sae_cupackage_dp_cn`,
  'SIN': `${CachedData.confLink('feature:common-buy')}/?commodityCode=sae_cupackage_dp_intl`,
}

export default (props: Props) => {
  const { title } = props;
  const [loading, setLoading] = useState(false);
  const [hasResource, setHasResource] = useState(false);
  const [resourcePackage, setResourcePackage] = useState([]);
  const [notAuthorized, setNotAuthorized] = useState(false);

  const channel = window.ALIYUN_CONSOLE_CONFIG?.CHANNEL;

  useEffect(() => {
    getResourcePackage();
  }, []);

  const getResourcePackage = async (PageNum = 1, PageSize = 99, LastInstance = []) => {
    setLoading(true);
    const res = await services.getResourcePackage({
      params: { PageNum, PageSize },
      customErrorHandle: (err, data, callback) => {
        setLoading(false);
        setHasResource(false);
        setResourcePackage([]);
        if (err.code === 'NotAuthorized') {
          setNotAuthorized(true);
          return;
        }
        callback && callback();
      },
    });
    const { Data = {} } = res || {};
    const { Instances = {}, TotalCount } = Data;
    const { Instance = [] } = Instances;
    if (PageSize * PageNum < TotalCount) {
      getResourcePackage(PageNum + 1, PageSize, [...Instance, ...LastInstance]);
    }
    const _resourcePackage = combineAvailablePackage([...Instance, ...LastInstance]);
    setLoading(false);
    setResourcePackage(_resourcePackage);
    setHasResource(!!_resourcePackage.length);
  };

  const combineAvailablePackage = (packages = []) => {
    let _resourcePackage = [];
    for (let i = 0; i < packages.length; i++) {
      const pkg = packages[i];
      // 1、过期的不展示
      if (moment(pkg.ExpiryTime).unix() < moment().unix()) continue;

      // 2、剩余量是0的不展示
      if (isEqual(pkg.RemainingAmount, '0')) continue;

      _resourcePackage.push(pkg);
    }

    if (isEmpty(_resourcePackage)) return [];

    function splitPackagesByCode(packages) {
      const cuPackages = [];
      const cufreePackages = [];
      const cpuMemPackages = [];
      const cpuMemfreePackages = [];
      forEach(packages, (pkg) => {
        const { CommodityCode } = pkg;
        switch (CommodityCode) {
          case PackageTypes.CU:
            cuPackages.push(pkg);
            break;
          case PackageTypes.CU_FREE:
            cufreePackages.push(pkg);
            break;
          case PackageTypes.MEM:
          case PackageTypes.CPU:
            cpuMemPackages.push(pkg);
            break;
          case PackageTypes.MEM_FREE:
          case PackageTypes.CPU_FREE:
            cpuMemfreePackages.push(pkg);
            break;
          default:
            break;
        }
      });
      return [cuPackages, cufreePackages, cpuMemPackages, cpuMemfreePackages];
    }

    // 3、将资源包分类 cu、免费cu、cpumem、免费cpumem 四种资源包类型
    const [cuPackages = [], cufreePackages = [], cpuMemPackages = [], cpuMemfreePackages = []] =
      splitPackagesByCode(_resourcePackage);

    function combinePackagesByExpiry(packages, isCu = false) {
      const _packages = [];
      forEach(packages, (pkg) => {
        // 先找_packages中是否有过期时间跟pkg 过期时间一致的
        const { CommodityCode } = pkg;
        const index = findIndex(_packages, (item) => {
          return (
            moment(item.ExpiryTime).format('YYYY-MM-DD') ===
            moment(pkg.ExpiryTime).format('YYYY-MM-DD')
          );
        });

        if (index >= 0) {
          const _existpkg = _packages[index];
          // 存在
          if (isCu) {
            _existpkg.totalCu += Number(pkg.TotalAmount);
            _existpkg.remainingCu += Number(pkg.RemainingAmount);
          } else {
            // cpu 或者 mem
            const key = CommodityCode.indexOf('cpu') > 0 ? 'Cpu' : 'Mem';
            const elseKey = key === 'Cpu' ? 'Mem' : 'Cpu';
            _existpkg[`total${key}`] += Number(pkg.TotalAmount);
            _existpkg[`remaining${key}`] += Number(pkg.RemainingAmount);
            _existpkg[`total${elseKey}`] += 0;
            _existpkg[`remaining${elseKey}`] += 0;
          }
        } else {
          // 不存在
          const _absentpkg = cloneDeep(pkg);
          if (isCu) {
            _absentpkg.totalCu = Number(pkg.TotalAmount);
            _absentpkg.remainingCu = Number(pkg.RemainingAmount);
          } else {
            // cpu 或者 mem
            const key = CommodityCode.indexOf('cpu') > 0 ? 'Cpu' : 'Mem';
            const elseKey = key === 'Cpu' ? 'Mem' : 'Cpu';
            _absentpkg[`total${key}`] = Number(pkg.TotalAmount);
            _absentpkg[`remaining${key}`] = Number(pkg.RemainingAmount);
            _absentpkg[`total${elseKey}`] = 0;
            _absentpkg[`remaining${elseKey}`] = 0;
          }
          _packages.push(_absentpkg);
        }
      });
      return _packages;
    }

    // 4、将同类型的资源包按照过期时间一致合并
    const _cuPackages = combinePackagesByExpiry(cuPackages, true);
    const _cufreePackages = combinePackagesByExpiry(cufreePackages, true);
    const _cpuMemPackages = combinePackagesByExpiry(cpuMemPackages);
    const _cpuMemfreePackages = combinePackagesByExpiry(cpuMemfreePackages);

    _resourcePackage = [
      ..._cuPackages,
      ..._cufreePackages,
      ..._cpuMemPackages,
      ..._cpuMemfreePackages,
    ];

    const resourcePackageSorted = sortBy(_resourcePackage, (item) => new Date(item.ExpiryTime));

    return resourcePackageSorted;
  };

  if (notAuthorized) {
    return (
      <div className="balance w-100">
        <div className="title mb">
          <span>{title}</span>
        </div>
        <div>{intl('saenext.components.pack-balance.YouDoNotHaveThe')}</div>
      </div>
    );
  }

  return (
    <Loading className="balance w-100" visible={loading}>
      <div className="title mb">
        <span>{title}</span>
        <span>
          {hasResource ? (
            <a href={CachedData.confLink('help:sae:resource-packs-prepaid')} target="_blank">
              <Button
                text
                type="primary"
              >
                <span>{intl('saenext.components.pack-balance.BillingDetails')}</span>
                <Icon type="external_link" />
              </Button>
            </a>
          ) : null}
          <RefreshButton
            handler={getResourcePackage}
            className="ml"
            label={intl('saenext.components.shared.TextRefreshButton.Refresh')}
          />
        </span>
      </div>
      {hasResource ? (
        <div className="resource">
          {resourcePackage.map((item, index, arr) => (
            <BagUsage
              data={item}
              defaultCollapse={arr.length > 2 && index !== 0}
              inUse={index === 0}
            />
          ))}
        </div>
      ) : (
        <>
          <div className="w-100">
            <span>{intl('saenext.components.pack-balance.ResourcePackageIsADiscount')}</span>
            <a href={CachedData.confLink('help:sae:resource-packs-prepaid')} target="_blank" data-spm-protocol="i">
              <Button
                text
                type="primary"
              >
                <span>{intl('saenext.components.pack-balance.BillingDetails')}</span>
                <Icon type="external_link" />
              </Button>
            </a>
          </div>
        </>
      )}

      <a href={CommonBuyLink[channel]} target="_blank" data-spm-protocol="i">
        <Button
          type="primary"
          className="mt"
        >
          <span>{intl('saenext.components.pack-balance.PurchaseAResourcePackage')}</span>
        </Button>
      </a>
    </Loading>
  );
};

const BagUsage = (props) => {
  const {
    style = {},
    data: {
      totalCu,
      remainingCu,
      totalCpu,
      remainingCpu,
      totalMem,
      remainingMem,
      CommodityCode,
      RemainingAmountUnit,
      TotalAmountUnit,
      ExpiryTime,
    },
    defaultCollapse = false,
    inUse = false,
  } = props;

  const title =
    PackageNames[CommodityCode] ||
    intl('saenext.components.pack-balance.EnterpriseResourcePackage');
  const isCu = CommodityCode === 'sae_cupackage_dp_cn' || CommodityCode === 'sae_cufreetrial_dp_cn' || CommodityCode === 'sae_cupackage_dp_intl';
  const [expand, setExpand] = useState(!defaultCollapse);

  const totalCuVal = isCu ? totalCu : convertToCu(totalCpu, 'cpu') + convertToCu(totalMem, 'mem');
  const remainingCuVal = isCu
    ? remainingCu
    : convertToCu(remainingCpu, 'cpu') + convertToCu(remainingMem, 'mem');

  const calcTotalMetric = (val) => {
    return isInteger(val) ? val : val.toFixed(2).toLocaleString();
  };

  const checkOutingExpiry = () => {
    return moment(ExpiryTime).unix() - moment().unix() < 7 * 24 * 3600;
  };

  return (
    <div style={style} className="bagusage-box">
      <div className="title-box">
        <div className="text-box">
          <span className="usage-title">
            {title} : {formatNumber(totalCuVal)} CU
            {!isCu && (
              <TextWithBalloon
                align="t"
                text={''}
                tips={
                  <>
                    {intl('saenext.components.pack-balance.ActualSpecifications')}
                    <br />
                    CPU: <span className="text-bold">{totalCpu} </span>
                    {intl('saenext.components.pack-balance.Nuclear')} <br />
                    Mem: <span className="text-bold">{totalMem} </span>
                    {intl('saenext.components.pack-balance.Gib')} <br />
                    {intl('saenext.components.pack-balance.WhereCoreCuGibCu')}
                  </>
                }
              />
            )}
            {inUse && (
              <Badge
                content={intl('saenext.components.pack-balance.Deductible')}
                className="ml-s"
                style={{
                  backgroundColor: '#EAFAEC',
                  color: '#038E4A',
                  height: 17,
                  lineHeight: '16px',
                }}
              />
            )}
          </span>
        </div>
        <div className="pointer" onClick={() => setExpand(!expand)}>
          <Icon type={expand ? 'arrow-up' : 'arrow-down'} size="xs" style={{ color: '#333' }} />
        </div>
      </div>
      <div className={'content-box ' + (expand ? '' : 'none')}>
        <Balloon
          align="t"
          closable={false}
          trigger={
            <Progress
              className="mt-s mb-s"
              percent={(1 - remainingCuVal / totalCuVal) * 100}
              textRender={(percent) => `${intl("saenext.components.pack-balance.Remaining")}${Math.round(100 - percent)}%`}
            />
          }
        >
          {intl('saenext.components.pack-balance.ResourcePackageMargin')}

          <span className="count text-bold">{formatNumber(remainingCuVal)} </span>
          <span className="unit">CU</span>

          {!isCu && (
            <div>
              CPU: <span className="text-bold">{remainingCpu} </span>
              {intl('saenext.components.pack-balance.Nuclear')} <br />
              Mem: <span className="text-bold">{remainingMem} </span>
              {intl('saenext.components.pack-balance.Gib')} <br />
              {intl('saenext.components.pack-balance.WhereCoreCuGibCu')}
            </div>
          )}
        </Balloon>
        <span className={`usage-time ${checkOutingExpiry() ? 'expiry' : 'not-expiry'}`}>
          <DateTime
            value={ExpiryTime}
            format={{
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            }}
          />

          <span className="ml-xs">{intl('saenext.components.pack-balance.Expiration')}</span>
        </span>
      </div>
    </div>
  );
};
