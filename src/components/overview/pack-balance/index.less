@box-space: 16px;
@border-width: 1px;
@right-side-width: 390px;
@border-radius: 4px;
@border-color: #e5e5e5;

.w-100 {
  width: 100%;
}

.balance {
  padding: @box-space;
  border-radius: @border-radius;
  border: @border-width solid @border-color;
  margin-bottom: @box-space;
  .title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .resource {
    border-top: 1px solid #E3E4E6;
    .bagusage-box {
      padding: 16px 0;
      border-bottom: 1px solid #E3E4E6;
      
      .title-box {
        display: flex;
        justify-content: space-between;
        // align-items: center;
        // height: calc(@box-space * 2);
        .text-box {
          .usage-title {
            font-size: 14px;
            font-weight: 500;
            color: #333333;
          }
          .usage-time {
            height: 24px;
            width: 120px;
            line-height: 24px;
            display: inline-block;
            text-align: center;
            background: #f5f5f5;
            border-radius: 12px;
            font-size: 12px;
          }
          .expiry {
            color: #996262;
            background: rgba(204, 0, 0, 0.05);
          }
          .not-expiry {
            color: #555555;
            background-color: rgb(245, 245, 245);
          }
        }
      }
      .content-box {
        .item {
          display: flex;
          margin-top: calc(@box-space / 2);
          .lable {
            font-size: 12px;
            color: #555555;
            letter-spacing: 0;
          }
          .count {
            font-size: 12px;
            color: #333333;
            letter-spacing: 0;
            // font-family: PingFangSC-Medium;
          }
          .unit {
            font-size: 12px;
            color: #555555;
            letter-spacing: 0;
            margin-left: 2px;
          }
        }

        .next-progress-line-show-info {
          .next-progress-line-container {
            padding-right: 70px;
            margin-right: -70px;
          }

          .next-progress-line-text {
            width: 60px;
          }
        }

        .usage-time {
          padding: 2px 8px;
          height: 24px;
          width: max-content;
          line-height: 20px;
          display: inline-block;
          text-align: center;
          background: #f5f5f5;
          border-radius: 2px;
          font-size: 12px;
        }
        .expiry {
          color: #996262;
          background: rgba(204, 0, 0, 0.05);
        }
        .not-expiry {
          color: #555555;
          background-color: rgb(245, 245, 245);
        }
      }
    }
  }
}