import { intl } from '@ali/cnd';
import { Field, Form, Input } from '@ali/cnd';
import React, { Component } from 'react';
import { getCookieByKeyName } from '~/utils/hacker';

export default class UserView extends Component {
  field: any;

  constructor(props) {
    super(props);
    this.state = {};
    this.field = new Field(this);
  }

  submit() {
    return new Promise<void>(async (resolve, reject) => {
      this.field.validate(async (err, payload) => {
        if (err) {
          reject();
          return;
        }

        const { uid } = payload;

        setTimeout(() => {
          const cuid = getCookieByKeyName('inner_oneconsole_aliyunpk');
          if (cuid) {
            window.sessionStorage.setItem('DISABLE_HACK_USER', cuid);
          }
          window.localStorage.setItem('HACK_USER', uid);
          window.location.reload();
          resolve();
        }, 500);
      });
    });
  }

  render() {
    return (
      <Form field={this.field}>
        <Form.Item
          required
          label={intl('saenext.components.overview.UserView.AccountId')}
          labelTextAlign="left"
          labelCol={{ fixedSpan: 4 }}
          wrapperCol={{ span: 20 }}
          requiredMessage={intl('saenext.components.overview.UserView.TheAccountIdIsRequired')}
        >
          <Input
            autoFocus={true}
            name="uid"
            maxLength={128}
            minLength={1}
            placeholder={intl('saenext.components.overview.UserView.EnterAnAccountId')}
          />
        </Form.Item>
      </Form>
    );
  }
}
