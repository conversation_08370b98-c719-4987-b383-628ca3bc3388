import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Button, Icon, Loading } from '@ali/cnd';
import services from '~/services';
import { forEach, findIndex, cloneDeep, isEqual, isEmpty } from 'lodash';
import moment from 'moment';
import { isInteger } from '~/utils/global';
import CachedData from '~/cache/common';

type Props = {
  title: string;
};

const PackageTypes = {
  CU: 'sae_cupackage_dp_cn',
  CU_FREE: 'sae_cufreetrial_dp_cn',
  MEM: 'sae_mem_bag',
  CPU: 'sae_cpu_bag',
  MEM_FREE: 'sae_trialmem_dp_cn',
  CPU_FREE: 'sae_trialcpu_dp_cn',
};

const PackageNames = {
  [PackageTypes.CU]: intl('saenext.components.overview.PackBalance.CuResourcePackage'),
  [PackageTypes.CU_FREE]: intl(
    'saenext.components.overview.PackBalance.CuResourcePackageFreeTrial',
  ),
  [PackageTypes.MEM]: intl('saenext.components.overview.PackBalance.CpuAndMemResourcePackages'),
  [PackageTypes.CPU]: intl('saenext.components.overview.PackBalance.CpuAndMemResourcePackages'),
  [PackageTypes.MEM_FREE]: intl(
    'saenext.components.overview.PackBalance.CpuAndMemResourcePackages.2',
  ),
  [PackageTypes.CPU_FREE]: intl(
    'saenext.components.overview.PackBalance.CpuAndMemResourcePackages.2',
  ),
};

export default (props: Props) => {
  const { title } = props;
  const [loading, setLoading] = useState(false);
  const [hasResource, setHasResource] = useState(false);
  const [resourcePackage, setResourcePackage] = useState([]);
  const [notAuthorized, setNotAuthorized] = useState(false);

  useEffect(() => {
    getResourcePackage();
  }, []);

  const getResourcePackage = async (PageNum = 1, PageSize = 99, LastInstance = []) => {
    setLoading(true);
    const res = await services.getResourcePackage({
      params: { PageNum, PageSize },
      customErrorHandle: (err, data, callback) => {
        setLoading(false);
        setHasResource(false);
        setResourcePackage([]);
        if (err.code === 'NotAuthorized') {
          setNotAuthorized(true);
          return;
        }
        callback && callback();
      },
    });
    const { Data = {} } = res;
    const { Instances = {}, TotalCount } = Data;
    const { Instance = [] } = Instances;
    if (PageSize * PageNum < TotalCount) {
      getResourcePackage(PageNum + 1, PageSize, [...Instance, ...LastInstance]);
    }
    const _resourcePackage = combineAvailablePackage([...Instance, ...LastInstance]);
    setLoading(false);
    setResourcePackage(_resourcePackage);
    setHasResource(!!_resourcePackage.length);
  };

  const combineAvailablePackage = (packages = []) => {
    let _resourcePackage = [];
    for (let i = 0; i < packages.length; i++) {
      const pkg = packages[i];
      // 1、过期的不展示
      if (moment(pkg.ExpiryTime).unix() < moment().unix()) continue;

      // 2、剩余量是0的不展示
      if (isEqual(pkg.RemainingAmount, '0')) continue;

      _resourcePackage.push(pkg);
    }

    if (isEmpty(_resourcePackage)) return [];

    function splitPackagesByCode(packages) {
      const cuPackages = [];
      const cufreePackages = [];
      const cpuMemPackages = [];
      const cpuMemfreePackages = [];
      forEach(packages, (pkg) => {
        const { CommodityCode } = pkg;
        switch (CommodityCode) {
          case PackageTypes.CU:
            cuPackages.push(pkg);
            break;
          case PackageTypes.CU_FREE:
            cufreePackages.push(pkg);
            break;
          case PackageTypes.MEM:
          case PackageTypes.CPU:
            cpuMemPackages.push(pkg);
            break;
          case PackageTypes.MEM_FREE:
          case PackageTypes.CPU_FREE:
            cpuMemfreePackages.push(pkg);
            break;
          default:
            break;
        }
      });
      return [cuPackages, cufreePackages, cpuMemPackages, cpuMemfreePackages];
    }

    // 3、将资源包分类 cu、免费cu、cpumem、免费cpumem 四种资源包类型
    const [cuPackages = [], cufreePackages = [], cpuMemPackages = [], cpuMemfreePackages = []] =
      splitPackagesByCode(_resourcePackage);

    function combinePackagesByExpiry(packages, isCu = false) {
      const _packages = [];
      forEach(packages, (pkg) => {
        // 先找_packages中是否有过期时间跟pkg 过期时间一致的
        const { CommodityCode } = pkg;
        const index = findIndex(_packages, (item) => {
          return (
            moment(item.ExpiryTime).format('YYYY-MM-DD') ===
            moment(pkg.ExpiryTime).format('YYYY-MM-DD')
          );
        });

        if (index >= 0) {
          const _existpkg = _packages[index];
          // 存在
          if (isCu) {
            _existpkg.totalCu += Number(pkg.TotalAmount);
            _existpkg.remainingCu += Number(pkg.RemainingAmount);
          } else {
            // cpu 或者 mem
            const key = CommodityCode.indexOf('cpu') > 0 ? 'Cpu' : 'Mem';
            const elseKey = key === 'Cpu' ? 'Mem' : 'Cpu';
            _existpkg[`total${key}`] += Number(pkg.TotalAmount);
            _existpkg[`remaining${key}`] += Number(pkg.RemainingAmount);
            _existpkg[`total${elseKey}`] += 0;
            _existpkg[`remaining${elseKey}`] += 0;
          }
        } else {
          // 不存在
          const _absentpkg = cloneDeep(pkg);
          if (isCu) {
            _absentpkg.totalCu = Number(pkg.TotalAmount);
            _absentpkg.remainingCu = Number(pkg.RemainingAmount);
          } else {
            // cpu 或者 mem
            const key = CommodityCode.indexOf('cpu') > 0 ? 'Cpu' : 'Mem';
            const elseKey = key === 'Cpu' ? 'Mem' : 'Cpu';
            _absentpkg[`total${key}`] = Number(pkg.TotalAmount);
            _absentpkg[`remaining${key}`] = Number(pkg.RemainingAmount);
            _absentpkg[`total${elseKey}`] = 0;
            _absentpkg[`remaining${elseKey}`] = 0;
          }
          _packages.push(_absentpkg);
        }
      });
      return _packages;
    }

    // 4、将同类型的资源包按照过期时间一致合并
    const _cuPackages = combinePackagesByExpiry(cuPackages, true);
    const _cufreePackages = combinePackagesByExpiry(cufreePackages, true);
    const _cpuMemPackages = combinePackagesByExpiry(cpuMemPackages);
    const _cpuMemfreePackages = combinePackagesByExpiry(cpuMemfreePackages);

    _resourcePackage = [
      ..._cuPackages,
      ..._cufreePackages,
      ..._cpuMemPackages,
      ..._cpuMemfreePackages,
    ];

    return _resourcePackage;
  };

  if (notAuthorized) {
    return (
      <div className="balance w-100">
        <div className="title mb">
          <span>{title}</span>
        </div>
        <div>{intl('saenext.components.overview.PackBalance.YouDoNotHaveThe')}</div>
      </div>
    );
  }

  return (
    <Loading className="balance w-100" visible={loading}>
      <div className="title mb">
        <span>{title}</span>
        {hasResource ? (
          <div>
            <Button
              text
              type="primary"
              onClick={() =>
                window.open(
                  CachedData.confLink('help:sae:resource-packs-prepaid'),
                  '_blank',
                )
              }
            >
              <span>{intl('saenext.pages.overview.PackBalance.BillingDetails')}</span>
              <Icon type="external_link" />
            </Button>
            <Button
              text
              type="primary"
              className="ml"
              onClick={() =>
                window.open(
                  `${CachedData.confLink('feature:common-buy')}/?commodityCode=sae_cupackage_dp_cn`,
                  '_blank',
                )
              }
            >
              <span>{intl('saenext.pages.overview.PackBalance.PurchaseAResourcePackage')}</span>
              <Icon type="external_link" />
            </Button>
          </div>
        ) : null}
      </div>
      {hasResource ? (
        <div className="resource">
          {resourcePackage.map((item, index) => (
            <BagUsage
              data={item}
              style={{ marginBottom: index === resourcePackage.length - 1 ? 0 : 16 }}
            />
          ))}
        </div>
      ) : (
        <>
          <div className="w-100">
            <span>
              {intl('saenext.components.overview.PackBalance.ResourcePackageIsADiscount')}
            </span>
            <Button
              text
              type="primary"
              className="ml-xs"
              style={{ marginBottom: 2 }}
              onClick={() =>
                window.open(
                  CachedData.confLink('help:sae:resource-packs-prepaid'),
                  '_blank',
                )
              }
            >
              <span>{intl('saenext.pages.overview.PackBalance.BillingDetails')}</span>
              <Icon type="external_link" />
            </Button>
          </div>
          <Button
            type="primary"
            className="mt-xl"
            style={{ width: 120, borderRadius: 0 }}
            onClick={() => {
              window.open(
                `${CachedData.confLink('feature:common-buy')}/?commodityCode=sae_cupackage_dp_cn`,
                '_blank',
              );
            }}
          >
            {intl('saenext.pages.overview.PackBalance.BuyNow')}
          </Button>
        </>
      )}
    </Loading>
  );
};

const BagUsage = (props) => {
  const {
    style,
    data: {
      totalCu,
      remainingCu,
      totalCpu,
      remainingCpu,
      totalMem,
      remainingMem,
      CommodityCode,
      RemainingAmountUnit,
      TotalAmountUnit,
      ExpiryTime,
    },
  } = props;

  const title =
    PackageNames[CommodityCode] ||
    intl('saenext.pages.overview.PackBalance.EnterpriseResourcePackage');
  const isCu = CommodityCode === 'sae_cupackage_dp_cn' || CommodityCode === 'sae_cufreetrial_dp_cn';
  const [expand, setExpand] = useState(true);

  const calcTotalMetric = (val) => {
    return isInteger(val) ? val : val.toFixed(2).toLocaleString();
  };

  const checkOutingExpiry = () => {
    return moment(ExpiryTime).unix() - moment().unix() < 7 * 24 * 3600;
  };

  return (
    <div style={style} className={expand ? 'bagusage-box bagusage-expend-box' : 'bagusage-box'}>
      <div className="title-box">
        <div className="text-box">
          <span className="usage-title">{title}</span>
          <span className={`usage-time ${checkOutingExpiry() ? 'expiry' : 'not-expiry'}`}>
            {moment(ExpiryTime).format('YYYY-MM-DD')}
            {intl('saenext.pages.overview.PackBalance.Expiration')}
          </span>
        </div>
        <div className="pointer" onClick={() => setExpand(!expand)}>
          <Icon
            type={expand ? 'button_up' : 'button_down'}
            size="medium"
            style={{ color: '#333' }}
          />
        </div>
      </div>
      <div className="content-box">
        {isCu ? (
          <div className="item">
            <div className="lable">
              {intl('saenext.components.overview.PackBalance.CuMarginTotal')}
            </div>
            <div>
              <span className="count">{calcTotalMetric(remainingCu)}</span>
              <span className="unit">{RemainingAmountUnit}</span>
              <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
              <span className="count">{calcTotalMetric(totalCu)}</span>
              <span className="unit">{TotalAmountUnit}</span>
            </div>
          </div>
        ) : (
          <>
            <div className="item">
              <div className="lable">
                {intl('saenext.components.overview.PackBalance.CpuMarginTotal')}
              </div>
              <div>
                <span className="count">{calcTotalMetric(remainingCpu)}</span>
                <span className="unit">
                  {RemainingAmountUnit.startsWith('OneThousand')
                    ? intl('saenext.pages.overview.PackBalance.NuclearScore')
                    : intl('saenext.pages.overview.PackBalance.Nuclear')}
                </span>
                <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
                <span className="count">{calcTotalMetric(totalCpu)}</span>
                <span className="unit">
                  {TotalAmountUnit.startsWith('OneThousand')
                    ? intl('saenext.pages.overview.PackBalance.NuclearScore')
                    : intl('saenext.pages.overview.PackBalance.Nuclear')}
                </span>
              </div>
            </div>
            <div className="item">
              <div className="lable">
                {intl('saenext.components.overview.PackBalance.MemMarginTotal')}
              </div>
              <div>
                <span className="count">{calcTotalMetric(remainingMem)}</span>
                <span className="unit">
                  {RemainingAmountUnit.startsWith('OneThousand')
                    ? intl('saenext.pages.overview.PackBalance.GibPoints')
                    : intl('saenext.pages.overview.PackBalance.Gib')}
                </span>
                <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
                <span className="count">{calcTotalMetric(totalMem)}</span>
                <span className="unit">
                  {TotalAmountUnit.startsWith('OneThousand')
                    ? intl('saenext.pages.overview.PackBalance.GibPoints')
                    : intl('saenext.pages.overview.PackBalance.Gib')}
                </span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
