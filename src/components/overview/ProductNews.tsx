import React from 'react';
import { intl, Button, Icon, Timeline } from '@ali/cnd';
import { get } from 'lodash';
import CachedData from '~/cache/common';

type Props = {
  title: string;
};
const { Item: TimelineItem } = Timeline;

const news = [
  {
    id: 1,
    title: intl('saenext.pages.overview.ProductNews.SeamlessMigrationOfMicroserviceApplications'),
    time: '2023-09',
    state: 'done',
  },
  {
    id: 2,
    title: intl('saenext.pages.overview.ProductNews.MicroservicesAutomaticallyIntegratesTheNew'),
    time: '2023-09',
    state: 'done',
  },
  {
    id: 3,
    title: intl('saenext.pages.overview.ProductNews.SaeSupportsCreatingAndPublishing'),
    time: '2023-08',
    state: 'done',
  },
];

export default (props: Props) => {
  const { title } = props;
  const _news = get(window, 'ALIYUN_CONSOLE_GLOBAL.overview.news', news);

  return (
    <div className="news w-100">
      <div className="title mb">
        <span>{title}</span>
        <Button
          text
          type="primary"
          onClick={() => {
            window.open(CachedData.confLink('help:sae:release-notes'), '_blank');
          }}
        >
          <span>{intl('saenext.pages.overview.ProductNews.More')}</span>
          <Icon type="forward" />
        </Button>
      </div>
      <div className="wrapper w-100">
        <Timeline>
          {
            _news.map((item) => (
              <TimelineItem
                title={
                  <div style={{ fontWeight: 'normal' }}>{item.title}</div>
                }
                time={
                  <div className='mb-s'>{item.time}</div>
                }
                // @ts-ignore
                state={item.state || 'done'}
              />
            ))
          }
        </Timeline>
      </div>
    </div>
  );
};
