/* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Tab, CndTable } from '@ali/cnd';
import { TOPN_TABS, TOPN_METRIC, TOPN_EVENTS } from './constants';

const tabs = [...TOPN_TABS];

type Keys = typeof TOPN_METRIC[keyof typeof TOPN_METRIC]

export default (props) => {
  const { regionId } = props;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [currentKey, setCurrentKey] = useState<Keys>(TOPN_METRIC.AVAILABILITY);

  useEffect(() => {
    getWebTopnAppByMetric(currentKey);
  }, [regionId]);

  const getWebTopnAppByMetric = async (key) => {
    setLoading(true);
    const { Data = [] } = await TOPN_EVENTS[key].handle({
      RegionId: regionId,
      Limit: 10,
      AppSource: 'micro_service',
      CpuStrategy: 'always',
      // @ts-ignore
      ...(TOPN_EVENTS[key].params || {}),
    }, (err, data, callback) => {
      setLoading(false);
      // callback && callback();
    });
    setLoading(false);
    setDataSource(Data);
  };

  return (
    <Tab
      size="small"
      shape="capsule"
      className='mt-s'
      excessMode="slide"
      activeKey={currentKey}
      onChange={(key) => {
        setCurrentKey(key as Keys);
        getWebTopnAppByMetric(key);
      }}
    >
      {
        tabs.map((item) => {
          return (
            <Tab.Item title={item.tab} key={item.key}>
              <div
                className='mt mb-s'
                style={{ fontSize: 12, color: '#808080' }}
              >
                {TOPN_EVENTS[item.key].tips}
              </div>
              <CndTable
                loading={loading}
                columns={TOPN_EVENTS[item.key].columns}
                dataSource={dataSource}
                style={{ minHeight: 224 }}
              />
            </Tab.Item>
          );
        })
      }
    </Tab>
  );
};

