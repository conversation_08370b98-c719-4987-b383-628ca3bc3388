import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect, useContext } from 'react';
import { Tab } from '@ali/cnd';
import { SAE_TYPES } from './constants';
import RecentFullIndicator from './RecentFullIndicator';
import RecentCoreIndicator from './RecentCoreIndicator';
import FeatureContext from '~/utils/featureContext';

type Keys = (typeof SAE_TYPES)[keyof typeof SAE_TYPES];

export default (props) => {
  const { regionId, namespaceId, refreshIndex, isCurrentChart, timeoutCallback, appTypeCallback, startTime, endTime, timeUnit, appVersions,diskSizeEnable } = props;
  const [currentKey, setCurrentKey] = useState<Keys>(SAE_TYPES.ALL);

  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  if (!EnableWebApplication) {
    return (
      <RecentCoreIndicator
        regionId={regionId}
        namespaceId={namespaceId}
        appSource="micro_service"
        cpuStrategy="always"
        refreshIndex={refreshIndex}
        isCurrentChart={isCurrentChart}
        isCurrentAppType
        timeoutCallback={timeoutCallback}
        startTime={startTime}
        endTime={endTime}
        timeUnit={timeUnit}
        appVersions={appVersions}
        diskSizeEnable={diskSizeEnable}
      />
    );
  }

  return (
    <Tab
      shape="capsule"
      className="mt-s"
      excessMode="slide"
      activeKey={currentKey}
      onChange={(key) => {
        setCurrentKey(key as Keys);
        appTypeCallback && appTypeCallback(key);
      }}
      unmountInactiveTabs
    >
      <Tab.Item
        title={intl('saenext.pages.overview.RecentAppsContent.AllApplicationTypes')}
        key={SAE_TYPES.ALL}
      >
        <RecentFullIndicator
          regionId={regionId}
          namespaceId={namespaceId}
          appSource="micro_service,web"
          cpuStrategy=""
          refreshIndex={refreshIndex}
          isCurrentChart={isCurrentChart}
          isCurrentAppType={currentKey === SAE_TYPES.ALL}
          timeoutCallback={timeoutCallback}
          startTime={startTime}
          endTime={endTime}
          timeUnit={timeUnit}
        />
      </Tab.Item>
      <Tab.Item
        title={intl('saenext.components.overview.RecentAppsContent.WebApplications')}
        key={SAE_TYPES.WEB_REQUEST}
      >
        <RecentFullIndicator
          regionId={regionId}
          namespaceId={namespaceId}
          appSource="web"
          cpuStrategy=""
          refreshIndex={refreshIndex}
          isCurrentChart={isCurrentChart}
          isCurrentAppType={currentKey === SAE_TYPES.WEB_REQUEST}
          timeoutCallback={timeoutCallback}
          startTime={startTime}
          endTime={endTime}
          timeUnit={timeUnit}
        />
      </Tab.Item>
      <Tab.Item
        title={intl('saenext.pages.overview.RecentAppsContent.MicroserviceApplications')}
        key={SAE_TYPES.MICRO_SERVICE}
      >
        <RecentCoreIndicator
          regionId={regionId}
          namespaceId={namespaceId}
          appSource="micro_service"
          cpuStrategy=""
          refreshIndex={refreshIndex}
          isCurrentChart={isCurrentChart}
          isCurrentAppType={currentKey === SAE_TYPES.MICRO_SERVICE}
          timeoutCallback={timeoutCallback}
          startTime={startTime}
          endTime={endTime}
          timeUnit={timeUnit}
          appVersions={appVersions}
          diskSizeEnable={diskSizeEnable}
        />
      </Tab.Item>
    </Tab>
  );
};
