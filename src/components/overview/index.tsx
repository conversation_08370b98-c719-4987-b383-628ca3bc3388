import React, { useState, useContext } from 'react';
import FeatureContext from '~/utils/featureContext';
import { intl } from '@ali/cnd';
import ConsumeGrid from './ConsumeGrid';
import RecentYearChart from './RecentYearChart';
import TrendingApps from './TrendingApps';
import ActionsGrid from './ActionsGrid';
// import PackBalance from './PackBalance';
import RuntimeEnvironment from './RuntimeEnvironment';
import TaskFramework from './TaskFramework';
import HighlightPractices from './HighlightPractices';
import ProductNews from './ProductNews';
import AnnouncementBar from '../shared/AnnouncementBar';
import MultiVersionGuide from './MultiVersionGuide';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';
import YundunCenter from './YundunCenter';
import PackBalance from '~/components/overview/pack-balance';
import { AppVersionMap } from '~/constants/application';
import { confFeature } from '@alicloud/console-one-conf';
import { get } from 'lodash';
import moment from 'moment';

const AppVersionOptions = Object.keys(AppVersionMap).map(key => ({
  label: (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <img src={AppVersionMap[key].icon} width={14} />
      <span style={{ marginLeft: 4 }}>{AppVersionMap[key].label}</span>
    </div>
  ),
  value: key,
}));

const OverviewPage = () => {
  const { feature, inDebtData, webFeature } = useContext(FeatureContext);
  const [appCount, setAppCount] = useState(0);
  const [taskCount, setTaskCount] = useState(0);
  // 支持多版的本老用户
  const { enableNewSaeVersion } = feature;
  // 是否多版本新用户 --- 轻量版+专业版
  const AccountOpenTime = webFeature?.AccountOpenTime;
  const multipleVersionsTime = get(window, 'ALIYUN_CONSOLE_GLOBAL.multipleVersionsTime');
  const isMultipleNewAccount =
    AccountOpenTime && multipleVersionsTime
      ? AccountOpenTime > moment(multipleVersionsTime).valueOf()
      : false;
  // 多版本新用户加白则支持标准版
  const multipleNewAccountStdEnable = confFeature('multipleNewAccount_std_enable');

  const diskSize_enable = confFeature('diskSize_enable');
  const updateRegionSum = sum => {
    const _appCount = sum?.apps?.application?.count || 0;
    const _taskCount = sum?.tasks?.taskTemplates || 0;
    setAppCount(_appCount);
    setTaskCount(_taskCount);
  };

  return (
    <>
      <AnnouncementBar className="mb-s" />
      <div className="overview">
        <div className="panel-sidebar">
          {appCount === 0 && taskCount === 0 && (isMultipleNewAccount && !enableNewSaeVersion) && (
            <MultiVersionGuide multipleNewAccountStdEnable={multipleNewAccountStdEnable} />
          )}
          <YundunCenter title={intl('saenext.components.overview.SecurityCenter')} />
          <ConsumeGrid
            title={intl('saenext.pages.overview.BasicData')}
            updateRegionSum={updateRegionSum}
            enableNewSaeVersion={enableNewSaeVersion}
            isMultipleNewAccount={isMultipleNewAccount}
            multipleNewAccountStdEnable={multipleNewAccountStdEnable}
            diskSizeEnable={diskSize_enable}
          />
          <RecentYearChart
            title={intl('saenext.pages.overview.UsageOverviewInTheLast')}
            enableNewSaeVersion={enableNewSaeVersion}
            isMultipleNewAccount={isMultipleNewAccount}
            multipleNewAccountStdEnable={multipleNewAccountStdEnable}
            AppVersionOptions={AppVersionOptions}
            diskSizeEnable={diskSize_enable}
          />
          <TrendingApps
            title={intl('saenext.pages.overview.TopApplications')}
          />
        </div>
        <div className="insight-sidebar">
          <ActionsGrid title={intl('saenext.pages.overview.CommonInformation')} />
          <PackBalance title={intl('saenext.pages.overview.ResourcePackage')} />
          <RuntimeEnvironment title={intl('saenext.pages.overview.CommonOperatingEnvironments')} />
          <TaskFramework title={intl('saenext.pages.overview.CommonTaskFramework')} />
          <If condition={CachedData.lang === 'zh-CN'}>
            <HighlightPractices title={intl('saenext.pages.overview.BestPractices')} />
            <ProductNews title={intl('saenext.pages.overview.CoreProductTrends')} />
          </If>
        </div>
      </div>
    </>
  );
};
export default OverviewPage;
