import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useContext, useState } from 'react';
import { Select, Tab } from '@ali/cnd';
import { REGIONS } from './constants';
import TrendWeb1Content from './TrendWeb1Content';
import TrendWeb2Content from './TrendWeb2Content';
import TrendMicroContent from './TrendMicroContent';
import FeatureContext from '~/utils/featureContext';
import If from '~/components/shared/If';

type Props = {
  title: string;
};

const regions = [...REGIONS.slice(1)];

export default (props: Props) => {
  const { title } = props;
  const [regionId, setRegionId] = useState(regions[0].value);

  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;
  const [curActiveKey, setCurActiveKey] = useState('request');

  return (
    <div className="trend-app w-100">
      <div className="title mb-s">
        <span>{title}</span>
        <Select
          size="small"
          className="ml"
          value={regionId}
          dataSource={regions}
          onChange={(regionId: string) => {
            setRegionId(regionId);
          }}
        />
      </div>
      <div className="wrapper w-100">
        <If condition={EnableWebApplication}>
          <Tab
            size="small"
            shape="pure"
            excessMode="slide"
            activeKey={curActiveKey}
            onChange={key => {
              setCurActiveKey(key);
            }}
          >
            <Tab.Item
              title={intl('saenext.components.overview.TrendingApps.WebApplications')}
              key="request"
            >
              <TrendWeb2Content />
            </Tab.Item>
            <Tab.Item
              title={intl('saenext.pages.overview.TrendingApps.MicroserviceApplications')}
              key="micro"
            >
              <TrendMicroContent regionId={regionId} />
            </Tab.Item>
          </Tab>
        </If>
        <If condition={!EnableWebApplication}>
          <TrendMicroContent regionId={regionId} />
        </If>
      </div>
    </div>
  );
};
