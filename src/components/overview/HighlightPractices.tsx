import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useContext } from 'react';
import { Button, Icon } from '@ali/cnd';
import { get } from 'lodash';
import FeatureContext from '~/utils/featureContext';
import CachedData from '~/cache/common';

type Props = {
  title: string;
};

const practices = [
  {
    case: intl('saenext.pages.overview.HighlightPractices.SpringcloudHowToSeamlesslyMigrate'),
    href: CachedData.confLink('help:sae:host-spring-cloud-applications-to-sae'),
  },
  {
    case: intl('saenext.pages.overview.HighlightPractices.DeployMultiLanguageWebApplications'),
    href: CachedData.confLink('help:sae:deploy-through-the-source-code'),
  },
  {
    case: intl('saenext.pages.overview.HighlightPractices.BestPracticesForSaeElastic'),
    href: CachedData.confLink('help:sae:sae-auto-scaling-best-practices'),
  },
];

export default (props: Props) => {
  const { title } = props;

  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  const _practices = get(window, 'ALIYUN_CONSOLE_GLOBAL.overview.practices', practices).filter(item => EnableWebApplication || !item?.case?.includes('Web'));

  return (
    <div className="practices w-100">
      <div className="title mb">
        <span>{title}</span>
        <Button
          text
          type="primary"
          onClick={() => {
            window.open(CachedData.confLink('help:sae:use-cases'), '_blank');
          }}
        >
          <span>{intl('saenext.pages.overview.HighlightPractices.More')}</span>
          <Icon type="forward" />
        </Button>
      </div>
      <div className="wrapper w-100">
        {_practices.map((practice) => (
          <div
              key={practice.href}
              className="next-practice"
            >
            <Button text type="primary" onClick={() => window.open(practice.href, '_blank')}>
              <span className="text">{practice.case}</span>
              <Icon type="external_link" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
