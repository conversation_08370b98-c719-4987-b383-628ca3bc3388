import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
// cpu /内存 两个指标
import React, { useState, useEffect, useContext } from 'react';
import { Loading, Radio } from '@ali/cnd';
import { Wline, COLORS, ChartProvider } from '@alife/aisc-widgets';
import moment from 'moment';
import { formatNumber, isInteger } from '~/utils/global';
import services from '~/services';
import { REGIONS, SAE_TYPES } from './constants';
import { map, get, includes, noop, isEmpty, has } from 'lodash';
import FeatureContext from '~/utils/featureContext';
import CachedData from '~/cache/common';

const regions = [...REGIONS.slice(1)];
const regionIds = map(regions, (region) => region.value).join(',');

const RadioGroup = Radio.Group;
const YAxisType = {
  cu: 'cu',
  cpu: 'cpu',
  memory: 'memory',
  request: 'request',
  traffic: 'traffic',
  diskSize: 'diskSize',
};
const Options = {
  grid: true,
  // @ts-ignore
  lineColors: [COLORS.widgetsColorPurple, COLORS.widgetsColorGreen],
  spline: true,
  legend: {
    visible: true,
    position: 'top',
    align: 'left',
    marker: {
      symbol: 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 3,
          lineWidth: 6,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
    nameFormatter: (v) => {
      switch (v) {
        case intl('saenext.components.overview.RecentCoreIndicator.CuResourceUsage'):
          return intl('saenext.components.overview.RecentCoreIndicator.VUnitCu', { v: v });
        case intl('saenext.components.overview.RecentCoreIndicator.IdleCpuResourceUsage'):
        case intl('saenext.components.overview.RecentCoreIndicator.ActiveCpuResourceUsage'):
        case intl('saenext.pages.overview.RecentCoreIndicator.CpuResourceUsage'):
          return intl('saenext.pages.overview.RecentCoreIndicator.VUnitUnit', {
            v: v,
            unit: intl('saenext.pages.overview.RecentCoreIndicator.Nuclear'),
          });
        case intl('saenext.pages.overview.RecentFullIndicator.MemoryResourceUsage'):
          return intl('saenext.pages.overview.RecentCoreIndicator.VUnitUnit', {
            v: v,
            unit: intl('saenext.pages.overview.RecentCoreIndicator.Gib'),
          });
        case intl('saenext.components.overview.RecentCoreIndicator.TemporaryDiskUsage'):
          return intl('saenext.pages.overview.RecentCoreIndicator.VUnitUnit', {
            v: v,
            unit: intl('saenext.pages.overview.RecentCoreIndicator.Gib'),
          });

        default:
          return v;
      }
    },
  },
  area: true,
  xAxis: {
    type: 'timeCat',
    mask: 'YYYY/MM',
    tickCount: 12,
    // rotate: 0.45,
  },
  yAxis: {
    min: 0,
    labelFormatter: formatNumber,
  },

  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
    valueFormatter: formatNumber,
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM');
    },
  },
};
const versionKeyMap = {
  lite: {
    cu: 'LiteCuUsed',
    cpu: 'LiteCpuUsed',
    idleCpu: 'LiteIdleCpuUsed',
    memory: 'LiteMemoryUsed',
    diskSize: 'LiteEphemeralStorageUsed',
  },
  std: {
    cu: 'StdCuUsed',
    cpu: 'Cpu',
    idleCpu: 'IdleCpu',
    memory: 'Memory',
    diskSize: 'EphemeralStorageUsed',
  },
  pro: {
    cu: 'ProCuUsed',
    cpu: 'ProCpuUsed',
    idleCpu: 'ProIdleCpuUsed',
    memory: 'ProMemoryUsed',
    diskSize: 'ProEphemeralStorageUsed',
  },
};

export default (props) => {
  const {
    regionId,
    namespaceId,
    appSource,
    cpuStrategy,
    refreshIndex,
    isCurrentChart,
    isCurrentAppType,
    timeoutCallback = noop,
    startTime,
    endTime,
    timeUnit,
    appVersions = [],
    diskSizeEnable,
  } = props;
  const [measures, setMeasures] = useState([]);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState(Options);
  const [yAxisType, setYAxisType] = useState(YAxisType.cu);
  const { feature } = useContext(FeatureContext);
  const isAllocateIdle = get(feature, 'idle', false);
  useEffect(() => {
    if (startTime && endTime) {
      initChart();
    }
  }, [
    regionId,
    namespaceId,
    appSource,
    cpuStrategy,
    refreshIndex,
    startTime,
    endTime,
    timeUnit,
    appVersions.length,
  ]);
  const initChart = async () => {
    if (!isCurrentChart) return;
    if (!isCurrentAppType) return;
    setLoading(true);
    const oneYearAgo = moment().subtract(1, 'years').add(1, 'month').startOf('month');
    const customErrorHandle = (err, data, callback) => {
      const code = get(err, 'code');
      const mseeage = get(err, 'response.data.message');
      const timeoutCodes = ['ServiceUnavailable', 'ApiReadTimeOut'];
      const timeoutMsgs = [
        'The request has failed due to a temporary failure of the server.',
        intl('saenext.components.overview.ConsumeGrid.TheRequestedApiProcessingTimed'),
      ];

      const isTimeout = includes(timeoutCodes, code) && includes(timeoutMsgs, mseeage);
      setLoading(false);
      if (isTimeout) {
        // 处理超时的逻辑
        const chart = appSource !== 'job' ? 'APPS' : 'TASKS';
        let appType = '';
        switch (appSource) {
          case 'web':
            appType = SAE_TYPES.WEB_REQUEST;
            break;
          case 'micro_service':
            appType = SAE_TYPES.MICRO_SERVICE;
            break;
          case 'micro_service,web':
            appType = SAE_TYPES.ALL;
            break;
        }
        let key = isEmpty(appType) ? chart : `${chart}-${appType}`;
        timeoutCallback && timeoutCallback({ [key]: true });
        return;
      }
      // callback && callback();
    };

    const data = await services.getUserMeasure(
      {
        RegionId: regionId,
        RegionIds: regionId ? regionId : regionIds,
        NamespaceId: namespaceId === 'all' ? '' : namespaceId,
        AppSource: appSource,
        CpuStrategy: cpuStrategy,
        StartTime: startTime,
        EndTime: endTime,
        WindowType: timeUnit,
        NewSaeVersion: appVersions.join(','),
      },
      { region: regionId },
      customErrorHandle,
    );

    const { Measures = [] } = data.Data;

    const [_data, _options] = renderWithYAxis(yAxisType, Measures);
    _options['xAxis'] = {
      type: 'timeCat',
      mask: timeUnit === 'day' ? 'YYYY/MM/DD' : 'YYYY/MM',
    };
    _options['tooltip']['titleFormatter'] = (v, t) => {
      return moment(t.x).format(timeUnit === 'day' ? 'YYYY/MM/DD' : 'YYYY/MM');
    };
    // @ts-ignore
    setOptions(_options);
    // @ts-ignore
    setData(_data);
    setMeasures(Measures);
    setLoading(false);
  };
  const renderWithYAxis = (yAxisType, measures) => {
    let _data = [];
    // let _yAxis = [{ min: 0, labelFormatter: formatNumber }];
    switch (yAxisType) {
      case YAxisType.cu:
        _data = [
          {
            name: intl('saenext.components.overview.RecentCoreIndicator.CuResourceUsage'),
            type: 'line',
            // yAxis: 0,
            data: measures.map((r) => {
              return [parseInt(r.StartTime) * 1000, Number(r.CuUsed)];
            }),
          },
        ];

        break;
      case YAxisType.cpu:
        _data =
          isAllocateIdle && appSource !== 'job'
            ? [
                {
                  name: intl(
                    'saenext.components.overview.RecentCoreIndicator.IdleCpuResourceUsage',
                  ),
                  type: 'line',
                  // yAxis: 0,
                  data: measures.map((r) => {
                    // IdleCpu 有不返回的情况
                    const _idleCpu = !r.IdleCpu ? 0 : Number(r.IdleCpu);
                    return [
                      parseInt(r.StartTime) * 1000,
                      isInteger(Number(_idleCpu) / 60)
                        ? Number(_idleCpu) / 60
                        : parseFloat((Number(_idleCpu) / 60).toFixed(2)),
                    ];
                  }),
                },
                {
                  name: intl(
                    'saenext.components.overview.RecentCoreIndicator.ActiveCpuResourceUsage',
                  ),
                  type: 'line',
                  // yAxis: 1,
                  data: measures.map((r) => {
                    return [
                      parseInt(r.StartTime) * 1000,
                      isInteger(Number(r.Cpu) / 60)
                        ? Number(r.Cpu) / 60
                        : parseFloat((Number(r.Cpu) / 60).toFixed(2)),
                    ];
                  }),
                },
              ]
            : [
                {
                  name: intl('saenext.pages.overview.RecentFullIndicator.CpuResourceUsage'),
                  type: 'line',
                  // yAxis: 0,
                  data: measures.map((r) => {
                    return [
                      parseInt(r.StartTime) * 1000,
                      isInteger(Number(r.Cpu) / 60)
                        ? Number(r.Cpu) / 60
                        : parseFloat((Number(r.Cpu) / 60).toFixed(2)),
                    ];
                  }),
                },
              ];

        // _yAxis = (isAllocateIdle && appSource !== 'job') ? [
        //   { min: 0, labelFormatter: formatNumber },
        //   { min: 0, labelFormatter: formatNumber },
        // ] : [
        //   { min: 0, labelFormatter: formatNumber }
        // ];
        break;
      case YAxisType.memory:
        _data = [
          {
            name: intl('saenext.pages.overview.RecentFullIndicator.MemoryResourceUsage'),
            type: 'line',
            // yAxis: 0,
            data: measures.map((r) => {
              return [
                parseInt(r.StartTime) * 1000,
                isInteger(Number(r.Memory) / 60)
                  ? Number(r.Memory) / 60
                  : parseFloat((Number(r.Memory) / 60).toFixed(2)),
              ];
            }),
          },
        ];

        break;
      case YAxisType.diskSize:
        _data = [
          {
            name: intl('saenext.components.overview.RecentCoreIndicator.TemporaryDiskUsage'),
            type: 'line',
            data: measures.map((r) => {
              return [
                parseInt(r.StartTime) * 1000,
                isInteger(Number(r.EphemeralStorageUsed) / 60)
                  ? Number(r.EphemeralStorageUsed) / 60
                  : parseFloat((Number(r.EphemeralStorageUsed) / 60).toFixed(2)),
              ];
            }),
          },
        ];

        break;
    }
    const _options = {
      ...Options,
      // yAxis: _yAxis,
    };
    return [_data, _options];
  };

  const yAxisTypeChange = (_yAxisType) => {
    setYAxisType(_yAxisType);
    const [_data, _options] = renderWithYAxis(_yAxisType, measures);
    // @ts-ignore
    setOptions(_options);
    // @ts-ignore
    setData(_data);
  };

  return (
    <>
      <RadioGroup shape="button" value={yAxisType} className="mb mt-s" onChange={yAxisTypeChange}>
        <Radio key={YAxisType.cu} value={YAxisType.cu}>
          {intl('saenext.components.overview.RecentCoreIndicator.CuResourceUsage')}
        </Radio>
        <Radio key={YAxisType.cpu} value={YAxisType.cpu}>
          {intl('saenext.components.overview.RecentCoreIndicator.CpuResourceUsage')}
        </Radio>
        <Radio key={YAxisType.memory} value={YAxisType.memory}>
          {intl('saenext.components.overview.RecentCoreIndicator.MemoryResourceUsage')}
        </Radio>
        {diskSizeEnable && (
          <Radio key={YAxisType.diskSize} value={YAxisType.diskSize}>
            {intl('saenext.components.overview.RecentCoreIndicator.TemporaryDiskUsage')}
          </Radio>
        )}
      </RadioGroup>
      <Loading visible={loading} className="full-width">
        <ChartProvider language={CachedData.lang === 'zh-CN' ? 'zh-cn' : 'en-us'}>
          <Wline
            data={data}
            height={240}
            // @ts-ignore
            config={options}
          />
        </ChartProvider>
      </Loading>
    </>
  );
};
