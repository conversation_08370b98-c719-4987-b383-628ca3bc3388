import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React from 'react';
import { Button, Balloon, Copy } from '@ali/cnd';
import { CommonBase } from './assets';
import CachedData from '~/cache/common';
import { removeUserView } from '~/utils/hacker';
import UserViewButton from './UserViewButton';

type Props = {
  title: string;
};

const actions = [
  {
    title: intl('saenext.pages.overview.ActionsGrid.QuickStart'),
    href: CachedData.confLink('help:sae:get-started-with-sae'),
  },
  {
    title: intl('saenext.pages.overview.ActionsGrid.ProductBilling'),
    href: CachedData.confLink('help:sae:measurement-method'),
  },
  {
    title: 'OpenAPI',
    href: CachedData.confLink('help:sae:api-overview'),
  },
  {
    title: intl('saenext.pages.overview.ActionsGrid.JoinACommunicationGroup'),
  },
];

export default (props: Props) => {
  const { title } = props;
  const MAIN_ACCOUNT_PK = CachedData.getAliyunConsoleConfig('MAIN_ACCOUNT_PK');

  return (
    <div className="actions w-100">
      <div className="title mb">{title}</div>
      <div className="mb-s">
        <span className="text-description">
          {intl('saenext.components.overview.ActionsGrid.PrimaryAccountId')}
        </span>
        <span className="ml-s">
          <Copy text={MAIN_ACCOUNT_PK}>{MAIN_ACCOUNT_PK}</Copy>
        </span>
      </div>
      {CachedData.isUserView() ? (
        <div className="wrapper w-100">
          <Button
            warning
            type="primary"
            className="next-action"
            onClick={() => {
              removeUserView();
            }}
          >
            {intl('saenext.components.overview.ActionsGrid.ClearUserPerspective')}
          </Button>
          <div className="next-action">
            <UserViewButton />
          </div>
        </div>
      ) : null}

      <div className="wrapper w-100">
        {actions.map((action) => {
          const { href } = action;
          return href && href.length > 0 ? (
            <Button
              className="next-action"
              onClick={() => {
                window.open(href, '_blank');
              }}
            >
              {action.title}
            </Button>
          ) : (
            <Balloon
              trigger={<Button className="next-action">{action.title}</Button>}
              align="t"
              alignEdge
              closable={false}
              triggerType="click"
            >
              {CommonBase.dingding}
              <p style={{ textAlign: 'center' }}>
                {intl('saenext.pages.overview.ActionsGrid.DingtalkGroupNumber')}
              </p>
            </Balloon>
          );
        })}
      </div>
    </div>
  );
};
