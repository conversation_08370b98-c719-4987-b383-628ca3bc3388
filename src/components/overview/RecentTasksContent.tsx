/* eslint react/prop-types: 0 */
import React from 'react';
import RecentCoreIndicator from './RecentCoreIndicator';

export default (props) => {
  const { regionId, namespaceId, refreshIndex, isCurrentChart, timeoutCallback, startTime, endTime, timeUnit } = props;

  return (
    <RecentCoreIndicator
      regionId={regionId}
      namespaceId={namespaceId}
      appSource='job'
      cpuStrategy='always'
      refreshIndex={refreshIndex}
      isCurrentChart={isCurrentChart}
      isCurrentAppType={true}
      timeoutCallback={timeoutCallback}
      startTime={startTime}
      endTime={endTime}
      timeUnit={timeUnit}
    />
  );
};
