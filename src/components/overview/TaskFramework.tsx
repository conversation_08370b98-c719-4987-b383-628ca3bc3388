/* eslint react/prop-types: 0 */
import React, { useContext } from 'react';
import { Framework } from './assets';
import { ConsoleContext } from '@ali/cnd';
import CachedData from '~/cache/common';

type Props = {
  title: string;
};



export default (props: Props) => {
  const { title } = props;
  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const isPre = window.location.host.startsWith('pre-')?'pre-':'';
  const origin = isPre
      ? CachedData.confLink('feature:pre-saenext:url')
      : CachedData.confLink('feature:saenext:url');

  const frameworks = [
    {
      libary: 'XXL-JOB',
      icon: Framework.xxl,
      href: `${origin}/${regionId}/job-list`,
    },
    {
      libary: 'Elastic-Job',
      icon: Framework.elastic,
      href: `${origin}/${regionId}/job-list`,
    },
    {
      libary: 'Spring Task',
      icon: Framework.springtask,
      href: `${origin}/${regionId}/job-list`,
    },
    {
      libary: 'K8S Cronjob',
      icon: Framework.k8s,
      href: `${origin}/${regionId}/job-list`,
    },
    {
      libary: 'Crontab',
      icon: Framework.crontab,
      href: `${origin}/${regionId}/job-list`,
    }
  ];
  return (
    <div className="framework w-100">
      <div className='title mb-s'>{title}</div>
      <div className="wrapper">
        {
          frameworks.map(framework => (
            <div
              className="next-framework"
              onClick={() => {
                window.open(framework.href);
              }}
            >
              <span className='mr'>{framework.icon}</span>
              <span>{framework.libary}</span>
            </div>
          ))
        }
      </div>
    </div>
  );
};
