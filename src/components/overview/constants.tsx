import React from 'react';
import { intl, Balloon } from '@ali/cnd';
import moment from 'moment';
import services from '~/services';
import { get, map, noop } from 'lodash';
import { isNumber, isInteger } from '~/utils/global';
import MicroAppLink from '../shared/MicroAppLink';
import { AppVersionMap } from '~/constants/application';

export const REGIONS = [
  { label: intl('saenext.pages.overview.constants.AllRegions'), value: '' },
  ...map(get(window, 'ALIYUN_CONSOLE_CONFIG.REGIONS') || [], (region) => ({
    label: region.name,
    value: region.regionId,
  })),
];

const toCpu = (target, key, fixed = 1) => {
  const core = {
    value: get(target, key, '-'),
    unit: intl('saenext.pages.overview.constants.Nuclear'),
  };
  const value = get(target, key, '-');
  if (!isNumber(value)) return core;
  // 后端返回是 核*分
  if (value >= 60) {
    core.unit = intl('saenext.pages.overview.constants.Nuclear');
    const _value = value / 60;
    core.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return core;
  }

  core.value = isInteger(value) ? value : parseFloat(value.toFixed(fixed));
  core.unit =
    core.value === 0
      ? intl('saenext.pages.overview.constants.Nuclear')
      : intl('saenext.pages.overview.constants.NuclearScore');

  return core;
};

const toMemory = (target, key, fixed = 1) => {
  const memory = {
    value: get(target, key, '-'),
    unit: intl('saenext.pages.overview.constants.Gib'),
  };
  const value = get(target, key, '-');
  if (!isNumber(value)) return memory;
  // 后端返回是 GiB*分

  if (value >= 60) {
    memory.unit = intl('saenext.pages.overview.constants.Gib');
    const _value = value / 60;
    memory.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return memory;
  }

  memory.value = isInteger(value) ? value : parseFloat(value.toFixed(fixed));
  memory.unit =
    memory.value === 0
      ? intl('saenext.pages.overview.constants.Gib')
      : intl('saenext.pages.overview.constants.GibPoints');

  return memory;
};

const toDiskSize = (target, key, fixed = 1) => {
  const diskSize = {
    value: get(target, key, '-'),
    unit: intl('saenext.pages.overview.constants.Gib'),
  };
  const value = get(target, key, '-');
  if (!isNumber(value)) return diskSize;
  // 后端返回是 GiB*分

  if (value >= 60) {
    diskSize.unit = intl('saenext.pages.overview.constants.Gib');
    const _value = value / 60;
    diskSize.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return diskSize;
  }

  diskSize.value = isInteger(value) ? value : parseFloat(value.toFixed(fixed));
  diskSize.unit =
    diskSize.value === 0
      ? intl('saenext.pages.overview.constants.Gib')
      : intl('saenext.pages.overview.constants.GibPoints');

  return diskSize;
};

const toRequest = (target, key) => {
  const request = {
    value: get(target, key, '-'),
    unit: intl('saenext.pages.overview.constants.Times'),
  };
  const value = get(target, key, '-');
  if (!isNumber(value)) return request;

  // 千万，如果大于千万 返回百万
  if (value >= 10000000) {
    request.unit = intl('saenext.pages.overview.constants.MillionsOfTimes');
    request.value = Math.floor(value / 1000000);
    return request;
  }

  // 十万
  if (value >= 100000) {
    request.unit = intl('saenext.pages.overview.constants.TenThousandTimes');
    request.value = Math.floor(value / 10000);
    return request;
  }

  request.unit = intl('saenext.pages.overview.constants.Times');
  request.value = value;

  return request;
};

const toIobound = (target, key, fixed = 1) => {
  const iobound = {
    value: get(target, key, '-'),
    unit: 'GB',
  };
  const value = get(target, key, '-');
  if (!isNumber(value)) return iobound;

  // 后端返回的是 bytes
  // GB
  if (value >= 1024 * 1024 * 1024) {
    iobound.unit = 'GB';
    const _value = value / 1024 / 1024 / 1024;
    iobound.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return iobound;
  }
  // MB
  if (value >= 1024 * 1024) {
    iobound.unit = 'MB';
    const _value = value / 1024 / 1024;
    iobound.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return iobound;
  }

  // KB
  if (value >= 1024) {
    iobound.unit = 'KB';
    const _value = value / 1024;
    iobound.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return iobound;
  }

  iobound.value = isInteger(value) ? value : parseFloat(value.toFixed(fixed));
  iobound.unit = iobound.value === 0 ? 'GB' : 'Bytes';
  return iobound;
};

const toCu = (target, key, fixed = 1) => {
  const cu = {
    value: get(target, key, '-'),
    unit: 'CU',
  };
  const value = get(target, key, '-');
  if (!isNumber(value)) return cu;

  if (value >= *********) {
    cu.unit = intl('saenext.components.overview.constants.BillionCu');
    const _value = value / *********;
    cu.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return cu;
  }

  if (value >= 10000) {
    cu.unit = intl('saenext.components.overview.constants.Wancu');
    const _value = value / 10000;
    cu.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return cu;
  }

  return cu;
};

export const getConsumeData = ({
  isAllocateIdle = false,
  value = {} as any,
  EnableWebApplication = false,
  enableNewSaeVersion = false,
  isMultipleNewAccount = false,
  multipleNewAccountStdEnable = false,
  diskSizeEnable = false,
}) => {
  const appVersions =
    enableNewSaeVersion || (isMultipleNewAccount && multipleNewAccountStdEnable)
      ? ['lite', 'std', 'pro']
      : ['lite', 'pro'];
  return {
    apps: [
      {
        title: intl('saenext.components.overview.constants.ApplicationCuUsageThisMonth'),
        tips: '',
        theme: 'blue',
        count: toCu(value, 'apps.cu.count', 2).value,
        unit: toCu(value, 'apps.cu.count', 2).unit,
        visible: true,
      },
      {
        title: intl('saenext.pages.overview.constants.ApplicationCpuResourceUsageThis'),
        tips: '',
        count: toCpu(value, 'apps.cpu.count').value,
        unit: toCpu(value, 'apps.cpu.count').unit,
        visible: true,
        detailVisible: EnableWebApplication || isAllocateIdle,
        detail: {
          columns: EnableWebApplication
            ? [
                {
                  dataIndex: 'appType',
                  title: intl('saenext.pages.overview.constants.ApplicationType'),
                },
                {
                  dataIndex: 'usage',
                  title: intl('saenext.pages.overview.constants.ApplicationCpuResourceUsageThis'),
                },
              ]
            : [
                {
                  dataIndex: 'type',
                  title: intl('saenext.components.overview.constants.Type'),
                },
                {
                  dataIndex: 'usage',
                  title: intl('saenext.pages.overview.constants.ApplicationCpuResourceUsageThis'),
                },
              ],

          dataSource: isAllocateIdle
            ? [
                {
                  appType: intl('saenext.components.overview.constants.WebApplications'),
                  usage: `${toCpu(value, 'apps.cpu.webRequestCpu', 2).value} ${
                    toCpu(value, 'apps.cpu.webRequestCpu').unit
                  } (${intl('saenext.components.idle-monitor.InstanceStatusIndicator.Active')})`,
                  visible: EnableWebApplication,
                },
                {
                  appType: intl('saenext.components.overview.constants.WebApplications'),
                  usage: `${toCpu(value, 'apps.cpu.webRequestIdleCpu', 2).value} ${
                    toCpu(value, 'apps.cpu.webRequestIdleCpu').unit
                  } (${intl('saenext.components.idle-monitor.InstanceStatusIndicator.Idle')})`,
                  visible: EnableWebApplication,
                },
                {
                  appType: intl('saenext.pages.overview.constants.MicroserviceApplications'),
                  type: intl('saenext.components.overview.constants.Active'),
                  usage: EnableWebApplication
                    ? `${toCpu(value, 'apps.cpu.microCpu', 2).value} ${
                        toCpu(value, 'apps.cpu.microCpu').unit
                      } (${intl('saenext.components.idle-monitor.InstanceStatusIndicator.Active')})`
                    : `${toCpu(value, 'apps.cpu.microCpu', 2).value} ${
                        toCpu(value, 'apps.cpu.microCpu').unit
                      }`,
                },
                {
                  appType: intl('saenext.pages.overview.constants.MicroserviceApplications'),
                  type: intl('saenext.components.overview.constants.Idle'),
                  usage: EnableWebApplication
                    ? `${toCpu(value, 'apps.cpu.microIdleCpu', 2).value} ${
                        toCpu(value, 'apps.cpu.microIdleCpu').unit
                      } (${intl('saenext.components.idle-monitor.InstanceStatusIndicator.Idle')})`
                    : `${toCpu(value, 'apps.cpu.microIdleCpu', 2).value} ${
                        toCpu(value, 'apps.cpu.microIdleCpu').unit
                      }`,
                },
              ].filter((item) => item.visible !== false)
            : [
                {
                  appType: intl('saenext.components.overview.constants.WebApplications'),
                  usage: `${toCpu(value, 'apps.cpu.webRequestCpu', 2).value} ${
                    toCpu(value, 'apps.cpu.webRequestCpu').unit
                  }`,

                  visible: EnableWebApplication,
                },
                {
                  appType: intl('saenext.pages.overview.constants.MicroserviceApplications'),
                  usage: `${toCpu(value, 'apps.cpu.microCpu', 2).value} ${
                    toCpu(value, 'apps.cpu.microCpu').unit
                  }`,
                },
              ].filter((item) => item.visible !== false),
          tableWidth: 320,
          cellProps:
            isAllocateIdle && EnableWebApplication
              ? (rowIndex, colIndex) => {
                  if (rowIndex === 0 && colIndex === 0) {
                    return {
                      rowSpan: 2,
                    };
                  }
                  if (rowIndex === 2 && colIndex === 0) {
                    return {
                      rowSpan: 2,
                    };
                  }
                }
              : noop,
        },
      },
      {
        title: intl('saenext.pages.overview.constants.ApplicationMemoryResourceUsageThis'),
        tips: '',
        count: toMemory(value, 'apps.memory.count').value,
        unit: toMemory(value, 'apps.memory.count').unit,
        visible: true,
        detailVisible: EnableWebApplication,
        detail: {
          columns: [
            {
              dataIndex: 'appType',
              title: intl('saenext.pages.overview.constants.ApplicationType'),
            },
            {
              dataIndex: 'usage',
              title: intl('saenext.pages.overview.constants.ApplicationMemoryResourceUsageThis'),
            },
          ],

          dataSource: [
            {
              appType: intl('saenext.components.overview.constants.WebApplications'),
              usage: `${toMemory(value, 'apps.memory.webRequestMemory', 2).value} ${
                toMemory(value, 'apps.memory.webRequestMemory').unit
              }`,
            },
            {
              appType: intl('saenext.pages.overview.constants.MicroserviceApplications'),
              usage: `${toMemory(value, 'apps.memory.microMemory', 2).value} ${
                toMemory(value, 'apps.memory.microMemory').unit
              }`,
            },
          ],

          tableWidth: 320,
        },
      },
      {
        title: intl('saenext.components.overview.constants.TemporaryDiskUsageThisMonth'),
        tips: '',
        count: toDiskSize(value, 'apps.diskSize.count').value,
        unit: toDiskSize(value, 'apps.diskSize.count').unit,
        visible: diskSizeEnable,
        detailVisible: EnableWebApplication,
      },
      {
        title: intl('saenext.pages.overview.constants.NumberOfApplications'),
        tips: '',
        count: get(value, 'apps.application.count', '-'),
        unit: intl('general.unit.count'),
        visible: true,
        detailVisible: EnableWebApplication || enableNewSaeVersion || isMultipleNewAccount,
        detail: {
          columns: EnableWebApplication
            ? [
                {
                  dataIndex: 'appType',
                  title: intl('saenext.pages.overview.constants.ApplicationType'),
                },
                {
                  dataIndex: 'usage',
                  title: intl('saenext.pages.overview.constants.NumberOfApplications'),
                  cell: (val, index, record) => {
                    if (record.key === 'microApp') {
                      if (enableNewSaeVersion || isMultipleNewAccount) {
                        return (
                          <div className="flex">
                            {appVersions.map((item) => {
                              const valueKey = `apps.application.microApps${
                                item.charAt(0).toUpperCase() + item.slice(1)
                              }`;
                              return (
                                <Balloon
                                  trigger={
                                    <div className="flex items-center mr-l">
                                      <img src={AppVersionMap[item].icon} width={14} />
                                      <span style={{ marginLeft: 4 }}>{`${get(
                                        value,
                                        valueKey,
                                        '-',
                                      )}`}</span>
                                    </div>
                                  }
                                  closable={false}
                                >
                                  {AppVersionMap[item].label}
                                </Balloon>
                              );
                            })}
                          </div>
                        );
                      } else {
                        return val;
                      }
                    } else {
                      return val;
                    }
                  },
                },
              ]
            : [
                {
                  dataIndex: 'appVersion',
                  title: intl('saenext.components.overview.constants.ApplicationVersion'),
                },
                {
                  dataIndex: 'usage',
                  title: intl('saenext.pages.overview.constants.NumberOfApplications'),
                },
              ],

          dataSource: EnableWebApplication
            ? [
                {
                  appType: intl('saenext.components.overview.constants.WebApplications'),
                  usage: get(value, 'apps.application.webRequestApps', '-'),
                  key: 'webApp',
                },
                {
                  appType: intl('saenext.pages.overview.constants.MicroserviceApplications'),
                  usage: get(value, 'apps.application.microApps', '-'),
                  key: 'microApp',
                },
              ]
            : appVersions.map((item) => {
                const valueKey = `microApps${item.charAt(0).toUpperCase() + item.slice(1)}`;
                return {
                  appVersion: (
                    <div className="flex items-center">
                      <img src={AppVersionMap[item].icon} width={14} />
                      <span className="ml-xs">{AppVersionMap[item].label}</span>
                    </div>
                  ),
                  usage: get(value, `apps.application.${valueKey}`, '-'),
                  key: valueKey,
                };
              }),

          tableWidth: 320,
        },
      },
      {
        title: intl('saenext.pages.overview.constants.TotalInstancesMaximumInstances'),
        tips: '',
        count: `${get(value, 'apps.instances.count', '-')}/${get(
          value,
          'apps.maxInstances.count',
          '-',
        )}`,
        unit: intl('general.unit.count'),
        visible: true,
        detailVisible: EnableWebApplication,
        detail: {
          columns: [
            {
              dataIndex: 'appType',
              title: intl('saenext.pages.overview.constants.ApplicationType'),
            },
            {
              dataIndex: 'usage',
              title: intl('saenext.pages.overview.constants.TotalInstancesMaximumInstances'),
            },
          ],

          dataSource: [
            {
              appType: intl('saenext.components.overview.constants.WebApplications'),
              usage:
                `${get(value, 'apps.instances.webRequestInstances', '-')}/${get(
                  value,
                  'apps.maxInstances.webRequestMaxInstances',
                  '-',
                )}` + intl('general.unit.count'),
            },
            {
              appType: intl('saenext.pages.overview.constants.MicroserviceApplications'),
              usage:
                `
                ${get(value, 'apps.instances.microInstances', 0)}/
                ${get(value, 'apps.maxInstances.microMaxInstances', 0)}` +
                intl('general.unit.count'),
            },
          ],

          tableWidth: 320,
        },
      },
      {
        title: intl('saenext.pages.overview.constants.ElasticPolicy'),
        tips: '',
        count: get(value, 'apps.scaleRules.count', '-'),
        unit: intl('general.unit.count'),
        visible: true,
        detailVisible: EnableWebApplication,
        detail: {
          columns: [
            {
              dataIndex: 'appType',
              title: intl('saenext.pages.overview.constants.ApplicationType'),
            },
            {
              dataIndex: 'usage',
              title: intl('saenext.pages.overview.constants.ElasticPolicy'),
            },
          ],

          dataSource: [
            {
              appType: intl('saenext.pages.overview.constants.MicroserviceApplications'),
              usage:
                `${get(value, 'apps.scaleRules.microScaleRules', '-')}` +
                intl('general.unit.count'),
            },
          ],

          tableWidth: 320,
        },
      },
      {
        title: intl('saenext.pages.overview.constants.TotalInternetExportsThisMonth'),
        tips: '',
        count: toIobound(value, 'apps.iobound.count').value,
        unit: toIobound(value, 'apps.iobound.count').unit,
        visible: EnableWebApplication,
        detailVisible: EnableWebApplication,
        detail: {
          columns: [
            {
              dataIndex: 'appType',
              title: intl('saenext.pages.overview.constants.ApplicationType'),
            },
            {
              dataIndex: 'usage',
              title: intl('saenext.pages.overview.constants.TotalInternetExportsThisMonth'),
            },
          ],

          dataSource: [
            {
              appType: intl('saenext.components.overview.constants.WebApplications'),
              usage: `${toIobound(value, 'apps.iobound.webRequestIObound', 2).value} ${
                toIobound(value, 'apps.iobound.webRequestIObound').unit
              }`,
            },
          ],

          tableWidth: 320,
        },
      },
      {
        title: intl('saenext.pages.overview.constants.TotalNumberOfRequestsThis'),
        tips: '',
        count: toRequest(value, 'apps.request.count').value,
        unit: toRequest(value, 'apps.request.count').unit,
        visible: EnableWebApplication,
        detailVisible: EnableWebApplication,
        detail: {
          columns: [
            {
              dataIndex: 'appType',
              title: intl('saenext.pages.overview.constants.ApplicationType'),
            },
            {
              dataIndex: 'usage',
              title: intl('saenext.pages.overview.constants.TotalNumberOfRequestsThis'),
            },
          ],

          dataSource: [
            {
              appType: intl('saenext.components.overview.constants.WebApplications'),
              usage:
                `${get(value, 'apps.request.webRequestRequest', '-')}` +
                intl('saenext.pages.overview.constants.Times'),
            },
          ],

          tableWidth: 320,
        },
      },
    ],

    task: [
      {
        title: intl('saenext.components.overview.constants.TaskCuUsageThisMonth'),
        tips: '',
        count: toCu(value, 'tasks.taskCu', 2).value,
        unit: toCu(value, 'tasks.taskCu', 2).unit,
        visible: true,
      },
      {
        title: intl('saenext.pages.overview.constants.CpuResourceUsageForTasks'),
        tips: '',
        count: toCpu(value, 'tasks.taskCpu').value,
        unit: toCpu(value, 'tasks.taskCpu').unit,
        visible: true,
      },
      {
        title: intl('saenext.pages.overview.constants.TaskMemoryResourceUsageThis'),
        tips: '',
        count: toMemory(value, 'tasks.taskMemory').value,
        unit: toMemory(value, 'tasks.taskMemory').unit,
        visible: true,
      },
      {
        title: intl('saenext.pages.overview.constants.TotalNumberOfTaskTemplates'),
        tips: '',
        count: get(value, 'tasks.taskTemplates', '-'),
        unit: intl('general.unit.count'),
        visible: true,
      },
      {
        title: intl('saenext.pages.overview.constants.NumberOfTaskTemplatesEnabled'),
        tips: '',
        count: get(value, 'tasks.taskEnabledTemplates', '-'),
        unit: intl('general.unit.count'),
        visible: true,
      },
      {
        title: intl('saenext.pages.overview.constants.NumberOfRunningTaskInstances'),
        tips: '',
        count: get(value, 'tasks.taskActiveInstances', '-'),
        unit: intl('general.unit.count'),
        visible: true,
      },
      {
        title: intl('saenext.pages.overview.constants.MaximumNumberOfTaskInstances'),
        tips: '',
        count: get(value, 'tasks.taskMaxInstances', '-'),
        unit: intl('general.unit.count'),
        visible: true,
      },
    ],
  };
};

export const SAE_TYPES = {
  ALL: 'ALL',
  WEB_ALWAYS: 'WEB_ALWAYS',
  WEB_REQUEST: 'WEB_REQUEST',
  MICRO_SERVICE: 'MICRO_SERVICE',
};

export const RECENT_TABS = [
  { tab: intl('saenext.pages.overview.constants.AllApplicationTypes'), key: SAE_TYPES.ALL },
  {
    tab: intl('saenext.pages.overview.constants.WebApplicationsAlwaysAllocateFixed'),
    key: SAE_TYPES.WEB_ALWAYS,
  },
  {
    tab: intl('saenext.pages.overview.constants.WebApplicationsCpuAllocationOnly'),
    key: SAE_TYPES.WEB_REQUEST,
  },
  {
    tab: intl('saenext.pages.overview.constants.MicroserviceApplications'),
    key: SAE_TYPES.MICRO_SERVICE,
  },
];

export const TOPN_METRIC = {
  AVAILABILITY: 'AVAILABILITY',
  WARNING_EVENT: 'WARNING_EVENT',
  ORDER_ERROR: 'ORDER_ERROR',
  ORDER_ERROR_PERCENT: 'ORDER_ERROR_PERCENT',
  SCALE_APP: 'SCALE_APP',
  ARMS_TOPN_ERROR: 'ARMS_TOPN_ERROR',
  ARMS_TOPN_COUNT: 'ARMS_TOPN_COUNT',
  ARMS_TOPN_RT: 'ARMS_TOPN_RT',
};

export const TOPN_TABS = [
  {
    tab: intl('saenext.pages.overview.constants.TheTotalNumberOfInstances'),
    key: TOPN_METRIC.AVAILABILITY,
  },
  { tab: intl('saenext.pages.overview.constants.WarningEvents'), key: TOPN_METRIC.WARNING_EVENT },
  {
    tab: intl('saenext.pages.overview.constants.ChangeOrderFailureRate'),
    key: TOPN_METRIC.ORDER_ERROR_PERCENT,
  },
  {
    tab: intl('saenext.pages.overview.constants.NumberOfChangeOrderFailures'),
    key: TOPN_METRIC.ORDER_ERROR,
  },
  {
    tab: intl('saenext.pages.overview.constants.ElasticEffectiveApplications'),
    key: TOPN_METRIC.SCALE_APP,
  },
  {
    tab: intl('saenext.pages.overview.constants.ApplicationErrors'),
    key: TOPN_METRIC.ARMS_TOPN_ERROR,
  },
  {
    tab: intl('saenext.pages.overview.constants.TotalApplicationRequests'),
    key: TOPN_METRIC.ARMS_TOPN_COUNT,
  },
  {
    tab: intl('saenext.pages.overview.constants.AverageApplicationResponseTime'),
    key: TOPN_METRIC.ARMS_TOPN_RT,
  },
];

export const TOPN_EVENTS = {
  [TOPN_METRIC.AVAILABILITY]: {
    tips: intl('saenext.pages.overview.constants.DisplaysAListOfTop'),
    handle: services.getAvailabilityMetric,
    columns: [
      {
        title: intl('saenext.pages.overview.constants.ApplicationName'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          return (
            <MicroAppLink appId={record.AppId} appName={record.Name} regionId={record.RegionId} />
          );
        },
      },
      {
        title: intl('saenext.pages.overview.constants.Namespace'),
        dataIndex: 'RegionId',
      },
      {
        title: intl('saenext.pages.overview.constants.CurrentInstances'),
        dataIndex: 'EnableAutoscale',
      },
      {
        title: intl('saenext.pages.overview.constants.TargetInstances'),
        dataIndex: 'Instances',
      },
      {
        title: intl('saenext.pages.overview.constants.NumberOfAbnormalInstances'),
        dataIndex: 'ErrorInstances',
      },
    ],
  },
  [TOPN_METRIC.WARNING_EVENT]: {
    tips: intl('saenext.pages.overview.constants.DisplaysAListOfWarning'),
    handle: services.getWarningEventMetric,
    params: {
      StartTime: Math.floor(moment().subtract(30, 'minutes').valueOf() / 1000),
      EndTime: Math.floor(moment().valueOf() / 1000),
    },
    columns: [
      {
        title: intl('saenext.pages.overview.constants.ApplicationName'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          return (
            <MicroAppLink appId={record.AppId} appName={record.Name} regionId={record.RegionId} />
          );
        },
      },
      {
        title: intl('saenext.pages.overview.constants.Namespace'),
        dataIndex: 'RegionId',
      },
      {
        title: intl('saenext.pages.overview.constants.WarningEvents.1'),
        dataIndex: 'WarningCount',
      },
    ],
  },
  [TOPN_METRIC.ORDER_ERROR_PERCENT]: {
    tips: intl('saenext.pages.overview.constants.DisplaysTheListOfApplications'),
    handle: services.getChangeOrderErrorPercentMetric,
    params: {
      CreateTime: moment().subtract(7, 'days').valueOf(),
    },
    columns: [
      {
        title: intl('saenext.pages.overview.constants.ApplicationName'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          return (
            <MicroAppLink appId={record.AppId} appName={record.Name} regionId={record.RegionId} />
          );
        },
      },
      {
        title: intl('saenext.pages.overview.constants.Namespace'),
        dataIndex: 'RegionId',
      },
      {
        title: intl('saenext.pages.overview.constants.ChangeOrderFailureRate'),
        dataIndex: 'ErrorPercent',
      },
      {
        title: intl('saenext.pages.overview.constants.NumberOfChangeOrderFailures'),
        dataIndex: 'Error',
      },
      {
        title: intl('saenext.pages.overview.constants.TotalNumberOfChangeOrders'),
        dataIndex: 'Total',
      },
    ],
  },
  [TOPN_METRIC.ORDER_ERROR]: {
    tips: intl('saenext.pages.overview.constants.DisplaysTheListOfTop'),
    handle: services.getChangeOrderErrorMetric,
    params: {
      CreateTime: moment().subtract(7, 'days').valueOf(),
    },
    columns: [
      {
        title: intl('saenext.pages.overview.constants.ApplicationName'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          return (
            <MicroAppLink appId={record.AppId} appName={record.Name} regionId={record.RegionId} />
          );
        },
      },
      {
        title: intl('saenext.pages.overview.constants.Namespace'),
        dataIndex: 'RegionId',
      },
      {
        title: intl('saenext.pages.overview.constants.NumberOfChangeOrderFailures'),
        dataIndex: 'Error',
      },
      {
        title: intl('saenext.pages.overview.constants.ChangeOrderFailureRate'),
        dataIndex: 'ErrorPercent',
      },
      {
        title: intl('saenext.pages.overview.constants.TotalNumberOfChangeOrders'),
        dataIndex: 'Total',
      },
    ],
  },
  [TOPN_METRIC.SCALE_APP]: {
    tips: intl('saenext.pages.overview.constants.DisplaysAListOfTop.1'),
    handle: services.getScaleAppMetric,
    columns: [
      {
        title: intl('saenext.pages.overview.constants.ApplicationName'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          return (
            <MicroAppLink appId={record.AppId} appName={record.Name} regionId={record.RegionId} />
          );
        },
      },
      {
        title: intl('saenext.pages.overview.constants.Namespace'),
        dataIndex: 'RegionId',
      },
      {
        title: intl('saenext.pages.overview.constants.CurrentInstances'),
        dataIndex: 'Runnings',
      },
      {
        title: intl('saenext.pages.overview.constants.MaximumNumberOfInstances'),
        dataIndex: 'MaxReplicas',
      },
    ],
  },
  [TOPN_METRIC.ARMS_TOPN_ERROR]: {
    tips: intl('saenext.pages.overview.constants.DisplaysAListOfTop.2'),
    handle: services.getArmsTopNErrorMetric,
    params: {
      StartTime: moment().valueOf() - 15 * 60 * 1000,
      EndTime: moment().valueOf(),
    },
    columns: [
      {
        title: intl('saenext.pages.overview.constants.ApplicationName'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          return (
            <MicroAppLink appId={record.AppId} appName={record.Name} regionId={record.RegionId} />
          );
        },
      },
      {
        title: intl('saenext.pages.overview.constants.Namespace'),
        dataIndex: 'RegionId',
      },
      {
        title: intl('saenext.pages.overview.constants.ApplicationErrors'),
        dataIndex: 'Error',
      },
    ],
  },
  [TOPN_METRIC.ARMS_TOPN_COUNT]: {
    tips: intl('saenext.pages.overview.constants.DisplaysAListOfTop.3'),
    handle: services.getArmsTopNCountMetric,
    params: {
      StartTime: moment().valueOf() - 15 * 60 * 1000,
      EndTime: moment().valueOf(),
    },
    columns: [
      {
        title: intl('saenext.pages.overview.constants.ApplicationName'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          return (
            <MicroAppLink appId={record.AppId} appName={record.Name} regionId={record.RegionId} />
          );
        },
      },
      {
        title: intl('saenext.pages.overview.constants.Namespace'),
        dataIndex: 'RegionId',
      },
      {
        title: intl('saenext.pages.overview.constants.TotalApplicationRequests'),
        dataIndex: 'Count',
      },
    ],
  },
  [TOPN_METRIC.ARMS_TOPN_RT]: {
    tips: intl('saenext.pages.overview.constants.DisplaysAListOfTop.4'),
    handle: services.getArmsTopNRtMetric,
    params: {
      StartTime: moment().valueOf() - 15 * 60 * 1000,
      EndTime: moment().valueOf(),
    },
    columns: [
      {
        title: intl('saenext.pages.overview.constants.ApplicationName'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          return (
            <MicroAppLink appId={record.AppId} appName={record.Name} regionId={record.RegionId} />
          );
        },
      },
      {
        title: intl('saenext.pages.overview.constants.Namespace'),
        dataIndex: 'RegionId',
      },
      {
        title: intl('saenext.pages.overview.constants.AverageApplicationResponseTime'),
        dataIndex: 'Rt',
      },
    ],
  },
};
