import { intl } from '@ali/cnd';
import React, { Component, RefObject } from 'react';
import Form from '@ali/deep-form';
import { DockerCommandEditorField, EnvEditorField } from '@ali/cnd';
import { ConsoleContext } from '@ali/cnd';
import _ from 'lodash';
import CollapseField from '~/components/shared/CollapseField';
import NetworkSetField from '~/components/shared/NetworkSetField';
import ArmsMonitorField from '~/components/shared/ArmsMonitorField/web-app';
import NasSelectorField from '~/components/shared/NasSelectorField/web-app';
import OssSelectorField from '~/components/shared/OssSelectorField/web-app';
import HealthCheckField from '~/components/shared/HealthCheckField/web-app/index_back';
import LogSelectorOldField from '~/components/shared/LogSelectorField/index';
import LogSelectorNewField from '~/components/shared/LogSelectorField/index_back';
import CustomHostField from '~/components/shared/CustomHostField';
import { formatHostsWeb, validateHostsWeb } from '~/components/shared/CustomHostField/utils';
import { objValueStringfy } from '~/utils/transfer-data';
import CachedData from '~/cache/common';

type Props = {
  ref: RefObject<any>;
  className?: string;
  vpcId: string;
  appValue?: WebAppCopyItem;
  basicConfig: { [key: string]: any };
};

type States = {
  showHealthCheck?: boolean;
  showLogSelector?: boolean;
};

class AdvanceCreator extends Component<Props, States> {
  private advanceForm = React.createRef() as any;
  private logSelectorField = React.createRef() as any;
  // private armsRef = React.createRef() as any;
  private healthCheckField = React.createRef as any;
  private networkSetField = React.createRef as any;
  private nasSelectorField = React.createRef as any;
  private ossSelectorField = React.createRef as any;

  constructor(props) {
    super(props);
    this.state = {
      showHealthCheck: false,
      showLogSelector: false,
    };
    this.onGetValue = this.onGetValue.bind(this);
    // @ts-ignore
    this.regionId = ConsoleContext._currentValue.region.getCurrentRegionId();
  }

  // API: 读取表单值
  onGetValue() {
    return new Promise((resolve, reject) => {
      this.advanceForm.validate(async (error, values) => {
        if (error) {
          resolve('');
          return;
        }
        // 校验 日志组件
        const logValidate = this.logSelectorField?.getInstance()?.controlRef?.validate;
        const logValidateResult = logValidate && (await logValidate());

        let healthValidateResult = true;
        if (this.healthCheckField.current) {
          const healthValidate = this.healthCheckField?.getInstance()?.controlRef?.validate;
          healthValidateResult = healthValidate && healthValidate();
        }

        const networkValidate = this.networkSetField?.getInstance()?.controlRef?.validate;
        const networkValidateResult = networkValidate && networkValidate();

        const nasValidate = this.nasSelectorField?.getInstance()?.controlRef?.validate;
        const nasValidateResult = nasValidate && (await nasValidate());

        const ossValidate = this.ossSelectorField?.getInstance()?.controlRef?.validate;
        const ossValidateResult = ossValidate && (await ossValidate());

        if (logValidateResult !== true) {
          resolve('');
          return;
        }
        if (healthValidateResult !== true) {
          resolve('');
          return;
        }
        if (networkValidateResult !== true) {
          resolve('');
          return;
        }
        if (nasValidateResult !== true) {
          resolve('');
          return;
        }

        if (ossValidateResult !== true) {
          resolve('');
          return;
        }

        // values 表单值
        const {
          startCmd = {},
          logConfig = {},
          logTupleConfig = {},
          environmentVariables = {},
          hostsArr = [],
          healthCheckConfig = {},
          armsConfig = { enableAppMetric: false },
          vpcConfig = {},
          nasConfig = {},
          ossMountConfig = {},
        } = values;

        const params = {};

        // 启动命令设置
        const { Command, CommandArgs } = startCmd;
        if (Command && CommandArgs?.length >= 2) {
          Object.assign(params, {
            command: JSON.stringify([Command]),
            args: JSON.stringify(CommandArgs),
          });
        }

        // 环境变量
        Object.assign(params, { environmentVariables: objValueStringfy(environmentVariables) });

        // hosts
        Object.assign(params, { customHostAlias: formatHostsWeb(hostsArr) });

        // 网络设置
        const { enableVpc = false, internetAccess = true, ...vpcConfig1Rest } = vpcConfig;

        Object.assign(params, { internetAccess });

        // 日志兼容老版本传参
        if (!_.isEmpty(logConfig)) {
          Object.assign(params, { logConfig: { ...logConfig } });
        }

        if (!_.isEmpty(logTupleConfig)) {
          Object.assign(params, { logConfig: logTupleConfig?.logConfig });
          Object.assign(params, { slsConfig: logTupleConfig?.slsConfig });
        }

        Object.assign(params, { startupProbe: healthCheckConfig?.startupProbe });
        Object.assign(params, { livenessProbe: healthCheckConfig?.livenessProbe });
        Object.assign(params, { enableAppMetric: armsConfig?.enableAppMetric });
        if (_.get(armsConfig, 'enableAppMetric')) {
          Object.assign(params, { programmingLanguage: armsConfig?.programmingLanguage || 'java' });
        }
        Object.assign(params, { vpcConfig: { ...vpcConfig1Rest } });
        Object.assign(params, { nasConfig: { ...nasConfig } });
        Object.assign(params, { ossMountConfig: { ...ossMountConfig } });

        // params 高级配置 接口参数
        resolve(params);
      });
    });
  }

  componentDidMount() {
    // 应用复制 逻辑
    const { appValue } = this.props;
    // _asiWhiteFeature 白名单灰度
    // _asiRegionFeature region灰度
    const _asiWhiteFeature = _.get(window, 'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS.asi-enable');
    const _asiRegionFeature = _.get(window, 'ALIYUN_CONSOLE_GLOBAL.onAsiFeature', []);
    let _showHealthCheck = !!_asiRegionFeature.length;
    let _showLogSelector = !!_asiRegionFeature.length;
    // 如果白名单没有 没必要遍历
    if (_asiWhiteFeature && _asiRegionFeature.length) {
      const regions = _.map(_asiRegionFeature, (item) => item.id);
      // @ts-ignore
      if (!regions.includes(this.regionId)) {
        _showHealthCheck = false;
        _showLogSelector = false;
      }
    }

    this.setState(
      {
        showHealthCheck: _asiWhiteFeature && _showHealthCheck,
        showLogSelector: _asiWhiteFeature && _showLogSelector,
      },
      () => {
        if (appValue) {
          this.advanceForm.field.setValues(appValue?.advanceCopyValue);
        }
      },
    );
  }

  render() {
    const { vpcId, basicConfig } = this.props;
    const { showHealthCheck, showLogSelector } = this.state;
    const { deployParams = {} } = basicConfig;
    const deployType = deployParams?.type || 'image';

    return (
      <Form
        {...this.props}
        ref={(c) => {
          if (c) {
            this.advanceForm = c.getInstance();
          }
        }}
      >
        <CollapseField
          title={intl('saenext.app-create.web-app.AdvanceCreator.StartCommand')}
          subTitle={intl(
            'saenext.app-create.web-app.AdvanceCreator.SetCommandsForContainerStartup',
          )}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetStartupCommands')}
          linkHref={CachedData.confLink('help:sae:set-startup-command')}
          className="mt-xl ml-l mr-l"
        >
          {/* @ts-ignore */}
          <DockerCommandEditorField
            name="startCmd"
            popupStyle={{ marginLeft: -16, paddingRight: 16, maxWidth: 1000 }}
          />
        </CollapseField>

        {showLogSelector ? (
          <CollapseField
            title={intl('saenext.app-create.web-app.AdvanceCreator.LogMonitoringMetricsSettings')}
            subTitle={intl('saenext.app-create.web-app.AdvanceCreator.SetLogMonitoringMetricsTo')}
            className="mt-l ml-l mr-l"
          >
            <LogSelectorNewField
              className="w-100"
              name="logTupleConfig"
              showSlsConfig={false}
              style={{ marginBottom: 0 }}
              ref={(ref) => (this.logSelectorField = ref)}
            />
          </CollapseField>
        ) : (
          <CollapseField
            title={intl('saenext.app-create.web-app.AdvanceCreator.LogConfiguration')}
            subTitle={intl('saenext.app-create.web-app.AdvanceCreator.SetLogCollectionRulesTo')}
            className="mt-l ml-l mr-l"
          >
            {/* @ts-ignore */}
            <LogSelectorOldField
              name="logConfig"
              showCreate={true}
              ref={(ref) => (this.logSelectorField = ref)}
            />
          </CollapseField>
        )}

        <CollapseField
          title={intl('saenext.app-create.web-app.AdvanceCreator.EnvironmentVariables')}
          subTitle={intl('saenext.app-create.web-app.AdvanceCreator.SetSomeVariablesInThe')}
          className="mt-l ml-l mr-l"
        >
          {/* @ts-ignore */}
          <EnvEditorField
            name="environmentVariables"
            validation={[
              {
                type: 'customValidate',
                param: (value, rule) => {
                  if (_.isEmpty(value)) {
                    return true;
                  }
                  for (const key in value) {
                    const val = value[key];
                    if (!key || !val) {
                      return intl(
                        'saenext.app-create.web-app.AdvanceCreator.CompleteTheEnvironmentVariables',
                      );
                    }
                  }
                  return true;
                },
              },
            ]}
            style={{ maxWidth: 1000 }}
          />
        </CollapseField>

        {/* <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.HostsBindingSettings')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.EnterTheConfigurationInThe',
          )}
          className="mt-l ml-l mr-l"
        >
          <CustomHostField
            name="hostsArr"
            validation={[
              {
                type: 'customValidate',
                param: validateHostsWeb,
              },
            ]}
          />
        </CollapseField> */}

        {/* {showHealthCheck ? (
          <CollapseField
            title={intl('saenext.app-create.web-app.AdvanceCreator.ApplyHealthCheckSettings')}
            subTitle={intl('saenext.app-create.web-app.AdvanceCreator.HelpsYouDetermineWhenThe')}
            className="mt-l ml-l mr-l"
          >
            <HealthCheckField
              name="healthCheckConfig"
              className="w-100"
              ref={(ref) => (this.healthCheckField = ref)}
            />
          </CollapseField>
        ) : null} */}

        <CollapseField
          title={intl('saenext.app-create.web-app.AdvanceCreator.ApplicationMonitoring')}
          subTitle={intl('saenext.app-create.web-app.AdvanceCreator.ProvidesRealTimeMonitoringFor')}
          className="mt-l ml-l mr-l"
        >
          {/* @ts-ignore */}
          <ArmsMonitorField name="armsConfig" deployType={deployType} />
        </CollapseField>

        <CollapseField
          title={intl('saenext.app-create.web-app.AdvanceCreator.NetworkSettings')}
          subTitle={intl('saenext.app-create.web-app.AdvanceCreator.SetTheNetworkUsedBy')}
          className="mt-xl ml-l mr-l"
        >
          <NetworkSetField
            name="vpcConfig"
            disabled={!!vpcId}
            defaultVpcId={vpcId}
            ref={(ref) => (this.networkSetField = ref)}
            showStaticIpOptions={true}
          />
        </CollapseField>

        <CollapseField
          title={intl('saenext.app-create.web-app.AdvanceCreator.PersistentStorage')}
          subTitle={intl('saenext.app-create.web-app.AdvanceCreator.SetPersistentStorageData')}
          className="mt-l ml-l mr-l"
        >
          <NasSelectorField
            name="nasConfig"
            className="w-100"
            formField={this.advanceForm.field}
            ref={(ref) => (this.nasSelectorField = ref)}
          />

          <div className="border-t mt-l mb-l" />
          <OssSelectorField
            name="ossMountConfig"
            className="w-100"
            ref={(ref) => (this.ossSelectorField = ref)}
          />
        </CollapseField>
      </Form>
    );
  }
}

export default AdvanceCreator;
