import { intl } from '@ali/cnd';
import React, { Fragment, useContext, useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Step, Button, Message, ConsoleContext, Dialog } from '@ali/cnd';
import WebBasicCreator from './BasicCreator';
import WebAdvanceCreator from './AdvanceCreator';
import { stepsDataSource } from '../constants';
import services from '~/services';
import { trackResource, AES_CONSTANT } from '~/tracker';
import { DEPLOY_TYPE, PACKAGE_TYPE } from '~/components/shared/DeploySelectorField/constant';
import ComShow from '~/components/shared/ComShow';
import If from '~/components/shared/If';
import CostSheet from '../CostSheet';
import { isEmpty } from 'lodash';

const WebAppCreator = ({ history, initialStep, appValue, v1Micro = false, baseAppId, }) => {
  const basicEl = useRef(null);
  const advanceEl = useRef(null);

  const [basicConfig, setBasicConfig] = useState<any>({});
  // 当前步骤
  const [currentStep, setCurrentStep] = useState(initialStep);
  // 是否访问过高级设置的步骤
  const [recordStep, setRecordStep] = useState(initialStep);
  const [createLoading, setCreateLoading] = useState(false);
  const [advanceLoading, setAdvanceLoading] = useState(false);
  const [costOptions, refreshCostOptions] = useState({
    appType: 'web',
    replicas: 2,
    spec: { cpu: 1, memory: 2 },
  });

  const [vpcId, setVpcId] = useState('');

  const BASIC_STEP = stepsDataSource.basic.key;
  const ADVANCE_STEP = stepsDataSource.advance.key;

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  useEffect(() => {
    return () => {
      const existApps = parseInt(window.localStorage.getItem('EXIST_APPS')) || 0;
      trackResource({
        behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE,
        stage: AES_CONSTANT.AES_STAGE_TYPE.EXIT,
        existApps,
      });
    };
  }, []);

  const handleQuickCreate = () => {
    if (recordStep >= 1) {
      handleBeforeCreate(handlerAdvanceCreator);
    } else {
      handleBeforeCreate(handlerBasicCreator);
    }
  }

  const handleBeforeCreate = async (createApp) => {
    const res = await basicEl.current?.onGetValue();
    const { applicationParams: { scaleConfig = {} } = {} } = res || {};

    if (scaleConfig.warnInstanceCount === 0) {
      Dialog.alert({
        title: intl('saenext.components.app-create.CreateAConfirmationReminder'),
        content: (
          <p style={{ width: 500 }}>
            {intl('saenext.components.app-create.CurrentlyYourWebApplicationHas')}
            <br />
            {intl('saenext.components.app-create.CommonScenariosWhereApplicationsAre')}
          </p>
        ),
        onOk: () => {
          createApp();
        },
      });
    } else {
      delete scaleConfig.warnInstanceCount;
      createApp();
    }
  };

  const handlerAdvanceStep = async () => {
    setAdvanceLoading(true);
    const basicConfig = await basicEl.current?.onGetValue();

    if (!basicConfig) {
      setAdvanceLoading(false);
      return;
    }
    setBasicConfig(basicConfig);
    setCurrentStep(ADVANCE_STEP);
    setRecordStep(currentStep + 1);
    setAdvanceLoading(false);
  };

  const handleBackStep = async () => {
    setAdvanceLoading(true);
    const advanceConfig = await advanceEl.current?.onGetValue();

    if (!basicConfig || !advanceConfig) {
      setAdvanceLoading(false);
      return;
    }
    setCurrentStep(BASIC_STEP);
    setAdvanceLoading(false);
  };

  const handlerBasicCreator = async () => {
    setCreateLoading(true);

    const basicConfig = await basicEl.current?.onGetValue();
    if (!basicConfig) {
      setCreateLoading(false);
      return;
    }
    const { applicationParams } = basicConfig;

    const callback = handlerCallback(basicConfig);
    await createWebApp(applicationParams, callback);

    setCreateLoading(false);
  };

  const handlerAdvanceCreator = async () => {
    setCreateLoading(true);

    const advanceConfig = await advanceEl.current?.onGetValue();

    if (!basicConfig || !advanceConfig) {
      setCreateLoading(false);
      return;
    }

    const applicationParams = {
      ...basicConfig.applicationParams,
      ...advanceConfig,
    };

    if (
      advanceConfig?.logConfig &&
      Reflect.has(advanceConfig?.logConfig, 'useCreatingResouces')
    ) {
      const useCreatingResouces = advanceConfig?.logConfig?.useCreatingResouces;
      Reflect.deleteProperty(advanceConfig.logConfig, 'useCreatingResouces');

      if (useCreatingResouces) {
        const logConfig = await createSlsResource(
          basicConfig?.applicationParams?.namespaceID,
          basicConfig?.applicationParams?.applicationName,
        );

        if (logConfig) {
          advanceConfig.logConfig = {
            ...advanceConfig.logConfig,
            ...logConfig,
          };
        } else {
          Message.error(intl('saenext.components.app-create.FailedToCreateLogResource'));
          setCreateLoading(false);
          return;
        }
      }
    }

    const callback = handlerCallback(basicConfig);
    await createWebApp(applicationParams, callback);

    setCreateLoading(false);
  };

  const createSlsResource = async (namespaceID: string, applicationName: string) => {
    const result = await services.CreateWebLogResource({
      params: {
        namespaceID,
        applicationName,
      },
    });
    if (!result) return;
    const params = {
      project: result.project,
      logstore: result.logStore,
    };
    return params;
  };

  const handlerCallback = (basicConfig) => {
    const { deployParams = {} } = basicConfig;
    const { type, payload } = deployParams;
    let callback = (data?: any) => {};
    switch (type) {
      case DEPLOY_TYPE.IMAGE:
        callback = (data) => {
          webAppImageDeploy(data);
        };
        break;
      case DEPLOY_TYPE.REPOISTORY:
        const pipelineParams = { ...payload };
        callback = (data) => {
          webAppRepoistoryDeploy(data, pipelineParams);
        };
        break;
      case DEPLOY_TYPE.WEB_PACKAGE:
        const packageParams = { ...payload };
        callback = (data) => {
          webAppPackageDeploy(data, packageParams);
        };
        break;
      default:
        break;
    }
    return callback;
  };

  const webAppImageDeploy = async (appData) => {
    const { applicationID, applicationName } = appData;
    if (applicationName) {
      Message.success(intl('saenext.components.app-create.CreatedSuccessfullyTheCreationProcess'));
      trackResource({
        behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE,
        stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
        deployType: DEPLOY_TYPE.IMAGE,
      });
      const _value = applicationID || applicationName;
      history.push(
        `/${regionId}/app-list/${_value}/web-app/base/?name=${applicationName}&state=initialize`,
      );
    }
  };

  const webAppRepoistoryDeploy = async (appData, params) => {
    const { applicationID, applicationName } = appData;
    const { BuildConfig, CodeConfig, TriggerConfig, DeployConfig, ImageConfig } = params;
    const res = await services.createBuildPipeline({
      content: {
        BuildConfig,
        CodeConfig,
        ImageConfig,
        TriggerConfig,
        DeployConfig,
        Enabled: true,
        ApplicationId: applicationID,
        ApplicationName: applicationName,
      },
    });
    if (res?.PipelineRunId) {
      setCreateLoading(false);
      trackResource({
        behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE,
        stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
        deployType: DEPLOY_TYPE.REPOISTORY,
        buildType: BuildConfig.BuildType,
      });
      history.push(
        `/${regionId}/app-list/${applicationID}/web-app/base/?name=${applicationName}&state=initialize`,
      );
      Message.success(intl('saenext.components.app-create.TheSourceCodeIsCreated'));
    }
  };

  const webAppPackageDeploy = async (appData, params) => {
    const { applicationID, applicationName } = appData;
    const {
      Jdk,
      PackageType,
      PackageUrl,
      FileName,
      PackageVersion,
      DeployConfig,
      RunCommand = '',
    } = params;
    const buildConfig = {
      // 添加 启动命令
      RunCommand,
      BuildType: 'Buildpack',
      RuntimeVersion: Jdk,
      RuntimeType: 'java',
    };
    if (PackageType === PACKAGE_TYPE.WAR) {
      const container = params.WebContainer || params.EdasContainerVersion;
      Reflect.set(buildConfig, 'TomcatConfig', { Version: container });
    }
    const data = await services.createBuildPipeline({
      content: {
        BuildConfig: buildConfig,
        PackageConfig: {
          PackageType,
          PackageUrl,
          PackageVersion, // 包的版本
          PackageName: FileName, // 文件名
        },
        DeployConfig,
        ImageConfig: {
          InstanceType: 'SHARE_ACREE',
        },
        ApplicationId: applicationID,
        ApplicationName: applicationName,
      },
    });
    if (data?.PipelineRunId) {
      setCreateLoading(false);
      history.push(
        `/${regionId}/app-list/${applicationID}/web-app/base/?name=${applicationName}&state=initialize`,
      );
      Message.success(intl('saenext.components.app-create.TheCodePackageHasBeen'));
    }
  };

  const createWebApp = async (params, callback) => {
    // 创建应用
    const data = await services.createApplication({
      content: {
        ...params,
        diskSize: 512,
        // internetAccess: true,
        runtime: 'custom-container',
        handler: 'index.handler',
      },
    });
    if (data) {
      callback && callback(data);
    } else {
      trackResource({
        behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE,
        stage: AES_CONSTANT.AES_STAGE_TYPE.FAIL,
      });
    }
  };

  return (
    <Fragment>
      <div className="app-creator-container">
        <div className="step-content">
          <Step current={currentStep} shape="circle" labelPlacement="hoz">
            <Step.Item
              key={BASIC_STEP}
              title={intl('saenext.components.app-create.BasicApplicationInformation')}
              onClick={(index) => setCurrentStep(index)}
            />
            <Step.Item
              key={ADVANCE_STEP}
              title={intl('saenext.components.app-create.AdvancedSettings')}
              onClick={(index) => setCurrentStep(index)}
            />
          </Step>
          <ComShow if={currentStep === BASIC_STEP}>
            <WebBasicCreator
              regionId={regionId}
              ref={basicEl}
              appValue={appValue}
              vpcId={vpcId}
              setVpcId={setVpcId}
              refreshCostOptions={(v) => refreshCostOptions(v)}
              baseAppId={baseAppId}
            />
          </ComShow>
          <ComShow if={currentStep === ADVANCE_STEP && recordStep >= 1}>
            <If condition={recordStep >= 1}>
              <WebAdvanceCreator 
                ref={advanceEl} 
                vpcId={vpcId} 
                appValue={appValue} 
                basicConfig={basicConfig}
              />
            </If>
          </ComShow>
        </div>
        <div className="step-footer">
          <CostSheet costOptions={costOptions} />
          <ComShow if={currentStep === BASIC_STEP}>
            <div className="ml-xl">
              <Button
                size="large"
                type="normal"
                className="bk-button"
                loading={advanceLoading}
                disabled={!vpcId}
                onClick={handlerAdvanceStep}
              >
                {intl('saenext.components.app-create.NextStepAdvancedSettings')}
              </Button>
              <If condition={isEmpty(appValue)}>
                <Button
                  size="large"
                  className="mr-s ml-s bk-button"
                  loading={createLoading}
                  disabled={!vpcId}
                  onClick={handleQuickCreate}
                >
                  {intl('saenext.components.app-create.SkipAdvancedSettingsAndCreate')}
                </Button>
              </If>
            </div>
          </ComShow>
          <ComShow if={currentStep === ADVANCE_STEP}>
            <div className="ml-xl">
              <Button
                size="large"
                type="normal"
                className="bk-button"
                onClick={handleBackStep}
              >
                {intl('saenext.components.app-create.PreviousBasicInformation')}
              </Button>
              <Button
                size="large"
                loading={createLoading}
                className="mr-s ml-s bk-button"
                onClick={() => handleBeforeCreate(handlerAdvanceCreator)}
              >
                {intl('saenext.components.app-create.CreateAnApplication')}
              </Button>
            </div>
          </ComShow>
        </div>
      </div>
    </Fragment>
  );
};

WebAppCreator.propTypes = {
  history: PropTypes.func,
  initStep: PropTypes.number,
};
WebAppCreator.defaultProps = {
  initialStep: stepsDataSource.basic.key,
};

export default WebAppCreator;
