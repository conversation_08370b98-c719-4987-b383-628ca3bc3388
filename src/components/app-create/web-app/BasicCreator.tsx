import { intl } from '@ali/cnd';
import React, { Component, RefObject } from 'react';
import Form from '@ali/deep-form';
import { TextField, RadioField, NumberField } from '@ali/deep';
import { Message, CpuMemSelectorField } from '@ali/cnd';
import services from '~/services';
import { CpuMemDataSource } from '../constants';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import NumberRangeField from '@ali/deep-number-range-field';
import DeploySelectorField from '~/components/shared/DeploySelectorField';
import { WebNamespaceField } from '~/components/shared/NamespaceField';
import { DEPLOY_TYPE } from '~/components/shared/DeploySelectorField/constant';
import CollapseField from '~/components/shared/CollapseField';
import { getParams } from '~/utils/global';
import _ from 'lodash';
import CachedData from '~/cache/common';

type BaseProps = {
  regionId: string;
  ref: RefObject<any>;
  appValue?: WebAppCopyItem;
  className?: string;
  vpcId?: string;
  setVpcId: (vpcId: string) => void;
  refreshCostOptions: (any) => void;
  baseAppId?: string;
};
type BaseStates = {
  refreshIndex: number;
  limitInstances: number;
  maxConcurrent: number;
};

class BasicCreator extends Component<BaseProps, BaseStates> {
  private regionId: any;
  private baseForm: any;

  constructor(props) {
    super(props);

    this.state = {
      refreshIndex: 0,
      limitInstances: 50,
      maxConcurrent: 200,
    }

    this.regionId = props.regionId;
    this.baseForm = null;
    this.onGetValue = this.onGetValue.bind(this);
  }

  componentDidMount(): void {
    this.setDefaultImageDemo();
    this.getLimitInstances();

    const uid = _.get(window, 'ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK');
    const grayRules = _.get(window, 'ALIYUN_CONSOLE_GLOBAL.instanceConcurrencyLimit', {});
    let _maxConcurrent = 200;
    if (!_.isEmpty(grayRules)) {
      const key = _.findKey(grayRules, (value) => _.includes(value, uid));
      _maxConcurrent = key ? Number(key) : this.state.maxConcurrent;
    }
    this.setState({
      maxConcurrent: _maxConcurrent
    });
  }

  setDefaultImageDemo = () => {
    const defaultDemoTag = getParams('imageDemoTag') || 'web-express-helloworld-v1.0'

    const defaultConfig = { port: 8080 };
    this.baseForm.field?.setValue('deployConfig', {
      type: DEPLOY_TYPE.IMAGE,
      image: `registry.${this.regionId}.aliyuncs.com/sae-serverless-demo/sae-demo:${defaultDemoTag}`,
      accelerationType: 'Default',
      ...defaultConfig,
    });
  };

  getLimitInstances = async () => {
    const res = await services.GetAccountSettings();
    const { maximumInstancesPerApplication = 50 } = res;
    this.setState({
      limitInstances: maximumInstancesPerApplication,
    });
  };

  onImageChange = ({ value }) => {
    if (value?.port) {
      this.baseForm.field.setValue('caPort', value.port);
    }
    if (!value?.port) {
      this.baseForm.field.setValue('caPort', null);
    }
  };

  applicationNameValidate = (value) => {
    let name = value;
    const nameRex = /^[_a-zA-Z][-_a-zA-Z0-9]{0,62}$/;
    if (name && !nameRex.test(name)) {
      return intl('saenext.app-create.web-app.BasicCreator.TheNameCanBeTo');
    }
    return true;
  };

  checkDuplicateNames = (value) => {
    if (!value) {
      return true;
    }
    const { namespaceID } = this.baseForm?.getValue() || {};
    let namespaceId = namespaceID;
    if (!namespaceId) {
      return true;
    }

    let name = value;
    return new Promise(async (resolve) => {
      this.queryDublicateNames(name, namespaceId, resolve);
    });
  };

  queryDublicateNames = _.debounce(async (value, namespaceId, resolve) => {
    const {
      Data: { Available = true },
    } =
      (await services.CheckAppNameV2({
        NamespaceId: namespaceId,
        AppName: value,
      })) || {};
    Available
      ? resolve(true)
      : resolve(intl('saenext.app-create.web-app.BasicCreator.TheApplicationNameIsDuplicate'));
  }, 500);

  getWebValues = (values) => {
    const { deployConfig, spec, scaleConfig, disableURLInternet, authType } = values;
    const { type } = deployConfig;
    let imageConfig = {
      accelerationType: 'Default',
      image: `registry.${this.regionId}.aliyuncs.com/sae-serverless-demo/sae-demo:web-express-helloworld-v1.0`,
    };
    let deployParams = {
      type: DEPLOY_TYPE.IMAGE,
      payload: null,
    };
    switch (type) {
      case DEPLOY_TYPE.IMAGE:
        deployParams = {
          type: DEPLOY_TYPE.IMAGE,
          payload: null,
        };
        imageConfig = { ...deployConfig };
        break;
      case DEPLOY_TYPE.REPOISTORY:
        deployParams = {
          type: DEPLOY_TYPE.REPOISTORY,
          payload: {
            ...deployConfig,
            // 创建应用添加只在
            DeployConfig: {
              ...deployConfig.DeployConfig,
              MinimumInstanceCount: scaleConfig?.start,
              MaximumInstanceCount: scaleConfig?.end,
            },
          },
        };
        break;
      case DEPLOY_TYPE.WEB_PACKAGE:
        deployParams = {
          type: DEPLOY_TYPE.WEB_PACKAGE,
          payload: {
            ...deployConfig,
            // 创建应用添加只在
            DeployConfig: {
              MinimumInstanceCount: scaleConfig?.start,
              MaximumInstanceCount: scaleConfig?.end,
            },
          },
        };
        break;
    }
    const params = {
      ...values,
      imageConfig,
      cpu: spec.cpu,
      memorySize: spec.memory * 1024,
      httpTriggerConfig: { disableURLInternet, authType },
      scaleConfig: {
        warnInstanceCount: scaleConfig?.start,
        minimumInstanceCount: type === DEPLOY_TYPE.IMAGE ? scaleConfig?.start : 0, // 持续部署时会先以 placeholder 镜像创建应用且实例数为0
        maximumInstanceCount: type === DEPLOY_TYPE.IMAGE ? scaleConfig?.end : 0,
        alwaysAllocateCPU: false,
      },
    };
    Reflect.deleteProperty(params, 'spec');
    // Reflect.deleteProperty(params, 'alwaysAllocateCPU');
    Reflect.deleteProperty(params, 'disableURLInternet');
    Reflect.deleteProperty(params, 'authType');
    Reflect.deleteProperty(params, 'deployConfig');
    return { appType: 'web', applicationParams: params, deployParams };
  };

  onGetValue() {
    return new Promise((resolve, reject) => {
      this.baseForm.validate(async (error, values) => {
        if (error) {
          resolve(false);
          return;
        }

        let params = {};
        const { deployConfig } = values;
        const { type } = deployConfig;
        if (type === DEPLOY_TYPE.REPOISTORY) {
          if (
            Object.keys(values.deployConfig.BuildConfig).length === 0 ||
            Object.keys(values.deployConfig.CodeConfig).length === 0
          ) {
            const target = document.getElementsByClassName('windcc-app-layout__content')[0];
            target.scrollTo(0, 0);
            resolve(false);
            return;
          }
        }
        params = this.getWebValues(values);
        resolve(params);
        return;
      });
    });
  };

  setRefresh = () => {
    this.setState({
      refreshIndex: this.state.refreshIndex + 1,
    });
  };

  namespaceChange = () => {
    this.baseForm.validate(['applicationName']);
  };

  refreshCostSheet = () => {
    if (!this.baseForm) return;

    const field = this.baseForm?.field;
    const values = field?.getValues(['scaleConfig', 'spec', 'replicas']);
    const { refreshCostOptions } = this.props;
    const { scaleConfig, spec } = values || {};
    // 2是web 和 micro应用的默认值
    let _replicas = scaleConfig?.start;
    refreshCostOptions &&
      refreshCostOptions({
        appType: 'web',
        replicas: _replicas || 2,
        spec: spec || { cpu: 1, memory: 2 },
      });
  };

  componentDidUpdate(prevProps, prevState) {
    // 应用复制 逻辑
    const { appValue, vpcId } = this.props;
    if (prevProps.appValue !== appValue) {
      this.baseForm.field.setValues(appValue.basicCopyValue);
    }
    if (prevProps.vpcId !== vpcId) {
      setTimeout(() => {
        this.baseForm.validate(['namespaceID']);
      }, 1000);
    }
  }

  filterSpec = (specs) => {
    const microSpecs = _.cloneDeep(specs);
    microSpecs.forEach((item) => {
      delete item.disabled;
      if (item.children) {
        item.children.forEach((child) => {
          delete child.disabled;
        });
      }
    });
    return microSpecs;
  };

  deployValidation = (value) => {
    switch (value.type) {
      case DEPLOY_TYPE.IMAGE:
        return value.image ? true : intl('saenext.app-create.web-app.BasicCreator.SelectAnImage');
      case DEPLOY_TYPE.REPOISTORY:
        return Object.keys(value.CodeConfig).length > 0 && Object.keys(value.BuildConfig).length > 0
          ? true
          : intl('saenext.app-create.web-app.BasicCreator.SetTheSourceCodeContinuous');
      case DEPLOY_TYPE.WEB_PACKAGE:
      case DEPLOY_TYPE.MICRO_PACKAGE:
        return value.PackageUrl
          ? true
          : intl('saenext.app-create.web-app.BasicCreator.PleaseConfigureTheCodePackage');
      default:
        return intl(
          'saenext.app-create.web-app.BasicCreator.NoApplicationDeploymentMethodSelected',
        );
    }
  };

  render() {
    const {
      authType,
      namespaceConfig: { VpcId: vpcId = '', NamespaceId: namespaceId = this.regionId } = {},
      scaleConfig = {},
    } = this.baseForm?.getValue() || {};

    const { setVpcId } = this.props;

    const defaultDemoTag = getParams('imageDemoTag') || 'web-express-helloworld-v1.0'

    return (
      <React.Fragment>
        <Form
          inline
          {...this.props}
          onChange={this.setRefresh}
          ref={(c) => {
            if (c) {
              this.baseForm = c.getInstance();
            }
          }}
        >
          <CollapseField
            title={intl('saenext.app-create.web-app.BasicCreator.BasicInformationSettings')}
            className="mt-l ml-l mr-l"
          >
            <TextField
              required
              name="applicationName"
              label={intl('saenext.app-create.web-app.BasicCreator.ApplicationName')}
              className="w-50"
              help={intl('saenext.app-create.web-app.BasicCreator.TheNameCanBeTo')}
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.app-create.web-app.BasicCreator.TheApplicationNameCannotBe',
                  ),
                },
                {
                  type: 'customValidate',
                  param: this.applicationNameValidate,
                },
                {
                  type: 'customValidate',
                  param: this.checkDuplicateNames,
                },
              ]}
            />

            <TextField
              name="description"
              label={intl('saenext.app-create.web-app.BasicCreator.ApplicationDescription')}
              className="w-50 mr-none"
              help={intl('saenext.app-create.web-app.BasicCreator.IntroduceTheBasicSituationOf')}
              maxLength={240}
              showLimitHint
              validation={[
                {
                  type: 'customValidate',
                  param: (value) => {
                    if (!value || value?.length <= 240) {
                      return true;
                    } else {
                      return intl(
                        'saenext.app-create.web-app.BasicCreator.TheApplicationDescriptionIsToo',
                      );
                    }
                  },
                },
              ]}
            />

            <WebNamespaceField
              name="namespaceID"
              label={intl('saenext.app-create.web-app.BasicCreator.Namespace')}
              className="w-50"
              required={true}
              requiredMessage={intl('saenext.app-create.web-app.BasicCreator.SelectANamespace')}
              defaultValue={getParams('namespaceId') || this.regionId}
              onNamespaceChange={this.namespaceChange}
              setVpcId={setVpcId}
              help={intl(
                'saenext.app-create.web-app.BasicCreator.NamespacesProvideIsolatedRuntimeEnvironments',
              )}
            />

            <DeploySelectorField
              required
              name="deployConfig"
              className="deploy-group mb-s"
              label={intl('saenext.app-create.web-app.BasicCreator.ApplicationDeploymentMethod')}
              defaultValue={{
                type: DEPLOY_TYPE.IMAGE,
                image: `registry.${this.regionId}.aliyuncs.com/sae-serverless-demo/sae-demo:${defaultDemoTag}`,
                accelerationType: 'Default',
                ProgrammingLanguage: 'java',
                Jdk: 'Open JDK 8',
              }}
              onChange={this.onImageChange}
              allowTypes={[DEPLOY_TYPE.IMAGE, DEPLOY_TYPE.REPOISTORY, DEPLOY_TYPE.WEB_PACKAGE]}
              appType="web"
              vpcId={vpcId}
              namespaceId={namespaceId}
              validation={[
                {
                  type: 'customValidate',
                  param: this.deployValidation,
                },
              ]}
            />
          </CollapseField>

          <CollapseField
            title={intl('saenext.app-create.web-app.BasicCreator.FullHostingOfHttpTraffic')}
            className="mt-l ml-l mr-l"
          >
            <Message type="notice" className="mb-l">
              <div className="text-line">
                {intl('saenext.app-create.web-app.BasicCreator.HttpTrafficIsFullyManaged')}
              </div>
              <div className="text-line">
                {intl('saenext.app-create.web-app.BasicCreator.AfterTheApplicationIsCreated')}
              </div>
            </Message>
            <RadioField
              required
              name="disableURLInternet"
              label={intl('saenext.app-create.web-app.BasicCreator.EntranceTypeOfData')}
              className="full-width"
              defaultValue={false}
              dataSource={[
                {
                  value: false,
                  text: (
                    <span className="radio-item-120">
                      {intl('saenext.app-create.web-app.BasicCreator.AllPublicAndPrivate')}
                    </span>
                  ),
                },
                {
                  value: true,
                  text: (
                    <span className="radio-item-120">
                      {intl('saenext.app-create.web-app.BasicCreator.Intranet')}
                    </span>
                  ),
                },
              ]}
              validation={[
                {
                  type: 'required',
                  message: intl('saenext.app-create.web-app.BasicCreator.SelectEntryTypeOfData'),
                },
              ]}
            />

            <RadioField
              required
              name="authType"
              label={intl('saenext.app-create.web-app.BasicCreator.CallAuthentication')}
              className="full-width"
              defaultValue="anonymous"
              help={
                authType === 'function'
                  ? intl(
                      'saenext.app-create.web-app.BasicCreator.SignatureAuthenticationIsRequiredFor',
                    )
                  : ''
              }
              dataSource={[
                {
                  value: 'anonymous',
                  text: (
                    <span className="radio-item-120">
                      {intl('saenext.app-create.web-app.BasicCreator.NoAuthenticationRequired')}
                    </span>
                  ),
                },
                {
                  value: 'function',
                  text: (
                    <span className="radio-item-120">
                      {intl('saenext.app-create.web-app.BasicCreator.SignatureAuthentication')}
                    </span>
                  ),
                },
              ]}
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.app-create.web-app.BasicCreator.SelectCallAuthentication',
                  ),
                },
              ]}
            />

            <NumberField
              required
              name="caPort"
              defaultValue={8080}
              placeholder={intl(
                'saenext.app-create.web-app.BasicCreator.EnterTheApplicationPort',
              )}
              label={intl('saenext.app-create.web-app.BasicCreator.HttpListeningPort')}
              min={1}
              max={65536}
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.app-create.web-app.BasicCreator.EnterTheHttpListeningPort',
                  ),
                },
              ]}
            />

            <NumberField
              required
              name="timeout"
              label={intl('saenext.app-create.web-app.BasicCreator.RequestTimeout')}
              defaultValue={10}
              min={1}
              max={3600}
              innerAfter={<span>{intl('saenext.app-create.web-app.BasicCreator.Seconds')}</span>}
              validation={[
                {
                  type: 'required',
                  message: intl('saenext.app-create.web-app.BasicCreator.PleaseFillInTheTimeout'),
                },
              ]}
            />
          </CollapseField>

          <CollapseField
            title={intl('saenext.app-create.web-app.BasicCreator.CapacitySettings')}
            className="mt-l ml-l mr-l"
          >
            {/* @ts-ignore */}
            <CpuMemSelectorField
              required
              hasClear
              name="spec"
              label={intl('saenext.app-create.web-app.BasicCreator.SingleInstanceType')}
              // help={'公测阶段仅提供 2Core 以及 2Core 以下的规格实例规格体验，商业化后会放开规格限制。'}
              defaultValue={{ cpu: 1, memory: 2 }}
              onChange={(value) => {
                this.baseForm?.field.setValue('spec', value);
                this.refreshCostSheet();
              }}
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.app-create.web-app.BasicCreator.SelectASingleInstanceType',
                  ),
                },
              ]}
              dataSource={CpuMemDataSource}
            />

            <br />
            <NumberField
              required
              name="instanceConcurrency"
              label={
                <TextWithBalloon
                  align="tr"
                  text={intl(
                    'saenext.app-create.web-app.BasicCreator.MaximumNumberOfConcurrentRequests',
                  )}
                  tips={
                    <>
                      {intl(
                        'saenext.app-create.web-app.BasicCreator.ConfigureTheMaximumNumberOf',
                      )}
                      <a
                        href={CachedData.confLink('help:sae:web-set-maximum-number-of-concurrent-requests')}
                        target="_blank"
                      >
                        {intl('saenext.app-create.web-app.BasicCreator.Details')}
                      </a>
                    </>
                  }
                />
              }
              defaultValue={10}
              min={1}
              max={this.state.maxConcurrent}
              innerAfter={<span>{intl('general.unit.count')}</span>}
              className="mr-xl"
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.app-create.web-app.BasicCreator.EnterTheNumberOfConcurrent',
                  ),
                },
              ]}
            />

            <NumberRangeField
              required
              min={0}
              defaultValue={{ start: 2, end: 10 }}
              label={intl('saenext.app-create.web-app.BasicCreator.AutomaticScalingOfInstances')}
              name="scaleConfig"
              extra={
                scaleConfig?.start === 0 && (
                  <span className="text-warning">
                    {intl('saenext.app-create.web-app.BasicCreator.IfTheConfigurationIsScaled')}
                    <br />
                    {intl(
                      'saenext.app-create.web-app.BasicCreator.CommonScenariosWhereApplicationsAre',
                    )}
                  </span>
                )
              }
              onChange={() => this.refreshCostSheet()}
              validation={[
                {
                  type: 'required',
                  message: intl('saenext.app-create.web-app.BasicCreator.SelectARangeOfAuto'),
                },
                {
                  type: 'customValidate',
                  param: (value) => {
                    const { start, end } = value;
                    if (start === undefined || !end) {
                      return intl('saenext.app-create.web-app.BasicCreator.SelectARangeOfAuto');
                    }
                    if (start > end) {
                      return intl(
                        'saenext.app-create.web-app.BasicCreator.SelectTheCorrectRangeOf',
                      );
                    }
                    if (end > this.state.limitInstances) {
                      return intl(
                        'saenext.app-create.web-app.BasicCreator.TheMaximumNumberOfSingle',
                        { thisStateLimitInstances: this.state.limitInstances },
                      );
                    }
                    return true;
                  },
                },
              ]}
            />
          </CollapseField>
        </Form>
      </React.Fragment>
    );
  }
}

export default BasicCreator;
