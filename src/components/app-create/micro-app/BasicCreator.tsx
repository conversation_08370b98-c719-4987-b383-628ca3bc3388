import { intl, Loading } from '@ali/cnd';
import React, { Component, RefObject } from 'react';
import Form from '@ali/deep-form';
import { TextField, NumberField } from '@ali/deep';
import { Message, CpuMemSelectorField, Radio, ToolTipCondition, Icon } from '@ali/cnd';
import services from '~/services';
import DeploySelector<PERSON>ield from '~/components/shared/DeploySelectorField';
import { MicroNamespaceField } from '~/components/shared/NamespaceField';
import { DEPLOY_TYPE, PACKAGE_TYPE } from '~/components/shared/DeploySelectorField/constant';
import CollapseField from '~/components/shared/CollapseField';
import If from '~/components/shared/If';
import { getParams } from '~/utils/global';
import _, { filter, find, isEmpty, get, includes } from 'lodash';

type BaseProps = {
  regionId: string;
  ref: RefObject<any>;
  appValue?: WebAppCopyItem;
  className?: string;
  vpcId?: string;
  setVpcId: (vpcId: string) => void;
  refreshCostOptions: (any) => void;
  baseAppId?: string;
  inDebtData: { [key: string]: any };
  hygonSupportInfo: { regions: string[]; zones: string[] };
  feature?: { [key: string]: any };
  NewSaeVersion?: string;
};
type BaseStates = {
  refreshIndex: number;
  limitInstances: number;
  maxConcurrent: number;
  cpuMemData: any[];
  resourceType?: string;
  isSupportHygon: boolean;
  vSwitchMap: { [key: string]: any };
  specValue: { cpu: number; memory: number };
};

const hygonSpecs = [
  {
    label: '2',
    value: 2,
    children: [
      {
        label: '8',
        value: 8,
      },
    ],
  },
  {
    label: '4',
    value: 4,
    children: [
      {
        label: 16,
        value: 16,
      },
    ],
  },
  {
    label: '8',
    value: 8,
    children: [
      {
        label: '32',
        value: 32,
      },
    ],
  },
  {
    label: '16',
    value: 16,
    children: [
      {
        label: '64',
        value: 64,
      },
    ],
  },
  {
    label: '32',
    value: 32,
    children: [
      {
        label: '128',
        value: 128,
      },
    ],
  },
];

class BasicCreator extends Component<BaseProps, BaseStates> {
  private regionId: any;
  private baseForm: any;

  constructor(props) {
    super(props);

    this.state = {
      refreshIndex: 0,
      limitInstances: 50,
      maxConcurrent: 200,
      cpuMemData: [],
      resourceType: 'default',
      isSupportHygon: false,
      vSwitchMap: {},
      specValue: { cpu: 1, memory: 2 },
    };

    this.regionId = props.regionId;
    this.baseForm = null;
    this.onGetValue = this.onGetValue.bind(this);
  }

  componentDidMount(): void {
    this.querySpec();
    this.setDefaultImageDemo();

    const uid = _.get(window, 'ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK');
    const grayRules = _.get(window, 'ALIYUN_CONSOLE_GLOBAL.instanceConcurrencyLimit', {});
    let _maxConcurrent = 200;
    if (!_.isEmpty(grayRules)) {
      const key = _.findKey(grayRules, (value) => _.includes(value, uid));
      _maxConcurrent = key ? Number(key) : this.state.maxConcurrent;
    }
    const { regionId, hygonSupportInfo, feature } = this.props;
    const hygonSupportRegions = get(hygonSupportInfo, 'regions', []);
    let _isSupportHygon = false;
    // 白名单用户&&region符合才可以
    if (includes(hygonSupportRegions, regionId) && feature?.haiguang_pool) {
      _isSupportHygon = true;
    }
    this.setState({
      maxConcurrent: _maxConcurrent,
      isSupportHygon: _isSupportHygon,
    });
  }

  querySpec = async () => {
    const res = await services.DescribeInstanceSpecifications();
    const { Data = [] } = res;
    if (isEmpty(Data)) return;
    Data.sort((a, b) => a.Memory - b.Memory).sort((a, b) => a.Cpu - b.Cpu);
    const specEnable = filter(Data, (item) => item.Enable);

    const cpuMemData = [];
    specEnable.forEach((spec) => {
      const { Cpu, Memory } = spec;

      const cpu = Cpu / 1000;
      const mem = Memory / 1024;

      const cpuItem = find(cpuMemData, { value: cpu });
      if (cpuItem) {
        cpuItem.children.push({
          label: `${mem}`,
          value: mem,
        });
      } else {
        cpuMemData.push({
          label: `${cpu}`,
          value: cpu,
          children: [
            {
              label: `${mem}`,
              value: mem,
            },
          ],
        });
      }
    });
    this.setState({
      cpuMemData,
    });
  };

  setDefaultImageDemo = () => {
    const defaultConfig = {
      ProgrammingLanguage: 'java',
      Jdk: 'Open JDK 8',
    };
    this.baseForm.field?.setValue('deployConfig', {
      type: DEPLOY_TYPE.IMAGE,
      image: `registry.${this.regionId}.aliyuncs.com/sae-serverless-demo/sae-demo:microservice-java-provider-v1.0`,
      accelerationType: 'Default',
      ...defaultConfig,
    });
  };

  onImageChange = ({ value }) => {
    if (value?.port) {
      this.baseForm.field.setValue('caPort', value.port);
    }
    if (!value?.port) {
      this.baseForm.field.setValue('caPort', null);
    }
  };

  applicationNameValidate = (value) => {
    let name = value;
    const maxLength = this.props.inDebtData?.MaxAppNameLength || 36;
    const nameRex = new RegExp(`^[a-zA-Z]([0-9a-zA-Z\-]{0,${maxLength}})$`);
    if (!!this.props.baseAppId) {
      // 创建灰度应用
      name = `${_.get(this.props, 'appValue.basicCopyValue.applicationName')}-${value}`;
    }
    if (name && !nameRex.test(name)) {
      return intl('saenext.app-create.web-app.BasicCreator.MicroNameHelp', { maxLength });
    }
    return true;
  };
  checkDuplicateNames = (value) => {
    if (!value) {
      return true;
    }
    const { namespaceConfig } = this.baseForm?.getValue() || {};

    const { AutoConfig, NamespaceId } = namespaceConfig || {};
    const namespaceId = AutoConfig ? this.regionId : NamespaceId;
    if (!namespaceId) {
      return true;
    }

    let name = value;
    if (!!this.props.baseAppId) {
      // 创建灰度应用
      name = `${_.get(this.props, 'appValue.basicCopyValue.applicationName')}-${value}`;
    }

    return new Promise(async (resolve) => {
      this.queryDublicateNames(name, namespaceId, resolve);
    });
  };

  queryDublicateNames = _.debounce(async (value, namespaceId, resolve) => {
    const {
      Data: { Available = true } = {},
    } =
      (await services.CheckAppNameV2({
        NamespaceId: namespaceId,
        AppName: value,
      })) || {};
    Available
      ? resolve(true)
      : resolve(intl('saenext.app-create.web-app.BasicCreator.TheApplicationNameIsDuplicate'));
  }, 500);

  getMicroValues = (values) => {
    const { baseAppId, NewSaeVersion='' } = this.props;
    const {
      applicationName,
      description,
      namespaceConfig,
      spec,
      deployConfig,
      replicas,
      resourceType,
    } = values;
    let deployParams = {} as any;
    const { type } = deployConfig;
    switch (type) {
      case DEPLOY_TYPE.IMAGE:
        const {
          image: ImageUrl,
          instanceId: AcrInstanceId,
          acrAssumeRoleArn: AcrAssumeRoleArn,
          enableImageAccl: EnableImageAccl,
          imagePullSecrets: ImagePullSecrets,
          customImageNetworkType: CustomImageNetworkType,
          ProgrammingLanguage,
          PackageType,
          Jdk,
        } = deployConfig;
        deployParams = {
          PackageType: PackageType || 'Image',
          ImageUrl,
          AcrInstanceId,
          AcrAssumeRoleArn,
          EnableImageAccl,
          ImagePullSecrets,
          CustomImageNetworkType,
          ProgrammingLanguage,
          Jdk,
        };
        break;
      case DEPLOY_TYPE.MICRO_PACKAGE:
        deployParams = { ...deployConfig };
        Reflect.deleteProperty(deployParams, 'type');
        Reflect.deleteProperty(deployParams, 'uploadType');
        break;
      default:
        deployParams = null;
        break;
    }
    let _applicationName = applicationName;
    if (!!this.props.baseAppId) {
      _applicationName = `${_.get(
        this.props,
        'appValue.basicCopyValue.applicationName',
      )}-${applicationName}`;
    }
    const params = {
      appType: 'micro',
      SaeVersion: 'v2',
      AppName: _applicationName,
      AppDescription: description,
      ...namespaceConfig,
      ...deployParams,
      Cpu: spec.cpu * 1000,
      Memory: spec.memory * 1024,
      Replicas: replicas,
      AppSource: 'micro_service',
      IsStateful: getParams('stateful') === 'true' ? true : undefined,
    };
    if (!!baseAppId) {
      Reflect.set(params, 'BaseAppId', baseAppId);
      // 测试 必须是java 应用
      Reflect.set(params, 'ProgrammingLanguage', 'java');
      Reflect.set(
        params,
        'ServiceTags',
        JSON.stringify({ 'alicloud.service.tag': _.get(values, 'TagValue') }),
      );
    }
    if (this.state.isSupportHygon) {
      Reflect.set(params, 'ResourceType', resourceType);
    }
    if (NewSaeVersion) {
      Reflect.set(params, 'NewSaeVersion', NewSaeVersion);
    }
    return { appType: 'micro', applicationParams: params };
  };

  onGetValue() {
    return new Promise((resolve, reject) => {
      this.baseForm.validate(async (error, values) => {
        if (error) {
          resolve(false);
          return;
        }
        let params = this.getMicroValues(values);
        resolve(params);
        return;
      });
    });
  }

  setRefresh = () => {
    this.setState({
      refreshIndex: this.state.refreshIndex + 1,
    });
  };

  namespaceChange = () => {
    const { isSupportHygon } = this.state;
    if (isSupportHygon) {
      this.setState(
        {
          resourceType: 'default',
        },
        () => {
          this.baseForm?.field.setValue('resourceType', 'default');
        },
      );
    }
    this.setState(
      {
        specValue: { cpu: 1, memory: 2 },
      },
      () => {
        this.baseForm?.field.setValue('spec', { cpu: 1, memory: 2 });
      },
    );
    this.baseForm.validate(['applicationName']);
  };

  refreshCostSheet = () => {
    if (!this.baseForm) return;

    const field = this.baseForm?.field;
    const values = field?.getValues(['appType', 'scaleConfig', 'resourceType', 'spec', 'replicas']);
    const { refreshCostOptions } = this.props;
    const { resourceType, spec, replicas } = values || {};
    let _replicas = replicas;
    refreshCostOptions &&
      refreshCostOptions({
        appType: 'micro',
        replicas: _replicas || 2,
        spec: spec || { cpu: 1, memory: 2 },
        resourceType: resourceType || '',
      });
  };

  componentDidUpdate(prevProps, prevState) {
    // 应用复制 逻辑
    const { appValue, vpcId, baseAppId } = this.props;
    if (prevProps.appValue !== appValue) {
      this.baseForm.field.setValues(appValue.basicCopyValue);
      // 灰度应用不用回填应用名称
      if (_.get(appValue, 'basicCopyValue.appType', 'micro') && !!baseAppId) {
        this.baseForm.field.setValues({ applicationName: '' });
      }
    }
    if (prevProps.vpcId !== vpcId) {
      setTimeout(() => {
        this.baseForm.validate(['namespaceID']);
      }, 1000);
    }
  }

  deployValidation = (value) => {
    switch (value.type) {
      case DEPLOY_TYPE.IMAGE:
        return value.image ? true : intl('saenext.app-create.web-app.BasicCreator.SelectAnImage');
      case DEPLOY_TYPE.REPOISTORY:
        return Object.keys(value.CodeConfig).length > 0 && Object.keys(value.BuildConfig).length > 0
          ? true
          : intl('saenext.app-create.web-app.BasicCreator.SetTheSourceCodeContinuous');
      case DEPLOY_TYPE.WEB_PACKAGE:
      case DEPLOY_TYPE.MICRO_PACKAGE:
        return value.PackageUrl
          ? true
          : intl('saenext.app-create.web-app.BasicCreator.PleaseConfigureTheCodePackage');
      default:
        return intl(
          'saenext.app-create.web-app.BasicCreator.NoApplicationDeploymentMethodSelected',
        );
    }
  };

  render() {
    const {
      appType = 'web',
      namespaceConfig: {
        VpcId: vpcId = '',
        NamespaceId: namespaceId = this.regionId,
        AutoConfig = true,
      } = {},
      deployConfig,
    } = this.baseForm?.getValue() || {};
    const { cpuMemData, isSupportHygon, resourceType } = this.state;

    return (
      <React.Fragment>
        <Form
          inline
          {...this.props}
          onChange={this.setRefresh}
          ref={(c) => {
            if (c) {
              this.baseForm = c.getInstance();
            }
          }}
        >
          <CollapseField
            title={intl('saenext.app-create.web-app.BasicCreator.BasicInformationSettings')}
            className="mt-l ml-l mr-l"
          >
            <TextField
              required
              name="applicationName"
              label={intl('saenext.app-create.web-app.BasicCreator.ApplicationName')}
              placeholder=" "
              className="w-50"
              addonTextBefore={
                !!this.props.baseAppId
                  ? _.get(this.props, 'appValue.basicCopyValue.applicationName')
                  : null
              }
              help={intl('saenext.app-create.web-app.BasicCreator.MicroNameHelp', {
                maxLength: this.props.inDebtData?.MaxAppNameLength || 36,
              })}
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.app-create.web-app.BasicCreator.TheApplicationNameCannotBe',
                  ),
                },
                {
                  type: 'customValidate',
                  param: this.applicationNameValidate,
                },
                {
                  type: 'customValidate',
                  param: this.checkDuplicateNames,
                },
              ]}
            />

            <TextField
              name="description"
              label={intl('saenext.app-create.web-app.BasicCreator.ApplicationDescription')}
              placeholder=" "
              className="w-50 mr-none"
              help={intl('saenext.app-create.web-app.BasicCreator.IntroduceTheBasicSituationOf')}
              maxLength={240}
              showLimitHint
              validation={[
                {
                  type: 'customValidate',
                  param: (value) => {
                    if (!value || value?.length <= 240) {
                      return true;
                    } else {
                      return intl(
                        'saenext.app-create.web-app.BasicCreator.TheApplicationDescriptionIsToo',
                      );
                    }
                  },
                },
              ]}
            />

            <MicroNamespaceField
              label={intl('saenext.app-create.web-app.BasicCreator.NamespaceType')}
              className="w-100"
              name="namespaceConfig"
              required={true}
              disabled={!!this.props.baseAppId}
              onNamespaceChange={this.namespaceChange}
              defaultValue={{
                AutoConfig: !getParams('namespaceId'),
                NamespaceId: getParams('namespaceId') || this.regionId,
              }}
              validation={[
                {
                  type: 'customValidate',
                  param: (value) => {
                    if (value.AutoConfig === true) {
                      return true;
                    }
                    const { NamespaceId, VpcId, VSwitchId, SecurityGroupId } = value;
                    if (
                      deployConfig?.instanceId &&
                      !deployConfig?.acrAssumeRoleArn &&
                      !deployConfig?.imagePullSecrets &&
                      !deployConfig?.vpc?.includes(VpcId)
                    ) {
                      return intl(
                        'saenext.app-create.web-app.BasicCreator.SelectANamespaceAndEnsure',
                      );
                    }
                    if (!NamespaceId && !VpcId && !VSwitchId && !SecurityGroupId) {
                      return intl(
                        'saenext.app-create.web-app.BasicCreator.SelectANamespaceVpcVswitch',
                      );
                    }
                    if (VSwitchId) {
                      if (isSupportHygon && resourceType === 'haiguang') {
                        const _vSwitchIds = VSwitchId?.split(',') || [];
                        const hygonSupportZones = get(this.props.hygonSupportInfo, 'zones', []);
                        const notHygonSupportZones = filter(_vSwitchIds, (item) => {
                          return !includes(hygonSupportZones, this.state.vSwitchMap[item]?.ZoneId);
                        });
                        if (notHygonSupportZones.length > 0) {
                          if (notHygonSupportZones.length === _vSwitchIds.length) {
                            return intl(
                              'saenext.app-create.micro-app.BasicCreator.SelectAZoneSwitchThat',
                            );
                          } else {
                            return intl(
                              'saenext.app-create.micro-app.BasicCreator.OnlyZoneSwitchesThatSupport',
                            );
                          }
                        }
                      }
                      const _vSwitchIds = VSwitchId?.split(',') || [];
                      if(_vSwitchIds.length > 5){
                        return ' '
                      }
                    }
                    if (NamespaceId && VpcId && VSwitchId && SecurityGroupId) {
                      return true;
                    } else {
                      return ' ';
                    }
                  },
                },
              ]}
              isSupportHygon={isSupportHygon}
              hygonSupportInfo={this.props.hygonSupportInfo}
              updateVswitchList={(v) => {
                let _vSwitchMap = {};
                v.forEach((item) => {
                  _vSwitchMap[item.VSwitchId] = { ...item };
                });
                this.setState({
                  vSwitchMap: _vSwitchMap,
                });
              }}
            />

            <DeploySelectorField
              required
              name="deployConfig"
              className="deploy-group mb-s"
              label={intl('saenext.app-create.web-app.BasicCreator.ApplicationDeploymentMethod')}
              defaultValue={{
                type: DEPLOY_TYPE.IMAGE,
                image: `registry.${this.regionId}.aliyuncs.com/sae-serverless-demo/sae-demo:microservice-java-provider-v1.0`,
                accelerationType: 'Default',
                ProgrammingLanguage: 'java',
                Jdk: 'Open JDK 8',
              }}
              onChange={this.onImageChange}
              allowTypes={[DEPLOY_TYPE.IMAGE, DEPLOY_TYPE.MICRO_PACKAGE]}
              appType="micro"
              vpcId={vpcId}
              namespaceId={namespaceId}
              actionType="create"
              validation={[
                {
                  type: 'customValidate',
                  param: this.deployValidation,
                },
              ]}
            />
          </CollapseField>

          <If condition={!!this.props.baseAppId}>
            <CollapseField
              title={intl('saenext.app-create.micro-app.BasicCreator.GrayscaleLabel')}
              className="mt-l ml-l mr-l"
            >
              <Message type="notice" className="mb-l">
                <div className="text-line">
                  {intl('saenext.app-create.micro-app.BasicCreator.TheFullLinkGrayScale')}
                </div>
              </Message>
              <TextField
                required
                name="TagName"
                label={intl('saenext.app-create.micro-app.BasicCreator.Name')}
                className="w-50"
                disabled
                value="alicloud.service.tag"
              />

              <TextField
                name="TagValue"
                label={intl('saenext.app-create.micro-app.BasicCreator.Value')}
                className="w-50 mr-none"
                maxLength={20}
                showLimitHint
                placeholder={intl('saenext.app-create.micro-app.BasicCreator.EnterATagValue')}
                validation={[
                  {
                    type: 'required',
                    message: intl('saenext.app-create.micro-app.BasicCreator.TheTagValueCannotBe'),
                  },
                  {
                    type: 'customValidate',
                    param: (value) => {
                      if(!/^[a-zA-Z0-9-]+$/.test(value)){
                        return  intl('saenext.shared.GrayTags.TagValuesCanOnlyConsist')
                      }else{
                        return true
                      }
                    },
                  },
                ]}
                help={intl('saenext.app-create.micro-app.BasicCreator.TheTagValueIsTo')}
              />
            </CollapseField>
          </If>

          <CollapseField
            title={intl('saenext.app-create.web-app.BasicCreator.CapacitySettings')}
            className="mt-l ml-l mr-l"
          >
            {isSupportHygon && (
              <Form.Item
                className="mb-l"
                label={intl('saenext.micro-app.basic-info.AppBaseInfo.ResourceType')}
                required
                style={{ width: '100%' }}
              >
                <Radio.Group
                  name="resourceType"
                  value={resourceType}
                  onChange={(value) => {
                    this.setState(
                      {
                        resourceType: value as string,
                        specValue:
                          value === 'haiguang' ? { cpu: 2, memory: 8 } : { cpu: 1, memory: 2 },
                      },
                      () => {
                        this.baseForm?.field.setValue('resourceType', value);
                        this.baseForm?.field.setValue(
                          'spec',
                          value === 'haiguang' ? { cpu: 2, memory: 8 } : { cpu: 1, memory: 2 },
                        );
                        this.refreshCostSheet();
                        this.baseForm?.field.validate('namespaceConfig');
                      },
                    );
                  }}
                >
                  <Radio value="default">
                    {intl('saenext.app-create.micro-app.BasicCreator.Default')}
                  </Radio>
                  <ToolTipCondition
                    show={AutoConfig}
                    tip={intl(
                      'saenext.app-create.micro-app.BasicCreator.SupportedWhenTheNamespaceType',
                    )}
                    align="r"
                  >
                    <Radio value="haiguang" disabled={AutoConfig}>
                      {intl('saenext.app-create.micro-app.BasicCreator.Hygon')}

                      {AutoConfig && <Icon type="help_fill" size="xs" style={{ color: '#888' }} />}
                    </Radio>
                  </ToolTipCondition>
                </Radio.Group>
              </Form.Item>
            )}
            <Loading visible={cpuMemData?.length === 0}>
              {/* @ts-ignore */}
              <CpuMemSelectorField
                key={resourceType === 'haiguang' ? 'hygonSpecs' : 'defaultSpec'}
                required
                hasClear
                name="spec"
                label={intl('saenext.app-create.web-app.BasicCreator.SingleInstanceType')}
                defaultValue={
                  resourceType === 'haiguang' ? { cpu: 1, memory: 2 } : { cpu: 2, memory: 8 }
                }
                value={this.state.specValue}
                className="mr-xl"
                validation={[
                  {
                    type: 'required',
                    message: intl(
                      'saenext.app-create.web-app.BasicCreator.SelectASingleInstanceType',
                    ),
                  },
                ]}
                dataSource={resourceType === 'haiguang' ? hygonSpecs : cpuMemData}
                onChange={(value) => {
                  this.baseForm?.field.setValue('spec', value);
                  this.refreshCostSheet();
                  this.setState({
                    specValue: value,
                  });
                }}
              />
            </Loading>

            <NumberField
              required
              name="replicas"
              label={intl('saenext.app-create.web-app.BasicCreator.NumberOfInstances')}
              defaultValue={2}
              min={1}
              validation={[
                {
                  type: 'required',
                  message: intl('saenext.app-create.web-app.BasicCreator.SelectARangeOfAuto'),
                },
                {
                  type: 'customValidate',
                  param: (value) => {
                    if (value > this.state.limitInstances) {
                      return intl(
                        'saenext.app-create.web-app.BasicCreator.TheMaximumNumberOfSingle',
                        { thisStateLimitInstances: this.state.limitInstances },
                      );
                    }
                    return true;
                  },
                },
              ]}
              onChange={() => this.refreshCostSheet()}
            />
          </CollapseField>
        </Form>
      </React.Fragment>
    );
  }
}

export default BasicCreator;
