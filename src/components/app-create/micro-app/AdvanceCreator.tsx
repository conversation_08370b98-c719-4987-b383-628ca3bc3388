import { intl } from '@ali/cnd';
import React, { Component, RefObject } from 'react';
import { ConsoleContext } from '@ali/cnd';
import Form from '@ali/deep-form';
import CollapseField from '~/components/shared/CollapseField';
import VariableField from '~/components/shared/VariableField/VariableField';
import HealthCheckField from '~/components/shared/HealthCheckField/micro-app';
import SlsSelectorField from '~/components/shared/SlsSelectorField';
import KafkaSelectorField from '~/components/shared/KafkaSelectorField';
import NasSelectorField from '~/components/shared/NasSelectorField/micro-app';
import OssSelectorField from '~/components/shared/OssSelectorField/micro-app';
import ConfigManageField from '~/components/shared/ConfigManageField/index_back';
import SecretField from '~/components/shared/SecretField';
import <PERSON><PERSON><PERSON>urst<PERSON>ield from '~/components/shared/CpuBurstField';
import EbpfMonitorConfig from '~/components/shared/EbpfMonitorConfig';
import PhpArmsMonitorField from '~/components/shared/PhpArmsMonitorField';
import ArmsMonitorNew from '~/components/shared/ArmsMonitorNew';
import PrometheusMonitorField from '~/components/shared/PrometheusMonitorField';
import JavaPackageStartCmd from '~/components/shared/JavaPackageStartCmd';
import {
  LANGUAGE_TYPE,
  PACKAGE_TYPE,
  LANGUAGE_NAME,
} from '~/components/shared/DeploySelectorField/constant';
import If from '~/components/shared/If';
import { isPhpHighVersion, isTypeImage } from '~/utils/global';
import NacosRegistrationField, {
  formatRegistration,
  parseRegistration,
} from '~/components/shared/NacosRegistrationField';
import K8sRegistrationField from '~/components/shared/K8sRegistrationField';
import NetworkAccessField from '~/components/shared/NetworkAccessField';
import CustomHostField from '~/components/shared/CustomHostField';
import OidcRole from '~/components/shared/OidcRole';
import { formatHosts, parseHosts } from '~/components/shared/CustomHostField/utils';
import { formatPvtz } from '~/components/shared/K8sRegistrationField/utils';
import { pick, get, has, includes, isEmpty } from 'lodash';
import Sidecar from '~/components/shared/Sidecar';
import LifeCycle from '~/components/shared/LifeCycle';
import CommandArgsForm from '~/components/shared/CommandArgsForm';
import MicroServceConfigField from '~/components/shared/MicroServiceConfigField';
import DiskSizeConfigField from '~/components/shared/DiskSizeConfigField';
import { confFeature } from '@alicloud/console-one-conf';
import CachedData from '~/cache/common';

type Props = {
  ref: RefObject<any>;
  className?: string;
  basicConfig: { [key: string]: any };
  feature?: { [key: string]: any };
  v1Micro?: boolean;
  appValue: { [key: string]: any };
  NewSaeVersion: string;
  hasMseRole: boolean;
  baseAppId: string;
  enableSaeStdVersionNewMse: boolean;
  agentVersion: string;
  enableCustomNasMountOptions: boolean;
};

type States = {
  storageKey: string;
  clearReadinessConfirm: boolean;
};

const diskSize_enable = confFeature('diskSize_enable');
const hideCpuburst = confFeature('hideCpuburst');
class AdvanceCreator extends Component<Props, States> {
  private regionId: any;
  private advanceForm = React.createRef() as any;

  private livenessField = React.createRef() as any;
  private readinessField = React.createRef() as any;
  private startupField = React.createRef() as any;
  private slsSelectorField = React.createRef() as any;
  private kafkaSelectorField = React.createRef() as any;
  private nasSelectorField = React.createRef() as any;
  private ossSelectorField = React.createRef() as any;
  private configSelectorField = React.createRef() as any;
  private variableField = React.createRef() as any;
  private k8sField = React.createRef() as any;
  private secretSelectorField = React.createRef() as any;
  private microserviceConfigField = React.createRef() as any;
  private prometheusMonitorField = React.createRef() as any;

  constructor(props) {
    super(props);
    this.state = {
      storageKey: 'nas',
      clearReadinessConfirm: false,
    };
    //@ts-ignore
    this.regionId = ConsoleContext._currentValue.region.getCurrentRegionId();
  }

  onGetValue() {
    return new Promise((resolve, reject) => {
      this.advanceForm.validate(async (error, values) => {
        if (error) {
          resolve('');
          return;
        }
        const filtedValues = this.filterComponentKeys(values);
        const {
          startCmd,
          ossConfigs,
          hostsArr,
          burstConfig = { enableCpuBurst: false },
          MicroserviceEngineConfig = '',
          PrometheusMonitorConfig = {},
          ...rest
        } = filtedValues;
        let _microServiceEngineConfig = '';
        if (MicroserviceEngineConfig && JSON.parse(MicroserviceEngineConfig)?.Enable) {
          const MseLosslessRule = JSON.parse(MicroserviceEngineConfig)?.MseLosslessRule || {};
          if (has(MicroserviceEngineConfig, 'delayEnable')) {
            delete MicroserviceEngineConfig.delayEnable;
          }
          _microServiceEngineConfig = JSON.stringify({
            Enable: JSON.parse(MicroserviceEngineConfig)?.Enable,
            MseLosslessRule,
          });
        }
        const params = {
          ...rest,
          ...startCmd,
          ...ossConfigs,
          CustomHostAlias: formatHosts(hostsArr),
          EnableCpuBurst: get(burstConfig, 'enableCpuBurst', false),
        };
        if (has(values, 'MicroserviceEngineConfig')) {
          Reflect.set(params, 'MicroserviceEngineConfig', _microServiceEngineConfig);
        }
        // prometheus监控是否开启由sae控制 采集配置需要调用arms的接口
        if (!isEmpty(PrometheusMonitorConfig)) {
          const { EnablePrometheus = false, interval, port, metricPath } = PrometheusMonitorConfig;
          Reflect.set(params, 'EnablePrometheus', EnablePrometheus);
          if (get(PrometheusMonitorConfig, 'EnablePrometheus', false)) {
            Reflect.set(params, 'PrometheusConfig', {
              interval,
              port,
              metricPath,
            });
          }
        }
        formatRegistration(params);

        resolve(params);
      });
    });
  }

  onSetValue() {
    const { appValue = {} } = this.props;
    const {
      Command,
      CommandArgs,
      EnableCpuBurst = false,
      JarStartOptions,
      WarStartOptions,
      JarStartArgs,
      PvtzDiscovery,
      CustomHostAlias,
      OssAkId,
      OssAkSecret,
      OssMountDescs,
      basicCopyValue,
      ...rest
    } = appValue;

    const values = {
      ...rest,
      burstConfig: {
        enableCpuBurst: EnableCpuBurst,
      },
      Command,
      CommandArgs,
      startCmd: {
        JarStartOptions,
        WarStartOptions,
        JarStartArgs,
      },
      ossConfigs: {
        OssAkId,
        OssAkSecret,
        OssMountDescs,
      },
      hostsArr: parseHosts(CustomHostAlias),
      PvtzDiscoverySvc: formatPvtz(PvtzDiscovery),
      ...parseRegistration(appValue),
    };
    const filtedValues = this.filterComponentKeys(values);

    this.advanceForm.setValue(filtedValues);
  }

  filterComponentKeys = (values) => {
    // 只保留页面上有对应组件的key
    const mountedComKeys = this.advanceForm.field?.getNames();
    const filtedValues = pick(values, mountedComKeys);
    return filtedValues;
  };

  componentDidMount() {
    this.onSetValue();
  }

  componentDidUpdate(prevProps): void {
    if (prevProps.basicConfig !== this.props.basicConfig) {
      const {
        basicConfig: { applicationParams },
      } = this.props;

      const { Php: phpVersion, ProgrammingLanguage: packageLanguage } = applicationParams;
      if (isPhpHighVersion(phpVersion) || packageLanguage !== LANGUAGE_TYPE.PHP) {
        this.advanceForm.field?.remove('PhpArmsConfigLocation');
      }
    }
  }

  render() {
    const {
      basicConfig: { applicationParams },
      feature: {
        ebpf = false,
        php_ebpf = false,
        microRegister = false,
        pvtzDiscovery = false,
        cpuBurst = false,
        oidc = false,
        sidecar = false,
      },
      v1Micro = false,
      NewSaeVersion = '',
      baseAppId,
      enableSaeStdVersionNewMse,
      agentVersion,
      enableCustomNasMountOptions,
    } = this.props;

    const {
      AppName: appName,
      VpcId: vpcId,
      Cpu: cpu = 1000,
      NamespaceId: namespaceId = this.regionId,
      Php: phpVersion,
      ProgrammingLanguage: packageLanguage,
      PackageType: packageType,
      Jdk: jdk,
    } = applicationParams || {};

    const { startCmd: { Command } = {} as any } = this.advanceForm?.getValue?.() || {};

    const hasMicroService =
      NewSaeVersion === 'pro' ||
      ((!NewSaeVersion || NewSaeVersion === 'std') &&
        enableSaeStdVersionNewMse &&
        packageLanguage === LANGUAGE_TYPE.JAVA);

    return (
      <Form
        {...this.props}
        ref={(c) => {
          if (c) {
            this.advanceForm = c.getInstance();
          }
        }}
        onChange={(val) => {
          this.setState({});
        }}
      >
        <If condition={isTypeImage(packageType)}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.StartCommand')}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.SetCommandsRequiredForContainer',
            )}
            linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetStartupCommands')}
            linkHref={CachedData.confLink('help:sae:set-startup-command-2-0')}
            className="mt-l ml-l mr-l"
          >
            <CommandArgsForm field={this.advanceForm.field} />
          </CollapseField>
        </If>

        <If condition={packageType === PACKAGE_TYPE.JAR || packageType === PACKAGE_TYPE.WAR}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.StartCommandSettings')}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.SetCommandsForJavaApplication',
            )}
            linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetStartupCommands')}
            linkHref={CachedData.confLink('help:sae:configure-a-startup-command')}
            className="mt-l ml-l mr-l"
          >
            <JavaPackageStartCmd packageType={packageType} jdk={jdk} name="startCmd" />
          </CollapseField>
        </If>

        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.EnvironmentVariables')}
          subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.SetSomeVariablesInThe')}
          linkText={intl(
            'saenext.app-create.micro-app.AdvanceCreator.HowToSetEnvironmentVariables',
          )}
          linkHref={CachedData.confLink('help:sae:configure-environment-variables-2-0')}
          className="mt-l ml-l mr-l"
        >
          <VariableField
            name="Envs"
            namespaceId={namespaceId}
            ref={(ref) => (this.variableField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: async () => {
                  return await this.variableField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        </CollapseField>

        <If
          condition={
            ((!NewSaeVersion || NewSaeVersion === 'std') &&
              ((packageType === 'Image' && packageLanguage === LANGUAGE_TYPE.GO) ||
                packageLanguage === LANGUAGE_TYPE.PYTHON)) ||
            (NewSaeVersion === 'pro' &&
              includes(
                [LANGUAGE_TYPE.JAVA, LANGUAGE_TYPE.PYTHON, LANGUAGE_TYPE.GO],
                packageLanguage,
              ))
          }
        >
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.ApplicationMonitoring')}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.ProvidesRealTimeMonitoringFor',
            )}
            linkText={
              NewSaeVersion
                ? intl('saenext.app-create.micro-app.AdvanceCreator.ViewApplicationMonitoring')
                : ''
            }
            linkHref={
              NewSaeVersion
                ? NewSaeVersion === 'pro'
                  ? CachedData.confLink('help:sae:app-monitor-pro')
                  : CachedData.confLink('help:sae:app-monitor-std')
                : ''
            }
            className="mt-l ml-l mr-l"
          >
            <ArmsMonitorNew
              name="EnableNewArms"
              lang={packageLanguage}
              packageType={packageType}
              NewSaeVersion={NewSaeVersion}
              setValue={() => {
                this.advanceForm.field?.setValue(
                  'EnableNewArms',
                  get(this.props.appValue, 'EnableNewArms', false),
                );
              }}
            />
          </CollapseField>
        </If>

        <If condition={NewSaeVersion !== 'lite'}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.PrometheusMonitoring')}
            subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.FullyConnectedToTheOpen')}
            linkText={intl(
              'saenext.app-create.micro-app.AdvanceCreator.HowToAccessPrometheusMonitoring',
            )}
            linkHref=""
            className="mt-l ml-l mr-l"
          >
            <PrometheusMonitorField
              name="PrometheusMonitorConfig"
              appConfig={this.props.appValue}
              ref={(ref) => (this.prometheusMonitorField = ref)}
              type='create'
              validation={[
                {
                  type: 'customValidate',
                  param: () => {
                    return this.prometheusMonitorField?.getInstance()?.controlRef?.validate?.();
                  },
                },
              ]}
            />
          </CollapseField>
        </If>

        <If
          condition={
            (!NewSaeVersion || NewSaeVersion === 'std') &&
            !isPhpHighVersion(phpVersion) &&
            packageLanguage === LANGUAGE_TYPE.PHP &&
            ebpf &&
            !php_ebpf
          }
        >
          <CollapseField
            title={intl(
              'saenext.app-create.micro-app.AdvanceCreator.PhpApplicationMonitoringSettings',
            )}
            linkText={intl(
              'saenext.app-create.micro-app.AdvanceCreator.HowToConfigureApplicationMonitoring',
            )}
            linkHref={CachedData.confLink(
              'help:sae:configure-a-configuration-file-for-a-php-application',
            )}
            className="mt-l ml-l mr-l"
          >
            <PhpArmsMonitorField
              name="PhpArmsConfigLocation"
              defaultValue="/usr/local/etc/php/conf.d/arms.ini"
            />
          </CollapseField>
        </If>

        <If
          condition={
            (!NewSaeVersion || NewSaeVersion === 'std') &&
            ((packageLanguage === LANGUAGE_TYPE.PYTHON && ebpf) ||
              (packageLanguage === LANGUAGE_TYPE.PHP && ebpf && php_ebpf))
          }
        >
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.ApplicationMonitoring')}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.TheSwitchControlsWhetherThe',
            )}
            linkText={intl(
              'saenext.app-create.micro-app.AdvanceCreator.HowToConfigureApplicationMonitoring',
            )}
            linkHref={CachedData.confLink(
              'help:sae:configure-a-configuration-file-for-a-php-application',
            )}
            className="mt-l ml-l mr-l"
          >
            <EbpfMonitorConfig />
          </CollapseField>
        </If>

        <If condition={v1Micro}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.ServiceRegistrationDiscovery')}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.JavaMicroservicesProvideAutomaticAddressing',
            )}
            linkText={intl(
              'saenext.app-create.micro-app.AdvanceCreator.ServiceRegistrationDiscovery',
            )}
            linkHref={CachedData.confLink('help:sae:service-registration-and-discovery')}
            className="mt-l ml-l mr-l"
            collapsed={true}
          >
            <NacosRegistrationField
              field={this.advanceForm.field}
              microRegistration={get(this.props.appValue, 'MicroRegistration', '0')}
              microRegistrationConfig={get(this.props.appValue, 'MicroRegistrationConfig', '{}')}
            />

            <If condition={pvtzDiscovery}>
              <div className="border-t mt-l mb-l" />
            </If>
            {pvtzDiscovery && (
              <K8sRegistrationField
                name="PvtzDiscoverySvc"
                appName={appName}
                namespaceId={namespaceId || this.regionId}
                ref={(ref) => (this.k8sField = ref)}
                validation={[
                  {
                    type: 'customValidate',
                    param: () => {
                      return this.k8sField?.getInstance()?.controlRef?.validate?.();
                    },
                  },
                ]}
              />
            )}
          </CollapseField>
        </If>

        <If condition={v1Micro}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.HostsBindingSettings')}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.EnterTheConfigurationInThe',
            )}
            linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetHostsBinding')}
            linkHref={CachedData.confLink('help:sae:configure-host-bindings-1')}
            collapsed={true}
            className="mt-l ml-l mr-l"
          >
            <CustomHostField name="hostsArr" />
          </CollapseField>
        </If>

        <If condition={hasMicroService}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.MicroserviceGovernance')}
            subTitle={
              intl(
                'saenext.app-create.micro-app.AdvanceCreator.ProvidesMicroserviceGovernanceFunctionsFor',
                { LANGUAGENAMEPackageLanguage: LANGUAGE_NAME[packageLanguage] },
              ) as string
            }
            linkText={intl(
              'saenext.app-create.micro-app.AdvanceCreator.HowToConfigureMicroserviceGovernance',
            )}
            linkHref={CachedData.confLink('help:sae:enable-microservices-governance')}
            className="mt-l ml-l mr-l"
          >
            <MicroServceConfigField
              name="MicroserviceEngineConfig"
              ref={(ref) => (this.microserviceConfigField = ref)}
              validation={[
                {
                  type: 'customValidate',
                  param: () => {
                    return this.microserviceConfigField?.getInstance()?.controlRef?.validate?.();
                  },
                },
              ]}
              onChangeReadniess={(v) => {
                this.advanceForm.field?.setValue('Readiness', v);
                this.setState({
                  clearReadinessConfirm: v === '{}' ? true : false,
                });
              }}
              readinessFieldValues={this.advanceForm.field?.getValue('Readiness')}
              appConfig={this.props.appValue}
              baseAppId={baseAppId}
              NewSaeVersion={NewSaeVersion}
              hasMicroService={hasMicroService}
              agentVersion={agentVersion}
            />
          </CollapseField>
        </If>

        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.ApplyHealthCheckSettings')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.UsedToDetermineWhetherContainers',
          )}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetApplicationHealth')}
          linkHref={CachedData.confLink('help:sae:set-health-check-2-0')}
          className="mt-l ml-l mr-l"
          collapsed={!hasMicroService}
        >
          <HealthCheckField
            name="Liveness"
            activeKey="Liveness"
            ref={(ref) => (this.livenessField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.livenessField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
            microserviceEngineConfig={this.advanceForm.field?.getValue('MicroserviceEngineConfig')}
            NewSaeVersion={NewSaeVersion}
            hasMicroService={hasMicroService}
          />

          <div className="border-t mt-l mb-l" />
          <HealthCheckField
            name="Readiness"
            activeKey="Readiness"
            ref={(ref) => (this.readinessField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.readinessField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
            microserviceEngineConfig={this.advanceForm.field?.getValue('MicroserviceEngineConfig')}
            NewSaeVersion={NewSaeVersion}
            hasMicroService={hasMicroService}
            appConfig={this.props.appValue}
            clearReadinessConfirm={this.state.clearReadinessConfirm}
          />

          <div className="border-t mt-l mb-l" />

          <HealthCheckField
            name="StartupProbe"
            activeKey="StartupProbe"
            ref={(ref) => (this.startupField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.startupField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        </CollapseField>

        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.ApplicationLifecycleManagement')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.LifecycleScriptsDefineAndManage',
          )}
          linkText={intl(
            'saenext.app-create.micro-app.AdvanceCreator.HowToSetApplicationLifecycle',
          )}
          linkHref={CachedData.confLink('help:sae:configure-application-lifecycle-management-2-0')}
          className="mt-l ml-l mr-l"
          collapsed={
            isEmpty(this.props.appValue?.PostStart) &&
            isEmpty(this.props.appValue?.PreStop) &&
            !this.props.appValue?.TerminationGracePeriodSeconds
          }
        >
          <LifeCycle
            NewSaeVersion={NewSaeVersion}
            appConfig={this.props.appValue}
            microserviceEngineConfig={this.advanceForm.field?.getValue('MicroserviceEngineConfig')}
            field={this.advanceForm.field}
            hasMicroService={hasMicroService}
          />
        </CollapseField>

        <If condition={v1Micro}>
          <CollapseField
            title={intl(
              'saenext.app-create.micro-app.AdvanceCreator.ApplyOutboundInboundInternetAccess',
            )}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.ApplyOutboundInboundSettingsSuch',
            )}
            linkText={intl(
              'saenext.app-create.micro-app.AdvanceCreator.HowToConfigureInternetAccess',
            )}
            linkHref={CachedData.confLink(
              'help:sae:configure-and-access-public-network-based-on-eip',
            )}
            className="mt-l ml-l mr-l"
            collapsed={true}
          >
            <NetworkAccessField name="AssociateEip" />
          </CollapseField>
        </If>

        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.LogConfiguration')}
          subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.SetLogCollectionRulesTo')}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToConfigureLogCollection')}
          linkHref={CachedData.confLink('help:sae:log-collection-service-2-0')}
          className="mt-l ml-l mr-l"
        >
          <SlsSelectorField
            name="SlsConfigs"
            className="mt-s"
            ref={(ref) => (this.slsSelectorField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.slsSelectorField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />

          <div className="border-t mt-l mb-l" />
          <KafkaSelectorField
            name="KafkaConfigs"
            vpcId={vpcId}
            className="mt-s"
            ref={(ref) => (this.kafkaSelectorField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.kafkaSelectorField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        </CollapseField>
        <If condition={diskSize_enable}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.TemporaryStorageSettings')}
            subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.SetTheTemporaryDiskSize')}
            // linkText="如何设置临时存储"
            // linkHref=""
            className="mt-l ml-l mr-l"
          >
            <DiskSizeConfigField name="DiskSize" />
          </CollapseField>
        </If>

        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.PersistentStorage')}
          subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.SetPersistentStorageData')}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetPersistentStorage')}
          linkHref={CachedData.confLink('help:sae:persistent-storage')}
          className="mt-l ml-l mr-l"
        >
          <NasSelectorField
            name="NasConfigs"
            vpcId={vpcId}
            ref={(ref) => (this.nasSelectorField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.nasSelectorField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
            NewSaeVersion={NewSaeVersion}
            enableCustomNasMountOptions={enableCustomNasMountOptions}
          />

          <div className="border-t mt-l mb-l" />
          <OssSelectorField
            name="ossConfigs"
            ref={(ref) => (this.ossSelectorField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.ossSelectorField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
            NewSaeVersion={NewSaeVersion}
          />
        </CollapseField>

        <If condition={oidc}>
          <CollapseField
            title={intl(
              'saenext.app-create.micro-app.AdvanceCreator.IdentityAuthenticationService',
            )}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.SetTheAuthenticationMethodFor',
            )}
            linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetRrsaOidc')}
            linkHref={CachedData.confLink('help:sae:configure-authentication-service')}
            className="mt-l ml-l mr-l"
            collapsed={true}
          >
            <OidcRole field={this.advanceForm.field} name="OidcRoleName" />
          </CollapseField>
        </If>

        <If condition={NewSaeVersion !== 'lite' && !hideCpuburst}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.CpuBurstConfiguration')}
            subTitle={intl(
              'saenext.app-create.micro-app.AdvanceCreator.SetCpuBurstPerformanceOptimization',
            )}
            linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetCpuBurst')}
            linkHref={CachedData.confLink('help:sae:enable-cpu-burst-function')}
            className="mt-l ml-l mr-l"
          >
            <CpuBurstField name="burstConfig" disabled={cpu > 4000} />
          </CollapseField>
        </If>

        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.ConfigurationManagement')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.InjectConfigurationInformationIntoThe',
          )}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToManageConfigurations')}
          linkHref={CachedData.confLink('help:sae:injection-configuration-information')}
          className="mt-l ml-l mr-l"
          // collapsed={false}
        >
          <ConfigManageField
            name="ConfigMapMountDesc"
            namespaceId={namespaceId}
            ref={(ref) => (this.configSelectorField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.configSelectorField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        </CollapseField>
        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.ConfidentialDictionary')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.InjectConfidentialInformationIntoThe',
          )}
          // linkText="如何管理保密字典"
          // linkHref=""
          className="mt-l ml-l mr-l"
        >
          <SecretField
            name="SecretMountDesc"
            namespaceId={namespaceId}
            ref={(ref) => (this.secretSelectorField = ref)}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return this.secretSelectorField?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        </CollapseField>

        <If condition={sidecar}>
          <CollapseField
            title={intl('saenext.app-create.micro-app.AdvanceCreator.AddSidecarContainers')}
            className="mt-l ml-l mr-l"
            collapsed={true}
          >
            <Form.Item>
              <Sidecar
                field={this.advanceForm.field}
                name="SidecarContainersConfig"
                defaultValue={[]}
                namespaceId={namespaceId}
                appConfig={{
                  ...applicationParams,
                  NamespaceId: namespaceId,
                }}
              />
            </Form.Item>
          </CollapseField>
        </If>
      </Form>
    );
  }
}

export default AdvanceCreator;
