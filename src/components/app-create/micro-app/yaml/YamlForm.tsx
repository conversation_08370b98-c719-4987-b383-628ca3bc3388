import React from 'react';
import {
  CascaderSelect,
  Form,
  Icon,
  intl,
  Radio,
  Upload,
  YamlEditorV2,
} from '@ali/cnd';
import jsyaml from 'js-yaml';
import { get, isEmpty } from 'lodash';
import If from '~/components/shared/If';
import C from '~/constants/common';

const YamlForm = (props) => {
  const { field } = props;

  const { init, getValue, getValues, setValues, validate } = field;
  const { type = 'upload', yamlContent } = getValues();

  const yamlTemplateData = get(window, 'ALIYUN_CONSOLE_GLOBAL.yamlTemplate', []);
  const defaultYamlTemplate = get(yamlTemplateData, '[1].children[0].value', '');

  const onTypeChange = (value) => {
    if (value === 'template' && !yamlContent) {
      setValues({
        template: defaultYamlTemplate,
        yamlContent: defaultYamlTemplate
      });
    }
  }

  const onTemplateChange = (value) => {
    setValues({
      template: value,
      yamlContent: value,
    });
  };

  const beforeUpload = async (file) => {
    if (file) {
      const content = await file.text();
      setValues({
        yamlContent: content,
      });
      validate('yamlContent');
    }
    return true;
  };

  const onUploadChange = (value) => {
    if (isEmpty(value)) {
      setValues({
        yamlContent: '',
      });
    }
  };

  const validateYaml = (rule, value, callback) => {
    try {
      jsyaml.loadAll(value);
      callback();
    } catch (err) {
      callback(err.reason);
    }
  };

  return (
    <Form field={field} {...C.FORM_LAYOUT} className='yaml-deploy'>
      <Form.Item label={intl('saenext.micro-app.yaml.YamlForm.UploadMethod')}>
        <Radio.Group name="type" defaultValue="upload" onChange={onTypeChange}>
          <Radio value="upload">{intl('saenext.micro-app.yaml.YamlForm.UploadFiles')}</Radio>
          <Radio value="template">{intl('saenext.micro-app.yaml.YamlForm.UseTemplates')}</Radio>
        </Radio.Group>
        <If condition={type === 'upload'}>
          <Upload.Dragger
            limit={1}
            listType="text"
            accept=".txt, .yaml, .yml"
            beforeUpload={beforeUpload}
            onChange={onUploadChange}
          >
            <div className={!isEmpty({}) ? 'next-upload next-disabled' : ''}>
              <div className="next-upload-inner">
                <div className="next-upload-drag">
                  <p className="next-upload-drag-icon">
                    <Icon type="upload" size="large" />
                  </p>
                  <p className="next-upload-drag-text">
                    {intl('saenext.shared.DeploySelectorField.PackageUploader.ClickOrDragTheFile')}
                  </p>
                  <p className="next-upload-drag-hint">
                    {intl('saenext.micro-app.yaml.YamlForm.FilesThatSupportSuffixesSuch')}
                  </p>
                </div>
              </div>
            </div>
          </Upload.Dragger>
        </If>
        <If condition={type === 'template'}>
          <div>
            <CascaderSelect
              {...init('template')}
              dataSource={yamlTemplateData}
              onChange={onTemplateChange}
              style={{ width: 400 }}
            />
          </div>
        </If>
      </Form.Item>
      <Form.Item
        required
        name="yamlContent"
        label={intl('saenext.micro-app.yaml.YamlForm.Content')}
        asterisk={false}
        useLabelForErrorMessage
        validator={validateYaml}
      >
        <YamlEditorV2 theme="idea" isAutoHeight allowMultiYaml />
      </Form.Item>
    </Form>
  );
};

export default YamlForm;
