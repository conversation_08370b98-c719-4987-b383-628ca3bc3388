import { intl } from '@ali/cnd';
import React from 'react';
import YamlForm from './YamlForm';
import { Button, Dialog, Field, Message } from '@ali/cnd';
import jsyaml from 'js-yaml';
import { ApplyResourceJson, GetResourceJson } from '~/utils/atlasService';
import { get } from 'lodash';
import { jsonParse } from '~/utils/transfer-data';
import RequestWrap from '~/components/shared/RequestWrap';
import { NAMESPACE_KIND_PATH } from './constants';

const CreateAppYaml = (props) => {
  const {
    match: {
      params: { regionId },
    },
    history,
  } = props;

  const field = Field.useField();

  const onSubmit = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) {
      return;
    }
    const { yamlContent } = values;
    const jsonContent = jsyaml.loadAll(yamlContent as string);

    const results = await Promise.all(
      jsonContent.map((item) => {
        return onRequest(item);
      }),
    );

    const errorResults = results.filter((item) => item !== true);

    if (errorResults.length === 0) {
      Message.success(intl('saenext.micro-app.yaml.SuccessfullyDeployed'));
      redirectToPage(jsonContent[0]);
    } else {
      if (jsonContent.length === 1) {
        Message.error(intl('saenext.micro-app.yaml.DeploymentFailed'));
        return;
      }
      Dialog.alert({
        title: intl('saenext.micro-app.yaml.TheFollowingResourceDeploymentFailed'),
        content: (
          <ul>
            {errorResults.map((item) => {
              const { jsonContent } = item as { jsonContent: any };
              const { kind } = jsonContent;
              const { name, namespace } = get(jsonContent, 'metadata', {});
              const metaMsg = {
                kind,
                name,
                namespace,
              };
              return (
                <li className="list-disc">
                  <pre>{JSON.stringify(metaMsg, null, 2)}</pre>
                </li>
              );
            })}
          </ul>
        ),

        footer: false,
      });
      return;
    }
  };

  const onRequest = async (jsonContent) => {
    const result = await ApplyResourceJson({
      ResourceJson: JSON.stringify(jsonContent),
    });
    if (result?.Success) {
      return true;
    } else {
      return {
        jsonContent,
      };
    }
  };

  const redirectToPage = async (params) => {
    const { kind } = params;
    const { namespace, name } = get(params, 'metadata', {});

    const namespaceId =
      !namespace || namespace === 'default' ? regionId : `${regionId}:${namespace}`;

    // 应用部署、公网访问、弹性规则
    if (kind === 'Deployment' || kind === 'Service' || kind === 'HorizontalPodAutoscaler') {
      let appName = name;
      let query = '';

      if (kind === 'Service') {
        appName = get(params, ['spec', 'selector', 'sae.aliyun.com/app-name']);
      } else if (kind === 'HorizontalPodAutoscaler') {
        appName = get(params, 'spec.scaleTargetRef.name');
        query = `?tab=hpa`;
      }

      const appId = await getAppId({
        namespaceId,
        name: appName,
      });
      if (!appId) return;
      history.push(`/${regionId}/app-list/${appId}/micro-app/base${query}`);
    } else if (NAMESPACE_KIND_PATH[kind]) {
      history.push(`/${regionId}/namespace/${namespaceId}/${NAMESPACE_KIND_PATH[kind]}`);
    } else {
      history.push(`/${regionId}/app-list/micro`);
    }
  };

  const getAppId = async (params: { namespaceId: string; name: string }) => {
    const { namespaceId, name } = params;
    const result = await GetResourceJson({
      ResourceType: 'deployment',
      NamespaceId: namespaceId,
      ResourceName: name,
    });
    if (!result?.Success) {
      return;
    }
    const { Data } = result;
    const { uid: AppId } = get(jsonParse(Data), 'metadata', {});
    return AppId;
  };

  const onCancel = () => {
    history.push(`/${regionId}/app-list/micro`);
  };

  return (
    <div className="app-creator-container">
      <div className="pl-xl pr-xl">
        <Message type="notice" className="mb">
          {intl('saenext.micro-app.yaml.NoteTheNamespaceIdField', {
            regionId: regionId,
          })}
        </Message>
        <YamlForm field={field} />
      </div>
      <div className="step-footer">
        <RequestWrap>
          <Button type="primary" onClick={onSubmit}>
            {intl('saenext.micro-app.yaml.Deployment')}
          </Button>
        </RequestWrap>
        <Button className="ml" onClick={onCancel}>
          {intl('saenext.micro-app.yaml.Cancel')}
        </Button>
      </div>
    </div>
  );
};

export default CreateAppYaml;
