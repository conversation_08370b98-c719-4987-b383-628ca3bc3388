import { intl } from '@ali/cnd';
import React, { Fragment, useContext, useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Step, Button, Message, ConsoleContext, Dialog } from '@ali/cnd';
import MicroBasicCreator from './BasicCreator';
import MicroAdvanceCreator from './AdvanceCreator';
import { microStepsDataSource } from '../constants';
import services from '~/services';
import { trackResource, AES_CONSTANT } from '~/tracker';
import ComShow from '~/components/shared/ComShow';
import If from '~/components/shared/If';
import FeatureContext from '~/utils/featureContext';
import CostSheet from '../CostSheet';
import { isEmpty, get, isEqual, cloneDeep } from 'lodash';
import { microAppLink } from '~/utils/global';
import CachedData from '~/cache/common';
import { PrometheusPolicyName } from '~/constants/ignoreMust';

const MicroAppCreator = ({ history, initialStep, appValue, baseAppId, version='' }) => {
  const basicEl = useRef(null);
  const advanceEl = useRef(null);

  const [NewSaeVersion, setNewSaeVersion] = useState<any>(version);
  const [hasMseRole, setHasMseRole] = useState(false);
  const [basicConfig, setBasicConfig] = useState<any>({});
  // 当前步骤
  const [currentStep, setCurrentStep] = useState(initialStep);
  // 是否访问过高级设置的步骤
  const [recordStep, setRecordStep] = useState(initialStep);
  const [createLoading, setCreateLoading] = useState(false);
  const [advanceLoading, setAdvanceLoading] = useState(false);
  const [costOptions, refreshCostOptions] = useState({
    appType: 'micro',
    replicas: 2,
    spec: { cpu: 1, memory: 2 },
    resourceType: '',
  });

  const [vpcId, setVpcId] = useState('');

  const BASIC_STEP = microStepsDataSource.basic.key;
  const ADVANCE_STEP = microStepsDataSource.advance.key;

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const { feature, inDebtData, webFeature} = useContext(FeatureContext);
  const hygonSupportInfo = get(window, 'ALIYUN_CONSOLE_GLOBAL.hygonSupportInfo', {
    regions: [],
    zones: [],
  });
  // const hygonSupportInfo = {"regions":["cn-shanghai","cn-hangzhou","cn-chengdu","cn-beijing"],"zones":["cn-shanghai-b","cn-shanghai-g","cn-shanghai-l","cn-hangzhou-j","cn-beijing-i","cn-chengdu-b"]}

  // 对于存量用户使用enableSaeStdVersionNewMse判断是否透出微服务治理
  const enableSaeStdVersionNewMse = feature?.enableSaeStdVersionNewMse;

  // 专业版 Agent版本 4.3.0及以上支持微服务治理--创建应用时取用户级别的agentVersion
  const AgentDownloadUrl = get(inDebtData, 'AgentDownloadUrl', '');
  const agentVersionMatch = AgentDownloadUrl.match(/\/(\d+\.\d+\.\d+(?:\.\d+)?)\//);
  const agentVersion = agentVersionMatch ? agentVersionMatch[1] : get(window, 'ALIYUN_CONSOLE_GLOBAL.DefaultAgentVersion', '4.2.5');

  const enableCustomNasMountOptions = feature?.enableCustomNasMountOptions;

  useEffect(() => {
    setCurrentStep(BASIC_STEP);
    return () => {
      const existApps = parseInt(window.localStorage.getItem('EXIST_APPS')) || 0;
      trackResource({
        behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE,
        stage: AES_CONSTANT.AES_STAGE_TYPE.EXIT,
        existApps,
      });
    };
  }, []);

  useEffect(()=>{
    if(appValue?.NewSaeVersion){
      setNewSaeVersion(appValue?.NewSaeVersion)
    }
  },[appValue])

  useEffect(() => {
    if (NewSaeVersion === 'pro') {
      // 判断是有MSE服务关联角色AliyunServiceRoleForMSE
      handleCheckMseRole();
    }
  }, [NewSaeVersion]);

  const handleCheckMseRole = async () => {
    const res = await services.checkMseRole();
    const HasServiceLinkRole = get(res, 'Data.HasServiceLinkRole', false);
    setHasMseRole(HasServiceLinkRole);
  };

  const handleQuickCreate = () => {
    if (recordStep >= 1) {
      handleBeforeCreate(handlerAdvanceCreator);
    } else {
      handleBeforeCreate(handlerBasicCreator);
    }
  };

  const handleBeforeCreate = async (createApp) => {
    const res = await basicEl.current?.onGetValue();
    const { applicationParams: { scaleConfig = {} } = {} } = res || {};

    Reflect.deleteProperty(scaleConfig, 'warnInstanceCount');
    createApp();
  };

  const handlerAdvanceStep = async () => {
    setAdvanceLoading(true);
    const basicConfig = await basicEl.current?.onGetValue();

    if (!basicConfig) {
      setAdvanceLoading(false);
      return;
    }
    setBasicConfig(basicConfig);
    setCurrentStep(ADVANCE_STEP);
    setRecordStep(currentStep + 1);
    setAdvanceLoading(false);
  };

  const handleBackStep = async () => {
    setAdvanceLoading(true);
    const advanceConfig = await advanceEl.current?.onGetValue();

    if (!basicConfig || !advanceConfig) {
      setAdvanceLoading(false);
      return;
    }
    setCurrentStep(BASIC_STEP);
    setAdvanceLoading(false);
  };

  const handlerBasicCreator = async () => {
    setCreateLoading(true);

    const basicConfig = await basicEl.current?.onGetValue();
    if (!basicConfig) {
      setCreateLoading(false);
      return;
    }
    const { applicationParams } = basicConfig;
    await createMicroApp(applicationParams);

    setCreateLoading(false);
  };

  const handlerAdvanceCreator = async () => {
    setCreateLoading(true);
    const advanceConfig = await advanceEl.current?.onGetValue();
    const _basicConfig = await basicEl.current?.onGetValue();
    if (!_basicConfig || !advanceConfig) {
      setCreateLoading(false);
      return;
    }
    const applicationParams = {
      ..._basicConfig.applicationParams,
      ...advanceConfig,
    };
    await createMicroApp(applicationParams);

    setCreateLoading(false);
  };

  const createMicroApp = async (params) => {
    let PrometheusConfig = {};
    const EnablePrometheus = params?.EnablePrometheus;
    if(params?.EnablePrometheus){
      PrometheusConfig = cloneDeep(params?.PrometheusConfig);
      delete params?.EnablePrometheus;
      delete params?.PrometheusConfig;
    }
    // 存量非白名单用户 enableSaeStdVersionNewMse支持时 MicroserviceEngineConfig不为空 NewSaeVersion手动设置为std
    if (enableSaeStdVersionNewMse && params?.MicroserviceEngineConfig && !params?.NewSaeVersion) {
      params.NewSaeVersion = 'std';
    }
    if(appValue?.NewSaeVersion){
      params.NewSaeVersion = appValue.NewSaeVersion;
    }
    // 创建应用
    const { Data = {} } =
      (await services.createMicroApplication({
        params: {
          ...params,
        },
        content: {
          ...params,
        },
      })) || {};
    if (Data.AppId) {
      Message.success(intl('saenext.components.app-create.TheMicroserviceApplicationHasBeen'));
      // 专业版应用开启了MSE微服务治理 && 未授权MSE服务关联角色 --- 主动进行授权
      if (
        params?.NewSaeVersion === 'pro' &&
        params?.MicroserviceEngineConfig &&
        JSON.parse(params?.MicroserviceEngineConfig)?.Enable &&
        !hasMseRole
      ) {
        services.createMseRole();
      }
      // 应用开启了 Prometheus 监控，promethues采集信息需要下发给Prometheus
      if(EnablePrometheus){
        try {
          await handlePrometheusMonitor(PrometheusConfig, Data.AppId, params?.VpcId);
        } catch (error) {
          console.error('Error handling Prometheus monitor:', error);
        }
      }
      const { link, enable } = microAppLink(Data.AppId, regionId);
      if (enable) {
        history.push(link);
      } else {
        history.push(`/${regionId}/app-list/micro`);
        window.open(link);
      }
    }
  };

  const handlePrometheusMonitor = async(config, appId, vpcId)=>{
    let _vpcId = vpcId;
    if(!vpcId){
      const appInfo = await services.describeMicroApplicationConfig({
        AppId: appId,
      });
      _vpcId = get(appInfo,'Data.VpcId','');
    }
    const policyRes = await services.CreateIntegrationPolicy({
      params: {
        content: {
          policyName: CachedData.lang === 'zh-CN' ? PrometheusPolicyName : 'SAE-IntegrationPolicy',
          policyType: 'SAE',
        },
      },
    });
    const policyId = policyRes?.policy?.policyId || '';
    if(policyId){
      await services.CreateAddonRelease({
        params: {
          policyId,
        },
        content: {
          addonName: 'cloud-sae-custom',
          envType: 'ECS',
          releaseName: `sae-custom-${appId}`,
          version: '*',
          values: JSON.stringify({
            _entity: {
              instance_id: appId,
              vpc_id: _vpcId,
            },
            ...config
          }),
        },
      });
    }
  }

  return (
    <Fragment>
      <div className="app-creator-container">
        <div className="step-content">
          <Step current={currentStep} shape="circle" labelPlacement="hoz">
            <Step.Item
              key={BASIC_STEP}
              title={intl('saenext.components.app-create.BasicApplicationInformation')}
              onClick={(index) => setCurrentStep(index)}
            />

            <Step.Item
              key={ADVANCE_STEP}
              title={intl('saenext.components.app-create.AdvancedSettings')}
              onClick={(index) => setCurrentStep(index)}
            />
          </Step>
          <ComShow if={currentStep === BASIC_STEP}>
            <MicroBasicCreator
              regionId={regionId}
              ref={basicEl}
              appValue={appValue}
              vpcId={vpcId}
              setVpcId={setVpcId}
              refreshCostOptions={(v) => refreshCostOptions(v)}
              baseAppId={baseAppId}
              inDebtData={inDebtData}
              hygonSupportInfo={hygonSupportInfo}
              feature={feature}
              NewSaeVersion={NewSaeVersion}
            />
          </ComShow>
          <ComShow if={currentStep === ADVANCE_STEP}>
            <If condition={recordStep >= 1}>
              <MicroAdvanceCreator
                ref={advanceEl}
                basicConfig={basicConfig}
                feature={feature}
                v1Micro={true}
                appValue={appValue}
                NewSaeVersion={NewSaeVersion}
                hasMseRole={hasMseRole}
                baseAppId={baseAppId}
                enableSaeStdVersionNewMse={enableSaeStdVersionNewMse}
                agentVersion={agentVersion}
                enableCustomNasMountOptions={enableCustomNasMountOptions}
              />
            </If>
          </ComShow>
        </div>
        <div className="step-footer">
          <CostSheet costOptions={costOptions} NewSaeVersion={NewSaeVersion} />
          <ComShow if={currentStep === BASIC_STEP}>
            <div className="ml-l">
              <Button
                size="large"
                type="normal"
                className="bk-button"
                loading={advanceLoading}
                disabled={!vpcId && false}
                onClick={handlerAdvanceStep}
              >
                {intl('saenext.components.app-create.NextStepAdvancedSettings')}
              </Button>
              <If condition={isEmpty(appValue)}>
                <Button
                  size="large"
                  className="mr-s ml-s bk-button"
                  loading={createLoading}
                  disabled={!vpcId && false}
                  onClick={handleQuickCreate}
                >
                  {intl('saenext.components.app-create.SkipAdvancedSettingsAndCreate')}
                </Button>
              </If>
            </div>
          </ComShow>
          <ComShow if={currentStep === ADVANCE_STEP}>
            <div className="ml-xl">
              <Button size="large" type="normal" className="bk-button" onClick={handleBackStep}>
                {intl('saenext.components.app-create.PreviousBasicInformation')}
              </Button>
              <Button
                size="large"
                loading={createLoading}
                className="mr-s ml-s bk-button"
                onClick={() => handleBeforeCreate(handlerAdvanceCreator)}
              >
                {intl('saenext.components.app-create.CreateAnApplication')}
              </Button>
            </div>
          </ComShow>
        </div>
      </div>
    </Fragment>
  );
};

MicroAppCreator.propTypes = {
  history: PropTypes.func,
  initStep: PropTypes.number,
};
MicroAppCreator.defaultProps = {
  initialStep: microStepsDataSource.basic.key,
};

export default MicroAppCreator;
