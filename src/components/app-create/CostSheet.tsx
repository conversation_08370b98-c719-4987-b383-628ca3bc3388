import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect, useMemo } from 'react';
import { Icon, Balloon } from '@ali/cnd';
import { get, isEmpty, map } from 'lodash';
import services from '~/services';
import CachedData from '~/cache/common';

type Props = {
  costOptions: {
    appType: string;
    replicas: number;
    spec: { cpu: number; memory: number };
    resourceType?: string;
  },
  NewSaeVersion?: string;
};

export default (props: Props) => {
  const { costOptions, NewSaeVersion = '' } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [costDetail, setCostDetail] = useState<any>({});
  const [isLeavePackage, setIsLeavePackage] = useState(false);

  useEffect(() => {
    getCostDetail();
  }, [JSON.stringify(costOptions)]);

  // @ts-ignore
  const costItems = useMemo(() => {
    return [
      {
        title: intl('saenext.components.app-create.CostSheet.ComputingResourceFee'),
        key: 'CpuMemPrice',
        unit: intl('saenext.components.app-create.CostSheet.YuanSecond'),
      },
      {
        title: intl('saenext.components.app-create.CostSheet.MillionsOfRequests'),
        key: 'RequestPrice',
        unit: intl('saenext.components.app-create.CostSheet.Yuan'),
      },
      {
        title: intl('saenext.components.app-create.CostSheet.InternetTrafficFee'),
        key: 'TrafficPrice',
        unit: intl('saenext.components.app-create.CostSheet.RmbGb'),
      },
    ];
  }, [JSON.stringify(costOptions)]);

  const getCostDetail = async () => {
    setIsLoading(true);
    // web_always=Web+固定分配CPU
    // web_request=Web+按请求分配CPU  目前web应用只支持这个
    // micro_service=微服务应用
    const { appType, replicas, spec, resourceType } = costOptions;
    const params={
      Cpu: replicas * spec.cpu * 1000,
      Memory: replicas * spec.memory * 1024,
      Workload: appType === 'web' ? 'web_request' : 'micro_service',
      ResourceType: appType === 'web' ? '': resourceType
    }
    if(NewSaeVersion){
      Reflect.set(params, 'NewSaeVersion', NewSaeVersion)
    }
    // @ts-ignore
    const res = await services.getApplicationCost({
      ...params,
    });
    const { Data } = res;
    // true 表示有余量 false 表示没有
    const _isLeavePackage = get(Data, 'BagUsage.Cpu', 0) > 0 || get(Data, 'BagUsage.Mem', 0) > 0;

    setCostDetail(Data);
    setIsLeavePackage(_isLeavePackage);
    setIsLoading(false);
  };

  return (
    <>
      {isLoading ? (
        <div className="price-footer">
          <div></div>
          <div className='loading'>{intl('saenext.components.app-create.CostSheet.CalculatingPrice')}</div>
        </div>
      ) : (
        <div className="price-footer">
          {isLeavePackage && (
            <div className="package-box flex">
              <div className="price-box">
                <div className="option">
                  <Icon
                    size="small"
                    type="hourglass"
                    className="mr-xs"
                    style={{ color: '#ff8a00' }}
                  />

                  <span>
                    {intl('saenext.components.app-create.CostSheet.TheRemainingAmountOfThe')}
                  </span>
                </div>
                <div className="flex">
                  <div>
                    <span className="amount">{get(costDetail, `BagUsage.Cpu`, '--')}</span>
                    <span className="unit">
                      {intl('saenext.components.app-create.CostSheet.Nuclear')}
                    </span>
                  </div>
                  <div>
                    <span>{intl("sae.common.comma")}</span>
                    <span className="amount">{get(costDetail, `BagUsage.Mem`, '--')}</span>
                    <span className="unit">
                      {intl('saenext.components.app-create.CostSheet.Gib')}
                    </span>
                  </div>
                </div>
              </div>
              {get(costDetail, 'BagUsage.Cu', 0) > 0 && (
                <div className="price-box">
                  <div className="option">
                    <Icon
                      size="small"
                      type="hourglass"
                      className="mr-xs"
                      style={{ color: '#ff8a00' }}
                    />

                    <span>
                      {intl('saenext.components.app-create.CostSheet.RemainingQuotaOfCurrentCu')}
                    </span>
                  </div>
                  <div>
                    <span className="amount">{get(costDetail, `BagUsage.Cu`, '--')}</span>
                    <span className="unit">CU</span>
                  </div>
                </div>
              )}
            </div>
          )}
          <div className="refer-box">
            {isLeavePackage ? (
              <div className="package text-right">
                <span>{intl('saenext.components.app-create.CostSheet.IfTheUsageExceedsThe')}</span>
                <span>
                  {intl('saenext.components.app-create.CostSheet.ThisIsTheReferencePrice')}

                  <Icon type="connection" size="small" style={{ color: '#0070cc' }} />
                  <a href={CachedData.confLink('help:sae:measurement-method')} target="_blank">
                    {intl('saenext.components.app-create.CostSheet.LearnMoreAboutBilling')}
                  </a>
                </span>
              </div>
            ) : (
              <div className="unpackage">
                <span>{intl('saenext.components.app-create.CostSheet.PleaseReferToTheBill')}</span>
                <span className="mt-xs">
                  {intl('saenext.components.app-create.CostSheet.ThisIsTheReferencePrice.1')}
                  <Icon type="connection" size="small" style={{ color: '#0070cc' }} />
                  <a href={CachedData.confLink('help:sae:measurement-method')} target="_blank">
                    {intl('saenext.components.app-create.CostSheet.LearnMoreAboutBilling')}
                  </a>
                </span>
              </div>
            )}
          </div>
          {
            <>
              {!isEmpty(get(costDetail, 'CpuMemPrice')) ? (
                <div className="price-box">
                  <span className="option">
                    {intl('saenext.components.app-create.CostSheet.ComputingResourceFee')}
                  </span>
                  <div>
                    <span className="prefix">￥</span>
                    <span className="amount">
                      {get(costDetail, `CpuMemPrice.Order.TradeAmount`, '--')}
                    </span>
                    <span className="unit">
                      {intl('saenext.components.app-create.CostSheet.YuanSecond')}
                    </span>
                  </div>
                </div>
              ) : null}

              {!isEmpty(get(costDetail, 'RequestPrice')) ? (
                <div className="price-box">
                  <span className="option">
                    {intl('saenext.components.app-create.CostSheet.MillionsOfRequests')}
                  </span>
                  <div>
                    <span className="prefix">￥</span>
                    <span className="amount">
                      {get(costDetail, `RequestPrice.Order.TradeAmount`, '--')}
                    </span>
                    <span className="unit">
                      {intl('saenext.components.app-create.CostSheet.Yuan')}
                    </span>
                  </div>
                </div>
              ) : null}

              {!isEmpty(get(costDetail, 'TrafficPrice')) ? (
                <div className="price-box">
                  <span className="option">
                    {intl('saenext.components.app-create.CostSheet.OutboundInternetTrafficFee')}
                  </span>
                  <div>
                    <span className="prefix">￥</span>
                    <span className="amount">
                      {get(costDetail, `TrafficPrice.Order.TradeAmount`, '--')}
                    </span>
                    <span className="unit">
                      {intl('saenext.components.app-create.CostSheet.RmbGb')}
                    </span>
                  </div>
                </div>
              ) : null}

              {!isEmpty(get(costDetail, 'CpuMemPrice')) ? (
                <div className="discount-box">
                  <Balloon
                    trigger={
                      <div className="detail">
                        <Icon type="gift" size="small" />
                        <span className="ml-s">
                          {intl('saenext.components.app-create.CostSheet.DiscountDetails')}
                        </span>
                      </div>
                    }
                    align="t"
                    closable={false}
                    style={{ width: 260 }}
                  >
                    {map(get(costDetail, 'CpuMemPrice.Rules', []), (rule: any) => (
                      <div>{rule?.Name}</div>
                    ))}
                  </Balloon>
                  <span className="amount">
                    {intl('saenext.components.app-create.CostSheet.Province')}
                    {get(costDetail, `CpuMemPrice.Order.DiscountAmount`)}
                    {intl('saenext.components.app-create.CostSheet.YuanSecond')}
                  </span>
                </div>
              ) : null}
            </>
          }
        </div>
      )}
    </>
  );
};
