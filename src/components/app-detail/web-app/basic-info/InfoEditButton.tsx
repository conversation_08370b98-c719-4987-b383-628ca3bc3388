import { intl } from '@ali/xconsole';
import React, { memo } from 'react';
import SlideButton from '~/components/shared/SlideButton';
import InfoSetting from './InfoSetting';

const InfoEditButton = ({ onSuccess, data }) => {
  const ref = React.createRef() as any;

  return (
    <SlideButton
      buttonText={intl('general.edit')}
      slideTitle={intl('application.edit.label', { name: data?.applicationName }) as any}
      slideSize={900}
      slideContent={
        <InfoSetting
          ref={ref}
          data={data}
          onSuccess={onSuccess}
        />
      }
      autoClose={false}
      linkButton
      submit={() => {
        if (ref.current) {
          return ref.current.submit();
        }
      }}
    />
  );
};

export default memo(InfoEditButton);
