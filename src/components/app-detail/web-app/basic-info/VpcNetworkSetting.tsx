import { intl } from '@ali/cnd';
import React, { HTMLAttributes, useEffect, useState, useRef, useContext } from 'react';
import {
  Card,
  Copy,
  Description,
  SlidePanel,
  Loading,
  Icon,
  Tag,
  Button,
  Message,
  ConsoleContext,
} from '@ali/cnd';
import NetworkSetField from '~/components/shared/NetworkSetField';
import services from '~/services';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import _ from 'lodash';
import If from '~/components/shared/If';
import { getParams } from '~/utils/global';
import { isForbidden } from '~/utils/authUtils';
import CachedData from '~/cache/common';

interface IVpcNetworkSet extends HTMLAttributes<HTMLDivElement> {
  appData: any;
  applicationID: string;
  refreshWebApp: () => void;
}

const CardProps = {
  showTitleBullet: false,
  showHeadDivider: false,
  contentHeight: 'auto',
};

const VpcNetworkSetting = (props: IVpcNetworkSet) => {
  const { className, applicationID } = props;

  const networkField = useRef(null);
  const [appData, setAppData] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [vpcConfig, setVpcConfig] = useState({
    enableVpc: false,
    vpcId: '',
    vSwitchIds: [],
    securityGroupId: '',
  });
  const [vpcValue, setVpcValue] = useState({});
  // 默认不支持编辑
  const [editableVpc, setEditableVpc] = useState(false);
  // 默认不支持编辑
  const [enableVpc, setEnableVpc] = useState(false);
  const [defaultVpcId, setDefaultVpcId] = useState('');

  const [vpcList, setVpcList] = useState([]);
  const [vswitchList, setVswitchList] = useState([]);
  const [securityGroupsList, setSecurityGroupsList] = useState([]);
  const [authedVPC, setAuthedVPC] = useState(true);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const applicationName = getParams('name');

  useEffect(() => {
    refreshWebApp();
  }, []);

  useEffect(() => {
    async function initData() {
      if (appData && appData.namespaceID) {
        setEditableVpc(false);
        const { namespaceID, vpcConfig = {}, internetAccess = true } = appData;
        const { vpcId, vSwitchIds = [], securityGroupId } = vpcConfig;

        const enableVpc = !!(vpcId && vSwitchIds.length && securityGroupId);

        const _vpcConfig = {
          enableVpc,
          vpcId,
          vSwitchIds,
          securityGroupId,
          internetAccess,
        };
        if (enableVpc) {
          setDefaultVpcId(vpcId);
        } else {
          getDefaultVpcId(namespaceID);
        }
        // @ts-ignore
        setVpcConfig(_vpcConfig);
        setVpcValue(_vpcConfig);
      }
    }

    initData();
  }, [appData]);

  useEffect(() => {
    const { vpcId } = vpcValue as any;
    if (vpcId) {
      getVpcs();
      getVswitches(vpcId);
      getSecurityGrouponList(vpcId);
    }
  }, [vpcValue]);

  const refreshWebApp = async () => {
    const _appData =
      (await services.getWebApplication({
        applicationID,
        applicationName,
        qualifier: 'LATEST',
      })) || {};
    setAppData(_appData);
  };

  const getDefaultVpcId = async (namespaceID) => {
    const { Data: namespace = {} } =
      (await services.getNamespaceDescribeV2({
        NamespaceId: namespaceID,
      })) || {};
    const { VpcId = '' } = namespace;
    setDefaultVpcId(VpcId);
    if (!VpcId) {
      setEditableVpc(true);
    }
  };

  const getVpcs = async () => {
    const { Vpcs = {} } = await services.DescribeVpcs({
      params: {
        RegionId: regionId,
        PageNumber: 1,
        PageSize: 50,
      },
      customErrorHandle: (error, data) => {
        if (error.code === 'Forbidden.RAM') {
          return {};
        }
        return error;
      },
    });
    const { Vpc = [] } = Vpcs;
    setVpcList(Vpc);
  };

  const getVswitches = async (vpcId) => {
    if (!vpcId) return;

    const { VSwitches = {} } = await services.DescribeVSwitches({
      params: {
        RegionId: regionId,
        VpcId: vpcId,
      },
      customErrorHandle: (error, _b, cb) => {
        if (isForbidden(error.code)) {
          setAuthedVPC(false);
        } else {
          setAuthedVPC(true);
          cb?.();
        }
      }
    });
    setVswitchList(VSwitches.VSwitch || []);
  };

  const getSecurityGrouponList = async (vpcId) => {
    if (!vpcId) return;
    const {
      SecurityGroups: { SecurityGroup },
    } = await services.DescribeSecurityGroups({
      RegionId: regionId,
      VpcId: vpcId,
      PageSize: 99,
      PageNumber: 1,
    });
    setSecurityGroupsList(SecurityGroup);
  };

  const renderVpc = (VpcId) => {
    if (!VpcId) return '--';

    const vpc = _.find(vpcList, { VpcId }) || { VpcId };

    const { VpcName = '' } = vpc;

    return (
      <>
        <If condition={VpcName}>
          <p>
            <span className="text-description">
              {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
            </span>
            {VpcName}
          </p>
        </If>
        <p>
          <span className="text-description">ID: </span>
          <Copy text={VpcId}>
            <a
              href={`${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/vpcs/${VpcId}`}
              target="_blank"
            >
              {VpcId}
            </a>
          </Copy>
        </p>
      </>
    );
  };

  const renderVswitch = (vSwitchIds) => {
    if (_.isEmpty(vSwitchIds)) return '--';

    const vSwitchs = _.map(vSwitchIds, (item) => {
      const vSwitch = _.find(vswitchList, { VSwitchId: item });
      return vSwitch || { VSwitchId: item };
    });

    return (
      <>
        {vSwitchs.map((item) => {
          const { VSwitchName = '', VSwitchId = '' } = item;
          return (
            <p>
              <If condition={VSwitchName}>
                <div>
                  <span className="text-description">
                    {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
                  </span>
                  {VSwitchName}
                </div>
              </If>
              <div>
                <span className="text-description">ID: </span>
                <Copy text={VSwitchId}>
                  <a
                    href={`${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/switches/${VSwitchId}`}
                    target="_blank"
                  >
                    {VSwitchId}
                  </a>
                </Copy>
              </div>
            </p>
          );
        })}
      </>
    );
  };

  const renderSecurityGroup = (SecurityGroupId) => {
    if (!SecurityGroupId) return '--';
    const securityGroup = _.find(securityGroupsList, { SecurityGroupId }) || { SecurityGroupId };
    const { SecurityGroupName = '' } = securityGroup;
    return (
      <>
        <If condition={SecurityGroupName}>
          <p>
            <span className="text-description">
              {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
            </span>
            {SecurityGroupName}
          </p>
        </If>
        <p>
          <span className="text-description">ID: </span>
          <Copy text={SecurityGroupId}>
            <a
              href={`${CachedData.confLink('feature:ecs:url')}/securityGroupDetail/region/${regionId}/groupId/${SecurityGroupId}`}
              target="_blank"
            >
              {SecurityGroupId}
            </a>
          </Copy>
        </p>
      </>
    );
  };

  const submitVpc = async () => {
    // const networkValidate = networkField.current.validate();
    const networkValidate = networkField.current.getInstance().controlRef.validate();
    if (!networkValidate) return;
    setIsProcessing(true);
    // @ts-ignore
    const { vpcId, vSwitchIds, securityGroupId, internetAccess } = vpcConfig;
    const res = await services.updateWebAppVpc(applicationID, appData.applicationName, {
      vpcConfig: {
        vpcId: vpcId || '',
        vSwitchIds: vSwitchIds || [],
        securityGroupId: securityGroupId || '',
      },
      internetAccess,
    });

    // @ts-ignore
    const resVersion = await services.updateAppVersions(applicationID, appData.applicationName, {
      vpcConfig: {
        vpcId: vpcId || '',
        vSwitchIds: vSwitchIds || [],
        securityGroupId: securityGroupId || '',
      },
      internetAccess,
    });
    setIsProcessing(false);

    if (!res || !resVersion) {
      Message.error(
        intl('saenext.web-app.basic-info.VpcNetworkSetting.NetworkConfigurationUpdateFailed'),
      );
      return;
    }
    Message.success(
      intl('saenext.web-app.basic-info.VpcNetworkSetting.TheNetworkConfigurationHasBeen'),
    );
    setVisible(false);
    refreshWebApp && refreshWebApp();
  };

  return (
    <>
      <Card
        title={intl('saenext.web-app.basic-info.VpcNetworkSetting.NetworkSettings')}
        subTitle={
          <>
            <Button
              text
              type="primary"
              onClick={() => setVisible(true)}
              style={{ fontWeight: 'normal', marginLeft: 40 }}
            >
              {intl('saenext.web-app.basic-info.VpcNetworkSetting.Edit')}
            </Button>
          </>
        }
        {...CardProps}
        className={className}
      >
        <Description
          dataSource={vpcValue}
          items={[
            {
              dataIndex: 'enableVpc',
              label: intl(
                'saenext.web-app.basic-info.VpcNetworkSetting.AllowApplicationsToAccessVpc',
              ),
              render: (value) => (
                <div>
                  {value ? (
                    <div>
                      <Icon type="check_fill" size="small" style={{ color: '#009431' }} />
                      <span className="ml-s">
                        {intl('saenext.web-app.basic-info.VpcNetworkSetting.Allow')}
                      </span>
                    </div>
                  ) : (
                    <div className="flex">
                      <Icon type="warning_fill" size="small" style={{ color: '#ffce03' }} />
                      <span className="ml-s">
                        {intl('saenext.web-app.basic-info.VpcNetworkSetting.NotAllowed')}
                      </span>
                    </div>
                  )}
                </div>
              ),
            },
            {
              dataIndex: 'vpcId',
              label: intl('saenext.web-app.basic-info.VpcNetworkSetting.Vpc'),
              // @ts-ignore
              render: renderVpc,
            },
            {
              dataIndex: 'vSwitchIds',
              label: intl('saenext.web-app.basic-info.VpcNetworkSetting.SwitchVswitch'),
              // @ts-ignore
              render: renderVswitch,
            },
            {
              dataIndex: 'securityGroupId',
              label: intl('saenext.web-app.basic-info.VpcNetworkSetting.SecurityGroup'),
              // @ts-ignore
              render: renderSecurityGroup,
            },
            {
              dataIndex: 'internetAccess',
              label: (
                <TextWithBalloon
                  text={intl(
                    'saenext.web-app.basic-info.VpcNetworkSetting.ApplicationAccessToThePublic',
                  )}
                  tips={intl(
                    'saenext.web-app.basic-info.VpcNetworkSetting.ConfigureWhetherTheApplicationCan',
                  )}
                />
              ),

              render: (value) => (
                <>
                  {!value
                    ? intl('saenext.web-app.basic-info.VpcNetworkSetting.FixedPublicIpAddress')
                    : intl('saenext.web-app.basic-info.VpcNetworkSetting.NonFixedPublicIpAddress')}
                </>
              ),
            },
          ]}
        />
      </Card>
      <SlidePanel
        title={intl('saenext.web-app.basic-info.VpcNetworkSetting.EditNetworkConfiguration')}
        isShowing={visible}
        onOk={submitVpc}
        onCancel={() => setVisible(false)}
        onClose={() => setVisible(false)}
        isProcessing={isProcessing}
        width={700}
        okText={intl('saenext.web-app.basic-info.VpcNetworkSetting.Ok')}
        cancelText={intl('saenext.web-app.basic-info.VpcNetworkSetting.Cancel')}
      >
        <Message type="warning" className="mb-l">
          {intl('saenext.web-app.basic-info.VpcNetworkSetting.VpcConfigurationChangesWillNot')}
        </Message>
        <NetworkSetField
          disabled={!editableVpc}
          defaultVpcId={defaultVpcId}
          value={vpcConfig}
          ref={networkField}
          showStaticIpOptions={true}
          onChange={({ value }) => {
            setVpcConfig(value);
          }}
        />
      </SlidePanel>
    </>
  );
};

export default VpcNetworkSetting;
