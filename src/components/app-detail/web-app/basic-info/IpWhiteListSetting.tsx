import { intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Message, Input } from '@ali/cnd';
import services from '~/services';
import { getParams } from '~/utils/global';
import _ from 'lodash';

const { TextArea } = Input;

const IpWhiteListSetting = (props, ref) => {
  const { data = [], httpTriggerConfig, applicationID, onSuccess } = props;

  const applicationName = getParams('name');

  const [ipStr, setIpStr] = useState('');

  useEffect(() => {
    setIpStr(formatData(data));
  }, [data]);

  useImperativeHandle(
    ref,
    () => ({
      submit,
    }),
    [ipStr],
  );

  const formatData = (data) => {
    const ipArr = _.map(data, (item) => {
      const { entry, description } = item;
      const ipItemStr = entry + (description ? ` | ${description}` : '');
      return ipItemStr;
    });
    const ipStr = ipArr.join('\n');
    return ipStr;
  };

  const submit = async () => {
    const promise = new Promise<void>(async (resolve, reject) => {
      updateApp(resolve, reject);
    });
    return promise;
  };

  const updateApp = async (resolve, reject) => {
    const aclEntries = getValue();

    if (aclEntries.length > 20) {
      Message.error(intl('saenext.web-app.basic-info.IpWhiteListSetting.YouCanAddUpTo'));
      reject();
      return;
    }
    if (aclEntries === '') {
      Message.error(intl('saenext.web-app.basic-info.IpWhiteListSetting.CheckTheIpFormat'));
      reject();
      return;
    }

    const content = {
      httpTriggerConfig: {
        ...httpTriggerConfig,
        aclConfig: {
          aclEntries,
          aclMode: 'allow',
        },
      },
    };

    const result = await services.updateWebAttributes(applicationID, applicationName, content);

    if (result) {
      onSuccess();
      resolve(result);
    } else {
      reject();
    }
  };

  const getValue = () => {
    if (ipStr === '') {
      return [];
    }
    const ipArrData = _.split(ipStr, /(?=\r|\n)\r?\n?/g);

    const ipArr = [];
    let validFlag = true;
    _.each(ipArrData, (item) => {
      let [ip = '', description = '', ...rest] = item.split('|');
      if (rest?.length > 0) {
        validFlag = false;
        return;
      }
      ip = ip.trim();
      description = description.trim();
      const ipRex = /^(\d{1,3}\.){3}\d{1,3}$/;
      if (ipRex.test(ip)) {
        const ipItem = {
          entry: ip,
          description,
        };
        ipArr.push(ipItem);
      } else {
        validFlag = false;
        return;
      }
    });
    if (!validFlag) {
      return '';
    }
    return ipArr;
  };

  return (
    <div className="gray-text">
      <p>
        {intl('saenext.web-app.basic-info.IpWhiteListSetting.Whitelist')}
        <span className="ml-l">
          {intl('saenext.web-app.basic-info.IpWhiteListSetting.AllowsASpecificIpAddress')}
        </span>
      </p>
      <p>
        {intl('saenext.web-app.basic-info.IpWhiteListSetting.IpVersion')}
        <span className="ml-l">IPv4</span>
      </p>

      <Message type="notice">
        {intl('saenext.web-app.basic-info.IpWhiteListSetting.FormatDescription')}

        <div className="text-line">
          {intl('saenext.web-app.basic-info.IpWhiteListSetting.IpAddressesAreAllowedTo')}
        </div>
        <div className="text-line">
          {intl('saenext.web-app.basic-info.IpWhiteListSetting.TheAddressAndRemarksOf')}
        </div>
        <div className="text-line">
          {intl('saenext.web-app.basic-info.IpWhiteListSetting.YouCanAddUpTo.1')}
        </div>
      </Message>

      <p>{intl('saenext.web-app.basic-info.IpWhiteListSetting.AddIpAddressesAndRemarks')}</p>

      <TextArea value={ipStr} onChange={setIpStr} className="full-width" rows={20} />
    </div>
  );
};

export default forwardRef(IpWhiteListSetting);
