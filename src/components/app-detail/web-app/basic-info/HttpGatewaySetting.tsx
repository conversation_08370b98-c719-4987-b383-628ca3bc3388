import { intl } from '@ali/cnd';
import React, { HTMLAttributes } from 'react';
import { Card, Divider } from '@ali/cnd';
import CustomDomain from './CustomDomain';
import AccessConfig from './AccessConfig';

interface IHttpGatewaySetting extends HTMLAttributes<HTMLDivElement> {
  applicationID: string;
  appData: object;
  fetchConfig: Function;
  deployStatus: { showProcess: boolean; success: boolean };
}

const HttpGatewaySetting = (props: IHttpGatewaySetting) => {
  const { className, appData = {} as any, fetchConfig, applicationID, deployStatus } = props;

  const CardProps = {
    showTitleBullet: false,
    showHeadDivider: false,
    contentHeight: 'auto',
  };

  return (
    <Card
      title={intl('saenext.web-app.basic-info.HttpGatewaySetting.FullHostingOfHttpTraffic')}
      {...CardProps}
      className={className}
      style={{ paddingBottom: 16 }}
    >
      <AccessConfig 
        appData={appData} 
        fetchConfig={fetchConfig} 
        applicationID={applicationID} 
        deployStatus={deployStatus}
      />

      <Divider />
      <h3 className="h3 mt">
        {intl('saenext.web-app.basic-info.HttpGatewaySetting.CustomDomainName')}
      </h3>
      <CustomDomain
        applicationID={applicationID}
        applicationName={appData?.applicationName}
        namespaceID={appData?.namespaceID}
      />
    </Card>
  );
};

HttpGatewaySetting.propTypes = {};

export default HttpGatewaySetting;
