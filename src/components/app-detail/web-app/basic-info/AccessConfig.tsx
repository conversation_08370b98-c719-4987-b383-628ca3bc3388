import React, { HTMLAttributes } from 'react';
import Copy from '@ali/cnd-copy';
import { intl, Description } from '@ali/cnd';
import { AUTH_TYPE_MAP } from '~/constants/application';
import AccessEditButton from './AccessEditButton';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import InternetIpWhiteList from './InternetIpWhiteList';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';

interface IAccessConfig extends HTMLAttributes<HTMLDivElement> {
  appData: object;
  fetchConfig: Function;
  applicationID: string;
  deployStatus: { showProcess: boolean; success: boolean };
}

const AccessConfig = (props: IAccessConfig) => {
  const { appData, fetchConfig, applicationID, deployStatus } = props;

  return (
    <Description
      title={intl('saenext.web-app.basic-info.AccessConfig.AccessSettings')}
      dataSource={appData}
      items={[
        {
          dataIndex: 'httpTriggerConfig',
          label: intl('saenext.web-app.basic-info.AccessConfig.EntranceTypeOfData'),
          render: (value) => (
            <>
              {value?.disableURLInternet
                ? intl('saenext.web-app.basic-info.AccessConfig.Intranet')
                : intl('saenext.web-app.basic-info.AccessConfig.AllAllowDirectAccessTo')}
            </>
          ),
        },
        {
          dataIndex: 'httpTriggerConfig',
          label: intl('saenext.web-app.basic-info.AccessConfig.CallAuthentication'),
          render: (value) => <>{AUTH_TYPE_MAP[value?.authType]}</>,
        },
        {
          dataIndex: 'urlInternet',
          // label: '公网访问地址',
          label: (
            <TextWithBalloon
              text={intl('saenext.web-app.basic-info.AccessConfig.PublicAccessAddress')}
              tips={intl('saenext.web-app.basic-info.AccessConfig.TheDefaultPublicDomainName')}
            />
          ),
          render: (value) => {
            let content = (
              <>
                <If condition={value}>
                  <a href={value} target="_blank">
                    <Copy text={value}>{value}</Copy>
                  </a>
                  <br />
                </If>
                <InternetIpWhiteList
                  appData={appData}
                  fetchConfig={fetchConfig}
                  applicationID={applicationID}
                />
              </>
            );
            if (deployStatus.showProcess) {
              content = (<span>-</span>);
              if (deployStatus.success) {
                content = content;
              }
              return content;
            }
            return content;
          },
        },
        {
          dataIndex: 'urlIntranet',
          // label: '私网访问地址',
          label: (
            <TextWithBalloon
              text={intl('saenext.web-app.basic-info.AccessConfig.PrivateAccessAddress')}
              tips={intl('saenext.web-app.basic-info.AccessConfig.TheDefaultPrivateAccessDomain')}
            />
          ),
          render: (value) => {
            let content = (
              <>
                <Copy text={value}>{value}</Copy>
                <br />
                <a href={CachedData.confLink('help:sae:access-through-the-private-network')} target="_blank">
                  {intl(
                    'saenext.web-app.basic-info.AccessConfig.AccessApplicationsThroughThePrivate',
                  )}
                </a>
              </>
            );
            if (deployStatus.showProcess) {
              content = (<span>-</span>);
              if (deployStatus.success) {
                content = content;
              }
              return content;
            }
            return content;
          },
        },
      ]}
      actions={
        <AccessEditButton onSuccess={fetchConfig} data={appData} applicationID={applicationID} />
      }
    />
  );
};

export default AccessConfig;
