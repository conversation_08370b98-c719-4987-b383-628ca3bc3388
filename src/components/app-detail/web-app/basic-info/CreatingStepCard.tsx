import { intl } from '@ali/cnd';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { getParams } from '~/utils/global';
import { Step, Icon, Button, Message, SlidePanel } from '@ali/cnd';
import services from '~/services';
import { DEPLOY_TYPE } from '~/components/shared/DeploySelectorField/constant';
import { getTimes } from '~/components/shared/TimeRangeSelector';
import { handleUrlParams } from '~/utils/global';
import { LogEditor } from '@ali/cnd';
import _ from 'lodash';

const IconSize = 14;
enum StepStatus {
  wait = 'WAIT',
  running = 'RUNNING',
  fail = 'FAIL',
  success = 'SUCCESS',
  stop = 'STOP',
}
type StepItemsParam = {
  status: StepStatus;
  content: any;
};
const DEFAULT_STEPS = {
  [DEPLOY_TYPE.IMAGE]: [
    {
      id: 'application',
      title: intl('saenext.web-app.basic-info.CreatingStepCard.CreateANewVersionOf'),
      content: (
        <>
          <Icon size={IconSize} type="loading" className="mr-s" />
          {intl('saenext.web-app.basic-info.CreatingStepCard.Creating')}
        </>
      ),
    },
    {
      id: 'traffic',
      title: intl('saenext.web-app.basic-info.CreatingStepCard.TrafficConfiguration'),
    },
    {
      id: 'instance',
      title: intl('saenext.web-app.basic-info.CreatingStepCard.StartAnApplicationInstance'),
    },
  ],

  [DEPLOY_TYPE.REPOISTORY]: [
    {
      id: 'application',
      title: intl('saenext.web-app.basic-info.CreatingStepCard.CreateAnApplication'),
      content: (
        <>
          <Icon size={IconSize} type="loading" className="mr-s" />
          {intl('saenext.web-app.basic-info.CreatingStepCard.Creating')}
        </>
      ),
    },
    {
      id: 's2a',
      title: intl('saenext.web-app.basic-info.CreatingStepCard.SourceCodeBuildImagesTraffic'),
    },
    {
      id: 'instance',
      title: intl('saenext.web-app.basic-info.CreatingStepCard.StartAnApplicationInstance'),
    },
  ],

  [DEPLOY_TYPE.WEB_PACKAGE]: [
    {
      id: 'application',
      title: intl('saenext.web-app.basic-info.CreatingStepCard.CreateAnApplication'),
      content: (
        <>
          <Icon size={IconSize} type="loading" className="mr-s" />
          {intl('saenext.web-app.basic-info.CreatingStepCard.Creating')}
        </>
      ),
    },
    {
      id: 's2a',
      title: intl(
        'saenext.web-app.basic-info.CreatingStepCard.CodePackageConstructionTrafficConfiguration',
      ),
    },
    {
      id: 'instance',
      title: intl('saenext.web-app.basic-info.CreatingStepCard.StartAnApplicationInstance'),
    },
  ],
};
const CreatingStepCard = (props) => {
  const {
    match: {
      params: { id },
    },
    history,
    refreshWebApp,
    appData,
    setDeployStatus = _.noop,
  } = props;
  const ApplicationId = id;
  const ApplicationName = getParams('name');

  const defaultObj = { status: StepStatus.wait, content: '' };
  const [applicationReady, setApplicationReady] = useState<StepItemsParam>(defaultObj);
  const [trafficReady, setTrafficReady] = useState<StepItemsParam>(defaultObj);
  const [s2aReady, setS2AReady] = useState<StepItemsParam>(defaultObj);
  const [instanceReady, setInstanceReady] = useState<StepItemsParam>(defaultObj);
  const [showCard, setShowCard] = useState(true);
  const [showDrawer, setShowDrawer] = useState(false);
  const [steps, setSteps] = useState<any>();
  const [logData, setLogData] = useState('');
  const [bindListener, setBindListener] = useState(false);

  const timer = useRef<any>(null);
  const pipelineCheck = useRef<StepStatus>();
  const deployType = useRef<DEPLOY_TYPE>();
  const taskId = useRef('');
  const currentOffset = useRef(0);
  const tmpLogData = useRef('');
  const minimumInstanceCount = useRef(0);

  useEffect(() => {
    preCheck().then(() => {
      if (!timer.current) {
        timer.current = setInterval(updateCreatingStep, 2000);
      }
    });
    return () => timer.current && clearInterval(timer.current);
  }, []);

  useEffect(() => {
    const _minInstanceCount = _.get(appData, 'scaleConfig.minimumInstanceCount', 0);
    minimumInstanceCount.current = _minInstanceCount;
  }, [appData]);

  useEffect(() => {
    if (logData && !bindListener) {
      const container = document.getElementsByClassName('log-container')[0];
      container?.addEventListener('scroll', _.throttle(scrollTrigger, 1000), false);
      setBindListener(true);
      return () => {
        container?.removeEventListener('scroll', () => scrollTrigger());
      };
    }
  }, [logData]);

  useEffect(() => {
    const mp = {
      application: applicationReady,
      traffic: trafficReady,
      s2a: s2aReady,
      instance: instanceReady,
    };
    setSteps(
      steps?.map((item) => {
        return {
          ...item,
          status: mp[item.id].status,
          content: mp[item.id].content,
        };
      }),
    );
  }, [applicationReady, trafficReady, s2aReady, instanceReady]);

  // 日志窗口处理逻辑
  const getRunDetailLogs = async (Offset = currentOffset.current, Limit = 200) => {
    const res = await services.listBuildPipelineRunsLogs({
      ApplicationId,
      ApplicationName,
      PipelineRunId: taskId.current,
      Offset,
      Limit,
    });
    if (res) {
      const { NextOffset, Items } = res;
      currentOffset.current = NextOffset;
      const extraLog = Items.map((e) => e.Content).join('\n') + (Items.length > 0 ? '\n' : '');
      if (Offset === 0) {
        tmpLogData.current = extraLog;
      } else {
        tmpLogData.current += extraLog;
      }
      setLogData(tmpLogData.current);
    }
  };

  const scrollTrigger = () => {
    const container = document.getElementsByClassName('log-container')[0];
    const { scrollTop, clientHeight, scrollHeight } = container;
    if (scrollHeight - scrollTop - clientHeight < 1) {
      getRunDetailLogs();
    }
  };

  const checkLog = () => (
    <Button
      type="primary"
      text
      onClick={() => {
        setShowDrawer(true);
        getRunDetailLogs();
      }}
    >
      {intl('saenext.web-app.basic-info.CreatingStepCard.ViewLogs')}
    </Button>
  );

  // 进度卡片处理逻辑
  const updateCreatingStep = useCallback(async () => {
    const clearTimer = () => {
      const tmp = timer.current;
      timer.current = null;
      clearInterval(tmp);
    };
    // update applicationReady / trafficReady
    if (ApplicationId && ApplicationName) {
      const tmpContent = {
        status: StepStatus.success,
        content: (
          <div className="flex" style={{ color: '#888888' }}>
            <Icon size={IconSize} type="success" className="mr-s" />
            {intl('saenext.web-app.basic-info.CreatingStepCard.Completed')}
          </div>
        ),
      };
      setApplicationReady(tmpContent);
      setTrafficReady(tmpContent);
    }
    // update s2aReady
    const PipelineRunId = taskId.current;
    const isPipelineBuild = [DEPLOY_TYPE.REPOISTORY, DEPLOY_TYPE.WEB_PACKAGE].includes(
      deployType.current,
    );
    if (isPipelineBuild && !pipelineCheck.current) {
      const res = await services.DescribeBuildPipelineRun({
        ApplicationId,
        ApplicationName,
        PipelineRunId,
      });
      if (res) {
        const { Data } = res;
        const { Status } = Data;
        if (Status === StepStatus.running) {
          setS2AReady({
            status: StepStatus.running,
            content: (
              <div className="flex">
                <Icon size={IconSize} type="loading" className="mr-s" />
                {intl('saenext.web-app.basic-info.CreatingStepCard.BuildingDeployment')}
                {checkLog()}
              </div>
            ),
          });
          return;
        } else if (Status === StepStatus.success) {
          setS2AReady({
            status: StepStatus.success,
            content: (
              <div className="flex" style={{ color: '#888888' }}>
                <Icon size={IconSize} type="success" className="mr-s" />
                {intl('saenext.web-app.basic-info.CreatingStepCard.Completed.1')}
                {checkLog()}
              </div>
            ),
          });
          // 刷新app，流水线构建默认创建2个版本 第2个版本成功之后 才有真实的实例数范围
          refreshWebApp && refreshWebApp();
          pipelineCheck.current = StepStatus.success;
        } else if (Status === StepStatus.fail) {
          setS2AReady({
            status: StepStatus.fail,
            content: (
              <Message type="error" className="flex">
                <div className="flex">
                  {intl('saenext.web-app.basic-info.CreatingStepCard.BuildFailed')}
                  {checkLog()}
                </div>
              </Message>
            ),
          });
          setInstanceReady({
            status: StepStatus.stop,
            content: (
              <div className="flex">
                <Icon size={IconSize} type="minus_fill" className="mr-s" />
                {/* 已停止 */}
                {intl('saenext.web-app.basic-info.CreatingStepCard.Stopped')}
              </div>
            ),
          });
          clearTimer();
          pipelineCheck.current = StepStatus.fail;
        } else if (Status === StepStatus.stop) {
          setS2AReady({
            status: StepStatus.fail,
            content: (
              <Message type="error" className="flex">
                <div className="flex">
                  {intl('saenext.web-app.basic-info.CreatingStepCard.BuildStop')}
                  {checkLog()}
                </div>
              </Message>
            ),
          });
          setInstanceReady({
            status: StepStatus.stop,
            content: (
              <div className="flex">
                <Icon size={IconSize} type="minus_fill" className="mr-s" />
                {/* 已停止 */}
                {intl('saenext.web-app.basic-info.CreatingStepCard.Stopped')}
              </div>
            ),
          });
          clearTimer();
          pipelineCheck.current = StepStatus.fail;
        }
      } else {
        pipelineCheck.current = StepStatus.fail; // 如果接口报错则控制不再请求
        return;
      }
    }
    // update instanceReady
    // 如果是持续部署则要求流水线构建成功
    if (isPipelineBuild && pipelineCheck.current !== StepStatus.success) {
      return;
    }
    const [startTime, endTime] = getTimes('last_15_minutes');
    const instanceData = await services.getAppVersionInstances({
      applicationID: ApplicationId,
      applicationName: ApplicationName,
      startTime,
      endTime,
    });
    const { instances, currentError } = instanceData;
    if (!currentError) {
      if (instances.length > 0 || minimumInstanceCount.current === 0) {
        clearTimer();
        setInstanceReady({
          status: StepStatus.success,
          content: (
            <div className="flex" style={{ color: '#888888' }}>
              <Icon size={IconSize} type="success" className="mr-s" />
              {/* 已完成 */}
              {intl('saenext.web-app.basic-info.CreatingStepCard.Completed')}
            </div>
          ),
        });
        setTimeout(() => {
          history.replace(handleUrlParams('delete', 'state')); // 唯一一处控制删除 url state 参数的出口
          Message.success(
            intl('saenext.web-app.basic-info.CreatingStepCard.TheInstanceIsStartedAnd'),
          );
          setShowCard(false);
          setDeployStatus && setDeployStatus({ showProcess: false, success: true });
        }, 1000);
      } else {
        setInstanceReady({
          status: StepStatus.running,
          content: (
            <>
              <Icon size={IconSize} type="loading" className="mr-s" />
              {/* 启动中 */}
              {intl('saenext.web-app.basic-info.CreatingStepCard.Starting')}
            </>
          ),
        });
      }
    } else {
      clearTimer();
      setInstanceReady({
        status: StepStatus.fail,
        content: (
          <div className="flex" style={{ color: '#888888' }}>
            <Icon size={IconSize} type="error" className="mr-s" />
            <span>
              {/* 启动失败 */}
              {intl('saenext.web-app.basic-info.CreatingStepCard.FailedToStartCurrenterror', {
                currentError: currentError,
              })}
            </span>
          </div>
        ),
      });
      setTimeout(() => {
        history.replace(handleUrlParams('delete', 'state'));
        setShowCard(false);
      }, 1000);
    }
  }, [applicationReady, s2aReady, trafficReady, instanceReady, appData]);

  const preCheck = async () => {
    // 回到顶部
    const target = document.getElementsByClassName('windcc-app-layout__content')[0];
    target.scrollTo(0, 0);
    // 查询是否有最新一次流水线实例

    const res = await services.listBuildPipelineRuns({
      ApplicationId,
      ApplicationName,
    });
    if (res) {
      const { Items } = res;
      if (Items.length === 0) {
        deployType.current = DEPLOY_TYPE.IMAGE;
        setSteps(DEFAULT_STEPS[DEPLOY_TYPE.IMAGE]);
        return;
      }
      // 默认第一条为当前构建记录
      const [buildRecord] = Items;
      const isRepository = Reflect.has(buildRecord, 'CodeConfig');
      const isPackage = Reflect.has(buildRecord, 'PackageConfig');
      const { PipelineRunId } = buildRecord;
      taskId.current = PipelineRunId;
      if (isRepository) {
        deployType.current = DEPLOY_TYPE.REPOISTORY;
        setSteps(DEFAULT_STEPS[DEPLOY_TYPE.REPOISTORY]);
        return;
      }

      if (isPackage) {
        deployType.current = DEPLOY_TYPE.WEB_PACKAGE;
        setSteps(DEFAULT_STEPS[DEPLOY_TYPE.WEB_PACKAGE]);
        return;
      }
    }
  };

  return (
    <div className="creating-process-card">
      <div className="flex mb-s">
        <span className="mr" style={{ fontSize: 14, fontWeight: 500 }}>
          {intl('saenext.web-app.basic-info.CreatingStepCard.CreationProgress')}
        </span>
        <Button type="primary" text onClick={() => setShowCard(!showCard)}>
          {showCard
            ? intl('saenext.web-app.basic-info.CreatingStepCard.HiddenState')
            : intl('saenext.web-app.basic-info.CreatingStepCard.DisplayStatus')}
          <Icon type={showCard ? 'arrow-up' : 'arrow-down'} />
        </Button>
      </div>
      {showCard && steps ? (
        <div
          className="border-radius pd-card mb"
          style={{ backgroundColor: '#F6F7F9', borderRadius: '4px' }}
        >
          <Step stretch direction="ver" shape="dot" labelPlacement="ver">
            {steps.map((item, index) => (
              <Step.Item
                key={index}
                title={item.title}
                content={
                  item['content'] ? (
                    <div style={{ width: 'calc(100% - 50px)' }}>{item['content']}</div>
                  ) : (
                    <>
                      <Icon size={IconSize} type="minus_fill" className="mr-s" />
                      {intl('saenext.web-app.basic-info.CreatingStepCard.PendingExecution')}
                    </>
                  )
                }
              />
            ))}
          </Step>
        </div>
      ) : null}

      {showDrawer ? (
        <SlidePanel
          title={intl('saenext.web-app.basic-info.CreatingStepCard.BuildLogs')}
          onClose={() => setShowDrawer(false)}
          isShowing={showDrawer}
          width={800}
        >
          <div className="flex" style={{ flexDirection: 'row-reverse' }}>
            <Button text type="primary" onClick={() => getRunDetailLogs(0)}>
              <Icon type="refresh" />
              {intl('saenext.web-app.basic-info.CreatingStepCard.Refresh')}
            </Button>
          </div>
          <div className="log-container" style={{ height: 'calc(100% - 20px)' }}>
            <LogEditor value={logData || 'Log generating...'} />
          </div>
        </SlidePanel>
      ) : null}
    </div>
  );
};

export default CreatingStepCard;
