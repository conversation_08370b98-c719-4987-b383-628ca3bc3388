import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Dialog,
  Input,
  Field,
  Message,
  Icon,
  intl,
  Checkbox,
  MenuButton,
  ErrorPrompt2 as errorPrompt,
} from '@ali/cnd';
import { AES_CONSTANT, trackResource } from '~/tracker';
import services from '~/services';
import { getTimes } from '~/components/shared/TimeRangeSelector';
import CachedData from '~/cache/common';
import { get, isEmpty, replace } from 'lodash';

const AppActionBar = (props) => {
  const { regionId, history, applicationID, applicationName, namespaceId, data, isLatestMonitor, run } = props;
  const timerRef = useRef(null);
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const [destroying, setDestroying] = useState(false);
  const [startDialogVisible, setStartDialogVisible] = useState(false);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [hasPipeline, setHasPipeline] = useState(false);
  const [checkEnableCicd, setCheckEnableCicd] = useState(true);
  const [loading, setLoading] = useState(false);
  const field = Field.useField();
  const { init, getValue } = field;

  useEffect(() => {
    clearTimer();
  }, []);

  const startWebApplicationRequest = async () => {
    return await services.startWebApplication({
      NamespaceId: namespaceId,
      ApplicationId: applicationID,
    });
  };

  const stopApplicationRequest = async () => {
    return await services.stopWebApplication({
      NamespaceId: namespaceId,
      ApplicationId: applicationID,
    });
  };

  const handlerEnableCiCd = (checked) => {
    console.log(checked, checkEnableCicd);
    setCheckEnableCicd(checked);
  };

  const onAppStatusChange = async () => {
    if (data?.scaleConfig?.maximumInstanceCount === 0) {
      setStartDialogVisible(true);
      const hasPipeline = await getBuildPipeline();
      setHasPipeline(hasPipeline);
    } else {
      // 停止
      Dialog.show({
        // @ts-ignore
        size: 'small',
        title: intl('saenext.web-app.basic-info.AppActionBar.StopApplicationApplicationname', {
          applicationName: applicationName,
        }),
        content: (
          <Message type="warning">
            {intl('saenext.web-app.basic-info.AppActionBar.StoppingAnApplicationWillInterrupt')}
          </Message>
        ),

        onOk: () =>
          new Promise(async (resolve, reject) => {
            try {
              const result = await stopApplicationRequest();
              if (result) {
                Message.success(
                  intl(
                    'saenext.web-app.basic-info.AppActionBar.StoppedSuccessfullyAndStartedCleaning',
                  ),
                );
                run();
                setDestroying(true);
                startTimer();
                resolve(result);
                // 尝试关闭流水线
                await tryChangeCicd(false);
              } else {
                reject();
              }
            } catch (err) {
              reject(err);
            }
          }),
      });
    }
  };

  const getBuildPipeline = async () => {
    let pipline;
    await services
      .describeBuildPipeline({
        ApplicationId: applicationID,
        ApplicationName: applicationName,
      })
      .then((result) => {
        if (result) {
          const { Data } = result;
          if (!Data) {
            // 没有流水线则直接返回不做任何处理
            return;
          }
          pipline = Data;
        }
      })
      .catch((err) => {
        Message.error(err.message);
      });
    return pipline;
  };

  const tryChangeCicd = async (enabled: boolean) => {
    const pipeline = await getBuildPipeline();
    pipeline && (await switchChangeCicd(enabled));
  };

  const switchChangeCicd = async (enabled: boolean) => {
    await services.updateBuildPipelineAttributes({
      Enabled: enabled,
      ApplicationId: applicationID,
      ApplicationName: applicationName,
    });
  };

  const getAppInstances = async () => {
    // 版本列表 默认获取15分钟之内的所有运行中实例
    const [startTime, endTime] = getTimes('last_15_minutes');
    const data = await services.getAppVersionInstances({
      applicationName,
      startTime,
      endTime,
      applicationID,
    });
    const { instances = [] } = data;
    if (instances.length === 0) {
      clearTimer();
      setDestroying(false);
    }
  };

  const startTimer = () => {
    clearTimer();
    timerRef.current = setInterval(() => {
      getAppInstances();
    }, 1000 * 5);
  };

  const clearTimer = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  // 修改配置
  const handleUpdateApplication = () => {
    Dialog.show({
      title: intl('saenext.web-app.basic-info.AppActionBar.ModifyConfiguration'),
      content: (
        <>
          <Message type="notice">
            {intl('saenext.web-app.basic-info.AppActionBar.TheSystemWillModifyThe')}
          </Message>
        </>
      ),

      onOk: () => {
        history.push(
          `/${regionId}/app-list/${applicationID}/web-app/version?name=${applicationName}&action=create`,
        );
      },
    });
  };

  // 删除应用
  const handleDeleteApplication = () => {
    if (data?.scaleConfig?.maximumInstanceCount !== 0) {
      Dialog.alert({
        title: intl('saenext.web-app.basic-info.AppActionBar.DeleteAnApplication'),
        content: (
          <p style={{ width: 360 }}>
            {intl('saenext.web-app.basic-info.AppActionBar.YouMustStopTheApplication')}
          </p>
        ),

        footerActions: ['cancel'],
      });
      return;
    }
    if (destroying) {
      Dialog.alert({
        title: intl('saenext.web-app.basic-info.AppActionBar.DeleteAnApplication'),
        content: (
          <p style={{ width: 360 }}>
            {intl('saenext.web-app.basic-info.AppActionBar.TheApplicationInstanceIsBeing.1')}
          </p>
        ),

        footerActions: ['cancel'],
      });
      return;
    }
    setDeleteDialogVisible(true);
  };

  const confirmDeleteApplication = async () => {
    setLoading(true);
    const tryfun = async (fn: (...args: any[]) => Promise<any>, ...args: any[]) => {
      try {
        const res = await fn(...args);
        return [null, res];
      } catch (ex) {
        return [ex, null];
      }
    };
    const deleteApplication = async () => {
      try {
        const result = await services.deleteWebApplication(
          {
            NamespaceId: namespaceId,
            ApplicationId: applicationID,
          },
          regionId,
        );
        return result;
      } catch (error) {
        if (error.code === 'FetcherErrorRiskCancelled') {
          // 风控弹窗点取消
          return {};
        }
        if (error.code === 'ProvisionConfigExist') {
          Dialog.alert({
            // @ts-ignore
            size: 'small',
            title: intl('saenext.web-app.basic-info.AppActionBar.FailedToDelete'),
            content: (
              <p>{intl('saenext.web-app.basic-info.AppActionBar.TheApplicationInstanceIsBeing')}</p>
            ),

            okProps: { children: intl('saenext.web-app.basic-info.AppActionBar.Confirm') },
            footerActions: ['ok'],
          });
        } else {
          // 通用控制台弹窗
          errorPrompt(error);
        }
      }
    };
    const deleteConfirm = async () => {
      const promise = new Promise(async (resolve, reject) => {
        try {
          const result = await deleteApplication();
          if (result?.Data?.ApplicationId) {
            Message.success(intl('saenext.web-app.basic-info.AppActionBar.DeletedSuccessfully'));
            const existApps = parseInt(window.localStorage.getItem('EXIST_APPS')) || 0;
            trackResource({
              behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.DELETE,
              stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
              existApps,
            });
            history.replace(`/${regionId}/app-list/web`);
            resolve(result);
          } else {
            reject();
          }
        } catch (err) {
          console.log(err);
          reject(err);
        }
      });
      return promise;
    };
    const [err] = await tryfun(async () => deleteConfirm());
    if (!err) setDeleteDialogVisible(false);
    setLoading(false);
  };

  // 开启/关闭应用监控
  const handleArmsApplication = async () => {
    const enableAppMetric = get(data, 'enableAppMetric', false);
    const minInstanceCount = get(data, 'scaleConfig.minimumInstanceCount', 0);
    const title = enableAppMetric
      ? intl('saenext.web-app.basic-info.AppActionBar.StopApplicationMonitoring')
      : intl('saenext.web-app.basic-info.AppActionBar.EnableApplicationMonitoring');
    if (!minInstanceCount && !enableAppMetric) {
      Dialog.alert({
        title: title,
        content: (
          <p style={{ width: 500 }}>
            {intl('saenext.web-app.basic-info.AppActionBar.CurrentlyYourApplicationDoesNot')}
          </p>
        ),

        footerActions: ['cancel'],
      });
      return;
    }

    const content = enableAppMetric
      ? intl('saenext.web-app.basic-info.AppActionBar.WhenTheApplicationMonitoringIs')
      : intl('saenext.web-app.basic-info.AppActionBar.WhenApplicationMonitoringIsEnabled');

    const options = {
      applicationID,
      applicationName,
      enableAppMetric: !enableAppMetric,
      effectiveImmediately: true,
    };

    Dialog.alert({
      title: title,
      content: <p style={{ width: 500 }}>{content}</p>,
      okProps: { children: intl('saenext.version-detail.log-control.Confirm') },
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res = await services.updateWebApplication({
            applicationName,
            applicationID,
            content: options,
            customErrorHandle: (error, data, callback) => {
              reject();
              callback && callback();
            },
          });
          const { version: latestVersion } = res;
          if (!isEmpty(latestVersion)) {
            run && run();
            resolve(true);
            Message.success(
              intl('saenext.web-app.basic-info.AppActionBar.TitleSucceeded', { title: title }),
            );
          } else {
            reject();
            Message.error(
              intl('saenext.web-app.basic-info.AppActionBar.TitleFailed', { title: title }),
            );
          }
        });
      },
    });
  };

  return (
    <div className="flex justify-between flex-row-revers" style={{ margin: '-50px 0 16px' }}>
      <div>
        <Button type="primary" className="mr-s" onClick={handleUpdateApplication}>
          {intl('saenext.web-app.basic-info.AppActionBar.ModifyConfiguration')}
        </Button>
        <Button
          type="primary"
          className="mr-s"
          loading={destroying}
          disabled={isInDebt && data?.scaleConfig?.maximumInstanceCount === 0}
          onClick={onAppStatusChange}
        >
          {destroying
            ? intl('saenext.web-app.basic-info.AppActionBar.CleaningInstance')
            : data?.scaleConfig?.maximumInstanceCount === 0
            ? intl('saenext.web-app.basic-info.AppActionBar.StartTheApplication')
            : intl('saenext.web-app.basic-info.AppActionBar.StopApplication')}
        </Button>
        <MenuButton label={intl('saenext.web-app.basic-info.AppActionBar.More')} autoWidth={false}>
          {
            get(data, 'enableAppMetric', false) && !isLatestMonitor && (
              <MenuButton.Item onClick={handleArmsApplication}>
                {intl('saenext.web-app.basic-info.AppActionBar.StopApplicationMonitoring')}
              </MenuButton.Item>
            )
          }
          <MenuButton.Item onClick={handleDeleteApplication}>
            {intl('saenext.web-app.basic-info.AppActionBar.DeleteAnApplication')}
          </MenuButton.Item>
        </MenuButton>

        <Dialog
          title={intl('saenext.web-app.basic-info.AppActionBar.StartApplicationApplicationname', {
            applicationName: applicationName,
          })}
          visible={startDialogVisible}
          style={{ width: 640 }}
          onOk={() =>
            new Promise(async (resolve, reject) => {
              try {
                const res = await startWebApplicationRequest();
                if (!res) return;
                Message.success(
                  intl('saenext.web-app.basic-info.AppActionBar.StartedSuccessfully'),
                );
                // 关闭窗口
                setStartDialogVisible(false);
                // 尝试开启流水线
                if (hasPipeline && checkEnableCicd) {
                  await tryChangeCicd(true);
                }
                run();
                resolve({});
              } catch (err) {
                reject(err);
              }
            })
          }
          onCancel={() => setStartDialogVisible(false)}
          onClose={() => setStartDialogVisible(false)}
        >
          <Message type="notice">
            {intl('saenext.web-app.basic-info.AppActionBar.AfterTheApplicationIsStarted')}
          </Message>
          {hasPipeline && (
            <Checkbox
              defaultChecked={checkEnableCicd}
              style={{ marginTop: 16 }}
              onChange={handlerEnableCiCd}
            >
              {intl('saenext.web-app.basic-info.AppActionBar.EnableContinuousDeployment')}
            </Checkbox>
          )}
        </Dialog>

        <Dialog
          title={
            <div>
              <Icon type="warning" style={{ color: '#ffc440', verticalAlign: 'middle' }} />

              <span
                style={{
                  color: '#333',
                  fontSize: 18,
                  verticalAlign: 'middle',
                  marginLeft: 8,
                }}
                dangerouslySetInnerHTML={{
                  __html: intl(
                    'saenext.web-app.basic-info.AppActionBar.DeleteApplicationApplicationname',
                    { applicationName: applicationName },
                  ),
                }}
              />
            </div>
          }
          isFullScreen
          shouldUpdatePosition
          style={{ width: 640 }}
          visible={deleteDialogVisible}
          onOk={confirmDeleteApplication}
          okProps={{ disabled: getValue('key') !== applicationName, loading }}
          onCancel={() => setDeleteDialogVisible(false)}
          onClose={() => setDeleteDialogVisible(false)}
        >
          <div
            dangerouslySetInnerHTML={{
              __html: replace(
                // @ts-ignore
                intl('saenext.web-app.basic-info.AppActionBar.AreYouSureYouWant', {
                  applicationName: applicationName,
                }),
                applicationName,
                `<span style="color: #d93026">${applicationName}</span>`,
              ),
            }}
          />

          <div className="mt-l">
            <Input
              {...init('key')}
              style={{ width: '100%' }}
              placeholder={intl('saenext.web-app.basic-info.AppActionBar.EnterAnApplicationName')}
            />
          </div>
        </Dialog>
      </div>
    </div>
  );
};

export default AppActionBar;
