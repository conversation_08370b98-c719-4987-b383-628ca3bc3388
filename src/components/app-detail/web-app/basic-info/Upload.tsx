import React, { PureComponent } from 'react';
import { Button, Icon, Input, intl } from '@ali/cnd';
import { get } from 'lodash';

type Props = {
  onChange?: Function;
  name?: string;
  maxLength: number;
  placeholder: string;
};

type State = {
  content: string;
};

class Upload extends PureComponent<Props, State> {
  ref: any;

  constructor(props) {
    super(props);
    this.state = {
      content: '',
    };
    this.ref = React.createRef();
  }

  onChange(value) {
    this.setState({ content: value });
    const { onChange = () => {} } = this.props;
    onChange(value);
  }

  onFileChange(event) {
    const files = get(event, 'target.files', []);
    if (files.length > 0) {
      const [file] = files;
      const reader = new FileReader();

      reader.onloadend = async () => {
        const result = reader.result as string;
        this.onChange(result);
      };
      reader.readAsText(file);
    }
  }

  render() {
    const { name, maxLength, placeholder } = this.props;
    const { content } = this.state;
    return (
      <div>
        <Input.TextArea
          className="full-width mb-s"
          name={name}
          value={content}
          onChange={this.onChange.bind(this)}
          maxLength={maxLength}
          placeholder={placeholder}
          showLimitHint
        />

        <Button
          onClick={() => {
            this.ref.current.click();
          }}
        >
          <Icon type="upload">
            {intl('domain.cert.upload')}
          </Icon>
        </Button>

        <input
          ref={this.ref}
          type="file"
          style={{ display: 'none' }}
          onChange={this.onFileChange.bind(this)}
          onClick={event => {
            (event as any).target.value = null;
          }}
        />
      </div>
    );
  }
}

export default Upload;
