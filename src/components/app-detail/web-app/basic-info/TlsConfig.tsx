import React, { Component } from 'react';
import { intl, Form, Select, Checkbox } from '@ali/xconsole';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import CachedData from '~/cache/common';

const { Option } = Select;

const DOMAIN_TLS_VERSION = 'tlsVersion';
const DOMAIN_SUITES = 'tlsCipherSuites';
const CUSTOM_SUITES = 'customSuites';
const CHECKED_TLSV13 = 'checkedTlsv13';
const DOMAIN_TLS_MAXVERSION = 'tlsmaxVersion';

const ALL = 'all';
const CUSTOM = 'custom';
export const TLS_VERSIONS = ['TLSv1.0', 'TLSv1.1', 'TLSv1.2'];

export const STRONG_SUITES = [
  'TLS_RSA_WITH_AES_128_CBC_SHA',
  'TLS_RSA_WITH_AES_256_CBC_SHA',
  'TLS_RSA_WITH_AES_128_GCM_SHA256',
  'TLS_RSA_WITH_AES_256_GCM_SHA384',
  'TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA',
  'TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA',
  'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA',
  'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA',
  'TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256',
  'TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384',
  'TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256',
  'TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384',
  'TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305',
  'TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305',
];

export const WEAK_SUITES = [
  'TLS_RSA_WITH_RC4_128_SHA',
  'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
  'TLS_RSA_WITH_AES_128_CBC_SHA256',
  'TLS_ECDHE_ECDSA_WITH_RC4_128_SHA',
  'TLS_ECDHE_RSA_WITH_RC4_128_SHA',
  'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
  'TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256',
  'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256',
];

const FORM_LAYOUT_LARGE = {
  labelTextAlign: "left" as Align,
  wrapperCol: { span: 17 },
  labelCol: { span: 7, style: { width: 170, maxWidth: 170 }},
}

type Align = 'left' | 'right' | undefined;

type Props = {
  field: any;
  regionId: string;
  editMode: boolean;
};

type State = {
  customMode: string;
  selectedTlsVersion: string;
  selectedSuites: string[];
  checkedTlsv13: boolean;
};

class TlsConfig extends Component<Props, State> {
  constructor(props) {
    super(props);
    const { field } = props;
    const selectedSuites = field.getValue(DOMAIN_SUITES) || [];
    this.state = {
      customMode:
        selectedSuites.length === STRONG_SUITES.length + WEAK_SUITES.length ? ALL : CUSTOM,
      selectedSuites,
      selectedTlsVersion: field.getValue(DOMAIN_TLS_VERSION) || '',
      checkedTlsv13: false,
    };

    this.onCustomModeChanged = this.onCustomModeChanged.bind(this);
    this.onSelectedSuitesChanged = this.onSelectedSuitesChanged.bind(this);
    this.onSelectedTlsVersionChanged = this.onSelectedTlsVersionChanged.bind(this);
    this.onSelectCheckedTlsv13Changed = this.onSelectCheckedTlsv13Changed.bind(this);
  }

  componentDidMount() {
    const { field } = this.props;
    if (field.getValue(DOMAIN_TLS_MAXVERSION) === 'TLSv1.3') {
      this.setState({
        checkedTlsv13: true,
      });
      field.setValue(CHECKED_TLSV13, true);
    }
  }

  onSelectedTlsVersionChanged(value) {
    const { field } = this.props;
    const { selectedTlsVersion } = this.state;
    this.setState({ selectedTlsVersion: value });
    if (!value || !selectedTlsVersion) {
      this.onCustomModeChanged(ALL);
    }
    field.setValue(DOMAIN_TLS_VERSION, value);
  }

  onCustomModeChanged(value) {
    this.setState({
      customMode: value,
    });
    this.onSelectedSuitesChanged([...STRONG_SUITES, ...WEAK_SUITES]);
  }

  onSelectedSuitesChanged(value = []) {
    const { field } = this.props;
    this.setState({
      selectedSuites: value,
    });
    field.setValue(DOMAIN_SUITES, [...value]);
    if (value.length === 0) {
      field.setError(CUSTOM_SUITES, intl('domain.suite.error.tips'));
    }
  }

  onSelectCheckedTlsv13Changed(value) {
    const { field } = this.props;
    this.setState({
      checkedTlsv13: value,
    });
    field.setValue(CHECKED_TLSV13, value);
  }

  render() {
    const { field } = this.props;
    const {
      customMode,
      selectedSuites,
      selectedTlsVersion,
      checkedTlsv13
    } = this.state;

    return (
      <Form field={field}>
        <Form.Item
          label={
            <TextWithBalloon
              align="tr"
              color="color-light-black"
              text={intl('domain.tlsVersion.label')}
              tips={intl.html('domain.tlsVersion.tips.new',{
                href:CachedData.confLink('help:fc-2-0:configure-a-custom-domain-name')
              })}
            />
          }
          {...FORM_LAYOUT_LARGE}
        >
          <Select
            className="full-width"
            placeholder={intl('domain.tlsVersion.placeholder')}
            value={selectedTlsVersion}
            onChange={this.onSelectedTlsVersionChanged}
            hasClear
          >
            {TLS_VERSIONS.map(version => (
              <Option key={version} value={version}>
                {intl(`${version}.label`)}
              </Option>
            ))}
          </Select>

          {selectedTlsVersion && (
            <div className="mt-s">
              <Checkbox
                checked={checkedTlsv13}
                onChange={this.onSelectCheckedTlsv13Changed}
                label={
                  <TextWithBalloon
                    align="tr"
                    color="color-light-black"
                    text={intl('domain.tlsVersion.support13.label')}
                    tips={intl.html('domain.tlsVersion.support13.tips.new',{
                      href:CachedData.confLink('help:fc-2-0:configure-a-custom-domain-name')
                    })}
                  />
                }
              />
            </div>
          )}
        </Form.Item>

        {selectedTlsVersion && (
          <Form.Item
            label={
              <TextWithBalloon
                align="tr"
                color="color-light-black"
                text={intl('domain.suite.label')}
                tips={intl.html('domain.suite.tips.new',{
                  href:CachedData.confLink('help:fc-2-0:configure-a-custom-domain-name')
                })}
              />
            }
            {...FORM_LAYOUT_LARGE}
          >
            <div>
              <Select className="full-width" value={customMode} onChange={this.onCustomModeChanged}>
                <Option value={ALL}>{intl('domain.suites.all.label')}</Option>
                <Option value={CUSTOM}>{intl('domain.suites.custom.label')}</Option>
              </Select>
            </div>
            {customMode === CUSTOM && (
              <div className="mt">
                <Form.Item>
                  <Select
                    multiple
                    showSearch
                    hasClear
                    name={CUSTOM_SUITES}
                    className="full-width"
                    value={selectedSuites}
                    onChange={this.onSelectedSuitesChanged}
                    placeholder={intl('domain.suite.placeholder')}
                  >
                    {STRONG_SUITES.map(strong => (
                      <Option key={strong} value={strong}>
                        <span>{strong}</span>
                      </Option>
                    ))}

                    {WEAK_SUITES.map(weak => (
                      <Option key={weak} value={weak}>
                        <span>{weak}</span>
                        <span className="text-description ml-s">
                          {intl('domain.suite.weak.label')}
                        </span>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default TlsConfig;
