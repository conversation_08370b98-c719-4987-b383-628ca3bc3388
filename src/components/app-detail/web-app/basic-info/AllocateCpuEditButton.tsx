import { intl } from '@ali/cnd';
import React, { memo, useRef, useState, useEffect } from 'react';
import { Dialog, Icon, LinkButton } from '@ali/cnd';
import { Form, RadioField } from '@ali/deep';
import services from '~/services';
import { getParams } from '~/utils/global';

const AllocateCpuEditButton = ({ onSuccess, data, applicationID }) => {
  const applicationName = getParams('name');
  const [visible, setVisible] = useState(false);
  const [alwaysAllocateCPU, setAlwaysAllocateCPU] = useState(false);
  const field = useRef(null);

  useEffect(() => {
    if (visible) {
      setAlwaysAllocateCPU(data?.alwaysAllocateCPU);
    }
  }, [visible]);

  const toggleVisible = () => {
    setVisible(!visible);
  };

  const onChangeAllocateCpu = async () => {
    const promise = new Promise<void>(async (resolve, reject) => {
      updateAllocateCpu(resolve, reject);
    });
    return promise;
  };

  const updateAllocateCpu = async (resolve, reject) => {
    const result = await services.updateApplicationScale(applicationID, applicationName, {
      ...data,
      alwaysAllocateCPU,
    });

    if (result) {
      setVisible(false);
      onSuccess();
      resolve(result);
    } else {
      reject();
    }
  };

  return (
    <>
      <LinkButton onClick={toggleVisible}>
        <Icon type={'edit'} size="small" className="ml-l" />
      </LinkButton>
      <Dialog
        size="medium"
        title={intl('saenext.web-app.basic-info.AllocateCpuEditButton.EditCpuAllocationPolicy')}
        visible={visible}
        onOk={onChangeAllocateCpu}
        onCancel={toggleVisible}
        onClose={toggleVisible}
      >
        <Form
          ref={(c) => {
            if (c) {
              field.current = c.getInstance();
            }
          }}
        >
          <RadioField
            required
            name="alwaysAllocateCPU"
            value={alwaysAllocateCPU}
            onChange={({ value }) => {
              setAlwaysAllocateCPU(value);
            }}
            dataSource={[
              {
                value: false,
                text: (
                  <span className="radio-item-120">
                    {intl(
                      'saenext.web-app.basic-info.AllocateCpuEditButton.CpuIsAllocatedOnlyDuring',
                    )}
                  </span>
                ),
              },
              {
                value: true,
                disabled: true,
                text: (
                  <span className="radio-item-120">
                    {intl(
                      'saenext.web-app.basic-info.AllocateCpuEditButton.AlwaysAllocateAFixedCpu',
                    )}
                  </span>
                ),
              },
            ]}
            help={
              !alwaysAllocateCPU
                ? intl(
                    'saenext.web-app.basic-info.AllocateCpuEditButton.DuringRequestProcessingYouNeed',
                  )
                : intl('saenext.web-app.basic-info.AllocateCpuEditButton.YouAlwaysHaveToPay')
            }
          />
        </Form>
      </Dialog>
    </>
  );
};

export default memo(AllocateCpuEditButton);
