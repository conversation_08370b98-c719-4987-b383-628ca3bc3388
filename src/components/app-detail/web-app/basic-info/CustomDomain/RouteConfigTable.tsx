import React, { useEffect, useState } from 'react';
import { Button, Form, Icon, Input, intl, Select } from '@ali/cnd';
import ValueTable from '~/components/shared/ValueTable';
import services from '~/services';
import { debounce, map } from 'lodash';

const RouteConfigTable = (props) => {
  const { field, name, value = [], onChange = () => {}, namespaceID } = props;

  const [applicationList, setApplicationList] = useState<any[]>([]);

  useEffect(() => {
    getApplicationList();
  }, []);

  const fetchApplications = async (prefix, limit) => {
    const { applications = [] } =
      (await services.listWebApplications({
        prefix,
        namespaceID,
        limit,
      })) || {};
    return applications;
  };

  const getApplicationList = async (prefix?) => {
    const applications = await fetchApplications(prefix, 100);
    const applicationList = map(applications, (item) => ({
      label: item.applicationName,
      value: item.applicationName,
      ...item,
    }));
    setApplicationList(applicationList);
  };

  const columns = ({ onDelete, onItemChange }) => [
    {
      dataIndex: 'path',
      width: 180,
      cell: (val, idx, record) => (
        <Form.Item
          required
          requiredMessage=" "
          pattern={/^\/[^\{\}\*]*(\*)?$|^\/\*$/}
          patternMessage={intl(
            'saenext.basic-info.CustomDomain.RouteConfigTable.EnterTheCorrectPath',
          )}
        >
          <Input
            className="full-width"
            addonTextBefore={intl('saenext.basic-info.CustomDomain.RouteConfigTable.Path')}
            placeholder={intl(
              'saenext.basic-info.CustomDomain.RouteConfigTable.EnterAPathForExample',
            )}
            name={`${name}.${idx}.path`}
            onChange={(val) => {
              onItemChange(idx, { path: val });
            }}
          />
        </Form.Item>
      ),
    },
    {
      dataIndex: 'applicationName',
      width: 100,
      cell: (val, idx, record) => (
        <Form.Item required requiredMessage=" ">
          <Select
            className="full-width"
            name={`${name}.${idx}.applicationName`}
            placeholder={intl(
              'saenext.basic-info.CustomDomain.RouteConfigTable.SelectAnApplication',
            )}
            dataSource={applicationList}
            showSearch
            filterLocal={false}
            onSearch={debounce(getApplicationList, 500)}
            onSearchClear={debounce(() => getApplicationList(), 500)}
            onChange={(val) => {
              onItemChange(idx, { applicationName: val });
            }}
          />
        </Form.Item>
      ),
    },
    {
      width: 10,
      cell: (val, idx, record) => {
        return (
          <Button
            text
            className="scale-medium"
            onClick={() => {
              onDelete(idx);
            }}
          >
            <Icon type="delete" />
          </Button>
        );
      },
    },
  ];

  return (
    <ValueTable
      className="route-config-table"
      field={field}
      name={name}
      value={value}
      onChange={onChange}
      maxLength={20}
      columns={columns}
      emptyContent={<></>}
    />
  );
};

export default RouteConfigTable;
