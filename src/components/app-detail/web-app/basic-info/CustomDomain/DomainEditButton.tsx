import { intl } from '@ali/cnd';
import React, { forwardRef, memo, useImperativeHandle, useRef } from 'react';
import SlideButton from '~/components/shared/SlideButton';
import DomainSetting from './DomainSetting';

const DomainEditButton = ({
  linkButton = false,
  buttonText,
  type,
  onSuccess,
  data,
  applicationID,
  applicationName,
  namespaceID,
  onClick = () => {},
}, domainRef) => {
  const ref = useRef(null);
  const btnRef = useRef(null);

  useImperativeHandle(
    domainRef,
    () => ({
      setActive: (active) => {
        btnRef.current.setActive(active);
      },
    })
  )

  return (
    <SlideButton
      ref={btnRef}
      buttonText={buttonText}
      slideTitle={intl('saenext.web-app.basic-info.DomainEditButton.CustomDomainName')}
      slideSize={900}
      slideContent={
        <DomainSetting
          ref={ref}
          type={type}
          data={data}
          applicationID={applicationID}
          applicationName={applicationName}
          namespaceID={namespaceID}
          onSuccess={onSuccess}
        />
      }
      autoClose={false}
      linkButton={linkButton}
      onClick={onClick}
      submit={() => {
        if (ref.current) {
          return ref.current.submit();
        }
      }}
    />
  );
};

export default memo(forwardRef(DomainEditButton));
