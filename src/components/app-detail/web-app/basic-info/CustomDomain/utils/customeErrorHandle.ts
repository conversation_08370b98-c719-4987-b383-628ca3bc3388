import { intl } from '@ali/cnd';
import { Dialog } from '@ali/cnd';
import CachedData from '~/cache/common';

export const customeErrorHandleForDomain = (err, data, callback) => {
  if (err.code === 'WAFIsNotEnable') {
    Dialog.alert({
      title: intl('saenext.CustomDomain.utils.customeErrorHandle.WebApplicationFirewallHasNot'),
      content: intl.html('saenext.CustomDomain.utils.customeErrorHandle.WebApplicationFirewallHasNot.1.new',{
        href: `${CachedData.confLink('feature:yundun:url')}/?p=waf#/waf/cn/dashboard/index`
      }),
      footerActions: ['ok'],
    });
  } else if (err.code === 'WAFVersionNotSupport') {
    Dialog.alert({
      title: intl('saenext.CustomDomain.utils.customeErrorHandle.UnableToEnableWebApplication'),
      content: intl('saenext.CustomDomain.utils.customeErrorHandle.SaeOnlySupportsWebApplication'),
      footerActions: ['ok'],
    });
    return;
  }
  callback();
};
