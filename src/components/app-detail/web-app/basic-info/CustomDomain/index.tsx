import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  Copy,
  Actions,
  Button,
  Dialog,
  DateTime,
  Icon,
  Message,
  intl,
  ConsoleContext,
  MenuButton,
  CndTable,
  Search
} from '@ali/cnd';
import EditButton from './DomainEditButton';
import services from '~/services';
import { AES_CONSTANT, trackCore } from '~/tracker';
import WafLabel from '../WafLabel';
import If from '~/components/shared/If';
import _, { filter, get } from 'lodash';
import useOpenApi from '~/utils/useOpenApi';
import { lowerFirstData } from '~/utils/transfer-data';

const { LinkButton } = Actions;
const { Item } = MenuButton;

type Props = {
  applicationID?: string;
  applicationName?: string;
  namespaceID?: string;
};

const CustomDomain = (props: Props) => {
  const { applicationID, applicationName, namespaceID } = props;

  const {
    data = [],
    loading,
    run: fetchData,
  } = useOpenApi(
    'serverless',
    'ListWebCustomDomains',
    {
      NamespaceId: namespaceID,
      ApplicationId: applicationID,
      Limit: 100,
      Prefix: '',
      NextToken: '',
    },
    {
      ignoreError: true,
    },
  );

  const { Data: { WebCustomDomains = [] } = {} } = data;
  lowerFirstData(WebCustomDomains);
  WebCustomDomains.sort(
    (a, b) => new Date(b.createdTime).getTime() - new Date(a.createdTime).getTime(),
  );

  const defaultSelectedDomains = WebCustomDomains.map((item) => item.domainName);

  const [namespaceDomains, setNamespaceDomains] = useState([]);
  const [selectedDomains, setSelectedDomains] = useState([]);
  const [bindLoading, setBindLoading] = useState(false);
  const [bindVisible, setBindVisible] = useState(false);
  const [bindData, setBindData] = useState({});

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const bindRef = useRef(null);

  useEffect(() => {
    if (!applicationID) return;
    setSelectedDomains(defaultSelectedDomains);
  }, [data?.Data?.WebCustomDomains]);

  const getNamespaceDomains = async () => {
    setBindLoading(true);
    const res = await services.listCustomDomains({
      NamespaceId: namespaceID,
      Limit: 100,
      Prefix: '',
      NextToken: '',
    });
    const { Data: { WebCustomDomains = [] } = {} } = res || {};
    setNamespaceDomains(lowerFirstData(WebCustomDomains));
    setBindLoading(false);
  };

  const deleteConfirm = async (domainName) => {
    const promise = new Promise(async (resolve, reject) => {
      try {
        const result = await deleteDomain(domainName);
        if (result) {
          Message.success(intl('saenext.web-app.basic-info.CustomDomain.DeletedSuccessfully'));
          fetchData();
          resolve(result);
        } else {
          reject();
        }
      } catch (err) {
        console.log(err);
        reject(err);
      }
    });
    return promise;
  };

  const deleteDomain = async (domainName) => {
    const result = await services.deleteCustomDomain({
      params: {
        NamespaceId: namespaceID,
        DomainName: domainName,
      },
    });
    return result;
  };

  const onPreviewRoutes = (routes, record) => {
    const { defaultForwardingAppName, domainName } = record;
    const defaultRoutes = defaultForwardingAppName
      ? [
          {
            path: '/*',
            applicationName: defaultForwardingAppName,
          },
        ]
      : [];
    const allRoutes = [...defaultRoutes, ...routes];

    Dialog.show({
      title: intl('saenext.basic-info.CustomDomain.ViewRouteConfigurations'),
      style: { width: 800 },
      content: (
        <CndTable dataSource={allRoutes}>
          <CndTable.Column
            title={intl('saenext.basic-info.CustomDomain.DomainNamePath')}
            dataIndex="path"
            cell={(value) => `${domainName}${value}`}
          />

          <CndTable.Column
            title={intl('saenext.basic-info.CustomDomain.ApplicationName')}
            dataIndex="applicationName"
            cell={(value) => (
              <a
                href={`/${regionId}/app-list/web-app/base?namespaceId=${namespaceID}&name=${value}`}
                target="_blank"
              >
                {value}
              </a>
            )}
          />
        </CndTable>
      ),
    });
  };

  const onBindSelect = (domainName) => {
    const selectedDomainData = namespaceDomains.find((item) => item.domainName === domainName);
    setBindData(selectedDomainData);
    bindRef.current.setActive(true);
    setBindVisible(false);
  };

  const columns = [
    {
      dataIndex: 'domainName',
      hide: !!applicationID,
      title: intl('saenext.web-app.basic-info.CustomDomain.DomainName'),
      cell: (value) => (
        <a href={`http://${value}`} target="_blank">
          <Copy text={value}>{value}</Copy>
        </a>
      ),
    },
    {
      dataIndex: 'domainName',
      hide: !applicationID,
      title: intl('saenext.web-app.basic-info.CustomDomain.DomainName'),
      cell: (value, _index, record) => {
        const routes = get(record, 'routeConfig.routes', []);
        const matchRoutes = filter(routes, { applicationName });
        if (record.defaultForwardingAppName) {
          matchRoutes.unshift({
            path: '/*',
            applicationName: record.defaultForwardingAppName,
          });
        }
        if (!matchRoutes.length) {
          return (
            <a href={`http://${value}`} target="_blank">
              <Copy text={value}>{value}</Copy>
            </a>
          );
        }
        return (
          <>
            {matchRoutes.map((item) => (
              <div>
                <a href={`http://${value}${item.path}`} target="_blank">
                  {value}
                  {item.path}
                </a>
              </div>
            ))}
          </>
        );
      },
    },
    {
      dataIndex: 'protocol',
      title: intl('saenext.web-app.basic-info.CustomDomain.ProtocolType'),
    },
    {
      dataIndex: 'wafConfig',
      title: intl('saenext.web-app.basic-info.CustomDomain.WebApplicationFirewall'),
      cell: (value, _index, record) => (
        <WafLabel
          record={record}
          namespaceId={namespaceID}
          refresh={fetchData}
        />
      ),
    },
    {
      dataIndex: 'routeConfig',
      title: intl('saenext.basic-info.CustomDomain.RouteConfiguration'),
      hide: !!applicationID,
      cell: (value, _index, record) => {
        const { routes = [] } = value || {};
        const { defaultForwardingAppName } = record;
        return (
          <LinkButton
            disabled={!routes.length && !defaultForwardingAppName}
            onClick={() => onPreviewRoutes(routes, record)}
          >
            {intl('saenext.basic-info.CustomDomain.ViewDetails')}
          </LinkButton>
        );
      },
    },
    {
      dataIndex: 'createdTime',
      title: intl('saenext.web-app.basic-info.CustomDomain.CreationTime'),
      cell: (value) => <DateTime value={value} />,
    },
    {
      dataIndex: 'lastModifiedTime',
      title: intl('saenext.web-app.basic-info.CustomDomain.LastModifiedTime'),
      cell: (value) => <DateTime value={value} />,
    },
    {
      dataIndex: 'operation',
      title: intl('saenext.web-app.basic-info.CustomDomain.Operation'),
      cell: (value, index, record) => (
        // @ts-ignore
        <Actions>
          <>
            <EditButton
              type={'update'}
              linkButton
              buttonText={intl('saenext.web-app.basic-info.CustomDomain.Edit')}
              data={record}
              applicationID={applicationID}
              applicationName={applicationName}
              namespaceID={namespaceID}
              onSuccess={fetchData}
            />

            <LinkButton
              onClick={() => {
                Dialog.confirm({
                  title: intl('list.delete.title'),
                  content: intl('saenext.web-app.basic-info.CustomDomain.DeleteACustomDomainName'),
                  messageProps: {
                    type: 'warning',
                  },
                  onOk: () => deleteConfirm(record.domainName),
                });
              }}
            >
              {intl('list.delete')}
            </LinkButton>
          </>
        </Actions>
      ),
    },
  ];

  return (
    <div className="extra-table">
      {/* @ts-ignore */}
      <CndTable
        loading={loading}
        dataSource={WebCustomDomains}
        columns={_.filter(columns, item => item.hide !== true)}
        operation={
          <>
            <If condition={!applicationID}>
              <EditButton
                type={'create'}
                buttonText={intl('saenext.web-app.basic-info.CustomDomain.CreateACustomDomainName')}
                data={{}}
                applicationID={applicationID}
                applicationName={applicationName}
                namespaceID={namespaceID}
                onClick={() => {
                  trackCore({
                    behavior: AES_CONSTANT.CORE_BEHAVIOR_TYPE.CUSTOM_HOST,
                    stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
                  });
                }}
                onSuccess={fetchData}
              />
            </If>
            <If condition={!!applicationID}>
              <MenuButton
                autoWidth={false}
                label={intl('saenext.web-app.basic-info.CustomDomain.AssociateCustomDomainNames')}
                visible={bindVisible}
                loading={bindLoading}
                selectMode="multiple"
                selectedKeys={selectedDomains}
                onVisibleChange={visible => {
                  if (visible && applicationID) {
                    getNamespaceDomains();
                  }
                  setBindVisible(visible);
                }}
                onItemClick={onBindSelect}
                type="primary"
              >
                <LinkButton
                  Component="a"
                  href={`/${regionId}/namespace/${namespaceID}/domain`}
                  target="_blank"
                  style={{ display: 'block', textAlign: 'center' }}
                >
                  {intl('saenext.web-app.basic-info.CustomDomain.CreateACustomDomainName.1')}
                </LinkButton>
                {namespaceDomains.map(item => {
                  return <Item key={item.domainName}>{item.domainName}</Item>;
                })}
              </MenuButton>
            </If>
            <Search
              className="ml-s"
              filter={[
                {
                  value: 'domain',
                  label: intl('saenext.web-app.basic-info.CustomDomain.DomainName'),
                },
              ]}
              filterValue="domain"
              placeholder={intl('saenext.web-app.basic-info.CustomDomain.EnterADomainNameTo')}
              hasClear
              onSearch={value => {
                fetchData({
                  NamespaceId: namespaceID,
                  ApplicationId: applicationID,
                  Limit: 100,
                  Prefix: value,
                  NextToken: '',
                });
              }}
              onChange={value => {
                if (!value) {
                  fetchData();
                }
              }}
            />
          </>
        }
        secondaryOperation={
          <Button onClick={() => fetchData()}>
            <Icon type="refresh" />
          </Button>
        }
        search={{
          children: !!applicationID ? (
            <Message type="notice" className="inline-block" style={{ padding: 8, height: 32 }}>
              {intl('saenext.basic-info.CustomDomain.WhenAssociatingACustomDomain')}
            </Message>
          ) : null,
        }}
      />

      <EditButton
        ref={bindRef}
        type={'update'}
        linkButton
        buttonText={''}
        data={bindData}
        applicationID={applicationID}
        applicationName={applicationName}
        namespaceID={namespaceID}
        onSuccess={fetchData}
      />
    </div>
  );
};

export default CustomDomain;
