import React, { useContext, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import {
  Copy,
  Field,
  Form,
  Input,
  Message,
  Radio,
  Checkbox,
  Select,
  Truncate,
  Button,
  ConsoleContext,
  intl,
  Loading,
} from '@ali/cnd';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import ExternalLink from '~/components/shared/ExternalLink';
import { replaceAll } from '~/utils/global';
import services from '~/services';
import RefreshButton from '~/components/shared/RefreshButton';
import TlsConfig from '../TlsConfig';
import _, { cloneDeep } from 'lodash';
import { AES_CONSTANT, trackCore } from '~/tracker';
import Upload from '../Upload';
import { lowerFirstData, upperFirstData } from '~/utils/transfer-data';
import RouteConfigTable from './RouteConfigTable';
import { customeErrorHandleForDomain } from './utils/customeErrorHandle';
import CachedData from '~/cache/common';

const RadioGroup = Radio.Group;

type Align = 'left' | 'right' | undefined;

const DomainSetting = (props, ref) => {
  const field = Field.useField({ parseName: true });
  const {
    data,
    type,
    applicationID,
    // applicationName,
    namespaceID,
    onSuccess,
  } = props;

  const [isHttpsOnly, setIsHttpsOnly] = useState(false);
  const [fetchingCerts, setFetchingCerts] = useState(true);
  const [noPermissionCerts, setNoPermissionCert] = useState(false);
  const [certs, setCerts] = useState<any[]>([]);
  const [yundungCert, setYundCert] = useState<any>({});
  const [allowEditCert, setAllowEditCert] = useState(type === 'create');
  const [formLoading, setFormLoading] = useState(false);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const uid = _.get(window, 'ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK');
  const DOMAIN_PREFIX = `${uid}.${regionId}`;

  useImperativeHandle(
    ref,
    () => ({
      submit,
    }),
    [field.getValues()],
  );

  useEffect(() => {
    if (data.domainName) {
      initDomainConfig(data.domainName);
    }
  }, [data.domainName]);

  const initDomainConfig = async (domainName) => {
    setFormLoading(true);
    const result = await services.getCustomDomain({
      DomainName: domainName,
      ApplicationId: applicationID,
      NamespaceId: namespaceID,
    });
    if (result) {
      const {
        // routeConfig :{
        //   routes = []
        // } = {},
        routeConfig = {},
        defaultForwardingAppName,
        protocol,
        webCertConfig = {},
        webTLSConfig = {},
        webWAFConfig = {},
      } = lowerFirstData(result);

      if (defaultForwardingAppName) {
        const { routes } = routeConfig;
        routes.unshift({
          path: '/*',
          applicationName: defaultForwardingAppName,
        });
      }

      const values = {
        ...result,
        // routes,
        routeConfig,
        isHttps: protocol.includes('HTTPS'),
        certName: protocol.includes('HTTPS') ? webCertConfig.certName : '',
        enableWAF: webWAFConfig?.enableWAF,
        tlsVersion: webTLSConfig.minVersion || '',
        tlsCipherSuites: webTLSConfig.cipherSuites || [],
        tlsmaxVersion: webTLSConfig.maxVersion || '',
      };

      const isHttpsOnly = protocol === 'HTTPS';

      field.setValues(values);
      setIsHttpsOnly(isHttpsOnly);

      // await getAppId(applicationName);
      setFormLoading(false);
    }
  };

  const checkDuplicateNames = async (rule, value, callback) => {
    //域名正则
    const domainReg = /^([*a-z0-9A-Z][-a-z0-9A-Z]{0,62}\.)+[a-zA-Z]{2,}$/;
    if (!domainReg.test(value)) {
      callback(intl('saenext.web-app.basic-info.DomainSetting.EnterAValidDomainName'));
      return;
    }
    if (type === 'update') return;
    const nameDuplicate = await services.getCustomDomain(
      {
        NamespaceId: namespaceID,
        DomainName: value,
      },
      true,
    );
    if (nameDuplicate) {
      callback(intl('saenext.web-app.basic-info.DomainSetting.CustomDomainNameDuplicate'));
    } else {
      callback();
    }
  };

  const submit = async () => {
    const promise = new Promise<void>(async (resolve, reject) => {
      field.validate((errors, values) => {
        if (errors) {
          reject(errors);
          return;
        }
        doSubmit(values, resolve, reject);
      });
    });

    return promise;
  };

  const doSubmit = async (values, resolve, reject) => {
    const {
      domainName,
      isHttps,
      isYundun,
      checkedTlsv13,
      tlsVersion,
      tlsCipherSuites,
      customCertName,
      certContent,
      keyContent,
      enableWAF,
      routeConfig,
    } = cloneDeep(values);
    const protocol = isHttps ? (isHttpsOnly ? 'HTTPS' : 'HTTP,HTTPS') : 'HTTP';

    const { routes = [] } = routeConfig;

    const defaultAppRouteIndex = routes.findIndex((item) => item.path === '/*');
    const defaultForwardingAppName =
      defaultAppRouteIndex > -1 ? routes[defaultAppRouteIndex].applicationName : undefined;
    if (defaultAppRouteIndex > -1) {
      routes.splice(defaultAppRouteIndex, 1);
    }

    const content: any = {
      domainName,
      protocol,
      webTLSConfig: null,
      webWAFConfig: {
        enableWAF,
      },
      routeConfig,
      defaultForwardingAppName,
    };

    if (isHttps && allowEditCert) {
      if (isYundun) {
        content['webCertConfig'] = yundungCert;
      } else {
        content['webCertConfig'] = {
          certName: customCertName,
          certificate: certContent,
          privateKey: keyContent,
        };
      }
    }

    if (tlsVersion && tlsCipherSuites && tlsCipherSuites.length > 0) {
      content['webTLSConfig'] = {
        minVersion: tlsVersion,
        cipherSuites: tlsCipherSuites,
        maxVersion: checkedTlsv13 ? 'TLSv1.3' : 'TLSv1.2',
      };
    }

    let service;
    let params: any;

    switch (type) {
      case 'create':
        params = {
          NamespaceId: namespaceID,
        };
        service = services.createCustomDomain;
        break;
      case 'update':
        params = {
          DomainName: domainName,
          NamespaceId: namespaceID,
        };
        service = services.updateCustomDomain;
        break;
    }

    try {
      const result = await service({
        params,
        content: upperFirstData(content),
      },
        customeErrorHandleForDomain
      );

      if (result) {
        trackCore({
          behavior: AES_CONSTANT.CORE_BEHAVIOR_TYPE.CUSTOM_HOST,
          stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
        });
        onSuccess();
        resolve(result);
      } else {
        reject();
      }
    } catch (err) {
      reject(err);
    }
  };

  const fetchCertificates = async () => {
    setFetchingCerts(true);
    setNoPermissionCert(false);

    let listData = [];
    let page = 1;
    let total = 0;
    try {
      do {
        const {
          CertificateList = [],
          TotalCount = 0,
          noPermission,
        } = await services.DescribeUserCertificateList({
          ShowSize: 100,
          CurrentPage: page,
          customErrorHandle: (error, data) => {
            if (
              error.code === 'NoPermission' ||
              error.response?.data?.message?.startsWith(
                'NoPermission : caller has no permission',
              ) ||
              error.code === 'Forbidden.NoPermission'
            ) {
              return { noPermission: true };
            }
          },
        });
        if (noPermission) {
          page = 0;
          setNoPermissionCert(true);
        } else {
          total = TotalCount;
          listData = listData.concat(CertificateList);
          total > 100 * page ? (page += 1) : (page = 0);
        }
      } while (page);

      const certs = listData.map((c) => {
        return { name: c.name, sans: c.sans, certId: c.id };
      });
      setCerts(certs);
    } catch (e) {}

    setFetchingCerts(false);
  };

  const onHttpsChange = (value) => {
    field.setValue('isYundun', value);
    if (value) {
      fetchCertificates();
    }
  };

  const onYundunCertChange = async (certId) => {
    const {
      Key,
      Cert,
      Name: certName,
    } = await services.DescribeUserCertificateDetail({
      CertId: certId,
    });
    let validCertName = replaceAll(certName, '.', '-');
    if (!/^[_a-zA-Z][-_a-zA-Z0-9]*$/.test(validCertName)) {
      validCertName = `cert-${certId}`;
    }
    const certConfig = {
      certName: validCertName,
      certificate: Cert,
      privateKey: Key,
    };
    setYundCert(certConfig);
  };

  const editCert = () => {
    field.setValue('isYundun', true);
    setAllowEditCert(true);
    fetchCertificates();
  };

  const FORM_LAYOUT_LARGE = {
    labelTextAlign: 'left' as Align,
    wrapperCol: { span: 17 },
    labelCol: { span: 7, style: { width: 170, maxWidth: 170 } },
  };

  return (
    <Loading visible={formLoading}>
      <Form field={field}>
        <Form.Item
          label={intl('saenext.web-app.basic-info.DomainSetting.DomainName')}
          required
          requiredMessage={intl('saenext.web-app.basic-info.DomainSetting.PleaseFillInTheDomain')}
          validator={checkDuplicateNames}
          validatorTrigger="onBlur"
          isPreview={type === 'update'}
          {...FORM_LAYOUT_LARGE}
        >
          <Input name="domainName" />
        </Form.Item>
        <Form.Item
          label={intl('saenext.web-app.basic-info.DomainSetting.PublicCname')}
          {...FORM_LAYOUT_LARGE}
        >
          <span className="block mb">
            <Copy text={`${DOMAIN_PREFIX}.sae.aliyuncs.com`}>
              {`${DOMAIN_PREFIX}.sae.aliyuncs.com`}
            </Copy>
          </span>
          <ExternalLink
            label={intl('saenext.web-app.basic-info.DomainSetting.CloudDnsConsole')}
            url={CachedData.confLink('feature:dns:url')}
          />

          <Message type="notice">
            {intl('saenext.web-app.basic-info.DomainSetting.NoteBeforeCreatingACustom')}
            {DOMAIN_PREFIX}
            {intl('saenext.web-app.basic-info.DomainSetting.TheCnameOfSaeAliyuncs')}
            <a href={CachedData.confLink('help:sae:access-apps-through-a-custom-domain-name')} target="_blank">
              {intl('saenext.web-app.basic-info.DomainSetting.ConfigureCustomDomainNames')}
            </a>
          </Message>
        </Form.Item>
        <Form.Item
          label={intl('saenext.web-app.basic-info.DomainSetting.IntranetCname')}
          {...FORM_LAYOUT_LARGE}
        >
          <span className="block mb">
            <Copy text={`${DOMAIN_PREFIX}-internal.sae.aliyuncs.com`}>
              {`${DOMAIN_PREFIX}-internal.sae.aliyuncs.com`}
            </Copy>
          </span>
          <ExternalLink
            label={intl('saenext.web-app.basic-info.DomainSetting.CloudDnsConsole')}
            url={CachedData.confLink('feature:dns:url')}
          />
        </Form.Item>
        <Form.Item label={'HTTPS'} {...FORM_LAYOUT_LARGE}>
          <RadioGroup
            name="isHttps"
            shape="button"
            size="medium"
            defaultValue={false}
            onChange={onHttpsChange}
          >
            <Radio value={true}>{intl('saenext.web-app.basic-info.DomainSetting.Enable')}</Radio>
            <Radio value={false}>{intl('saenext.web-app.basic-info.DomainSetting.Disable')}</Radio>
          </RadioGroup>

          {field.getValue('isHttps') && (
            <div className="mt-s">
              <Checkbox checked={isHttpsOnly} onChange={setIsHttpsOnly}>
                <TextWithBalloon
                  text={intl('domain.https.only.label')}
                  tips={intl.html('domain.https.only.tips')}
                />
              </Checkbox>
            </div>
          )}
        </Form.Item>

        {type === 'update' && !allowEditCert && field.getValue('isHttps') && (
          <Form.Item label={intl('domain.cert.yundun.certName.label')} {...FORM_LAYOUT_LARGE}>
            <Input name="certName" value={field.getValue('certName')} isPreview />
            <Button text onClick={editCert}>
              {intl('domain.cert.edit.upload')}
            </Button>
          </Form.Item>
        )}

        {allowEditCert && field.getValue('isHttps') && (
          <Form.Item
            label={intl('domain.cert.type.label')}
            {...FORM_LAYOUT_LARGE}
            extra={
              <>
                {field.getValue('isYundun') && (
                  <Message type="notice" className="mt">
                    {intl.html('domain.cert.yundun.tips', {
                      url: `${CachedData.confLink('feature:yundun:url')}/?p=cas#/overview/${regionId}`,
                    })}
                  </Message>
                )}
              </>
            }
          >
            <RadioGroup name={'isYundun'} shape="button" size="medium">
              <Radio value={true}>{intl('domain.cert.yundun.label')}</Radio>
              <Radio value={false}>{intl('domain.cert.custom.label')}</Radio>
            </RadioGroup>
          </Form.Item>
        )}

        {allowEditCert && field.getValue('isYundun') && (
          <Form.Item
            label={intl('domain.cert.yundun.certName.label')}
            {...FORM_LAYOUT_LARGE}
            required
            requiredMessage={
              intl('general.required', {
                name: intl('domain.cert.yundun.certName.label'),
              }) as string
            }
            extra={
              <div>
                <div className="mt-s">
                  <RefreshButton
                    label={intl('saenext.web-app.basic-info.DomainSetting.Refresh')}
                    handler={fetchCertificates}
                  />
                </div>
                {noPermissionCerts && (
                  <Message className="mt-s" type="warning">
                    {intl('cert.no.permission')}
                  </Message>
                )}
              </div>
            }
          >
            <Select
              disabled={fetchingCerts}
              name={'certName'}
              className="full-width"
              showSearch
              placeholder={intl('domain.cert.yundun.certName.placeholder')}
              onChange={onYundunCertChange}
            >
              {certs.map((cert) => (
                <Select.Option key={cert.name} value={cert.certId}>
                  {cert.name}
                  <span className="ml-l">
                    {cert.sans && (
                      <Truncate threshold={800} showTooltip={false}>
                        <span className="text-description">{cert.sans}</span>
                      </Truncate>
                    )}
                  </span>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {allowEditCert && field.getValue('isHttps') && !field.getValue('isYundun') && (
          <Form.Item
            label={intl('domain.cert.yundun.certName.label')}
            {...FORM_LAYOUT_LARGE}
            required
            requiredMessage={
              intl('general.required', {
                name: intl('domain.cert.yundun.certName.label'),
              }) as string
            }
            pattern={/^[_a-zA-Z][-_a-zA-Z0-9]*$/}
            patternMessage={intl('domain.cert.yundun.certName.rule')}
          >
            <Input
              name={'customCertName'}
              placeholder={intl('domain.cert.custom.certName.placeholder')}
            />
          </Form.Item>
        )}

        {allowEditCert && field.getValue('isHttps') && !field.getValue('isYundun') && (
          <Form.Item
            label={intl('domain.cert.certificate.label')}
            {...FORM_LAYOUT_LARGE}
            required
            requiredMessage={
              intl('general.required', {
                name: intl('domain.cert.certificate.label'),
              }) as string
            }
          >
            <Upload
              name={'certContent'}
              maxLength={20480}
              placeholder={intl('domain.cert.certificate.placeholder')}
            />
          </Form.Item>
        )}

        {allowEditCert && field.getValue('isHttps') && !field.getValue('isYundun') && (
          <Form.Item
            label={intl('domain.cert.key.label')}
            {...FORM_LAYOUT_LARGE}
            required
            requiredMessage={
              intl('general.required', {
                name: intl('domain.cert.key.label'),
              }) as string
            }
          >
            <Upload
              name={'keyContent'}
              maxLength={4096}
              placeholder={intl('domain.cert.key.placeholder')}
            />
          </Form.Item>
        )}

        {field.getValue('isHttps') && (
          <TlsConfig field={field} regionId={regionId} editMode={true} />
        )}

        {/* <Form.Item
            label={
              <TextWithBalloon
                align="tr"
                color="color-light-black"
                text='CDN加速'
                tips='根据业务实际的需求，您可以为该域名设置 CDN 加速功能。将部署在SAE上的应用作为源站，把源内容发布到边缘节点，使终端用户能快速读取所需要内容。查看详情'
              />
            }
            {...FORM_LAYOUT_LARGE}
           >
            <RadioGroup
              name="cdn"
              shape="button"
              size="medium"
              defaultValue={false}
            >
              <Radio value={true}>启用</Radio>
              <Radio value={false}>禁用</Radio>
            </RadioGroup>
           </Form.Item> */}
        <Form.Item
          label={
            <TextWithBalloon
              align="tr"
              color="color-light-black"
              text={intl('saenext.web-app.basic-info.DomainSetting.WebApplicationFirewallWaf')}
              tips={intl(
                'saenext.web-app.basic-info.DomainSetting.WebApplicationFirewallWafProvides',
              )}
            />
          }
          {...FORM_LAYOUT_LARGE}
        >
          <RadioGroup name="enableWAF" shape="button" size="medium" defaultValue={false}>
            <Radio value={true}>{intl('saenext.web-app.basic-info.DomainSetting.Enable')}</Radio>
            <Radio value={false}>{intl('saenext.web-app.basic-info.DomainSetting.Disable')}</Radio>
          </RadioGroup>

          {field.getValue('enableWAF') && (
            <Message type="notice" className="mt-s">
              {intl('saenext.web-app.basic-info.DomainSetting.WhenEnabledTheTrafficTo')}
              <a href={CachedData.confLink('help:waf:billing-description-v3')} target="_blank">
                {intl('saenext.web-app.basic-info.DomainSetting.BillingMethod')}
              </a>
            </Message>
          )}

          {!field.getValue('enableWAF') && data?.wafConfig?.enableWAF && (
            <Message type="warning" className="mt-s">
              {intl('saenext.web-app.basic-info.DomainSetting.YourDomainNameIsNot')}
            </Message>
          )}
        </Form.Item>
        <Form.Item
          label={intl('saenext.basic-info.CustomDomain.DomainSetting.RouteConfiguration')}
          {...FORM_LAYOUT_LARGE}
        >
          <RouteConfigTable
            field={field}
            name="routeConfig.routes"
            defaultValue={[
              {
                path: '/*',
                applicationName: '',
              }
            ]}
            namespaceID={namespaceID}
          />
        </Form.Item>
      </Form>
    </Loading>
  );
};

export default forwardRef(DomainSetting);
