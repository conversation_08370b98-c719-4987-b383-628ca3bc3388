import { intl } from '@ali/cnd';
import React, { useEffect, forwardRef, useImperativeHandle } from 'react';
import Form from '@ali/deep-form';
import { RadioField } from '@ali/deep';
import _ from 'lodash';
import { getParams } from '~/utils/global';
import services from '~/services';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 17 },
  labelCol: {
    span: 7,
    style: {
      width: 170,
      maxWidth: 170,
    },
  },
};

const AccessSetting = (props, ref) => {
  const { data, onSuccess, applicationID } = props;

  const applicationName = getParams('name');
  let form = React.createRef() as any;

  useEffect(() => {
    if (_.isEmpty(data)) return;
    const { httpTriggerConfig = {} } = data;
    const initData = {
      disableURLInternet: httpTriggerConfig?.disableURLInternet || false,
      authType: httpTriggerConfig?.authType || 'anonymous',
      httpTriggerConfig,
    };
    form.field.setValues(initData);
  }, [data]);

  useImperativeHandle(
    ref,
    () => ({
      submit,
    }),
    [form?.field?.getValues()],
  );

  const submit = () => {
    const promise = new Promise<void>(async (resolve, reject) => {
      form.field.validate((errors, values) => {
        if (errors) {
          reject(errors);
          return;
        }
        updateApp(values, resolve, reject);
      });
    });

    return promise;
  };

  const updateApp = async (values, resolve, reject) => {
    const { disableURLInternet, authType, httpTriggerConfig } = values;
    const content = {
      httpTriggerConfig: {
        ...httpTriggerConfig,
        disableURLInternet,
        authType,
      },
    };
    try {
      const result = await services.updateWebAttributes(applicationID, applicationName, content);

      if (result) {
        onSuccess();
        resolve(result);
      } else {
        reject();
      }
    } catch (err) {
      console.log(err);
      reject(err);
    }
  };

  return (
    <>
      <Form
        ref={(c) => {
          if (c) {
            form = c.getInstance();
          }
        }}
      >
        <RadioField
          required
          name="disableURLInternet"
          label={intl('saenext.web-app.basic-info.AccessSetting.EntranceTypeOfData')}
          {...fieldLayout}
          defaultValue={false}
          dataSource={[
            {
              value: false,
              text: (
                <span className="radio-item-120">
                  {intl('saenext.web-app.basic-info.AccessSetting.AllPublicAndPrivate')}
                </span>
              ),
            },
            {
              value: true,
              text: (
                <span className="radio-item-120">
                  {intl('saenext.web-app.basic-info.AccessSetting.Intranet')}
                </span>
              ),
            },
          ]}
          validation={[
            {
              type: 'required',
              message: intl('saenext.web-app.basic-info.AccessSetting.SelectEntryTypeOfData'),
            },
          ]}
        />

        <RadioField
          required
          name="authType"
          label={intl('saenext.web-app.basic-info.AccessSetting.CallAuthentication')}
          {...fieldLayout}
          defaultValue="anonymous"
          dataSource={[
            {
              value: 'anonymous',
              text: (
                <span className="radio-item-120">
                  {intl('saenext.web-app.basic-info.AccessSetting.NoAuthenticationRequired')}
                </span>
              ),
            },
            {
              value: 'function',
              text: (
                <span className="radio-item-120">
                  {intl('saenext.web-app.basic-info.AccessSetting.SignatureAuthentication')}
                </span>
              ),
            },
          ]}
          validation={[
            {
              type: 'required',
              message: intl('saenext.web-app.basic-info.AccessSetting.SelectCallAuthentication'),
            },
          ]}
        />
      </Form>
    </>
  );
};

export default forwardRef(AccessSetting);
