import { <PERSON><PERSON>, Button, Dialog, Message, intl } from '@ali/cnd';
import React from 'react';
import services from '~/services';
import { customeErrorHandleForDomain } from './CustomDomain/utils/customeErrorHandle';
import CachedData from '~/cache/common';

export const WAF_DISABLED_ICON = (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="918"
    width="18"
    height="18"
    data-spm-anchor-id="5176.mse-prod.0.i2.72e0142fLnZGKL"
  >
    <path
      d="M514.2 101.54L98.16 255.81l9.08 32.85c13.91 50.34 20.81 94.27 27.5 136.76 20.54 130.69 36.77 233.91 231.59 368.01 53.08 36.53 102.55 86.29 118.8 102.64l28.82 28.82 26.24-25.74c17.96-17.6 66.35-63.05 127.96-104.8C842.1 676.53 918.62 301.92 921.8 286.04l6.2-31.06-413.8-153.44zM792.73 467C744.26 600.12 686.78 692.07 626.5 732.9c-46.15 31.27-85.43 64.45-111.65 88.21-25.27-24.35-64.41-59.91-106.43-88.82C239.89 616.27 227.23 535.76 208.07 413.9c-5.29-33.65-11.14-70.86-20.82-111.94L514.2 180.71l327.84 121.57c-8.48 35.44-24.81 97.45-49.31 164.72z"
      fill="#808080"
      p-id="919"
    ></path>
    <path
      d="M512.24 289.05a211.932 211.932 0 0 0-14.32 0c-99.73 3.78-179.4 85.82-179.4 186.44s79.67 182.67 179.4 186.44a211.932 211.932 0 0 0 14.32 0c99.73-3.78 179.4-85.82 179.4-186.44s-79.67-182.67-179.4-186.44z m144.5 169.48h-67.46c-3.86-56.82-26.59-102.15-45.54-130.77 60.26 15.78 106.05 67.42 113 130.77zM505.11 332.09c16.66 21.71 45.37 67 50.16 126.44h-100.4c4.79-59.19 33.58-104.65 50.24-126.44z m50.17 160.36c-4.79 59.19-33.54 104.65-50.24 126.44-16.66-21.71-45.37-67-50.16-126.44h100.4zM466.4 327.76c-18.95 28.62-41.68 73.95-45.54 130.77H353.4c6.96-63.35 52.75-114.99 113-130.77z m-113 164.69h67.46c3.86 56.78 26.54 102.1 45.5 130.73-60.25-15.73-106-67.38-112.96-130.73z m190.39 130.73c18.95-28.62 41.64-73.95 45.5-130.73h67.46c-6.96 63.35-52.71 115-112.96 130.73z"
      fill="#808080"
      p-id="920"
      data-spm-anchor-id="5176.mse-prod.0.i4.72e0142fLnZGKL"
    ></path>
  </svg>
);

export const WAF_ENABLED_ICON = (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="918"
    width="18"
    height="18"
    data-spm-anchor-id="5176.mse-prod.0.i7.72e0142fLnZGKL"
  >
    <path
      d="M514.2 101.54L98.16 255.81l9.08 32.85c13.91 50.34 20.81 94.27 27.5 136.76 20.54 130.69 36.77 233.91 231.59 368.01 53.08 36.53 102.55 86.29 118.8 102.64l28.82 28.82 26.24-25.74c17.96-17.6 66.35-63.05 127.96-104.8C842.1 676.53 918.62 301.92 921.8 286.04l6.2-31.06-413.8-153.44zM792.73 467C744.26 600.12 686.78 692.07 626.5 732.9c-46.15 31.27-85.43 64.45-111.65 88.21-25.27-24.35-64.41-59.91-106.43-88.82C239.89 616.27 227.23 535.76 208.07 413.9c-5.29-33.65-11.14-70.86-20.82-111.94L514.2 180.71l327.84 121.57c-8.48 35.44-24.81 97.45-49.31 164.72z"
      fill="#06B624"
      p-id="919"
    ></path>
    <path
      d="M512.24 289.05a211.932 211.932 0 0 0-14.32 0c-99.73 3.78-179.4 85.82-179.4 186.44s79.67 182.67 179.4 186.44a211.932 211.932 0 0 0 14.32 0c99.73-3.78 179.4-85.82 179.4-186.44s-79.67-182.67-179.4-186.44z m144.5 169.48h-67.46c-3.86-56.82-26.59-102.15-45.54-130.77 60.26 15.78 106.05 67.42 113 130.77zM505.11 332.09c16.66 21.71 45.37 67 50.16 126.44h-100.4c4.79-59.19 33.58-104.65 50.24-126.44z m50.17 160.36c-4.79 59.19-33.54 104.65-50.24 126.44-16.66-21.71-45.37-67-50.16-126.44h100.4zM466.4 327.76c-18.95 28.62-41.68 73.95-45.54 130.77H353.4c6.96-63.35 52.75-114.99 113-130.77z m-113 164.69h67.46c3.86 56.78 26.54 102.1 45.5 130.73-60.25-15.73-106-67.38-112.96-130.73z m190.39 130.73c18.95-28.62 41.64-73.95 45.5-130.73h67.46c-6.96 63.35-52.71 115-112.96 130.73z"
      fill="#06B624"
      p-id="920"
    ></path>
  </svg>
);

const WafLabel = (props) => {
  const {
    record,
    namespaceId,
    refresh,
  } = props;
  const enabled = record?.webWAFConfig?.enableWAF;

  const {
    domainName
  } = record;

  const toggleWaf = async (value) => {
    const promise = new Promise(async (resolve, reject) => {
      const result = await services.updateCustomDomain({
        params: {
          DomainName: domainName,
          NamespaceId: namespaceId,
        },
        content: {
          WebWAFConfig: {
            EnableWAF: value
          }
        },
      },
      customeErrorHandleForDomain
    )

      if (result) {
        value ?
        Message.success(intl('waf.status.enabled.done.tips')) :
        Message.success(intl('waf.status.disabled.done.tips'));
        refresh();
        resolve(result);
      } else {
        reject(result);
      }
    });
    return promise;
  }

  return (
    <Balloon
      align={'r'}
      trigger={
        <div className="flex" style={{ maxWidth: 60, cursor: 'default' }}>
          {enabled ? WAF_ENABLED_ICON : WAF_DISABLED_ICON}
          <span className="ml-xs">
            {intl(enabled ? 'waf.status.enabled.label' : 'waf.status.disabled.label')}
          </span>
        </div>
      }
      closable={false}
    >
      <div>
        {intl.html('domain.wat.enable.tips.new', {
          href: CachedData.confLink('help:waf:billing-description-v3'),
        })}
      </div>

      <div className="mt-l">
        <Button
          type="primary"
          text
          onClick={() => {
            Dialog.confirm({
              title: intl(
                enabled ? 'waf.quick.disabled.button.label' : 'waf.quick.enable.button.label',
              ),
              messageProps: { type: enabled ? 'warning' : 'notice' },
              content: intl.html(
                enabled ? 'waf.quick.disabled.content' : 'waf.quick.enable.content',
              ),
              onOk: () => {
                return toggleWaf(!enabled);
              },
              okProps: { children: intl('button.ok'), warning: enabled },
              cancelProps: { children: intl('button.cancel') },
            });
          }}
        >
          {intl(enabled ? 'waf.quick.disabled.button.label' : 'waf.quick.enable.button.label')}
        </Button>
      </div>
    </Balloon>
  );
};

export default WafLabel;
