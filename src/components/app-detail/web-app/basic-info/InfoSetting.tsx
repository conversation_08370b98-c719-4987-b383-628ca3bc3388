import { intl } from '@ali/cnd';
import React, { useEffect, forwardRef, useImperativeHandle } from 'react';
import Form from '@ali/deep-form';
import NumberRangeField from '@ali/deep-number-range-field';
import _ from 'lodash';
import { getParams } from '~/utils/global';
import services from '~/services';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 17 },
  labelCol: { span: 7, style: { width: 170, maxWidth: 170 } },
};

const InfoSetting = (props, ref) => {
  const { data, onSuccess } = props;

  let form = React.createRef() as any;

  useEffect(() => {
    if (_.isEmpty(data)) return;
    const {
      scaleConfig: { minimumInstanceCount, maximumInstanceCount },
    } = data;
    const initData = {
      scaleConfig: {
        start: minimumInstanceCount,
        end: maximumInstanceCount,
      },
    };
    form.field.setValues(initData);
  }, [data]);

  useImperativeHandle(
    ref,
    () => ({
      submit,
    }),
    [form?.field?.getValues()],
  );

  const submit = () => {
    const promise = new Promise<void>(async (resolve, reject) => {
      form.field.validate((errors, values) => {
        if (errors) {
          reject(errors);
          return;
        }
        updateApp(values, resolve, reject);
      });
    });

    return promise;
  };

  const updateApp = async (values, resolve, reject) => {
    const { scaleConfig } = values;

    const { start, end } = scaleConfig;

    const content = {
      scaleConfig: {
        ...data.scaleConfig,
        minimumInstanceCount: start,
        maximumInstanceCount: end,
      },
    };
    try {
      // @ts-ignore
      const result = await services.updateAppVersions(
        getParams('name'),
        //@ts-ignore
        content,
      );

      if (result) {
        onSuccess();
        resolve(result);
      } else {
        reject();
      }
    } catch (err) {
      reject(err);
    }
  };

  return (
    <>
      <Form
        ref={(c) => {
          if (c) {
            form = c.getInstance();
          }
        }}
      >
        <NumberRangeField
          required
          min={0}
          max={50}
          label={intl('saenext.web-app.basic-info.InfoSetting.AutomaticScalingOfInstances')}
          name="scaleConfig"
          {...fieldLayout}
          validation={[
            {
              type: 'required',
              message: intl('saenext.web-app.basic-info.InfoSetting.SelectARangeOfAuto'),
            },
          ]}
        />
      </Form>
    </>
  );
};

export default forwardRef(InfoSetting);
