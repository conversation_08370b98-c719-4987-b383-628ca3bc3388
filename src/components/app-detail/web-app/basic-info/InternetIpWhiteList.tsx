import { intl } from '@ali/cnd';
import React, { useRef } from 'react';
import SlideButton from '~/components/shared/SlideButton';
import { Icon, Truncate } from '@ali/cnd';
import IpWhiteListSetting from './IpWhiteListSetting';
import _ from 'lodash';

const InternetIpWhiteList = (props) => {
  const { appData = {}, fetchConfig, applicationID } = props;

  const aclEntries = _.get(appData, 'httpTriggerConfig.aclConfig.aclEntries') || [];

  const renderIps = () => {
    const ipArr = _.map(aclEntries, 'entry');
    const ipStr = ipArr.join(', ');
    return ipStr ? (
      <Truncate
        type="width"
        threshold={190}
        // @ts-ignore
        align="top"
      >
        {ipStr}
      </Truncate>
    ) : (
      '-'
    );
  };

  const ref = useRef(null);

  return (
    <>
      {intl('saenext.web-app.basic-info.InternetIpWhiteList.PublicIpAddressWhitelist')}

      <span className="ml-s">{renderIps()}</span>

      <SlideButton
        buttonText={<Icon type="edit" size="small" />}
        slideTitle={intl('saenext.web-app.basic-info.InternetIpWhiteList.SetThePublicIpAddress')}
        slideSize={900}
        slideContent={
          <IpWhiteListSetting
            ref={ref}
            data={aclEntries}
            applicationID={applicationID}
            httpTriggerConfig={appData.httpTriggerConfig}
            onSuccess={fetchConfig}
          />
        }
        autoClose={false}
        linkButton
        submit={() => {
          if (ref.current) {
            return ref.current.submit();
          }
        }}
      />
    </>
  );
};

export default InternetIpWhiteList;
