import React, { useState } from 'react';
import { Dialog, Message, intl } from '@ali/cnd';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import { Form } from '@ali/deep';
import NumberRangeField from '@ali/deep-number-range-field';
import services from '~/services';
import { get } from 'lodash';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 18 },
  labelCol: { span: 6 },
};

const ScaleConfigEditButton = (props) => {
  const {
    appData: { applicationID, applicationName, scaleConfig = {} } = {} as any,
    onSuccess,
    children,
  } = props;
  const _alwaysAllocateCPU = get(scaleConfig, 'alwaysAllocateCPU', false);

  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [limitInstances, setLimitInstances] = useState(50);

  const field = React.useRef(null);

  React.useEffect(() => {
    if (visible) {
      getLimitInstances();
      getInitValues();
    }
  }, [visible]);

  const toogleDialog = () => {
    setVisible(!visible);
  };

  const getLimitInstances = async () => {
    const res = await services.GetAccountSettings();
    const { maximumInstancesPerApplication = 50 } = res;
    setLimitInstances(maximumInstancesPerApplication);
  };

  const getInitValues = () => {
    const { minimumInstanceCount, maximumInstanceCount } = scaleConfig;
    field.current?.setValue({
      scaleConfig: {
        start: minimumInstanceCount,
        end: maximumInstanceCount,
      },
    });
  };

  const onOk = () => {
    field.current?.validate((errors, values) => {
      if (errors) {
        return;
      }
      const { scaleConfig } = values;
      const { start: minimumInstanceCount, end: maximumInstanceCount } = scaleConfig;
      const params = {
        minimumInstanceCount,
        maximumInstanceCount,
        alwaysAllocateCPU: _alwaysAllocateCPU,
      };
      submitScale(params);
    });
  };

  const submitScale = async (params) => {
    setLoading(true);
    const res = await services.updateApplicationScale(applicationID, applicationName, params);
    if (res) {
      Message.success(
        intl('saenext.web-app.basic-info.ScaleConfigEditButton.TheRangeOfInstancesHas'),
      );
      setLoading(false);
      onSuccess?.();
      toogleDialog();
    } else {
      setLoading(false);
    }
  };

  return (
    <>
      <AddPropsWrap onClick={toogleDialog}>{children}</AddPropsWrap>
      <Dialog
        size="medium"
        title={intl('saenext.web-app.basic-info.ScaleConfigEditButton.ChangeTheRangeOfInstances')}
        visible={visible}
        okProps={{ loading }}
        onOk={onOk}
        onCancel={toogleDialog}
        onClose={toogleDialog}
      >
        <Form
          ref={(c) => {
            if (c) {
              field.current = c.getInstance();
            }
          }}
        >
          <NumberRangeField
            min={0}
            label={intl(
              'saenext.version-list.TrafficConfPanel.TrafficField.AutomaticScalingOfInstances',
            )}
            {...fieldLayout}
            name="scaleConfig"
            validation={[
              {
                type: 'required',
                message: intl(
                  'saenext.version-list.TrafficConfPanel.TrafficField.SelectARangeOfAuto',
                ),
              },
              {
                type: 'customValidate',
                param: (value) => {
                  const { start, end } = value;
                  if (start === undefined || !end) {
                    return intl(
                      'saenext.version-list.TrafficConfPanel.TrafficField.SelectARangeOfAuto',
                    );
                  }
                  if (end > limitInstances) {
                    return intl(
                      'saenext.version-list.TrafficConfPanel.TrafficField.TheMaximumNumberOfSingle',
                      { thisStateLimitInstances: limitInstances },
                    );
                  }
                  return true;
                },
              },
            ]}
          />
        </Form>
      </Dialog>
    </>
  );
};

export default ScaleConfigEditButton;
