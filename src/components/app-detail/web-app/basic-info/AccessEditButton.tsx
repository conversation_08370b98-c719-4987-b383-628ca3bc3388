import { intl } from '@ali/cnd';
import React, { memo, useRef } from 'react';
import SlideButton from '~/components/shared/SlideButton';
import AccessSetting from './AccessSetting';

const AccessEditButton = ({ onSuccess, data, applicationID }) => {
  const ref = useRef(null);

  return (
    <SlideButton
      buttonText={intl('saenext.web-app.basic-info.AccessEditButton.Edit')}
      slideTitle={intl('saenext.web-app.basic-info.AccessEditButton.EditAccessSettings')}
      slideSize={700}
      slideContent={
        <AccessSetting ref={ref} data={data} applicationID={applicationID} onSuccess={onSuccess} />
      }
      autoClose={false}
      linkButton
      submit={() => {
        if (ref.current) {
          return ref.current.submit();
        }
      }}
    />
  );
};

export default memo(AccessEditButton);
