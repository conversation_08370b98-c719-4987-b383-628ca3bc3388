import React, { useEffect, useState, useContext } from 'react';
// docs: https://unpkg.alibaba-inc.com/@ali/cnd-stat-card@0.1.5/build/index.html
import { StatCard, intl, ConsoleContext, Icon } from '@ali/cnd';
import services from '~/services';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import { getTimes } from '~/components/shared/TimeRangeSelector';
import { isEmpty, get } from 'lodash';
import FeatureContext from '~/utils/featureContext';
import { isInteger } from '~/utils/global';

type Props = {
  applicationID: string;
  applicationName: string;
};

type Statics = {
  cu: number | string;
  invocations: number | string;
  cpuQuota: number | string;
  memoryQuota: number | string;
  internetOut: number | string;
};

const timeInitValue = 'this_month';
const Default = '-';

const WebAppStatics = (props: Props) => {
  const { applicationID, applicationName } = props;
  const { feature } = useContext(FeatureContext);
  const isAllocateIdle = get(feature, 'idle', false);
  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const [loading, setLoading] = useState(false);
  const [webApp, setWebApp] = useState<Statics>({
    cu: Default,
    invocations: Default,
    cpuQuota: Default,
    memoryQuota: Default,
    internetOut: Default,
  });
  const refreshCount = React.useRef(1);
  const [refreshIndex, setRefreshIndex] = useState(1);

  useEffect(() => {
    getWebAppStatics();
  }, [refreshIndex]);

  const getWebAppStatics = async () => {
    setLoading(true);
    const [start, end] = getTimes(timeInitValue);
    const data = await services.getWebAppStatics(
      {
        regionId,
        applicationID,
        applicationName,
        startTime: start,
        endTime: end,
      },
      (error, data, callback) => {
        // 如果网络超时 重新请求一次，但最多请求2次
        if (error?.code === 'ServiceUnavailable' && refreshCount.current < 2) {
          refreshCount.current += 1;
          setRefreshIndex(Date.now());
          return;
        } else if (error.code === 'ApplicationNotFound') {
          setLoading(false);
          return {};
        }
        setLoading(false);
        callback && callback();
      },
    );
    setLoading(false);
    if (isEmpty(data)) return;
    const { statics = [] } = data || {};
    const _statics = statics[0] || {};
    // cpu core*s = core*ms / 1000
    const _activeCpuUsage = _statics.activeCPUUsage || 0;
    const _idleCPUUsage = _statics.idleCPUUsage || 0;
    const allocateCpu = Math.ceil(_activeCpuUsage / 1000);
    const allocateIdleCpu = Math.ceil(_idleCPUUsage / 1000);
    const _cpuQuota = isAllocateIdle ? allocateCpu + allocateIdleCpu : allocateCpu;

    // mem 本月Memory资源使用量 原始单位MB * ms (GiB * s) = / 1000 / 1024
    const _memoryUsage = _statics.memoryUsage || 0;
    const _memoryQuota = Math.ceil(_memoryUsage / 1000 / 1024);

    // 出口流量 原始单位byte 转化GB / 1024 / 1024 / 1024
    const _internetOutUsage =
      _statics.invokeInternetOut + _statics.invokeCDNOut + _statics.instanceTrafficOut || 0;
    const _internetOut = _internetOutUsage / 1024 / 1024 / 1024;

    // 请求次数
    const _invocations = _statics.invocations || 0;

    //  app cu
    // 活跃cpu使用量（核*秒）* 1 + 闲置cpu使用量（核*秒）* 0.2 + 内存（GiB*秒）* 0.25 + Web请求次数(百万次) * 2332.8 + Web公网出口总量(GB) * 15552
    // 活跃cpu使用量后端返回 核*分
    // 闲置cpu使用量后端返回 核*分
    // 内存后端返回 GiB*分
    // Web请求次数后端返回 次
    // Web公网出口总量后端返回 bytes
    const _cu =
      allocateCpu * 1 +
      allocateIdleCpu * 0.2 +
      _memoryQuota * 0.25 +
      (_invocations / 1000000) * 2332.8 +
      _internetOut * 15552;

    const _webApp = {
      cu: _cu,
      invocations: intl.number(_invocations),
      cpuQuota: intl.number(_cpuQuota),
      memoryQuota: intl.number(_memoryQuota),
      internetOut: intl.number(_internetOut, {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }),
    };
    setWebApp(_webApp);
    if (!isEmpty(_statics)) {
      refreshCount.current = 1;
    }
  };

  const toCu = (value, fixed = 2) => {
    const cu = { value, unit: 'CU' };

    if (value >= 100000000) {
      cu.unit = intl('saenext.web-app.basic-info.WebAppStatics.BillionCu');
      const _value = value / 100000000;
      cu.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
      return cu;
    }
    if (value >= 10000) {
      cu.unit = intl('saenext.web-app.basic-info.WebAppStatics.Wancu');
      const _value = value / 10000;
      cu.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
      return cu;
    }

    return cu;
  };

  return (
    <>
      <StatCard
        title={intl('saenext.web-app.basic-info.WebAppStatics.MeteringData')}
        extra={
          <div className="flex">
            <span className="gray-text ml-s">
              {intl('saenext.web-app.basic-info.WebAppStatics.TheCumulativeUsageDataOf')}
            </span>
            {loading ? (
              <Icon type="loading" size="small" />
            ) : (
              <Icon
                type="refresh"
                size="small"
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  refreshCount.current = 1;
                  setRefreshIndex(Date.now());
                }}
              />
            )}
          </div>
        }
        items={[
          {
            // @ts-ignore
            title: (
              <TextWithBalloon
                text={intl('saenext.web-app.basic-info.WebAppStatics.CuUsageThisMonth')}
                tips={intl('saenext.web-app.basic-info.WebAppStatics.TheTotalAmountOfCu')}
              />
            ),

            count: [toCu(webApp.cu).value, toCu(webApp.cu).unit],
          },
          {
            // @ts-ignore
            title: (
              <TextWithBalloon
                text={intl('saenext.web-app.basic-info.WebAppStatics.CpuResourceUsageThisMonth')}
                tips={intl('saenext.web-app.basic-info.WebAppStatics.TheTotalCpuResourcesUsed')}
              />
            ),

            count: [webApp.cpuQuota, intl('saenext.web-app.basic-info.WebAppStatics.CoreSeconds')],
          },
          {
            // @ts-ignore
            title: (
              <TextWithBalloon
                text={intl('saenext.web-app.basic-info.WebAppStatics.MemoryResourceUsageThisMonth')}
                tips={intl('saenext.web-app.basic-info.WebAppStatics.TheTotalMemoryResourcesUsed')}
              />
            ),

            count: [
              webApp.memoryQuota,
              intl('saenext.web-app.basic-info.WebAppStatics.GibSeconds'),
            ],
          },
          {
            // @ts-ignore
            title: (
              <TextWithBalloon
                text={intl(
                  'saenext.web-app.basic-info.WebAppStatics.PublicNetworkExportTrafficThis',
                )}
                tips={intl(
                  'saenext.web-app.basic-info.WebAppStatics.TheCumulativePublicNetworkExport',
                )}
              />
            ),

            count: [webApp.internetOut, 'GB'],
          },
          {
            // @ts-ignore
            title: (
              <TextWithBalloon
                text={intl('saenext.web-app.basic-info.WebAppStatics.NumberOfRequestsThisMonth')}
                tips={intl(
                  'saenext.web-app.basic-info.WebAppStatics.TheCumulativeNumberOfRequests',
                )}
              />
            ),

            count: [webApp.invocations, intl('saenext.web-app.basic-info.WebAppStatics.Times')],
          },
        ]}
      />
    </>
  );
};

export default WebAppStatics;
