import React, { HTMLAttributes, useEffect, useRef, useState, useContext } from 'react';
import {
  Button,
  Card,
  Copy,
  DateTime,
  Description,
  Icon,
  intl,
  StatusIndicator,
  Switch,
  Dialog,
} from '@ali/cnd';
import { getParams } from '~/utils/global';
import { getTimes } from '~/components/shared/TimeRangeSelector';
import services from '~/services';
import ScaleConfigEditButton from './ScaleConfigEditButton';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import { get, isEqual, isEmpty } from 'lodash';
import FeatureContext from '~/utils/featureContext';
// import AllocateCpuEditButton from './AllocateCpuEditButton';
import CachedData from '~/cache/common';

interface IAppBaseInfo extends HTMLAttributes<HTMLDivElement> {
  appData: object;
  refreshWebApp: Function;
  applicationID: string;
}

const AppBaseInfo = (props: IAppBaseInfo) => {
  const { className, appData, refreshWebApp, applicationID } = props;

  const CardProps = {
    showTitleBullet: false,
    showHeadDivider: false,
    contentHeight: 'auto',
  };
  const { feature } = useContext(FeatureContext);
  const isAllocateIdle = get(feature, 'idle', false);
  const _alwaysAllocateCPU = get(appData, 'scaleConfig.alwaysAllocateCPU', false);
  const timerRef = useRef(null);
  const state = getParams('state');
  const applicationName = getParams('name');
  const [isWarmup, setIsWarmup] = useState(false);
  // 应用启动状态 true 表示成功
  const [startState, setStartState] = useState(true);
  const [alwaysAllocateCPU, setAlwaysAllocateCPU] = useState(_alwaysAllocateCPU);

  useEffect(() => {
    initInstanceState();
    return () => {
      clearTimer();
    };
  }, [appData]);

  const initInstanceState = () => {
    if (state && state === 'initialize') {
      setIsWarmup(true);
      setStartState(true);
      clearTimer();
      timerRef.current = setInterval(() => {
        getAppInstances();
      }, 1000 * 1.5);
    }
  };

  const clearTimer = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const getAppInstances = async () => {
    if (!state || state !== 'initialize') return;
    // 版本列表 默认获取15分钟之内的所有运行中实例
    const [startTime, endTime] = getTimes('last_15_minutes');
    const data = await services.getAppVersionInstances({
      applicationID,
      applicationName,
      startTime,
      endTime,
    });
    const { instances = [], currentError } = data;
    if (currentError) {
      setStartState(false);
      setIsWarmup(false);
      clearTimer();
      return;
    }
    // @ts-ignore
    if (instances.length >= appData?.scaleConfig?.minimumInstanceCount) {
      clearTimer();
      setStartState(true);
      setIsWarmup(false);
    }
  };

  const handleAllocateChange = async () => {
    // alwaysAllocateCPU 为 false 代表开启闲置，为 true 代表关闭
    const title = alwaysAllocateCPU
      ? intl('saenext.web-app.basic-info.AppBaseInfo.EnableIdleMode')
      : intl('saenext.web-app.basic-info.AppBaseInfo.DisableIdleMode');
    const content = alwaysAllocateCPU
      ? intl('saenext.web-app.basic-info.AppBaseInfo.AfterTheIdleModeIs')
      : intl('saenext.web-app.basic-info.AppBaseInfo.AfterTheIdleModeIs.1');

    const params = {
      // @ts-ignore
      minimumInstanceCount: appData?.scaleConfig?.minimumInstanceCount,
      // @ts-ignore
      maximumInstanceCount: appData?.scaleConfig?.maximumInstanceCount,
      alwaysAllocateCPU: !alwaysAllocateCPU,
    };
    Dialog.alert({
      title: title,
      content: <p style={{ width: 500 }}>{content}</p>,
      okProps: { children: intl('saenext.web-app.basic-info.AppBaseInfo.Confirm') },
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res = await services.updateApplicationScale(applicationID, applicationName, params);
          if (isEmpty(res)) reject();
          const _alwaysAllocateCPU = get(res, 'alwaysAllocateCPU', false);
          setAlwaysAllocateCPU(_alwaysAllocateCPU);
          resolve(true);
          refreshWebApp && refreshWebApp();
        });
      },
    });
  };

  return (
    <Card
      title={intl('saenext.web-app.basic-info.AppBaseInfo.ApplicationInformation')}
      {...CardProps}
      className={className}
    >
      <Description
        dataSource={appData}
        items={[
          {
            dataIndex: 'applicationName',
            label: intl('saenext.web-app.basic-info.AppBaseInfo.ApplicationName'),
            render: (value) => <Copy text={value}>{value}</Copy>,
          },
          {
            dataIndex: 'description',
            label: intl('saenext.web-app.basic-info.AppBaseInfo.ApplicationDescription'),
            render: (value) =>
              value ? (
                <div style={{ wordBreak: 'break-all' }}>
                  {value}
                  <Copy showIcon text={value} />
                </div>
              ) : (
                <span>-</span>
              ),
          },
          {
            dataIndex: 'applicationID',
            label: intl('saenext.web-app.basic-info.AppBaseInfo.ApplicationId'),
            render: (value) => (value ? <Copy text={value}>{value}</Copy> : <span>-</span>),
          },
          {
            dataIndex: 'createdTime',
            label: intl('instance.prop.create_time.label'),
            render: (value) => <DateTime value={value} />,
          },
          {
            dataIndex: 'lastModifiedTime',
            label: intl('saenext.web-app.basic-info.AppBaseInfo.LastModifiedTime'),
            render: (value) => <DateTime value={value} />,
          },
          {
            dataIndex: 'namespaceName',
            label: intl('saenext.web-app.basic-info.AppBaseInfo.Namespace'),
            render: (value) => (value ? <Copy text={value}>{value}</Copy> : <span>-</span>),
          },
          {
            dataIndex: 'scaleConfig',
            label: intl('saenext.web-app.basic-info.AppBaseInfo.NumberOfInstances'),
            render: (value) => {
              if (isWarmup && startState) {
                return (
                  <StatusIndicator type="loading" shape="dot">
                    {intl('saenext.web-app.basic-info.AppBaseInfo.Starting')}
                  </StatusIndicator>
                );
              }
              if (!isWarmup && !startState) {
                return (
                  <StatusIndicator type="error" shape="dot">
                    {intl('saenext.web-app.basic-info.AppBaseInfo.FailedToStart')}
                  </StatusIndicator>
                );
              }
              return (
                <>
                  {value?.minimumInstanceCount}
                  <span style={{ margin: '0 4px' }}>-</span>
                  {value?.maximumInstanceCount}

                  <ScaleConfigEditButton appData={appData} onSuccess={refreshWebApp}>
                    <Button text type="primary" className="ml-xs">
                      <Icon type="edit" />
                    </Button>
                  </ScaleConfigEditButton>
                </>
              );
            },
          },
          {
            dataIndex: 'alwaysAllocateCPU',
            visible: isAllocateIdle,
            label: (
              <TextWithBalloon
                align="r"
                text={intl('saenext.web-app.basic-info.AppBaseInfo.IdleMode')}
                tips={
                  <>
                    {intl('saenext.web-app.basic-info.AppBaseInfo.WhenIdleModeIsEnabled')}
                    <a
                      href={CachedData.confLink('help:sae:charge-by-volume-2-0')}
                      target="_blank"
                    >
                      {intl('saenext.web-app.basic-info.AppBaseInfo.ProductBillingDocument')}
                    </a>
                    {intl("saenext.common.full-stop")}
                  </>
                }
              />
            ),

            render: () => {
              return (
                <Switch
                  checked={!alwaysAllocateCPU}
                  disabled={isEqual(get(appData, 'scaleConfig.minimumInstanceCount'), 0)}
                  onChange={handleAllocateChange}
                />
              );
            },
          },
        ]}
      />
    </Card>
  );
};

export default AppBaseInfo;
