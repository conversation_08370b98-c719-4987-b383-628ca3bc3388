import React, { useState } from 'react';
import { getParams } from '~/utils/global';
import AppActionBar from './AppActionBar';
import WebAppStatics from './WebAppStatics';
import AppBaseInfo from './AppBaseInfo';
import HttpGatewaySetting from './HttpGatewaySetting';
import VpcNetworkSetting from './VpcNetworkSetting';
import CreatingStepCard from './CreatingStepCard';
import StaticsIndicator from '~/components/monitor-indicator/WebStaticsIndicator';


const AppDetail = (props) => {
  const {
    match: {
      params: { regionId, id },
    },
    history,
    appData,
    isLatestMonitor,
    setRefreshIndex,
  } = props;
  const applicationID = id;
  const applicationName = getParams('name');
  const showProcess = getParams('state') === 'initialize';
  const [deployStatus, setDeployStatus] = useState({ showProcess, success: false });

  return (
    <>
      <AppActionBar
        regionId={regionId}
        history={history}
        namespaceId={appData?.namespaceID}
        applicationID={applicationID}
        applicationName={applicationName}
        data={appData}
        isLatestMonitor={isLatestMonitor}
        run={() => setRefreshIndex(Date.now())}
      />
      {
        showProcess && 
         <CreatingStepCard 
            {...props}
            appData={appData}
            // 启动成功 / 构建成功需要刷新应用
            refreshWebApp={() => setRefreshIndex(Date.now())}
            setDeployStatus={(val) => setDeployStatus({ ...deployStatus, ...val })}
         />
      }
      <WebAppStatics 
        applicationID={applicationID}
        applicationName={applicationName}
      />
      <StaticsIndicator
        applicationID={applicationID}
        urlInternet={appData?.urlInternet}
      />
      <AppBaseInfo
        appData={appData}
        applicationID={applicationID}
        refreshWebApp={() => setRefreshIndex(Date.now())}
        className="next-extra-card mt"
      />
      <HttpGatewaySetting
        appData={appData}
        applicationID={applicationID}
        deployStatus={deployStatus}
        fetchConfig={() => setRefreshIndex(Date.now())}
        className="next-extra-card mt"
      />
      <VpcNetworkSetting
        appData={appData}
        applicationID={applicationID}
        className="next-extra-card mt"
        refreshWebApp={() => setRefreshIndex(Date.now())}
      />
    </>
  );
};

export default AppDetail;
