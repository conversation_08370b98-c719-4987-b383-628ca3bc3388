import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { useRoaApi, Button, Icon, Loading, Select, Message, Actions, Dialog } from '@ali/cnd';
import { getParams, unshiftZero } from '~/utils/global';
import { LogEditor } from '@ali/cnd';
import _ from 'lodash';
import services from '~/services';
import TimeRangeSelector, { getTimes } from '~/components/shared/TimeRangeSelector';
import InstanceStatus from '../../../version-detail/instance-list/InstanceStatus';
import { AES_CONSTANT, trackOpt } from '~/tracker';

const { LinkButton } = Actions;
const OPENSLS_CONST = 'OPENSLS_CONST';
// const DEFAULT_ALL_VERSION = { label: '所有版本', value: '' };
// const DEFAULT_ALL_INSTANCE = { label: '所有实例', value: '' };

const LogControl = (props) => {
  const {
    match: {
      params: { regionId, id: applicationID },
    },
    appData,
  } = props;
  const _instanceId = getParams('instance') || '';
  const timeInitValue = 'last_15_minutes';
  const [start, end] = getTimes(timeInitValue);
  const [startTime, setStartTime] = useState(start);
  const [endTime, setEndTime] = useState(end);
  const [versionsData, setVersionsData] = useState([]);
  const [versionId, setVersionId] = useState();
  const [instanceId, setInstanceId] = useState(_instanceId);
  const [instanceStatus, setInstanceStatus] = useState('');
  const [instancesData, setInstancesData] = useState([]);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpensls, setIsOpensls] = useState(true);
  const cacheOpensls = useRef<string | boolean>(OPENSLS_CONST);
  const [value, setVaule] = useState();
  const applicationName = getParams('name');
  const child = useRef(null);
  const [selectedTimeKey, updateSelectedTimeKey] = useState(timeInitValue);

  const params = {
    applicationID,
    applicationName,
  };
  const content = {
    applicationID,
    applicationName,
    startTime,
    endTime,
    versionId,
    instanceId,
  };

  const { run } = useRoaApi(
    'serverless',
    'GetWebApplicationLogs',
    {
      params,
      content,
    },
    {
      manual: true,
      disableErrorPrompt: true,
    },
  );

  useEffect(() => {
    if (!refreshIndex) return;
    initApplicationLog();
  }, [refreshIndex]);

  const initApplicationLog = async () => {
    setIsLoading(true);
    if (typeof cacheOpensls.current === 'string') {
      const { logConfig = { logstore: '', project: '' } } = appData;
      const { logstore, project } = logConfig;
      cacheOpensls.current = !!(logstore && project);
      const _isOpensls = cacheOpensls.current;
      setIsOpensls(_isOpensls);
    }
    const _versionsData = await getApplicationVersions();
    const version = _versionsData[0];
    const _versionId = version.value;
    setVersionId(_versionId);
    const _instancesData = await getAppVersionInstances(_versionId);
    const instance = _instancesData[0];
    if (instance) {
      const _instanceId = instance.value;
      const _instanceStatus = instance.status;
      setInstanceId(_instanceId);
      setInstanceStatus(_instanceStatus);
      getApplicationLogData({
        params,
        content: {
          ...content,
          versionId: _versionId,
          instanceId: _instanceId,
        },
      });
    }
    setIsLoading(false);
  };

  const getApplicationVersions = async () => {
    const data = await services.getListAppVersions({
      applicationID,
      applicationName,
      limit: 100,
    });
    const _versionsData = _.map(data.versions, (version) => ({
      value: version.versionId,
      label: unshiftZero(version.versionId),
    }));
    setVersionsData(_versionsData);
    return _versionsData;
  };

  const getAppVersionInstances = async (_versionId?: string) => {
    const data = await services.getAppVersionInstances({
      applicationID,
      applicationName,
      startTime,
      endTime,
      qualifier: _versionId || versionId,
    });
    const { instances = [] } = data;
    const _instancesData = _.map(instances, (instance) => ({
      value: instance.instanceId,
      status: instance.status,
      label: (
        <>
          <InstanceStatus value={instance.status} />
          <span style={{ marginLeft: 10 }}>{instance.instanceId}</span>
        </>
      ),
    }));
    // @ts-ignore
    setInstancesData(_instancesData);
    return _instancesData;
  };

  const getApplicationLogData = async (params) => {
    const data = await run(params);

    if (data instanceof Error) {
      onError(data);
      return;
    }

    const logData = _.map(data?.logEntrys, (val) => val.message.replace(/\/r$/, ''));
    const _value = logData.join('');
    // @ts-ignore
    setVaule(_value);
    if (_value) {
      trackOpt({
        behavior: AES_CONSTANT.OPT_BEHAVIOR_TYPE.LOG,
        stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
      });
    }
  };

  const onLogTimeChange = (value) => {
    const { start, end, key } = value;
    setEndTime(end);
    setStartTime(start);
    updateSelectedTimeKey(key);
    setRefreshIndex(Date.now());
  };

  const onVersionChange = async (_versionId) => {
    setIsLoading(true);
    setVersionId(_versionId);
    // @ts-ignore
    setVaule('');
    setInstanceId('');
    const _instancesData = await getAppVersionInstances(_versionId);
    const instance = _instancesData[0];
    if (instance) {
      const _instanceId = instance.value;
      const _instanceStatus = instance.status;
      setInstanceId(_instanceId);
      setInstanceStatus(_instanceStatus);
      await getApplicationLogData({
        params,
        content: { ...content, versionId: _versionId, instanceId: _instanceId },
      });
    }
    setIsLoading(false);
  };

  const onInstanceChange = async (_instanceId, __, recored) => {
    setIsLoading(true);
    const { status } = recored;
    setInstanceId(_instanceId);
    setInstanceStatus(status);
    // @ts-ignore
    setVaule('');
    await getApplicationLogData({
      params,
      content: { ...content, instanceId: _instanceId },
    });
    setIsLoading(false);
  };

  const onInstanceShell = () => {
    if (!instanceId) return;
    const uri = `/${regionId}/monitor/${applicationID}/${versionId}/${instanceId}?name=${applicationName}`;
    window.open(uri, '_blank');
  };

  const createLogIndex = async () => {
    const promise = new Promise(async (resolve, reject) => {
      try {
        const appData = await services.getAppVersionConfig({
          applicationID,
          applicationName,
        });
        const {
          logConfig: { project, logstore },
        } = appData || {};
        const result = await services.CreateWebLogIndex({
          params: {
            applicationID,
            applicationName,
          },
          content: {
            project,
            logstore,
          },
        });
        if (result) {
          Dialog.confirm({
            title: intl('saenext.web-app.log-control.FixedSuccessfully'),
            className: 'fc-dialog-body',
            content: (
              <div className="mt-10" style={{ lineHeight: '22px' }}>
                {intl('saenext.web-app.log-control.CongratulationsYourConfigurationHasBeen')}
                <br />
                <span className="color-orange">
                  {intl('saenext.web-app.log-control.NoteTheRepairOperationTakes')}
                </span>
              </div>
            ),

            okProps: { children: intl('saenext.web-app.log-control.Confirm') },
            footerActions: ['ok'],
          });
          resolve(result);
        } else {
          reject();
        }
      } catch (err) {
        console.log(err);
        reject(err);
      }
    });
    return promise;
  };

  const onError = (error) => {
    // @ts-ignore
    if (error.code === 'IndexConfigNotExist' || error.code === 'IndexForInstanceMetricsNotExists') {
      Dialog.alert({
        title: intl('saenext.web-app.log-control.QueryFailed'),
        content: (
          <>
            <p>{intl('saenext.web-app.log-control.TheIndexInTheLogstore')}</p>
            <p className="color-orange">{intl('saenext.web-app.log-control.YouCanClickTheFix')}</p>
          </>
        ),

        onOk: createLogIndex,
        okProps: { children: intl('saenext.web-app.log-control.FixNow') },
      });
    }
  };

  const refreshLog = () => {
    const [start, end] = getTimes(selectedTimeKey);
    if (isOpensls) {
      child?.current.onChild([start, end]);
    }
    setEndTime(end);
    setStartTime(start);
    setRefreshIndex(Date.now());
  };

  return (
    <>
      {!isOpensls ? (
        <Message type="notice" className="mb-s">
          {intl('saenext.web-app.log-control.AMaximumOfLogsAt')}

          <LinkButton className="ml-s mr-s" disabled={!instanceId} onClick={onInstanceShell}>
            Webshell
          </LinkButton>
          {intl('saenext.web-app.log-control.ViewLogsOnline')}
        </Message>
      ) : null}

      {instanceStatus === 'Idle' ? (
        <Message type="warning" className="mb-s">
          {intl('saenext.web-app.log-control.TheInstanceDoesNotProcess')}
        </Message>
      ) : null}

      <div className="log-panel">
        <div style={{ flexWrap: 'wrap' }} className="flex full-width monitor-range">
          {isOpensls ? (
            <TimeRangeSelector
              onRef={child}
              defaultTime={false}
              timeInitValue={timeInitValue}
              periodInitValue={60}
              onTimeChanged={onLogTimeChange}
            />
          ) : null}

          <Select
            label={intl('saenext.web-app.log-control.Version')}
            className="mr-s mb-s"
            style={{ minWidth: 400 }}
            value={versionId}
            dataSource={versionsData}
            onChange={onVersionChange}
          />

          <Select
            label={intl('saenext.web-app.log-control.VersionInstance')}
            className="mb-s"
            style={{ minWidth: 400 }}
            value={instanceId}
            dataSource={instancesData}
            onChange={onInstanceChange}
          />
        </div>
        <Button className="ml-l" onClick={refreshLog}>
          <Icon type={'refresh'} />
        </Button>
      </div>

      <Loading
        className="app-log mt-s"
        visible={isLoading}
        style={{
          width: '100%',
          overflow: 'auto',
          height: `calc(100% - ${isOpensls ? 60 : 80}px)`,
        }}
      >
        <LogEditor value={value} />
      </Loading>
    </>
  );
};

export default LogControl;
