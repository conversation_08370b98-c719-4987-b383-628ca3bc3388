import { intl } from '@ali/cnd';
import React, { useRef, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  SlidePanel,
  Loading,
  Message,
  CndTable,
  Icon,
  Grid,
  Tab,
  Button,
  Truncate,
  DataFields,
} from '@ali/cnd';
import _ from 'lodash';
import { getRepoFullUrl, unshiftZero, getDurationString } from '~/utils/global';
import services from '~/services';
import moment from 'moment';
import { PIPELINE_STATUS } from '~/constants/application';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import TextSnippet from '~/components/shared/TextSnippet';
import { LogEditor } from '@ali/cnd';
import { TabItem, StepStatus } from './constants';
import { C_TRIGGER } from '~/components/shared/DeploySelectorField/constant';
import BuildStatus from './BuildStatus';

const { Row, Col } = Grid;
interface Props {
  taskId: string;
  applicationId: string;
  applicationName: string;
  setRefreshIndex: (index: number) => void;
}

export default forwardRef((props: Props, ref) => {
  const { taskId, applicationId, applicationName, setRefreshIndex } = props;
  const [visible, setVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [buildData, setBuildData] = useState({
    VersionId: 0,
    Status: 'RUNNING',
    Steps: [
      {
        Id: 'CreateEnv',
        Name: intl('saenext.web-app.build-record.RepoistoryPanel.EnvironmentCreation'),
        Result: intl('saenext.web-app.build-record.RepoistoryPanel.Running'),
        Status: 'RUNNING',
      },
      {
        Id: 'GitClone',
        Name: intl('saenext.web-app.build-record.RepoistoryPanel.CodePull'),
        Result: intl('saenext.web-app.build-record.RepoistoryPanel.Queuing'),
        Status: 'WAIT',
      },
      {
        Id: 'SourceBuild',
        Name: intl('saenext.web-app.build-record.RepoistoryPanel.SourceCodeConstruction'),
        Result: intl('saenext.web-app.build-record.RepoistoryPanel.Queuing'),
        Status: 'WAIT',
      },
      {
        Id: 'ImageBuild',
        Name: intl('saenext.web-app.build-record.RepoistoryPanel.ImageBuilding'),
        Result: intl('saenext.web-app.build-record.RepoistoryPanel.Queuing'),
        Status: 'WAIT',
      },
      {
        Id: 'CreateVersion',
        Name: intl('saenext.web-app.build-record.RepoistoryPanel.VersionCreation'),
        Result: intl('saenext.web-app.build-record.RepoistoryPanel.Queuing'),
        Status: 'WAIT',
      },
    ],
  });
  const [isExpand, setIsExpand] = useState(false);
  const [activeKey, setActiveKey] = useState<TabItem>(TabItem.Log);
  const [logData, setLogData] = useState('');
  const [logRefresh, setLogRefresh] = useState(false);
  const [autoShift, setAutoShift] = useState(false);
  const timerRef = useRef(null);
  const logDataRef = useRef('');
  const currentOffset = useRef(0);

  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }));

  useEffect(() => {
    // 处理回显
    if (!visible) return;
    getBuildProcess();
    return () => {
      setClearTimer();
      setLogData('');
      currentOffset.current = 0;
      logDataRef.current = '';
    };
  }, [visible, taskId]);

  useEffect(() => {
    // 处理回显
    if (!visible) return;
    getBuildPipelineLog();
  }, [visible, taskId, buildData]);

  useEffect(() => {
    const container = document.getElementsByClassName('log-container')[0];
    container?.addEventListener('scroll', _.throttle(scrollTrigger, 1000), false);
    return () => {
      container?.removeEventListener('scroll', () => scrollTrigger());
    };
  }, [taskId]);

  const getBuildProcess = async () => {
    setIsLoading(true);
    await getBuildPipelineRun();
    setIsLoading(false);
  };

  const getBuildPipelineRun = async () => {
    const res = await services.DescribeBuildPipelineRun({
      PipelineRunId: taskId,
      ApplicationId: applicationId,
      ApplicationName: applicationName,
    });

    if (res) {
      const { Data = {} } = res;
      const { Steps = [], Status } = Data;
      /*** 由于后端状态更新不及时，此处为前端体验优化，可酌情修改 */
      // 确保流水线已成功但子步骤仍存在 running, 将状态设为 running 以持续请求更新
      let buildStatus = Status;
      if (Status === PIPELINE_STATUS.SUCCESS) {
        // 检查子步骤 是否未结束，如果有未结束的子步骤，则流水线状态为运行
        const isIncomplete = Steps.some((item) =>
          [PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT].includes(item.Status),
        );
        if (isIncomplete) {
          // 实际流出未完成
          buildStatus = PIPELINE_STATUS.RUNNING;
        } else {
          // 流程完成 清除定时器
          setClearTimer();
        }
      }

      const firstStep = Steps[0];
      if (buildStatus === PIPELINE_STATUS.RUNNING && firstStep.Status === PIPELINE_STATUS.WAIT) {
        Data.Steps[0].Status = PIPELINE_STATUS.RUNNING;
      }

      if (buildStatus === PIPELINE_STATUS.STOP) {
        Data.Steps = Data.Steps.map((item) => {
          const status =
            item.Status === PIPELINE_STATUS.RUNNING ? PIPELINE_STATUS.STOP : item.Status;
          return { ...item, Status: status };
        });
        setClearTimer();
      }

      if (buildStatus === PIPELINE_STATUS.FAIL) {
        // 若流水线已失败, 将 running 的子步骤设为失败
        Data.Steps = Data.Steps.map((item) => {
          const status =
            item.Status === PIPELINE_STATUS.RUNNING ? PIPELINE_STATUS.FAIL : item.Status;
          return { ...item, Status: status };
        });
        setClearTimer();
      }
      /*** 前端优化结束 */
      const { StartTime, CodeConfig, BuildConfig } = Data;
      const { Provider, RepoFullName, BranchName, CommitId, CommitUrl } = CodeConfig;
      const { BuildType, BeforeBuildCommand, RunCommand, WorkingDir } = BuildConfig;
      const _autoShift = _.get(Data, 'DeployConfig.UpdateTraffic', false);
      setAutoShift(_autoShift);
      setBuildData({
        ...Data,
        Status: buildStatus,
        StartTime: StartTime,
        RepoUrl: getRepoFullUrl(Provider, RepoFullName),
        BranchName,
        CommitId,
        CommitUrl,
        BuildType,
        BeforeBuildCommand,
        RunCommand,
        WorkingDir,
      });

      // 当流水线的状态状态是 运行中 ｜ 等待中 开启轮训
      if ([PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT].includes(buildStatus)) {
        setStartTimer();
      }
    }
  };

  const getBuildPipelineLog = async (Offset = currentOffset.current, Limit = 200) => {
    const res = await services.listBuildPipelineRunsLogs({
      Offset,
      Limit,
      PipelineRunId: taskId,
      ApplicationId: applicationId,
      ApplicationName: applicationName,
    });
    if (res) {
      const { NextOffset, Items } = res;
      currentOffset.current = NextOffset;
      const extraLog = Items.map((e) => e.Content).join('\n') + (Items.length > 0 ? '\n' : '');
      if (Offset === 0) {
        logDataRef.current = extraLog;
      } else {
        logDataRef.current += extraLog;
      }
      setLogData(logDataRef.current);
    }
  };

  const handleBuild = async () => {
    const { Status } = buildData;
    // final state 终态
    const finalState = [PIPELINE_STATUS.FAIL, PIPELINE_STATUS.SUCCESS, PIPELINE_STATUS.STOP];

    // @ts-ignore
    if (finalState.includes(Status)) {
      // 重新发起构建
      setLogData('');
      logDataRef.current = '';
      setIsLoading(true);
      const res = await services.copyBuildPipelineRun({
        PipelineRunId: taskId,
        ApplicationId: applicationId,
        ApplicationName: applicationName,
      });
      setIsLoading(false);
      if (res) {
        setVisible(false);
        setRefreshIndex && setRefreshIndex(Date.now());
      }
    } else if (Status === PIPELINE_STATUS.RUNNING) {
      // 停止构建
      const res = await services.stopBuildPipelineRun({
        ApplicationId: applicationId,
        PipelineRunId: taskId,
      });
      if (res) {
        getBuildProcess();
      }
    }
  };

  // 启动计时器
  const setStartTimer = () => {
    setClearTimer();
    timerRef.current = setInterval(() => {
      getBuildPipelineRun();
    }, 1000 * 1.5);
  };

  // 清除计时器
  const setClearTimer = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const handleExpand = () => {
    setIsExpand(!isExpand);
    setTimeout(() => {
      const target = document.getElementsByClassName('windcc-app-layout__content')[0];
      target.scrollTo(0, isExpand ? 0 : document.documentElement.offsetHeight);
    }, 0);
  };

  const handleScroll = (position) => {
    const target = document.getElementsByClassName('log-container')[0];
    const { clientHeight, scrollHeight } = target;
    if (position === 'bot') {
      target.scrollTo(0, scrollHeight - clientHeight - 10); // 预留10px避免触发 scrollTrigger
    } else {
      target.scrollTo(0, 0);
    }
  };

  const scrollTrigger = () => {
    const container = document.getElementsByClassName('log-container')[0];
    const { scrollTop, clientHeight, scrollHeight } = container;
    if (scrollHeight - scrollTop - clientHeight < 1) {
      getBuildPipelineLog();
    }
  };

  return (
    <SlidePanel
      title={
        <div className="flex" style={{ cursor: 'pointer' }} onClick={() => setVisible(false)}>
          <svg className="icon" viewBox="0 0 1024 1024" p-id="2170" width="24" height="40">
            <path
              d="M97.834667 542.165333L384 828.330667 444.330667 768l-213.333334-213.333333H938.666667v-85.333334H230.997333l213.333334-213.333333L384 195.626667 97.834667 481.834667a42.666667 42.666667 0 0 0 0 60.330666z"
              fill="#333333"
              p-id="2171"
            ></path>
          </svg>
          <span className="ml-l">
            {intl('saenext.web-app.build-record.RepoistoryPanel.TaskidBuildDetails', {
              taskId: taskId,
            })}
          </span>
        </div>
      }
      width={1200}
      isShowing={visible}
      onClose={() => setVisible(false)}
    >
      <Loading visible={isLoading} className="pipeline full-width full-height">
        {buildData.Status && buildData.VersionId && buildData.Status == 'SUCCESS' ? (
          <Message type="notice" className="mb-s">
            {intl('saenext.web-app.build-record.RepoistoryPanel.TheNewVersionOfThe')}

            <span className="ml-xs mr-xs" style={{ fontWeight: 'bold' }}>
              {unshiftZero(buildData.VersionId)}
            </span>
            {
              (autoShift || buildData.VersionId === 2) ? (
                <span>
                  {intl('saenext.web-app.build-record.RepoistoryPanel.TheTrafficOfTheInitial')}
                </span>
              ) : (
                <span>
                  {intl('saenext.web-app.build-record.RepoistoryPanel.YouCanGoToThe')}
                </span>
              )
            }
          </Message>
        ) : null}

        <Row
          gutter={0}
          wrap
          className="border"
          style={{ height: isExpand ? 'auto' : '100%' }}
        >
          <Col span={isExpand ? 24 : 8}>
            <CndTable
              fixedHeader
              maxBodyHeight={550}
              dataSource={buildData.Steps}
              // @ts-ignore
              columns={[
                {
                  title: '',
                  dataIndex: 'Status',
                  width: 30,
                  cell: (value) => StepStatus[value]?.icon,
                },
                {
                  title: intl('saenext.web-app.build-record.RepoistoryPanel.PerformSteps'),
                  dataIndex: 'Name',
                  cell: (value, idx, item) => (
                    <div style={{ minWidth: 160 }}>
                      <TextWithBalloon text={item.Name} tips={item.Description} />
                    </div>
                  ),
                },
                {
                  title: intl('saenext.web-app.build-record.RepoistoryPanel.Details'),
                  dataIndex: 'Result',
                  cell: (value) => <TextSnippet text={value} maxLength={60} />,
                },
                {
                  title: intl('saenext.web-app.build-record.RepoistoryPanel.TimeConsuming'),
                  dataIndex: 'Duration',
                  cell: (value) => (value ? getDurationString(value) : '-'),
                },
              ]}
            />
          </Col>
          <Col span={isExpand ? 24 : 16} style={{ height: '100%' }}>
            <div style={{ overflow: 'hidden', height: '100%', position: 'relative' }}>
              <Button
                type="primary"
                className="mr"
                onClick={handleBuild}
                style={{ position: 'absolute', right: 0, top: 8, zIndex: 100 }}
              >
                {buildData.Status && StepStatus[buildData.Status].buttonText}
              </Button>
              <Tab
                shape="wrapped"
                activeKey={activeKey}
                onChange={(key: TabItem) => setActiveKey(key)}
              >
                <Tab.Item
                  title={intl('saenext.web-app.build-record.RepoistoryPanel.BuildLogs')}
                  key={TabItem.Log}
                />
                <Tab.Item
                  title={intl('saenext.web-app.build-record.RepoistoryPanel.ExecutionDetails')}
                  key={TabItem.Detail}
                />
              </Tab>
              {activeKey === TabItem.Log ? (
                <>
                  <div className="flex border-l" style={{ flexDirection: 'row-reverse' }}>
                    <Button className="mr-l" text type="primary" onClick={handleExpand}>
                      <Icon type={isExpand ? 'arrow-double-right' : 'arrow-double-left'} />
                      {isExpand
                        ? intl('saenext.web-app.build-record.RepoistoryPanel.PutItAway')
                        : intl('saenext.web-app.build-record.RepoistoryPanel.Expand')}
                    </Button>
                    <Button
                      className="mr-l"
                      text
                      type="primary"
                      onClick={() => handleScroll('bot')}
                    >
                      <Icon type="arrow-down" />
                      {intl('saenext.web-app.build-record.RepoistoryPanel.Bottom')}
                    </Button>
                    <Button
                      className="mr-l"
                      text
                      type="primary"
                      onClick={() => handleScroll('top')}
                    >
                      <Icon type="arrow-up" />
                      {intl('saenext.web-app.build-record.RepoistoryPanel.Top')}
                    </Button>
                    <Button
                      text
                      type="primary"
                      className="mr-l"
                      onClick={async () => {
                        setLogRefresh(true);
                        await getBuildPipelineLog(0);
                        setLogRefresh(false);
                      }}
                    >
                      <Icon type="refresh" />
                      {intl('saenext.web-app.build-record.RepoistoryPanel.Refresh')}
                    </Button>
                  </div>
                  <div
                    className="log-container"
                    style={{ height: isExpand ? 'auto' : 'calc(100% - 62px)' }}
                  >
                    <Loading visible={logRefresh}>
                      <LogEditor className="border-none" value={logData || 'Log generating...'} />
                    </Loading>
                  </div>
                </>
              ) : (
                <div className={`pt-l pl-l pr-l full-height ${!isExpand ? 'border-l' : ''}`}>
                  <DataFields
                    dataSource={buildData}
                    items={[
                      {
                        dataIndex: 'PipelineRunId',
                        span: 24,
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.BuildId'),
                      },
                      {
                        dataIndex: 'Status',
                        span: 24,
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.BuildStatus'),
                        render: (value) => <BuildStatus value={value} />,
                      },
                      {
                        dataIndex: 'RepoUrl',
                        span: 24,
                        label: intl(
                          'saenext.web-app.build-record.RepoistoryPanel.SourceCodeRepository',
                        ),
                        render: (value) => (
                          <Truncate type="width" threshold={580} align="t">
                            <a href={value} target="_blank">
                              {value}
                            </a>
                          </Truncate>
                        ),
                      },
                      {
                        dataIndex: 'BranchName',
                        span: 24,
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.CodeBranch'),
                      },
                      {
                        label: intl(
                          'saenext.web-app.build-record.RepoistoryPanel.BuildTriggerMode',
                        ),
                        span: 24,
                        dataIndex: 'TriggerConfig',
                        render: (value) => C_TRIGGER[value?.Type]?.label || '-',
                      },
                      {
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.TriggerTag'),
                        dataIndex: 'TriggerConfig',
                        // visible: (value) => value?.Type === TRIGGER_TYPE.TAG,
                        render: (value) => value?.TagName || '-',
                      },
                      {
                        dataIndex: 'CommitId',
                        span: 24,
                        label: 'Commit ID',
                        render: (value, item?: any) => (
                          <Truncate type="width" threshold={580} align="t">
                            <a href={item.CommitUrl} target="_blank">
                              {value}
                            </a>
                          </Truncate>
                        ),
                      },
                      {
                        dataIndex: 'CreateTime',
                        span: 24,
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.CreationTime'),
                        render: (value) => moment(value).format('lll'),
                      },
                      {
                        dataIndex: 'StartTime',
                        span: 24,
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.StartTime'),
                        render: (value) => moment(value).format('lll'),
                      },
                      {
                        dataIndex: 'WaitDuration',
                        span: 24,
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.QueuingTime'),
                        render: (value) => getDurationString(value),
                      },
                      {
                        dataIndex: 'Steps',
                        span: 24,
                        label: intl(
                          'saenext.web-app.build-record.RepoistoryPanel.TotalBuildDuration',
                        ),
                        render: (value) =>
                          getDurationString(
                            value.reduce((pre, cur) => pre + (cur.Duration || 0), 0),
                          ),
                      },
                      {
                        dataIndex: 'BuildType',
                        span: 24,
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.DeploymentType'),
                        render: (value) =>
                          value === 'Dockerfile'
                            ? intl(
                                'saenext.web-app.build-record.RepoistoryPanel.SpecifyDockerfileBuild',
                              )
                            : intl(
                                'saenext.web-app.build-record.RepoistoryPanel.NoDockerfileBuild',
                              ),
                      },
                      {
                        dataIndex: 'WorkingDir',
                        span: 24,
                        label: intl(
                          'saenext.web-app.build-record.RepoistoryPanel.WorkingDirectory',
                        ),
                        render: (value) =>
                          value || intl('saenext.web-app.build-record.RepoistoryPanel.NotSet'),
                      },
                      {
                        dataIndex: 'BeforeBuildCommand',
                        span: 24,
                        label: intl(
                          'saenext.web-app.build-record.RepoistoryPanel.PrebuildCommands',
                        ),
                        render: (value) =>
                          value || intl('saenext.web-app.build-record.RepoistoryPanel.NotSet'),
                      },
                      {
                        dataIndex: 'RunCommand',
                        span: 24,
                        label: intl('saenext.web-app.build-record.RepoistoryPanel.StartCommand'),
                        render: (value) =>
                          value || intl('saenext.web-app.build-record.RepoistoryPanel.NotSet'),
                      },
                    ]}
                  />
                </div>
              )}
            </div>
          </Col>
        </Row>
      </Loading>
    </SlidePanel>
  );
});
