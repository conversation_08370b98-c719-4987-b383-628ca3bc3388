import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { getParams, unshiftZero, getDurationString, getRepoFullUrl } from '~/utils/global';
import services from '~/services';
import BuildStatus from './BuildStatus';
import { Message, Dialog, Button, Icon, CndTable, Instance, LinkButton, DateTime } from '@ali/cnd';
import RepoistoryPanel from './RepoistoryPanel';
import { includes, isEmpty } from 'lodash';
import { InstanceStatus } from './constants';
import { PIPELINE_STATUS } from '~/constants/application';
import { C_REPOSITORY, C_TRIGGER } from '~/components/shared/DeploySelectorField/constant';

type Props = {
  history: any;
  regionId: string;
  urlSearch: any;
  applicationId: string;
};

const RepoistoryRecord = (props: Props) => {
  const applicationName = getParams('name');
  const { history, regionId, urlSearch, applicationId } = props;
  const [hasRecord, setHasRecord] = useState(false);
  const [buildEnabled, setBuildEnabled] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [taskId, setTaskId] = useState();
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(10);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [loading, setLoading] = useState<boolean>(false);
  const timerRef = useRef(null);
  const refBuild = useRef(null);

  useEffect(() => {
    async function getPipelineList() {
      setLoading(true);
      await getBuildList();
      await getPipelineInfo();
      setLoading(false);
    }
    getPipelineList();

    return () => {
      setClearTimer();
    };
  }, [refreshIndex, page, pageSize]);

  const getPipelineInfo = async () => {
    const res = await services.describeBuildPipeline({
      ApplicationId: applicationId,
      ApplicationName: applicationName,
    });
    const { Data } = res;
    setHasRecord(!!Data);
    setBuildEnabled(Data?.Enabled);
  };

  const getBuildList = async () => {
    const _dataSource = await services.listBuildPipelineRuns({
      ApplicationId: applicationId,
      ApplicationName: applicationName,
      PageNo: page,
      PageSize: pageSize,
    });
    setDataSource(_dataSource.Items);
    if (_dataSource.Total < pageSize) {
      setTotal((page - 1) * pageSize + _dataSource.Total);
    } else {
      setTotal(page * pageSize + 1);
    }

    // 如果有正在构建中的流逝线需要轮训
    const runStatus = [PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT];
    if (_dataSource.Items.some((item) => includes(runStatus, item.Status))) {
      if (timerRef?.current) return;
      timerRef.current = setInterval(() => {
        getBuildList();
      }, 1000 * 1.5);
    } else {
      setClearTimer();
    }
  };

  const handleSwitch = async () => {
    await services.updateBuildPipelineAttributes({
      Enabled: !buildEnabled,
      ApplicationId: applicationId,
      ApplicationName: applicationName,
    });
    setBuildEnabled(!buildEnabled);
    setIsShow(false);
  };

  const handleDetail = (taskId) => {
    // 构建详情 不做单独路由
    setTaskId(taskId);
    refBuild?.current.show();
  };

  const handleVersionDetail = (versionId) => {
    if (versionId) {
      history.push(
        `/${regionId}/app-list/${applicationId}/version/${versionId}/configure${urlSearch}`,
      );
    }
  };

  const setClearTimer = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  return (
    <>
      <Message type="notice" className="mb-s">
        {intl('saenext.web-app.build-record.Repoistory.TheDetailedBuildLogsOf')}
      </Message>
      <CndTable
        className="mt-s"
        loading={loading}
        dataSource={dataSource}
        operation={
          <div>
            <Button
              className="mr"
              type="primary"
              onClick={() =>
                history.push(
                  `/${regionId}/app-list/${applicationId}/web-app/version${urlSearch}&action=create`,
                )
              }
            >
              {intl('saenext.web-app.build-record.Repoistory.CreateANewVersionAnd')}
            </Button>
            {hasRecord ? (
              <Button onClick={() => setIsShow(true)}>
                {buildEnabled
                  ? intl('saenext.web-app.build-record.Repoistory.Stop')
                  : intl('saenext.web-app.build-record.Repoistory.Enable')}
                {intl('saenext.web-app.build-record.Repoistory.ContinuousBuildDeployment')}
              </Button>
            ) : null}
          </div>
        }
        secondaryOperation={
          <Button onClick={() => setRefreshIndex(Date.now())}>
            <Icon type="refresh" />
          </Button>
        }
        pagination={{
          total,
          pageSize,
          current: page,
          pageSizeList: [10, 20, 50, 100],
          onChange: page => {
            setClearTimer();
            setPage(page);
          },
          onPageSizeChange: pageSize => {
            setClearTimer();
            setPageSize(pageSize);
          },
          hideOnlyOnePage: false,
        }}
        columns={[
          {
            title: intl('saenext.web-app.build-record.Repoistory.BuildId'),
            dataIndex: 'PipelineRunId',
            width: 150,
            lock: 'left',
            cell: (value, index, recoder) => {
              return (
                <div style={{ minWidth: 100 }}>
                  {/* @ts-ignore */}
                  <Instance
                    link={{
                      value: value,
                      onClick: () => handleDetail(value),
                    }}
                    status={InstanceStatus[recoder.Status] || 'info'}
                  />
                </div>
              );
            },
          },
          {
            title: intl('saenext.web-app.build-record.Repoistory.Status'),
            dataIndex: 'Status',
            width: 100,
            cell: value => <BuildStatus value={value} />,
          },
          {
            title: intl('saenext.web-app.build-record.Repoistory.ApplicationVersion'),
            dataIndex: 'VersionId',
            width: 100,
            cell: value =>
              value ? (
                <LinkButton onClick={() => handleVersionDetail(value)}>
                  <div style={{ minWidth: 60, textAlign: 'left' }}>{unshiftZero(value)}</div>
                </LinkButton>
              ) : (
                '-'
              ),
          },
          {
            title: intl('saenext.web-app.build-record.Repoistory.SourceCodeRepository'),
            dataIndex: 'CodeConfig',
            width: 250,
            cell: value =>
              !isEmpty(value.RepoFullName) ? (
                <div className="flex" style={{ minWidth: 190 }}>
                  {C_REPOSITORY[value.Provider]?.svg()}

                  <a
                    className="ml-xs"
                    href={getRepoFullUrl(value.Provider, value.RepoFullName)}
                    target="_blank"
                  >
                    {value.RepoFullName}
                  </a>
                </div>
              ) : (
                '-'
              ),
          },
          {
            title: intl('saenext.web-app.build-record.Repoistory.CodeBranch'),
            dataIndex: 'CodeConfig',
            width: 100,
            cell: value =>
              !isEmpty(value.BranchName) ? (
                <div style={{ minWidth: 60 }}>{value.BranchName}</div>
              ) : (
                '-'
              ),
          },
          {
            title: intl('saenext.web-app.build-record.Repoistory.BuildTriggerMode'),
            dataIndex: 'TriggerConfig.Type',
            width: 150,
            cell: value => C_TRIGGER[value]?.label || '-',
          },
          {
            title: intl('saenext.web-app.build-record.Repoistory.TriggerTag'),
            width: 150,
            dataIndex: 'TriggerConfig.TagName',
            cell: value => value || '-',
          },
          {
            title: 'Commit ID',
            dataIndex: 'CodeConfig',
            width: 320,
            cell: value =>
              !isEmpty(value.CommitId) ? (
                <LinkButton onClick={() => window.open(value.CommitUrl, '_blank')}>
                  {value.CommitId}
                </LinkButton>
              ) : (
                '-'
              ),
          },
          {
            title: intl('saenext.web-app.build-record.Repoistory.CreationTime'),
            dataIndex: 'CreateTime',
            width: 200,
            cell: value => <DateTime value={value}></DateTime>,
          },
          {
            title: intl('saenext.web-app.build-record.Repoistory.BuildDuration'),
            dataIndex: 'BuildDuration',
            width: 100,
            cell: value => <div style={{ minWidth: 60 }}>{getDurationString(value)}</div>,
          },
        ]}
      />

      <Dialog
        title={
          buildEnabled
            ? intl('saenext.web-app.build-record.Repoistory.StopContinuousBuildDeployment')
            : intl('saenext.web-app.build-record.Repoistory.EnableContinuousBuildDeployment')
        }
        visible={isShow}
        onOk={handleSwitch}
        onClose={() => setIsShow(false)}
        onCancel={() => setIsShow(false)}
      >
        <Message type={buildEnabled ? 'warning' : 'notice'}>
          {buildEnabled
            ? intl('saenext.web-app.build-record.Repoistory.AfterStoppingTheSystemWill')
            : intl('saenext.web-app.build-record.Repoistory.WhenEnabledTheSystemWill')}
        </Message>
      </Dialog>
      <RepoistoryPanel
        ref={refBuild}
        taskId={taskId}
        applicationId={applicationId}
        applicationName={applicationName}
        setRefreshIndex={setRefreshIndex}
      />
    </>
  );
};

export default RepoistoryRecord;
