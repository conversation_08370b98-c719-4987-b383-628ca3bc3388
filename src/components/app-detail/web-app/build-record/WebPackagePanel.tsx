import { intl } from '@ali/cnd';
import React, { useRef, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { SlidePanel, Loading, Timeline, Icon, Tab, Button, Description } from '@ali/cnd';
import _ from 'lodash';
import moment from 'moment';
import { LogEditor } from '@ali/cnd';
import { unshiftZero, getDurationString } from '~/utils/global';
import services from '~/services';
import { PIPELINE_STATUS } from '~/constants/application';
import { TabItem, StepStatus } from './constants';

interface Props {
  pipelineRunId: string;
  applicationId: string;
  applicationName: string;
  setRefreshIndex: (index: number) => void;
}

export default forwardRef((props: Props, ref) => {
  const { pipelineRunId, applicationId, applicationName, setRefreshIndex } = props;
  const [visible, setVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  // 包含创建的流程步骤 详情
  const [buildData, setBuildData] = useState({
    Status: 'RUNNING',
    Steps: [
      {
        Id: 'CreateEnv',
        Name: intl('saenext.web-app.build-record.WebPackagePanel.EnvironmentCreation'),
        Result: intl('saenext.web-app.build-record.WebPackagePanel.Running'),
        Status: 'RUNNING',
      },
      {
        Id: 'ImageBuild',
        Name: intl('saenext.web-app.build-record.WebPackagePanel.ImageBuilding'),
        Result: intl('saenext.web-app.build-record.WebPackagePanel.Queuing'),
        Status: 'WAIT',
      },
      {
        Id: 'CreateVersion',
        Name: intl('saenext.web-app.build-record.WebPackagePanel.VersionCreation'),
        Result: intl('saenext.web-app.build-record.WebPackagePanel.Queuing'),
        Status: 'WAIT',
      },
    ],
  });
  const [activeKey, setActiveKey] = useState<TabItem>(TabItem.Log);
  const [logData, setLogData] = useState('');
  const [logRefresh, setLogRefresh] = useState(false);
  const timerRef = useRef(null);
  const logDataRef = useRef('');
  const currentOffset = useRef(0);

  useEffect(() => {
    // 处理回显
    if (!visible) return;
    getBuildProcess();
    return () => {
      setClearTimer();
      setLogData('');
      currentOffset.current = 0;
      logDataRef.current = '';
    };
  }, [visible, pipelineRunId]);

  useEffect(() => {
    // 处理回显
    if (!visible) return;
    getBuildPipelineLog();
    // buildData 当在运行中时加载日志
  }, [visible, pipelineRunId, buildData]);

  useEffect(() => {
    const container = document.getElementsByClassName('log-container')[0];
    container?.addEventListener('scroll', _.throttle(scrollTrigger, 1000), false);
    return () => {
      container?.removeEventListener('scroll', () => scrollTrigger());
    };
  }, [pipelineRunId]);

  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }));

  const getBuildProcess = async () => {
    setIsLoading(true);
    await getBuildPipelineRun();
    setIsLoading(false);
  };

  const getBuildPipelineRun = async () => {
    const res = await services.DescribeBuildPipelineRun({
      ApplicationId: applicationId,
      ApplicationName: applicationName,
      PipelineRunId: pipelineRunId,
    });
    const { Data = {} } = res;
    const { Status, Steps = [] } = Data;
    // Status 可以拿到流水线的状态 但是后端状态更新不及时，当流水线状态是成功时，子步骤可能仍在运行，此时不能标定为结束
    let buildStatus = Status;
    if (Status === PIPELINE_STATUS.SUCCESS) {
      // 检查子步骤 是否未结束，如果有未结束的子步骤，则流水线状态为运行
      const isIncomplete = Steps.some((item) =>
        [PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT].includes(item.Status),
      );
      if (isIncomplete) {
        // 实际流出未完成
        buildStatus = PIPELINE_STATUS.RUNNING;
      } else {
        // 流程完成 清除定时器
        setClearTimer();
      }
    }

    const firstStep = Steps[0];
    // 前端体验优化,当流水线的状态状态是 运行中，如果第一步是等待⌛️，设置为运行中
    if (buildStatus === PIPELINE_STATUS.RUNNING && firstStep.Status === PIPELINE_STATUS.WAIT) {
      Data.Steps[0].Status = PIPELINE_STATUS.RUNNING;
    }

    // 1、停止 流水线的终态，将子步骤设为停止
    if (buildStatus === PIPELINE_STATUS.STOP) {
      Data.Steps = Data.Steps.map((item) => {
        const status = item.Status === PIPELINE_STATUS.RUNNING ? PIPELINE_STATUS.STOP : item.Status;
        return { ...item, Status: status };
      });
      setClearTimer();
    }

    // 2、失败 流水线的终态，将子步骤设为失败
    if (buildStatus === PIPELINE_STATUS.FAIL) {
      Data.Steps = Data.Steps.map((item) => {
        const status = item.Status === PIPELINE_STATUS.RUNNING ? PIPELINE_STATUS.FAIL : item.Status;
        return { ...item, Status: status };
      });
      setClearTimer();
    }

    const { StartTime, PackageConfig = {}, BuildConfig = {} } = Data;
    const { PackageName, PackageVersion, PackageType, PackageUrl } = PackageConfig;
    const { BuildType, BeforeBuildCommand, RunCommand, WorkingDir } = BuildConfig;

    //  代码包部署 只要 CreateEnv ImageBuild CreateVersion 三个步骤
    const packageSteps = Steps.filter(
      (step) => step.Id === 'CreateEnv' || step.Id === 'ImageBuild' || step.Id === 'CreateVersion',
    );

    setBuildData({
      ...Data,
      Status: buildStatus,
      Steps: packageSteps,
      StartTime: StartTime,
      BuildType,
      BeforeBuildCommand,
      RunCommand,
      WorkingDir,
      PackageName,
      PackageVersion,
      PackageType,
      PackageUrl,
    });

    // 当流水线的状态状态是 运行中 ｜ 等待中 开启轮训
    if ([PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT].includes(buildStatus)) {
      setStartTimer();
    }
  };

  const getBuildPipelineLog = async (Offset = currentOffset.current, Limit = 200) => {
    const res = await services.listBuildPipelineRunsLogs({
      Offset,
      Limit,
      ApplicationId: applicationId,
      ApplicationName: applicationName,
      PipelineRunId: pipelineRunId,
    });
    if (res) {
      const { NextOffset, Items } = res;
      currentOffset.current = NextOffset;
      const extraLog = Items.map((e) => e.Content).join('\n') + (Items.length > 0 ? '\n' : '');
      if (Offset === 0) {
        logDataRef.current = extraLog;
      } else {
        logDataRef.current += extraLog;
      }
      setLogData(logDataRef.current);
    }
  };

  // @ts-ignore
  const handleBuild = async () => {
    const { Status } = buildData;
    // final state 终态
    const finalState = [PIPELINE_STATUS.FAIL, PIPELINE_STATUS.SUCCESS, PIPELINE_STATUS.STOP];

    // @ts-ignore
    if (finalState.includes(Status)) {
      // 重新发起构建
      setLogData('');
      logDataRef.current = '';
      setIsLoading(true);
      const res = await services.copyBuildPipelineRun({
        ApplicationId: applicationId,
        ApplicationName: applicationName,
        PipelineRunId: pipelineRunId,
      });
      setIsLoading(false);
      if (res) {
        setVisible(false);
        setRefreshIndex && setRefreshIndex(Date.now());
      }
    } else if (Status === PIPELINE_STATUS.RUNNING) {
      // 停止构建
      const res = await services.stopBuildPipelineRun({
        ApplicationId: applicationId,
        PipelineRunId: pipelineRunId,
      });
      if (res) {
        getBuildProcess();
      }
    }
  };

  // 启动计时器
  const setStartTimer = () => {
    setClearTimer();
    timerRef.current = setInterval(() => {
      getBuildPipelineRun();
    }, 1000 * 1.5);
  };

  // 清除计时器
  const setClearTimer = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const handleScroll = (position) => {
    const target = document.getElementsByClassName('log-container')[0];
    const { clientHeight, scrollHeight } = target;
    if (position === 'bot') {
      // 预留10px避免触发 scrollTrigger
      target.scrollTo(0, scrollHeight - clientHeight - 10);
    } else {
      target.scrollTo(0, 0);
    }
  };

  const scrollTrigger = () => {
    const container = document.getElementsByClassName('log-container')[0];
    const { scrollTop, clientHeight, scrollHeight } = container;
    if (scrollHeight - scrollTop - clientHeight < 1) {
      getBuildPipelineLog();
    }
  };

  return (
    <SlidePanel
      title={
        <div className="flex" style={{ cursor: 'pointer' }} onClick={() => setVisible(false)}>
          <svg className="icon" viewBox="0 0 1024 1024" p-id="2170" width="24" height="40">
            <path
              d="M97.834667 542.165333L384 828.330667 444.330667 768l-213.333334-213.333333H938.666667v-85.333334H230.997333l213.333334-213.333333L384 195.626667 97.834667 481.834667a42.666667 42.666667 0 0 0 0 60.330666z"
              fill="#333333"
              p-id="2171"
            ></path>
          </svg>
          <span className="ml-l">
            {intl('saenext.web-app.build-record.WebPackagePanel.PipelinerunidBuildDetails', {
              pipelineRunId: pipelineRunId,
            })}
          </span>
        </div>
      }
      width={1200}
      isShowing={visible}
      onClose={() => setVisible(false)}
    >
      <Loading visible={isLoading} className="pipeline full-width full-height">
        <div className="timeline">
          <Timeline>
            {buildData.Steps.map((step) => {
              // @ts-ignore
              const { StartTime, Status, Name, Id, Result } = step;
              const { icon: dot } = StepStatus[Status];
              const time = StartTime ? new Date(StartTime).toLocaleString() : '';
              return (
                <Timeline.Item
                  key={Id}
                  title={Name}
                  dot={dot}
                  time={time}
                  content={
                    // @ts-ignore
                    buildData?.VersionId && Id === 'CreateVersion' ? (
                      <span>
                        {intl('saenext.web-app.build-record.WebPackagePanel.TheNewVersionOfThe')}

                        <span className="ml-xs mr-xs" style={{ fontWeight: 'bold' }}>
                          {/* @ts-ignore */}
                          {unshiftZero(buildData.VersionId)}
                        </span>
                      </span>
                    ) : (
                      Result
                    )
                  }
                />
              );
            })}
          </Timeline>
        </div>

        <div className="buildlog mt-l" style={{ height: 'calc(100% - 244px)', overflow: 'hidden' }}>
          <div style={{ position: 'relative' }}>
            <Tab
              shape="wrapped"
              activeKey={activeKey}
              onChange={(key: TabItem) => setActiveKey(key)}
            >
              <Tab.Item
                title={intl('saenext.web-app.build-record.WebPackagePanel.BuildLogs')}
                key="log"
              />
              <Tab.Item
                title={intl('saenext.web-app.build-record.WebPackagePanel.ExecutionDetails')}
                key="detail"
              />
            </Tab>

            {/* <Button 
               type="primary"
               onClick={handleBuild}
               style={{ position: 'absolute', right: 0, top: 8 }}
              >
               { buildData?.Status && StepStatus[buildData?.Status].buttonText }
              </Button>       */}
          </div>
          <>
            {activeKey === TabItem.Log ? (
              <>
                <div className="flex" style={{ flexDirection: 'row-reverse' }}>
                  <Button className="mr-l" text type="primary" onClick={() => handleScroll('bot')}>
                    <Icon type="arrow-down" />
                    {intl('saenext.web-app.build-record.WebPackagePanel.Bottom')}
                  </Button>
                  <Button className="mr-l" text type="primary" onClick={() => handleScroll('top')}>
                    <Icon type="arrow-up" />
                    {intl('saenext.web-app.build-record.WebPackagePanel.Top')}
                  </Button>
                  <Button
                    text
                    type="primary"
                    className="mr-l"
                    onClick={async () => {
                      setLogRefresh(true);
                      await getBuildPipelineLog(0);
                      setLogRefresh(false);
                    }}
                  >
                    <Icon type="refresh" />
                    {intl('saenext.web-app.build-record.WebPackagePanel.Refresh')}
                  </Button>
                </div>
                <div className="log-container" style={{ height: 'calc(100% - 68px)' }}>
                  <Loading visible={logRefresh} style={{ height: '100%' }}>
                    <LogEditor className="border-none" value={logData || 'Log generating...'} />
                  </Loading>
                </div>
              </>
            ) : (
              <div className="pl-l pr-l mt-l">
                <Description
                  title=""
                  // @ts-ignore
                  dataSource={buildData}
                  items={[
                    {
                      dataIndex: 'PipelineRunId',
                      label: intl('saenext.web-app.build-record.WebPackagePanel.BuildId'),
                    },
                    {
                      dataIndex: 'Status',
                      label: intl('saenext.web-app.build-record.WebPackagePanel.BuildStatus'),
                      render: (value) => StepStatus[value].label,
                    },
                    {
                      dataIndex: 'CreateTime',
                      label: intl('saenext.web-app.build-record.WebPackagePanel.CreationTime'),
                      render: (value) => moment(value).format('lll'),
                    },
                    {
                      dataIndex: 'StartTime',
                      label: intl('saenext.web-app.build-record.WebPackagePanel.StartTime'),
                      render: (value) => moment(value).format('lll'),
                    },
                    {
                      dataIndex: 'WaitDuration',
                      label: intl('saenext.web-app.build-record.WebPackagePanel.QueuingTime'),
                      render: (value) => getDurationString(value),
                    },
                    {
                      dataIndex: 'Steps',
                      label: intl(
                        'saenext.web-app.build-record.WebPackagePanel.TotalBuildDuration',
                      ),
                      render: (value) =>
                        getDurationString(value.reduce((pre, cur) => pre + (cur.Duration || 0), 0)),
                    },
                    {
                      dataIndex: 'PackageConfig',
                      label: intl('saenext.web-app.build-record.WebPackagePanel.PackageVersion'),
                      render: (value) => value?.PackageVersion,
                    },
                    {
                      dataIndex: 'PackageConfig',
                      label: intl('saenext.web-app.build-record.WebPackagePanel.PackageName'),
                      render: (value) => value?.PackageName,
                    },
                  ]}
                />
              </div>
            )}
          </>
        </div>
      </Loading>
    </SlidePanel>
  );
});
