import { intl } from '@ali/cnd';
import { Icon } from '@ali/cnd';
import React, { memo } from 'react';

export const MAP = {
  RUNNING: {
    type: 'loading',
    text: intl('saenext.web-app.build-record.BuildStatus.Running'),
  },
  SUCCESS: {
    type: 'success',
    text: intl('saenext.web-app.build-record.BuildStatus.Success'),
  },
  FAIL: {
    type: 'error',
    text: intl('saenext.web-app.build-record.BuildStatus.Failed'),
  },
  WAIT: {
    type: 'loading',
    text: intl('saenext.web-app.build-record.BuildStatus.Queuing'),
  },
  STOP: {
    type: 'minus_fill',
    text: intl('saenext.web-app.build-record.BuildStatus.Terminated'),
  },
  CANCEL: {
    type: 'minus_fill',
    text: intl('saenext.web-app.build-record.BuildStatus.Canceled'),
  },
};

const BuildStatus = (props) => {
  const { value } = props;
  if (!value) {
    return <span>--</span>;
  }
  const { type, text } = MAP[value] || {};
  if (!type) {
    return <span>--</span>;
  }
  return (
    <div className="flex" style={{ minWidth: 60 }}>
      <Icon type={type} size="xs" />
      <span className="ml-s">{text}</span>
    </div>
  );
};

export default memo(BuildStatus);
