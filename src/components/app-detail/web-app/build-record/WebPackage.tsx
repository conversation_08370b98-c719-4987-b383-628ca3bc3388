import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Icon, CndTable, Instance, LinkButton, DateTime } from '@ali/cnd';
import { getParams, unshiftZero, getDurationString } from '~/utils/global';
import services from '~/services';
import BuildStatus from './BuildStatus';
import { InstanceStatus } from './constants';
import WebPackagePanel from './WebPackagePanel';
import { PIPELINE_STATUS } from '~/constants/application';
import { includes } from 'lodash';

type Props = {
  history: any;
  regionId: string;
  urlSearch: any;
  applicationId: string;
};

const WebPackageRecord = (props: Props) => {
  const { history, regionId, urlSearch, applicationId } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(10);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [pipelineRunId, setPipelineRunId] = useState();
  const applicationName = getParams('name');
  const refBuild = useRef(null);
  const timerRef = useRef(null);

  useEffect(() => {
    async function getPipelineList() {
      setLoading(true);
      await getBuildList();
      setLoading(false);
    }
    getPipelineList();
  }, [refreshIndex, page, pageSize]);

  const getBuildList = async () => {
    const _dataSource = await services.listBuildPipelineRuns({
      ApplicationId: applicationId,
      ApplicationName: applicationName,
      PageNo: page,
      PageSize: pageSize,
    });
    setDataSource(_dataSource.Items);
    if (_dataSource.Total < pageSize) {
      setTotal((page - 1) * pageSize + _dataSource.Total);
    } else {
      setTotal(page * pageSize + 1);
    }

    // 如果有正在构建中的流逝线需要轮训
    const runStatus = [PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT];
    if (_dataSource.Items.some((item) => includes(runStatus, item.Status))) {
      if (timerRef?.current) return;
      timerRef.current = setInterval(() => {
        getBuildList();
      }, 1000 * 1.5);
    } else {
      setClearTimer();
    }
  };

  const setClearTimer = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const handleDetail = (runId) => {
    // 构建详情 不做单独路由
    setPipelineRunId(runId);
    refBuild?.current.show();
  };

  const handleVersionDetail = (versionId) => {
    if (versionId) {
      history.push(
        `/${regionId}/app-list/${applicationId}/version/${versionId}/configure${urlSearch}`,
      );
    }
  };

  return (
    <>
      <CndTable
        className="mt-s"
        loading={loading}
        dataSource={dataSource}
        secondaryOperation={
          <Button onClick={() => setRefreshIndex(Date.now())}>
          <Icon type="refresh" />
        </Button>
        }
        pagination={{
          total,
          current: page,
          pageSize,
          pageSizeList: [10, 20, 50, 100],
          onChange: setPage,
          onPageSizeChange: setPageSize,
          hideOnlyOnePage: false,
        }}
        // @ts-ignore
        columns={[
          {
            title: intl('saenext.web-app.build-record.WebPackage.BuildId'),
            dataIndex: 'PipelineRunId',
            cell: (value, index, recoder) => {
              return (
                // @ts-ignore
                <Instance
                  link={{
                    value: value,
                    onClick: () => handleDetail(value),
                  }}
                  status={InstanceStatus[recoder.Status] || 'info'}
                />
              );
            },
          },
          {
            title: intl('saenext.web-app.build-record.WebPackage.Status'),
            dataIndex: 'Status',
            cell: (value) => <BuildStatus value={value} />,
          },
          {
            title: intl('saenext.web-app.build-record.WebPackage.ApplicationVersion'),
            dataIndex: 'VersionId',
            cell: (value) =>
              value ? (
                <LinkButton onClick={() => handleVersionDetail(value)}>
                  {unshiftZero(value)}
                </LinkButton>
              ) : (
                '-'
              ),
          },
          {
            title: intl('saenext.web-app.build-record.WebPackage.Package'),
            dataIndex: 'PackageConfig',
            cell: (value) =>
              value
                ? intl('saenext.web-app.build-record.WebPackage.Version') +
                  value.PackageVersion +
                  intl('saenext.web-app.build-record.WebPackage.PackageName') +
                  value.PackageName
                : '-',
          },
          {
            title: intl('saenext.web-app.build-record.WebPackage.CreationTime'),
            dataIndex: 'CreateTime',
            cell: (value) => <DateTime value={value}></DateTime>,
          },
          {
            title: intl('saenext.web-app.build-record.WebPackage.BuildDuration'),
            dataIndex: 'BuildDuration',
            cell: (value) => getDurationString(value),
          },
        ]}
      />

      <WebPackagePanel
        ref={refBuild}
        pipelineRunId={pipelineRunId}
        applicationId={applicationId}
        applicationName={applicationName}
        setRefreshIndex={setRefreshIndex}
      />
    </>
  );
};

export default WebPackageRecord;
