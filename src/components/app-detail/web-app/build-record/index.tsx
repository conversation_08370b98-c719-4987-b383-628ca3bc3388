import React from 'react';
import { DEPLOY_TYPE } from '~/constants/application';
import RepoistoryRecord from './Repoistory';
import WebPackageRecord from './WebPackage';


const BuildRecord = (props) => {
  const {
    match: {
      params: { regionId, id },
    },
    history,
    location: { search },
    deployType
  } = props;
  const applicationId = id;

  return (
    <>
     {
      deployType === DEPLOY_TYPE.REPOISTORY ? (
        <RepoistoryRecord
          history={history}
          regionId={regionId} 
          urlSearch={search}
          applicationId={applicationId}
        />
      ) : (
        <WebPackageRecord 
          history={history}
          regionId={regionId} 
          urlSearch={search}
          applicationId={applicationId}
        />
      )
     }
    </>
  );
};

export default BuildRecord;