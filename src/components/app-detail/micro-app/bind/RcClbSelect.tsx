import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { get, noop, includes } from 'lodash';
import { Select, Icon, Balloon, Button } from '@ali/cnd';
import services from "~/services";
import ExternalLink from '../../../shared/ExternalLink';
import CachedData from '../../../../cache/common';

const PayBySpec = -1;
const PayByCLCU = -2;
export const ChargeTypes = [PayBySpec, PayByCLCU];
export const SlbChargePay = { [PayBySpec]: 'PayBySpec', [PayByCLCU]: 'PayByCLCU' };
const InitializrSlbs = [
  {
    label: intl('saenext.components.application.RcClbSelect.CreateAClbInstanceBilled'),
    value: PayBySpec,
  },
  {
    label: intl('saenext.components.application.RcClbSelect.CreateAClbInstanceBilled.1'),
    value: PayByCLCU,
  },
];

const RcClbSelect = (props) => {
  const { isCreate, isShowing, regionId, appId, isPublic, callback = noop } = props;
  if (!isCreate) return null;

  const [loading, setLoading] = useState(false);
  const [slbList, setSlbList] = useState([...InitializrSlbs]);
  const [currentSlbId, setCurrentSlbId] = useState(PayBySpec);

  useEffect(() => {
    getAvailableSlbs();
  }, [isShowing]);

  const getAvailableSlbs = async (pageNumber = 1, pageSize = 20, lastSlbList = []) => {
    if (!isCreate) return;
    if (!isShowing) return;
    setLoading(true);
    const res = await services.getAvailableSlbs({
      params: {
        RegionId: regionId,
        AppId: appId,
        Type: isPublic ? 'internet' : 'intranet',
        CurrentPage: pageNumber,
        PageSize: pageSize,
        Force: true,
      },
      customErrorHandle: (error, data, callback) => {
        setLoading(false);
        callback && callback();
      },
    });
    const slbs = get(res, 'Data.Slbs', []);
    const total = get(res, 'Data.TotalSize', 0);
    const _lastSlbList = [...lastSlbList, ...slbs];
    if (pageNumber * pageSize < total) {
      getAvailableSlbs(++pageNumber, pageSize, _lastSlbList);
    }
    const _slbList = isCreate ? [...InitializrSlbs] : [];
    const reuseMap = {
      'Aliyun Serverless App Engine': intl(
        'saenext.components.application.RcClbSelect.SaeSurrogateShoppingReuseIs',
      ),
      'Aliyun Container Service': intl(
        'saenext.components.application.RcClbSelect.AckSurrogateShoppingReuseIs',
      ),
    };
    _lastSlbList.forEach((item) => {
      const { CreatedBy } = item;
      _slbList.push({
        // @ts-ignore
        disabled: !!CreatedBy,
        label: `${item.Address}(${item.SlbName})${CreatedBy ? reuseMap[CreatedBy] : ''}`,
        value: item.SlbId,
      });
    });
    // @ts-ignore
    _slbList.sort((a, b) => a.disabled - b.disabled);
    setLoading(false);
    setSlbList(_slbList);
  };

  const getLoadBalanceInfo = async (loadBalancerId) => {
    const res = await services.getLoadBalanceInfo({
      params: {
        RegionId: regionId,
        LoadBalancerId: loadBalancerId,
      }
    });
    console.log(res);
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', marginTop: 8, marginBottom: 8 }}>
      <Select
        dataSource={slbList}
        label={
          <div
            style={{
              height: 26,
              lineHeight: '26px',
              borderRadius: 2,
              padding: '0 8px',
              marginLeft: -14,
              background: '#f7f9fa',
            }}
          >
            {intl('saenext.components.application.RcClbSelect.ClbInstance')}
          </div>
        }
        value={currentSlbId}
        state={loading ? 'loading' : null}
        style={{ minWidth: 290 }}
        onChange={(value: number) => {
          setCurrentSlbId(value);
          callback && callback(value);
          if (includes(ChargeTypes, value)) return;
          getLoadBalanceInfo(value);
        }}
      />

      <Button
        style={{
          marginRight: 16,
          borderLeft: 'none',
          borderTopLeftRadius: 0,
          borderBottomLeftRadius: 0,
        }}
        onClick={() => getAvailableSlbs(1, 20, [])}
      >
        {loading ? <Icon type="loading" /> : <Icon type="refresh" />}
      </Button>
      <ExternalLink
        url={`${CachedData.confLink('feature:slb:url')}/slb/${regionId}/slbs`}
        label={intl('saenext.components.application.RcClbSelect.CreateAClbInstance')}
      />
      <Balloon
        trigger={
          <div style={{ marginLeft: 16, cursor: 'help' }}>
            <span>{intl('saenext.components.application.RcClbSelect.ReuseClbDescription')}</span>
            <Icon type="help" size="xs" style={{ marginLeft: 8 }} />
          </div>
        }
        align="r"
        closable={false}
      >
        {intl('saenext.components.application.RcClbSelect.SaeCanOnlyReuseNon')}
      </Balloon>
    </div>
  );
};

export default RcClbSelect;
