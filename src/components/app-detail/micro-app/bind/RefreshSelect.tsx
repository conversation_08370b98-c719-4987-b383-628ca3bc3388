import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Select, Icon, LinkButton, Balloon, Checkbox } from '@ali/cnd';
import { get, map } from 'lodash';

export default (props) => {
  const { external, fetchMethod, ...selectProps } = props;
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState([]);

  const loadDataSource = async () => {
    setLoading(true);
    const _dataSource = await fetchMethod();
    setDataSource(_dataSource);
    setLoading(false);
  };

  const handleMenuProps = {
    footer: (
      <div style={{ padding: '0px 16px', borderTop: '1px solid #eee' }}>
        <LinkButton
          type="primary"
          onClick={() => {
            window.open(external.url, '_blank');
          }}
        >
          <span style={{ marginLeft: 2 }}>{external.label}</span>
        </LinkButton>
        <LinkButton style={{ marginLeft: 10 }} onClick={loadDataSource}>
          {loading ? <Icon size="xs" type="loading" /> : <Icon size="xs" type="refresh" />}

          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.application.RefreshSelect.Refresh')}
          </span>
        </LinkButton>
        <LinkButton
          type="primary"
          onClick={() => {
            setVisible(false);
          }}
          style={{ float: 'right' }}
        >
          {intl('saenext.components.application.RefreshSelect.Close')}
        </LinkButton>
      </div>
    ),
  };

  const handleItemRender = (item) => {
    return (
      <div>
        <Checkbox checked={selectProps.value?.indexOf(item.value) > -1} />
        {item.label}
      </div>
    );
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
      {get(selectProps, 'mode', 'single') === 'single' ? (
        <Select
          dataSource={dataSource}
          className="refresh-select"
          menuProps={handleMenuProps}
          {...selectProps}
          // @ts-ignore
          state={loading ? 'loading' : null}
          style={{
            flex: 1,
            ...selectProps?.style,
          }}
          popupClassName="refresh-popup-select"
          visible={visible}
          onVisibleChange={(val) => setVisible(val)}
        />
      ) : (
        <Select
          mode="multiple"
          className="refresh-select"
          itemRender={handleItemRender}
          menuProps={handleMenuProps}
          popupClassName="select-popup-checkbox"
          {...selectProps}
          // @ts-ignore
          state={loading ? 'loading' : null}
          style={{
            flex: 1,
            ...selectProps?.style,
          }}
          visible={visible}
          onVisibleChange={(val) => setVisible(val)}
        >
          {map(dataSource, (item) => (
            // @ts-ignore
            <Select.Option key={item.value} value={item.value}>
              {
                // @ts-ignore
                item.tip ? (
                  // @ts-ignore
                  <Balloon.Tooltip align="r" trigger={item.label}>
                    {/* @ts-ignore */}
                    {item?.tip}
                  </Balloon.Tooltip>
                ) : (
                  // @ts-ignore
                  <span>{item.label}</span>
                )
              }
            </Select.Option>
          ))}
        </Select>
      )}
    </div>
  );
};
