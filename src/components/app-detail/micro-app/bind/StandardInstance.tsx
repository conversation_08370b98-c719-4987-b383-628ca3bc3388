import { intl } from '@ali/cnd';
import React from 'react';
import { get } from 'lodash';
import { CndTable } from '@ali/cnd';
import CachedData from '../../../../cache/common';

const StandardInstance = (props) => {
  const { isCreate, isPublic, isReuseSlb, regionId, reuseSlbId } = props;

  if (!isCreate || isReuseSlb) return null;

  const regionName = React.useMemo(() => {
    return {
      'cn-hangzhou': intl('saenext.components.application.StandardInstance.EastChina'),
      'cn-qingdao': intl('saenext.components.application.StandardInstance.NorthChina'),
      'cn-beijing': intl('saenext.components.application.StandardInstance.NorthChina.1'),
      'cn-shenzhen': intl('saenext.components.application.StandardInstance.SouthChina'),
      'cn-shanghai': intl('saenext.components.application.StandardInstance.EastChina.1'),
    };
  }, []);

  const dataSource = React.useMemo(() => {
    const payType =
      reuseSlbId === -1
        ? intl('saenext.components.application.StandardInstance.PayBySpecification')
        : intl('saenext.components.application.StandardInstance.PayAsYouGo');
    return [
      {
        productType: (
          <>
            <span>{intl('saenext.components.application.StandardInstance.Slb')}</span>
            <div style={{ marginTop: 4 }}>
              {intl('saenext.components.application.StandardInstance.NetworkType')}
              {isPublic
                ? intl('saenext.components.application.StandardInstance.PublicNetwork')
                : intl('saenext.components.application.StandardInstance.PrivateNetwork')}
            </div>
          </>
        ),

        config: (
          <div>
            <span>
              {intl('saenext.components.application.StandardInstance.Region')}
              {get(regionName, regionId, '-')}
            </span>
            {isPublic ? (
              <div style={{ marginTop: 4 }}>
                {intl(
                  'saenext.components.application.StandardInstance.PublicNetworkBandwidthPayBy',
                )}
              </div>
            ) : null}
          </div>
        ),

        spec: (
          <>
            <div>
              {intl('saenext.components.application.StandardInstance.GuaranteedPerformance')}
            </div>
            <div style={{ marginTop: 4 }}>
              {intl('saenext.components.application.StandardInstance.StandardIiSlbSMedium')}
            </div>
          </>
        ),

        count: 1,
        priceType: intl('saenext.components.application.StandardInstance.PayAsYouGoPaytype', {
          payType: payType,
        }),
        time: intl('saenext.components.application.StandardInstance.None'),
        priceLink: (
          <a
            href={CachedData.confLink('help:slb:pay-as-you-go')}
            target="_blank"
          >
            {intl('saenext.components.application.StandardInstance.ViewPrices')}
          </a>
        ),
      },
    ];
  }, [isPublic, regionId, reuseSlbId]);

  return (
    <CndTable hasBorder={false} dataSource={dataSource} style={{ marginTop: 16, marginBottom: 16 }}>
      <CndTable.Column
        title={intl('saenext.components.application.StandardInstance.ProductCategory')}
        dataIndex="productType"
      />
      <CndTable.Column
        title={intl('saenext.components.application.StandardInstance.ProductConfiguration')}
        dataIndex="config"
      />
      {reuseSlbId === -1 ? (
        <CndTable.Column
          title={intl('saenext.components.application.StandardInstance.Specifications')}
          dataIndex="spec"
        />
      ) : null}

      <CndTable.Column
        title={intl('saenext.components.application.StandardInstance.Quantity')}
        dataIndex="count"
      />
      <CndTable.Column
        title={intl('saenext.components.application.StandardInstance.PaymentMethod')}
        dataIndex="priceType"
      />
      <CndTable.Column
        title={intl('saenext.components.application.StandardInstance.PurchaseCycle')}
        dataIndex="time"
      />
      <CndTable.Column
        title={intl('saenext.components.application.StandardInstance.Tariff')}
        dataIndex="priceLink"
      />
    </CndTable>
  );
};

export default StandardInstance;
