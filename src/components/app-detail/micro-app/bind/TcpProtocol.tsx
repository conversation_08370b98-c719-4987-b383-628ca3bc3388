import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { isEmpty, filter, map } from 'lodash';
import { LinkButton, Icon, NumberPicker, Button, CndTable } from '@ali/cnd';

const TcpProtocol = (props) => {
  let { dataSource: _dataSource, validatePort, updateEventTrigger } = props;
  const [dataSource, setDataSource] = useState(_dataSource);

  useEffect(() => {
    if (isEmpty(_dataSource)) {
      _dataSource = [
        { Port: undefined, TargetPort: undefined, Protocol: 'TCP', Key: Math.random() },
      ];
    }
    setDataSource(_dataSource);
  }, [_dataSource]);
  const renderPort = (value, index, record) => {
    const { disable = false } = record;
    return (
      <NumberPicker
        min={1}
        max={65535}
        value={value}
        disabled={disable}
        style={{ width: '100%' }}
        onBlur={(e) => checkPortDuplicated(value, record)}
        onChange={(val) => handleValueChange('Port', val, record)}
      />
    );
  };

  const renderContainerPort = (value, index, record) => {
    const { disable = false } = record;
    const _value = disable ? 'N/A' : value;
    return (
      <NumberPicker
        min={1}
        max={65535}
        value={_value}
        disabled={disable}
        style={{ width: '100%' }}
        onChange={(val) => handleValueChange('TargetPort', val, record)}
      />
    );
  };

  const renderActions = (value, index, record) => {
    return (
      <Button
        text
        iconSize="small"
        onClick={() => {
          let _dataSource = [...dataSource];
          _dataSource = filter(_dataSource, (item) => item.Key !== record.Key);
          setDataSource(_dataSource);
          updateEventTrigger(_dataSource);
        }}
      >
        <Icon type="delete" />
      </Button>
    );
  };

  const handleValueChange = (key, value, record) => {
    const _dataSource = map(dataSource, (item) => {
      if (item.Key === record.Key) {
        item[key] = value;
      }
      return item;
    });
    setDataSource(_dataSource);
    updateEventTrigger(_dataSource);
  };

  const checkPortDuplicated = (val, record) => {
    const result = validatePort && validatePort(record.Key, val);
    if (!result) {
      const _dataSource = map(dataSource, (item) => {
        if (item.Key === record.Key) {
          item.Port = undefined;
        }
        return item;
      });
      setDataSource(_dataSource);
    }
  };

  return (
    <>
      <CndTable
        dataSource={dataSource}
        hasBorder={false}
        style={{ marginTop: 8 }}
        columns={[
          {
            title:intl('saenext.components.application.TcpProtocol.NetworkProtocol'),
            width:'18%',
            dataIndex:'Protocol'
          },
          {
            title:intl('saenext.components.application.TcpProtocol.ClbPort'),
            width:'35%',
            dataIndex:'Port',
            cell:renderPort
          },
          {
            title:intl('saenext.components.application.TcpProtocol.ContainerPort'),
            width:'35%',
            dataIndex:'TargetPort',
            cell:renderContainerPort
          },
          {
            title:intl('saenext.components.application.TcpProtocol.Operation'),
            cell:renderActions
          }
        ]}
      />
      <LinkButton
        style={{ marginTop: 16 }}
        onClick={() => {
          const _dataSource = [...dataSource];
          _dataSource.push({
            Port: undefined,
            TargetPort: undefined,
            Protocol: 'TCP',
            Key: Math.random(),
          });
          setDataSource(_dataSource);
        }}
      >
        <Icon type="plus" size="xs" />
        <span style={{ marginLeft: 5 }}>
          {intl('saenext.components.application.TcpProtocol.AddTheNextListener')}
        </span>
      </LinkButton>
    </>
  );
};

export default TcpProtocol;
