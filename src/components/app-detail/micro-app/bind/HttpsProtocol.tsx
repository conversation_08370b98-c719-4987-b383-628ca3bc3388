import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { isEmpty, filter, map, cloneDeep } from 'lodash';
import RefreshSelect from './RefreshSelect';
import { UnAuthedLabel } from "../../../shared/unauthedLabel";
import { isForbidden } from "../../../../utils/authUtils";
import { CndTable, LinkButton, Icon, NumberPicker, Button, Switch } from '@ali/cnd';
import services from "~/services";
import CachedData from '../../../../cache/common';

const HttpsProtocol = (props) => {
  let { regionId, isSupportTls, dataSource: _dataSource, validatePort, updateEventTrigger } = props;
  const [dataSource, setDataSource] = useState(_dataSource);
  const [sslCertificationList, setSslCertificationList] = useState([]);
  const [caCertificationList, setCaCertificationList] = useState([]);
  const [authedSLB, setAuthSLB] = useState(true);

  useEffect(() => {
    getSslCertificates();
    getCaCertificates();
    if (isEmpty(_dataSource)) {
      _dataSource = isSupportTls
        ? [
          {
            Protocol: 'HTTPS',
            Port: undefined,
            HttpsCertId: undefined,
            isBind: false,
            HttpsCaCertId: undefined,
            TargetPort: undefined,
            Key: Math.random(),
          },
        ]
        : [
          {
            Protocol: 'HTTPS',
            Port: undefined,
            HttpsCertId: undefined,
            TargetPort: undefined,
            Key: Math.random(),
          },
        ];
    }
    setDataSource(_dataSource);
  }, [_dataSource]);

  const getSslCertificates = async () => {
    const res = await services.getServerCertificates({
      params: {
        RegionId: regionId,
      },
      customErrorHandle: (error, _p, cb) => {
        if (isForbidden(error.code)) {
          setAuthSLB(false);
        } else {
          setAuthSLB(true);
          cb();
        }
      }
    });
    const { ServerCertificates = {} } = res;
    const { ServerCertificate = [] } = ServerCertificates;
    const _sslCertificationList = map(ServerCertificate, (item) => ({
      label: item.ServerCertificateName,
      value: item.ServerCertificateId,
    }));
    // @ts-ignore
    setSslCertificationList(_sslCertificationList);
    return _sslCertificationList;
  };

  const getCaCertificates = async () => {
    const res = await services.getServerCaCertificates({
      params: {
        RegionId: regionId,
      },
      customErrorHandle: (error, _p, cb) => {
        if (isForbidden(error.code)) {
          setAuthSLB(false);
        } else {
          setAuthSLB(true);
          cb();
        }
      }
    });
    const { CACertificates = {} } = res;
    const { CACertificate = [] } = CACertificates;
    const _caCertificationList = map(CACertificate, (item) => ({
      label: item.CACertificateName,
      value: item.CACertificateId,
    }));
    // @ts-ignore
    setCaCertificationList(_caCertificationList);
    return _caCertificationList;
  };

  const renderPort = (value, index, record) => {
    const { disabled = false } = record;
    return (
      <NumberPicker
        min={1}
        max={65535}
        value={value}
        disabled={disabled}
        style={{ width: '100%' }}
        onBlur={() => checkPortDuplicated(value, record)}
        onChange={(val) => handleValueChange('Port', val, record)}
      />
    );
  };

  const renderCertItem = (item) => {
    const { label, value } = item;
    return (
      <div>
        <span>{label}</span>
        <span className='ml text-description'>{value}</span>
      </div>
    )
  };

  const renderSslCertification = (value, index, record) => {
    const { disabled = false } = record;
    return (
      <RefreshSelect
        value={value}
        disabled={disabled}
        placeholder={intl('saenext.components.application.HttpsProtocol.SelectAnSslCertificate')}
        fetchMethod={getSslCertificates}
        onChange={(val) => handleSslCertification(val, record)}
        dataSource={sslCertificationList}
        autoWidth={false}
        valueRender={renderCertItem}
        itemRender={renderCertItem}
        style={{ maxWidth: 210 }}
        external={{
          label: intl('saenext.components.application.HttpsProtocol.CreateAnSslCertificate'),
          url: `${CachedData.confLink('feature:slbnew.url')}/slb/${regionId}/certs`,
        }}
      />
    );
  };

  const renderBindSwitch = (value, index, record) => {
    const _checked = !!value;
    return <Switch checked={_checked} onChange={(val) => handleBindSwitch(val, record)} />;
  };

  const renderCaCertification = (value, index, record) => {
    const { disabled = false } = record;
    return (
      <RefreshSelect
        value={value}
        disabled={disabled || !record.isBind}
        placeholder={
          record.isBind
            ? intl('saenext.components.application.HttpsProtocol.SelectACaCertificate')
            : intl('saenext.components.application.HttpsProtocol.CaCertificateNotInvolved')
        }
        fetchMethod={getCaCertificates}
        onChange={(val) => handleCaCertification(val, record)}
        dataSource={caCertificationList}
        autoWidth={false}
        valueRender={renderCertItem}
        itemRender={renderCertItem}
        style={{ maxWidth: 210 }}
        external={{
          label: intl('saenext.components.application.HttpsProtocol.CreateACaCertificate'),
          url: `${CachedData.confLink('feature:slbnew.url')}/slb/${regionId}/certs`,
        }}
      />
    );
  };

  const renderContainerPort = (value, index, record) => {
    const { disabled = false } = record;
    const _value = disabled ? 'N/A' : value;
    return (
      <NumberPicker
        min={1}
        max={65535}
        value={_value}
        disabled={disabled}
        style={{ width: '100%' }}
        onChange={(val) => handleValueChange('TargetPort', val, record)}
      />
    );
  };

  const renderActions = (value, index, record) => {
    return (
      <Button
        text
        iconSize="small"
        onClick={() => {
          let _dataSource = [...dataSource];
          _dataSource = filter(_dataSource, (item) => item.Key !== record.Key);
          setDataSource(_dataSource);
          updateEventTrigger(_dataSource);
        }}
      >
        <Icon type="delete" />
      </Button>
    );
  };

  const handleValueChange = (key, value, record) => {
    const _dataSource = map(dataSource, (item) => {
      if (item.Key === record.Key) {
        item[key] = value;
      }
      return item;
    });
    setDataSource(_dataSource);
    updateEventTrigger(_dataSource);
  };

  const handleSslCertification = (val, record) => {
    const _dataSource = cloneDeep(dataSource);
    const index = _dataSource.findIndex((item) => item.Key === record.Key);
    if (index < 0) return;

    _dataSource[index].HttpsCertId = val;
    setDataSource(_dataSource);
    updateEventTrigger(_dataSource);
  };

  const handleBindSwitch = (val, record) => {
    const _dataSource = cloneDeep(dataSource);
    const index = _dataSource.findIndex((item) => item.Key === record.Key);
    if (index < 0) return;

    _dataSource[index].isBind = val;
    if (!val) {
      _dataSource[index].HttpsCaCertId = '';
      Reflect.deleteProperty(_dataSource[index], 'HttpsCaCertId');
    }
    setDataSource(_dataSource);
    updateEventTrigger(_dataSource);
  };

  const handleCaCertification = (val, record) => {
    const _dataSource = cloneDeep(dataSource);
    const index = _dataSource.findIndex((item) => item.Key === record.Key);
    if (index < 0) return;

    _dataSource[index].HttpsCaCertId = val;
    setDataSource(_dataSource);
    updateEventTrigger(_dataSource);
  };

  const checkPortDuplicated = (val, record) => {
    const result = validatePort && validatePort(record.Key, val);
    if (!result) {
      const _dataSource = map(dataSource, (item) => {
        if (item.Key === record.Key) {
          item.Port = undefined;
        }
        return item;
      });
      setDataSource(_dataSource);
    }
  };

  return (
    <>
      <CndTable hasBorder={false} style={{ marginTop: 8 }} dataSource={dataSource}>
        <CndTable.Column
          title={intl('saenext.components.application.HttpsProtocol.NetworkProtocol')}
          width="9%"
          dataIndex="Protocol"
        />

        <CndTable.Column
          title={intl('saenext.components.application.HttpsProtocol.HttpsPort')}
          width={isSupportTls ? '13%' : '22%'}
          dataIndex="Port"
          cell={renderPort}
        />

        <CndTable.Column
          title={
            <UnAuthedLabel
              text={intl('saenext.components.application.HttpsProtocol.SslCertificate')}
              authed={authedSLB}
              authKey='AliyunSLBReadOnlyAccess'
            />
          }
          width={isSupportTls ? '25%' : '40%'}
          dataIndex="HttpsCertId"
          cell={renderSslCertification}
        />

        {isSupportTls ? (
          <>
            <CndTable.Column
              title={intl('saenext.components.application.HttpsProtocol.TwoWayAuthentication')}
              width="10%"
              dataIndex="isBind"
              cell={renderBindSwitch}
            />

            <CndTable.Column
              title={
                <UnAuthedLabel
                  text={intl('saenext.components.application.HttpsProtocol.CaCertificate')}
                  authed={authedSLB}
                  authKey='AliyunSLBReadOnlyAccess'
                />
              }
              width="24%"
              dataIndex="HttpsCaCertId"
              cell={renderCaCertification}
            />
          </>
        ) : null}

        <CndTable.Column
          title={intl('saenext.components.application.HttpsProtocol.ContainerPort')}
          width={isSupportTls ? '13%' : '22%'}
          dataIndex="TargetPort"
          cell={renderContainerPort}
        />

        <CndTable.Column
          title={intl('saenext.components.application.HttpsProtocol.Operation')}
          cell={renderActions}
        />
      </CndTable>
      <LinkButton
        style={{ marginTop: 16 }}
        onClick={() => {
          const _dataSource = [...dataSource];
          const _item = {
            Protocol: 'HTTPS',
            Port: undefined,
            TargetPort: undefined,
            HttpsCertId: undefined,
            Key: Math.random(),
          };
          if (isSupportTls) {
            Reflect.set(_item, 'isBind', false);
            Reflect.set(_item, 'HttpsCaCertId', undefined);
          }
          _dataSource.push(_item);
          setDataSource(_dataSource);
        }}
      >
        <Icon type="plus" size="xs" />
        <span style={{ marginLeft: 5 }}>
          {intl('saenext.components.application.HttpsProtocol.AddTheNextListener')}
        </span>
      </LinkButton>
    </>
  );
};

export default HttpsProtocol;
