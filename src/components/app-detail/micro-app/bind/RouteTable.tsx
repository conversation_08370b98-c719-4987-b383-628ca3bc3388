import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { isEmpty, get, map, noop, forEach, head } from 'lodash';
import services from "~/services";
import { LinkButton, Truncate, Icon, Copy, Dialog, Tag, CndTable } from '@ali/cnd';
import CachedData from '../../../../cache/common';

const Gateway = {
  mse: 'mse',
  clb: 'clb',
  alb: 'alb',
  apig: 'apig',
};
const Matches = {
  PRE: intl('saenext.components.application.RouteTable.PrefixMatching'),
  EQUAL: intl('saenext.components.application.RouteTable.ExactMatch'),
  ERGULAR: intl('saenext.components.application.RouteTable.RegularMatching'),
};

const transferMatchTypeToMse = {
  Prefix: 'PRE',
  Exact: 'EQUAL',
  Regex: 'ERGULAR',
};

const { Group: TagGroup } = Tag;

const RouteTable = (props) => {
  const {
    regionId,
    ingressList,
    slbList,
    albList,
    apigList,
    mseGatewayList,
    transmitCallback = noop,
    toAppCallback = noop,
    namespaceId,
    permissionInfo,
  } = props;
  if (isEmpty(ingressList)) return null;
  const [loading, setLoading] = useState(false);
  const [combineIngress, setCombineIngress] = useState({});

  useEffect(() => {
    combineBlendIngress();
  }, [ingressList]);

  const combineBlendIngress = async () => {
    if (isEmpty(ingressList)) return;
    setLoading(true);
    const _combineIngress = {};
    for (let ingress of ingressList) {
      const ingressId = get(ingress, 'Id');
      const _ingress = await getIngress(ingress);
      _combineIngress[ingressId] = _ingress;
    }
    setLoading(false);
    setCombineIngress(_combineIngress);
  };

  const getIngress = async (ingress) => {
    const ingressId = get(ingress, 'Id');
    const loadBalance = get(ingress, 'LoadBalanceType');
    let _ingress = {};
    switch (loadBalance) {
      case Gateway.clb:
      case Gateway.alb:
        _ingress = await getSlbIngress(ingressId);
        break;
      case Gateway.mse:
        _ingress = await getMseIngress(ingressId);
        break;
      case Gateway.apig:
        _ingress = await getApigIngress(ingressId);
        break;
      default:
        return null;
    }
    return _ingress;
  };

  const getSlbIngress = async (ingressId) => {
    const res = await services.getIngressInfo({
      RegionId: regionId,
      IngressId: ingressId,
    });
    const _ingress = get(res, 'Data', {});
    return _ingress;
  };

  const getMseIngress = async (ingressId) => {
    const res = await services.describeMSEIngress(
      {
        RegionId: regionId,
        IngressId: ingressId,
      },
      (error, data, callback) => {
        const code = get(error, 'code');
        const meaasge = get(error, 'response.data.data.Message');
        const isDeletedCode = code === 'InvalidParameter.WithMessage';
        const isDeletedMsg = meaasge === 'The parameter is invalid {RouteId}: mse route not exist';
        // 判断是否删除了mse网关
        if (isDeletedCode && isDeletedMsg) return {};
        callback && callback();
      },
    );
    const _ingress = get(res, 'Data', {});
    return _ingress;
  };

  const getApigIngress = async (ingressId) => {
    const res = await services.getHttpApiRoute({
      params: { IngressId: ingressId, NamespaceId: namespaceId },
      customErrorHandle: (err, data, callback) => {},
    });
    const { Data = {} } = res;
    const { RouteId, Services = [], Domains = [], Predicates, Policies } = Data;
    const PathPredicates = get(Predicates, 'PathPredicates', {});
    const MethodPredicates = get(Predicates, 'MethodPredicates', []);
    let HeaderPredicates = get(Predicates, 'HeaderPredicates', []);
    let QueryPredicates = get(Predicates, 'QueryPredicates', []);
    forEach(HeaderPredicates, (headerItem) => {
      headerItem.Type = transferMatchTypeToMse[headerItem.Type];
      headerItem.Key = headerItem.Name;
    });
    forEach(QueryPredicates, (queryItem) => {
      queryItem.Type = transferMatchTypeToMse[queryItem.Type];
      queryItem.Key = queryItem.Name;
    });
    const Fallback = get(Policies, 'Fallback.Enable', false);
    const FallbackServices = head(get(Policies, 'Fallback.Destinations', [])) || {};
    let _routerDetail = {
      RouteId,
      DomainList: map(Domains, (domain) => domain.DomainName),
      RoutePredicates: {
        PathPredicates,
        MethodPredicates,
        HeaderPredicates,
        QueryPredicates,
      },
      RouteServices: Services,
      Fallback,
      FallbackServices,
    };
    return _routerDetail;
  };

  const renderRouteSlbItem = (value, __, record) => {
    const ingress = get(record, 'LoadBalanceType', '');
    switch (ingress) {
      case Gateway.alb:
        const alb = albList.find((item) => item.LoadBalancerId === value);
        const isExistalb = !isEmpty(alb);
        return isExistalb ? (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <LinkButton
              onClick={() =>
                window.open(
                  `${CachedData.confLink('feature:slb:url')}/alb/${regionId}/albs/${value}`,
                  '_blank',
                )
              }
            >
              <Copy text={alb.LoadBalancerId} style={{ whiteSpace: 'nowrap' }}>
                {alb.LoadBalancerId}
              </Copy>
            </LinkButton>
            <div>{alb.LoadBalancerName}</div>
          </div>
        ) : (
          <div style={{ display: 'flex', alignItems: 'center', minWidth: 160 }}>
            <Icon type="warning" size="xs" style={{ color: '#ff3333' }} />
            <span style={{ marginLeft: 4 }}>
              {!permissionInfo?.alb
                ? intl('saenext.components.application.RouteTable.TheCurrentUserLacksThe')
                : intl('saenext.components.application.RouteTable.TheIngressGatewayInstanceHas')}
            </span>
          </div>
        );

      case Gateway.clb:
        const slb = slbList.find((item) => item.LoadBalancerId === value);
        const isExistslb = !isEmpty(slb);
        return isExistslb ? (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <LinkButton
              onClick={() =>
                window.open(
                  `${CachedData.confLink('feature:slb:url')}/slb/${regionId}/slbs/${value}`,
                  '_blank',
                )
              }
            >
              <Copy text={slb.LoadBalancerId} style={{ whiteSpace: 'nowrap' }}>
                {slb.LoadBalancerId}
              </Copy>
            </LinkButton>
            <div>{slb.LoadBalancerName}</div>
          </div>
        ) : (
          <div style={{ display: 'flex', alignItems: 'center', minWidth: 160 }}>
            <Icon type="warning" size="xs" style={{ color: '#ff3333' }} />
            <span style={{ marginLeft: 4 }}>
              {!permissionInfo?.clb
                ? intl('saenext.components.application.RouteTable.TheCurrentUserLacksThe.1')
                : intl('saenext.components.application.RouteTable.TheIngressGatewayInstanceHas')}
            </span>
          </div>
        );

      case Gateway.mse:
        const { MseGatewayId } = record;
        const mse = mseGatewayList.find((item) => item.GatewayUniqueId === MseGatewayId);
        const isExistmse = !isEmpty(mse);
        return isExistmse ? (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <LinkButton
              onClick={() =>
                window.open(
                  `${CachedData.confLink('feature:mse:url')}/#/gateway/basicInfo?Id=${mse.GatewayUniqueId}&&region=${regionId}`,
                  '_blank',
                )
              }
            >
              <Copy text={mse.GatewayUniqueId} style={{ whiteSpace: 'nowrap' }}>
                <Truncate type="width" threshold={160}>
                  <span>{mse.GatewayUniqueId}</span>
                </Truncate>
              </Copy>
            </LinkButton>
            <div>{mse.Name}</div>
          </div>
        ) : (
          <div style={{ display: 'flex', alignItems: 'center', minWidth: 160 }}>
            <Icon type="warning" size="xs" style={{ color: '#ff3333' }} />
            <span style={{ marginLeft: 4 }}>
              {!permissionInfo?.mse
                ? intl('saenext.components.application.RouteTable.TheCurrentUserDoesNot')
                : intl('saenext.components.application.RouteTable.TheIngressGatewayInstanceHas')}
            </span>
          </div>
        );

      case Gateway.apig:
        const targetApig = apigList.find((item) => item.gatewayId === record?.MseGatewayId);
        const isExistApig = !isEmpty(targetApig);
        return isExistApig ? (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <LinkButton
              onClick={() =>
                window.open(
                  `${CachedData.confLink('feature:apigw:url')}/#/${regionId}/gateway/${targetApig.gatewayId}/detail`,
                  '_blank',
                )
              }
            >
              <Copy text={targetApig.gatewayId} style={{ whiteSpace: 'nowrap' }}>
                <Truncate type="width" threshold={160}>
                  <span>{targetApig.gatewayId}</span>
                </Truncate>
              </Copy>
            </LinkButton>
            <div>{targetApig.name}</div>
          </div>
        ) : (
          <div style={{ display: 'flex', alignItems: 'center', minWidth: 160 }}>
            <Icon type="warning" size="xs" style={{ color: '#ff3333' }} />
            <span style={{ marginLeft: 4 }}>
              {!permissionInfo?.apig
                ? intl('saenext.components.application.RouteTable.TheCurrentUserLacksThe.2')
                : intl('saenext.components.application.RouteTable.TheIngressGatewayInstanceHas')}
            </span>
          </div>
        );

      default:
        return <span>{value}</span>;
    }
  };

  const renderTransmitRules = (value, index, record) => {
    const ingress = get(combineIngress, value, {});
    if (isEmpty(ingress)) return '-';
    // @ts-ignore
    const { Rules = [], RouteId } = ingress;
    if (RouteId) {
      return (
        <div style={{ minWidth: 200 }}>
          <LinkButton onClick={() => mseTransmitRules(ingress)}>
            {intl('saenext.components.application.RouteTable.ForwardingPolicyDetails')}
          </LinkButton>
        </div>
      );
    }
    const protocol = get(ingress, 'ListenerProtocol');
    const port = get(ingress, 'ListenerPort');
    const showHttpsProtocol =
      record.LoadBalanceType === Gateway.alb && record.ListenerProtocol === 'HTTPS';
    return (
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {map(Rules, (rule) => {
          const {
            Domain: domain,
            Path: path,
            BackendProtocol: backendProtocol,
            AppName: appName,
            ContainerPort: containerPort,
            RuleActions,
          } = rule;
          const { rewriteConfig = {} as any, redirectConfig = {} as any } = handleRouterRulePolicy(
            RuleActions || [],
          );
          const href = `${protocol}://${domain}:${port}${path}`;
          const url = `${domain}:${port}${path}`;
          const _backendProtocol = `${backendProtocol || 'http'}://`;
          return (
            <div style={{ whiteSpace: 'nowrap', display: 'flex', alignItems: 'center' }}>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <LinkButton onClick={() => window.open(href, '_blank')}>
                  {isEmpty(rewriteConfig) ? url : `${domain}:${port}`}
                </LinkButton>
                {!isEmpty(rewriteConfig) && (
                  <div style={{ display: 'flex', color: '#0064c8' }}>
                    {`${intl("saenext.components.gateway.rewrite")}${rewriteConfig?.host}${handlePath(rewriteConfig?.path)}${
                      rewriteConfig?.query ? `?${rewriteConfig?.query}` : ''
                    }${intl("saenext.common.parenthesis.right")}`}
                  </div>
                )}
              </div>
              <div style={{ marginLeft: 16, marginRight: 16 }}>
                {isEmpty(redirectConfig)
                  ? intl('saenext.components.application.RouteTable.Forward')
                  : intl('saenext.components.application.RouteTable.Redirect')}
              </div>
              <div>
                {isEmpty(redirectConfig) ? (
                  <>
                    {showHttpsProtocol ? <span>{_backendProtocol}</span> : null}
                    <span>
                      {appName}:{containerPort}
                    </span>
                  </>
                ) : (
                  <>
                    <span>{`${redirectConfig?.protocol}//${redirectConfig?.host}${
                      redirectConfig?.port ? `:${redirectConfig?.port}` : ''
                    }${handlePath(redirectConfig.path)}${
                      redirectConfig?.query ? `?${redirectConfig?.query}` : ''
                    }`}</span>
                    {redirectConfig?.httpCode && (
                        <span style={{ marginLeft: 8 }}>
                          {intl('saenext.components.gateway.StatusCodeHttpcode', {
                              httpCode: redirectConfig?.httpCode,
                            })}
                        </span>
                    )}
                  </>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const handleRouterRulePolicy = (policyConfig) => {
    // 每条规则中重写(转发至)与重定向只会存在一种
    let rewriteConfig = {};
    let redirectConfig = {};
    forEach(policyConfig, (item) => {
      if (item.ActionType === 'rewrite') {
        rewriteConfig = JSON.parse(item?.ActionConfig);
      }
      if (item.ActionType === 'redirect') {
        redirectConfig = JSON.parse(item?.ActionConfig);
      }
    });
    return {
      rewriteConfig,
      redirectConfig,
    };
  };

  const handlePath = (path) => {
    if (path && !path.startsWith) {
      return `/${path}`;
    } else {
      return path;
    }
  };

  const renderInitTransmitRules = (value, __, record) => {
    const ingress = get(combineIngress, value, {});
    if (isEmpty(ingress)) return '-';
    const fallback = get(ingress, 'Fallback', false);
    if (fallback) {
      const fallbackServices = get(ingress, 'FallbackServices', {});
      const {
        // @ts-ignore
        AppId: appId,
        // @ts-ignore
        AppName: appName,
        // @ts-ignore
        ServiceName: serviceName,
        // @ts-ignore
        ServicePort: servicePort,
      } = fallbackServices;
      return (
        <>
          <span style={{ color: '#aaa' }}>
            {intl('saenext.components.application.RouteTable.WhenNoNodeIsAvailable')}
          </span>
          <LinkButton
            onClick={() => {
              toAppCallback && toAppCallback(appId, appName);
            }}
          >
            {appName}/{serviceName}:{servicePort}
          </LinkButton>
        </>
      );
    }

    const showHttpsProtocol = record.LoadBalanceType === Gateway.alb && record.CertIds;
    const backendProtocol = get(ingress, 'DefaultRule.BackendProtocol', 'http');
    const _backendProtocol = `${backendProtocol}://`;
    const appId = get(ingress, 'DefaultRule.AppId');
    const appName = get(ingress, 'DefaultRule.AppName');
    const containerPort = get(ingress, 'DefaultRule.ContainerPort');
    return (
      <>
        <span style={{ color: '#aaa' }}>
          {intl('saenext.components.application.RouteTable.AllRequestsThatDoNot')}
        </span>
        {showHttpsProtocol ? <span>{_backendProtocol}</span> : null}
        <LinkButton
          onClick={() => {
            toAppCallback && toAppCallback(appId, appName);
          }}
        >
          {appName}:{containerPort}
        </LinkButton>
      </>
    );
  };

  const mseTransmitRules = (ingress) => {
    const dataSource = get(ingress, 'RouteServices', []);
    const domains = get(ingress, 'DomainList', []);
    const type = get(ingress, 'RoutePredicates.PathPredicates.Type');
    const path = get(ingress, 'RoutePredicates.PathPredicates.Path');
    const ignoreCase = get(ingress, 'RoutePredicates.PathPredicates.IgnoreCase');
    const methodPredicates = get(ingress, 'RoutePredicates.MethodPredicates', []);
    const headerPredicates = get(ingress, 'RoutePredicates.HeaderPredicates', []);
    const queryPredicates = get(ingress, 'RoutePredicates.QueryPredicates', []);
    Dialog.show({
      title: intl('saenext.components.application.RouteTable.CustomForwardingPolicy'),
      content: (
        <CndTable hasBorder={false} dataSource={dataSource}>
          <CndTable.Column
            title={intl('saenext.components.application.RouteTable.DomainName')}
            cell={() => {
              if (isEmpty(domains)) return '-';
              return (
                <div>
                  {map(domains, (domain) => (
                    <span key={domain} style={{ marginLeft: 2, marginRight: 2 }}>
                      {domain}
                    </span>
                  ))}
                </div>
              );
            }}
          />

          <CndTable.Column
            title={intl('saenext.components.application.RouteTable.Path')}
            cell={() => (
              <div>
                <span>{Matches[type]}</span>
                <span style={{ marginLeft: 8, marginRight: 8 }}>{path}</span>
                <span>
                  {ignoreCase
                    ? intl('saenext.components.application.RouteTable.IgnoreCase')
                    : intl('saenext.components.application.RouteTable.CaseSensitive')}
                </span>
              </div>
            )}
          />

          <CndTable.Column
            title={intl('saenext.components.application.RouteTable.Method')}
            cell={() => {
              if (isEmpty(methodPredicates)) return '-';
              return (
                <TagGroup>
                  {map(methodPredicates, (method) => (
                    <Tag key={method} type="normal" size="small">
                      {method}
                    </Tag>
                  ))}
                </TagGroup>
              );
            }}
          />

          <CndTable.Column
            title={intl('saenext.components.application.RouteTable.RequestHeader')}
            cell={() => {
              if (isEmpty(headerPredicates)) return '-';
              return (
                <div>
                  {map(headerPredicates, (header) => (
                    <p>
                      <span>{header.Key}</span>
                      <span style={{ marginLeft: 8, marginRight: 8 }}>{Matches[header.Type]}</span>
                      <span>{header.Value}</span>
                    </p>
                  ))}
                </div>
              );
            }}
          />

          <CndTable.Column
            title={intl('saenext.components.application.RouteTable.RequestParametersQuery')}
            cell={() => {
              if (isEmpty(headerPredicates)) return '-';
              return (
                <div>
                  {map(queryPredicates, (query) => (
                    <p>
                      <span>{query.Key}</span>
                      <span style={{ marginLeft: 8, marginRight: 8 }}>{Matches[query.Type]}</span>
                      <span>{query.Value}</span>
                    </p>
                  ))}
                </div>
              );
            }}
          />

          <CndTable.Column title="" cell={() => <Icon type="arrow-right" size="xs" />} />

          <CndTable.Column
            title={intl('saenext.components.application.RouteTable.ApplicationsServices')}
            // @ts-ignore
            cell={(val, idx, record = {}) => (
              <LinkButton
                onClick={() => {
                  // @ts-ignore
                  toAppCallback && toAppCallback(record.AppId, record.AppName);
                }}
              >
                {/* @ts-ignore */}
                {record.AppName}/{record.ServiceName}:{record.ServicePort}
              </LinkButton>
            )}
          />
        </CndTable>
      ),

      footerActions: ['ok'],
    });
  };

  return (
    <>
      <CndTable
        primaryKey="Id"
        hasBorder={false}
        loading={loading}
        style={{ marginBottom: 8 }}
        dataSource={ingressList}
      >
        <CndTable.Column
          title={intl('saenext.components.application.RouteTable.NetworkType')}
          dataIndex="SlbType"
          width={120}
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <div style={{ minWidth: 60 }}>
                {value.toLowerCase() === 'internet'
                  ? intl('saenext.components.application.RouteTable.PublicNetwork')
                  : intl('saenext.components.application.RouteTable.PrivateNetwork')}
              </div>
            );
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.application.RouteTable.GatewayType')}
          dataIndex="LoadBalanceType"
          width={120}
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return <div style={{ minWidth: 60 }}>{value.toUpperCase()}</div>;
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.application.RouteTable.ProtocolType')}
          dataIndex="ListenerProtocol"
          width={120}
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return <div style={{ minWidth: 60 }}>{value}</div>;
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.application.RouteTable.IngressGatewayIp')}
          dataIndex="SlbId"
          width={200}
          cell={renderRouteSlbItem}
        />

        <CndTable.Column
          title={intl('saenext.components.application.RouteTable.CustomForwardingPolicy')}
          dataIndex="Id"
          cell={renderTransmitRules}
        />

        <CndTable.Column
          title={intl('saenext.components.application.RouteTable.DefaultForwardingPolicy')}
          dataIndex="Id"
          cell={renderInitTransmitRules}
        />

        <CndTable.Column
          title={intl('saenext.components.application.RouteTable.Operation')}
          width={100}
          cell={() => {
            return (
              <LinkButton style={{ width: 40 }} onClick={() => transmitCallback()}>
                {intl('saenext.components.application.RouteTable.Edit')}
              </LinkButton>
            );
          }}
        />
      </CndTable>
    </>
  );
};

export default RouteTable;
