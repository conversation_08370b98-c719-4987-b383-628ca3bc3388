import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { isEmpty, filter, map } from 'lodash';
import { CndTable, LinkButton, Icon, NumberPicker, Button } from '@ali/cnd';

const HttpProtocol = (props) => {
  let { dataSource: _dataSource, validatePort, updateEventTrigger } = props;
  const [dataSource, setDataSource] = useState(_dataSource);

  useEffect(() => {
    if (isEmpty(_dataSource)) {
      _dataSource = [
        { Port: undefined, TargetPort: undefined, Protocol: 'HTTP', Key: Math.random() },
      ];
    }
    setDataSource(_dataSource);
  }, [_dataSource]);

  const renderPort = (value, index, record) => {
    const { disabled = false } = record;
    return (
      <NumberPicker
        min={1}
        max={65535}
        value={value}
        disabled={disabled}
        style={{ width: '100%' }}
        onBlur={() => checkPortDuplicated(value, record)}
        onChange={(val) => handleValueChange('Port', val, record)}
      />
    );
  };

  const renderContainerPort = (value, index, record) => {
    const { disabled = false } = record;
    const _value = disabled ? 'N/A' : value;
    return (
      <NumberPicker
        min={1}
        max={65535}
        value={_value}
        disabled={disabled}
        style={{ width: '100%' }}
        onChange={(val) => handleValueChange('TargetPort', val, record)}
      />
    );
  };

  const renderActions = (value, index, record) => {
    return (
      <Button
        text
        iconSize="small"
        onClick={() => {
          let _dataSource = [...dataSource];
          _dataSource = filter(_dataSource, (item) => item.Key !== record.Key);
          setDataSource(_dataSource);
          updateEventTrigger(_dataSource);
        }}
      >
        <Icon type="delete" />
      </Button>
    );
  };

  const handleValueChange = (key, value, record) => {
    const _dataSource = map(dataSource, (item) => {
      if (item.Key === record.Key) {
        item[key] = value;
      }
      return item;
    });
    setDataSource(_dataSource);
    updateEventTrigger(_dataSource);
  };

  const checkPortDuplicated = (val, record) => {
    const result = validatePort && validatePort(record.Key, val);
    if (!result) {
      const _dataSource = map(dataSource, (item) => {
        if (item.Key === record.Key) {
          item.Port = undefined;
        }
        return item;
      });
      setDataSource(_dataSource);
    }
  };

  return (
    <>
      <CndTable hasBorder={false} style={{ marginTop: 8 }} dataSource={dataSource}>
        <CndTable.Column
          title={intl('saenext.components.application.HttpProtocol.NetworkProtocol')}
          width="18%"
          dataIndex="Protocol"
        />

        <CndTable.Column
          title={intl('saenext.components.application.HttpProtocol.HttpPort')}
          width="35%"
          dataIndex="Port"
          cell={renderPort}
        />

        <CndTable.Column
          title={intl('saenext.components.application.HttpProtocol.ContainerPort')}
          width="35%"
          dataIndex="TargetPort"
          cell={renderContainerPort}
        />

        <CndTable.Column
          title={intl('saenext.components.application.HttpProtocol.Operation')}
          cell={renderActions}
        />
      </CndTable>
      <LinkButton
        style={{ marginTop: 16 }}
        onClick={() => {
          const _dataSource = [...dataSource];
          _dataSource.push({
            Port: undefined,
            TargetPort: undefined,
            Protocol: 'HTTP',
            Key: Math.random(),
          });
          setDataSource(_dataSource);
        }}
      >
        <Icon type="plus" size="xs" />
        <span style={{ marginLeft: 5 }}>
          {intl('saenext.components.application.HttpProtocol.AddTheNextListener')}
        </span>
      </LinkButton>
    </>
  );
};

export default HttpProtocol;
