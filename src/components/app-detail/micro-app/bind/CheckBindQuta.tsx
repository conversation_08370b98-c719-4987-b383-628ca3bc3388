import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import { CndTable, Icon } from '@ali/cnd';
import services from "~/services";

const CheckBindQuta = (props) => {
  const { isCreate, isReuseSlb, isShowing, regionId } = props;
  if (!isCreate || isReuseSlb) return null;

  const [slbQuotaState, setSlbQuotaState] = useState(0); // 0：检测中， -1：失败， 1:成功
  const [accoutQutaState, setAccoutQutaState] = useState(0); // 0：检测中， -1：失败， 1:成功

  useEffect(() => {
    checkSlbQuota();
    // checkAccountQuta();
    checkAccountQuta_new();
  }, [isShowing]);

  const checkSlbQuota = async () => {
    if (!isCreate) return;
    if (!isShowing) return;
    const res = await services.getSlbQuota({
      params: {
        RegionId: regionId,
      },
      customErrorHandle: (error, data, callback) => {
        callback && callback();
        return {
          data: [],
          total: 0,
        };
      },
    });
    const current = get(res, 'Data.Current', 0);
    const upperBound = get(res, 'Data.UpperBound', 0);
    const _state = current < upperBound ? 1 : -1;
    setSlbQuotaState(_state);
  };

  const checkAccountQuta_new = async()=>{
    if (!isCreate) return;
    if (!isShowing) return;
    const res = await services.getAccountQuota(() => {
      setAccoutQutaState(1);
    });
    if(res.Success){
      const availableAmount = get(res, 'Data.AvailableAmount', 0);
      const availableAmountToNumber = parseFloat(availableAmount.replace(/,/g, ''));
      setAccoutQutaState(availableAmountToNumber > 0 ? 1 : -1);
    }else{
      setAccoutQutaState(1);
    }
  }

  const checkAccountQuta = () => {
    if (!isCreate) return;
    if (!isShowing) return;
    const _data = `[
      {
        orderType: 'instance-buy',
        regionId: '${regionId}',
        commodity: {
          zoneId: '${regionId}-a',
          instanceType: 'ecs.n1.large',
          ioOptimized: true,
          networkType: 'vpc',
          internetChargeType: 'PayByTraffic',
          internetMaxBandwidthOut: 100,
          systemDisk: { category: 'cloud_efficiency', size: 120 },
          dataDisks: [],
          priceUnit: 'Hour',
          period: 1,
          amount: 3
        }
      },
      { orderType: 'traffic-bandwidth', regionId: '${regionId}' }
    ]`;
    const url = 'https://ecs-buy.aliyun.com/api/ecsPrice/describePrice.jsonp';
    const base64Encode = (str) => {
      return btoa(
        encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
          // @ts-ignore
          return String.fromCharCode(`0x${p1}`);
        }),
      );
    };
    const data = base64Encode(_data);

    ((url, callback, data) => {
      const src = `${url}?callback=${callback}&data=${data}`;
      let scriptElement = document.createElement('script');
      document.body.appendChild(scriptElement);
      scriptElement.src = src;
    })(url, 'checkAccount', data);

    // @ts-ignore
    window.checkAccount = (res) => {
      const ErrorCode = [
        'PAY.INSUFFICIENT_BALANCE',
        'OWNING_FEE',
        'PAY.OWNING_FEE',
        'ARREARAGE',
        'MONEY_LESSTHAN_100',
      ];

      const code = get(res, 'data.order.code');
      const _state = ErrorCode.includes(code) ? -1 : 1;
      setAccoutQutaState(_state);
    };
  };

  const dataSource = React.useMemo(() => {
    return [
      {
        type: 'slb',
        project: intl('saenext.components.application.CheckBindQuta.ClbQuotaCheck'),
        intro:
          slbQuotaState === -1
            ? intl('saenext.components.application.CheckBindQuta.InsufficientClbQuota')
            : '-',
      },
      {
        type: 'price',
        project: intl('saenext.components.application.CheckBindQuta.AccountBalanceCheck'),
        intro:
          accoutQutaState === -1
            ? intl('saenext.components.application.CheckBindQuta.TheAccountBalanceIsLess')
            : '-',
      },
    ];
  }, [slbQuotaState, accoutQutaState]);

  return (
    <CndTable hasBorder={false} dataSource={dataSource}>
      <CndTable.Column
        title={intl('saenext.components.application.CheckBindQuta.CheckItems')}
        dataIndex="project"
        style={{ width: '30%' }}
      />

      <CndTable.Column
        title={intl('saenext.components.application.CheckBindQuta.Status')}
        dataIndex="type"
        style={{ width: '30%' }}
        cell={(value) => {
          const state = value === 'slb' ? slbQuotaState : accoutQutaState;
          let stateNode = (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span>{intl('saenext.components.application.CheckBindQuta.Checking')}</span>
              <Icon type="loading" size="xs" style={{ marginLeft: 4 }} />
            </div>
          );

          if (state === 1) {
            stateNode = (
              <div style={{ color: 'green', display: 'flex', alignItems: 'center' }}>
                <span>{intl('saenext.components.application.CheckBindQuta.Success')}</span>
                <Icon type="success-filling" size="xs" style={{ marginLeft: 4 }} />
              </div>
            );
          }
          if (state === -1) {
            stateNode = (
              <div style={{ color: 'red', display: 'flex', alignItems: 'center' }}>
                <span>{intl('saenext.components.application.CheckBindQuta.Failed')}</span>
                <Icon type="delete-filling" size="xs" style={{ marginLeft: 4 }} />
              </div>
            );
          }
          return stateNode;
        }}
      />

      <CndTable.Column
        title={intl('saenext.components.application.CheckBindQuta.Description')}
        dataIndex="intro"
      />
    </CndTable>
  );
};

export default CheckBindQuta;
