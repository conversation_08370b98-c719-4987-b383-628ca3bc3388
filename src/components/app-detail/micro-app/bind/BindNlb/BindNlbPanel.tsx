import React, { useState, useEffect } from 'react';
import {
  intl,
  SlidePanel,
  Form,
  Field,
  Select,
  Button,
  Radio,
  CndRcSelect,
  Icon,
  NumberPicker,
  Checkbox,
  Message,
  Balloon,
} from '@ali/cnd';
import services from "~/services";
import { isEmpty, get, map, filter, cloneDeep, has, set, some, startsWith } from 'lodash';
import CustomRadioButton from './CustomRadioButton';
import NlbSelect from './NlbSelect';
import CachedData from '~/cache/common';

const BindNlbPanel = (props) => {
  const { regionId, appId, appConfig, nlbInfo, nlbListenersMap, handleClose, handleOK } = props;
  const field = Field.useField();
  const [nlbFrom, setNlbFrom] = useState('use_add');
  const [nlbZones, setNlbZones] = useState([]);
  const [vpcInfo, setVpcInfo] = useState<any>({});
  const [selectVswitches, setSelectVsWitches] = useState<any>({});
  const [showMoreZones, setShowMoreZones] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false); // 高级配置后续补充
  const [validateVswitches, setValidateVswitches] = useState('');
  const isEdit = !isEmpty(nlbInfo);

  useEffect(() => {
    if (!isEdit) {
      regionId && getNlbZones();
      appConfig?.VpcId && getVpcInfo();
    }
  }, [regionId, appConfig?.VpcId]);

  useEffect(() => {
    if (!isEmpty(nlbInfo)) {
      field.setValues({
        ...nlbInfo,
      });
    }
  }, [JSON.stringify(nlbInfo)]);

  useEffect(() => {
    if (!isEdit && nlbFrom === 'use_add' && !isEmpty(selectVswitches)) {
      setValidateVswitches('');
    }
  }, [selectVswitches]);

  const getNlbZones = async () => {
    const res = await services.queryNlbZones({
      params: { RegionId: regionId },
      customErrorHandle: (error, data, callback) => {
        callback();
      },
    });
    const zones = get(res, 'Zones', []);
    setNlbZones(zones);
  };

  const getVpcInfo = async () => {
    const res = await services.DescribeVpcs({
      params: {
        VpcId: appConfig?.VpcId,
        RegionId: regionId
      },
      customErrorHandle: (error, data, callback) => {
        callback();
      },
    });
    setVpcInfo(get(res, 'Vpcs.Vpc[0]', {}));
  };

  const getVsiwtchs = async (search = '', page = 1, zoneId) => {
    if (!appConfig?.VpcId || !zoneId) {
      return;
    }
    const res = await services.DescribeVSwitches({
      params: {
        VpcId: appConfig?.VpcId,
        ZoneId: zoneId,
      },
      customErrorHandle: (error, data, callback) => {
        callback();
      },
    });
    const _vSwitchs = map(get(res, 'VSwitches.VSwitch', []), (item) => {
      return {
        label: item.VSwitchName,
        value: item.VSwitchId,
        ...item,
      };
    });
    return {
      data: _vSwitchs || [],
      total: get(res, 'TotalCount', 0),
    };
  };

  const getYundunCertificates = async () => {
    const { CertMetaList, TotalCount } = await services.getSSLCertificateList({
      params: {
        ShowSize: 99,
        CurrentPage: 1,
        RegionId: regionId,
      }
    });
    const _certificates = CertMetaList.map((item) => ({
      label: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: '#333' }}>{item.CommonName}</span>
          <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
          <span style={{ color: '#666' }}>{item.CertName}</span>
        </div>
      ),
      value: item.CertIdentifier,
      ...item,
    }));
    return {
      data: _certificates,
      total: TotalCount,
    };
  };

  const getNlbInstances = async (search, pageNumber = 1) => {
    const res = await services.getNlbLoadBalancers({
      params: {
        RegionId: regionId,
        MaxResults: 50,
      },
      customErrorHandle: (error, data, callback) => {
        callback();
      },
    });
    const { LoadBalancers = [], TotalCount = 0 } = res;
    const _loadBalancers = filter(LoadBalancers, (item) => {
      return item.LoadBalancerStatus === 'Active' && item.VpcId === appConfig?.VpcId;
    });
    const _nlbInstances = _loadBalancers.map((item) => ({
      label: item.LoadBalancerName,
      value: item.LoadBalancerId,
      ...item,
    }));
    return {
      data: _nlbInstances,
      total: TotalCount,
    };
  };

  const handleSubmit = () => {
    const { validateMsg, vswitchIds } = validateVswitch();
    if (!isEdit && nlbFrom === 'use_add') {
      setValidateVswitches(validateMsg);
      if (validateMsg) {
        setSubmitLoading(false);
        return;
      }
    }
    field.validate(async (error, values) => {
      if (!error) {
        setSubmitLoading(true);
        const validate_listeners = await handleValidateListeners(values);
        if (validate_listeners) {
          let params = {};
          const listeners = [
            {
              Port: values.Port,
              Protocol: values.Protocol,
              CertIds: values.CertIds || '',
              TargetPort: values.TargetPort,
            },
          ];

          if (!isEdit && nlbFrom === 'use_add') {
            params = {
              AppId: appId,
              Listeners: JSON.stringify(listeners),
              AddressType: values.AddressType,
              ZoneMappings: JSON.stringify(vswitchIds),
            };
          }
          if ((!isEdit && nlbFrom === 'use_already') || isEdit) {
            params = {
              AppId: appId,
              NlbId: values.NlbId,
              Listeners: JSON.stringify(listeners),
            };
          }
          const res = await services.BindNlb({
            params: params,
          });
          setSubmitLoading(false);
          if (res?.Success) {
            handleOK();
          }
        } else {
          setSubmitLoading(false);
        }
      }
    });
  };

  const validateVswitch = () => {
    if (isEmpty(selectVswitches)) {
      return {
        validateMsg: intl('saenext.application.BindNlb.BindNlbPanel.SelectAVirtualSwitch'),
        vswitchIds: [],
      };
    }
    let _vswitchIds = [];
    for (let k in selectVswitches) {
      const item = selectVswitches[k];
      if (item?.vSwitchId) {
        _vswitchIds.push({
          VswitchId: item.vSwitchId,
          ZoneId: k,
        });
      }
    }
    let msg = '';
    if (_vswitchIds.length === 0) {
      msg = intl('saenext.application.BindNlb.BindNlbPanel.SelectAVirtualSwitch');
    }
    if (_vswitchIds.length === 1) {
      msg = intl('saenext.application.BindNlb.BindNlbPanel.SelectAtLeastOrMore');
    }
    return {
      validateMsg: msg,
      vswitchIds: _vswitchIds,
    };
  };

  const handleValidateListeners = (values) => {
    if (!isEdit && nlbFrom === 'use_add') {
      return true;
    }
    const nlbListeners = nlbListenersMap.current.get(values.NlbId)?.Listeners || {};
    // 判断所选实例是否已创建监听 && 是否存在同样的监听
    if (!isEdit && nlbFrom === 'use_already') {
      if (!isEmpty(nlbListeners) && has(nlbListeners, `${values.Protocol}:${values.Port}`)) {
        Message.error(
          intl('saenext.application.BindNlb.BindNlbPanel.TheInstanceAlreadyHasA', {
            valuesProtocol: values.Protocol,
            valuesPort: values.Port,
          }),
        );
        return false;
      } else {
        return true;
      }
    }
    if (isEdit) {
      // 判断是否已存在同样监听
      const filterNlbList = filter(nlbInfo.listeners || [], (item) => {
        return !(item.Port === nlbInfo.Port && item.Protocol === nlbInfo.Protocol);
      });
      const isRepeat = some(
        filterNlbList,
        (item) => item.Port === values.Port && item.Protocol === values.Protocol,
      );
      if (isRepeat) {
        Message.error(
          intl(
            'saenext.application.BindNlb.BindNlbPanel.AValuesprotocolValuesportListenerAlready',
            { valuesProtocol: values.Protocol, valuesPort: values.Port },
          ),
        );
        return false;
      } else {
        return true;
      }
    }
  };

  return (
    <SlidePanel
      isShowing={true}
      isProcessing={submitLoading}
      title={
        !isEdit
          ? intl('saenext.application.BindNlb.BindNlbPanel.AddLbAccess')
          : intl('saenext.application.BindNlb.BindNlbPanel.EditLbAccess')
      }
      onOk={handleSubmit}
      onCancel={handleClose}
      onClose={handleClose}
      okText={intl('saenext.application.BindNlb.BindNlbPanel.Ok')}
      cancelText={intl('saenext.application.BindNlb.BindNlbPanel.Cancel')}
    >
      <Form field={field} labelCol={{ span: 6 }}>
        {!isEdit && (
          <Form.Item
            label={intl('saenext.application.BindNlb.BindNlbPanel.InstanceSource')}
            extra={
              <>
                {nlbFrom === 'use_add' ? (
                  <div style={{ marginTop: 4 }}>
                    {intl(
                      'saenext.application.BindNlb.BindNlbPanel.TheSystemAutomaticallyCreatesA',
                    )}

                    <a href={CachedData.confLink('help:slb:nlb-billing-overview')} target="_blank">
                      {intl('saenext.application.BindNlb.BindNlbPanel.NlbBillingInstructions')}
                    </a>
                  </div>
                ) : (
                  <></>
                )}
              </>
            }
          >
            <CustomRadioButton
              name="nlbFrom"
              dataSource={[
                {
                  label: intl('saenext.application.BindNlb.BindNlbPanel.CreateAnInstance'),
                  value: 'use_add',
                },
                {
                  label: intl('saenext.application.BindNlb.BindNlbPanel.UseExisting'),
                  value: 'use_already',
                },
              ]}
              defaultValue="use_add"
              onChange={(v) => {
                setNlbFrom(v);
                if (v === 'use_add') {
                  setSelectVsWitches([]);
                } else {
                  field.setValues({
                    NlbId: '',
                  });
                }
                field.setValues({
                  AddressType: 'internet',
                  Protocol: 'TCP',
                  Port: null,
                  CertIds: '',
                });
              }}
            />
          </Form.Item>
        )}
        {!isEdit && nlbFrom === 'use_add' && (
          <Form.Item label={intl('saenext.application.BindNlb.BindNlbPanel.NetworkType')} required>
            <CustomRadioButton
              name="AddressType"
              dataSource={[
                {
                  label: intl('saenext.application.BindNlb.BindNlbPanel.PublicNetwork'),
                  value: 'internet',
                },
                {
                  label: intl('saenext.application.BindNlb.BindNlbPanel.PrivateNetwork'),
                  value: 'intranet',
                },
              ]}
              defaultValue="internet"
            />
          </Form.Item>
        )}
        {!isEdit && nlbFrom === 'use_add' && (
          <>
            {' '}
            <Form.Item label={intl('saenext.application.BindNlb.BindNlbPanel.Vpc')}>
              <Balloon
                trigger={
                  <Select
                    name="VpcId"
                    value={appConfig?.VpcId}
                    dataSource={[
                      { label: vpcInfo?.VpcName || vpcInfo?.VpcId, value: vpcInfo?.VpcId },
                    ]}
                    disabled
                    style={{ width: '100%' }}
                  />
                }
                closable={false}
              >
                {intl('saenext.application.BindNlb.BindNlbPanel.EnsureThatTheNlbInstance')}
              </Balloon>
            </Form.Item>
            <Form.Item
              label={intl('saenext.application.BindNlb.BindNlbPanel.VirtualSwitch')}
              required
              extra={
                <div>
                  <div>
                    {intl('saenext.application.BindNlb.BindNlbPanel.ToEnsureHighAvailabilityOf')}
                  </div>
                  {validateVswitches && <div style={{ color: 'red' }}>{validateVswitches}</div>}
                </div>
              }
            >
              {nlbZones.map((zone: any, index) => {
                return (
                  <>
                    {showMoreZones || index < 3 ? (
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom:
                            nlbZones.length > 3
                              ? (showMoreZones && index === nlbZones.length - 1) ||
                                (!showMoreZones && index === 2)
                                ? 0
                                : 8
                              : 8,
                        }}
                      >
                        <Checkbox
                          key={zone?.ZoneId}
                          onChange={(v) => {
                            if (v) {
                              if (!has(selectVswitches, zone?.ZoneId)) {
                                let _selectVswitches: any = cloneDeep(selectVswitches);
                                _selectVswitches[zone?.ZoneId] = {
                                  vSwitchId: '',
                                };
                                setSelectVsWitches(_selectVswitches);
                              }
                            } else {
                              let _selectVswitches: any = cloneDeep(selectVswitches);
                              delete _selectVswitches[zone?.ZoneId];
                              setSelectVsWitches(_selectVswitches);
                            }
                          }}
                          checked={has(selectVswitches, zone?.ZoneId)}
                        >
                          {zone?.LocalName}
                        </Checkbox>
                        {has(selectVswitches, zone?.ZoneId) && (
                          <CndRcSelect
                            fetchData={(search, pageNumber) =>
                              // @ts-ignore
                              getVsiwtchs(search, pageNumber, zone?.ZoneId)
                            }
                            value={get(selectVswitches, `${zone.ZoneId}.vSwitchId`)}
                            onChange={(v) => {
                              let _selectVswitches: any = cloneDeep(selectVswitches);
                              set(_selectVswitches, `${zone.ZoneId}.vSwitchId`, v);
                              setSelectVsWitches(_selectVswitches);
                            }}
                            style={{ width: '75%' }}
                            menuHeaderProps={{
                              headerProps: {
                                // @ts-ignore
                                text: (
                                  <div className="flex-center">
                                    {intl('saenext.application.BindNlb.BindNlbPanel.ToCreate')}
                                    <Icon type="external_link" size="xs" className="ml-xs" />
                                  </div>
                                ),
                                clickFunc: () => {
                                  window.open(
                                    `${CachedData.confLink(
                                      'feature:vpc:url',
                                    )}/vpc/${regionId}/switches?VpcId=${appConfig?.VpcId}&ZoneId=${
                                      zone.ZoneId
                                    }`,
                                    '_balnk',
                                  );
                                },
                              },
                            }}
                          />
                        )}
                      </div>
                    ) : null}
                  </>
                );
              })}
              {nlbZones.length > 3 && (
                <Button
                  type="primary"
                  text
                  onClick={() => setShowMoreZones(!showMoreZones)}
                  className="mt-s mb-s"
                >
                  <span>{intl('saenext.application.BindNlb.BindNlbPanel.ShowMore')}</span>
                  <Icon type={showMoreZones ? 'arrow-up' : 'arrow-down'} />
                </Button>
              )}
            </Form.Item>
          </>
        )}
        {((!isEdit && nlbFrom === 'use_already') || isEdit) && (
          <Form.Item
            label={intl('saenext.application.BindNlb.BindNlbPanel.NlbInstance')}
            required
            requiredMessage={intl('saenext.application.BindNlb.BindNlbPanel.SelectAnNlbInstance')}
          >
            <NlbSelect
              name="NlbId"
              fetchData={getNlbInstances}
              disabled={isEdit}
              style={{ width: isEdit ? '100%' : '80%', marginRight: 8 }}
              onChange={(v) => {
                field.reset('NlbId');
                field.setValue('NlbId', v);
              }}
              href={isEdit ? '' : CachedData.confLink('feature:commonbuy:slb_nlb', { regionId: regionId })}
            />
          </Form.Item>
        )}
        <Form.Item label={intl('saenext.application.BindNlb.BindNlbPanel.ProtocolType')}>
          <CustomRadioButton
            name="Protocol"
            dataSource={[
              {
                label: 'TCP',
                value: 'TCP',
              },
              {
                label: 'UDP',
                value: 'UDP',
              },
              {
                label: 'TCPSSL',
                value: 'TCPSSL',
              },
            ]}
            defaultValue="TCP"
            onChange={(v) => {
              if (v !== 'TCPSSL') {
                field.remove('CertIds');
              }
            }}
            disabled={isEdit}
          />
        </Form.Item>
        <Form.Item
          label={intl('saenext.application.BindNlb.BindNlbPanel.ListeningPort')}
          required
          requiredMessage={intl('saenext.application.BindNlb.BindNlbPanel.EnterTheListeningPort')}
        >
          <NumberPicker
            // @ts-ignore
            name="Port"
            min={1}
            max={65535}
            placeholder={intl('saenext.application.BindNlb.BindNlbPanel.EnterTheListeningPortThe')}
            style={{ width: '100%' }}
            disabled={isEdit}
          />
        </Form.Item>
        <Form.Item
          label={intl('saenext.application.BindNlb.BindNlbPanel.ContainerPort')}
          required
          requiredMessage={intl('saenext.application.BindNlb.BindNlbPanel.EnterTheContainerPort')}
        >
          <NumberPicker
            // @ts-ignore
            name="TargetPort"
            min={1}
            max={65535}
            placeholder={intl('saenext.application.BindNlb.BindNlbPanel.EnterTheContainerPortThe')}
            style={{ width: '100%' }}
          />
        </Form.Item>
        {field.getValue('Protocol') === 'TCPSSL' && (
          <Form.Item
            label={intl('saenext.application.BindNlb.BindNlbPanel.SelectSslCertificate')}
            required
            requiredMessage={intl(
              'saenext.application.BindNlb.BindNlbPanel.SelectAnSslCertificate',
            )}
          >
            <CndRcSelect
              name="CertIds"
              fetchData={getYundunCertificates}
              style={{ width: '100%' }}
              followTrigger={false}
              menuHeaderProps={{
                headerProps: {
                  // @ts-ignore
                  text: (
                    <div className="flex-center">
                      {intl('saenext.application.BindNlb.BindNlbPanel.ToCreate')}
                      <Icon type="external_link" size="xs" className="ml-xs" />
                    </div>
                  ),
                  clickFunc: () => {
                    window.open(
                      `${CachedData.confLink('feature:yundunnext:url')}/?&p=cas#/certExtend/upload/${regionId}`,
                      '_balnk',
                    );
                  },
                },
              }}
            />
          </Form.Item>
        )}
        {/* TODO 高级配置待后期完善 */}
        {/* <Button type="primary" text onClick={() => setShowAdvancedConfig(!showAdvancedConfig)}>
             <span>高级配置</span>
             <Icon type={showAdvancedConfig ? 'arrow-up' : 'arrow-down'} />
            </Button>
            <Form.Item
             label={
               <TextWithBalloon
                 text="连接空闲超时时间"
                 tips="在超时时间内一直没有访问请求，负载均衡会暂时中断当前连接，直到下一次请求来临时重新建立新的连接"
               />
             }
             help="输入范围为1~900秒"
             hidden={!showAdvancedConfig}
            >
             <NumberPicker
               // @ts-ignore
               name="idleTimeout"
               min={1}
               max={999}
               defaultValue={999}
               innerAfter="秒"
               style={{ width: '100%' }}
             />
            </Form.Item> */}
      </Form>
    </SlidePanel>
  );
};
export default BindNlbPanel;
