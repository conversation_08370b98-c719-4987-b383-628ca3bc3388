import React from 'react';
import './index.less';

const CustomRadioButton = props => {
  const { dataSource = [], value = '', onChange, disabled = false } = props;
  return (
    <div className="custom-radio-button">
      {dataSource.map((item, index) => {
        return (
          <div
            className={`custom-radio-item ${value === item.value ? 'active-raido' : ''} ${disabled ? 'disabled-radio' : ''}`}
            style={{ marginRight: index === dataSource.length - 1 ? 0 : 8 }}
            key={item.value}
            onClick={() => {
              if(!disabled){
                onChange(item.value);
              }
            }}
          >
            {item.label}
          </div>
        );
      })}
    </div>
  );
};

export default CustomRadioButton;
