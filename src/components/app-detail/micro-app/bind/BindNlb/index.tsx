import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  intl,
  Message,
  CndTable,
  Button,
  Actions,
  LinkButton,
  Dialog,
  Instance,
  CndStatus,
  Icon,
} from '@ali/cnd';
import services from "~/services";
import { get, includes, startsWith, isEmpty, has, cloneDeep } from 'lodash';
import BindNlbPanel from './BindNlbPanel';
import AddListeners from './AddListeners';
import CachedData from '../../../../../cache/common';

const ListernerStatusMap = {
  Bounded: {
    label: intl('saenext.application.BindNlb.Running'),
    type: 'success',
  },
  Creating: {
    label: intl('saenext.application.BindNlb.Creating'),
    type: 'loading',
  },
  Configuring: {
    label: intl('saenext.application.BindNlb.Updating'),
    type: 'loading',
  },
  Unbinding: {
    label: intl('saenext.application.BindNlb.Deleting'),
    type: 'loading',
  },
  Failed: {
    label: intl('saenext.application.BindNlb.Failed'),
    type: 'error',
  },
};

const BindNlb = (props) => {
  const { regionId, appId, appConfig } = props;
  const [bindNlbVisible, setBindNlbVisible] = useState(false);
  const [currentNlbInfo, setCurrentNlbInfo] = useState({});
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [loopEnable, setLoopEnable] = useState(false);
  const [certificationList, setCertificationList] = useState([]);
  const [addListenersVisible, setAddListenersVisible] = useState(false);
  const [loopNLbInstance, setLoopNLbInstance] = useState(false);
  const [nlbInstanceMap,setNlbInstanceMap] = useState(new Map());
  const nlbListenersMap = useRef(new Map());
  const changingListenerRef = useRef({});
  const timerRef = useRef<any>();
  const changingListener = localStorage.getItem('changingListener')? JSON.parse(localStorage.getItem('changingListener')) : {};

  useEffect(() => {
    getYundunCertificates();
    getNlbInstances();
  }, []);

  useEffect(()=>{
    if(loopNLbInstance){
      timerRef.current = setInterval(getNlbInstances, 5000);
    }else{
      clearInterval(timerRef.current);
      timerRef.current = null
    }
    return ()=>{
      clearInterval(timerRef.current);
      timerRef.current = null
    }
  },[loopNLbInstance])

  const getNlbInstances = async () => {
    const res = await services.getNlbLoadBalancers({
      params: {
        RegionId: regionId,
        MaxResults: 50,
      },
      customErrorHandle: (error, data, callback) => {
        callback();
      },
    });
    let _nlbInstanceMap = new Map();
    const { LoadBalancers = [] } = res;
    LoadBalancers.forEach((item) => {
      _nlbInstanceMap.set(item.LoadBalancerId, {
        ...item,
      });
    });
    setNlbInstanceMap(_nlbInstanceMap);
  };
  const getYundunCertificates = async () => {
    const { CertMetaList, TotalCount } = await services.getSSLCertificateList({
      params: {
        ShowSize: 99,
        CurrentPage: 1,
        RegionId: regionId,
      },
    });
    const _certificates = CertMetaList.map(item => ({
      label: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: '#333' }}>{item.CommonName}</span>
          <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
          <span style={{ color: '#666' }}>{item.CertName}</span>
        </div>
      ),
      value: item.CertIdentifier,
      ...item,
    }));
    setCertificationList(_certificates);
  };
  const getNlbDataSource = async () => {
    const res = await services.DescribeApplicationNlbs({
      params: {
        AppId: appId,
      },
    });
    const instances = get(res, 'Data.Instances', {});
    let _instanceListeners = [];
    let _loopEnable = false;
    let _loopNLbInstance = false;
    for (let key in instances) {
      const dnsName = instances[key]?.DnsName;
      let listeners = get(instances[key], 'Listeners', {});
      // listeners 先按照协议排序 TCP>UDP>TCPSSL 然后按照 Port 从小到大排序
      if (!isEmpty(listeners)) {
        listeners = handleSortListeners(listeners);
      }
      let _listeners = [];
      for (let listenerKey in listeners) {
        if (includes(['Creating', 'Configuring', 'Unbinding'], listeners[listenerKey]?.Status)) {
          _loopEnable = true;
        }
        if(includes(['Creating'], listeners[listenerKey]?.Status) && !nlbInstanceMap.get(key)){
          _loopNLbInstance = true;
        }
        _listeners.push({
          ...listeners[listenerKey],
        });
      }
      _listeners.forEach((item, index) => {
        // Creating/Unbinding 持续超过5分钟就设置为失败
        if (includes(['Creating', 'Configuring', 'Unbinding'], item.Status)) {
          if (has(changingListener, `${key}-${item.Protocol}:${item.Port}`)) {
            const diff_5_minutes = 5 * 60 * 1000;
            const diff_time =
              Date.now() - changingListener[`${key}-${item.Protocol}:${item.Port}`];
            if (diff_time >= diff_5_minutes) {
              item.Status = 'Failed';
            }
          } else {
            const _changingListener = cloneDeep(changingListener);
            Reflect.set(_changingListener, `${key}-${item.Protocol}:${item.Port}`, Date.now());
            changingListenerRef.current[`${key}-${item.Protocol}:${item.Port}`] = Date.now();
            localStorage.setItem('changingListener', JSON.stringify(_changingListener));
          }
        } else {
          if (has(changingListener, `${key}-${item.Protocol}:${item.Port}`)) {
            const _changingListener = cloneDeep(changingListener)
            delete _changingListener[`${key}-${item.Protocol}:${item.Port}`];
            localStorage.setItem('changingListener', JSON.stringify(_changingListener));
          }
        }
        _instanceListeners.push({
          ...item,
          listeners: _listeners,
          NlbId: key,
          DnsName: dnsName,
          rowSpan: index === 0 ? _listeners.length + 1 : 0,
        });
      });
      // 手动添加 实例级监听创建入口
      if (_listeners.length > 0) {
        _instanceListeners.push({
          colSpan: 5,
          listeners: _listeners,
          NlbId: key,
          DnsName: dnsName,
        });
      }
      nlbListenersMap.current.set(key, {
        ...instances[key],
        DnsName: dnsName,
        NlbId: key,
      });
    }
    setLoopEnable(_loopEnable);
    setLoopNLbInstance(_loopNLbInstance);
    return {
      data: _instanceListeners,
      total: _instanceListeners.length,
    };
  };

  const handleSortListeners = (listeners) => {
    const listenersArray = Object.values(listeners);
    const protocolOrder = { TCP: 1, UDP: 2, TCPSSL: 3 };
    // 排序
    listenersArray.sort((a: any, b: any) => {
      // 按照协议排序
      if (protocolOrder[a.Protocol] !== protocolOrder[b.Protocol]) {
        return protocolOrder[a.Protocol] - protocolOrder[b.Protocol];
      }
      // 如果协议相同，则按照端口排序
      return a.Port - b.Port;
    });
    // 将排序后的数组转换回对象
    const sortedListeners = {};
    listenersArray.forEach((listener: any) => {
      const key = `${listener.Protocol}:${listener.Port}`;
      sortedListeners[key] = listener;
    });

    return sortedListeners;
  };

  const deleteNlb = async (info) => {
    let isNlbLastListener = false;
    const { Listeners = {}, CreatedBySae = false } = nlbListenersMap.current.get(info.NlbId);
    if (Object.keys(Listeners).length === 1 && CreatedBySae) {
      isNlbLastListener = true;
    }
    Dialog.confirm({
      title: intl('saenext.application.BindNlb.DeleteConfirmation'),
      content: (
        <div>
          <div>
            {intl('saenext.application.BindNlb.MakeSureYouWantTo', {
              infoProtocol: info.Protocol,
              infoPort: info.Port,
            })}
          </div>
          {isNlbLastListener && (
            <Message type="warning" className="mt-s">
              {intl('saenext.application.BindNlb.SaeDoesNotAutomaticallyDelete')}
            </Message>
          )}
        </div>
      ),

      onOk: async () => {
        const res = await services.UnbindNlb({
          params: {
            AppId: appId,
            NlbId: info.NlbId,
            Protocol: info.Protocol,
            Port: info.Port,
          },
        });
        if (res?.Success) {
          setRefreshIndex(Date.now());
          if (has(changingListener, `${info.NlbId}-${info.Protocol}:${info.Port}`)) {
            const _changingListener = cloneDeep(changingListener)
            delete _changingListener[`${info.NlbId}-${info.Protocol}:${info.Port}`];
            localStorage.setItem('changingListener', JSON.stringify(_changingListener));
          }
        }
      },
    });
  };

  const columns = useMemo(()=>{
    return [
      {
        key: 'NlbId',
        title: intl('saenext.application.BindNlb.InstanceIdDnsName'),
        dataIndex: 'NlbId',
        width: 300,
        cell: (val, index, record) => {
          return (
            <Instance
              link={{
                value: val,
                onClick: () => {
                  window.open(
                    `${CachedData.confLink('feature:slbnew.url')}/nlb/${regionId}/nlbs/${val}`,
                    '_blank',
                  );
                },
              }}
              text={{
                value: record.DnsName || '-',
              }}
              truncateProps={{
                threshold: 260,
                type: 'width',
              }}
            />
          );
        },
      },
      {
        key: 'Address',
        title: intl('saenext.application.BindNlb.NetworkType'),
        dataIndex: 'Address',
        width: 200,
        cell: (val, index, record) => {
          if (get(nlbInstanceMap.get(record.NlbId), 'AddressType', '')) {
            const addressType = get(nlbInstanceMap.get(record.NlbId), 'AddressType', '');
            if (addressType === 'Internet') {
              return intl('saenext.application.BindNlb.PublicNetwork');
            }
            if (addressType === 'Intranet') {
              return intl('saenext.application.BindNlb.PrivateNetwork');
            }
          } else {
            if(record?.Status==='Creating'){
              return '-'
            }
            return (
              <div style={{ color: '#ff3333', display: 'flex', alignItems: 'center' }}>
                <Icon type="warning" size="xs" style={{ color: '#ff3333' }} />
                <span style={{ marginLeft: 4 }}>
                  {intl('saenext.application.BindNlb.TheNlbInstanceMayBe')}
                </span>
              </div>
            );
          }
        },
      },
      {
        key: 'Port',
        title: intl('saenext.application.BindNlb.Port'),
        dataIndex: 'Port',
        width: 120,
        cell: (val, index, record) => {
          if (record?.colSpan) {
            return (
              <div>
                <LinkButton
                  onClick={() => {
                    setAddListenersVisible(true);
                    setCurrentNlbInfo(record);
                  }}
                >
                  <Icon type="plus" size="xs" className="mr-xs" />
                  <span>{intl('saenext.application.BindNlb.AddListener')}</span>
                </LinkButton>
              </div>
            );
          } else {
            return val || '-';
          }
        },
      },
      {
        key: 'Protocol',
        title: intl('saenext.application.BindNlb.Agreement'),
        dataIndex: 'Protocol',
        width: 120,
      },
      {
        key: 'CertIds',
        title: intl('saenext.application.BindNlb.SslCertificate'),
        dataIndex: 'CertIds',
        width: 200,
        cell: (val) => {
          if (val) {
            const targetCert = certificationList.find((item) => item.value === val);
            if (targetCert) {
              return targetCert.label;
            } else {
              return '-';
            }
          } else {
            return '-';
          }
        },
      },
      {
        key: 'Status',
        title: intl('saenext.application.BindNlb.InstanceStatus'),
        dataIndex: 'Status',
        width: 150,
        cell: (val) => {
          return (
            <CndStatus type={ListernerStatusMap[val]?.type} label={ListernerStatusMap[val]?.label} />
          );
        },
      },
      {
        key: 'Operate',
        title: intl('saenext.application.BindNlb.Operation'),
        width: 160,
        cell: (val, index, record) => {
          const { Status } = record;
          const disabled = includes(['Creating', 'Configuring', 'Unbinding'], Status);
          return (
            <Actions>
              <LinkButton
                onClick={() => {
                  setCurrentNlbInfo(record);
                  setBindNlbVisible(true);
                }}
                disabled={disabled}
              >
                {intl('saenext.application.BindNlb.Edit')}
              </LinkButton>
              <LinkButton onClick={() => deleteNlb(record)} disabled={disabled}>
                {intl('saenext.application.BindNlb.Delete')}
              </LinkButton>
            </Actions>
          );
        },
      },
    ]
  },[nlbInstanceMap]);

  return (
    <div style={props.style}>
      <CndTable
        fetchData={getNlbDataSource}
        columns={columns}
        operation={
          <Button type="primary" onClick={() => setBindNlbVisible(true)}>
            {intl('saenext.application.BindNlb.AddLbAccess')}
          </Button>
        }
        secondaryOperation={
          <Button
            type="normal"
            style={{ padding: '0 8px' }}
            onClick={async () => {
              await getNlbInstances();
              setRefreshIndex(Date.now());
            }}
          >
            <Icon type="refresh" style={{ color: '#666' }} />
          </Button>
        }
        cellProps={(rowIndex, colIndex, dataIndex, record) => {
          if ((dataIndex === 'NlbId' || dataIndex === 'Address') && record?.rowSpan) {
            return {
              rowSpan: record.rowSpan,
            };
          }
          if (dataIndex === 'Port' && record.colSpan) {
            return {
              colSpan: record.colSpan,
            };
          }
        }}
        // showRefreshButton
        refreshIndex={refreshIndex}
        loop={{ enable: loopEnable, time: 5000, showLoading: false }}
      />

      {bindNlbVisible && (
        <BindNlbPanel
          handleClose={() => {
            setBindNlbVisible(false);
            setCurrentNlbInfo({});
          }}
          handleOK={() => {
            setBindNlbVisible(false);
            if (!isEmpty(currentNlbInfo)) {
              const { NlbId, Protocol, Port } = currentNlbInfo as any;
              if (has(changingListener, `${NlbId}-${Protocol}:${Port}`)) {
                const _changingListener = cloneDeep(changingListener)
                delete _changingListener[`${NlbId}-${Protocol}:${Port}`];
                localStorage.setItem('changingListener', JSON.stringify(_changingListener));
              }
              setCurrentNlbInfo({});
            }
            setRefreshIndex(Date.now());
          }}
          regionId={regionId}
          appId={appId}
          appConfig={appConfig}
          nlbInfo={currentNlbInfo}
          nlbListenersMap={nlbListenersMap}
        />
      )}
      {addListenersVisible && (
        <AddListeners
          handleClose={() => {
            setAddListenersVisible(false);
            setCurrentNlbInfo({});
          }}
          handleOK={() => {
            setAddListenersVisible(false);
            setCurrentNlbInfo({});
            setRefreshIndex(Date.now());
          }}
          regionId={regionId}
          appId={appId}
          nlbInfo={currentNlbInfo}
          nlbListenersMap={nlbListenersMap}
        />
      )}
    </div>
  );
};

export default BindNlb;
