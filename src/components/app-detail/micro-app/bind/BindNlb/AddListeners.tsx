import React, { useState, useEffect } from 'react';
import { intl, Dialog, Form, Field, CndRcSelect, NumberPicker, Message, Icon } from '@ali/cnd';
import services from "~/services";
import { isEmpty, get, has, startsWith } from 'lodash';
import CustomRadioButton from './CustomRadioButton';
import NlbSelect from './NlbSelect';
import CachedData from '../../../../../cache/common';

const AddListeners = (props) => {
  const { regionId, appId, nlbInfo, nlbListenersMap, handleClose, handleOK } = props;
  const field = Field.useField();
  const [submitLoading, setSubmitLoading] = useState(false);

  useEffect(() => {
    field.setValues({
      NlbId: nlbInfo?.NlbId,
    });
  }, [nlbInfo?.NlbId]);

  const getNlbInstances = async (search, pageNumber = 1) => {
    const res = await services.getNlbLoadBalancers({
      params: {
        RegionId: regionId,
        MaxResults: 50,
      },
      customErrorHandle: (error, data, callback) => {
        callback();
      },
    });
    const { LoadBalancers = [], TotalCount = 0 } = res;
    const _nlbInstances = LoadBalancers.map((item) => ({
      label: item.LoadBalancerName,
      value: item.LoadBalancerId,
      ...item,
    }));
    return {
      data: _nlbInstances,
      total: TotalCount,
    };
  };

  const getCertificates = async () => {
    const res = await services.getServerCertificates({
      params: {
        RegionId: regionId,
      },
      customErrorHandle: (error, data, callback) => {
        callback();
      },
    });
    const serverCertificate = get(res, 'ServerCertificates.ServerCertificate', []);
    const _certificates = serverCertificate.map((item) => ({
      label: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: '#333' }}>{item.ServerCertificateName}</span>
          <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
          <span style={{ color: '#666' }}>{item.AliCloudCertificateName}</span>
        </div>
      ),

      value:
        startsWith(regionId, 'cn-') && regionId !== 'cn-hongkong'
          ? `${item.AliCloudCertificateId}-cn-hangzhou`
          : `${item.AliCloudCertificateId}-ap-southeast-1`,
      ...item,
    }));
    return {
      data: _certificates,
      total: _certificates.length,
    };
  };


    const getYundunCertificates = async () => {
      const { CertMetaList, TotalCount } = await services.getSSLCertificateList({
        params: {
          ShowSize: 99,
          CurrentPage: 1,
          RegionId: regionId,
        }
      });
      const _certificates = CertMetaList.map((item) => ({
        label: (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ color: '#333' }}>{item.CommonName}</span>
            <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
            <span style={{ color: '#666' }}>{item.CertName}</span>
          </div>
        ),
        value: item.CertIdentifier,
        ...item,
      }));
      return {
        data: _certificates,
        total: TotalCount,
      };
    };

  const handleSubmit = async () => {
    field.validate(async (errors, values) => {
      if (!errors) {
        const nlbListeners = nlbListenersMap.current.get(nlbInfo.NlbId)?.Listeners || {};
        if (!isEmpty(nlbListeners) && has(nlbListeners, `${values.Protocol}:${values.Port}`)) {
          Message.error(
            intl('saenext.application.BindNlb.AddListeners.TheInstanceAlreadyHasA', {
              valuesProtocol: values.Protocol,
              valuesPort: values.Port,
            }),
          );
          return;
        }
        const { NlbId, ...resetValues } = values;
        setSubmitLoading(true);
        const res = await services.BindNlb({
          params: {
            AppId: appId,
            NlbId: nlbInfo.NlbId,
            Listeners: JSON.stringify([
              {
                ...resetValues,
              },
            ]),
          },
        });
        setSubmitLoading(false);
        if (res?.Success) {
          handleOK();
        }
      }
    });
  };

  return (
    <Dialog
      visible={true}
      title={intl('saenext.application.BindNlb.AddListeners.AddListener')}
      onOk={handleSubmit}
      onClose={handleClose}
      onCancel={handleClose}
      okProps={{
        loading: submitLoading,
        children: intl('saenext.application.BindNlb.AddListeners.Ok'),
      }}
      cancelProps={{
        children: intl('saenext.application.BindNlb.AddListeners.Cancel'),
      }}
      style={{
        width: 640,
      }}
    >
      <Form field={field} labelCol={{ span: 6 }}>
        <Form.Item label={intl('saenext.application.BindNlb.AddListeners.NlbInstance')} required>
          <NlbSelect
            name="NlbId"
            fetchData={getNlbInstances}
            style={{ width: '100%' }}
            onChange={(v) => {
              field.reset('NlbId');
              field.setValue('NlbId', v);
            }}
            disabled={true}
          />
        </Form.Item>
        <Form.Item label={intl('saenext.application.BindNlb.AddListeners.ProtocolType')}>
          <CustomRadioButton
            name="Protocol"
            dataSource={[
              {
                label: 'TCP',
                value: 'TCP',
              },
              {
                label: 'UDP',
                value: 'UDP',
              },
              {
                label: 'TCPSSL',
                value: 'TCPSSL',
              },
            ]}
            defaultValue="TCP"
            onChange={(v) => {
              field.setValues({
                CertIds: '',
              });
            }}
          />
        </Form.Item>
        <Form.Item
          label={intl('saenext.application.BindNlb.AddListeners.ListeningPort')}
          required
          requiredMessage={intl('saenext.application.BindNlb.AddListeners.EnterTheListeningPort')}
        >
          <NumberPicker
            // @ts-ignore
            name="Port"
            min={1}
            max={65535}
            placeholder={intl('saenext.application.BindNlb.AddListeners.EnterTheListeningPortThe')}
            style={{ width: '100%' }}
          />
        </Form.Item>
        <Form.Item
          label={intl('saenext.application.BindNlb.AddListeners.ContainerPort')}
          required
          requiredMessage={intl('saenext.application.BindNlb.AddListeners.EnterTheContainerPort')}
        >
          <NumberPicker
            // @ts-ignore
            name="TargetPort"
            min={1}
            max={65535}
            placeholder={intl('saenext.application.BindNlb.AddListeners.EnterTheContainerPortThe')}
            style={{ width: '100%' }}
          />
        </Form.Item>
        {field.getValue('Protocol') === 'TCPSSL' && (
          <Form.Item
            label={intl('saenext.application.BindNlb.AddListeners.SelectSslCertificate')}
            required
            requiredMessage={intl(
              'saenext.application.BindNlb.AddListeners.SelectAnSslCertificate',
            )}
          >
            <CndRcSelect
              name="CertIds"
              fetchData={getYundunCertificates}
              style={{ width: '100%' }}
              followTrigger={false}
              menuHeaderProps={{
                headerProps: {
                  // @ts-ignore
                  text: (
                    <div className="flex-center">
                      {intl('saenext.application.BindNlb.BindNlbPanel.ToCreate')}
                      <Icon type="external_link" size="xs" className="ml-xs" />
                    </div>
                  ),
                  clickFunc: () => {
                    window.open(
                      `${CachedData.confLink('feature:yundunnext:url')}/?&p=cas#/certExtend/upload/${regionId}`,
                      '_balnk',
                    );
                  },
                },
              }}
            />
          </Form.Item>
        )}
      </Form>
    </Dialog>
  );
};

export default AddListeners;
