import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { isEmpty, get, map, join } from 'lodash';
import services from "~/services";
import { Message, LinkButton, Dialog, Truncate, CndTable } from '@ali/cnd';
import CachedData from '../../../../cache/common';

const IpMessage = (props) => {
  const { regionId, appId, slb } = props;
  const [internetWarns, setInternetWarns] = useState([]);
  const [intranetWarns, setIntranetWarns] = useState([]);

  useEffect(() => {
    // 公网
    getInternetSlbWarns();
    // 私网
    getIntranetSlbWarns();
  }, []);

  const getInternetSlbWarns = async () => {
    const res = await services.getSlbWarnState({
      params: {
        Type: 'internet',
        AppId: appId,
        RegionId: regionId,
      },
    });
    const _internetWarns = get(res, 'Data.Warnings', []);
    setInternetWarns(_internetWarns);
  };

  const getIntranetSlbWarns = async () => {
    const res = await services.getSlbWarnState({
      params: {
        Type: 'intranet',
        AppId: appId,
        RegionId: regionId,
      },
    });
    const _intranetWarns = get(res, 'Data.Warnings', []);
    setIntranetWarns(_intranetWarns);
  };

  const renderMessage = React.useCallback(() => {
    let message = (
      <Message type="notice" style={{ marginBottom: 8 }}>
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.components.application.IpMessage.BeforeUsingTheSaeClb')}
          <a href={CachedData.confLink('help:sae:clb-instructions-for-use')} target="_blank">
            {intl('saenext.components.application.IpMessage.ClbConfigurationConstraints')}
          </a>
          ,
          <a href={CachedData.confLink('help:sae:configuration-practices-for-clb')} target="_blank">
            {intl('saenext.components.application.IpMessage.BestPracticesForTypicalClb')}
          </a>
        </div>
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.components.application.IpMessage.SinceAprilAfterCreatingEditing')}
        </div>
      </Message>
    );

    if (internetWarns.length > 0 && intranetWarns.length > 0) {
      // 两者都有
      const overlaps = [
        {
          text: intl('saenext.components.application.IpMessage.PrivateNetworkCoverageDetails'),
          title: intl(
            'saenext.components.application.IpMessage.PrivateClbConfigurationOverwrittenNotification',
          ),
          ip: get(slb, 'IntranetIp'),
          dataSource: intranetWarns,
        },
        {
          text: intl('saenext.components.application.IpMessage.InternetCoverageDetails'),
          title: intl('saenext.components.application.IpMessage.NotificationThatThePublicClb'),
          ip: get(slb, 'InternetIp'),
          dataSource: internetWarns,
        },
      ];

      message = (
        <Message type="warning" style={{ marginBottom: 8 }}>
          <div className="text-line">
            <span>
              {intl('saenext.components.application.IpMessage.ThePrivateClbConfigurationAnd')}
            </span>
            {overlaps.map((item) => (
              <LinkButton
                onClick={() => {
                  Dialog.show({
                    title: item.title,
                    cancelProps: {
                      children: intl('saenext.components.application.IpMessage.Close'),
                    },
                    footerActions: ['cancel'],
                    content: <OverlapContent ip={item.ip} dataSource={item.dataSource} />,
                  });
                }}
                style={{ marginRight: 8 }}
              >
                {item.text}
              </LinkButton>
            ))}
          </div>
          <div className="text-line">
            <span>
              {intl('saenext.components.application.IpMessage.PleaseAvoidConfiguringClbAt')}
            </span>
            <a href={CachedData.confLink('help:sae:clb-instructions-for-use')} target="_blank">
              {intl('saenext.components.application.IpMessage.ClbConfigurationConstraints')}
            </a>
            <a
              href={CachedData.confLink('help:sae:configuration-practices-for-clb')}
              style={{ marginLeft: 8 }}
              target="_blank"
            >
              {intl('saenext.components.application.IpMessage.BestPracticesForTypicalClb')}
            </a>
          </div>
        </Message>
      );

      return message;
    }

    if (!isEmpty(internetWarns)) {
      message = (
        <Message type="warning" style={{ marginBottom: 8 }}>
          <div className="text-line">
            <span>
              {intl('saenext.components.application.IpMessage.ThePublicClbConfigurationThat')}
            </span>
            <LinkButton
              onClick={() => {
                Dialog.show({
                  title: intl(
                    'saenext.components.application.IpMessage.NotificationThatThePublicClb',
                  ),
                  cancelProps: {
                    children: intl('saenext.components.application.IpMessage.Close'),
                  },
                  footerActions: ['cancel'],
                  content: (
                    <OverlapContent ip={get(slb, 'InternetIp')} dataSource={internetWarns} />
                  ),
                });
              }}
            >
              {intl('saenext.components.application.IpMessage.InternetCoverageDetails')}
            </LinkButton>
          </div>
          <div className="text-line">
            <span>
              {intl('saenext.components.application.IpMessage.PleaseAvoidConfiguringClbAt')}
            </span>
            <a href={CachedData.confLink('help:sae:clb-instructions-for-use')} target="_blank">
              {intl('saenext.components.application.IpMessage.ClbConfigurationConstraints')}
            </a>
            <a
              href={CachedData.confLink('help:sae:configuration-practices-for-clb')}
              style={{ marginLeft: 8 }}
              target="_blank"
            >
              {intl('saenext.components.application.IpMessage.BestPracticesForTypicalClb')}
            </a>
          </div>
        </Message>
      );

      return message;
    }

    if (!isEmpty(intranetWarns)) {
      message = (
        <Message type="warning" style={{ marginBottom: 8 }}>
          <div className="text-line">
            <span>
              {intl('saenext.components.application.IpMessage.ThePrivateClbConfigurationThat')}
            </span>
            <LinkButton
              onClick={() => {
                Dialog.show({
                  title: intl(
                    'saenext.components.application.IpMessage.PrivateClbConfigurationOverwrittenNotification',
                  ),
                  cancelProps: {
                    children: intl('saenext.components.application.IpMessage.Close'),
                  },
                  footerActions: ['cancel'],
                  content: (
                    <OverlapContent ip={get(slb, 'IntranetIp')} dataSource={intranetWarns} />
                  ),
                });
              }}
            >
              {intl('saenext.components.application.IpMessage.PrivateNetworkCoverageDetails')}
            </LinkButton>
          </div>
          <div className="text-line">
            <span>
              {intl('saenext.components.application.IpMessage.PleaseAvoidConfiguringClbAt')}
            </span>
            <a href={CachedData.confLink('help:sae:clb-instructions-for-use')} target="_blank">
              {intl('saenext.components.application.IpMessage.ClbConfigurationConstraints')}
            </a>
            <a
              href={CachedData.confLink('help:sae:configuration-practices-for-clb')}
              style={{ marginLeft: 8 }}
              target="_blank"
            >
              {intl('saenext.components.application.IpMessage.BestPracticesForTypicalClb')}
            </a>
          </div>
        </Message>
      );

      return message;
    }
    return message;
  }, [slb, internetWarns, intranetWarns]);

  return <>{renderMessage()}</>;
};

const OverlapContent = (props) => {
  const { ip, dataSource } = props;
  return (
    <div style={{ width: 970 }}>
      <Message type="notice" style={{ marginBottom: 8 }}>
        <div className="text-line">
          <span>
            {intl('saenext.components.application.IpMessage.BecauseYouModifiedClbFor', { ip: ip })}
          </span>
          <span style={{ color: '#d93026' }}>
            {join(
              map(dataSource, (item) => item.Item),
              ',',
            )}
          </span>
          <span>
            {intl('saenext.components.application.IpMessage.ConflictsWithTheLatestConfiguration')}

            <a href={CachedData.confLink('help:sae:clb-instructions-for-use')} target="_blank">
              {intl('saenext.components.application.IpMessage.ClbConfigurationConstraints')}
            </a>
          </span>
        </div>
      </Message>
      <CndTable dataSource={dataSource} hasBorder={false}>
        <CndTable.Column
          title={intl('saenext.components.application.IpMessage.ConfigurationItems')}
          dataIndex="Item"
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <Truncate type="width" threshold={120}>
                <span>{value}</span>
              </Truncate>
            );
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.application.IpMessage.LatestConfigurationValuesSavedBy')}
          dataIndex="Actual"
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <Truncate type="width" threshold={320}>
                <span>{value}</span>
              </Truncate>
            );
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.application.IpMessage.TheModifiedValueOfThe')}
          dataIndex="Expected"
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <Truncate type="width" threshold={260}>
                <span>{value}</span>
              </Truncate>
            );
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.application.IpMessage.Result')}
          dataIndex="Message"
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <Truncate type="width" threshold={200}>
                <span>{value}</span>
              </Truncate>
            );
          }}
        />
      </CndTable>
    </div>
  );
};

export default IpMessage;
