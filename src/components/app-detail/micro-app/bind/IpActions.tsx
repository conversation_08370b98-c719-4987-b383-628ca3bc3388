import React, { FC, useState, useEffect } from 'react';
import services from "~/services";
import HttpProtocol from './HttpProtocol';
import TcpProtocol from './TcpProtocol';
import UdpProtocol from './UdpProtocol';
import HttpsProtocol from './HttpsProtocol';
import RcClbSelect, { ChargeTypes, SlbChargePay } from './RcClbSelect';
import CheckBindQuta from './CheckBindQuta';
import StandardInstance from './StandardInstance';
import {
  noop,
  get,
  find,
  map,
  isEmpty,
  concat,
  filter,
  omit,
  isEqual,
  forEach,
  head,
  includes,
} from 'lodash';
import { Message, SlidePanel, Tab } from '@ali/cnd';
import { intl } from '@ali/cnd';
import CachedData from '../../../../cache/common';

type Props = {
  isCreate?: boolean; // 新建 编辑
  isPublic?: boolean; // 公网 私网
  regionId: string;
  appId: string;
  autoHttpsTls: boolean;
  slb?: Record<string, any>;
  updateIpTrigger?: (key: string) => void;
};

const IpActions: FC<Props> = (props) => {
  const {
    isCreate = true,
    isPublic = false,
    regionId,
    autoHttpsTls = true,
    appId,
    slb,
    updateIpTrigger = noop,
    children,
  } = props;
  const key = isPublic ? 'Internet' : 'Intranet';
  const [isShowing, setIsShowing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [reuseSlbId, setReuseSlbId] = useState(-1);
  const [isReuseSlb, setIsReuseSlb] = useState(false);
  const [activeKey, setActiveKey] = useState(0);
  const [httpDataSource, setHttpDataSource] = useState([]);
  const [httpsDataSource, setHttpsDataSource] = useState([]);
  const [tcpDataSource, setTcpDataSource] = useState([]);
  const [udpDataSource, setUdpDataSource] = useState([]);
  const [requiredMsg, setRequiredMsg] = useState('');
  const isSupportTls = autoHttpsTls;
  const loadBalancerIdRef = React.useRef(0);

  useEffect(() => {
    setAvailableSlb();
  }, [slb, isShowing]);

  const title = React.useMemo(() => {
    let _title = intl('saenext.components.application.IpActions.AddPrivateClbAccess');
    const network = isPublic
      ? intl('saenext.components.application.IpActions.PublicNetwork')
      : intl('saenext.components.application.IpActions.PrivateNetwork');
    _title = isCreate
      ? intl('saenext.components.application.IpActions.AddNetworkClbAccess', { network: network }) as string
      : intl('saenext.components.application.IpActions.EditNetworkClbAccess', { network: network }) as string;
    return _title;
  }, [isCreate, isPublic]);

  const setAvailableSlb = () => {
    if (isCreate) return;
    if (!isShowing) return;
    let _dataSource = get(slb, key, []);
    const _activeKey = useProtocol(_dataSource);
    const _httpDataSource = useDataSource('HTTP', _dataSource);
    const _httpsDataSource = useDataSource('HTTPS', _dataSource);
    const _tcpDataSource = useDataSource('TCP', _dataSource);
    const _udpDataSource = useDataSource('UDP', _dataSource);
    // @ts-ignore
    setActiveKey(_activeKey);
    setHttpDataSource(_httpDataSource);
    setHttpsDataSource(_httpsDataSource);
    setTcpDataSource(_tcpDataSource);
    setUdpDataSource(_udpDataSource);
  };

  const useDataSource = (protocol, dataSource) => {
    if (isEmpty(dataSource)) return [];
    let _dataSource = dataSource.filter((item) => item.Protocol === protocol);
    _dataSource = map(_dataSource, (item) => {
      if (protocol === 'HTTPS' && isSupportTls) {
        return {
          ...item,
          isBind: isEmpty(item.HttpsCaCertId) ? false : true,
          Key: Math.random(),
        };
      }
      return { ...item, Key: Math.random() };
    });
    return _dataSource;
  };

  const useProtocol = (dataSource) => {
    let _activeKey = 0;
    if (find(dataSource, (v) => v.Protocol === 'HTTPS')) {
      _activeKey = 1;
      return _activeKey;
    }
    if (find(dataSource, (v) => v.Protocol === 'TCP')) {
      _activeKey = 2;
      return _activeKey;
    }
    if (find(dataSource, (v) => v.Protocol === 'UDP')) {
      _activeKey = 3;
      return _activeKey;
    }
    return _activeKey;
  };

  const handleSubmit = async () => {
    const doSubmit = async (params) => {
      const res = await services.setBindSlb({
        params: { ...params },
        customErrorHandle: (error, data, callback) => {
          setIsProcessing(false);
          callback && callback();
        },
      });
      const orderId = get(res, 'Data.ChangeOrderId');
      if (!isEmpty(orderId)) {
        Message.success(
          intl('saenext.components.application.IpActions.TitleSucceeded', { title: title }),
        );
        setIsShowing(false);
        updateIpTrigger && updateIpTrigger(key);
      }

      setIsProcessing(false);
    };

    const validate = beforeSubmit();
    if (validate) {
      setIsProcessing(true);
      let newOptions = concat(httpDataSource, httpsDataSource, tcpDataSource, udpDataSource);
      newOptions = filter(newOptions, (item) => item.Port && item.TargetPort);
      newOptions = map(newOptions, (item) => omit(item, 'Key'));

      let closeTlsResult = true;
      const isHttps = find(newOptions, (v) => v.Protocol === 'HTTPS');
      // 如果为编辑slb 需要单独调用关闭 tls 的功能
      if (isSupportTls && isHttps && !isCreate) {
        const res = await handleCloseTls(newOptions);
        closeTlsResult = res;
        setIsProcessing(closeTlsResult);
        if (!closeTlsResult) return;
      }

      // 判断如果为https 删除掉 isBind 属性
      if (isSupportTls && isHttps) {
        newOptions = newOptions.map((item) => omit(item, 'isBind'));
      }

      // 判断如果不支持tls 并且存在https 删掉 HttpsCaCertId
      if (!isSupportTls && isHttps) {
        newOptions = newOptions.map((item) => omit(item, 'HttpsCaCertId'));
      }

      // 转小写
      const lowercaseLetter = (string) => {
        if (!string) return string;
        return string.charAt(0).toLowerCase() + string.slice(1);
      };
      newOptions = newOptions.map((item) => {
        let _item = {};
        for (let key in item) {
          if (item.hasOwnProperty(key)) {
            _item[lowercaseLetter(key)] = item[key];
          }
        }
        return _item;
      });

      const params = {
        AppId: appId,
        [key]: newOptions,
      };
      // 复用 clb
      if (isReuseSlb) {
        Reflect.set(params, `${key}SlbId`, reuseSlbId);
      } else {
        const payCharge = SlbChargePay[reuseSlbId];
        Reflect.set(params, `${key}SlbChargeType`, payCharge);
      }
      if (!isCreate) {
        const payCharge = get(slb, `${key}SlbChargeType`);
        Reflect.set(params, `${key}SlbChargeType`, payCharge);
      }
      doSubmit(params);
    }
  };

  const beforeSubmit = () => {
    let requiredMsg = '';
    let _dataSource = concat(httpDataSource, httpsDataSource, tcpDataSource, udpDataSource);
    if (isEmpty(_dataSource)) {
      requiredMsg = intl('saenext.components.application.IpActions.KeepAtLeastOneListening');
      setRequiredMsg(requiredMsg);
      return false;
    }
    const emptyData = filter(_dataSource, (item) => !item.Port || !item.TargetPort);
    if (!isEmpty(emptyData)) {
      requiredMsg = intl('saenext.components.application.IpActions.CheckWhetherTheInformationOf');
      setRequiredMsg(requiredMsg);
      return false;
    }

    _dataSource = filter(_dataSource, (item) => item.Port && item.TargetPort);
    const isHttps = find(_dataSource, (v) => v.Protocol === 'HTTPS');
    if (isSupportTls && isHttps) {
      for (let i = 0; i < _dataSource.length; i++) {
        const tuple = _dataSource[i];
        if (tuple.isBind && isEmpty(tuple.HttpsCaCertId)) {
          requiredMsg = intl(
            'saenext.components.application.IpActions.CheckWhetherTheCaCertificate',
          );
          setRequiredMsg(requiredMsg);
          return false;
        }
      }
    }
    return true;
  };

  const handleCloseTls = async (newOptions) => {
    const dataSource = get(slb, key, []);

    // 开启状态的旧规则
    const _dataSource = filter(dataSource, (item) => get(item, 'HttpsCaCertId', '').length > 0);
    if (isEmpty(_dataSource)) return true;

    // 关闭状态的规则
    const _newOptions = filter(
      newOptions,
      (item) => get(item, 'Protocol') === 'HTTPS' && get(item, 'isBind', false) === false,
    );

    // 状态关闭的旧规则
    const tlsClosedOldOptions = [];
    for (let i = 0; i < _dataSource.length; i++) {
      const oldItem = _dataSource[i];
      for (let j = 0; j < _newOptions.length; j++) {
        const newItem = _newOptions[j];
        const isPortEqual = isEqual(oldItem.Port, newItem.Port);
        const isTargetPortEqual = isEqual(oldItem.TargetPort, newItem.TargetPort);
        const isCertIdEqual = isEqual(oldItem.HttpsCertId, newItem.HttpsCertId);
        if (isPortEqual && isTargetPortEqual && isCertIdEqual) {
          tlsClosedOldOptions.push(newItem);
        }
      }
    }

    if (isEmpty(tlsClosedOldOptions)) return true;

    const tlsClosedOldPromise = [];
    forEach(tlsClosedOldOptions, async (item) => {
      const { Port } = item;
      tlsClosedOldPromise.push(closedCACertAttribute(Port));
    });
    const res = await Promise.all(tlsClosedOldPromise);
    return !res.includes(false);
  };

  const closedCACertAttribute = (listenerPort) => {
    const key = isPublic ? 'InternetIp' : 'IntranetIp';
    let address = get(slb, key, '');
    return services
      .getLoadBalancers({
        params: {
          Address: address,
          RegionId: regionId,
        }
      })
      .then((res) => {
        const { LoadBalancers = {} } = res || {};
        const { LoadBalancer = [] } = LoadBalancers;
        const loadBalancer = head(LoadBalancer);
        // @ts-ignore
        const { LoadBalancerId = '' } = loadBalancer || {};
        loadBalancerIdRef.current = LoadBalancerId;
        return LoadBalancerId;
      })
      .then((loadBalancerId) =>
        services.getLoadBalancerAttribute({
          RegionId: regionId,
          LoadBalancerId: loadBalancerId,
          ListenerPort: listenerPort,
        }),
      )
      .then((params) =>
        services.setLoadBalancerHTTPSListenerAttribute({
          RegionId: regionId,
          LoadBalancerId: loadBalancerIdRef.current,
          ListenerPort: listenerPort,
          ...params,
          CACertificateId: '',
        }),
      )
      .then((res) => {
        loadBalancerIdRef.current = 0;
        if (isEmpty(res)) {
          setIsProcessing(false);
          return false;
        }
        return true;
      })
      .catch((error) => {
        setIsProcessing(false);
        loadBalancerIdRef.current = 0;
        return false;
      });
  };

  const handleCancel = () => setIsShowing(false);

  const renderMessage = React.useCallback(() => {
    if (!isCreate) return null;
    const first = isPublic
      ? intl('saenext.components.application.IpActions.SetPublicLoadBalancingTo')
      : intl('saenext.components.application.IpActions.AfterYouSetUpPrivate');
    const clb = isPublic
      ? intl('saenext.components.application.IpActions.PublicClbService')
      : intl('saenext.components.application.IpActions.PrivateClbService');
    return (
      <Message type="notice">
        <div style={{ lineHeight: 1.5 }}>{first}</div>
        <div style={{ lineHeight: 1.5 }}>
          <span>
            {intl('saenext.components.application.IpActions.AfterYouSelectCreateThe', { clb: clb })}
          </span>
          <a href={CachedData.confLink('feature:slb:price')} target="_blank">
            {intl('saenext.components.application.IpActions.ClbBillingDetails')}
          </a>
          <span>
            {intl('saenext.components.application.IpActions.ThePurchasedClbInformationCan')}
          </span>
        </div>
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.components.application.IpActions.FromJulyYouCanNo')}
        </div>
      </Message>
    );
  }, [isCreate, isPublic]);

  const validatePort = (key, port) => {
    const dataSource = httpDataSource
      .concat(httpsDataSource)
      .concat(tcpDataSource)
      .concat(udpDataSource);
    let newOptions = filter(dataSource, (item) => item.Key !== key);
    newOptions = map(newOptions, (item) => item.Port);
    if (includes(newOptions, parseInt(port))) {
      setRequiredMsg(
        intl('saenext.components.application.IpActions.TheListeningPortAlreadyExists'),
      );
      return false;
    }
    return true;
  };

  return (
    <>
      <span onClick={() => setIsShowing((prev) => !prev)}>{children}</span>
      <SlidePanel
        title={title}
        width={1000}
        onCancel={handleCancel}
        onOk={handleSubmit}
        isShowing={isShowing}
        isProcessing={isProcessing}
        okText={intl('saenext.components.application.IpActions.Ok')}
        cancelText={intl('saenext.components.application.IpActions.Cancel')}
        processingText={intl('saenext.components.application.IpActions.Processing')}
      >
        {renderMessage()}
        <RcClbSelect
          isCreate={isCreate}
          isShowing={isShowing}
          regionId={regionId}
          appId={appId}
          isPublic={isPublic}
          callback={(value) => {
            setReuseSlbId(value);
            setIsReuseSlb(!includes(ChargeTypes, value));
          }}
        />

        <CheckBindQuta
          isCreate={isCreate}
          isShowing={isShowing}
          regionId={regionId}
          isReuseSlb={isReuseSlb}
        />

        <StandardInstance
          isCreate={isCreate}
          isPublic={isPublic}
          regionId={regionId}
          isReuseSlb={isReuseSlb}
          reuseSlbId={reuseSlbId}
        />

        <Tab
          shape="wrapped"
          lazyLoad={false}
          size="small"
          activeKey={String(activeKey)}
          // @ts-ignore
          onChange={(key) => setActiveKey(key)}
        >
          <Tab.Item title={intl('saenext.components.application.IpActions.HttpProtocol')}>
            <HttpProtocol
              dataSource={httpDataSource}
              validatePort={validatePort}
              updateEventTrigger={(value) => {
                setRequiredMsg('');
                setHttpDataSource(value);
              }}
            />
          </Tab.Item>
          <Tab.Item title={intl('saenext.components.application.IpActions.HttpsProtocol')}>
            <HttpsProtocol
              regionId={regionId}
              isSupportTls={isSupportTls}
              dataSource={httpsDataSource}
              validatePort={validatePort}
              updateEventTrigger={(value) => {
                setRequiredMsg('');
                setHttpsDataSource(value);
              }}
            />
          </Tab.Item>
          <Tab.Item title={intl('saenext.components.application.IpActions.TcpProtocol')}>
            <TcpProtocol
              dataSource={tcpDataSource}
              validatePort={validatePort}
              updateEventTrigger={(value) => {
                setRequiredMsg('');
                setTcpDataSource(value);
              }}
            />
          </Tab.Item>
          <Tab.Item title={intl('saenext.components.application.IpActions.UdpProtocol')}>
            <UdpProtocol
              dataSource={udpDataSource}
              validatePort={validatePort}
              updateEventTrigger={(value) => {
                setRequiredMsg('');
                setUdpDataSource(value);
              }}
            />
          </Tab.Item>
        </Tab>
        <div style={{ color: '#c80000', marginTop: 16 }}>{requiredMsg}</div>
      </SlidePanel>
    </>
  );
};

export default IpActions;
