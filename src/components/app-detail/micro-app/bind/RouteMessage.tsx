import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { isEmpty, map, each, concat, find, join } from 'lodash';
import services from "~/services";
import { Message, LinkButton, Truncate, Dialog, CndTable } from '@ali/cnd';
import CachedData from '../../../../cache/common';

const RouteMessage = (props) => {
  const { regionId, loaded, ingressList, namespaceId, slbList } = props;
  if (!loaded) return null;
  const [warnings, setWarnings] = useState([]);

  useEffect(() => {
    getIngressWarns();
  }, [ingressList]);

  const getIngressWarns = async () => {
    if (isEmpty(ingressList)) return;
    let _warnings = [];
    for await (const item of ingressList) {
      const res = await services.checkIngressWarn({
        IngressId: item.Id,
        NamespaceId: namespaceId,
        RegionId: regionId,
      });
      const { Data = {} } = res;
      _warnings = concat(_warnings, {
        ingressId: item.Id,
        list: Data.Warnings,
      });
    }
    setWarnings(_warnings);
  };

  const disableIngressWarns = () => {
    const option = [];
    each(ingressList, (item) => {
      const fetchOne = services.disableIngressWarn({
        IngressId: item.Id,
        NamespaceId: namespaceId,
        RegionId: regionId,
      });
      option.push(fetchOne);
    });
    Promise.all(option).then(() => {
      setWarnings([]);
      getIngressWarns();
    });
  };

  const renderMessage = () => {
    let option = [];
    each(warnings, (item) => {
      option = concat(option, item.list);
    });

    let message = (
      <Message type="notice" style={{ marginBottom: 8 }}>
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.components.application.RouteMessage.BeforeUsingTheSaeClb')}
          <a href={CachedData.confLink('help:sae:limits-of-clb')} target="_blank">
            {intl('saenext.components.application.RouteMessage.ClbUsageConstraints')}
          </a>
          ,
          <a href={CachedData.confLink('help:sae:limits-of-alb')} target="_blank">
            {intl('saenext.components.application.RouteMessage.AlbUsageConstraints')}
          </a>
        </div>
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.components.application.RouteMessage.SinceAprilAfterConfiguringThe')}
        </div>
      </Message>
    );

    if (option.length > 0) {
      message = (
        <Message type="warning" style={{ marginBottom: 8, position: 'relative' }}>
          <LinkButton
            style={{ position: 'absolute', top: 8, right: 16 }}
            onClick={disableIngressWarns}
          >
            {intl('saenext.components.application.RouteMessage.NoMoreReminders')}
          </LinkButton>
          {map(ingressList, (item) => {
            const network =
              item.SlbType === 'internet'
                ? intl('saenext.components.application.RouteMessage.PublicNetwork')
                : intl('saenext.components.application.RouteMessage.PrivateNetwork');
            const slb = find(slbList, (slb) => slb.LoadBalancerId === item.SlbId);
            const ip = isEmpty(slb) ? item.SlbId : slb.Address;
            const dataSource = find(warnings, (wran) => wran.ingressId === item.Id) || {};
            const protocol = item.CertId ? 'HTTPS' : 'HTTP';
            return (
              <div style={{ lineHeight: 1.5 }}>
                <span>
                  {intl('saenext.components.application.RouteMessage.YouHaveUpdatedTheRouting', {
                    protocol: protocol,
                    network: network,
                    ip: ip,
                  })}
                </span>
                <LinkButton
                  onClick={() => {
                    Dialog.show({
                      title: intl(
                        'saenext.components.application.RouteMessage.NetworkConfigurationOverwrittenNotification',
                        { network: network },
                      ),
                      cancelProps: {
                        children: intl('saenext.components.application.RouteMessage.Close'),
                      },
                      footerActions: ['cancel'],
                      content: <OverlapContent ip={ip} dataSource={dataSource} />,
                    });
                  }}
                >
                  <span>
                    {intl('saenext.components.application.RouteMessage.NetworkOverwriteDetails', {
                      network: network,
                    })}
                  </span>
                </LinkButton>
              </div>
            );
          })}

          <div style={{ lineHeight: 1.5 }}>
            <span>
              {intl(
                'saenext.components.application.RouteMessage.PleaseAvoidConfiguringGatewayRouting',
              )}
            </span>
            <a href={CachedData.confLink('help:sae:limits-of-clb')} target="_blank">
              {intl(
                'saenext.components.application.RouteMessage.GatewayRoutingConfigurationConstraints',
              )}
            </a>
          </div>
        </Message>
      );
    }
    return message;
  };

  return <>{renderMessage()}</>;
};

const OverlapContent = (props) => {
  const { ip, dataSource } = props;
  return (
    <div style={{ width: 970 }}>
      <Message type="notice" style={{ marginBottom: 8 }}>
        <div className="text-line">
          <span>
            {intl('saenext.components.application.RouteMessage.BecauseYouModifiedClbFor', {
              ip: ip,
            })}
          </span>
          <span style={{ color: '#d93026' }}>
            {join(
              map(dataSource, (item) => item.Item),
              ',',
            )}
          </span>
          <span>
            {intl('saenext.components.application.RouteMessage.ConflictsWithTheLatestGateway')}

            <a href={CachedData.confLink('help:sae:limits-of-clb')} target="_blank">
              {intl(
                'saenext.components.application.RouteMessage.GatewayRoutingConfigurationConstraints',
              )}
            </a>
          </span>
        </div>
      </Message>
      <CndTable dataSource={dataSource} hasBorder={false}>
        <CndTable.Column
          title={intl('saenext.components.application.RouteMessage.ConfigurationItems')}
          dataIndex="Item"
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <Truncate type="width" threshold={120}>
                <span>{value}</span>
              </Truncate>
            );
          }}
        />

        <CndTable.Column
          title={intl(
            'saenext.components.application.RouteMessage.LatestConfigurationValuesSavedBy',
          )}
          dataIndex="Actual"
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <Truncate type="width" threshold={320}>
                <span>{value}</span>
              </Truncate>
            );
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.application.RouteMessage.TheModifiedValueOfThe')}
          dataIndex="Expected"
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <Truncate type="width" threshold={260}>
                <span>{value}</span>
              </Truncate>
            );
          }}
        />

        <CndTable.Column
          title={intl('saenext.components.application.RouteMessage.Result')}
          dataIndex="Message"
          cell={(value) => {
            if (isEmpty(value)) return '-';
            return (
              <Truncate type="width" threshold={200}>
                <span>{value}</span>
              </Truncate>
            );
          }}
        />
      </CndTable>
    </div>
  );
};

export default RouteMessage;
