import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { isEmpty, get, map, noop, isEqual, join, find, filter, cloneDeep } from 'lodash';
import IpActions from './IpActions';
import IpMessage from './IpMessage';
import LoadWarmUp from './LoadWarmUp';
import services from "~/services";
import { Description, Button, Icon, Dialog, Message, Copy, Actions, LinkButton } from '@ali/cnd';
import { isForbidden } from '../../../../utils/authUtils';

const _dataSource = {
  public: { loading: false, ip: '', ports: [], validate: true },
  private: { loading: false, ip: '', ports: [], validate: true },
};

const BindIp = (props) => {
  const {
    regionId,
    appId,
    appStatus,
    autoWarmUp = true,
    autoHttpsTls = true,
    targetContainer = 'v2',
    setSlbQpsRt: setSlbQpsRtCallback = noop,
    setSlbQpsRtPermission = noop,
    boundCallback = noop,
  } = props;
  const [slb, setSlb] = useState({});
  const [dataSource, setDataSource] = useState(_dataSource);
  const [slbLoadBalancersPermission, setLoadBalancersPermission] = useState(true);
  const timer = React.useRef(null);
  const isOrderRun = get(appStatus, 'LastChangeOrderRunning', false);
  const unknownStatus = isEqual(get(appStatus, 'CurrentStatus'), 'UNKNOWN');
  const bindState = isOrderRun || unknownStatus;

  useEffect(() => {
    getApplicationSlbs();
  }, [isOrderRun]);

  const getApplicationSlbs = async () => {
    const res = await services.getApplicationSlbs({
      params: {
        AppId: appId,
        RegionId: regionId,
      },
      customErrorHandle: (error, data, callback) => {
        setDataSource(_dataSource);
        timer.current && clearInterval(timer.current);
        // callback && callback();
      },
    });
    const _slb = get(res, 'Data', {});
    const _intranetIp = get(_slb, 'IntranetIp', '');
    const _intranetPorts = get(_slb, 'Intranet', []);
    const _internetIp = get(_slb, 'InternetIp', '');
    const _internetPorts = get(_slb, 'Internet', []);
    const _dataSource = {
      public: { loading: false, ip: _internetIp, ports: _internetPorts, validate: true },
      private: { loading: false, ip: _intranetIp, ports: _intranetPorts, validate: true },
    };
    // 校验 slb 是否有
    // 私网ip
    const _privateValidate = await checkSlbExpired(_intranetIp);
    // 公网ip
    const _publicValidate = await checkSlbExpired(_internetIp);
    _dataSource.private.validate = _privateValidate;
    _dataSource.public.validate = _publicValidate;
    if (setSlbQpsRtCallback && targetContainer === 'v2') {
      const slbQps = await updateSlbQpsRt(_internetIp, _internetPorts, _intranetIp, _intranetPorts);
      setSlbQpsRtCallback(slbQps);
    }
    setSlb(_slb);
    setDataSource(_dataSource);
  };

  const checkSlbExpired = async (address) => {
    if (isEmpty(address)) return Promise.resolve(true);
    const res = await services.getLoadBalancers({
      params: {
        Address: address,
        PageNumber: 1,
        PageSize: 20,
        RegionId: regionId,
      },
      customErrorHandle: (error, data, callback) => {
        const forbidden = isForbidden(error.code);
        setLoadBalancersPermission(!forbidden);
        !forbidden && callback?.();
      },
    });
    const loadBalancers = get(res, 'LoadBalancers.LoadBalancer', []);
    const loadBalancerId = get(loadBalancers, '[0].LoadBalancerId');
    return !!loadBalancerId;
  };

  const updateSlbQpsRt = async (internetIp, internet, intranetIp, intranet) => {
    const internetPorts = join(
      map(
        filter(internet, (v) => v.Protocol === 'HTTP' || v.Protocol === 'HTTPS'),
        (item) => item.Port,
      ),
      ',',
    );
    let intranetPorts = join(
      map(
        filter(intranet, (v) => v.Protocol === 'HTTP' || v.Protocol === 'HTTPS'),
        (item) => item.Port,
      ),
      ',',
    );
    const internetLogsDownloadAttribute = await setSlbQpsRt(internetIp);
    const intranetLogsDownloadAttribute = await setSlbQpsRt(intranetIp);
    return {
      internetPorts,
      intranetPorts,
      internetLogsDownloadAttribute,
      intranetLogsDownloadAttribute,
    };
  };

  const setSlbQpsRt = async (address) => {
    let logsDownloadAttribute = '';
    if (isEmpty(address)) return Promise.resolve(logsDownloadAttribute);
    const res = await services.getLoadBalancers({
      params: {
        Address: address,
        PageNumber: 1,
        PageSize: 20,
        RegionId: regionId,
      },
      customErrorHandle: (error, data, callback) => {
        const forbidden = isForbidden(error.code);
        setLoadBalancersPermission(!forbidden);
        !forbidden && callback?.();
      },
    });
    const loadBalancers = get(res, 'LoadBalancers.LoadBalancer', []);
    const loadBalancerId = get(loadBalancers, '[0].LoadBalancerId');
    if (isEmpty(loadBalancerId)) return logsDownloadAttribute;
    logsDownloadAttribute = await getLogsDownloadAttributes(1, 50, loadBalancerId);
    return logsDownloadAttribute;
  };

  const getLogsDownloadAttributes = async (pageNumber = 1, pageSize = 50, loadBalancerId) => {
    setSlbQpsRtPermission(true);
    const res = await services.getLogsDownloadAttribute({
      params: {
        PageNumber: pageNumber,
        PageSize: pageSize,
        LoadBalancerId: loadBalancerId,
        headers: {
          'x-oneconsole-no-host': true,
        },
        RegionId: regionId,
      },
      customErrorHandle: (error, data, callback) => {
        const forbidden = isForbidden(error.code);
        setSlbQpsRtPermission(!forbidden);
        !forbidden && callback?.();
      },
    });
    const totalCount = get(res, 'TotalCount', 0);
    const logsDownloadAttribute = get(res, 'LogsDownloadAttributes.LogsDownloadAttribute', []);

    const _logsDownloadAttribute = find(
      logsDownloadAttribute,
      (v) => v.LoadBalancerId === loadBalancerId,
    );
    if (totalCount > pageNumber * pageSize && isEmpty(_logsDownloadAttribute)) {
      getLogsDownloadAttributes(++pageNumber, 50, loadBalancerId);
    } else {
      return _logsDownloadAttribute;
    }
  };

  const updateIpTrigger = (key) => {
    const _dataSource = cloneDeep(dataSource);
    if (key === 'Internet') {
      // 更新的公网
      _dataSource.public.loading = true;
    } else {
      // 更新的私网
      _dataSource.private.loading = true;
    }
    setDataSource(_dataSource);
    // 绑定成功后需要刷新新的 clb
    getApplicationSlbs();
    boundCallback && boundCallback();
  };

  const handUnbindSlb = (ispublic) => {
    const key = ispublic ? 'InternetSlbId' : 'IntranetSlbId';
    const title = ispublic
      ? intl('saenext.components.application.BindIp.DeletePublicClbAccess')
      : intl('saenext.components.application.BindIp.DeletePrivateClbAccess');
    const content = ispublic
      ? intl('saenext.components.application.BindIp.AfterDeletionTheApplicationIs.2')
      : intl('saenext.components.application.BindIp.AfterDeletionTheApplicationIs.3');

    // let publicContent = intl('saenext.components.application.BindIp.AfterDeletionThePublicClb');
    // let privateContent = intl('saenext.components.application.BindIp.AfterDeletionThePrivateClb');
    // const hasSlb = !isEmpty(get(slb, key, ''));
    // if (hasSlb) {
    //   publicContent = intl('saenext.components.application.BindIp.AfterDeletionTheApplicationIs');
    //   privateContent = intl(
    //     'saenext.components.application.BindIp.AfterDeletionTheApplicationIs.1',
    //   );
    // }

    // const content = ispublic ? publicContent : privateContent;

    Dialog.alert({
      title: title,
      content: <p style={{ width: 500 }}>{content}</p>,

      onOk: () =>
        new Promise(async (resolve, reject) => {
          const res = await services.setUnbindSlb({
            params: {
              RegionId: regionId,
              AppId: appId,
              Internet: ispublic,
              Intranet: !ispublic,
            },
            customErrorHandle: (error, data, callback) => {
              callback && callback();
              reject(false);
            },
          });
          const orderId = get(res, 'Data.ChangeOrderId');
          if (!isEmpty(orderId)) {
            Message.success(
              intl('saenext.components.application.BindIp.TitleSucceeded', { title: title }),
            );
            // 删除成功后需要刷新新的 clb
            updateIpTrigger(ispublic ? 'Internet' : 'Intranet');
            resolve(true);
          } else {
            Message.error(
              intl('saenext.components.application.BindIp.TitleFailed', { title: title }),
            );
            reject(false);
          }
        }),
      okProps: { children: intl('saenext.common.dialog.ok') },
    });
  };

  const renderIpItem = (ispublic, value) => {
    const loading = get(value, 'loading', false);
    if (loading) {
      return (
        <div>
          {intl('saenext.components.application.BindIp.TheAccessAddressIsBeing')}
          <Icon style={{ marginLeft: 4 }} type="loading" size="xs" />
        </div>
      );
    }
    let content = null;
    const validate = get(value, 'validate', true);
    const hasIp = !isEmpty(get(value, 'ip', ''));
    const hasPort = !isEmpty(get(value, 'ports', []));
    if (hasIp || hasPort) {
      content = (
        <div style={{ marginBottom: 4 }}>
          {isEmpty(value.ip) ? (
            isOrderRun ? (
              <span>
                {intl('saenext.components.application.BindIp.TheAccessAddressIsBeing.1')}
                <Icon style={{ marginLeft: 4 }} type="loading" size="xs" />
              </span>
            ) : (
              <span>--</span>
            )
          ) : (
            <>
              {map(get(value, 'ports', []), (item) => {
                const { Port, Protocol } = item;
                return (
                  <div key={Math.random()}>
                    <span style={{ display: 'inline-block', minWidth: 140 }}>
                      <Copy text={`${value.ip}:${Port}`}>
                        {value.ip}:{Port}
                      </Copy>
                    </span>
                    <span style={{ marginLeft: 8, marginRight: 16 }}>{Protocol}</span>
                  </div>
                );
              })}
            </>
          )}

          {!slbLoadBalancersPermission && (
            <div style={{ color: '#ff3333', display: 'flex', alignItems: 'center' }}>
              <Icon type="warning" size="xs" style={{ color: '#ff3333' }} />
              <span style={{ marginLeft: 4 }}>
                {intl('saenext.components.application.BindIp.TheCurrentUserLacksThe')}
              </span>
            </div>
          )}
          {slbLoadBalancersPermission && !validate && (
            <div style={{ color: '#ff3333', display: 'flex', alignItems: 'center' }}>
              <Icon type="warning" size="xs" style={{ color: '#ff3333' }} />
              <span style={{ marginLeft: 4 }}>
                {intl('saenext.components.application.BindIp.TheClbInstanceHasBeen')}
              </span>
            </div>
          )}
        </div>
      );
    }
    return (
      <>
        <>{content}</>
        <>
          {hasIp || hasPort ? (
            <Actions style={{ height: 20 }}>
              <IpActions
                isCreate={false}
                isPublic={ispublic}
                regionId={regionId}
                appId={appId}
                slb={slb}
                autoHttpsTls={autoHttpsTls}
                updateIpTrigger={updateIpTrigger}
              >
                <LinkButton
                  disabled={loading || bindState || !validate || !slbLoadBalancersPermission}
                >
                  <span>
                    {intl('saenext.components.application.BindIp.Edit')}
                    {ispublic
                      ? intl('saenext.components.application.BindIp.PublicNetwork')
                      : intl('saenext.components.application.BindIp.PrivateNetwork')}
                    {intl('saenext.components.application.BindIp.ClbAccess')}
                  </span>
                  <Icon type="edit" size="small" style={{ marginLeft: 4 }} />
                </LinkButton>
              </IpActions>
              <LinkButton disabled={bindState} onClick={() => handUnbindSlb(ispublic)}>
                <span>
                  {intl('saenext.components.application.BindIp.Delete')}
                  {ispublic
                    ? intl('saenext.components.application.BindIp.PublicNetwork')
                    : intl('saenext.components.application.BindIp.PrivateNetwork')}
                  {intl('saenext.components.application.BindIp.ClbAccess')}
                </span>
                <Icon type="delete" size="small" style={{ marginLeft: 4 }} />
              </LinkButton>
            </Actions>
          ) : (
            <Actions style={{ height: 20 }}>
              <IpActions
                isCreate={true}
                isPublic={ispublic}
                regionId={regionId}
                appId={appId}
                autoHttpsTls={autoHttpsTls}
                updateIpTrigger={updateIpTrigger}
              >
                <LinkButton disabled={loading || bindState || hasPort}>
                  <span>
                    {intl('saenext.components.application.BindIp.Add')}
                    {ispublic
                      ? intl('saenext.components.application.BindIp.PublicNetwork')
                      : intl('saenext.components.application.BindIp.PrivateNetwork')}
                    {intl('saenext.components.application.BindIp.ClbAccess')}
                  </span>
                  <Icon type="edit" size="small" style={{ marginLeft: 4 }} />
                </LinkButton>
              </IpActions>
            </Actions>
          )}
        </>
      </>
    );
  };

  return (
    <div style={props.style}>
      <IpMessage regionId={regionId} appId={appId} slb={slb} />
      <Description
        dataSource={dataSource}
        items={[
          {
            dataIndex: 'private',
            label: intl('saenext.components.application.BindIp.PrivateAccessAddress'),
            // @ts-ignore
            render: (value) => renderIpItem(false, value),
          },
          {
            dataIndex: 'public',
            label: intl('saenext.components.application.BindIp.PublicAccessAddress'),
            // @ts-ignore
            render: (value) => renderIpItem(true, value),
          },
        ]}
      />

      <LoadWarmUp regionId={regionId} appId={appId} slb={slb} autoWarmUp={autoWarmUp} />
    </div>
  );
};

export default BindIp;
