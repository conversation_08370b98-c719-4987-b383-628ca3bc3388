import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Card, Message, Button } from '@ali/cnd';
import services from "~/services";
import { get, map, some, isEmpty, noop, filter, cloneDeep } from 'lodash';
import RouteMessage from './RouteMessage';
import RouteTable from './RouteTable';
import { isForbidden } from '../../../../utils/authUtils';

const CardProps = {
  showTitleBullet: false,
  showHeadDivider: false,
  contentHeight: 'auto',
};

const BindRouteCard = (props) => {
  const {
    regionId,
    appId,
    appConfig,
    transmitCallback = noop,
    toAppCallback = noop,
    className,
  } = props;
  const namespaceId = get(appConfig, 'NamespaceId');
  if (isEmpty(namespaceId)) return null;
  const [loaded, setLoaded] = useState(false);
  const [ingressList, setIngressList] = useState([]);
  const [slbList, setSlbList] = useState([]);
  const [albList, setAlbList] = useState([]);
  const [mseGatewayList, setMseGatewayList] = useState([]);
  const [apigList, setApigList] = useState([]);
  const [permissionInfo,setPermissionInfo] = useState({
    alb:true,
    clb:true,
    mse:true,
    apig:true,
  });

  useEffect(() => {
    getIngressList();
  }, [namespaceId]);

  const getIngressList = async () => {
    if (isEmpty(namespaceId)) return;
    const res = await services.getListIngresses({
      RegionId: regionId,
      NamespaceId: namespaceId,
      AppId: appId,
    });
    const _ingressList = get(res, 'Data.IngressList', []);
    const hasType = (type) => {
      return some(_ingressList, (ingress) => ingress.LoadBalanceType === type);
    };
    setLoaded(true);
    setIngressList(_ingressList);
    if (isEmpty(_ingressList)) return;

    hasType('clb') && getSlbList();
    hasType('alb') && getAlbList();
    hasType('mse') && getMseGatewayList();
    hasType('apig') && getApigGatewayList();
  };

  const getSlbList = async () => {
    const res = await services.getLoadBalancers({
      params: {
        PageNumber: 1,
        PageSize: 50,
        RegionId: regionId,
      },
      customErrorHandle: (error,data,callback) => {
        const forbidden = isForbidden(error.code);
        let _permissionInfo = cloneDeep(permissionInfo);
        _permissionInfo.clb = !forbidden;
        setPermissionInfo(_permissionInfo);
        !forbidden && callback?.();
      },
    });
    const _slbList = get(res, 'LoadBalancers.LoadBalancer', []);
    setLoaded(true);
    setSlbList(_slbList);
  };

  const getAlbList = async () => {
    const res = await services.getAlbLoadBalancers({
      params: {
        NextToken: '',
        MaxResults: 50,
        RegionId: regionId,
      },
      customErrorHandle: (error,data,callback) => {
        const forbidden = isForbidden(error.code);
        let _permissionInfo = cloneDeep(permissionInfo);
        _permissionInfo.alb = !forbidden;
        setPermissionInfo(_permissionInfo);
        !forbidden && callback?.();
      },
    });
    const _albList = get(res, 'LoadBalancers', []);
    setAlbList(_albList);
  };

  const getMseGatewayList = async () => {
    const vpcId = get(appConfig, 'VpcId');
    const res = await services.getListGateway({
      params: {
        PageNumber: 1,
        PageSize: 50,
        FilterParams: {
          Vpc: vpcId,
        },
      },
      customErrorHandle: (error,data,callback) => {
        const forbidden = isForbidden(error.code);
        let _permissionInfo = cloneDeep(permissionInfo);
        _permissionInfo.mse = !forbidden;
        setPermissionInfo(_permissionInfo);
        !forbidden && callback?.();
      },
    });

    const _mseGatewayList = get(res, 'Data.Result');
    setLoaded(true);
    setMseGatewayList(_mseGatewayList);
  };

  const getApigGatewayList = async () => {
    const vpcId = get(appConfig, 'VpcId');
    const res = await services.getHttpApiGateways({
      params: {
        pageSize: 200,
        pageNumber: 1,
      },
      customErrorHandle: (error,data,callback) => {
        const forbidden = isForbidden(error.code);
        let _permissionInfo = cloneDeep(permissionInfo);
        _permissionInfo.apig = !forbidden;
        setPermissionInfo(_permissionInfo);
        !forbidden && callback?.();
      },
    });
    const list = res?.data?.items || [];
    const _list = filter(list, item => {
      return item?.vpc?.vpcId === vpcId;
    });
    setLoaded(true);
    setApigList(_list);
  };

  return (
    <Card
      title={intl('saenext.components.application.BindRouteCard.GatewayRoutingSettings')}
      {...CardProps}
      className={className}
    >
      <RouteMessage
        loaded={loaded}
        regionId={regionId}
        ingressList={ingressList}
        slbList={slbList}
        namespaceId={namespaceId}
      />

      <Message
        type="notice"
        title={intl('saenext.components.application.BindRouteCard.ScenariosForGatewayRouting')}
        style={{ marginBottom: 8 }}
      >
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.components.application.BindRouteCard.ASingleApplicationOrMultiple')}
        </div>
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.components.application.BindRouteCard.ASingleApplicationOrMultiple.1')}
        </div>
      </Message>
      <RouteTable
        regionId={regionId}
        ingressList={ingressList}
        slbList={slbList}
        albList={albList}
        mseGatewayList={mseGatewayList}
        apigList={apigList}
        toAppCallback={toAppCallback}
        transmitCallback={transmitCallback}
        namespaceId={namespaceId}
        permissionInfo={permissionInfo}
      />

      <Button type="primary" onClick={() => transmitCallback()}>
        {intl('saenext.components.application.BindRouteCard.AddForwardingPolicy')}
      </Button>
    </Card>
  );
};

export default BindRouteCard;
