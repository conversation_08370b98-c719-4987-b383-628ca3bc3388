import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { get, isEmpty, isEqual } from 'lodash';
import services from "~/services";
import { Field, Form, Dialog, LinkButton, Icon, NumberPicker, Button, Message } from '@ali/cnd';
import CachedData from '../../../../cache/common';

const containerStyle = {
  display: 'flex',
  alignItems: 'start',
  width: '50%',
  marginBottom: 20,
  marginTop: -8,
};
const FormItem = Form.Item;

const LoadWarmUp = (props) => {
  const { regionId, appId, slb, autoWarmUp = true } = props;
  if (!autoWarmUp) return null;
  const internetIp = get(slb, 'InternetIp', '');
  const intranetIp = get(slb, 'IntranetIp', '');
  const [loading, setLoading] = useState(false);
  const [enableWarmup, setEnableWarmup] = useState(false);
  const [initWeight, setInitWeight] = useState(0);
  const [totalTime, setTotalTime] = useState(0);
  const [warmupVisible, setWarmupVisible] = useState(false);
  const field = Field.useField();
  const { init, validate, setValues } = field;

  useEffect(() => {
    if (internetIp || intranetIp) {
      getLoadBalancerWarmup();
    } else {
      setEnableWarmup(false);
    }
  }, [internetIp, intranetIp]);

  const getLoadBalancerWarmup = async () => {
    const res = await services.getLoadBalancerWarmup({
      AppId: appId,
      RegionId: regionId,
    });

    let _enableWarmup = !isEmpty(get(res, 'Data.Config'));
    const _initWeight = get(res, 'Data.Config.InitWeight', 0);
    const _totalTime = get(res, 'Data.Config.TotalTime', 0);
    setInitWeight(_initWeight);
    setTotalTime(_totalTime);
    setEnableWarmup(_enableWarmup);
  };

  const closeLoadBalancerWarmup = async () => {
    Dialog.alert({
      title: intl('saenext.components.application.LoadWarmUp.TurnOffSmallFlowPreheating'),
      content: intl('saenext.components.application.LoadWarmUp.AreYouSureYouWant'),
      onOk: () =>
        new Promise(async (resolve, reject) => {
          const res = await services.closeLoadBalancerWarmup({
            AppId: appId,
            RegionId: regionId,
          });
          console.log(res);
          const message = get(res, 'Message');
          if (isEqual(message, 'success')) {
            setEnableWarmup(false);
            setTotalTime(0);
            setInitWeight(0);
            resolve(true);
          } else {
            Message.error(intl('saenext.components.application.LoadWarmUp.FailedToSet'));
            reject(false);
          }
        }),
    });
  };

  const updateLoadBalancerWarmup = async () => {
    validate((error, values) => {
      const doSubmit = async (params) => {
        const res = await services.updateLoadBalancerWarmup({
          ...params,
        });
        const data = get(res, 'Data.Config', {});
        if (!isEmpty(data)) {
          const { InitWeight = 0, TotalTime = 0 } = data;
          setEnableWarmup(true);
          setInitWeight(InitWeight);
          setTotalTime(TotalTime);
          setWarmupVisible(false);
          Message.success(intl('saenext.components.application.LoadWarmUp.SetSuccessfully'));
        }
        setLoading(false);
      };

      if (error) return;
      setLoading(true);
      const params = {
        AppId: appId,
        RegionId: regionId,
        ...values,
      };
      doSubmit(params);
    });
  };

  const disabledWarmup = !internetIp && !intranetIp;

  return (
    <>
      <div style={containerStyle}>
        <div style={{ color: '#555', width: 160, height: 20, lineHeight: '20px' }}>
          {intl('saenext.components.application.LoadWarmUp.SmallFlowPreheating')}
        </div>
        <div style={{ color: '#333', flex: 1, height: 20, lineHeight: '20px' }}>
          {enableWarmup ? (
            <div style={{ display: 'flex', alignItems: 'end' }}>
              <div style={{ display: 'flex', flexDirection: 'column', minWidth: 140 }}>
                <div style={{ height: 20, lineHeight: '20px', marginBottom: 4 }}>
                  <span style={{ color: '#888' }}>
                    {intl('saenext.components.application.LoadWarmUp.InitialTrafficWeight')}
                  </span>
                  <span style={{ color: '#333' }}>{initWeight}%</span>
                </div>
                <div style={{ height: 20, lineHeight: '20px' }}>
                  <span style={{ color: '#888' }}>
                    {intl('saenext.components.application.LoadWarmUp.TotalWarmUpTime')}
                  </span>
                  <span style={{ color: '#333' }}>
                    {intl('saenext.components.application.LoadWarmUp.TotaltimeSeconds', {
                      totalTime: totalTime,
                    })}
                  </span>
                </div>
              </div>
              <div>
                <Button
                  text
                  iconSize="small"
                  onClick={() => {
                    setValues({
                      InitWeight: initWeight,
                      TotalTime: totalTime,
                    });
                    setWarmupVisible(true);
                  }}
                >
                  <Icon type="edit" />
                </Button>
                <Button
                  text
                  iconSize="small"
                  style={{ marginLeft: 8 }}
                  onClick={closeLoadBalancerWarmup}
                >
                  <Icon type="delete" />
                </Button>
              </div>
            </div>
          ) : disabledWarmup ? (
            <LinkButton disabled={true}>
              <span>{intl('saenext.components.application.LoadWarmUp.AddClbAccessToWarm')}</span>
              <Icon type="edit" size="small" style={{ marginLeft: 4 }} />
            </LinkButton>
          ) : (
            <LinkButton
              disabled={disabledWarmup}
              onClick={() => {
                setValues({
                  InitWeight: initWeight,
                  TotalTime: totalTime,
                });
                setWarmupVisible(true);
              }}
            >
              <span>{intl('saenext.components.application.LoadWarmUp.EnableSmallFlowWarmUp')}</span>
              <Icon type="edit" size="small" style={{ marginLeft: 4 }} />
            </LinkButton>
          )}
        </div>
      </div>
      <Dialog
        title={intl('saenext.components.application.LoadWarmUp.SmallFlowPreheatingFunction')}
        visible={warmupVisible}
        okProps={{
          loading: loading,
        }}
        onOk={updateLoadBalancerWarmup}
        onCancel={() => setWarmupVisible(false)}
        onClose={() => setWarmupVisible(false)}
      >
        <div style={{ width: 640 }}>
          <Message type="notice" style={{ marginBottom: 15 }}>
            <span>
              {intl('saenext.components.application.LoadWarmUp.AfterEnablingEachInstanceCan')}
            </span>
            <a
              href={CachedData.confLink('help:sae:bind-clb-for-app')}
              target="_blank"
            >
              {intl('saenext.components.application.LoadWarmUp.LearnMore')}
            </a>
          </Message>
          <Form field={field} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
            <FormItem
              label={intl('saenext.components.application.LoadWarmUp.InitialTrafficWeight.1')}
              required
              help={
                <>
                  <span>
                    {intl('saenext.components.application.LoadWarmUp.ThatIsTheClbWeight')}
                  </span>
                </>
              }
            >
              <NumberPicker
                {...init('InitWeight', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.application.LoadWarmUp.EnterInitialTrafficWeight',
                      ),
                    },
                  ],
                })}
                min={0}
                max={100}
                innerAfter="%"
                style={{ width: 370 }}
              />
            </FormItem>
            <FormItem
              label={intl('saenext.components.application.LoadWarmUp.TotalWarmUpTime.1')}
              required
              help={intl('saenext.components.application.LoadWarmUp.ThatIsTheWeightOf')}
            >
              <NumberPicker
                {...init('TotalTime', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.application.LoadWarmUp.EnterTheTotalWarmUp',
                      ),
                    },
                  ],
                })}
                min={0}
                max={600}
                innerAfter={intl('saenext.components.application.LoadWarmUp.Seconds')}
                style={{ width: 370 }}
              />
            </FormItem>
          </Form>
          <div style={{ color: '#888' }}>
            {intl('saenext.components.application.LoadWarmUp.WhenTheApplicationIsActivated')}
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default LoadWarmUp;
