import { intl } from '@ali/cnd';
import React, { useState, useEffect, useMemo } from 'react';
import { CndTable, Copy, Tab, ToolTipCondition, Message } from '@ali/cnd';
import { forIn, get, uniqueId } from 'lodash';

const BindK8s = (props) => {
  const { regionId, pvtzDiscovery, swimlanePvtzDiscovery } = props;
  const [serviceName, setServiceName] = useState('');
  const [portAndProtocols, setPortAndProtocols] = useState([]);
  const [enable, setEnable] = useState(false);
  const [swimlaneServiceName, setSwimlaneServiceName] = useState('');
  const [swimlanePortAndProtocols, setSwimlanePortAndProtocols] = useState([]);
  const [swimlaneEnable, setSwimlaneEnable] = useState(false);


  useEffect(() => {
    const {
      enable: _enable,
      serviceName,
      portAndProtocol,
    } = pvtzDiscovery && handleDiscoveryInfo(pvtzDiscovery);
    setEnable(_enable);
    setServiceName(serviceName);
    setPortAndProtocols(portAndProtocol);
  }, [pvtzDiscovery]);

  useEffect(() => {
    const {
      enable: _enable,
      serviceName,
      portAndProtocol,
    } = swimlanePvtzDiscovery && handleDiscoveryInfo(swimlanePvtzDiscovery);
    setSwimlaneEnable(_enable);
    setSwimlaneServiceName(serviceName);
    setSwimlanePortAndProtocols(portAndProtocol);
  }, [swimlanePvtzDiscovery]);

  const handleDiscoveryInfo = (data) => {
    const obj = JSON.parse(data) || {};
    const { enable = false, serviceName, portProtocols = [] } = obj;
    return {
      enable,
      serviceName,
      portAndProtocol: portProtocols,
    };
  };

  const renderContent = (type) => {
    let _serviceName = type === 'app' ? serviceName : swimlaneServiceName;
    let showServiceName = _serviceName;
    let accessDomians = [];
    if(_serviceName.includes('.')){
      showServiceName = _serviceName?.split('.')[0];
      accessDomians=[
        _serviceName,
        `${_serviceName}.svc.cluster.local`,
        `${_serviceName}.svc.cluster.local.${regionId}`
      ];
    }else{
      showServiceName = `${_serviceName}.svc.cluster.local.${regionId}`
    }
    return (
      <div>
        {_serviceName?.includes('.') && (
          <Message type="notice" className="mb-l">
            <div>
              <div>
                {intl.html("saenext.components.application.BindK8s.ServiceName.msessage.tip",{
                  serviceName: showServiceName
                })}
              </div>
              <ul>
                {accessDomians.map((item, index) => (
                  <li key={item}>
                    {index + 1}. <Copy text={item}>{item}</Copy>
                  </li>
                ))}
              </ul>
            </div>
          </Message>
        )}
        <div className="flex item-start mb-s">
          <div className="text-bold mr-xl">
            {type === 'app'
              ? intl('saenext.components.application.BindK8s.ServiceName')
              : intl('saenext.components.application.BindK8s.FullLinkGrayscaleServiceName')}
          </div>
          <div className="ml-xl">
            <Copy className="ml-s" showIcon={true} text={showServiceName}>
              {showServiceName}
            </Copy>
          </div>
        </div>
        <CndTable
          primaryKey="key"
          hasBorder={false}
          style={{ marginBottom: 20 }}
          dataSource={type === 'app' ? portAndProtocols : swimlanePortAndProtocols}
        >
          <CndTable.Column
            title={intl('saenext.components.application.BindK8s.Port')}
            dataIndex="port"
          />

          <CndTable.Column
            title={intl('saenext.components.application.BindK8s.ContainerPort')}
            dataIndex="targetPort"
          />

          <CndTable.Column
            title={intl('saenext.components.application.BindK8s.Agreement')}
            dataIndex="protocol"
          />
        </CndTable>
      </div>
    );
  };

  return (
    <div style={props.style}>
      {enable && swimlaneEnable && (
        <Tab unmountInactiveTabs shape="capsule" defaultActiveKey="app">
          <Tab.Item
            title={intl('saenext.components.application.BindK8s.KSBasedServiceApplication')}
            key="app"
          >
            <div className="mt-s">{renderContent('app')}</div>
          </Tab.Item>
          <Tab.Item
            title={intl('saenext.components.application.BindK8s.FullLinkGrayBasedOn')}
            key="gray"
          >
            <div className="mt-s">{renderContent('gray')}</div>
          </Tab.Item>
        </Tab>
      )}
      {enable && !swimlaneEnable && renderContent('app')}
      {!enable && swimlaneEnable && renderContent('gray')}
    </div>
  );
};

export default BindK8s;
