import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { isEmpty, get, noop } from 'lodash';
import BindIp from './BindIp';
import BindK8s from './BindK8s';
import BindNlb from './BindNlb';
import { Card, Tab } from '@ali/cnd';
import { confFeature } from '@alicloud/console-one-conf';

const CardProps = {
  showTitleBullet: false,
  showHeadDivider: false,
  contentHeight: 'auto',
};

const BindLanesCard = (props) => {
  const {
    regionId,
    appId,
    appConfig,
    appStatus,
    autoWarmUp = true,
    autoHttpsTls = true,
    targetContainer = 'v2',
    setSlbQpsRt = noop,
    setSlbQpsRtPermission = noop,
    boundCallback = noop,
    className,
  } = props;
  const [showK8sItem, setShowK8sItem] = useState(false);
  const _pvtzDiscovery = get(appConfig, 'PvtzDiscovery', '{}');
  const _swimlanePvtzDiscovery = get(appConfig, 'SwimlanePvtzDiscovery', '{}');
  const showNlbTab = confFeature('nlb_enable');

  useEffect(() => {
    let _showK8sItem = false;
    if (!isEmpty(appConfig)) {
      if (!isEmpty(_pvtzDiscovery) || !isEmpty(_swimlanePvtzDiscovery)) {
        const pvtzDiscovery = JSON.parse(_pvtzDiscovery);
        const swimlanePvtzDiscovery = JSON.parse(_swimlanePvtzDiscovery);
        const enable =
          get(pvtzDiscovery, 'enable', false) || get(swimlanePvtzDiscovery, 'enable', false);
        _showK8sItem = enable === 'true';
      }
    }
    setShowK8sItem(_showK8sItem);
  }, [appConfig]);

  return (
    <Card
      title={intl('saenext.components.application.BindLanesCard.ApplicationAccessSettings')}
      {...CardProps}
      className={className}
    >
      <Tab shape="wrapped" size="small" unmountInactiveTabs>
        {showNlbTab && (
          <Tab.Item title={intl('saenext.components.application.BindLanesCard.NlbBasedAccess')}>
            <BindNlb
              regionId={regionId}
              appId={appId}
              appStatus={appStatus}
              appConfig={appConfig}
              style={{ marginTop: 8 }}
            />
          </Tab.Item>
        )}
        <Tab.Item title={intl('saenext.components.application.BindLanesCard.ClbBasedAccess')}>
          <BindIp
            regionId={regionId}
            appId={appId}
            appStatus={appStatus}
            style={{ marginTop: 8 }}
            autoWarmUp={autoWarmUp}
            autoHttpsTls={autoHttpsTls}
            targetContainer={targetContainer}
            setSlbQpsRt={setSlbQpsRt}
            boundCallback={boundCallback}
            setSlbQpsRtPermission={setSlbQpsRtPermission}
          />
        </Tab.Item>
        {showK8sItem && (
          <Tab.Item title={intl('saenext.components.application.BindLanesCard.KSBasedServiceName')}>
            <BindK8s
              regionId={regionId}
              style={{ marginTop: 8 }}
              pvtzDiscovery={_pvtzDiscovery}
              swimlanePvtzDiscovery={_swimlanePvtzDiscovery}
            />
          </Tab.Item>
        )}
      </Tab>
    </Card>
  );
};

export default BindLanesCard;
