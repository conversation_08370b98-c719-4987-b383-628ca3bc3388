import { intl } from '@ali/cnd';
import React from 'react';
import CndTable from '@ali/cnd-table';
import { Copy, Actions } from '@ali/cnd';
import services from "~/services";
import { isEqual, isEmpty, get, map } from 'lodash';
import CachedData from '../../../../../cache/common';

const { LinkButton } = Actions;

const SlsContainer = (props) => {
  const { regionId, applicationId } = props;

  const fetchData = async (params) => {
    const res = await services.getSlsLogs({
      params: {
        CurrentPage: params.current,
        PageSize: params.pageSize,
        RegionId: regionId,
        AppId: applicationId,
      },
      customErrorHandle: (error, data, callback) => {
        callback && callback();
        return {
          data: [],
          total: 0,
        };
      },
    });
    const _data = get(res, 'Data.LogConfigs', []);
    const _total = get(res, 'Data.TotalSize', 0);
    return {
      data: map(_data, (item) => {
        return {
          ...item,
          primaryKey: `${item.LogDir}-${item.CreateTime}`,
        };
      }),
      total: _total,
    };
  };

  const columns = [
    {
      key: 'LogDir',
      title: intl('saenext.components.log-control.SlsContainer.LogSource'),
      dataIndex: 'LogDir',
      cell: (value, index, record) => {
        if (isEqual(record.LogType, 'stdout')) {
          return 'stdout.log';
        }
        if (isEmpty(value)) return '-';
        return <Copy text={value}>{value}</Copy>;
      },
    },
    {
      key: '',
      title: intl('saenext.components.log-control.SlsContainer.Consumer'),
      dataIndex: '',
      cell: () => <>{intl('saenext.components.log-control.SlsContainer.SlsLogService')}</>,
    },
    {
      key: 'SlsLogStore',
      title: 'SLS LogStore',
      dataIndex: 'SlsLogStore',
      cell: (value) => {
        if (isEmpty(value)) return '-';
        return <Copy text={value}>{value}</Copy>;
      },
    },
    {
      key: 'SlsProject',
      title: 'SLS Project',
      dataIndex: 'SlsProject',
      cell: (value) => {
        if (isEmpty(value)) return '-';
        return <Copy text={value}>{value}</Copy>;
      },
    },
    {
      key: 'CreateTime',
      title: intl('saenext.components.log-control.SlsContainer.CreationTime'),
      dataIndex: 'CreateTime',
    },
    {
      key: '',
      title: intl('saenext.components.log-control.SlsContainer.Operation'),
      dataIndex: '',
      width: 120,
      cell: (value, _, record) => {
        const { SlsProject, SlsLogStore } = record;
        return (
          <Actions>
            <LinkButton
              onClick={() => {
                window.open(
                  `${CachedData.confLink('feature:sls:url')}/lognext/project/${SlsProject}/logsearch/${SlsLogStore}`,
                );
              }}
            >
              {intl('saenext.components.log-control.SlsContainer.FileLog')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];

  return (
    <>
      <CndTable
        fetchData={fetchData}
        primaryKey="primaryKey"
        columns={columns as []}
        recordCurrent
        pagination={{
          hideOnlyOnePage: true,
          pageSizeList: [10, 20, 50, 100],
        }}
      />
    </>
  );
};

export default SlsContainer;
