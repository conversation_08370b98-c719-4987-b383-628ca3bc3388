import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Loading, Message, Select, Icon, Button, Dialog } from '@ali/cnd';
import { get, isEqual, isEmpty, map, flatten, cloneDeep, noop, find, keys } from 'lodash';
import services from "~/services";
import PodIndicator from './PodIndicator';
import { LogEditor } from '@ali/cnd';
import If from '../../../../shared/If';
import CachedData from '../../../../../cache/common';

const frequencies = [
  { value: 15000, label: '15s' },
  { value: 10000, label: '10s' },
  { value: 5000, label: '5s' },
  { value: -1, label: intl('saenext.components.log-control.RealtimeLog.DoNotRefresh') },
];

const RealtimeLog = (props) => {
  const {
    regionId,
    applicationId,
    appConfig,
    podId,
    containerId,
    instanceDisabled,
    onContainerChange,
    callback = noop,
  } = props;
  const isJava = isEqual(get(appConfig, 'ProgrammingLanguage'), 'java');
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [podInstances, setPodInstances] = useState([]);
  const [podInstanceId, setPodInstanceId] = useState('');
  const [sidecarId, setSidecarId] = useState(containerId || 'main');
  const [frequency, setFrequency] = useState(15000);
  const [value, setVaule] = useState();
  const [refresh, setRefresh] = useState(false);
  const timer = useRef(null);

  const sidecarSource = useMemo(() => {
    const curPod = find(podInstances, { value: podInstanceId });
    const { SidecarContainersStatus } = curPod || {};

    if (isEmpty(SidecarContainersStatus)) return [];

    const sidecars = map(SidecarContainersStatus, (v) => ({
      label: intl('saenext.components.log-control.RealtimeLog.SidecarContainerVcontainerid', {
        vContainerId: v.ContainerId,
      }),
      value: v.ContainerId,
    }));
    return [
      {
        label: intl('saenext.components.log-control.RealtimeLog.MainApplicationContainer'),
        value: 'main',
      },
      ...sidecars,
    ];
  }, [podInstances, podInstanceId]);

  useEffect(() => {
    getApplicationGroups();
    return () => {
      callback && callback();
      if (timer.current) {
        clearInterval(timer.current);
      }
    };
  }, []);

  const getApplicationGroups = async () => {
    setLoading(true);
    setIsLoading(true);
    const res = await services.DescribeApplicationGroups({
      AppId: applicationId,
    });
    const _groups = get(res, 'Data', []);
    if (isEmpty(_groups)) {
      setLoading(false);
      setIsLoading(false);
      return;
    }
    const podPromises = map(_groups, async (v) => await getPodsByGroup(v.GroupId));
    let _podInstances = await Promise.all(podPromises);
    _podInstances = flatten(_podInstances);
    if (isEmpty(_podInstances)) {
      setLoading(false);
      setIsLoading(false);
      return;
    }
    _podInstances = map(_podInstances, (pod) => ({
      ...pod,
      value: pod.InstanceId,
      status: pod.InstanceContainerStatus,
      instanceContainerIp: pod.InstanceContainerIp,
      instanceId: pod.InstanceId,
      label: (
        <>
          <PodIndicator value={pod.InstanceContainerStatus} />
          <span style={{ marginLeft: 10, color: '#555' }}>{pod.InstanceContainerIp}</span>
          <span style={{ marginLeft: 10 }}>{pod.InstanceId}</span>
        </>
      ),
    }));
    setLoading(false);

    // 默认选中一个 pod 或者从路由获取
    let isExitPod = !isEmpty(podId);
    if (isExitPod) {
      isExitPod = _podInstances.some((pod) => isEqual(pod.value, podId));
    }
    const _podInstanceId = isExitPod ? podId : get(_podInstances, '[0].value');
    setPodInstances(_podInstances);
    setPodInstanceId(_podInstanceId);
    setAutoRefreshTimer(_podInstanceId, frequency, _podInstances, sidecarId);
  };

  const getPodsByGroup = async (groupId) => {
    const res = await services.DescribeApplicationInstances({
      PageSize: 999,
      CurrentPage: 1,
      AppId: applicationId,
      GroupId: groupId,
    });
    const pods = get(res, 'Data.Instances', []);
    return pods;
  };

  const getInstanceLog = async (instanceId, callback?, podInstances?, sidecarId?) => {
    const res = await services.getInstanceLog({
      params: {
        RegionId: regionId,
        InstanceId: instanceId,
        ContainerId: sidecarId === 'main' ? undefined : sidecarId,
      },
      customErrorHandle: (error, data, callback) => {
        setRefresh(false);
        setIsLoading(false);
        // 出错之后取消轮询接口
        if (timer.current) {
          clearInterval(timer.current);
          setFrequency(-1);
        }
        const notEmptyCode = isEqual(get(error, 'code'), 'InvalidParameter.NotEmpty');
        const specifyMessage = isEqual(
          get(error, 'response.data.data.Message'),
          'You must specify the parameter instanceId.',
        );
        if (notEmptyCode && specifyMessage) {
          localRefreshPodInstances(instanceId, podInstances);
          const dialog = Dialog.alert({
            title: intl('saenext.components.log-control.RealtimeLog.RebuildOrDeleteAPod'),
            content: (
              <p style={{ width: 460 }}>
                {intl(
                  'saenext.components.log-control.RealtimeLog.TheCurrentPodInstanceInstanceid',
                  { instanceId: instanceId },
                )}
              </p>
            ),

            onOk: () => {
              dialog.hide();
              getApplicationGroups();
            },
          });
          return;
        }
        callback && callback();
      },
    });
    const data = get(res, 'Data');
    callback && callback(data);
    return data;
  };

  const localRefreshPodInstances = (instanceId, podInstances) => {
    let _podInstances = cloneDeep(podInstances);
    _podInstances = _podInstances.map((pod) => {
      if (isEqual(pod.value, instanceId)) {
        pod.status = 'running';
        return {
          ...pod,
          status: 'RebuildOrDeleted',
          label: (
            <>
              <PodIndicator value="RebuildOrDeleted" />
              <span style={{ marginLeft: 10, color: '#555' }}>{pod.instanceContainerIp}</span>
              <span style={{ marginLeft: 10 }}>{pod.instanceId}</span>
            </>
          ),
        };
      }
      return {
        ...pod,
      };
    });
    setPodInstances(_podInstances);
  };

  const setAutoRefreshTimer = async (instanceId?, interval?, podInstances?, sidecarId?) => {
    // 切换pod 和 刷新频率都要删掉之前的计数器
    if (timer.current) {
      clearInterval(timer.current);
    }
    if (interval > 0) {
      timer.current = setInterval(() => {
        const callback = (_value) => {
          setVaule(_value);
          setRefresh(false);
          setIsLoading(false);
        };
        setRefresh(true);
        getInstanceLog(instanceId, callback, podInstances, sidecarId);
      }, interval);
    }
    setIsLoading(true);
    // @ts-ignore
    setVaule('');
    const _value = await getInstanceLog(instanceId, noop, podInstances, sidecarId);
    setVaule(_value);
    setIsLoading(false);
  };

  return (
    <>
      <Message
        type="notice"
        className="mb-s"
        title={intl('saenext.components.log-control.RealtimeLog.RealTimeLogsAreStandard')}
      >
        <div className="text-line">
          {intl('saenext.components.log-control.RealtimeLog.CollectRealTimeStandardOutput')}
          <a
            href={CachedData.confLink('help:sae:configure-log-collection-to-sls')}
            target="_blank"
          >
            {intl('saenext.components.log-control.RealtimeLog.SetLogCollectionToSls')}
          </a>
        </div>
        <div className="text-line">
          {intl('saenext.components.log-control.RealtimeLog.RedirectRealTimeStandardOutput')}
          <span style={{ color: '#ffa500' }}>
            {intl('saenext.components.log-control.RealtimeLog.NoteThisMethodCanOnly')}
          </span>
          {intl('saenext.common.parenthesis.right')}
        </div>
      </Message>
      {isJava ? (
        <Message
          type="notice"
          className="mb-s"
          title={intl(
            'saenext.components.log-control.RealtimeLog.JavaMicroserviceRegistrationConfigurationCenter',
          )}
        >
          <div className="text-line">
            {intl('saenext.components.log-control.RealtimeLog.ServiceRegistrationIsNormalBut')}
          </div>
          <div className="text-line">
            {intl('saenext.components.log-control.RealtimeLog.IfYouCannotConnectTo')}
          </div>
          <div className="text-line">
            {intl('saenext.components.log-control.RealtimeLog.IfYouCannotReadThe')}
            <a href={CachedData.confLink('help:sae:faq-about-microservices')} target="_blank">
              {intl('saenext.components.log-control.RealtimeLog.MicroservicesQA')}
            </a>
          </div>
        </Message>
      ) : null}

      <div className="flex full-width">
        <Select
          label={intl('saenext.components.log-control.RealtimeLog.PodName')}
          className="mr-s mb-s"
          style={{ minWidth: 520 }}
          value={podInstanceId}
          // @ts-ignore
          state={loading ? 'loading' : ''}
          dataSource={podInstances}
          onChange={(_instanceId: string) => {
            setPodInstanceId(_instanceId);
            setAutoRefreshTimer(_instanceId, frequency, podInstances, sidecarId);
          }}
          disabled={instanceDisabled}
        />

        <If condition={sidecarSource.length > 0}>
          <Select
            className="mr-s mb-s"
            style={{ minWidth: 200 }}
            value={sidecarId}
            state={loading ? 'loading' : undefined}
            dataSource={sidecarSource}
            onChange={(val) => {
              setSidecarId(val);
              setAutoRefreshTimer(podInstanceId, frequency, podInstances, val);
              onContainerChange?.(val);
            }}
          />
        </If>

        <Select
          label={
            <>
              <span style={{ marginRight: 4 }}>
                {intl('saenext.components.log-control.RealtimeLog.RealTimeLogRefreshFrequency')}
              </span>
              {refresh ? <Icon type="loading" /> : <Icon type="refresh" />}
            </>
          }
          className="mr-s mb-s"
          value={frequency}
          dataSource={frequencies}
          onChange={(_frequency: number) => {
            setFrequency(_frequency);
            setAutoRefreshTimer(podInstanceId, _frequency, podInstances, sidecarId);
          }}
        />

        {frequency > 0 ? null : (
          <Button iconSize="small" className="mr-s mb-s" onClick={getApplicationGroups}>
            <Icon type="refresh" />
          </Button>
        )}
      </div>
      <Loading
        visible={isLoading}
        style={{
          width: '100%',
          overflow: 'auto',
          minHeight: `calc(100% - ${isJava ? 200 : 80}px)`,
        }}
      >
        <LogEditor value={value} />
      </Loading>
    </>
  );
};

export default RealtimeLog;
