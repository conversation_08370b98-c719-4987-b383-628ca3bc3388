import { intl } from '@ali/cnd';
import React from 'react';
import { Message, Tab, Icon } from '@ali/cnd';
import SlsContainer from './SlsContainer';
import KafkaContainer from './KafkaContainer';
import { noop } from 'lodash';
import CachedData from '../../../../../cache/common';

const PersistLog = (props) => {
  const { regionId, applicationId, showKafka = true, callback = noop } = props;
  const [activeKey, setActiveKey] = React.useState('sls');

  return (
    <>
      <Message type="notice" className="mb-s">
        {intl('saenext.components.log-control.PersistLog.WhenYouCreateOrDeploy')}
      </Message>
      <Tab
        shape="wrapped"
        // @ts-ignore
        onChange={(key) => setActiveKey(key)}
        extra={
          <>
            <Icon type="help" size="xs" className="mr-xs" style={{ color: '#0064c8' }} />
            {activeKey === 'sls' ? (
              <a
                href={CachedData.confLink('help:sae:configure-log-collection-to-sls')}
                target="_blank"
              >
                {intl('saenext.components.log-control.PersistLog.HowToCollectLogsTo')}
              </a>
            ) : (
              <a
                href={CachedData.confLink('help:sae:collect-logs-to-kafka')}
                target="_blank"
              >
                {intl('saenext.components.log-control.PersistLog.HowToCollectLogsTo.1')}
              </a>
            )}
          </>
        }
      >
        <Tab.Item
          key="sls"
          title={intl('saenext.components.log-control.PersistLog.LogCollectionToSls')}
        >
          <SlsContainer regionId={regionId} applicationId={applicationId} />
        </Tab.Item>
        {showKafka ? (
          <Tab.Item
            key="kafka"
            title={intl('saenext.components.log-control.PersistLog.LogCollectionToKafka')}
          >
            <KafkaContainer regionId={regionId} applicationId={applicationId} />
          </Tab.Item>
        ) : null}
      </Tab>
    </>
  );
};

export default PersistLog;
