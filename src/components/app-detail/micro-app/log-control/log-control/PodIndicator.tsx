import { intl } from '@ali/cnd';
import React, { memo } from 'react';
import { StatusIndicator } from '@ali/cnd';
import { isEmpty } from 'lodash';

export const MAP = {
  Running: {
    type: 'success',
    text: intl('saenext.components.log-control.PodIndicator.Running'),
  },
  Failed: {
    type: 'error',
    text: intl('saenext.components.log-control.PodIndicator.Failed'),
  },
  Unknown: {
    type: 'disabled',
    text: intl('saenext.components.log-control.PodIndicator.Unknown'),
  },
  Pending: {
    type: 'loading',
    text: intl('saenext.components.log-control.PodIndicator.Waiting'),
  },
  Succeeded: {
    type: 'success',
    text: intl('saenext.components.log-control.PodIndicator.Completed'),
  },
  BackOff: {
    type: 'warning',
    text: intl('saenext.components.log-control.PodIndicator.RetreatRetry'),
  },
  ErrImagePull: {
    type: 'error',
    text: intl('saenext.components.log-control.PodIndicator.FailedToPullTheImage'),
  },
  ImagePullBackOff: {
    type: 'error',
    text: intl('saenext.components.log-control.PodIndicator.FailedToPullTheImage'),
  },
  CrashLoopBackOff: {
    type: 'error',
    text: intl('saenext.components.log-control.PodIndicator.ContainerCrashesOrExits'),
  },
  RebuildOrDeleted: {
    type: 'disabled',
    text: intl('saenext.components.log-control.PodIndicator.RebuildOrDelete'),
  },
};

const PodIndicator = ({ value }) => {
  if (isEmpty(value)) return <span>--</span>;
  const condition = MAP[value] || {};
  if (isEmpty(condition)) {
    return (
      <StatusIndicator type="warning" shape="dot">
        {value}
      </StatusIndicator>
    );
  }
  const { type, text } = condition;
  return (
    <StatusIndicator type={type} shape="dot">
      {text}
    </StatusIndicator>
  );
};

export default memo(PodIndicator);
