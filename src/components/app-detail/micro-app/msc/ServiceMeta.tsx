import { intl } from '@ali/cnd';
import React, { FC, useState, useEffect } from 'react';
import { SlidePanel, Tag, Table, Truncate } from '@ali/cnd';
import services from '../../../../services';
import CndTable, { ISearch } from '@ali/cnd-table';
import { get, isEmpty, map, join, filter, includes } from 'lodash';

type Props = {
  applicationID: string;
  record: Record<string, any>;
};
const { Group: TagGroup } = Tag;

const ServiceMeta: FC<Props> = (props) => {
  const { record, applicationID, children } = props;
  const [isShowing, setIsShowing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  // @ts-ignore
  const [metadata, setMetadata] = useState([]);

  const fetchData = async (params) => {
    if (!isShowing) return;
    setIsLoading(true);
    const res = await services.describeAppServiceDetail({
      AppId: applicationID,
      ServiceType: record.ServiceType,
      ServiceGroup: record.ServiceGroup,
      ServiceVersion: record.ServiceVersion,
      ServiceName: record.ServiceName,
    });
    let _methods = get(res, 'Data.Methods', []);
    _methods = filter(_methods, (method) => {
      const { MethodController = '', Name = '', Paths: Path = '' } = params;
      let hasController = true;
      let hasPath = true;
      let hasName = true;
      if (!isEmpty(MethodController)) {
        hasController = method.MethodController.indexOf(MethodController) > -1;
      }
      if (!isEmpty(Name)) {
        hasName = method.Name.indexOf(Name) > -1;
      }
      if (!isEmpty(Path)) {
        hasPath = includes(method.Paths, Path);
      }
      return hasController && hasPath && hasName;
    });
    let _metadata = get(res, 'Data.Metadata', {});
    _metadata = flatMetaData(_metadata);
    _metadata = convertObjectToArray(_metadata);
    setMetadata(_metadata);
    setIsLoading(false);
    return {
      data: map(_methods, (method) => ({
        ...method,
        primaryKey: `${method.MethodController}.${method.Name}`,
      })),
      total: 0,
    };
  };

  const flatMetaData = (data) => {
    let flatData = { ...data };
    for (const i in flatData) {
      const value = flatData[i];
      if (Object.prototype.toString.call(value) === '[object Object]') {
        for (const index in value) {
          const val = value[index];
          if (Object.prototype.toString.call(val) === '[object Object]') {
            for (const idx in val) {
              const v = val[idx];
              if (Object.prototype.toString.call(v) === '[object Object]') {
              } else {
                flatData[`${i}.${index}.${idx}`] = v;
              }
            }
          } else {
            flatData[`${i}.${index}`] = value[index];
          }
        }
        delete flatData[i];
      }
    }
    return flatData;
  };

  const convertObjectToArray = (data) => {
    const keys = Object.keys(data);
    const result = [];

    for (let i = 0; i < keys.length; i += 4) {
      // 取出键，确保不会超出原对象的键的数量
      const k1 = keys[i];
      const v1 = data[k1];
      const k2 = keys[i + 1];
      const v2 = k2 !== undefined ? data[k2] : undefined;

      if (k2 === undefined) {
        result.push({ k1, v1 });
        break;
      } else {
        result.push({ k1, v1, k2, v2 });
      }
    }
    console.log(result);
    return result;
  };

  const columns = React.useMemo(() => {
    // 当开启了微服务治理之后 需要提供查看元数据的功能
    let _springCloudColumns = [
      {
        key: 'MethodController',
        title: intl('saenext.components.msc.ServiceMeta.Class'),
        dataIndex: 'MethodController', // 166 - 32
        cell: (value) => <div style={{ width: 130, wordBreak: 'break-all' }}>{value || '-'}</div>,
      },
      {
        key: 'RequestMethods',
        title: intl('saenext.components.msc.ServiceMeta.RequestMethod'),
        dataIndex: 'RequestMethods',
        cell: (value) => (
          <div style={{ width: 90, wordBreak: 'break-all' }}>
            {isEmpty(value) ? '-' : join(value, ', ')}
          </div>
        ),
      },
      {
        key: 'Paths',
        title: intl('saenext.components.msc.ServiceMeta.RequestPath'),
        dataIndex: 'Paths',
        cell: (value) => {
          if (isEmpty(value)) return '-';
          return (
            <div style={{ width: 130, display: 'flex', flexWrap: 'wrap' }}>
              {map(value, (item) => (
                <Tag key={JSON.stringify(item)} type="normal" size="small">
                  <Truncate type="width" threshold={120}>
                    <span>{item}</span>
                  </Truncate>
                </Tag>
              ))}
            </div>
          );
        },
      },
      {
        key: 'Name',
        title: intl('saenext.components.msc.ServiceMeta.MethodNameDescription'),
        dataIndex: 'Name',
        cell: (value, index, record) => {
          if (isEmpty(value)) return '-';
          return (
            <div style={{ width: 160, display: 'flex', flexDirection: 'column' }}>
              <Truncate type="width" threshold={146}>
                <span>{value}</span>
                {!isEmpty(record.NameDetail) ? (
                  <span style={{ color: '#999' }}> / {record.NameDetail}</span>
                ) : null}
              </Truncate>
            </div>
          );
        },
      },
      {
        key: 'ParameterDefinitions',
        title: intl('saenext.components.msc.ServiceMeta.ParameterListDescription'),
        dataIndex: 'ParameterDefinitions',
        cell: (value) => {
          if (isEmpty(value)) return '-';
          return (
            <div style={{ width: 160, display: 'flex', flexWrap: 'wrap' }}>
              {map(value, (item) => (
                <Truncate type="width" threshold={146}>
                  <span>{item.Type}</span>
                  {!isEmpty(item.Description) ? (
                    <span style={{ color: '#999' }}> / {item.Description}</span>
                  ) : null}
                </Truncate>
              ))}
            </div>
          );
        },
      },
    ];

    let _dubboColumns = [
      {
        key: 'Name',
        title: intl('saenext.components.msc.ServiceMeta.MethodName'),
        dataIndex: 'Name',
        width: 200,
        cell: (value) => <div>{value || '-'}</div>,
      },
      {
        key: 'ParameterTypes',
        title: intl('saenext.components.msc.ServiceMeta.ParameterList'),
        dataIndex: 'ParameterTypes',
        cell: (value) => {
          if (isEmpty(value)) return '-';
          return (
            <TagGroup>
              {map(value, (item) => (
                <Tag key={item} type="normal" size="small">
                  {item}
                </Tag>
              ))}
            </TagGroup>
          );
        },
      },
      {
        key: 'ReturnType',
        title: intl('saenext.components.msc.ServiceMeta.ReturnType'),
        dataIndex: 'ReturnType',
        cell: (value) => <div>{value || '-'}</div>,
      },
    ];

    const serviceType = get(record, 'ServiceType', 'dubbo');
    return serviceType === 'dubbo' ? _dubboColumns : _springCloudColumns;
  }, [record]);

  const search = React.useMemo(() => {
    let _dubboSearch = {
      defaultDataIndex: 'Name',
      defaultSelectedDataIndex: 'Name',
      onlySupportOne: false,
      options: [
        {
          label: intl('saenext.components.msc.ServiceMeta.MethodName'),
          dataIndex: 'Name',
          template: 'input',
          templateProps: {
            placeholder: intl('saenext.components.msc.ServiceMeta.EnterAMethodName'),
          },
        },
      ],
    };
    let _springCloudSearch = {
      defaultDataIndex: 'MethodController',
      defaultSelectedDataIndex: 'MethodController',
      onlySupportOne: false,
      options: [
        {
          label: intl('saenext.components.msc.ServiceMeta.Class'),
          dataIndex: 'MethodController',
          template: 'input',
          templateProps: {
            placeholder: intl('saenext.components.msc.ServiceMeta.EnterAClassName'),
          },
        },
        {
          label: intl('saenext.components.msc.ServiceMeta.Path'),
          dataIndex: 'Paths',
          template: 'input',
          templateProps: {
            placeholder: intl('saenext.components.msc.ServiceMeta.EnterAPath'),
          },
        },
        {
          label: intl('saenext.components.msc.ServiceMeta.MethodName'),
          dataIndex: 'Name',
          template: 'input',
          templateProps: {
            placeholder: intl('saenext.components.msc.ServiceMeta.EnterAMethodName'),
          },
        },
      ],
    };
    const serviceType = get(record, 'ServiceType', 'dubbo');
    return serviceType === 'dubbo' ? _dubboSearch : _springCloudSearch;
  }, [record]);

  return (
    <>
      <span onClick={() => setIsShowing((prev) => !prev)}>{children}</span>
      <SlidePanel
        title={intl('saenext.components.msc.ServiceMeta.RecordservicenameMetadata', {
          recordServiceName: record.ServiceName,
        })}
        width={880}
        isShowing={isShowing}
        onClose={() => setIsShowing(false)}
      >
        <p style={{ marginBottom: 8, fontWeight: 500 }}>
          {intl('saenext.components.msc.ServiceMeta.InterfaceMetadata')}
        </p>
        <CndTable
          fetchData={fetchData}
          primaryKey="primaryKey"
          // @ts-ignore
          columns={columns}
          showRefreshButton
          // @ts-ignore
          pagination={false}
          search={search as ISearch}
        />

        <p style={{ marginTop: 16, marginBottom: 8, fontWeight: 500 }}>
          {intl('saenext.components.msc.ServiceMeta.Metadata')}
        </p>
        <CndTable
          loading={isLoading}
          dataSource={metadata}
          columns={[
            {
              title: 'Key',
              dataIndex: 'k1',
              cell: (value) => {
                if (isEmpty(value)) return '-';
                return (
                  <div style={{ width: 120 }}>
                    <Truncate type="width" threshold={110}>
                      <span>{value}</span>
                    </Truncate>
                  </div>
                );
              },
            },
            {
              title: 'Value',
              dataIndex: 'v1',
              cell: (value) => {
                if (isEmpty(value)) return '-';
                return <div style={{ width: 230, wordBreak: 'break-all' }}>{value}</div>;
              },
            },
            {
              title: 'Key',
              dataIndex: 'k2',
              cell: (value) => {
                if (isEmpty(value)) return '-';
                return (
                  <div style={{ width: 120 }}>
                    <Truncate type="width" threshold={110}>
                      <span>{value}</span>
                    </Truncate>
                  </div>
                );
              },
            },
            {
              title: 'Value',
              dataIndex: 'v2',
              cell: (value) => {
                if (isEmpty(value)) return '-';
                return <div style={{ width: 230, wordBreak: 'break-all' }}>{value}</div>;
              },
            },
          ]}
        />
      </SlidePanel>
    </>
  );
};

export default ServiceMeta;
