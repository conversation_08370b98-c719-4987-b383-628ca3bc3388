import React, { useEffect } from 'react';
import MscIframe from './MscIframe';
import MscInterface from '~/components/app-detail/micro-app/msc/InterfaceDetails';
import { get, isEqual } from 'lodash';
import CachedData from '~/cache/common';

export default (props) => {
  const { history, location, regionId, appId, appStatus, NewSaeVersion, microServiceEnable, isNewMsc } = props;
  // 新应用+运行中+LastChangeOrderRunning=false 才可开启 Msc
  const isRunning = isEqual(get(appStatus, 'CurrentStatus'), 'RUNNING');
  const noChangeOrder = get(appStatus, 'LastChangeOrderRunning') !== true
  const isStartMsc = isRunning && noChangeOrder;


  useEffect(() => {
    window.addEventListener('message', handleReceiveMessage);
    return () => {
      window.removeEventListener('message', handleReceiveMessage);
    };
  }, []);

  const handleReceiveMessage = (event) => {
    if ([
      CachedData.confLink('ffeature:pre-mse:url'),
      CachedData.confLink('feature:mse:url')
    ].includes(event.origin)) {
      if (event.data === 'handleNodeDetail') {
        // 调用父窗口中的方法
        handleNodeDetail();
      }
    }
  };

  const handleNodeDetail = () => {
    // 节点详情
    const { search } = location;
    history.push(
      `/${regionId}/app-list/${appId}/micro-app/msc-node${search}`,
    );
  };

  return (
    <MscInterface
      history={history}
      regionId={regionId}
      applicationID={appId}
      isStartMsc={isStartMsc}
      NewSaeVersion={NewSaeVersion}
      microServiceEnable={microServiceEnable}
      isNewMsc={isNewMsc}
      children= {
        (mseApp) => {
          const { MseAppId, MseAppName, MseAppNameSpace } = mseApp || {};
          const channel = get(window, 'ALIYUN_CONSOLE_CONFIG.CHANNEL', '');
          const baseUrl = CachedData.confLink('feature:mse:url');
          // 接口详情
          return (
            <MscIframe
              src={`${baseUrl}#/msc/appList/info/systemGuardApiDetails?AppId=${MseAppId}&region=${regionId}&appName=${MseAppName}&ns=${MseAppNameSpace}&hideTopbar=true&hideNavbar=true&saeReference=true`}
            />
          );
        }
      }
    />
  );
};
