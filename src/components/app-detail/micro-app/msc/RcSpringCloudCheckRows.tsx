import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import { Button, LinkButton, Grid, Input, Select, Icon } from '@ali/cnd';
import { get, includes, split, isEmpty, cloneDeep, forEach } from 'lodash';
import If from '../../../shared/If';

const { Row, Col } = Grid;
const ParamTypes = [
  { label: '<PERSON>ie', value: 'cookie' },
  { label: 'Header', value: 'header' },
  { label: 'Parameter', value: 'param' },
];

const Conditions = [
  { label: '=', value: '==' },
  { label: '!=', value: '!=' },
  { label: '>', value: '>' },
  { label: '<', value: '<' },
  { label: '>=', value: '>=' },
  { label: '<=', value: '<=' },
  { label: 'in', value: 'list' },
  { label: intl('saenext.components.msc.RcSpringCloudCheckRows.Percentage'), value: '%' },
  {
    label: intl('saenext.components.msc.RcSpringCloudCheckRows.Die'),
    value: 'mod',
    children: [
      { value: 'mod-==', label: intl('saenext.components.msc.RcSpringCloudCheckRows.Die.1') },
      { value: 'mod-!=', label: intl('saenext.components.msc.RcSpringCloudCheckRows.Mold') },
      { value: 'mod->', label: intl('saenext.components.msc.RcSpringCloudCheckRows.Die.2') },
      { value: 'mod-<', label: intl('saenext.components.msc.RcSpringCloudCheckRows.Die.3') },
      { value: 'mod->=', label: intl('saenext.components.msc.RcSpringCloudCheckRows.Die.4') },
      { value: 'mod-<=', label: intl('saenext.components.msc.RcSpringCloudCheckRows.Die.5') },
    ],
  },
];

const formatRowItem = (item) => {
  const translateCond = (cond) => {
    switch (cond) {
      case 'list':
        return '==';
      case '%':
        return '<';
      default:
        return includes(cond, 'mod') ? get(split(cond, '-'), '[1]') : cond;
    }
  };

  const translateOperator = (cond) => {
    switch (cond) {
      case 'list':
        return 'list';
      case '%':
        return 'deterministic_proportional_steaming_division';
      default:
        return includes(cond, 'mod') ? 'mod' : 'rawvalue';
    }
  };
  const { cond } = item;
  const newItem = {
    ...item,
    cond: translateCond(cond),
    operator: translateOperator(cond),
  };
  return newItem;
};

export const formatScRules = (scRules) => {
  const _scRules = cloneDeep(scRules);
  let __scRules = [];
  forEach(_scRules, (rule) => {
    const _rule = cloneDeep(rule);
    let _items = [];
    forEach(_rule.scItems, (item) => {
      _items.push(formatRowItem(item));
    });
    _rule.items = _items;
    Reflect.deleteProperty(_rule, 'key');
    Reflect.deleteProperty(_rule, 'type');
    Reflect.deleteProperty(_rule, 'scItems');
    Reflect.deleteProperty(_rule, 'dubboItems');
    __scRules.push(_rule);
  });
  return __scRules;
};

export default (props) => {
  const { value = [], onChange, isPreview } = props;

  useEffect(() => {
    if (!isEmpty(value)) return;
    onChange([{ type: 'cookie', name: '', cond: '', value: '' }]);
  }, []);

  const handleChange = (key, value, index) => {
    let _value = get(props, 'value', []);
    _value = _value.map((item, idx) => {
      if (idx === index) {
        return { ...item, type: item.type || 'cookie', [key]: value };
      }
      return { ...item, type: item.type || 'cookie' };
    });
    onChange && onChange(_value);
  };

  return (
    <div style={{ background: '#f6f6f6', padding: 16 }}>
      <Row style={{ color: '#555' }}>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.msc.RcSpringCloudCheckRows.ParameterType')}
        </Col>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.msc.RcSpringCloudCheckRows.Parameter')}
        </Col>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.msc.RcSpringCloudCheckRows.Condition')}
        </Col>
        <Col span="6">{intl('saenext.components.msc.RcSpringCloudCheckRows.Value')}</Col>
      </Row>
      {get(props, 'value', []).map((item, index) => (
        <Row key={index} style={{ marginTop: 10 }}>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Select
              value={item?.type || 'cookie'}
              dataSource={ParamTypes}
              style={{ width: '100%' }}
              onChange={(v) => handleChange('type', v, index)}
            />
          </Col>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Input
              maxLength={64}
              showLimitHint
              value={item?.name}
              style={{ width: '100%' }}
              onChange={(v) => handleChange('name', v, index)}
              onPressEnter={(e) => {
                e.preventDefault();
              }}
            />
          </Col>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Select
              value={item?.cond}
              dataSource={Conditions}
              style={{ width: '100%' }}
              onChange={(v) => handleChange('cond', v, index)}
            />
          </Col>
          <Col span={6}>
            <Input
              style={{ width: 'calc(100% - 22px)' }}
              value={item?.value}
              onChange={(v) => handleChange('value', v, index)}
              onPressEnter={(e) => {
                e.preventDefault();
              }}
            />

            <If condition={!isPreview}>
              <Button
                text
                style={{ width: 20 }}
                disabled={props.value?.length > 1 ? false : true}
                onClick={() => {
                  props.onChange(props.value.filter((i, idx) => idx !== index));
                }}
              >
                <div>
                  <Icon
                    className="rows-delete"
                    size="small"
                    style={{ marginLeft: 5 }}
                    type="delete"
                  />
                </div>
              </Button>
            </If>
          </Col>
        </Row>
      ))}
      {get(props, 'value', []).length < 10 && !isPreview && (
        <div style={{ marginTop: 10 }}>
          <LinkButton
            onClick={() => {
              props.onChange([...props.value, { type: 'cookie', name: '', cond: '', value: '' }]);
            }}
          >
            <div>
              <Icon size="xs" type="plus" />
            </div>
            <span style={{ marginLeft: 5 }}>
              {intl('saenext.components.msc.RcSpringCloudCheckRows.AddCondition')}
            </span>
          </LinkButton>
        </div>
      )}
    </div>
  );
};
