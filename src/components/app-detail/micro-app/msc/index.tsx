import React from 'react';
import MscPage from './MscPage';

type Props = {
  pageName: 'appOverview' | 'interfaceDetails' | 'nodeDetails';
  applicationID: string;
  isStartMsc: boolean;
  NewSaeVersion: string;
  microServiceEnable: boolean;
  children: (mseApp) => React.ReactNode;
};


const MscPolymerize = (props: Props) => {
  const { pageName, isStartMsc, applicationID, NewSaeVersion, microServiceEnable, children } = props;

  return (
    <MscPage
      pageName={pageName}
      isStartMsc={isStartMsc}
      applicationId={applicationID}
      NewSaeVersion={NewSaeVersion}
      microServiceEnable={microServiceEnable}
      children={children}
    />
  );
};

export default MscPolymerize;
