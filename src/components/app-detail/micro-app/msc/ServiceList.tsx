import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import CndTable, { ISearch } from '@ali/cnd-table';
import services from '../../../../services';
import { get, isEmpty, map, head } from 'lodash';
import { Copy, Actions, Select } from '@ali/cnd';
import ServiceMeta from './ServiceMeta';
import { isForbidden } from '../../../../utils/authUtils';

type Props = {
  applicationID: string;
};
const Register = {
  sae: '0',
  user: '1',
  mse: '2',
};
const { LinkButton } = Actions;

const ServiceList = (props: Props) => {
  const { applicationID } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [enabeldMsc, setEnabeldMsc] = useState(false);
  const [registration, setRegistration] = useState(Register.sae);
  const [mseNacosInstanceId, setMseNacosInstanceId] = useState('');
  const [mseNacosInstances, setMseNacosInstances] = useState([]);
  const [mseNacosNamespaceId, setMseNacosNamespaceId] = useState('public');
  const [mseNacosNamespaces, setMseNacosNamespaces] = useState([]);
  const [authedMSE, setAuthMSE] = useState(true);

  useEffect(() => {
    getMicroAppStatus();
  }, []);

  useEffect(() => {
    if (enabeldMsc) return;
    if (registration !== Register.mse) return;
    if (!mseNacosInstanceId) return;
    if (!mseNacosNamespaceId) return;
    setRefreshIndex(Date.now());
  }, [enabeldMsc, registration, mseNacosInstanceId, mseNacosNamespaceId]);

  const getMicroAppStatus = async () => {
    // 根据应用详情 获取应用的 配置的注册中心类型、是否开启微服务治理
    // MicroRegistration  '0': sae 内置注册中心； '1': 自建注册中心； '2': mse nacos 注册中心
    // MseApplicationId   微服务治理应用id
    const res = await services.getMicroAppStatus({
      AppId: applicationID,
    });
    const _mseAppId = get(res, 'Data.MseApplicationId', '');
    const _enabeldMsc = !isEmpty(_mseAppId);
    const _registration = get(res, 'Data.MicroRegistration', Register.sae);
    setEnabeldMsc(_enabeldMsc);
    setRegistration(_registration);

    // 如果注册中心类型是 mse nacos，并且未开启微服务治理，需要增加 mse nacos实例的查询条件
    const isMseNacos = _registration === Register.mse && !_enabeldMsc;
    if (!isMseNacos) return;
    const nacosRes = await services.listMseNacosInstances({
      params: {
        PageNum: 1,
        PageSize: 99,
      },
      customErrorHandle(error, _p, cb) {
        const forbidden = isForbidden(error.code);
        setAuthMSE(!forbidden);
        if (!forbidden) cb?.();
      },
    });
    const _nacosInstances = map(get(nacosRes, 'Data', []), (item) => ({
      ...item,
      label: item?.ClusterAliasName,
      value: item?.InstanceId,
    }));
    const _instanceId = get(head(_nacosInstances), 'InstanceId', '');
    setMseNacosInstanceId(_instanceId);
    setMseNacosInstances(_nacosInstances);
    getMseNacosNamespaces(_instanceId);
  };

  const getMseNacosNamespaces = async (instanceId) => {
    if (!instanceId) return;
    const res = await services.listMseNacosNamespaces({
      PageNum: 1,
      PageSize: 99,
      InstanceId: instanceId,
    });
    const { Data = [] } = res || {};
    const _nacosNamespaces = map(Data, (item) => ({
      ...item,
      label: item?.NamespaceShowName,
      value: item?.Namespace || 'public',
    }));
    const _namespaceId = get(head(_nacosNamespaces), 'value', 'public');
    setMseNacosNamespaceId(_namespaceId);
    setMseNacosNamespaces(_nacosNamespaces);
  };

  const fetchData = async (params) => {
    const _params = {
      PageNumber: params.current,
      PageSize: params.pageSize,
      ServiceName: params.ServiceName || '',
      ServiceType: params.ServiceType || '',
    };
    if (registration === Register.mse && !enabeldMsc) {
      Reflect.set(_params, 'NacosInstanceId', mseNacosInstanceId);
      Reflect.set(_params, 'NacosNamespaceId', mseNacosNamespaceId);
    }
    const res = await services.getServices({
      // @ts-ignore
      params: {
        ..._params,
        AppId: applicationID,
      },
    });
    const _data = get(res, 'Data', []);
    return {
      data: _data,
      total: _data.length,
    };
  };

  const columns = React.useMemo(() => {
    // 当开启了微服务治理之后 需要提供查看元数据的功能
    let _columns = [
      {
        key: 'ServiceName',
        title: intl('saenext.components.msc.ServiceList.ServiceName'),
        dataIndex: 'ServiceName',
        width: '40%',
        cell: (value) => {
          return <Copy text={value}>{value}</Copy>;
        },
      },
      {
        key: 'ServiceType',
        title: intl('saenext.components.msc.ServiceList.ServiceType'),
        dataIndex: 'ServiceType',
        cell: (value) => <div>{value || '-'}</div>,
      },
      {
        key: 'ServiceVersion',
        title: intl('saenext.components.msc.ServiceList.VersionNumber'),
        dataIndex: 'ServiceVersion',
        cell: (value) => <div>{value || '-'}</div>,
      },
      {
        key: 'ServiceGroup',
        title: intl('saenext.components.msc.ServiceList.Group'),
        dataIndex: 'ServiceGroup',
        cell: (value) => <div>{value || '-'}</div>,
      },
    ];

    if (enabeldMsc) {
      _columns.push({
        key: 'operations',
        title: intl('saenext.components.msc.ServiceList.Operation'),
        // @ts-ignore
        width: 120,
        // @ts-ignore
        cell: (value, index, record) => (
          <Actions>
            <ServiceMeta record={record} applicationID={applicationID}>
              <LinkButton>{intl('saenext.components.msc.ServiceList.Metadata')}</LinkButton>
            </ServiceMeta>
          </Actions>
        ),
      });
    }
    return [..._columns];
  }, [enabeldMsc]);

  const search = {
    defaultDataIndex: 'ServiceName',
    defaultSelectedDataIndex: 'ServiceName',
    onlySupportOne: false,
    options: [
      {
        label: intl('saenext.components.msc.ServiceList.ServiceName'),
        dataIndex: 'ServiceName',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.components.msc.ServiceList.EnterAServiceName'),
        },
      },
      {
        label: intl('saenext.components.msc.ServiceList.ServiceType'),
        dataIndex: 'ServiceType',
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.components.msc.ServiceList.SelectAServiceType'),
          dataSource: [
            {
              label: 'dubbo',
              value: 'dubbo',
            },
            {
              label: 'springCloud',
              value: 'springCloud',
            },
          ],
        },
        defaultValue: '',
      },
    ],
  };

  const operation = React.useMemo(() => {
    // 如果注册中心类型是 mse nacos，并且未开启微服务治理，需要增加 mse nacos实例的查询条件
    if (registration === Register.mse && !enabeldMsc) {
      const _children = (
        <>
          <Select
            dataSource={mseNacosInstances}
            disabled={!authedMSE}
            placeholder={intl('saenext.components.msc.ServiceList.SelectMseNacosInstance')}
            style={{ width: 280 }}
            value={mseNacosInstanceId}
            label={
              <div
                style={{
                  height: 26,
                  lineHeight: '26px',
                  borderRadius: 2,
                  padding: '0 8px',
                  marginLeft: -14,
                  background: '#f7f9fa',
                }}
              >
                {intl('saenext.components.msc.ServiceList.NacosInstance')}
              </div>
            }
            onChange={(val: string) => {
              setMseNacosInstanceId(val);
              getMseNacosNamespaces(val);
            }}
          />

          <Select
            disabled={!authedMSE}
            dataSource={mseNacosNamespaces}
            placeholder={intl('saenext.components.msc.ServiceList.SelectMseNacosNamespace')}
            style={{ width: 280, marginLeft: 8 }}
            value={mseNacosNamespaceId}
            label={
              <div
                style={{
                  height: 26,
                  lineHeight: '26px',
                  borderRadius: 2,
                  padding: '0 8px',
                  marginLeft: -14,
                  background: '#f7f9fa',
                }}
              >
                {intl('saenext.components.msc.ServiceList.Namespace')}
              </div>
            }
            onChange={(val: string) => {
              setMseNacosNamespaceId(val);
            }}
          />
        </>
      );

      return _children;
    }
    return null;
  }, [
    enabeldMsc,
    registration,
    mseNacosInstances,
    mseNacosNamespaces,
    mseNacosInstanceId,
    mseNacosNamespaceId,
  ]);

  return (
    <>
      <CndTable
        fetchData={fetchData}
        primaryKey="primaryKey"
        // @ts-ignore
        columns={columns}
        showRefreshButton
        refreshIndex={refreshIndex}
        // @ts-ignore
        pagination={{
          pageSizeList: [10, 20, 50, 100],
          hideOnlyOnePage: true,
        }}
        operation={operation}
        search={search as ISearch}
      />
    </>
  );
};

export default ServiceList;
