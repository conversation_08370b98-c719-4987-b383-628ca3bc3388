import { intl } from '@ali/cnd';
import React, { FC, useState, useEffect } from 'react';
import { SlidePanel, Field, Form, Radio, Badge, Input, Balloon, Icon, Message } from '@ali/cnd';
import services from '../../../../services';
import { get, isEmpty, forEach, noop, map } from 'lodash';
import RcMicroGrayRules, { FrameworkTypes } from './RcMicroGrayRules';
import RcIngressGrayRules from './RcIngressGrayRules';
import { formatScRules } from './RcSpringCloudCheckRows';
import { formatDubboRules } from './RcDubboCheckRows';

type Props = {
  type?: 'create' | 'edit';
  appId?: string;
  appConfig?: Record<string, any>;
  regionId?: string;
  record: Record<string, any>;
  microRuleExist?: boolean;
  ingressRuleExist?: boolean;
  setRefreshIndex: (index: number) => void;
  albGray: boolean;
};
const FormItem = Form.Item;
const { Group: RadioGroup } = Radio;
const BadgeStyle = {
  backgroundColor: '#f54743',
  color: '#fff',
  borderRadius: '10px',
};
const SlideTitle = {
  create: intl('saenext.components.msc.CreateGrayRule.CreateAGrayscaleRule'),
  edit: intl('saenext.components.msc.CreateGrayRule.EditGrayscaleRules'),
};
const RuleTypes = {
  k8s: 'ingress',
  micro: 'microService',
};

const ImgType = {
  [RuleTypes.k8s]: 'https://img.alicdn.com/imgextra/i4/O1CN01FMG6vP1aArqb0rplr_!!6000000003290-2-tps-669-624.png',
  [RuleTypes.micro]: 'https://img.alicdn.com/imgextra/i4/O1CN018s9o7b1bt7cKCpOa4_!!6000000003522-2-tps-742-608.png',
}

const CreateGrayRule: FC<Props> = (props) => {
  const {
    type = 'create',
    appId,
    regionId,
    appConfig,
    record,
    microRuleExist = true,
    ingressRuleExist = true,
    setRefreshIndex = noop,
    children,
    albGray
  } = props;
  const [isShowing, setIsShowing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [ruleType, setRuleType] = useState(RuleTypes.micro);
  const field = Field.useField();
  const { init, validate, resetToDefault, setValue, setValues } = field;

  useEffect(() => {
    if (!isShowing) return;
    const _ruleType = microRuleExist ? RuleTypes.k8s : RuleTypes.micro;
    setRuleType(_ruleType);
    setValue('RuleType', _ruleType);
    setRulesValues(record);
  }, [record, isShowing, microRuleExist]);

  const setRulesValues = (record) => {
    if (type !== 'edit') return;
    const albRules = get(record, 'AlbRules', []);
    let ruleType = isEmpty(albRules) ? RuleTypes.micro : RuleTypes.k8s;
    let scRules = joinMicroRules(record);
    const values = {
      RuleType: ruleType,
      Name: get(record, 'Name', ''),
      Description: get(record, 'Description', ''),
      ScRules: scRules,
      AlbRules: get(record, 'AlbRules', []),
    };
    setRuleType(ruleType);
    setValues(values);
  };

  const handleSubmit = async () => {
    const doSubmit = async (params) => {
      let response = {};
      if (type === 'create') {
        response = await services.createGreyTagRoute({
          params,
          customErrorHandle(err, data, callback) {
            setIsProcessing(false);
            callback && callback();
            return [];
          },
        });
      } else {
        response = await services.updateGreyTagRoute({
          params: {
            ...params,
            GreyTagRouteId: get(record, 'GreyTagRouteId', ''),
          },
          customErrorHandle(err, data, callback) {
            setIsProcessing(false);
            callback && callback();
            return [];
          },
        });
      }
      if (!isEmpty(response) && get(response, 'Data.GreyTagRouteId', false)) {
        Message.success(
          type === 'create'
            ? intl('saenext.components.msc.CreateGrayRule.TheGrayscaleRuleIsCreated')
            : intl('saenext.components.msc.CreateGrayRule.TheGrayscaleRuleHasBeen'),
        );
        setRefreshIndex && setRefreshIndex(Date.now());
        setIsShowing(false);
      }
      setIsProcessing(false);
    };
    validate((error, values) => {
      if (error) return;
      setIsProcessing(true);
      // @ts-ignore
      const { RuleType } = values;
      const [scRules = [], dubboRules = []] = splitMicroRules(values);
      const params = {
        AppId: appId,
        RegionId: regionId,
        ...values,
        ScRules: JSON.stringify(RuleType === RuleTypes.micro ? formatScRules(scRules) : []),
        DubboRules: JSON.stringify(
          RuleType === RuleTypes.micro ? formatDubboRules(dubboRules) : [],
        ),
        AlbRules: JSON.stringify(RuleType === RuleTypes.k8s ? get(values, 'AlbRules', []) : []),
      };
      doSubmit(params);
    });
  };

  const splitMicroRules = (values) => {
    const scRules = [];
    const dubboRules = [];
    const microRules = get(values, 'ScRules', []);
    forEach(microRules, (rule) => {
      const type = get(rule, 'type', FrameworkTypes.sc);
      if (type === FrameworkTypes.sc) {
        scRules.push(rule);
      } else {
        dubboRules.push(rule);
      }
    });
    return [scRules, dubboRules];
  };

  const joinMicroRules = (record) => {
    const rules = [];
    const scRules = get(record, 'ScRules', []);
    const dubboRules = get(record, 'DubboRules', []);
    forEach(scRules, (rule) => {
      const { items = [] } = rule;
      rules.push({
        ...rule,
        key: Math.random(),
        type: FrameworkTypes.sc,
        scItems: map(items, (item) => ({ ...item })),
      });
    });
    forEach(dubboRules, (rule) => {
      const { items = [] } = rule;
      rules.push({
        ...rule,
        key: Math.random(),
        type: FrameworkTypes.dubbo,
        dubboItems: map(items, (item) => ({ ...item })),
      });
    });
    return rules;
  };

  const handleCancel = () => {
    resetToDefault();
    setIsShowing(false);
  };

  return (
    <>
      <span onClick={() => setIsShowing(prev => !prev)}>{children}</span>
      <SlidePanel
        title={SlideTitle[type]}
        width={880}
        isShowing={isShowing}
        isProcessing={isProcessing}
        onCancel={handleCancel}
        onOk={handleSubmit}
        onClose={handleCancel}
      >
        <Form field={field}>
          <FormItem required label={intl('saenext.components.msc.CreateGrayRule.RuleType')}>
            <RadioGroup
              {...init('RuleType', {
                initValue: record.ruleType || ruleType,
                rules: [
                  {
                    required: true,
                    message: intl('saenext.components.msc.CreateGrayRule.SelectARuleType'),
                  },
                ],

                props: {
                  onChange: (val: string) => setRuleType(val),
                },
              })}
              disabled={type === 'edit'}
            >
              {albGray && (
                <Radio id="ingress" value={RuleTypes.k8s} disabled={ingressRuleExist}>
                  <Badge content="Beta" style={BadgeStyle}>
                    <span style={{ display: 'inline-block', width: 170 }}>
                      {intl('saenext.components.msc.CreateGrayRule.SevenLayerTrafficGrayK')}
                    </span>
                  </Badge>
                </Radio>
              )}
              <Radio
                id="microService"
                value={RuleTypes.micro}
                style={{ marginLeft: 16 }}
                disabled={microRuleExist}
              >
                <Badge
                  content={intl('saenext.components.msc.CreateGrayRule.PublicBeta')}
                  style={BadgeStyle}
                >
                  <span style={{ display: 'inline-block', width: 106 }}>
                    {intl('saenext.components.msc.CreateGrayRule.MicroserviceTrafficGray')}
                  </span>
                </Badge>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem
            required
            label={intl('saenext.components.msc.CreateGrayRule.RuleName')}
            help={intl('saenext.components.msc.CreateGrayRule.ItCanStartWithA')}
          >
            <Input
              {...init('Name', {
                initValue: record.Name,
                rules: [
                  {
                    required: true,
                    message: intl('saenext.components.msc.CreateGrayRule.EnterARuleName'),
                  },
                  {
                    message: intl('saenext.components.msc.CreateGrayRule.ItCanStartWithA'),
                    pattern: /^[a-z][\u4e00-\u9fa5-a-z0-9]{0,62}[a-z0-9]{1,1}$/,
                  },
                ],
              })}
              minLength={1}
              maxLength={64}
              showLimitHint
              disabled={type === 'edit'}
              placeholder={intl('saenext.components.msc.CreateGrayRule.EnterARuleName')}
            />
          </FormItem>
          <FormItem label={intl('saenext.components.msc.CreateGrayRule.RuleDescription')}>
            <Input.TextArea
              {...init('Description', {
                initValue: record.Description,
              })}
              rows={1}
              maxLength={64}
              showLimitHint
            />
          </FormItem>
          <FormItem
            required
            label={
              <>
                <span style={{ marginRight: 32 }}>
                  {intl('saenext.components.msc.CreateGrayRule.GrayscaleType')}
                </span>
                <>
                  <span style={{ marginRight: 8 }}>
                    {intl('saenext.components.msc.CreateGrayRule.GrayscaleByContent')}
                  </span>
                  <Balloon
                    cache={true}
                    align="r"
                    trigger={<Icon size="xs" type="help" />}
                    closable={false}
                  >
                     <div style={{ width: 300 }}>
                      <img src={ImgType[ruleType]} style={{ width: '100%' }} />
                     </div>
                  </Balloon>
                </>
              </>
            }
          />

          {ruleType === RuleTypes.k8s ? (
            <FormItem>
              {/* @ts-ignore */}
              <RcIngressGrayRules
                {...init('AlbRules', {
                  initValue: get(record, 'AlbRules', []),
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.msc.CreateGrayRule.PleaseConfigureGrayscaleRules',
                      ),
                    },
                  ],
                })}
                appConfig={appConfig}
              />
            </FormItem>
          ) : (
            <FormItem>
              {/* @ts-ignore */}
              <RcMicroGrayRules
                {...init('ScRules', {
                  initValue: [],
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.msc.CreateGrayRule.PleaseConfigureGrayscaleRules',
                      ),
                    },
                  ],
                })}
                appConfig={appConfig}
              />
            </FormItem>
          )}
        </Form>
      </SlidePanel>
    </>
  );
};

export default CreateGrayRule;
