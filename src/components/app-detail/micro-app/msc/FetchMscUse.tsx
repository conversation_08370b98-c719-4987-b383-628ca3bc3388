import React, { useState, useEffect, useRef } from 'react';
import { Message, Button, Icon, Form, Input, Dialog, ToolTipCondition, Select } from '@ali/cnd';
import _ from 'lodash';
import { intl } from '@ali/cnd';
import services from '../../../../services';
import TextWithBalloon from '../../../shared/TextWithBalloon';
import TextRefreshButton from '../../../shared/TextRefreshButton';
import CachedData from '../../../../cache/common';

type Props = {
  AppId: string;
  rebooting: boolean;
  isStartMsc: boolean;
  // pageName: 'throttle' | 'grayscale' | 'lossless',
  pageName:
    | 'appOverview'
    | 'interfaceDetails'
    | 'nodeDetails'
    | 'seamlessDeploy'
    | 'grayScale'
    | 'systemProtect'
    | 'trafficProtect'
    | 'mqTag'
    | 'az';
  mscVersion: 'basic' | 'profess' | 'business' | 'disable' | 'test' | 'testExpire';
  FreeVersion: number;
  mseAppNameSpace: string;
  setRefreshIndex: (index: number) => void;
  refreshContent: (mseApp: {
    MseAppId: string;
    MseAppName: string;
    MseAppNameSpace: string;
  }) => void;
};

type InitState = {
  isFinal?: boolean;
  isFailed?: boolean;
  isSuccess?: boolean;
};

const mseVersion = new Map()
  .set('basic', intl('saenext.components.msc.FetchMseUse.BasicEdition'))
  .set('profess', intl('saenext.components.msc.FetchMseUse.ProfessionalEdition'))
  .set('business', intl('saenext.components.msc.FetchMseUse.EnterpriseEdition'))
  .set('disable', intl('saenext.components.msc.FetchMseUse.NotActivated'))
  .set('test', intl('saenext.components.msc.FetchMseUse.TrialVersion'))
  .set('testExpire', intl('saenext.components.msc.FetchMseUse.TrialVersion'));

const enableContent = {
  appOverview: {
    text: intl('saenext.components.msc.FetchMseUse.MicroserviceGovernance'),
    version: 'profess',
  },
  interfaceDetails: {
    text: intl('saenext.components.msc.FetchMseUse.MicroserviceGovernance'),
    version: 'profess',
  },
  nodeDetails: {
    text: intl('saenext.components.msc.FetchMseUse.MicroserviceGovernance'),
    version: 'profess',
  },
  seamlessDeploy: {
    text: intl('saenext.components.msc.FetchMseUse.MicroserviceGovernance'),
    version: 'profess',
  },
  grayScale: {
    text: intl('saenext.components.msc.FetchMseUse.MicroserviceGovernance'),
    version: 'profess',
  },
  systemProtect: {
    text: intl('saenext.components.msc.FetchMseUse.SystemProtection'),
    version: 'business',
  },
  trafficProtect: {
    text: intl('saenext.components.msc.FetchMseUse.TrafficProtection'),
    version: 'business',
  },
  mqTag: {
    text: intl('saenext.components.msc.FetchMscUse.MessageGray'),
    version: 'profess',
  },
  az: {
    text: intl('saenext.components.msc.FetchMscUse.MessageGray'),
    version: 'profess',
  },
};

const FetchMscUse = (props: Props) => {
  // mscVersion 当前版本
  const {
    AppId,
    rebooting,
    isStartMsc,
    pageName,
    mscVersion,
    FreeVersion,
    mseAppNameSpace,
    refreshContent,
    setRefreshIndex,
  } = props;
  const { text, version } = enableContent[pageName];
  const timerRef = useRef(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRefresh, setIsRefresh] = useState<boolean>(false);
  const [updateVersion, setUpdateVersion] = useState(mscVersion);
  const [internalState, setInternalState] = useState<InitState>({
    isFinal: false,
    isFailed: false,
    isSuccess: false,
  });

  // 是否已经开启
  const [mseApp, setMseApp] = useState({ MseAppId: '', MseAppName: '', MseAppNameSpace: '' });
  const [errorMessage, setErrorMessage] = useState('');
  const [targetVersion, setTargetVersion] = useState('business');
  const isIntl = CachedData.isSinSite();

  useEffect(() => {
    setUpdateVersion(mscVersion);

    // 当rebooting为true时 应用正在重启跳过开启过程
    loopMscStatus();

    return () => {
      setClearInterval();
    };
  }, [mscVersion, rebooting]);

  const loopMscStatus = () => {
    if (!rebooting) return;
    setIsLoading(true);
    setClearInterval();
    timerRef.current = setInterval(async () => {
      // 轮训 DescribeApplicationMseService
      const res = await services.getAppMseStatus({
        params: {
          AppId: AppId,
          // enableAhas 限流降级是true. 无损上下线 灰度规则是false
          // EnableAhas: pageName === 'throttle',
          EnableAhas: ['systemProtect', 'trafficProtect'].includes(pageName),
        },
        customErrorHandle: (err, data, callback) => {
          openMscFailed(err.message);
          setClearInterval();
          callback && callback();
        },
      });
      const { Data = {} } = res || {};
      const { Status, MseAppId, MseAppName, MseAppNameSpace } = Data;
      if (Status === 'SUCCESS') {
        setClearInterval();
        setMseApp({
          MseAppId,
          MseAppName,
          MseAppNameSpace,
        });
        setInternalState({
          isFinal: true,
          isFailed: false,
          isSuccess: true,
        });
        setIsLoading(false);
      }
    }, 1000 * 5);
  };

  const handleEnableMsc = async () => {
    const res = await services.CheckApplicationInstanceVersion(
      {
        AppId: AppId,
      },
      false,
    );
    if (res && !res?.Data?.CheckResult) {
      Dialog.alert({
        title: intl('saenext.components.msc.FetchMseUse.EnableMicroserviceGovernance'),
        content: intl('saenext.application-instance.version.checkResult.false'),
        style: {
          width: 480,
        },
        okProps: {
          children: intl('saenext.commom.close'),
        },
      });
    } else if(res && res?.Data?.CheckResult){
      const dialog = Dialog.alert({
        title: intl('saenext.components.msc.FetchMseUse.EnableMicroserviceGovernance'),
        content: (
          <p style={{ width: 420 }}>
            {intl('saenext.components.msc.FetchMseUse.AreYouSureYouWant')}
          </p>
        ),

        onOk: () => {
          enableMsc();
          dialog.hide();
        },
        okProps: { children: intl('saenext.common.dialog.ok') },
      });
    }
  };

  const enableMsc = async () => {
    setIsLoading(true);
    try {
      // CreateApplicationMseService 接口
      const data = await services.enableAppMseStatus({
        params: {
          AppId,
          EnableAhas: updateVersion === 'business' && targetVersion === 'business' ? true : false,
          FreeVersion: FreeVersion,
        },
        customErrorHandle: (error, data, callback) => {
          openMscFailed(error.message);
          callback && callback();
        },
      });
      const { Data = {} } = data || {};
      const { MseAppId, MseAppName, MseAppNameSpace } = Data;

      // 需要 调用 DescribeApplicationMseService 通过status判断
      if (MseAppId) {
        setClearInterval();
        timerRef.current = setInterval(async () => {
          // 轮训 DescribeApplicationMseService
          const res = await services.getAppMseStatus({
            params: {
              AppId: AppId,
              // enableAhas 限流降级是true. 无损上下线 灰度规则是false
              // EnableAhas: pageName === 'throttle',
              EnableAhas: ['systemProtect', 'trafficProtect'].includes(pageName),
            },
            customErrorHandle: (err, data, callback) => {
              openMscFailed(err.message);
              callback && callback();
            },
          });
          const { Data = {} } = res || {};
          const { Status } = Data;
          if (Status === 'SUCCESS') {
            setClearInterval();
            setMseApp({
              MseAppId,
              MseAppName,
              MseAppNameSpace,
            });
            setInternalState({
              isFinal: true,
              isFailed: false,
              isSuccess: true,
            });
            setIsLoading(false);
          }
        }, 1000 * 5);
      }
    } catch (error) {
      openMscFailed(error.message);
    }
  };

  const openMscFailed = (message) => {
    setInternalState({
      isFinal: true,
      isFailed: true,
      isSuccess: false,
    });
    setIsLoading(false);
    setErrorMessage(message);
  };

  const setClearInterval = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const onEnablemsc = () => {
    const uri = `${CachedData.confLink('feature:common-buy')}/?commodityCode=${isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'}`;
    window.open(uri, '_blank');
  };

  const onUpdatemsc = () => {
    const userId =
      _.get(window, `ALIYUN_CONSOLE_CONFIG.CURRENT_PK`) ||
      _.get(window, `ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK`);
    const uri = `${CachedData.confLink('feature:common-buy')}/?commodityCode=${isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'}&orderType=UPGRADE&instanceId=synthetic_post_${userId}`;
    window.open(uri, '_blank');
  };

  const renderContent = () => {
    const { isFinal, isSuccess, isFailed } = internalState;
    if (isFinal && isSuccess) {
      return (
        <>
          <Icon type="success">
            <span style={{ color: '#333', fontSize: 14, fontWeight: 500, marginLeft: 8 }}>
              {intl('saenext.components.msc.FetchMseUse.TextEnabled', { text: text })}
            </span>
          </Icon>
          <div style={{ display: 'flex', alignItems: 'center', marginTop: 8, marginLeft: 28 }}>
            {text}
            {intl('saenext.components.msc.FetchMseUse.TheApplicationHasBeenUpdated')}
            <Button
              text
              type="primary"
              loading={isRefresh}
              style={{ marginLeft: 4, marginRight: 4 }}
              onClick={() => {
                // 打开iframe
                setIsRefresh(true);
                refreshContent && refreshContent(mseApp);
              }}
            >
              {intl('saenext.components.msc.FetchMseUse.Refresh')}
            </Button>
            {intl('saenext.components.msc.FetchMseUse.ViewTheLatestData')}
          </div>
        </>
      );
    }

    if (isFinal && isFailed) {
      return (
        <>
          <Icon type="error">
            <span style={{ color: '#333', fontSize: 14, fontWeight: 500, marginLeft: 8 }}>
              {intl('saenext.components.msc.FetchMseUse.TextFailedToOpen', { text: text })}
            </span>
          </Icon>
          <div style={{ display: 'flex', alignItems: 'center', marginTop: 8, marginLeft: 28 }}>
            Error Message:{errorMessage || '-'}
            <div style={{ display: 'flex', alignItems: 'center', marginLeft: 16 }}>
              <span>{intl('saenext.components.msc.FetchMseUse.Click')}</span>
              <Button
                text
                type="primary"
                style={{ marginLeft: 4, marginRight: 4 }}
                onClick={() => {
                  setInternalState({
                    isFinal: false,
                    isFailed: false,
                    isSuccess: false,
                  });
                  setIsLoading(false);
                }}
              >
                {intl('saenext.components.msc.FetchMseUse.Return')}
              </Button>
              <span>{intl('saenext.components.msc.FetchMseUse.Restart')}</span>
            </div>
          </div>
        </>
      );
    }

    const renderDisabledTip = () => {
      if (!(mscVersion === 'business' || mscVersion === 'test' || mscVersion === version)) {
        return intl('saenext.components.msc.FetchMscUse.TheCurrentMseMicroserviceGovernance');
      }
      if (!isStartMsc) {
        return intl('saenext.components.msc.FetchMscUse.TheCurrentApplicationIsNot');
      }
    };

    return (
      <>
        <Form labelAlign="top">
          <Form.Item
            required
            label={
              <TextWithBalloon
                align="r"
                text={intl('saenext.components.msc.FetchMseUse.MseMicroserviceGovernanceVersion')}
                tips={
                  <span>
                    {intl('saenext.components.msc.FetchMseUse.MseMicroserviceGovernanceIsDivided')}
                    <a
                      href={CachedData.confLink('help:mse:microservice-governance-edition-selection')}
                      target="_blank"
                    >
                      {intl('saenext.components.msc.FetchMseUse.VersionSelection')}
                    </a>
                    {intl('saenext.components.msc.FetchMseUse.And')}
                    <a
                      href={CachedData.confLink('help:mse:billing-overview')}
                      target="_blank"
                    >
                      {intl('saenext.components.msc.FetchMseUse.BillingOverview')}
                    </a>{' '}
                    {intl('saenext.common.full-stop')}
                  </span>
                }
              />
            }
          >
            {updateVersion === 'business' ? (
              <Select
                value={targetVersion}
                dataSource={[
                  {
                    label: intl('saenext.components.msc.FetchMseUse.EnterpriseEdition'),
                    value: 'business',
                  },
                  {
                    label: intl('saenext.components.msc.FetchMseUse.ProfessionalEdition'),
                    value: 'profess',
                  },
                ]}
                style={{ width: 400, marginRight: 10 }}
                onChange={(val: string) => {
                  setTargetVersion(val);
                }}
              />
            ) : (
              <Input
                readOnly
                value={updateVersion === 'disable' ? '' : mseVersion.get(updateVersion)}
                style={{ width: 400, marginRight: 10 }}
              />
            )}

            <TextRefreshButton onClick={() => setRefreshIndex && setRefreshIndex(Date.now())} />
            {updateVersion === 'business' &&
              targetVersion === 'profess' &&
              mseAppNameSpace === 'sae-pro' && (
                <div style={{ marginTop: 8 }}>
                  {intl(
                    'saenext.components.msc.FetchMscUse.EnterpriseEditionApplicationsAndProfessional',
                  )}
                </div>
              )}
            {mscVersion === 'disable' ? (
              <div style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
                {intl('saenext.components.msc.FetchMseUse.CurrentlyMseMicroserviceGovernanceIs')}
                <Button
                  text
                  type="primary"
                  style={{ marginLeft: 4, marginRight: 4 }}
                  onClick={onEnablemsc}
                >
                  {intl('saenext.components.msc.FetchMseUse.Activate')}
                </Button>
                {intl('saenext.components.msc.FetchMseUse.GoToTheMsePurchase')}
                <span style={{ color: '#ff6a00' }}>
                  {['systemProtect', 'trafficProtect'].includes(pageName)
                    ? intl('saenext.components.msc.FetchMseUse.EnterpriseEdition')
                    : intl('saenext.components.msc.FetchMseUse.ProfessionalEdition')}
                </span>
                {intl('saenext.components.msc.FetchMseUse.Or')}
                <span style={{ color: '#ff6a00' }}>
                  {intl('saenext.components.msc.FetchMseUse.FreeTrialVersion')}
                </span>
                {intl('saenext.common.full-stop')}
              </div>
            ) : mscVersion === 'testExpire' ? (
              <div style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
                {intl('saenext.components.msc.FetchMseUse.TheCurrentMseFreeTrial')}

                <Button
                  text
                  type="primary"
                  style={{ marginLeft: 4, marginRight: 4 }}
                  onClick={onEnablemsc}
                >
                  {intl('saenext.components.msc.FetchMseUse.Activate')}
                </Button>
                {intl('saenext.components.msc.FetchMseUse.GoToTheMsePurchase')}

                <span style={{ color: '#ff6a00' }}>
                  {['systemProtect', 'trafficProtect'].includes(pageName)
                    ? intl('saenext.components.msc.FetchMseUse.EnterpriseEdition')
                    : intl('saenext.components.msc.FetchMseUse.ProfessionalEdition')}
                </span>
                {intl('saenext.components.msc.FetchMseUse.ContinueToUse')}
              </div>
            ) : mscVersion !== version && mscVersion !== 'business' && mscVersion !== 'test' ? (
              <div style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
                {intl('saenext.components.msc.FetchMseUse.MseMicroserviceGovernance')}
                {mseVersion.get(mscVersion)}
                {intl('saenext.components.msc.FetchMseUse.Click.1')}
                <Button
                  text
                  type="primary"
                  style={{ marginLeft: 4, marginRight: 4 }}
                  onClick={onUpdatemsc}
                >
                  {intl('saenext.components.msc.FetchMseUse.Upgrade')}
                </Button>
                {intl('saenext.components.msc.FetchMseUse.YouCanGoToThe')}
              </div>
            ) : null}
          </Form.Item>
        </Form>
        <div className="flex">
          <ToolTipCondition
            show={
              !(mscVersion === 'business' || mscVersion === 'test' || mscVersion === version) ||
              !isStartMsc
            }
            tip={renderDisabledTip()}
            align="r"
          >
            <Button
              loading={isLoading}
              // onClick={enableMsc}
              onClick={handleEnableMsc}
              type={isLoading ? 'normal' : 'primary'}
              disabled={
                !(mscVersion === 'business' || mscVersion === 'test' || mscVersion === version) ||
                !isStartMsc
              }
            >
              {isLoading
                ? mscVersion === 'disable'
                  ? intl('saenext.components.msc.FetchMseUse.MicroserviceGovernanceEnabled')
                  : intl('saenext.components.msc.FetchMseUse.TextIsOn', { text: text })
                : mscVersion === 'disable'
                ? intl('saenext.components.msc.FetchMseUse.EnableMicroserviceGovernance')
                : intl('saenext.components.msc.FetchMseUse.EnableText', { text: text })}
            </Button>
          </ToolTipCondition>
          {isLoading ? (
            <span style={{ marginLeft: 16, color: '#999' }}>
              {intl('saenext.components.msc.FetchMseUse.ItMayTakeToMinutes')}
            </span>
          ) : null}
        </div>
      </>
    );
  };

  return (
    <>
      <Message type="notice">
        {mscVersion === 'disable' ? (
          <>
            <div className="text-line">
              {intl('saenext.components.msc.FetchMseUse.ServerlessByDefaultTheApplication')}
            </div>
            <div className="text-line">
              {intl('saenext.components.msc.FetchMseUse.CurrentlyMseMicroserviceGovernanceIs.2')}
              <span style={{ color: '#ff6a00' }}>
                {intl('saenext.components.msc.FetchMseUse.DuringTheStartupProcessThe')}
              </span>
            </div>
          </>
        ) : mscVersion == 'profess' && version === 'business' ? (
          <div className="text-line">
            <span>
              {intl('saenext.components.msc.FetchMseUse.SystemProtectionAndTrafficProtection')}
            </span>
            <span style={{ color: '#ff6a00' }}>
              {intl('saenext.components.msc.FetchMseUse.DuringTheStartupProcessThe')}
            </span>
          </div>
        ) : mscVersion == 'business' && version === 'business' ? (
          <div className="text-line">
            <span>
              {intl('saenext.components.msc.FetchMseUse.SystemProtectionAndTrafficProtection.1')}
            </span>
            <span style={{ color: '#ff6a00' }}>
              {intl('saenext.components.msc.FetchMseUse.DuringTheStartupProcessThe')}
            </span>
          </div>
        ) : (
          <>
            <div className="text-line">
              {intl('saenext.components.msc.FetchMseUse.ServerlessByDefaultTheApplication')}
            </div>
            <div className="text-line">
              {intl('saenext.components.msc.FetchMseUse.CurrentlyMseMicroserviceGovernanceIs.2')}
              <span style={{ color: '#ff6a00' }}>
                {intl('saenext.components.msc.FetchMseUse.DuringTheStartupProcessThe')}
              </span>
            </div>
          </>
        )}
      </Message>
      {mscVersion == 'profess' && version === 'business' ? (
        <Message type="warning" className="mt-s">
          {intl('saenext.components.msc.FetchMseUse.AfterUpgradingTheEnterpriseEdition')}
        </Message>
      ) : null}

      <div style={{ height: 370, position: 'relative' }}>
        <img
          style={{
            width: '100%',
            height: '100%',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'top',
            backgroundSize: 'cover',
            objectFit: 'cover',
          }}
          src="https://img.alicdn.com/imgextra/i4/O1CN01gRoOLZ22qpIm2endI_!!6000000007172-1-tps-3840-740.gif"
        />

        <div
          style={{ position: 'absolute', top: 32, left: 0, background: 'transparent', zIndex: 1 }}
        >
          {renderContent()}
        </div>
      </div>
    </>
  );
};

export default FetchMscUse;
