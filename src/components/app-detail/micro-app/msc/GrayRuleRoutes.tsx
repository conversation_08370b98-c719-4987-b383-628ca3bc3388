import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import CndTable from '@ali/cnd-table';
import { Message, Button, Actions, Copy, Dialog } from '@ali/cnd';
import services from '../../../../services';
import { get, isEmpty, findIndex } from 'lodash';
import moment from 'moment';
import CreateGrayRule from './CreateGrayRule';
import { AES_CONSTANT, trackMicroAppMsc } from '~/tracker';

type Props = {
  appId: string;
  appConfig: Record<string, any>;
  regionId: string;
  albGray: boolean;
};
const { LinkButton } = Actions;

const GrayRuleRoutes = (props: Props) => {
  const { appId, regionId, appConfig, albGray } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [ingressRuleExist, setIngressRuleExist] = useState(false);
  const [microRuleExist, setMicroRuleExist] = useState(false);

  const fetchData = async () => {
    const res = await services.getGrayTagRoutes(
      {
        AppId: appId,
        RegionId: regionId,
      },
      (err, data, callback) => {
        callback && callback(err, data);
        return { data: [], total: 0 };
      },
    );
    const _data = get(res, 'Data.Result', []) as any[];
    const _ingressRuleExist = findIndex(_data, (item) => item.AlbRules?.length > 0) > -1;
    const _microRuleExist =
      findIndex(_data, (item) => item.ScRules?.length > 0 || item.DubboRules?.length > 0) > -1;
    setMicroRuleExist(_microRuleExist);
    setIngressRuleExist(_ingressRuleExist);
    return {
      data: _data,
      total: get(_data, 'Data.TotalSize', 1),
    };
  };

  const handleDeleteRule = async (record) => {
    handleDeleteTrack(AES_CONSTANT.AES_STAGE_TYPE.TRIGGER);
    Dialog.alert({
      title: intl('saenext.components.msc.GrayRuleRoutes.DeleteAGrayscaleRule'),
      content: (
        <p>
          <span>{intl('saenext.components.msc.GrayRuleRoutes.AreYouSureYouWant')}</span>
          <span style={{ color: '#F90' }}>{record?.Name}</span> ?
        </p>
      ),

      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res = await services.deleteGrayTagRoute({
            GreyTagRouteId: record.GreyTagRouteId,
          });
          const data = get(res, 'Data');
          if (isEmpty(data.GreyTagRouteId)) {
            resolve(true);
            Message.success(intl('saenext.components.msc.GrayRuleRoutes.DeletedSuccessfully'));
            setRefreshIndex(Date.now());
            handleDeleteTrack(AES_CONSTANT.AES_STAGE_TYPE.SUCCESS);
          } else {
            reject();
            Message.error(intl('saenext.components.msc.GrayRuleRoutes.FailedToDelete'));
            handleDeleteTrack(AES_CONSTANT.AES_STAGE_TYPE.FAIL);
          }
        });
      },
      onCancel:()=>{
        handleDeleteTrack(AES_CONSTANT.AES_STAGE_TYPE.EXIT);
      },
      onClose:()=>{
        handleDeleteTrack(AES_CONSTANT.AES_STAGE_TYPE.EXIT);
      }
    });
  };

  const handleDeleteTrack = stage => {
    trackMicroAppMsc({
      behavior: 'DELETE',
      stage: stage,
      appId: appId,
      section: 'msc-traffic',
      trackType: 'GRAYSCALE_RULE',
    });
  };

  const columns = React.useMemo(() => {
    let _columns = [
      {
        key: 'Name',
        title: intl('saenext.components.msc.GrayRuleRoutes.RuleName'),
        dataIndex: 'Name',
        width: '30%',
        cell: (value) => {
          return (
            <Copy text={value}>
              <LinkButton>{value}</LinkButton>
            </Copy>
          );
        },
      },
      {
        key: 'Description',
        title: intl('saenext.components.msc.GrayRuleRoutes.RuleDescription'),
        dataIndex: 'Description',
        width: '30%',
        cell: (value) => <div>{value || '-'}</div>,
      },
      {
        key: 'UpdateTime',
        title: intl('saenext.components.msc.GrayRuleRoutes.LastModifiedTime'),
        dataIndex: 'UpdateTime',
        width: '30%',
        cell: (value) => {
          if (value) {
            return moment(value).format('YYYY-MM-DD HH:mm:ss');
          }
          return '-';
        },
      },
      {
        key: 'operations',
        title: intl('saenext.components.msc.GrayRuleRoutes.Operation'),
        dataIndex: 'operations',
        width: 120,
        cell: (value, index, record) => (
          <Actions>
            <CreateGrayRule
              type="edit"
              appId={appId}
              appConfig={appConfig}
              record={record}
              regionId={regionId}
              setRefreshIndex={setRefreshIndex}
              albGray={albGray}
            >
              <LinkButton>{intl('saenext.components.msc.GrayRuleRoutes.Edit')}</LinkButton>
            </CreateGrayRule>
            <LinkButton onClick={() => handleDeleteRule(record)}>
              {intl('saenext.components.msc.GrayRuleRoutes.Delete')}
            </LinkButton>
          </Actions>
        ),
      },
    ];

    return [..._columns];
  }, [appConfig,albGray]);

  const operation = React.useMemo(() => {
    return (
      <CreateGrayRule
        type="create"
        appId={appId}
        appConfig={appConfig}
        record={{}}
        regionId={regionId}
        microRuleExist={microRuleExist}
        ingressRuleExist={ingressRuleExist}
        setRefreshIndex={setRefreshIndex}
        albGray={albGray}
      >
        <Button type="primary">
          {intl('saenext.components.msc.GrayRuleRoutes.CreateAGrayscaleRule')}
        </Button>
      </CreateGrayRule>
    );
  }, [appConfig, microRuleExist, ingressRuleExist,albGray]);

  return (
    <>
      <Message type="notice" style={{ marginBottom: 8 }}>
        {intl('saenext.components.msc.GrayRuleRoutes.IfYourApplicationIsA')}
      </Message>
      <CndTable
        fetchData={fetchData}
        primaryKey="GreyTagRouteId"
        // @ts-ignore
        columns={columns}
        showRefreshButton
        refreshIndex={refreshIndex}
        // @ts-ignore
        pagination={{
          pageSizeList: [10, 20, 50, 100],
          hideOnlyOnePage: true,
        }}
        operation={operation}
      />
    </>
  );
};

export default GrayRuleRoutes;
