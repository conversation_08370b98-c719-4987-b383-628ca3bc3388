import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import { Button, LinkButton, Grid, Input, Select, Icon } from '@ali/cnd';
import { get, map, split, isEmpty } from 'lodash';
import If from '../../../shared/If';

const { Row, Col } = Grid;
const ParamTypes = [
  { label: '<PERSON>ie', value: 'cookie' },
  { label: 'Header', value: 'header' },
  { label: intl('saenext.components.msc.RcIngressCheckRows.SourceIp'), value: 'sourceIp' },
];

export default (props) => {
  const { albEdition = '', value = [], onChange, isPreview } = props;

  useEffect(() => {
    if (isEmpty(value)) {
      onChange([{ type: 'header', name: '', cond: '==', value: '' }]);
    }
  }, []);

  const paramTypes = React.useMemo(() => {
    const _paramTypes = [];
    for (let i = 0; i < ParamTypes.length; i++) {
      const type = ParamTypes[i];
      if (i === 1) {
        _paramTypes.push({ ...type, disabled: false });
      } else {
        _paramTypes.push({ ...type, disabled: albEdition === 'Basic' });
      }
    }
    return _paramTypes;
  }, [albEdition]);

  const handleChange = (key, value, index) => {
    let _value = get(props, 'value', []);
    _value = _value.map((item, idx) => {
      if (idx === index) {
        return { ...item, type: item.type || 'header', [key]: value, operator: 'rawvalue' };
      }
      return { ...item, type: item.type || 'header', operator: 'rawvalue' };
    });
    onChange && onChange(_value);
  };

  return (
    <div style={{ background: '#f6f6f6', padding: 16 }}>
      <Row style={{ color: '#555' }}>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.msc.RcIngressCheckRows.ParameterType')}
        </Col>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.msc.RcIngressCheckRows.Parameter')}
        </Col>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.msc.RcIngressCheckRows.Condition')}
        </Col>
        <Col span="6">{intl('saenext.components.msc.RcIngressCheckRows.Value')}</Col>
      </Row>
      {get(props, 'value', []).map((item, index) => (
        <Row key={index} style={{ marginTop: 10 }}>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Select
              value={item?.type || 'header'}
              dataSource={paramTypes}
              style={{ width: '100%' }}
              onChange={(v) => handleChange('type', v, index)}
            />
          </Col>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Input
              maxLength={64}
              showLimitHint
              value={item?.name}
              style={{ width: '100%' }}
              disabled={item.type === 'sourceIp'}
              onChange={(v) => handleChange('name', v, index)}
              onPressEnter={(e) => {
                e.preventDefault();
              }}
            />
          </Col>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Input disabled value={item?.cond} style={{ width: '100%' }} />
          </Col>
          <Col span={6}>
            <Input
              style={{ width: 'calc(100% - 22px)' }}
              value={item?.value}
              onChange={(v) => handleChange('value', v, index)}
              onPressEnter={(e) => {
                e.preventDefault();
              }}
            />

            <If condition={!isPreview}>
              <Button
                text
                style={{ width: 20 }}
                disabled={props.value?.length > 1 ? false : true}
                onClick={() => {
                  props.onChange(props.value.filter((i, idx) => idx !== index));
                }}
              >
                <div>
                  <Icon
                    className="rows-delete"
                    size="small"
                    style={{ marginLeft: 5 }}
                    type="delete"
                  />
                </div>
              </Button>
            </If>
          </Col>
        </Row>
      ))}
      {get(props, 'value', []).length < 10 && !isPreview && (
        <div style={{ marginTop: 10 }}>
          <LinkButton
            onClick={() => {
              props.onChange([...props.value, { type: 'header', name: '', cond: '==', value: '' }]);
            }}
          >
            <div>
              <Icon size="xs" type="plus" />
            </div>
            <span style={{ marginLeft: 5 }}>
              {intl('saenext.components.msc.RcIngressCheckRows.AddCondition')}
            </span>
          </LinkButton>
        </div>
      )}
    </div>
  );
};
