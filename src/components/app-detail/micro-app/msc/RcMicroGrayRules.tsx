import { intl } from '@ali/cnd';
import React, { FC, useState, useEffect } from 'react';
import { Field, Form, Radio, Input, LinkButton, Icon, Button, Select } from '@ali/cnd';
import { get, map, isEmpty, isEqual, forEach, find } from 'lodash';
import services from '../../../../services';
import RcSpringCloudCheckRows from './RcSpringCloudCheckRows';
import RcDubboCheckRows from './RcDubboCheckRows';
import If from '../../../shared/If';

type RuleItem = {
  key?: number;
  type?: string;
  path: string;
  serviceName?: string;
  version?: string;
  group?: string;
  condition: string;
  mseMethods?: { label: string; value: string }[];
  methodName?: string;
  dubboConditions?: { label: string; value: string }[];
  items: {
    type: string;
    name?: string;
    operator?: string;
    value: string;
    cond?: string;
  }[];
};
type Props = {
  appConfig: Record<string, any>;
  value: RuleItem[];
  onChange: (value: RuleItem[]) => {};
  isPreview?: boolean;
};
const FormItem = Form.Item;
const { Group: RadioGroup } = Radio;
export const FrameworkTypes = {
  sc: 'Sc',
  dubbo: 'Dubbo',
};

const RcMicroGrayRules: FC<Props> = (props) => {
  const { appConfig, value = [], onChange, isPreview } = props;
  const regionId = get(appConfig, 'RegionId');
  const mseApplicationId = get(appConfig, 'MseApplicationId');
  const field = Field.useField();
  const { init, setValues } = field;
  const [mseServices, setMseServices] = useState([]);

  useEffect(() => {
    getMseServices();
    if (!isEmpty(value)) return;
    onChange([
      { key: Math.random(), type: FrameworkTypes.sc, path: '', condition: 'AND', items: [] },
    ]);
  }, [mseApplicationId]);

  useEffect(() => {
    forEach(value, (rule) => {
      if (!isEmpty(rule.serviceName)) {
        const _mseService = find(mseServices, {
          value: rule.serviceName,
          serviceName: rule.serviceName,
          group: rule.group,
          version: rule.version,
        });
        const _mseMethods = get(_mseService, 'methods', []);
        rule.mseMethods = _mseMethods;
      }
      if (!isEmpty(rule.methodName)) {
        const _mseMethod = find(rule.mseMethods, { value: rule.methodName });
        const _dubboConditions = get(_mseMethod, 'parameterTypes', []);
        rule.dubboConditions = _dubboConditions;
      }
    });
  }, [value, mseServices]);

  const getMseServices = async () => {
    const mseApplicationId = get(appConfig, 'MseApplicationId');
    if (isEmpty(mseApplicationId)) return;
    const res = await services.getServiceList({
      params: {
        Region: regionId,
        ServiceType: 'dubbo',
        AppId: mseApplicationId,
        Source: 'edasmsc',
      },
      customErrorHandle: (error, data, callback) => {
        callback && callback();
      },
    });
    const data = get(res, 'Data', []);
    const _mseServices = map(data, (item) => {
      return {
        label: `${item.ServiceName}:${item.Version}${item.Group ? `:${item.Group}` : ''}`,
        value: item.ServiceName,
        version: item.Version,
        group: item.Group,
        serviceName: item.ServiceName,
        methods: map(item.Methods, (method) => {
          return {
            label: `${method.Name} (${method.ParameterTypes.join(', ')})`,
            value: method.Name,
            serviceName: item.ServiceName,
            version: item.Version,
            group: item.Group,
            methodName: method.Name,
            parameterTypes: method.ParameterTypes,
          };
        }),
      };
    });
    setMseServices(_mseServices);
  };

  const handleChange = (key, value, index) => {
    let _values = get(props, 'value', []);
    _values = map(_values, (item) => {
      if (item.key === index) {
        const _item = { ...item, [key]: value };
        if (key === 'serviceName') {
          const group = get(value, 'group', '');
          const version = get(value, 'version', '');
          const serviceName = get(value, 'serviceName', '');
          Reflect.set(_item, 'group', group);
          Reflect.set(_item, 'version', version);
          Reflect.set(_item, 'serviceName', serviceName);
        }
        if (key === 'methodName') {
          const methodName = get(value, 'methodName', '');
          Reflect.set(_item, 'methodName', methodName);
        }
        return _item;
      }
      return { ...item };
    });
    onChange && onChange(_values);
  };

  return (
    <>
      {get(props, 'value', []).map((rule) => {
        return (
          <div
            key={rule.key}
            style={{
              border: '1px dashed #ddd',
              padding: '0 16px',
              marginBottom: 8,
              borderRadius: 2,
            }}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                background: '#f2f2f2',
                padding: 16,
                margin: '0 -16px 8px -16px',
              }}
            >
              <span style={{ fontWeight: 'bold' }}>
                {intl('saenext.components.msc.RcMicroGrayRules.GrayscaleRules')}
              </span>
              <If condition={!isPreview}>
                <Button
                  text
                  style={{ width: 20 }}
                  disabled={props.value?.length > 1 ? false : true}
                  onClick={() => {
                    props.onChange(props.value.filter((item) => item.key !== rule.key));
                  }}
                >
                  <div>
                    <Icon
                      className="rows-delete"
                      size="small"
                      style={{ marginLeft: 5 }}
                      type="delete"
                    />
                  </div>
                </Button>
              </If>
            </div>
            <Form field={field}>
              <FormItem required label={intl('saenext.components.msc.RcMicroGrayRules.FrameType')}>
                <RadioGroup
                  {...init(`type-${rule.key}`, {
                    initValue: rule.type || FrameworkTypes.sc,
                    rules: [
                      {
                        required: true,
                        message: intl('saenext.components.msc.RcMicroGrayRules.SelectAFrameType'),
                      },
                    ],

                    props: {
                      onChange: (val) => {
                        handleChange(`type`, val, rule.key);
                      },
                    },
                  })}
                >
                  <Radio id="Sc" value={FrameworkTypes.sc}>
                    Spring Cloud
                  </Radio>
                  <Radio id="Dubbo" value={FrameworkTypes.dubbo}>
                    Dubbo
                  </Radio>
                </RadioGroup>
              </FormItem>
              <>
                {isEqual(get(rule, 'type', FrameworkTypes.sc), FrameworkTypes.sc) ? (
                  <FormItem
                    label="Path"
                    help={intl('saenext.components.msc.RcMicroGrayRules.ItStartsWithAndCan')}
                  >
                    <Input
                      {...init(`path-${rule.key}`, {
                        initValue: rule.path,
                        rules: [
                          {
                            trigger: ['onBlur', 'onChange'],
                            message: intl(
                              'saenext.components.msc.RcMicroGrayRules.ItStartsWithAndCan',
                            ),
                            pattern: /^[/][\u4e00-\u9fa5-a-z0-9]{0,62}/,
                          },
                        ],

                        props: {
                          onChange: (val) => handleChange(`path`, val, rule.key),
                        },
                      })}
                      maxLength={64}
                      showLimitHint
                      placeholder={intl(
                        'saenext.components.msc.RcMicroGrayRules.HttpRelativePathSuchAs',
                      )}
                    />
                  </FormItem>
                ) : (
                  <FormItem label={intl('saenext.components.msc.RcMicroGrayRules.ServiceMethod')}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Select
                        {...init(`serviceName-${rule.key}`, {
                          initValue: rule.serviceName,
                          rules: [
                            {
                              required: true,
                              message: intl(
                                'saenext.components.msc.RcMicroGrayRules.SelectAService',
                              ),
                            },
                          ],

                          props: {
                            onChange: (val, __, item) => {
                              const _mseMethods = get(item, 'methods', []);
                              rule.mseMethods = _mseMethods;
                              handleChange(
                                `serviceName`,
                                {
                                  serviceName: val,
                                  version: get(item, 'version', ''),
                                  group: get(item, 'group', ''),
                                },
                                rule.key,
                              );
                            },
                          },
                        })}
                        dataSource={mseServices}
                        style={{ flex: 6 }}
                      />

                      <Select
                        {...init(`methodName-${rule.key}`, {
                          initValue: rule.methodName,
                          rules: [
                            {
                              required: true,
                              message: intl(
                                'saenext.components.msc.RcMicroGrayRules.SelectAMethod',
                              ),
                            },
                          ],

                          props: {
                            onChange: (val, __, item) => {
                              const _dubboConditions = get(item, 'parameterTypes', []);
                              rule.dubboConditions = _dubboConditions;
                              handleChange(
                                `methodName`,
                                {
                                  serviceName: val,
                                  version: get(item, 'version', ''),
                                  group: get(item, 'group', ''),
                                  methodName: get(item, 'methodName', ''),
                                },
                                rule.key,
                              );
                            },
                          },
                        })}
                        dataSource={rule.mseMethods || []}
                        style={{ flex: 4, marginLeft: 8 }}
                      />
                    </div>
                  </FormItem>
                )}
              </>
              <FormItem
                required
                label={intl('saenext.components.msc.RcMicroGrayRules.ConditionalMode')}
              >
                <RadioGroup
                  {...init(`condition-${rule.key}`, {
                    initValue: rule.condition || 'AND',
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.components.msc.RcMicroGrayRules.SelectAConditionalMode',
                        ),
                      },
                    ],

                    props: {
                      onChange: (val) => handleChange(`condition`, val, rule.key),
                    },
                  })}
                >
                  <Radio id="AND" value="AND">
                    {intl('saenext.components.msc.RcMicroGrayRules.MeetTheFollowingConditionsAt')}
                  </Radio>
                  <Radio id="OR" value="OR">
                    {intl('saenext.components.msc.RcMicroGrayRules.MeetAnyOfTheFollowing')}
                  </Radio>
                </RadioGroup>
              </FormItem>
              <>
                {isEqual(get(rule, 'type', FrameworkTypes.sc), FrameworkTypes.sc) ? (
                  <FormItem
                    required
                    label={intl('saenext.components.msc.RcMicroGrayRules.ConditionList')}
                  >
                    <RcSpringCloudCheckRows
                      {...init(`scItems-${rule.key}`, {
                        initValue: rule.scItems || [],
                        rules: [
                          {
                            required: true,
                            message: intl(
                              'saenext.components.msc.RcMicroGrayRules.SelectARuleCondition',
                            ),
                          },
                        ],

                        props: {
                          onChange: (val) => handleChange(`scItems`, val, rule.key),
                        },
                      })}
                      isPreview={isPreview}
                    />
                  </FormItem>
                ) : (
                  <FormItem
                    required
                    label={intl('saenext.components.msc.RcMicroGrayRules.ConditionList')}
                  >
                    <RcDubboCheckRows
                      {...init(`dubboItems-${rule.key}`, {
                        initValue: rule.dubboItems || [],
                        rules: [
                          {
                            required: true,
                            message: intl(
                              'saenext.components.msc.RcMicroGrayRules.SelectARuleCondition',
                            ),
                          },
                        ],

                        props: {
                          onChange: (val) => handleChange(`dubboItems`, val, rule.key),
                        },
                      })}
                      dubboConditions={rule.dubboConditions || []}
                      isPreview={isPreview}
                    />
                  </FormItem>
                )}
              </>
            </Form>
          </div>
        );
      })}
      <If condition={!isPreview}>
        <div style={{ marginTop: 16 }}>
          <LinkButton
            onClick={() => {
              // @ts-ignore
              props.onChange([
                ...props.value,
                {
                  key: Math.random(),
                  type: FrameworkTypes.sc,
                  path: '',
                  condition: 'AND',
                  items: [{}] as any,
                },
              ]);
            }}
          >
            <div>
              <Icon size="xs" type="plus" />
            </div>
            <span style={{ marginLeft: 5 }}>
              {intl('saenext.components.msc.RcMicroGrayRules.AddGrayscaleRules')}
            </span>
          </LinkButton>
        </div>
      </If>
    </>
  );
};

export default RcMicroGrayRules;
