import React, { useState, useEffect } from 'react';
import MscPage from './MscPage';
import { Tab, Button, Dialog, Message, Badge, ToolTipCondition } from '@ali/cnd';
import { intl } from '@ali/cnd';
import services from '../../../../services';
import { isEmpty, noop } from 'lodash';
import { AES_CONSTANT, trackMicroAppMsc } from '~/tracker';

type TabKeys = 'seamlessDeploy' | 'grayScale' | 'systemProtect' | 'trafficProtect' | 'mqTag' | 'az';

type Props = {
  history?: any;
  regionId?: string;
  applicationID: string;
  isStartMsc: boolean;
  activeKey: TabKeys;
  NewSaeVersion: string;
  microServiceEnable: boolean;
  isNewMsc: boolean;
  disableMscCallback?: () => void;
  child: {
    seamlessDeploy: (mseApp) => React.ReactNode;
    grayScale: (mseApp) => React.ReactNode;
    systemProtect: (mseApp) => React.ReactNode;
    trafficProtect: (mseApp) => React.ReactNode;
    mqTag: (mseApp) => React.ReactNode;
    az: (mseApp) => React.ReactNode;
  };
};

const TrafficGovern = (props: Props) => {
  const {
    history,
    regionId,
    applicationID,
    isStartMsc,
    child,
    activeKey = 'seamlessDeploy',
    NewSaeVersion,
    microServiceEnable,
    isNewMsc,
    disableMscCallback = noop,
  } = props;
  const {
    seamlessDeploy: seamlessDeployChild,
    grayScale: grayScaleChild,
    systemProtect: systemProtectChild,
    trafficProtect: trafficProtectChild,
    mqTag: mqTagChild,
    az: azChild,
  } = child;

  const [currentActiveKey, setCurrentActiveKey] = useState(activeKey);
  const [refreshMscIndex, setRefreshMscIndex] = useState(0);

  useEffect(() => {
    setCurrentActiveKey(activeKey);
  }, [activeKey]);

  useEffect(() => {
    if (currentActiveKey === 'seamlessDeploy' || currentActiveKey === 'grayScale') {
      trackMicroAppMsc({
        behavior: 'VIEW',
        stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
        appId: applicationID,
        section: 'msc-traffic',
        trackType: currentActiveKey === 'seamlessDeploy' ? 'LOSSLESS' : 'GRAYSCALE_RULE',
      });
    }
  }, [currentActiveKey]);

  const handleCloseMsc = async () => {
    Dialog.alert({
      title: intl('saenext.components.msc.TrafficGovern.AreYouSureYouWant'),
      content: (
        <p style={{ width: 500 }}>
          {intl('saenext.components.msc.TrafficGovern.AfterTheMicroserviceGovernanceFunction')}
        </p>
      ),

      okProps: {
        children: intl('saenext.components.msc.TrafficGovern.Ok'),
      },
      onOk: () =>
        new Promise(async (resolve, reject) => {
          const res = await services.disableAppMseStatus({
            params: {
              AppId: applicationID,
            },
            customErrorHandle: (err, data, callback) => {
              callback && callback();
              reject(false);
            },
          });
          if (!isEmpty(res)) {
            Message.success(
              intl('saenext.components.msc.TrafficGovern.MicroserviceGovernanceClosedSuccessfully'),
            );
            // 请求新的变更单
            resolve(true);
            setRefreshMscIndex(Date.now());
            disableMscCallback && disableMscCallback();
          } else {
            Message.error(
              intl('saenext.components.msc.TrafficGovern.FailedToDisableMicroserviceGovernance'),
            );
            reject(false);
          }
        }),
    });
  };

  return (
    <MscPage
      history={history}
      regionId={regionId}
      pageName="seamlessDeploy"
      isStartMsc={isStartMsc}
      applicationId={applicationID}
      refreshMscIndex={refreshMscIndex}
      NewSaeVersion={NewSaeVersion}
      microServiceEnable={microServiceEnable}
      isNewMsc={isNewMsc}
      children={mseApp => {
        return (
          <Tab
            shape="wrapped"
            // @ts-ignore
            lazy
            unmountInactiveTabs
            activeKey={currentActiveKey}
            onChange={(key: TabKeys) => setCurrentActiveKey(key)}
            extra={
              !isNewMsc ? (
                <ToolTipCondition
                  show={!isStartMsc}
                  tip={intl('saenext.components.msc.TrafficGovern.TheCurrentApplicationIsNot')}
                  align="l"
                >
                  <Button type="primary" disabled={!isStartMsc} onClick={handleCloseMsc}>
                    {intl('saenext.components.msc.TrafficGovern.DisableMicroserviceGovernance')}
                  </Button>
                </ToolTipCondition>
              ) : null
            }
          >
            <Tab.Item
              title={intl('saenext.components.msc.TrafficGovern.LosslessUpperAndLowerLine')}
              key="seamlessDeploy"
            >
              {seamlessDeployChild(mseApp)}
            </Tab.Item>
            <Tab.Item
              title={intl('saenext.components.msc.TrafficGovern.GrayscaleRules')}
              key="grayScale"
            >
              {grayScaleChild(mseApp)}
            </Tab.Item>
            <Tab.Item
              title={intl('saenext.components.msc.TrafficGovern.SystemProtection')}
              key="systemProtect"
            >
              <MscPage
                history={history}
                regionId={regionId}
                pageName="systemProtect"
                isStartMsc={isStartMsc}
                applicationId={applicationID}
                NewSaeVersion={NewSaeVersion}
                microServiceEnable={microServiceEnable}
                isNewMsc={isNewMsc}
                children={systemProtectChild}
              />
            </Tab.Item>
            <Tab.Item
              title={intl('saenext.components.msc.TrafficGovern.TrafficProtection')}
              key="trafficProtect"
            >
              <MscPage
                history={history}
                regionId={regionId}
                pageName="trafficProtect"
                isStartMsc={isStartMsc}
                applicationId={applicationID}
                NewSaeVersion={NewSaeVersion}
                microServiceEnable={microServiceEnable}
                isNewMsc={isNewMsc}
                children={trafficProtectChild}
              />
            </Tab.Item>
            <Tab.Item
              title={
                <div style={{ position: 'relative' }}>
                  <span>{intl('saenext.components.msc.TrafficGovern.MessageGray')}</span>
                  {!isNewMsc && (
                    <span
                      style={{
                        backgroundColor: '#D50B16',
                        color: '#fff',
                        borderRadius: '8px',
                        lineHeight: '18px',
                        display: 'inline-block',
                        fontSize: 12,
                        fontWeight: 500,
                        padding: '0 4px',
                        position: 'inherit',
                        top: '-8px',
                        right: '4px',
                      }}
                    >
                      {intl('saenext.components.msc.TrafficGovern.InGrayScale')}
                    </span>
                  )}
                </div>
              }
              key="mqTag"
            >
              <MscPage
                history={history}
                regionId={regionId}
                pageName="mqTag"
                isStartMsc={isStartMsc}
                applicationId={applicationID}
                NewSaeVersion={NewSaeVersion}
                microServiceEnable={microServiceEnable}
                isNewMsc={isNewMsc}
                children={mqTagChild}
              />
            </Tab.Item>
            <Tab.Item
              title={
                <div style={{ position: 'relative' }}>
                  <span>
                    {intl('saenext.components.msc.TrafficGovern.TheSameZoneTakesPrecedence')}
                  </span>
                  <span
                    style={{
                      backgroundColor: '#D50B16',
                      color: '#fff',
                      borderRadius: '8px',
                      lineHeight: '18px',
                      display: 'inline-block',
                      fontSize: 12,
                      fontWeight: 500,
                      padding: '0 4px',
                      position: 'inherit',
                      top: '-8px',
                      right: '4px',
                    }}
                  >
                    {intl('saenext.components.msc.TrafficGovern.InGrayScale')}
                  </span>
                </div>
              }
              key="az"
            >
              <MscPage
                history={history}
                regionId={regionId}
                pageName="az"
                isStartMsc={isStartMsc}
                applicationId={applicationID}
                NewSaeVersion={NewSaeVersion}
                microServiceEnable={microServiceEnable}
                isNewMsc={isNewMsc}
                children={azChild}
              />
            </Tab.Item>
          </Tab>
        );
      }}
    />
  );
};

export default TrafficGovern;
