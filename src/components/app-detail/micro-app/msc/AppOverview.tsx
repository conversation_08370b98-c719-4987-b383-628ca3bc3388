import React, { useEffect } from 'react';
import MscIframe from './MscIframe';
import { get, isEqual } from 'lodash';
import CachedData from '~/cache/common';
import MscOverview from './MscOverview';

export default (props) => {
  const { history, location, regionId, appId, appStatus, NewSaeVersion, microServiceEnable, isNewMsc } = props;
  // 新应用+运行中+LastChangeOrderRunning=false 才可开启 Msc
  const isRunning = isEqual(get(appStatus, 'CurrentStatus'), 'RUNNING');
  const noChangeOrder = get(appStatus, 'LastChangeOrderRunning') !== true
  const isStartMsc = isRunning && noChangeOrder;

  useEffect(() => {
    window.addEventListener('message', handleReceiveMessage);
    return () => {
      window.removeEventListener('message', handleReceiveMessage);
    };
  }, []);

  const handleReceiveMessage = (event) => {
    if ([
      CachedData.confLink('feature:pre-mse:url'),
      CachedData.confLink('feature:mse:url')
    ].includes(event.origin)) {
      if (event.data === 'handleSystemProtection') {
        // 调用父窗口中的方法
        handleSystemProtection();
      }
      if (event.data === 'handleApiDetail') {
        // 调用父窗口中的方法
        handleApiDetail();
      }
    }
  };

  const handleSystemProtection = () => {
    // 系统防护
    const { search } = location;
    history.push(
      `/${regionId}/app-list/${appId}/micro-app/msc-traffic${search}&activeKey=systemProtect`,
    );
  };

  const handleApiDetail = () => {
    // 查看全部 跳转到接口详情
    const { search } = location;
    history.push(
      `/${regionId}/app-list/${appId}/micro-app/msc-interface${search}`,
    );
  };

  return (
    <MscOverview
      history={history}
      regionId={regionId}
      applicationID={appId}
      isStartMsc={isStartMsc}
      NewSaeVersion={NewSaeVersion}
      microServiceEnable={microServiceEnable}
      isNewMsc={isNewMsc}
      children= {
        (mseApp) => {
          const { MseAppId, MseAppName, MseAppNameSpace } = mseApp || {};
          const channel = get(window, 'ALIYUN_CONSOLE_CONFIG.CHANNEL', '');
          const baseUrl = CachedData.confLink(`feature:mse:url`);
          // 应用概览
          return (
            <MscIframe
              src={`${baseUrl}#/msc/appList/info?AppId=${MseAppId}&region=${regionId}&appName=${MseAppName}&ns=${MseAppNameSpace}&hideTopbar=true&hideNavbar=true&saeReference=true`}
            />
          );
        }
      }
    />
  );
};
