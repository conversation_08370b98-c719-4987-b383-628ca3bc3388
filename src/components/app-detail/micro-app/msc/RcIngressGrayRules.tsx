import { intl } from '@ali/cnd';
import React, { FC, useState, useEffect } from 'react';
import { Field, Form, Balloon, Icon, Select, CndTable } from '@ali/cnd';
import { get, map, isEmpty, filter, uniqBy, find, isEqual, forEach, head } from 'lodash';
import services from '../../../../services';
import RcIngressCheckRows from './RcIngressCheckRows';
import CachedData from '../../../../cache/common';
import './index.less';

type RuleItem = {
  albId?: string;
  ingressId?: string;
  serviceName: string;
  condition: string;
  items: {
    type: string;
    name?: string;
    operator?: string;
    value: string;
    cond?: string;
  }[];
};
type Props = {
  appConfig: Record<string, any>;
  value: RuleItem[];
  onChange: (value: RuleItem[]) => {};
  isPreview?: boolean;
};
const FormItem = Form.Item;
const AlbEditionMap = {
  Basic: intl('saenext.components.msc.RcIngressGrayRules.BasicEdition'),
  Standard: intl('saenext.components.msc.RcIngressGrayRules.StandardEdition'),
};
const AdressMap = {
  Internet: intl('saenext.components.msc.RcIngressGrayRules.PublicNetwork'),
  Intranet: intl('saenext.components.msc.RcIngressGrayRules.PrivateNetwork'),
};

const RcIngressGrayRules: FC<Props> = (props) => {
  const { appConfig, value = [], onChange, isPreview } = props;
  const appId = get(appConfig, 'AppId');
  const regionId = get(appConfig, 'RegionId');
  const namespaceId = get(appConfig, 'NamespaceId');
  const appName = get(appConfig, 'AppName');
  const field = Field.useField();
  const { init, setValues } = field;
  const [albIngressList, setAlbIngressList] = useState([]);
  const [albLoadBalancers, setAlbLoadBalancers] = useState([]);
  const [albDataSource, setAlbDataSource] = useState([]);
  const [ingressDataSource, setIngressDataSource] = useState([]);
  const [albEdition, setAlbEdition] = useState('');
  const [loading, setLoading] = useState(false);
  const [grayServices, setGrayServices] = useState([]);
  const [selectedServices, setSelectedServices] = useState([]);

  useEffect(() => {
    getAlbDataSource();
    if (isEmpty(value)) {
      onChange && onChange([{ ingressId: '', serviceName: '', condition: 'AND', items: [] }]);
    }
    setIngressValues();
  }, []);

  const setIngressValues = async () => {
    const record = head(value);
    const ingressId = get(record, 'ingressId', '');
    const serviceName = get(record, 'serviceName', '');
    const items = get(record, 'items', []);
    setValues({ items });
    setSelectedServices([serviceName]);
    const ingress = await getServices2Ingress(ingressId);
    // 通过ingress详情获取到绑定的slb
    const albId = get(ingress, 'SlbId', '');
    const _albIngressList = await getAlbIngressList();
    await getIngressDataSource(albId, _albIngressList);
    setValues({ albId, ingressId, items });
  };

  const getAlbDataSource = async () => {
    const _albLoadBalancers = await getAlbLoadBalancers();
    const _albIngressList = await getAlbIngressList();
    if (isEmpty(_albLoadBalancers) || isEmpty(_albIngressList)) return;

    const filterAlbs = uniqBy(_albIngressList, 'SlbId');
    const _albDataSource = map(filterAlbs, (ingress) => {
      const { SlbId } = ingress;
      const targetAlb = find(_albLoadBalancers, { LoadBalancerId: SlbId }) || {};
      const { AddressType = '', LoadBalancerEdition = '', LoadBalancerName = '' } = targetAlb;
      const notTarget = isEmpty(targetAlb);
      return {
        value: SlbId,
        label: !notTarget
          ? `${AdressMap[AddressType] || ''}-${AlbEditionMap[LoadBalancerEdition] || ''}-${
              LoadBalancerName || SlbId
            }`
          : SlbId,
        LoadBalancerEdition,
        disabled: notTarget,
      };
    });
    setAlbDataSource(_albDataSource);
  };

  const getIngressDataSource = (SlbId, ingressList?: any[]) => {
    const _albIngressList = isEmpty(ingressList) ? albIngressList : ingressList;
    const filterAlbs = filter(_albIngressList, { SlbId });
    const _ingressDataSource = map(filterAlbs, (ingress) => {
      const { Id, ListenerProtocol, ListenerPort } = ingress;
      return {
        value: Id,
        label: intl(
          'saenext.components.msc.RcIngressGrayRules.PortListenerportFrontendProtocolListenerprotocol',
          { ListenerPort: ListenerPort, ListenerProtocol: ListenerProtocol },
        ),
      };
    });
    setIngressDataSource(_ingressDataSource);
  };

  const getAlbLoadBalancers = async () => {
    const res = await services.getAlbLoadBalancers({
      params: {
        NextToken: '',
        MaxResults: 50,
        RegionId: regionId,
      },
    });
    const _albLoadBalancers = get(res, 'LoadBalancers', []);
    setAlbLoadBalancers(_albLoadBalancers);
    return _albLoadBalancers;
  };

  const getAlbIngressList = async () => {
    if (isEmpty(namespaceId)) return;
    const res = await services.getListIngresses({
      RegionId: regionId,
      NamespaceId: namespaceId,
      AppId: appId,
    });
    const _ingressList = get(res, 'Data.IngressList', []);
    const _albIngressList = filter(_ingressList, { LoadBalanceType: 'alb' });
    setAlbIngressList(_albIngressList);
    return _albIngressList;
  };

  const getServices2Ingress = async (ingressId) => {
    if (!ingressId) return;
    setLoading(true);
    const res = await services.getIngressInfo({
      RegionId: regionId,
      IngressId: ingressId,
    });
    const { Data: _ingress = {} } = res || {};
    const svcs = get(_ingress, 'Svcs', []);
    const _grayServices = filter(svcs, { appId });
    forEach(_grayServices, (service) => {
      service.appName = appName;
    });
    setLoading(false);
    setGrayServices(_grayServices);
    return _ingress;
  };

  const handleChange = (key, value) => {
    let _values = get(props, 'value', []);
    _values = map(_values, (item) => {
      return { ...item, [key]: value };
    });
    onChange && onChange(_values);
  };

  return (
    <>
      <Form field={field}>
        <FormItem
          required
          label={
            <>
              <span style={{ marginRight: 8 }}>
                {intl('saenext.components.msc.RcIngressGrayRules.GatewayInstanceAlbIngress')}
              </span>
              <Balloon
                cache={true}
                align="r"
                trigger={<Icon size="xs" type="help" />}
                closable={false}
              >
                {intl('saenext.components.msc.RcIngressGrayRules.AlbIngressIsAMore')}
              </Balloon>
            </>
          }
          help={
            <>
              {isEmpty(albLoadBalancers) ? (
                <span>
                  {intl('saenext.components.msc.RcIngressGrayRules.ThereIsNoAlbInstance')}
                  <a href={`${CachedData.confLink('feature:slb:url')}/alb`} target="_blank">
                    {intl('saenext.components.msc.RcIngressGrayRules.AlbConsole')}
                  </a>
                  {intl('saenext.components.msc.RcIngressGrayRules.Create')}
                </span>
              ) : null}
              {isEqual(albEdition, 'Basic') ? (
                <span>
                  {intl('saenext.components.msc.RcIngressGrayRules.TheBasicVersionOfAlb')}
                </span>
              ) : null}
            </>
          }
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Select
              {...init('albId', {
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.msc.RcIngressGrayRules.SelectAGatewayInstance',
                    ),
                  },
                ],

                props: {
                  onChange: (val) => {
                    getIngressDataSource(val);
                    setValues({ ingressId: '' });
                    const { LoadBalancerEdition = '' } = find(albDataSource, { value: val }) || {};
                    setAlbEdition(LoadBalancerEdition);
                  },
                },
              })}
              dataSource={albDataSource}
              placeholder={intl('saenext.components.msc.RcIngressGrayRules.SelectAGatewayInstance')}
              style={{ flex: 6 }}
              renderPreview={(val: any) => {
                if (isEmpty(val)) return '';
                const { label = '', value = '' } = val;
                return `${label}(${value})`;
              }}
            />

            <Select
              {...init('ingressId', {
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.msc.RcIngressGrayRules.SelectPortAndProtocol',
                    ),
                  },
                ],

                props: {
                  onChange: (val) => {
                    setSelectedServices([]);
                    getServices2Ingress(val);
                    handleChange('ingressId', val);
                  },
                },
              })}
              dataSource={ingressDataSource}
              placeholder={intl('saenext.components.msc.RcIngressGrayRules.SelectPortAndProtocol')}
              style={{ flex: 4, marginLeft: 8 }}
            />
          </div>
        </FormItem>
        <FormItem
          required
          label={intl('saenext.components.msc.RcIngressGrayRules.GrayscaleServices')}
          className="ingress-rules"
        >
          <CndTable
            loading={loading}
            dataSource={grayServices}
            hasBorder={false}
            size="small"
            columns={[
              {
                title: intl('saenext.components.msc.RcIngressGrayRules.ApplicationName'),
                dataIndex: 'appName',
              },
              {
                title: intl('saenext.components.msc.RcIngressGrayRules.ContainerPort'),
                dataIndex: 'backendPort',
              },
              {
                title: intl('saenext.components.msc.RcIngressGrayRules.BackendProtocol'),
                dataIndex: 'backendProtocol',
              },
            ]}
            emptyContent={
              <div>
                {intl('saenext.components.msc.RcIngressGrayRules.SelectAGatewayInstancePort')}
              </div>
            }
            primaryKey="name"
            rowSelection={{
              mode: 'single',
              selectedRowKeys: selectedServices,
              onChange: (val) => {
                setSelectedServices(val);
                const svcName = head(val);
                handleChange('serviceName', svcName);
              },
            }}
          />
        </FormItem>
        <FormItem required label={intl('saenext.components.msc.RcIngressGrayRules.ConditionList')}>
          <RcIngressCheckRows
            {...init('items', {
              initValue: get(value, 'items', []),
              rules: [
                {
                  required: true,
                  message: intl('saenext.components.msc.RcIngressGrayRules.SelectARuleCondition'),
                },
              ],

              props: {
                onChange: (val) => handleChange('items', val),
              },
            })}
            albEdition={albEdition}
            isPreview={isPreview}
          />
        </FormItem>
      </Form>
    </>
  );
};

export default RcIngressGrayRules;
