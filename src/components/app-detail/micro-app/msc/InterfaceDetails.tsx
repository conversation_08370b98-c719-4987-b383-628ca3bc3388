import React from 'react';
import MscPage from './MscPage';

type Props = {
  history?: any;
  regionId?: string;
  applicationID: string;
  isStartMsc: boolean;
  NewSaeVersion: string;
  microServiceEnable: boolean;
  isNewMsc: boolean;
  children: (mseApp) => React.ReactNode;
};


const InterfaceDetails = (props: Props) => {
  const { history, regionId, applicationID, isStartMsc, NewSaeVersion, microServiceEnable,isNewMsc, children } = props;

  return (
    <MscPage
      history={history}
      regionId={regionId}
      pageName="interfaceDetails"
      isStartMsc={isStartMsc}
      applicationId={applicationID}
      NewSaeVersion={NewSaeVersion}
      microServiceEnable={microServiceEnable}
      isNewMsc={isNewMsc}
      children= {children}
    />
  );
};

export default InterfaceDetails;
