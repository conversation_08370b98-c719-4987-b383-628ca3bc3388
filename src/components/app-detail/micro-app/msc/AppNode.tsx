import React from 'react';
import MscIframe from './MscIframe';
import MscNode from '~/components/app-detail/micro-app/msc/NodeDetails';
import { get, isEqual } from 'lodash';
import CachedData from '~/cache/common';

export default (props) => {
  const { history, regionId, appId, appStatus, NewSaeVersion, microServiceEnable, isNewMsc } = props;
  // 新应用+运行中+LastChangeOrderRunning=false 才可开启 Msc
  const isRunning = isEqual(get(appStatus, 'CurrentStatus'), 'RUNNING');
  const noChangeOrder = get(appStatus, 'LastChangeOrderRunning') !== true
  const isStartMsc = isRunning && noChangeOrder;


  return (
    <MscNode
      history={history}
      regionId={regionId}
      applicationID={appId}
      isStartMsc={isStartMsc}
      NewSaeVersion={NewSaeVersion}
      microServiceEnable={microServiceEnable}
      isNewMsc={isNewMsc}
      children= {
        (mseApp) => {
          const { MseAppId, MseAppName, MseAppNameSpace } = mseApp || {};
          const channel = get(window, 'ALIYUN_CONSOLE_CONFIG.CHANNEL', '');
          const baseUrl = CachedData.confLink('feature:mse:url');
          // 节点详情
          return (
            <MscIframe
              src={`${baseUrl}#/msc/appList/info/nodeDetails?AppId=${MseAppId}&region=${regionId}&appName=${MseAppName}&ns=${MseAppNameSpace}&hideTopbar=true&hideNavbar=true&saeReference=true`}
            />
          );
        }
      }
    />
  );
};
