import { intl } from '@ali/cnd';
import React from 'react';
import { Button } from '@ali/cnd';
const AppProMscNotEnable = (props) => {
  const demoOptions = [
    {
      title: intl('saenext.components.msc.AppProMscNotEnable.LosslessUpperAndLowerLine'),
      imgUrl:
        'https://img.alicdn.com/imgextra/i3/O1CN01xlq1nS1bqq05sVukp_!!6000000003517-2-tps-2064-694.png',
    },
    {
      title: intl('saenext.components.msc.AppProMscNotEnable.TrafficProtection'),
      imgUrl:
        'https://img.alicdn.com/imgextra/i2/O1CN01AmfyoU1oTYsQJWER4_!!*************-2-tps-1636-848.png',
    },
    {
      title: intl('saenext.components.msc.AppProMscNotEnable.FullLinkGray'),
      imgUrl:
        'https://img.alicdn.com/imgextra/i4/O1CN016MZZME213XNJ452co_!!*************-2-tps-1945-723.png',
    },
    {
      title: intl('saenext.components.msc.AppProMscNotEnable.RoutingInTheSameZone'),
      imgUrl:
        'https://img.alicdn.com/imgextra/i3/O1CN01vIcytF1kOkFRq3EPu_!!*************-2-tps-1976-967.png',
    },
  ];

  return (
    <div
      style={{
        overflow: 'auto',
        marginTop: ['systemProtect', 'trafficProtect'].includes(props.pageName) ? 10 : 0,
      }}
    >
      <div style={{ fontSize: '14px', fontWeight: 'bold', lineHeight: '24px', marginBottom: 10 }}>
        {intl('saenext.components.msc.AppProMscNotEnable.WelcomeToMicroserviceGovernance')}
      </div>
      <div style={{ lineHeight: '20px' }}>
        {intl('saenext.components.msc.AppProMscNotEnable.ServerlessTheApplicationEngineIntegrates')}

        <br />
        {intl('saenext.components.msc.AppProMscNotEnable.LosslessUplinkAndDownlinkCanary')}
      </div>
      <div className="mt-l">
        <Button
          type="primary"
          onClick={() => {
            const { history, regionId, applicationID } = props;
            const search = window?.location?.search
              ? `${window.location.search}&targetPart=microService`
              : '?targetPart=microService';
            history.push(`/${regionId}/app-list/${applicationID}/micro-app/deploy${search}`);
          }}
        >
          {intl('saenext.components.msc.AppProMscNotEnable.ConfigureMicroserviceGovernance')}
        </Button>
      </div>
      <div
        className="mt-xl"
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '20px',
          width: '95%',
          maxWidth: 1560,
          paddingBottom: 20,
        }}
      >
        {demoOptions.map((item) => {
          return (
            <div style={{ flex: '1 1 calc(50% - 10px)' }}>
              <div className="mb-s" style={{ fontWeight: 'bold' }}>
                {item.title}
              </div>
              <div
                style={{
                  width: '100%',
                  height: '300px',
                  border: '1px solid #e5e5e5',
                  borderRadius: '4px',
                  backgroundImage: `url(${item.imgUrl})`,
                  backgroundSize: 'contain',
                  backgroundRepeat: 'no-repeat',
                }}
              ></div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AppProMscNotEnable;
