import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import MscIframe from './MscIframe';
import Msc<PERSON>raffic from '~/components/app-detail/micro-app/msc/TrafficGovern';
import MscGrayRules from '~/components/app-detail/micro-app/msc/GrayRuleRoutes';
import { get, isEqual } from 'lodash';
import { getParams, removeParams } from '~/utils/global';
import CachedData from '~/cache/common';

export default props => {
  const {
    history,
    regionId,
    appId,
    appConfig,
    appStatus,
    NewSaeVersion,
    microServiceEnable,
    isNewMsc,
    albGray
  } = props;
  // 新应用+运行中+LastChangeOrderRunning=false 才可开启 Msc
  const isRunning = isEqual(get(appStatus, 'CurrentStatus'), 'RUNNING');
  const noChangeOrder = get(appStatus, 'LastChangeOrderRunning') !== true;
  const isStartMsc = isRunning && noChangeOrder;
  const activeKey = getParams('activeKey') || 'seamlessDeploy';

  const channel = get(window, 'ALIYUN_CONSOLE_CONFIG.CHANNEL', '');
  const baseUrl = CachedData.confLink('feature:mse:url');

  useEffect(() => {
    return () => {
      removeParams('activeKey');
    };
  }, []);

  useEffect(() => {
    window.addEventListener('message', handleDelayTime);
    return () => {
      window.removeEventListener('message', handleDelayTime);
    };
  }, []);

  const handleDelayTime = event => {
    if (
      ['https://mse.console.aliyun.com'].includes(
        event.origin,
      )
    ) {
      if (event.data === 'handleDelayTime') {
        // 调用父窗口中的方法
        const search = window?.location?.search
          ? `${window.location.search}&targetPart=microService`
          : '?targetPart=microService';
        history.push(`/${regionId}/app-list/${appId}/micro-app/deploy${search}`);
      }
    }
  };

  return (
    <MscTraffic
      history={history}
      regionId={regionId}
      applicationID={appId}
      isStartMsc={isStartMsc}
      // @ts-ignore
      activeKey={activeKey}
      NewSaeVersion={NewSaeVersion}
      microServiceEnable={microServiceEnable}
      isNewMsc={isNewMsc}
      albGray={albGray}
      child={{
        seamlessDeploy: mseApp => {
          const { MseAppId, MseAppName, MseAppNameSpace } = mseApp || {};
          let search=`AppId=${MseAppId}&region=${regionId}&appName=${MseAppName}&ns=${MseAppNameSpace}&hideTopbar=true&hideNavbar=true&hideTabbar=true&type=lossless&saeReference=true`;
          if(isNewMsc){
            search = `${search}&isMultipleSAE=true&isSaePre=${window.ALIYUN_CONSOLE_CONFIG.fEnv==='pre'}`;
          }
          return (
            <MscIframe
              className="mt-s"
              src={`${baseUrl}#/msc/appList/info/flowGovernment?${search}`}
            />
          );
        },
        grayScale: () => (
          <div className="mt-s">
            <MscGrayRules appId={appId} regionId={regionId} appConfig={appConfig} albGray={albGray} />
          </div>
        ),
        systemProtect: mseApp => {
          const { MseAppId, MseAppName, MseAppNameSpace } = mseApp || {};
          return (
            <MscIframe
              className="mt-s"
              src={`${baseUrl}#/msc/appList/info/flowGovernment?AppId=${MseAppId}&region=${regionId}&appName=${MseAppName}&ns=${MseAppNameSpace}&type=systemProtection&hideTopbar=true&hideNavbar=true&hideTabbar=true&saeReference=true`}
            />
          );
        },
        trafficProtect: mseApp => {
          const { MseAppId, MseAppName, MseAppNameSpace } = mseApp || {};
          return (
            <MscIframe
              className="mt-s"
              src={`${baseUrl}#/msc/appList/info/flowGovernment?AppId=${MseAppId}&region=${regionId}&appName=${MseAppName}&ns=${MseAppNameSpace}&type=flowProtection&hideTopbar=true&hideNavbar=true&hideTabbar=true&saeReference=true`}
            />
          );
        },
        mqTag: mseApp => {
          const { MseAppId, MseAppName, MseAppNameSpace } = mseApp || {};
          return (
            <MscIframe
              className="mt-s"
              src={`${baseUrl}#/msc/appList/info/flowGovernment?AppId=${MseAppId}&region=${regionId}&appName=${MseAppName}&ns=${MseAppNameSpace}&type=mq_tag&hideTopbar=true&hideNavbar=true&hideTabbar=true&saeReference=true`}
            />
          );
        },
        az: mseApp => {
          const { MseAppId, MseAppName, MseAppNameSpace } = mseApp || {};
          return (
            <MscIframe
              className="mt-s"
              src={`${baseUrl}#/msc/appList/info/flowGovernment?AppId=${MseAppId}&region=${regionId}&appName=${MseAppName}&ns=${MseAppNameSpace}&type=AZ&hideTopbar=true&hideNavbar=true&hideTabbar=true&saeReference=true`}
            />
          );
        },
      }}
    />
  );
};
