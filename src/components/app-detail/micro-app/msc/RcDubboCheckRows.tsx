import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import { Button, LinkButton, Grid, Input, Select, Icon, Balloon } from '@ali/cnd';
import { get, cloneDeep, forEach, isEmpty, includes, split } from 'lodash';
import If from '../../../shared/If';

const { Row, Col } = Grid;
const Conditions = [
  { label: '=', value: '==' },
  { label: '!=', value: '!=' },
  { label: '>', value: '>' },
  { label: '<', value: '<' },
  { label: '>=', value: '>=' },
  { label: '<=', value: '<=' },
  { label: 'in', value: 'list' },
  { label: intl('saenext.components.msc.RcDubboCheckRows.Percentage'), value: '%' },
  {
    label: intl('saenext.components.msc.RcDubboCheckRows.Die'),
    value: 'mod',
    children: [
      { value: 'mod-==', label: intl('saenext.components.msc.RcDubboCheckRows.Die.1') },
      { value: 'mod-!=', label: intl('saenext.components.msc.RcDubboCheckRows.Mold') },
      { value: 'mod->', label: intl('saenext.components.msc.RcDubboCheckRows.Die.2') },
      { value: 'mod-<', label: intl('saenext.components.msc.RcDubboCheckRows.Die.3') },
      { value: 'mod->=', label: intl('saenext.components.msc.RcDubboCheckRows.Die.4') },
      { value: 'mod-<=', label: intl('saenext.components.msc.RcDubboCheckRows.Die.5') },
    ],
  },
];

const formatRowItem = (item) => {
  const translateCond = (cond) => {
    switch (cond) {
      case 'list':
        return '==';
      case '%':
        return '<';
      default:
        return includes(cond, 'mod') ? get(split(cond, '-'), '[1]') : cond;
    }
  };

  const translateOperator = (cond) => {
    switch (cond) {
      case 'list':
        return 'list';
      case '%':
        return 'deterministic_proportional_steaming_division';
      default:
        return includes(cond, 'mod') ? 'mod' : 'rawvalue';
    }
  };
  const { cond } = item;
  const newItem = {
    ...item,
    cond: translateCond(cond),
    operator: translateOperator(cond),
  };
  return newItem;
};

export const formatDubboRules = (dubboRules) => {
  const _dubboRules = cloneDeep(dubboRules);
  let __dubboRules = [];
  forEach(_dubboRules, (rule) => {
    const _rule = cloneDeep(rule);
    let _items = [];
    forEach(_rule.dubboItems, (item, index) => {
      const __item = formatRowItem(item);
      Reflect.deleteProperty(__item, 'type');
      _items.push({ ...__item, index });
    });
    _rule.items = _items;
    Reflect.deleteProperty(_rule, 'key');
    Reflect.deleteProperty(_rule, 'type');
    Reflect.deleteProperty(_rule, 'scItems');
    Reflect.deleteProperty(_rule, 'dubboItems');
    Reflect.deleteProperty(_rule, 'dubboConditions');
    Reflect.deleteProperty(_rule, 'mseMethods');
    __dubboRules.push(_rule);
  });
  return __dubboRules;
};

export default (props) => {
  const { dubboConditions = [], value = [], onChange, isPreview } = props;

  useEffect(() => {
    if (!isEmpty(value)) return;
    onChange([{ type: '', expr: '', cond: '', value: '' }]);
  }, []);

  const handleChange = (key, value, index) => {
    let _value = get(props, 'value', []);
    _value = _value.map((item, idx) => {
      if (idx === index) {
        return { ...item, type: item.type, [key]: value };
      }
      return { ...item, type: item.type };
    });
    onChange && onChange(_value);
  };

  return (
    <div style={{ background: '#f6f6f6', padding: 16 }}>
      <Row style={{ color: '#555' }}>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.msc.RcDubboCheckRows.Parameter')}
        </Col>
        <Col span="6" style={{ paddingRight: 10 }}>
          <span style={{ marginRight: 8 }}>
            {intl('saenext.components.msc.RcDubboCheckRows.ParameterValueObtainingExpression')}
          </span>
          <Balloon cache={true} align="t" trigger={<Icon size="xs" type="help" />} closable={false}>
            <div>{intl('saenext.components.msc.RcDubboCheckRows.AParameterExpressionIsUsed')}</div>
            <div>
              <span style={{ color: '#0070cc' }}>
                {intl('saenext.components.msc.RcDubboCheckRows.LeaveBlank')}
              </span>
              {intl('saenext.components.msc.RcDubboCheckRows.IndicatesThatTheValueOf')}
            </div>
            <div>
              <span style={{ color: '#0070cc' }}>{'.name'}</span>
              {intl('saenext.components.msc.RcDubboCheckRows.IndicatesTheNameAttributeOf')}
            </div>
            <div>
              <span style={{ color: '#0070cc' }}>{'.isEnabled()'}</span>
              {intl('saenext.components.msc.RcDubboCheckRows.IndicatesThatTheEnabledAttribute')}
            </div>
            <div>
              <span style={{ color: '#0070cc' }}>{'[0]'}</span>
              {intl('saenext.components.msc.RcDubboCheckRows.IndicatesThatTheCurrentParameter')}
            </div>
            <div>
              <span style={{ color: '#0070cc' }}>{'.get(0)'}</span>
              {intl('saenext.components.msc.RcDubboCheckRows.IndicatesThatTheCurrentParameter.1')}
            </div>
            <div>
              <span style={{ color: '#0070cc' }}>{'.get("key")'}</span>
              {intl('saenext.components.msc.RcDubboCheckRows.IndicatesThatTheCurrentParameter.2')}
            </div>
          </Balloon>
        </Col>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.msc.RcDubboCheckRows.Condition')}
        </Col>
        <Col span="6">{intl('saenext.components.msc.RcDubboCheckRows.Value')}</Col>
      </Row>
      {get(props, 'value', []).map((item, index) => (
        <Row key={index} style={{ marginTop: 10 }}>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Select
              value={item?.type || dubboConditions[0]}
              dataSource={dubboConditions}
              style={{ width: '100%' }}
              onChange={(v) => handleChange('type', v, index)}
            />
          </Col>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Input
              maxLength={64}
              showLimitHint
              addonTextBefore="arg"
              value={item?.expr}
              style={{ width: '100%' }}
              onChange={(v) => handleChange('expr', v, index)}
              onPressEnter={(e) => {
                e.preventDefault();
              }}
            />
          </Col>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Select
              value={item?.cond}
              dataSource={Conditions}
              style={{ width: '100%' }}
              onChange={(v) => handleChange('cond', v, index)}
            />
          </Col>
          <Col span={6}>
            <Input
              style={{ width: 'calc(100% - 22px)' }}
              value={item?.value}
              onChange={(v) => handleChange('value', v, index)}
              onPressEnter={(e) => {
                e.preventDefault();
              }}
            />

            <If condition={!isPreview}>
              <Button
                text
                style={{ width: 20 }}
                disabled={props.value?.length > 1 ? false : true}
                onClick={() => {
                  props.onChange(props.value.filter((i, idx) => idx !== index));
                }}
              >
                <div>
                  <Icon
                    className="rows-delete"
                    size="small"
                    style={{ marginLeft: 5 }}
                    type="delete"
                  />
                </div>
              </Button>
            </If>
          </Col>
        </Row>
      ))}
      {get(props, 'value', []).length < 10 && !isPreview && (
        <div style={{ marginTop: 10 }}>
          <LinkButton
            onClick={() => {
              props.onChange([...props.value, { type: '', expr: '', cond: '', value: '' }]);
            }}
          >
            <div>
              <Icon size="xs" type="plus" />
            </div>
            <span style={{ marginLeft: 5 }}>
              {intl('saenext.components.msc.RcDubboCheckRows.AddCondition')}
            </span>
          </LinkButton>
        </div>
      )}
    </div>
  );
};
