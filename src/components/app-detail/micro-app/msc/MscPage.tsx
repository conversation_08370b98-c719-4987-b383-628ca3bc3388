import React, { useState, useEffect } from 'react';
import { intl, Button, Dialog, Message, ToolTipCondition, Loading,Input,Form } from '@ali/cnd';
import _ from 'lodash';
import services from '../../../../services';
import FetchMscUse from './FetchMscUse';
import AppProMscNotEnable from './AppProMscNotEnable';
import CachedData from '../../../../cache/common';

type Props = {
  pageName: 'appOverview' | 'interfaceDetails' | 'nodeDetails' | 'seamlessDeploy' | 'grayScale' | 'systemProtect' | 'trafficProtect' | 'mqTag' | 'az';
  applicationId: string;
  isStartMsc: boolean;
  refreshMscIndex?: number;
  NewSaeVersion: string;
  microServiceEnable: boolean;
  history?: any;
  regionId?: string;
  isNewMsc?: boolean;
  children: (mseApp) => React.ReactNode;
};

const mseVersion = new Map()
  .set(0, 'basic')
  .set(1, 'profess')
  .set(2, 'business')
  .set(3, 'disable')
  .set(4, 'test')
  .set(5, 'testExpire');
  const mseVersionLableMap = new Map()
  .set('basic', intl('saenext.components.msc.FetchMseUse.BasicEdition'))
  .set('profess', intl('saenext.components.msc.FetchMseUse.ProfessionalEdition'))
  .set('business', intl('saenext.components.msc.FetchMseUse.EnterpriseEdition'))
  .set('disable', intl('saenext.components.msc.FetchMseUse.NotActivated'))
  .set('test', intl('saenext.components.msc.FetchMseUse.TrialVersion'))
  .set('testExpire', intl('saenext.components.msc.FetchMseUse.TrialVersion'));


const MscPage = (props: Props) => {
  const { pageName, applicationId, isStartMsc, refreshMscIndex, NewSaeVersion, microServiceEnable,isNewMsc, children } = props;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [mscVersion, setMscVersion] = useState('');
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [rebooting, setRebooting] = useState(false);
  const [mseApp, setMseApp] = useState({ MseAppId: '', MseAppName: '', MseAppNameSpace: '' });
  const [freeVersion, setFreeVersion] = useState(0);
  const [mseAppNameSpace,setMseAppNameSpace] = useState('');
  const isIntl = CachedData.isSinSite();


  useEffect(() => {
    // 获取是否已经开启msc
    getAppMseStatus();
  }, [refreshIndex, refreshMscIndex, NewSaeVersion, microServiceEnable, isNewMsc]);

  const getAppMseStatus = async () => {
    setIsLoading(true);
    try {
      const data = await services.getAppMseStatus({
        params: {
          AppId: applicationId,
          // enableAhas 之前的逻辑：限流降级是true. 无损上下线 灰度规则是false
          // 企业版需要传 true
          // EnableAhas: pageName === 'throttle',
          EnableAhas: ['systemProtect', 'trafficProtect'].includes(pageName),
        },
        customErrorHandle: (err, data, callback) => {
          setIsLoading(false);
          callback && callback();
        },
      });
      const { Data = {} } = data;
      const { MseAppId, MseAppName, MseAppNameSpace, Status = 'express' } = Data;
      setMseAppNameSpace(MseAppNameSpace);
      // 新版微服务治理（创建/部署应用）--此处获取mseApp信息，是否开启的判断根据应用的MicroserviceEngineConfig以及MseAppId是否存在判断
      if (isNewMsc) {
        setIsLoading(false);
        const _mseApp = { MseAppId, MseAppName, MseAppNameSpace };
        setMseApp(_mseApp);
        if (microServiceEnable) {
          await getUserStatus();
        }
      } else {
        // 未区分版本&&enableSaeStdVersionNewMse 之前的逻辑
        // Status === SUCCESS 或者 Status为空  REBOOTING开启中
        const _enableMsc = Status === 'SUCCESS' || (MseAppId && _.isEmpty(Status));
        setRebooting(Status === 'REBOOTING');
        let _mseApp = { MseAppId: '', MseAppName: '', MseAppNameSpace: '' };
        if (_enableMsc) {
          setIsLoading(false);
          // 直接显示iframe
          _mseApp = { MseAppId, MseAppName, MseAppNameSpace };
          setMseApp(_mseApp);
          return;
        }
        setMseApp(_mseApp);
        if (!_enableMsc) {
          await getUserStatus();
          setIsLoading(false);
        }
      }

    } catch (error) {
      setIsLoading(false);
    }
  };

  const getUserStatus = async () => {
    // 获取用户当前开通的 mse 服务治理的版本 GetUserStatus
    const res = await services.getUserStatus({
      customErrorHandle: (err, data, callback) => {
        setIsLoading(false);
        callback();
      },
    });
    const { Data = {} } = res;
    // Status    是否开通  1 未开通、2 开通
    // Version   开通版本  0 基础版、 1 专业版、 2 企业版
    // FreeVersion  是否开通试用版  0 未开通、1 开通试用版、 2 试用版到期
    const { Status = 1, Version = 0, FreeVersion = 0 } = Data;
    let version = 3;
    if (Status === 2 && FreeVersion !== 1) {
      version = Version;
    } else {
      version = FreeVersion === 0 ? 3 : FreeVersion + 3;
    }
    const _mscVersion = mseVersion.get(version);
    setMscVersion(_mscVersion);
    setFreeVersion(FreeVersion);
  };

  const refreshContent = (mseApp) => {
    setMseApp(mseApp);
  };

  const handleCloseMsc = async () => {
    Dialog.alert({
      title: intl('saenext.components.msc.TrafficGovern.AreYouSureYouWant'),
      content: (
        <p style={{ width: 500 }}>
          {intl('saenext.components.msc.TrafficGovern.AfterTheMicroserviceGovernanceFunction')}
        </p>
      ),

      okProps: {
        children: intl('saenext.components.msc.TrafficGovern.Ok'),
      },
      onOk: () =>
        new Promise(async (resolve, reject) => {
          const res = await services.disableAppMseStatus({
            params: {
              AppId: applicationId,
            },
            customErrorHandle: (err, data, callback) => {
              callback && callback();
              reject(false);
            },
          });
          if (!_.isEmpty(res)) {
            Message.success(
              intl('saenext.components.msc.TrafficGovern.MicroserviceGovernanceClosedSuccessfully'),
            );
            resolve(true);
            getAppMseStatus();
          } else {
            Message.error(
              intl('saenext.components.msc.TrafficGovern.FailedToDisableMicroserviceGovernance'),
            );
            reject(false);
          }
        }),
    });
  };

  const renderContent = () => {
    const { MseAppId } = mseApp;
    if (isNewMsc) {
      if (microServiceEnable && MseAppId) {
        return children && children(mseApp);
      } else if (
        ['systemProtect', 'trafficProtect'].includes(pageName) &&
        microServiceEnable &&
        !MseAppId &&
        mscVersion !== 'business'
      ) {
        return (
          <div style={{ marginTop: 20, overflow: 'auto' }}>
            <Form>
              <Form.Item
                label={intl('saenext.components.msc.FetchMseUse.MseMicroserviceGovernanceVersion')}
                help={
                  <div style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
                    {intl('saenext.components.msc.FetchMseUse.MseMicroserviceGovernance')}
                    {mseVersionLableMap.get(mscVersion)}
                    {intl('saenext.components.msc.FetchMseUse.Click.1')}
                    <Button
                      text
                      type="primary"
                      style={{ marginLeft: 4, marginRight: 4 }}
                      onClick={() => {
                        const userId =
                          _.get(window, `ALIYUN_CONSOLE_CONFIG.CURRENT_PK`) ||
                          _.get(window, `ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK`);
                        const uri = `${CachedData.confLink('feature:common-buy')}/?commodityCode=${isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'}&orderType=UPGRADE&instanceId=synthetic_post_${userId}`;
                        window.open(uri, '_blank');
                      }}
                    >
                      {intl('saenext.components.msc.FetchMseUse.Upgrade')}
                    </Button>
                    {intl('saenext.components.msc.FetchMseUse.YouCanGoToThe')}
                  </div>
                }
              >
                <Input
                  readOnly
                  value={mseVersionLableMap.get(mscVersion)}
                  style={{ width: 400, marginRight: 10 }}
                />
              </Form.Item>
            </Form>
          </div>
        );
      } else {
        const { history, regionId, applicationId } = props;
        return (
          <AppProMscNotEnable
            history={history}
            regionId={regionId}
            applicationID={applicationId}
            pageName={pageName}
          />
        );
      }
    } else {
      if (MseAppId) {
        return (
          <div className="msc-page">
            {_.includes(['appOverview', 'interfaceDetails', 'nodeDetails'], pageName) && (
              <div className="mb-s" style={{ textAlign: 'right' }}>
                <ToolTipCondition
                  show={!isStartMsc}
                  tip={intl('saenext.components.msc.MscPage.TheCurrentApplicationIsNot')}
                  align="l"
                >
                  <Button type="primary" disabled={!isStartMsc} onClick={handleCloseMsc}>
                    {intl('saenext.components.msc.TrafficGovern.DisableMicroserviceGovernance')}
                  </Button>
                </ToolTipCondition>
              </div>
            )}

            {children && children(mseApp)}
          </div>
        );
      }
      return (
        <FetchMscUse
          pageName={pageName}
          // @ts-ignore
          mscVersion={mscVersion}
          FreeVersion={freeVersion}
          AppId={applicationId}
          rebooting={rebooting}
          isStartMsc={isStartMsc}
          mseAppNameSpace={mseAppNameSpace}
          setRefreshIndex={setRefreshIndex}
          refreshContent={refreshContent}
        />
      );
    }
  };

  return (
    <>
      <Loading visible={isLoading} style={{ width: '100%', height: isLoading ? '100%' : 0 }}>
        {!isLoading && renderContent()}
      </Loading>
    </>
  );
};

export default MscPage;
