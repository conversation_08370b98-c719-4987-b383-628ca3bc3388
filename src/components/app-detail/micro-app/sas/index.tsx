/* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Loading, Button, Icon, Select, Message, Card } from '@ali/cnd';
import { intl } from '@ali/cnd';
import services from "~/services";
import { get, isEmpty, isEqual, map } from 'lodash';
import UnBindPermission from './UnBindPermission';
import WholeBindsas from './WholeBindsas';
import WholeUnBindsas from './WholeUnBindsas';
import HalfWithWpieUnBindsas from './HalfWithWpieUnBindsas';
import HalfWithWarnUnBindsas from './HalfWithWarnUnBindsas';
import CachedData from '~/cache/common';

type Props = {
  appId?: string;
  title?: string;
  regionId?: string;
  className?: string;
};

type RegionItem = {
  label?: string;
  value?: string;
}

const CardProps = {
  showTitleBullet: false,
  showHeadDivider: false,
  contentHeight: 'auto',
};
const REGIONS = [
  { label: intl('saenext.pages.overview.constants.AllRegions'), value: '' },
  ...map(get(window, 'ALIYUN_CONSOLE_CONFIG.REGIONS') || [], (region) => ({
    label: region.name,
    value: region.regionId,
  })),
];

const regions = [...REGIONS.slice(1)];

export default (props: Props) => {
  const { appId = '', regionId: _regionId, className } = props;
  let defaultRegionId = isEmpty(appId) ? regions[0].value : _regionId;
  const [loading, setLoading] = useState(false);
  const [regionId, setRegionId] = useState(defaultRegionId);
  const [regionName, setRegionName] = useState(regions[0].label);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [data, setData] = useState({
    PostPaidModuleSwitch: false,
    IsBind: false,
    IsEnableYunDun: false,
    IsUserLevelYunDunEnabled: false,
    TotalBindAppCount: 0,
    TotalUnBindAppCount: 0,
    EventLevels: { Remind: 0, Serious: 0, Suspicious: 0 },
  });
  const [timeoutYundun, setTimeoutYundun] = useState(false);
  // 缓存region的云安全数据
  const yundunRef = React.useRef(new Map());
  const isRefresh = React.useRef(false);
  const isMainAccount = CachedData.isMainAccount();

  useEffect(() => {
    getYundunResources(regionId);
  }, [refreshIndex]);

  const getYundunResources = async (_regionId) => {
    setLoading(true);
    const __regionId = _regionId || regionId;
    let _data = yundunRef.current.get(__regionId);
    if (!isEmpty(_data) && !isRefresh.current) {
      setData(_data);
      setLoading(false);
      return;
    }
    const params = { RegionId: __regionId, AppId: appId };
    const customErrorHandle = (err, data, callback) => {
      setLoading(false);
      const code = get(err, 'code');
      const mseeage = get(err, 'response.data.message');
      let _timeout = false;
      if (
        code === 'ApiReadTimeOut' &&
        mseeage === intl('saenext.components.sas.TheRequestedApiProcessingTimed')
      ) {
        _timeout = true;
        setTimeoutYundun(_timeout);
        return;
      }
      // callback && callback();
    };
    const res = await services.getEventLevelCount(params, customErrorHandle);
    _data = get(res, 'Data', {});
    yundunRef.current.set(__regionId, _data);
    setData(_data);
    setLoading(false);
  };

  const titleYundun = React.useMemo(() => {
    let title = intl('saenext.components.sas.SecurityCenterNotActivated');
    const permission = get(data, 'PostPaidModuleSwitch', false);
    if (permission) {
      title = intl('saenext.components.sas.NotBoundToSecurityCenter');
      if (isEmpty(appId)) {
        title = intl('saenext.components.sas.MicroserviceApplicationsNotBoundTo');
      }
      const totalBindAppCount = get(data, 'TotalBindAppCount', 0);
      const totalUnBindAppCount = get(data, 'TotalUnBindAppCount', 0);
      // @ts-ignore
      const total = parseInt(totalBindAppCount, 10) + parseInt(totalUnBindAppCount, 10);

      const _isBind =
        (isEmpty(appId) && total && isEqual(get(data, 'TotalUnBindAppCount'), 0)) ||
        (!isEmpty(appId) && get(data, 'IsBind', false));

      if (_isBind) {
        title = intl('saenext.components.sas.NumberOfSecurityAlerts');
      }
    }
    return title;
  }, [data]);

  // 不是主账号不展示
  // 账户灰度不支持云安全中心不展示
  // 应用不支持云安全中心不展示
  if (
    isMainAccount === false ||
    get(data, 'IsUserLevelYunDunEnabled') === false ||
    (appId && get(data, 'IsEnableYunDun') === false)
  )
    return null;

  return (
    <>
      {isEmpty(appId) ? (
        <>
          <div className="yundun w-100">
            <div className="title mb">
              <span>{titleYundun}</span>
              <Select
                size="small"
                className="ml mr-s"
                value={regionId}
                dataSource={regions}
                onChange={(regionId: string, _, region: RegionItem) => {
                  isRefresh.current = false;
                  setRegionName(region?.label);
                  setRegionId(regionId);
                  getYundunResources(regionId);
                }}
              />

              <Button
                size="small"
                className="isOnlyIcon"
                style={{ minWidth: 24 }}
                onClick={() => {
                  isRefresh.current = true;
                  setTimeoutYundun(false);
                  setRefreshIndex(Date.now());
                }}
              >
                <Icon type={loading ? 'loading' : 'refresh'} />
              </Button>
              {!timeoutYundun ? null : (
                <Message type="error" size="medium" className="timeout-message ml-s">
                  {intl('saenext.components.sas.SecurityCenterDataRequestTimed')}
                </Message>
              )}
            </div>
            <div className="divider"></div>
            <Loading visible={loading} className="full-width">
              <UnBindPermission appId={appId} data={data} regionId={regionId} />

              <WholeBindsas appId={appId} data={data} regionId={regionId} />

              <WholeUnBindsas appId={appId} data={data} regionId={regionId} />

              <HalfWithWpieUnBindsas appId={appId} data={data} regionId={regionId} />
            </Loading>
          </div>
          <HalfWithWarnUnBindsas
            loading={loading}
            data={data}
            regionId={regionId}
            appId={appId}
            regionName={regionName}
          />
        </>
      ) : (
        <Card title={titleYundun} {...CardProps} className={className}>
          <Loading visible={loading} className="full-width">
            <UnBindPermission appId={appId} data={data} regionId={regionId} />

            <WholeBindsas appId={appId} data={data} regionId={regionId} />

            <WholeUnBindsas appId={appId} data={data} regionId={regionId} />
          </Loading>
        </Card>
      )}
    </>
  );
};
