import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React from 'react';
import { Button, Icon, Message, LinkButton } from '@ali/cnd';
import { get, isEmpty } from 'lodash';
import CachedData from '../../../../cache/common';

// Serverless资产未开通 只展示开通按钮
export default (props) => {
  const { regionId, appId = '', data = {}, style } = props;
  const permission = get(data, 'PostPaidModuleSwitch', false);
  if (permission) return null;

  let _style = style;
  if (isEmpty(style)) {
    _style = { alignItems: 'center' };
  }
  const _isPre = get(window, 'ALIYUN_CONSOLE_CONFIG.fEnv') === 'pre';

  return (
    <div className="flex" style={_style}>
      <Button
        type="primary"
        className="mr-s"
        onClick={() => {
          const yundun = _isPre
            ? `${CachedData.confLink('feature:yundun-pre:url')}/?p=sas_buy&fromSAE=true&regionId=${regionId}&microFrontVersionName=serverless#/buy/cn-hangzhou`
            : `${CachedData.confLink('feature:yundun:url')}/?p=sas_buy&fromSAE=true&regionId=${regionId}#/buy/cn-hangzhou`;
          window.open(yundun, '_blank');
        }}
      >
        {intl('saenext.components.sas.UnBindPermission.ActivateNow')}
      </Button>
      <Message type="warning">
        <span>
          {intl('saenext.components.sas.UnBindPermission.CurrentYour')}
          {isEmpty(appId)
            ? intl('saenext.components.sas.UnBindPermission.AllApplications')
            : intl('saenext.components.sas.UnBindPermission.Application')}
          {intl('saenext.components.sas.UnBindPermission.TheSecurityProtectionCapabilityHas')}
          <a
            href={`${CachedData.confLink('feature:yundun:url')}/?spm=5176.saenext.0.0.230c6a68IKkUkf&p=sas#/serverless/cn-hangzhou`}
            target="_blank"
          >
            {intl('saenext.components.sas.UnBindPermission.SecurityCenter')}
          </a>
          {intl('saenext.components.sas.UnBindPermission.ForMoreInformationSee')}
        </span>
        <LinkButton
          className="ml-xs"
          onClick={() => {
            window.open(
              CachedData.confLink('help:security-center:billing-overview'),
              '_blank',
            );
          }}
        >
          <span>{intl('saenext.components.sas.UnBindPermission.SecurityCenterBillingRules')}</span>
          <Icon type="external_link" size="xs" className="ml-xs" />
        </LinkButton>
      </Message>
    </div>
  );
};
