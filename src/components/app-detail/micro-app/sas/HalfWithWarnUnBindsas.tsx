import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React from 'react';
import { Loading } from '@ali/cnd';
import { get, isEmpty } from 'lodash';
import WholeBindsas from './WholeBindsas';

// 部分接入 展示安全告警数
export default (props) => {
  const { regionId, appId = '', loading, data = {}, regionName } = props;
  const showWarn =
    get(data, 'PostPaidModuleSwitch') &&
    get(data, 'TotalUnBindAppCount', 0) &&
    get(data, 'TotalBindAppCount', 0);

  if (!showWarn || !isEmpty(appId)) return null;

  return (
    <div className="yundun w-100">
      <Loading visible={loading} className="full-width">
        <div className="title mb">
          <span>
            {intl(
              'saenext.components.sas.HalfWithWarnUnBindsas.MicroserviceApplicationsBoundToSecurity',
            )}
          </span>
          <div className="display ml">{regionName}</div>
        </div>
        <div className="divider"></div>
        <div className="flex">
          <span style={{ marginRight: 16 }}>
            {intl('saenext.components.sas.HalfWithWarnUnBindsas.NumberOfSecurityAlerts')}
          </span>
          <WholeBindsas regionId={regionId} showWarn={true} data={data} />
        </div>
      </Loading>
    </div>
  );
};
