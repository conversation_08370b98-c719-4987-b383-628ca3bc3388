import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React from 'react';
import { Button, Icon, Message, LinkButton } from '@ali/cnd';
import { get, isEmpty, isEqual } from 'lodash';
import CachedData from '../../../../cache/common';

// 全部未接入
export default (props) => {
  const { regionId, data = {}, appId = '', style, type = 'warning', showInsert = false } = props;
  const _showInsert =
    (isEmpty(appId) &&
      get(data, 'PostPaidModuleSwitch') &&
      isEqual(get(data, 'TotalBindAppCount'), 0)) ||
    showInsert ||
    (!isEmpty(appId) && !get(data, 'IsBind', false) && get(data, 'PostPaidModuleSwitch'));

  if (!_showInsert) return null;

  let _style = style;
  if (isEmpty(style)) {
    _style = { alignItems: 'center' };
  }
  const _isPre = get(window, 'ALIYUN_CONSOLE_CONFIG.fEnv') === 'pre';

  return (
    <div className="flex" style={_style}>
      <Button
        type="primary"
        className="mb-s mr-s"
        style={{ minWidth: 116, marginBottom: isEmpty(style) ? 0 : 8 }}
        onClick={() => {
          const suffixPath = isEmpty(appId) ? '' : `&appId=${appId}`;
          const yundun = _isPre
            ? `${CachedData.confLink('feature:yundun-pre:url')}/?p=sas&fromSAE=true&regionId=${regionId}${suffixPath}&microFrontVersionName=serverless#/serverless/cn-hangzhou`
            : `${CachedData.confLink('feature:yundun:url')}/?p=sas&fromSAE=true&regionId=${regionId}${suffixPath}#/serverless/cn-hangzhou`;
          window.open(yundun, '_blank');
        }}
      >
        {isEmpty(appId)
          ? intl('saenext.components.sas.WholeUnBindsas.CustomOnDemandBinding')
          : intl('saenext.components.sas.WholeUnBindsas.AccessSecurityProtection')}
      </Button>
      <Message type={type}>
        <span>
          {intl('saenext.components.sas.WholeUnBindsas.CurrentYour')}
          {isEmpty(appId)
            ? type === 'warning'
              ? intl('saenext.components.sas.WholeUnBindsas.All')
              : intl('saenext.components.sas.WholeUnBindsas.Part')
            : ''}
          {intl('saenext.components.sas.WholeUnBindsas.TheApplicationHasNotBeen')}
          <a
            href={`${CachedData.confLink('feature:yundun:url')}/?spm=5176.saenext.0.0.230c6a68IKkUkf&p=sas#/serverless/cn-hangzhou`}
            target="_blank"
          >
            {intl('saenext.components.sas.WholeUnBindsas.SecurityCenter')}
          </a>
          {intl('saenext.components.sas.WholeUnBindsas.ForMoreInformationSee')}
        </span>
        <LinkButton
          className="ml-xs"
          onClick={() => {
            window.open(
              CachedData.confLink('help:security-center:billing-overview'),
              '_blank',
            );
          }}
        >
          <span>{intl('saenext.components.sas.WholeUnBindsas.SecurityCenterBillingRules')}</span>
          <Icon type="external_link" size="xs" className="ml-xs" />
        </LinkButton>
      </Message>
    </div>
  );
};
