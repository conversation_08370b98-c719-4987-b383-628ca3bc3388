import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React from 'react';
import { get, isEmpty } from 'lodash';
import WholeUnBindsas from './WholeUnBindsas';
import { Wpie, Wnumber, COLORS } from '@alife/aisc-widgets';

// 部分接入 展示绑定按钮和占比
export default (props) => {
  const { regionId, appId = '', data = {} } = props;
  const showWpie =
    get(data, 'PostPaidModuleSwitch') &&
    get(data, 'TotalUnBindAppCount', 0) &&
    get(data, 'TotalBindAppCount', 0);

  if (!showWpie || !isEmpty(appId)) return null;
  const totalBindAppCount = get(data, 'TotalBindAppCount', 0);
  const totalUnBindAppCount = get(data, 'TotalUnBindAppCount', 0);
  const total = parseInt(totalBindAppCount, 10) + parseInt(totalUnBindAppCount, 10);

  return (
    <>
      <div className="flex">
        <div style={{ width: 300 }}>
          <Wpie
            height="120"
            config={{
              cycle: true,
              innerRadius: 0.9,
              outerRadius: 1,
              legend: {
                visible: true,
                position: 'bottom',
                align: 'center',
                showData: true,
                marker: {
                  symbol: 'hyphen',
                  style(oldStyle) {
                    return {
                      ...oldStyle,
                      r: 3,
                      lineWidth: 6,
                      fill: oldStyle.stroke,
                      lineAppendWidth: 0,
                    };
                  },
                },
                nameFormatter: (name) => {
                  const count =
                    name === intl('saenext.components.sas.HalfWithWpieUnBindsas.Unbound')
                      ? totalUnBindAppCount
                      : totalBindAppCount;
                  return `${name} ${count}`;
                },
              },
              tooltip: {
                nameFormatter: function (v) {
                  const name =
                    v === intl('saenext.components.sas.HalfWithWpieUnBindsas.Unbound')
                      ? intl(
                          'saenext.components.sas.HalfWithWpieUnBindsas.MicroserviceApplicationsNotBoundTo',
                        )
                      : intl(
                          'saenext.components.sas.HalfWithWpieUnBindsas.MicroserviceApplicationsBoundToSecurity',
                        );
                  return name;
                },
              },
              // @ts-ignore
              colors: [COLORS.widgetsColorGray, COLORS.widgetsColorPurple],
            }}
            data={{
              name: intl(
                'saenext.components.sas.HalfWithWpieUnBindsas.PercentageOfCloudSecurityCenter',
              ),
              data: [
                [intl('saenext.components.sas.HalfWithWpieUnBindsas.Unbound'), totalUnBindAppCount],
                [intl('saenext.components.sas.HalfWithWpieUnBindsas.Bound'), totalBindAppCount],
              ],
            }}
          >
            <Wnumber
              bottomTitle={intl('saenext.components.sas.HalfWithWpieUnBindsas.TotalApplication')}
              style={{ fontSize: 24 }}
              unit={intl('general.unit.count')}
            >
              {total}
            </Wnumber>
          </Wpie>
        </div>
        <div style={{ flex: 1 }}>
          <WholeUnBindsas
            type="notice"
            showInsert={true}
            regionId={regionId}
            appId={appId}
            style={{ flexDirection: 'column', alignItems: 'flex-start' }}
          />
        </div>
      </div>
    </>
  );
};
