import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React from 'react';
import { Tag } from '@ali/cnd';
import { get, isEqual, isEmpty } from 'lodash';
import CachedData from '../../../../cache/common';

// 全部接入 只展示安全告警数
export default (props) => {
  const { regionId, appId = '', data = {}, showWarn = false } = props;
  const _showWarn =
    showWarn ||
    (isEmpty(appId) &&
      get(data, 'PostPaidModuleSwitch') &&
      get(data, 'TotalBindAppCount', 0) &&
      isEqual(get(data, 'TotalUnBindAppCount'), 0)) ||
    (!isEmpty(appId) && get(data, 'IsBind', false) && get(data, 'PostPaidModuleSwitch'));

  if (!_showWarn) return null;
  const eventLevels = get(data, 'EventLevels', { Remind: 0, Serious: 0, Suspicious: 0 });
  const _isPre = get(window, 'ALIYUN_CONSOLE_CONFIG.fEnv') === 'pre';

  const toYundunCenter = () => {
    const yundun = _isPre
      ? `${CachedData.confLink('feature:yundun-pre:url')}/?p=sas&microFrontVersionName=serverless#/securityAlarm/container/cn-hangzhou`
      : `${CachedData.confLink('feature:yundun:url')}/?p=sas#/securityAlarm/container/cn-hangzhou`;
    window.open(yundun, '_blank');
  };

  return (
    <div className="flex">
      <Tag color="red" className="mr-s" onClick={toYundunCenter}>
        <span>
          {intl('saenext.components.sas.WholeBindsas.Urgent')}
          {get(eventLevels, 'Serious', 0)}
        </span>
      </Tag>
      <Tag color="yellow" className="mr-s" onClick={toYundunCenter}>
        <span>
          {intl('saenext.components.sas.WholeBindsas.Suspicious')}
          {get(eventLevels, 'Suspicious', 0)}
        </span>
      </Tag>
      <Tag style={{ background: '#f5f5f5', color: '#808080' }} onClick={toYundunCenter}>
        <span>
          {intl('saenext.components.sas.WholeBindsas.Reminder')}
          {get(eventLevels, 'Remind', 0)}
        </span>
      </Tag>
    </div>
  );
};
