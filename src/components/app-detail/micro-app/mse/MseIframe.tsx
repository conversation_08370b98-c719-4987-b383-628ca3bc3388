import React from 'react';
import { Loading } from "@ali/cnd";
import CachedData from '../../../../cache/common';

type Props = {
  pageName: string;
  query?: string;
  regionId: string;
  mseApplicationId: string;
  mseApplicationName: string;
};

type State = {
  loading: boolean;
};

const ConsoleNavHeight = 50;
const BreadcrumbHeight = 48;
const AppPageNavHeight = 56;
const ReservedSpaceHeight = 16;

class AhasIframe extends React.Component<Props, State> {
  private height: number;
  private iframeRef = React.createRef() as any;

  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };

    // 默认高度
    this.height =  (
      document.documentElement.clientHeight -
      ConsoleNavHeight -
      BreadcrumbHeight -
      AppPageNavHeight -
      ReservedSpaceHeight
    );
  }

  componentDidMount(): void {
    this.setState({
      loading: true,
    });
    window.addEventListener("message", this.onMessage);
  }

  onMessage = (e) => {
    const { type, payload } = e.data || {};
    if (type === 'iframeHeight') {
      const _height = parseInt(payload, 10);
      const iframeHeight = Math.max(this.height, _height);
      // @ts-ignore
      this.iframeRef.style.height = `${iframeHeight}px`;
    }
    if (type === 'iframeLoaded') {
      this.setState({
        loading: false,
      });
    }
  };

  // iframe自带的onload做兜底
  iframeLoaded = () => {
    this.setState({loading: false});
  }


  getIframeSrc = () => {
      const { pageName, query = '', regionId, mseApplicationId, mseApplicationName } = this.props;
      const _query = query ? `&${query}` : '';
      return `${CachedData.confLink('feature:mse:url')}#/${pageName}?AppId=${mseApplicationId}&region=${regionId}&appName=${mseApplicationName}&ns=sentinel&hideTopbar=true&hideNavbar=true${_query}`;
  }

  componentWillUnmount(): void {
    window.removeEventListener("message", this.onMessage);
  }

  render() {
    const { loading } = this.state;
    return (
      <Loading
        visible={loading}
        style={{ width: "100%", minHeight: this.height, position: "relative" }}
      >
        <iframe
          width="100%"
          height={this.height}
          scrolling="no"
          frameBorder={0}
          onLoad={this.iframeLoaded}
          src={this.getIframeSrc()}
          ref={(ref) => this.iframeRef = ref}
        />
      </Loading>
    );
  }
}
export default AhasIframe;
