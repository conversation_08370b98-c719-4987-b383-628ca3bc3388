import React from 'react';
import MsePage from './MsePage';

type Props = {
  appId: string;
  regionId: string;
  appConfig: Record<string, any>;
};


const InterfaceDetails = (props: Props) => {
  const { appId, regionId, appConfig } = props;

  return (
    <MsePage
      appConfig={appConfig}
      regionId={regionId}
      pageName="msc/appList/info/systemGuardApiDetails"
    />
  );
};

export default InterfaceDetails;