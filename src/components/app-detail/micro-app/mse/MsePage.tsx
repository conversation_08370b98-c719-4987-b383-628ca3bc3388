import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import MseIframe from './MseIframe';
import FetchMseUse from './FetchMseUse';

const MsePage = (props) => {
  const { appConfig, regionId, pageName, query = '' } = props;
  const mseApplicationId = get(appConfig, 'MseApplicationId', '');
  const mseApplicationName = get(appConfig, 'MseApplicationName', '');

  const [enableAhas, setEnableAhas] = useState(false);

  useEffect(() => {
    const _enableAhas = get(appConfig, 'EnableAhas', false);
    setEnableAhas(_enableAhas);
  }, [appConfig])

  return (
    <>
      {
        enableAhas ? (
          <MseIframe
            pageName={pageName}
            query={query}
            regionId={regionId}
            mseApplicationId={mseApplicationId}
            mseApplicationName={mseApplicationName}
          />
        ) : (
          <FetchMseUse />
        )
      }
    </>
  );
};

export default MsePage;
