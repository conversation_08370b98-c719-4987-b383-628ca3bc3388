import { intl } from '@ali/cnd';
import React from 'react';
import { Message } from '@ali/cnd';
import CachedData from '~/cache/common';

const FetchAhasUse = (props) => {
  return (
    <>
      <Message type="notice">
        {intl('saenext.components.mse.FetchMseUse.TheCurrentApplicationDoesNot')}
        <a
          href={`${CachedData.confLink('help:mse:billing-overview')}?spm=a2c4g.11186623.0.0.653add7eubDvJ7`}
          target="_blank"
        >
          {intl('saenext.components.mse.FetchMseUse.MseServiceGovernanceProductPricing')}
        </a>{' '}
        {intl("saenext.common.full-stop")}
      </Message>
      <div style={{ height: 370, position: 'relative' }}>
        <img
          style={{
            width: '100%',
            height: '100%',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'top',
            backgroundSize: 'cover',
            objectFit: 'cover',
          }}
          src="https://img.alicdn.com/imgextra/i4/O1CN01gRoOLZ22qpIm2endI_!!6000000007172-1-tps-3840-740.gif"
        />

        <div
          style={{ position: 'absolute', top: 32, left: 0, background: 'transparent', zIndex: 1 }}
        />
      </div>
    </>
  );
};

export default FetchAhasUse;
