import React from 'react';
import MsePage from './MsePage';

type Props = {
  appId: string;
  regionId: string;
  appConfig: Record<string, any>;
};


const RulesConfigure = (props: Props) => {
  const { appId, regionId, appConfig } = props;

  return (
    <MsePage
      appConfig={appConfig}
      regionId={regionId}
      pageName="msc/appList/info/flowGovernment"
      query='hideTabbar=true&type=flowProtection'
    />
  );
};

export default RulesConfigure;