import { intl } from '@ali/cnd';

import React, { useState, useEffect } from 'react';
import ArmsIframe from '~/components/shared/ArmsIframe';
import services from "~/services";
import { get, isEqual } from 'lodash';
import { getArmsDomain } from '~/utils/global';
import CachedData from '~/cache/common';

type Props = {
  history: any;
  appId: string;
  regionId: string;
  appConfig: Record<string, any>;
};

const AlarmRules = (props: Props) => {
  const title = intl('saenext.components.alarm.AlarmRules.ApplyMonitoringAlertRules');
  const { regionId, appId, appConfig, history } = props;
  const [clusterId, setClusterId] = useState('');

  useEffect(() => {
    getOpenVCluster();
    window.addEventListener('message', onMessage);
    return () => {
      window.removeEventListener('message', onMessage);
    };
  }, []);

  const getOpenVCluster = async () => {
    const res = await services.getOpenVCluster({
      params: {
        RegionId: regionId,
        ClusterType: 'cloud-product-prometheus',
        Product: 'sae',
      },
      customErrorHandle: (error, data, callback) => {
        callback && callback();
      },
    });
    const _clusterId = get(res, 'Data', '');
    setClusterId(_clusterId);
  };

  const onMessage = ({ data }) => {
    const { form, type } = data;
    if (form !== 'ARMS2') return;
    switch (type) {
      case 'alarmHistory':
        // 告警发送历史
        history.push(
          `/${regionId}/app-list/${appId}/micro-app/alarm-dispatch?name=${appConfig.AppName}`,
        );
        break;
      case 'alarmDispatch':
        // 通知策略
        history.push(
          `/${regionId}/app-list/${appId}/micro-app/alarm-notify?name=${appConfig.AppName}`,
        );
        break;
      case 'alarmContact':
        // 联系人管理
        history.push(
          `/${regionId}/app-list/${appId}/micro-app/alarm-contact?name=${appConfig.AppName}`,
        );
      default:
        break;
    }
  };

  const isPythonOrOther = React.useMemo(() => {
    const isPython = isEqual(get(appConfig, 'ProgrammingLanguage'), 'python');
    const isOther = isEqual(get(appConfig, 'ProgrammingLanguage'), 'other');
    return isPython || isOther;
  }, [appConfig]);

  const appIdText = intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationId');

  const equalText = intl('saenext.components.route-create.MseForwardform.Equal');

  return (
    <>
      {!isPythonOrOther ? (
        <ArmsIframe
          regionId={regionId}
          applicationId={appId}
          url={`${getArmsDomain(
            regionId,
          )}/?iframeMode=edas&iframeModeApp=sae&regionId=${regionId}&pid=${appId}#/tracing/alarm/rule/list/${regionId}?tracing`}
        />
      ) : null}
      {isPythonOrOther && clusterId ? (
        <ArmsIframe
          regionId={regionId}
          applicationId={appId}
          url={`${CachedData.confLink('feature:arms:url')}/?newPage=false&iframeMode=true&productCode=sae&alertType=0&previewPromQL=false&customCluster={"showCluster":false,"value":"${clusterId}","content":[{"value":"${clusterId}","label":"sae"}]}&customFilter=[{"showFilter":false,"showInput":false,"filterLabel":"appId","filterDimName":"${appIdText}","filterDim":"appId","value":"${appId}","valueType":"string","opt":"=","supportOpts":[{"value":"=","label":"${equalText}"}],"values":[{"value":"${appId}","label":"${appId}"}],"type":"number","staticValue":true}]&listTitle=${title}&alertTitle=${title}#/prom/alert/${regionId}`}
        />
      ) : null}
    </>
  );
};

export default AlarmRules;
