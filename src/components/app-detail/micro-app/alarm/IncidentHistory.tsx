// 告警事件历史

import React from 'react';
import ArmsIframe from '~/components/shared/ArmsIframe';
import CachedData from '~/cache/common';

type Props = {
  appId: string;
  regionId: string;
};


const IncidentHistory = (props: Props) => {
  const { regionId, appId } = props;

  return (
    <ArmsIframe
      regionId={regionId}
      applicationId={appId}
      url={`${CachedData.confLink('feature:arms:url')}/index?edasiframeMode=sae&regionId=${regionId}&pid=${appId}#/alarm/incident`}
    />
  );
};

export default IncidentHistory;
