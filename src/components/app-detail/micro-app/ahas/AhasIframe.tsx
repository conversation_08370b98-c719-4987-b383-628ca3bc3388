import React from 'react';
import { Loading } from "@ali/cnd";
import { get } from 'lodash';
import CachedData from '~/cache/common';

type Props = {
  pageName: string;
  regionId: string;
  applicationName: string;
  applicationId: string;
};

type State = {
  loading: boolean;
};

const ConsoleNavHeight = 50;
const BreadcrumbHeight = 48;
const AppPageNavHeight = 56;
const ReservedSpaceHeight = 16;

class AhasIframe extends React.Component<Props, State> {
  private height: number;
  private iframeRef = React.createRef() as any;

  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };

    // 默认高度
    this.height =  (
      document.documentElement.clientHeight -
      ConsoleNavHeight -
      BreadcrumbHeight -
      AppPageNavHeight -
      ReservedSpaceHeight
    );
  }

  componentDidMount(): void {
    this.setState({
      loading: true,
    });
    window.addEventListener("message", this.onMessage);
  }

  onMessage = (e) => {
    const { type, payload } = e.data || {};
    if (type === 'iframeHeight') {
      const _height = parseInt(payload, 10);
      const iframeHeight = Math.max(this.height, _height);
      // @ts-ignore
      this.iframeRef.style.height = `${iframeHeight}px`;
    }
    if (type === 'iframeLoaded') {
      this.setState({
        loading: false,
      });
    }
  };

  // iframe自带的onload做兜底
  iframeLoaded = () => {
    this.setState({loading: false});
  }


  getIframeSrc = () => {
    const { pageName, regionId, applicationId, applicationName } = this.props;
    const channel = get(window, 'ALIYUN_CONSOLE_CONFIG.CHANNEL', '');
    const _baseUrl = CachedData.confLink('feature:ahas:url');
    const baseUrl = `${_baseUrl}/flowProtection/systemGuard/`;
    return `${baseUrl}${pageName}?hideNarutoNav=true&hideTopbar=true&hideSidebar=true&ahasAppName=${applicationName}&appName=${applicationId}&ns=default&region=${regionId}&saeAppName=${applicationName}`;
  }

  componentWillUnmount(): void {
    window.removeEventListener("message", this.onMessage);
  }

  render() {
    const { loading } = this.state;
    return (
      <Loading
        visible={loading}
        style={{ width: "100%", minHeight: this.height, position: "relative" }}
      >
        <iframe
          width="100%"
          height={this.height}
          scrolling="no"
          frameBorder={0}
          onLoad={this.iframeLoaded}
          src={this.getIframeSrc()}
          ref={(ref) => this.iframeRef = ref}
        />
      </Loading>
    );
  }
}
export default AhasIframe;
