import { intl } from '@ali/cnd';
import React from 'react';
import { Message } from '@ali/cnd';

const FetchAhasUse = (props) => {
  return (
    <>
      <Message type="notice">
        {intl('saenext.components.ahas.FetchAhasUse.TheCurrentApplicationDoesNot')}
        <a
          href="https://www.aliyun.com/price/product?spm=a2c4g.11186623.2.7.656179edoWMs1X#/ahas/detail"
          target="_blank"
        >
          {intl('saenext.components.ahas.FetchAhasUse.AhasPricing')}
        </a>{' '}
        {intl("saenext.common.full-stop")}
      </Message>
      <div style={{ height: 370, position: 'relative' }}>
        <img
          style={{
            width: '100%',
            height: '100%',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'top',
            backgroundSize: 'cover',
            objectFit: 'cover',
          }}
          src="https://img.alicdn.com/imgextra/i4/O1CN01oh1U621FLIr6lGEtA_!!6000000000470-1-tps-3840-740.gif"
        />

        <div
          style={{ position: 'absolute', top: 32, left: 0, background: 'transparent', zIndex: 1 }}
        />
      </div>
    </>
  );
};

export default FetchAhasUse;
