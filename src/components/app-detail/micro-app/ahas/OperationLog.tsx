import React from 'react';
import AhasPage from './AhasPage';

type Props = {
  appId: string;
  regionId: string;
  appConfig: Record<string, any>;
};


const OperationLog = (props: Props) => {
  const { appId, regionId, appConfig } = props;

  return (
    <AhasPage
      appConfig={appConfig}
      appId={appId}
      regionId={regionId}
      pageName="operationLog"
    />
  );
};

export default OperationLog;