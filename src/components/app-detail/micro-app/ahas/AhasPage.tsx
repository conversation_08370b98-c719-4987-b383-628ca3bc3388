import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import AhasIframe from './AhasIframe';
import FetchAhasUse from './FetchAhasUse';

const AhasPage = (props) => {
  const { appConfig, regionId, appId, pageName } = props;
  const appName = get(appConfig, 'AppName', '');

  const [enableAhas, setEnableAhas] = useState(false);

  useEffect(() => {
    const _enableAhas = get(appConfig, 'EnableAhas', false);
    setEnableAhas(_enableAhas);
  }, [appConfig])

  return (
    <>
      {
        enableAhas ? (
          <AhasIframe
            pageName={pageName}
            regionId={regionId}
            applicationId={appId}
            applicationName={appName}
          />
        ) : (
          <FetchAhasUse />
        )
      }
    </>
  );
};

export default AhasPage;
