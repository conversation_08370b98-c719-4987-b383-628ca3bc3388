import React from 'react';
import AhasPage from './AhasPage';

type Props = {
  appId: string;
  regionId: string;
  appConfig: Record<string, any>;
};


const MachineMonitor = (props: Props) => {
  const { appId, regionId, appConfig } = props;

  return (
    <AhasPage
      appConfig={appConfig}
      appId={appId}
      regionId={regionId}
      pageName="systemGuardMachineDetails"
    />
  );
};

export default MachineMonitor;