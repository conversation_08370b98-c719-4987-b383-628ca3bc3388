import { intl } from '@ali/cnd';
import { Button, Dialog, Loading, Message, SlidePanel, ToolTipCondition } from '@ali/cnd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import If from '~/components/shared/If';
import DeployForm from './DeployForm';
import { getParams, removeParams, setSearchParams } from '~/utils/global';
import './index.less';
import { isEmpty } from 'lodash';
import services from '~/services';
import ExternalLink from '~/components/shared/ExternalLink';
import { ConfigProvider } from '@alifd/next';
import { PREVIEW_CONFIG } from '~/constants/previewConfig';
import MicroAppContext from '~/utils/microAppContext';
import CachedData from '~/cache/common';
import { AES_CONSTANT, trackMicroAppMsc } from '~/tracker';

const DeploySlide = (props) => {
  const { title, isAppStopped, isPreview, appConfig, onClose, children, appStatus, appAddonReleaseInfo={} } = props;

  const actionParam = getParams('action');
  const defaultOpen =
    (!isPreview && actionParam === 'deploy') || (isPreview && actionParam === 'preview');
  const [slideVisible, setSlideVisible] = useState(defaultOpen);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [slideFooter, setSlideFooter] = useState(null);
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const { LastChangeOrderRunning } = appStatus || {};

  const { setType } = useContext(MicroAppContext);

  useEffect(() => {
    setType(actionParam === 'deploy' ? 'deploy' : 'preview');
  }, [actionParam]);

  useEffect(() => {
    if (LastChangeOrderRunning || isInDebt) {
      setSlideFooter(
        (() => {
          return (
            <>
              <ToolTipCondition
                show
                tip={intl('saenext.basic-info.DeploySlide.TheApplicationIsChangingPlease')}
                align="t"
              >
                <Button type="primary" disabled>
                  {intl('button.ok')}
                </Button>
              </ToolTipCondition>
              <Button style={{ marginLeft: 8 }} onClick={toggleVisible}>
                {intl('button.cancel')}
              </Button>
            </>
          );
        })(),
      );
    }
  }, [LastChangeOrderRunning, isInDebt]);

  const formRef = useRef(null);

  const toggleVisible = () => {
    if (!slideVisible) {
      !isPreview && setSearchParams({ action: 'deploy' });
      onClose?.();
    } else {
      removeParams('action');
    }
    setSlideVisible(!slideVisible);
  };

  const beforeSubmit = (onOk) => {
    if (!isAppStopped) {
      onOk();
      return;
    }

    Dialog.show({
      title: intl('saenext.basic-info.DeploySlide.ModifyApplicationConfiguration'),
      content: intl('saenext.basic-info.DeploySlide.BecauseYourCurrentNumberOf'),
      footerActions: ['ok'],
      okProps: {
        children: intl('saenext.basic-info.DeploySlide.ConfirmIAlreadyKnow'),
      },
      onOk: onOk,
    });
  };

  const onSubmit = async () => {
    setSubmitLoading(true);

    const values = await formRef.current?.validateForm();

    if (!values) {
      setSubmitLoading(false);
      return;
    }

    const { Data: { AppId, ChangeOrderId, IsNeedApproval } = {} as any } =
      (await services.deployMicroApplication({
        params: values,
      })) || {};

    setSubmitLoading(false);

    if (!AppId) {
      Message.error(intl('saenext.basic-info.DeploySlide.DeploymentFailed'));
      return;
    }

    let originReadiness = appConfig?.Readiness || '';
    let currentReadiness = values?.Readiness || '';
    if (originReadiness) {
      const originPath = JSON.parse(originReadiness)?.httpGet?.path;
      const originPort = JSON.parse(originReadiness)?.httpGet?.port;
      const curPath = JSON.parse(currentReadiness)?.httpGet?.path;
      const curPort = JSON.parse(currentReadiness)?.httpGet?.port;
      // 应用配了/health 54199 又修改了 进行埋点
      if (
        originPath === '/health' &&
        originPort === 54199 &&
        (curPath !== '/health' || curPort !== 54199)
      ) {
        trackMicroAppMsc({
          behavior: 'VIEW',
          stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
          appId: appConfig?.AppId,
          section: 'base',
          trackType: 'HEALTH_READINESS',
        });
      }
    }

    toggleVisible();

    if (IsNeedApproval) {
      Dialog.confirm({
        title: intl('saenext.basic-info.DeploySlide.ApplicationDeployment'),
        footerActions: ['ok'],
        content: (
          <Message type="notice">
            {intl('saenext.basic-info.DeploySlide.YourApplicationForDeploymentHas')}
            <br />
            {intl('saenext.basic-info.DeploySlide.YouCanGoToPermission')}
            <span className="bold">{intl('saenext.basic-info.DeploySlide.IInitiatedIt')}</span>
            {intl('saenext.basic-info.DeploySlide.ViewTheApprovalProgress')}
            <ExternalLink url={'/operations-management/approval?type=approval-records&activeTab=mine'} />

            <br />
            {intl('saenext.basic-info.DeploySlide.YouCanAlsoGo')}
            <span className="bold">{intl('saenext.basic-info.DeploySlide.ContactManagement')}</span>
            {intl('saenext.basic-info.DeploySlide.SetYourContactInformationAfter')}
            <ExternalLink url={'/operations-management/approval?type=concats'} />
          </Message>
        ),
      });
    } else {
      Message.success(intl('saenext.basic-info.DeploySlide.SuccessfullyDeployed'));
    }
  };

  return (
    <>
      <AddPropsWrap onClick={toggleVisible}>{children}</AddPropsWrap>
      <SlidePanel
        title={title}
        isShowing={slideVisible}
        width={1200}
        isProcessing={submitLoading}
        onMaskClick={toggleVisible}
        onClose={toggleVisible}
        onCancel={toggleVisible}
        onOk={() => beforeSubmit(onSubmit)}
        okProps={{
          className: isPreview ? 'none' : '',
        }}
        customFooter={slideFooter}
        cancelProps={{
          children: intl('saenext.basic-info.DeploySlide.Close'),
        }}
      >
        <If condition={slideVisible}>
          <Loading visible={isEmpty(appConfig)} className="full-width full-height">
            <If condition={!isEmpty(appConfig)}>
              {/* @ts-ignore */}
              <ConfigProvider defaultPropsConfig={isPreview ? PREVIEW_CONFIG : {}}>
                <DeployForm
                  ref={formRef}
                  appConfig={appConfig}
                  isPreview={isPreview}
                  isAppStopped={isAppStopped}
                  appAddonReleaseInfo={appAddonReleaseInfo}
                />
              </ConfigProvider>
            </If>
          </Loading>
        </If>
      </SlidePanel>
    </>
  );
};

export default DeploySlide;
