import React, {
  forwardRef,
  memo,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from 'react';
import { Field, Form, Input, intl, Loading, Radio, Button } from '@ali/cnd';
import {
  DEPLOY_TYPE,
  LANGUAGE_TYPE,
  LANGUAGE_NAME,
} from '~/components/shared/DeploySelectorField/constant';
import { PACKAGE_TYPE } from '~/constants/application';
import FeatureContext from '~/utils/featureContext';

import If from '~/components/shared/If';
import DeploySelectorField from '~/components/shared/DeploySelectorField';
import CollapseField from '~/components/shared/CollapseField';
import VariableField from '~/components/shared/VariableField/VariableField';
import HealthCheckField from '~/components/shared/HealthCheckField/micro-app';
import SlsSelectorField from '~/components/shared/SlsSelectorField';
import KafkaSelector<PERSON>ield from '~/components/shared/KafkaSelectorField';
import NasSelector<PERSON>ield from '~/components/shared/NasSelectorField/micro-app';
import OssSelector<PERSON>ield from '~/components/shared/OssSelectorField/micro-app';
import ConfigManageField from '~/components/shared/ConfigManageField/index_back';
import SecretField from '~/components/shared/SecretField';
import CpuBurstField from '~/components/shared/CpuBurstField';
import EbpfMonitorConfig from '~/components/shared/EbpfMonitorConfig';
import PhpArmsMonitorField from '~/components/shared/PhpArmsMonitorField';
import ArmsMonitorNew from '~/components/shared/ArmsMonitorNew';
import PrometheusMonitorField from '~/components/shared/PrometheusMonitorField';
import JavaPackageStartCmd from '~/components/shared/JavaPackageStartCmd';
import { isPhpHighVersion, isTypeImage } from '~/utils/global';
import NacosRegistrationField from '~/components/shared/NacosRegistrationField';
import K8sRegistrationField from '~/components/shared/K8sRegistrationField';
import SwimLaneK8sRegistrationField from '~/components/shared/SwimLaneK8sRegistrationField';
import NetworkAccessField from '~/components/shared/NetworkAccessField';
import CustomHostField from '~/components/shared/CustomHostField';
import DeployStrategy from './DeployStrategy';
import AhasRateLimit from '~/components/shared/AhasRateLimit';
import MseRateLimit from '~/components/shared/MseRateLimit';
import OidcRole from '~/components/shared/OidcRole';
import GrayTags from '~/components/shared/GrayTags';
import TextWithBalloon from '~/components/shared/TextWithBalloon';

import { formatValue, parseValue } from './utils';
import { getParams } from '~/utils/global';
import { get, isEmpty, isEqual, has, size, includes, cloneDeep } from 'lodash';
import { domecsticRegionFilter } from '~/utils/featureFilter';
import C from '~/constants/common';
import MicroAppContext from '~/utils/microAppContext';
import Sidecar from '~/components/shared/Sidecar';
import PackageSkipBuild from './PackageSkipBuild';
import { jsonStringify } from '~/utils/transfer-data';
import LifeCycle from '~/components/shared/LifeCycle';
import CommandArgsForm from '~/components/shared/CommandArgsForm';
import MicroServceConfigField from '~/components/shared/MicroServiceConfigField';
import CachedData from '~/cache/common';
import { confFeature } from '@alicloud/console-one-conf';

const DeployForm = (props, ref) => {
  const {
    appConfig: initAppConfig = {},
    isPreview,
    isAppStopped,
    enableCustomNasMountOptions = false,
    agentDownloadUrl = '',
    appAddonReleaseInfo = {},
    hasCmsPermission = true,
  } = props;

  // 保存初始值，避免受 props.appConfig 变化改变值
  const [appConfig, setAppConfig] = useState(initAppConfig);
  const [clearReadinessConfirm, setClearReadinessConfirm] = useState(false);

  const {
    appConfig: { AssociateEip = false, PackageType },
    scaleRuleEnabled,
  } = useContext(MicroAppContext);

  const variableRef = useRef(null);
  const k8sRegistRef = useRef(null);
  const swimlaneK8sRegistRef = useRef(null);
  const livenessRef = useRef(null);
  const readinessRef = useRef(null);
  const startupRef = useRef(null);
  const slsRef = useRef(null);
  const kafkaRef = useRef(null);
  const nasRef = useRef(null);
  const ossRef = useRef(null);
  const configRef = useRef(null);
  const secretRef = useRef(null);
  const microserviceConfigRef = useRef(null);
  const prometheusMonitorRef = useRef(null);

  const field = Field.useField({
    values: {},
    parseName: true,
  });

  const { getValues, setValues, getNames } = field;

  const {
    AppName: appName,
    RegionId: regionId,
    NamespaceId: namespaceId,
    VpcId: vpcId,
    Cpu: cpu,
    ProgrammingLanguage: packageLanguage,
    PackageType: packageType,
    SaeVersion: saeVersion,
    ServiceTags: serviceTags,
    BaseAppId,
    MseApplicationId,
    NewSaeVersion = '',
  } = appConfig;
  const { feature, inDebtData } = useContext(FeatureContext);
  const {
    startCmd,
    EnableNewArms,
    ebpf = false,
    php_ebpf = false,
    microRegister = false,
    pvtzDiscovery = false,
    ahas_migrate = false,
    cpuBurst = false,
    oidc = false,
    sidecar = false,
    allowSkipBuildImage = false,
    enableSaeStdVersionNewMse,
    allowUpdateAppPackageType,
  } = feature;

  const {
    deployConfig: { Php: phpVersion, Jdk: jdk, PackageUrl } = {} as any,
    startCmd: { Command = '/bin/sh' } = {},
    EnableAhas = false,
  } = getValues() as any;

  const supportChangePackageType_language = [
    LANGUAGE_TYPE.JAVA,
    LANGUAGE_TYPE.PHP,
    LANGUAGE_TYPE.PYTHON,
    LANGUAGE_TYPE.DOTNET,
  ];

  // 专业版 Agent版本 4.3.0及以上支持微服务治理
  const agentVersionMatch = agentDownloadUrl.match(/\/(\d+\.\d+\.\d+(?:\.\d+)?)\//);
  const agentVersion = agentVersionMatch
    ? agentVersionMatch[1]
    : get(window, 'ALIYUN_CONSOLE_GLOBAL.DefaultAgentVersion', '4.2.5');
  const hideCpuburst = confFeature('hideCpuburst');

  useEffect(() => {
    setAppConfig(initAppConfig);
    setValues(parseValue(initAppConfig));
  }, []);

  useEffect(()=>{
    if(!isEmpty(appAddonReleaseInfo) && appAddonReleaseInfo?.enablePrometheus && !isEmpty(appAddonReleaseInfo.config)){
      setValues({
        PrometheusMonitorConfig:{
          EnablePrometheus: appAddonReleaseInfo?.enablePrometheus,
          ...appAddonReleaseInfo.config
        }
      })
    }
  },[JSON.stringify(appAddonReleaseInfo)])

  useEffect(() => {
    if (getParams('targetPart') === 'microService') {
      document
        .getElementById('micro-service-config')
        ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    if (getParams('targetPart') === 'armsMonitor') {
      document
        .getElementById('arms-monitor')
        ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [location.href]);

  useEffect(() => {
    if (isTypeImage(appConfig.PackageType)) return;
    validateSlipBuild();
  }, [appConfig.PackageType, jsonStringify(startCmd), EnableNewArms]);

  useImperativeHandle(ref, () => ({
    validateForm,
  }));

  const validateForm = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) return;
    let params = formatValue(values);
    if (appConfig.EnableImageAccl) {
      // 若之前已开启镜像加速，EnableImageAccl 需必传 true/false
      params.EnableImageAccl = params.EnableImageAccl || false;
    }
    if (scaleRuleEnabled) {
      Reflect.set(params, 'AutoEnableApplicationScalingRule', true);
    }
    if (allowUpdateAppPackageType && includes(supportChangePackageType_language, packageLanguage)) {
      params = handleDeployTypeChangeParams(params);
    }
    return params;
  };

  const handleDeployTypeChangeParams = (params) => {
    let newParams = cloneDeep(params);
    const deployConfigType = params?.PackageType;
    // 部署方式改变时，部分原有字段需进行调整
    if (deployConfigType !== appConfig?.PackageType) {
      if (isTypeImage(deployConfigType) && has(appConfig, 'skipPackageBuild')) {
        Reflect.set(newParams, 'skipPackageBuild', false);
      }
      if (isTypeImage(deployConfigType) && has(appConfig, 'PackageVersion')) {
        Reflect.set(newParams, 'PackageVersion', '');
      }
      // EnableNewArms(镜像部署&&GO语言展示 || python)---该字段忽略
      if (
        !(
          newPackageType !== PACKAGE_TYPE.JAR &&
          newPackageType !== PACKAGE_TYPE.WAR &&
          newPackageType !== PACKAGE_TYPE.DOTNETZIP
        ) &&
        has(appConfig, 'Command')
      ) {
        Reflect.set(newParams, 'Command', '');
        Reflect.set(newParams, 'CommandArgs', '');
      }
      if (
        isTypeImage(deployConfigType) &&
        (has(appConfig, 'JarStartArgs') ||
          has(appConfig, 'JarStartOptions') ||
          has(appConfig, 'WarStartOptions'))
      ) {
        Reflect.set(newParams, 'JarStartArgs', '');
        Reflect.set(newParams, 'JarStartOptions', '');
        Reflect.set(newParams, 'WarStartOptions', '');
      }
      if (
        !(
          !isPhpHighVersion(phpVersion) &&
          packageLanguage === LANGUAGE_TYPE.PHP &&
          ebpf &&
          !php_ebpf
        ) &&
        has(appConfig, 'PhpArmsConfigLocation')
      ) {
        Reflect.set(newParams, 'PhpArmsConfigLocation', '');
      }
      if (
        !(
          enableRateLimit &&
          packageLanguage === LANGUAGE_TYPE.JAVA &&
          jdk !== 'Dragonwell 17' &&
          saeVersion !== 'v2'
        ) &&
        has(appConfig, 'EnableAhas')
      ) {
        Reflect.set(newParams, 'EnableAhas', false);
      }
    }
    return newParams;
  };

  const validateSlipBuild = () => {
    field.validate('skipPackageBuild');
  };

  const onDeployChange = ({ value }) => {
    const { type } = value;
    if (type === DEPLOY_TYPE.MICRO_PACKAGE) {
      validateSlipBuild();
    }
  };

  const deployValidation = (value) => {
    switch (value.type) {
      case DEPLOY_TYPE.IMAGE:
        return value.image ? true : intl('saenext.app-create.web-app.BasicCreator.SelectAnImage');
      case DEPLOY_TYPE.REPOISTORY:
        return Object.keys(value.CodeConfig).length > 0 && Object.keys(value.BuildConfig).length > 0
          ? true
          : intl('saenext.app-create.web-app.BasicCreator.SetTheSourceCodeContinuous');
      case DEPLOY_TYPE.WEB_PACKAGE:
      case DEPLOY_TYPE.MICRO_PACKAGE:
        if (value.PackageUrl) {
          const url = new URL(value.PackageUrl);
          const expires = url.searchParams.get('Expires');
          if (expires) {
            const now = new Date().getTime();
            const expiresTime = Number(expires) * 1000;
            if (now > expiresTime) {
              return intl('saenext.basic-info.DeploySlide.DeployForm.CheckWhetherTheCodePackage');
            }
          }
          return true;
        } else {
          return intl('saenext.app-create.web-app.BasicCreator.PleaseConfigureTheCodePackage');
        }
      default:
        return intl(
          'saenext.app-create.web-app.BasicCreator.NoApplicationDeploymentMethodSelected',
        );
    }
  };

  const isJava = packageLanguage === LANGUAGE_TYPE.JAVA;

  const enableRateLimit = domecsticRegionFilter(regionId);

  const readinessObj = (appConfig?.Readiness && JSON.parse(appConfig?.Readiness)) || {};

  const isNotOpenMse_saev1 =
    (saeVersion === 'v1' || !saeVersion) &&
    get(readinessObj, 'httpGet.port') != 54199 &&
    !includes(get(appConfig, 'PreStop', ''), '127.0.0.1:54199/offline') &&
    !get(appConfig, 'EnableAhas', false);

  const hasMicroService =
    NewSaeVersion === 'pro' ||
    ((!NewSaeVersion || NewSaeVersion === 'std') &&
      enableSaeStdVersionNewMse &&
      packageLanguage === LANGUAGE_TYPE.JAVA &&
      (saeVersion === 'v2' || isNotOpenMse_saev1));

  const newPackageType = useMemo(() => {
    const type = get(getValues(), 'deployConfig.type');
    if (type === DEPLOY_TYPE.IMAGE) {
      return get(getValues(), 'deployConfig.PackageType') || 'Image';
    }
    if (type === DEPLOY_TYPE.MICRO_PACKAGE) {
      return get(getValues(), 'deployConfig.PackageType');
    }
    return packageType;
  }, [get(getValues(), 'deployConfig')]);

  const showSwimLaneK8s = useMemo(() => {
    if (hasMicroService) {
      const MicroserviceEngineConfig = get(getValues(), 'MicroserviceEngineConfig');
      if (MicroserviceEngineConfig && typeof MicroserviceEngineConfig === 'string') {
        return JSON.parse(MicroserviceEngineConfig)?.Enable;
      }
      return false;
    } else {
      return pvtzDiscovery && !BaseAppId && MseApplicationId;
    }
  }, [
    hasMicroService,
    pvtzDiscovery,
    BaseAppId,
    MseApplicationId,
    get(getValues(), 'MicroserviceEngineConfig'),
  ]);

  return (
    <Form field={field} useLabelForErrorMessage className="deploy-container">
      <CollapseField
        title={intl('saenext.app-create.web-app.BasicCreator.BasicInformationSettings')}
        className="mt-l ml-l mr-l"
      >
        <DeploySelectorField
          required
          name="deployConfig"
          className="deploy-group mb-s"
          label={intl('saenext.app-create.web-app.BasicCreator.ApplicationDeploymentMethod')}
          defaultValue={{
            type: DEPLOY_TYPE.IMAGE,
            // image: `registry.${regionId}.aliyuncs.com/sae-serverless-demo/sae-demo:microservice-java-provider-v1.0`,
            // accelerationType: 'Default',
            // ProgrammingLanguage: 'java',
            // Jdk: 'Open JDK 8',
          }}
          // allowTypes={isTypeImage(packageType) ? [DEPLOY_TYPE.IMAGE] : [DEPLOY_TYPE.MICRO_PACKAGE]}
          allowTypes={
            allowUpdateAppPackageType &&
            includes(supportChangePackageType_language, packageLanguage)
              ? [DEPLOY_TYPE.IMAGE, DEPLOY_TYPE.MICRO_PACKAGE]
              : isTypeImage(packageType)
              ? [DEPLOY_TYPE.IMAGE]
              : [DEPLOY_TYPE.MICRO_PACKAGE]
          }
          appType="micro"
          vpcId={vpcId}
          namespaceId={namespaceId}
          actionType="deploy"
          onChange={onDeployChange}
          preIsTypeImage={isTypeImage(packageType)}
          validation={[
            {
              type: 'customValidate',
              param: deployValidation,
            },
          ]}
          isSupportUpdateDeployType={
            allowUpdateAppPackageType &&
            includes(supportChangePackageType_language, packageLanguage)
          }
        />

        <If condition={!isTypeImage(newPackageType)}>
          <If condition={allowSkipBuildImage}>
            <PackageSkipBuild field={field} appConfig={appConfig} />
          </If>
          <Form.Item
            label={intl('saenext.DeploySelectorField.micro-app.PackageConfig.Version')}
            required
            requiredMessage={intl(
              'saenext.DeploySelectorField.micro-app.PackageConfig.PleaseFillInTheVersion',
            )}
            {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
            wrapperCol={{ span: 12 }}
            labelTextAlign="left"
            validator={(rule, value, callback: any) => {
              const { PackageVersion } = appConfig;
              if (value === PackageVersion) {
                callback(
                  intl(
                    'saenext.basic-info.DeploySlide.DeployForm.TheVersionNumberPackageversionAlready',
                    { PackageVersion: PackageVersion },
                  ),
                );
              } else {
                callback();
              }
            }}
          >
            <Input
              trim
              hasLimitHint
              maxLength={32}
              defaultValue={appConfig?.PackageVersion}
              name="PackageVersion"
            />

            <Button
              onClick={() => {
                field.reset('PackageVersion');
                field.setValue('PackageVersion', new Date().getTime());
              }}
              text
              type="primary"
              className="ml-s timestamp"
            >
              {intl(
                'saenext.DeploySelectorField.micro-app.PackageConfig.UseTimestampAsVersionNumber',
              )}
            </Button>
          </Form.Item>
        </If>

        {/* <If condition={scaleRuleEnabled && !isPreview}>
              <Form.Item
                label={intl('saenext.basic-info.DeploySlide.DeployForm.RestoreAutomaticElasticity')}
                help={intl('saenext.basic-info.DeploySlide.DeployForm.TheCurrentApplicationHasEnabled')}
                extra={
                  AssociateEip && intl('saenext.basic-info.DeploySlide.DeployForm.AfterYouBindAnEip')
                }
                required
                {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
              >
                <Radio.Group
                  name="AutoEnableApplicationScalingRule"
                  defaultValue={!AssociateEip ? true : false}
                >
                  <Radio id="true" value={true} disabled={AssociateEip}>
                    <TextWithBalloon
                      text={intl('saenext.basic-info.DeploySlide.DeployForm.AutomaticSystemRecovery')}
                      tips={
                        <>
                          {intl(
                            'saenext.basic-info.DeploySlide.DeployForm.TheSystemAutomaticallyRestoresElasticity',
                          )}
                           <br />
                          {intl(
                            'saenext.basic-info.DeploySlide.DeployForm.HoweverWhenTheApplicationFails',
                          )}
                        </>
                      }
                      align="t"
                    />
                  </Radio>
                  <Radio id="false" value={false}>
                    {intl('saenext.basic-info.DeploySlide.DeployForm.ManuallyEnableRecovery')}
                  </Radio>
                </Radio.Group>
              </Form.Item>
             </If> */}

        <Form.Item
          label={intl('saenext.basic-info.DeploySlide.DeployForm.DeploymentDescription')}
          {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        >
          <Input.TextArea
            name="ChangeOrderDesc"
            maxLength={502}
            style={{ width: 400 }}
            hasLimitHint
          />
        </Form.Item>
      </CollapseField>

      <If condition={!isPreview && !isAppStopped && appConfig.Replicas > 1}>
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.PublishPolicySettings')}
          className="mt-l ml-l mr-l"
        >
          <DeployStrategy field={field} appConfig={appConfig} />
        </CollapseField>
      </If>

      <If
        condition={
          ((!NewSaeVersion || NewSaeVersion === 'std') &&
            ((newPackageType === 'Image' && packageLanguage === LANGUAGE_TYPE.GO) ||
              packageLanguage === LANGUAGE_TYPE.PYTHON)) ||
          (NewSaeVersion === 'pro' &&
            includes([LANGUAGE_TYPE.JAVA, LANGUAGE_TYPE.PYTHON, LANGUAGE_TYPE.GO], packageLanguage))
        }
      >
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.ApplicationMonitoring')}
          subTitle={intl('saenext.basic-info.DeploySlide.DeployForm.ProvidesRealTimeMonitoringFor')}
          linkText={
            NewSaeVersion
              ? intl('saenext.basic-info.DeploySlide.DeployForm.ViewApplicationMonitoring')
              : ''
          }
          linkHref={
            NewSaeVersion
              ? NewSaeVersion === 'pro'
                ? CachedData.confLink('help:sae:app-monitor-pro')
                : CachedData.confLink('help:sae:app-monitor-std')
              : ''
          }
          className="mt-l ml-l mr-l"
          collapsed={!(appConfig.EnableNewArms || getParams('targetPart') === 'armsMonitor')}
        >
          <ArmsMonitorNew
            name="EnableNewArms"
            lang={packageLanguage}
            packageType={newPackageType}
            NewSaeVersion={NewSaeVersion}
            setValue={() => {
              field.setValue('EnableNewArms', appConfig.EnableNewArms);
            }}
          />
        </CollapseField>
      </If>

      <If condition={NewSaeVersion !== 'lite'}>
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.PrometheusMonitoring')}
          subTitle={intl('saenext.basic-info.DeploySlide.DeployForm.FullyConnectedToTheOpen')}
          linkText={intl(
            'saenext.basic-info.DeploySlide.DeployForm.HowToAccessPrometheusMonitoring',
          )}
          linkHref=""
          className="mt-l ml-l mr-l"
        >
          <PrometheusMonitorField
            name="PrometheusMonitorConfig"
            appConfig={appConfig}
            appAddonReleaseInfo={appAddonReleaseInfo}
            ref={prometheusMonitorRef}
            hasCmsPermission={hasCmsPermission}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return prometheusMonitorRef?.current?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        </CollapseField>
      </If>

      <If condition={isTypeImage(newPackageType)}>
        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.StartCommand')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.SetCommandsRequiredForContainer',
          )}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetStartupCommands')}
          linkHref={CachedData.confLink('help:sae:set-startup-command-2-0')}
          className="mt-l ml-l mr-l"
          collapsed={!appConfig.Command && !appConfig.CommandArgs}
        >
          <CommandArgsForm field={field} />
        </CollapseField>
      </If>

      <If condition={newPackageType === PACKAGE_TYPE.JAR || newPackageType === PACKAGE_TYPE.WAR}>
        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.StartCommandSettings')}
          subTitle={intl(
            'saenext.app-create.micro-app.AdvanceCreator.SetCommandsForJavaApplication',
          )}
          linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetStartupCommands')}
          linkHref={CachedData.confLink('help:sae:configure-a-startup-command')}
          className="mt-l ml-l mr-l"
          collapsed={
            !appConfig.JarStartArgs && !appConfig.JarStartOptions && !appConfig.WarStartOptions
          }
        >
          <JavaPackageStartCmd packageType={newPackageType} jdk={jdk} name="startCmd" />
        </CollapseField>
      </If>

      <CollapseField
        title={intl('saenext.app-create.micro-app.AdvanceCreator.EnvironmentVariables')}
        subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.SetSomeVariablesInThe')}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetEnvironmentVariables')}
        linkHref={CachedData.confLink('help:sae:configure-environment-variables-2-0')}
        className="mt-l ml-l mr-l"
        collapsed={appConfig.Envs === '[]'}
      >
        <VariableField
          name="Envs"
          namespaceId={namespaceId}
          ref={variableRef}
          validation={[
            {
              type: 'customValidate',
              param: async () => {
                return await variableRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
          operateType="edit"
          isPreview={isPreview}
        />
      </CollapseField>

      <If
        condition={
          !isPhpHighVersion(phpVersion) &&
          packageLanguage === LANGUAGE_TYPE.PHP &&
          ebpf &&
          !php_ebpf
        }
      >
        <CollapseField
          title={intl(
            'saenext.app-create.micro-app.AdvanceCreator.PhpApplicationMonitoringSettings',
          )}
          linkText={intl(
            'saenext.app-create.micro-app.AdvanceCreator.HowToConfigureApplicationMonitoring',
          )}
          linkHref={CachedData.confLink(
            'help:sae:configure-a-configuration-file-for-a-php-application',
          )}
          className="mt-l ml-l mr-l"
          collapsed={!appConfig.PhpArmsConfigLocation}
        >
          <PhpArmsMonitorField
            name="PhpArmsConfigLocation"
            defaultValue="/usr/local/etc/php/conf.d/arms.ini"
          />
        </CollapseField>
      </If>

      <If
        condition={
          (packageLanguage === LANGUAGE_TYPE.PYTHON && ebpf) ||
          (packageLanguage === LANGUAGE_TYPE.PHP && ebpf && php_ebpf)
        }
      >
        <CollapseField
          title={intl('saenext.app-create.micro-app.AdvanceCreator.ApplicationMonitoring')}
          subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.TheSwitchControlsWhetherThe')}
          linkText={intl(
            'saenext.app-create.micro-app.AdvanceCreator.HowToConfigureApplicationMonitoring',
          )}
          linkHref={CachedData.confLink(
            'help:sae:configure-a-configuration-file-for-a-php-application',
          )}
          className="mt-l ml-l mr-l"
          collapsed={!appConfig.EnableEbpf}
        >
          <EbpfMonitorConfig />
        </CollapseField>
      </If>

      <CollapseField
        title={intl('saenext.app-create.micro-app.AdvanceCreator.ServiceRegistrationDiscovery')}
        subTitle={intl(
          'saenext.app-create.micro-app.AdvanceCreator.JavaMicroservicesProvideAutomaticAddressing',
        )}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.ServiceRegistrationDiscovery')}
        linkHref={CachedData.confLink('help:sae:service-registration-and-discovery')}
        className="mt-l ml-l mr-l"
        collapsed={
          appConfig.MicroRegistration === '0' &&
          isEmpty(appConfig.MicroRegistrationConfig) &&
          isEmpty(appConfig.PvtzDiscovery)
        }
      >
        {isJava && microRegister && (
          <NacosRegistrationField
            field={field}
            microRegistration={get(appConfig, 'MicroRegistration', '0')}
            microRegistrationConfig={get(appConfig, 'MicroRegistrationConfig', '{}')}
          />
        )}

        <If condition={isJava && microRegister && pvtzDiscovery}>
          <div className="border-t mt-l mb-l" />
        </If>
        {pvtzDiscovery && (
          <K8sRegistrationField
            name="PvtzDiscoverySvc"
            appName={appName}
            namespaceId={namespaceId}
            ref={k8sRegistRef}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return k8sRegistRef.current?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
          />
        )}
        {showSwimLaneK8s && (
          <SwimLaneK8sRegistrationField
            name="SwimlanePvtzDiscoverySvc"
            appName={appName}
            namespaceId={namespaceId}
            ref={swimlaneK8sRegistRef}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return swimlaneK8sRegistRef.current?.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
            appConfig={appConfig}
            pvtzDiscoverySvc={field.getValue('PvtzDiscoverySvc')}
          />
        )}
      </CollapseField>

      <CollapseField
        title={intl('saenext.app-create.micro-app.AdvanceCreator.HostsBindingSettings')}
        subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.EnterTheConfigurationInThe')}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetHostsBinding')}
        linkHref={CachedData.confLink('help:sae:configure-host-bindings-1')}
        className="mt-l ml-l mr-l"
        collapsed={appConfig.CustomHostAlias === '[]'}
      >
        <CustomHostField name="hostsArr" />
      </CollapseField>

      <If condition={hasMicroService}>
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.MicroserviceGovernance')}
          subTitle={
            intl(
              'saenext.basic-info.DeploySlide.DeployForm.ProvidesMicroserviceGovernanceFunctionsFor',
              { LANGUAGENAMEPackageLanguage: LANGUAGE_NAME[packageLanguage] },
            ) as string
          }
          linkText={intl(
            'saenext.basic-info.DeploySlide.DeployForm.HowToConfigureMicroserviceGovernance',
          )}
          linkHref={CachedData.confLink('help:sae:enable-microservices-governance')}
          className="mt-l ml-l mr-l"
          collapsed={
            isEmpty(appConfig?.MicroserviceEngineConfig) &&
            !(getParams('targetPart') === 'microService')
          }
        >
          <MicroServceConfigField
            name="MicroserviceEngineConfig"
            ref={microserviceConfigRef}
            validation={[
              {
                type: 'customValidate',
                param: () => {
                  return microserviceConfigRef?.current.getInstance()?.controlRef?.validate?.();
                },
              },
            ]}
            onChangeReadniess={(v) => {
              setValues({
                Readiness: v,
              });
              setClearReadinessConfirm(v === '{}' ? true : false);
            }}
            readinessFieldValues={getValues()?.Readiness}
            appConfig={appConfig}
            isPreview={isPreview}
            baseAppId={appConfig?.BaseAppId}
            hasGrayApp={props.hasGrayApp}
            NewSaeVersion={NewSaeVersion}
            hasMicroService={hasMicroService}
            agentVersion={agentVersion}
          />
        </CollapseField>
      </If>

      <CollapseField
        title={intl('saenext.app-create.micro-app.AdvanceCreator.ApplyHealthCheckSettings')}
        subTitle={intl(
          'saenext.app-create.micro-app.AdvanceCreator.UsedToDetermineWhetherContainers',
        )}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetApplicationHealth')}
        linkHref={CachedData.confLink('help:sae:set-health-check-2-0')}
        className="mt-l ml-l mr-l"
        collapsed={
          isEmpty(appConfig.Liveness) &&
          isEmpty(appConfig.Readiness) &&
          isEmpty(appConfig.StartupProbe)
        }
      >
        <HealthCheckField
          name="Liveness"
          activeKey="Liveness"
          ref={livenessRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return livenessRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
          NewSaeVersion={NewSaeVersion}
          microserviceEngineConfig={getValues()?.MicroserviceEngineConfig}
          isPreview={isPreview}
          hasMicroService={hasMicroService}
        />

        <div className="border-t mt-l mb-l" />
        <HealthCheckField
          name="Readiness"
          activeKey="Readiness"
          ref={readinessRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return readinessRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
          NewSaeVersion={NewSaeVersion}
          microserviceEngineConfig={getValues()?.MicroserviceEngineConfig}
          isPreview={isPreview}
          hasMicroService={hasMicroService}
          appConfig={appConfig}
          clearReadinessConfirm={clearReadinessConfirm}
        />

        <div className="border-t mt-l mb-l" />

        <HealthCheckField
          name="StartupProbe"
          activeKey="StartupProbe"
          ref={startupRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return startupRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
          isPreview={isPreview}
        />
      </CollapseField>

      <CollapseField
        title={intl('saenext.app-create.micro-app.AdvanceCreator.ApplicationLifecycleManagement')}
        subTitle={intl(
          'saenext.app-create.micro-app.AdvanceCreator.LifecycleScriptsDefineAndManage',
        )}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetApplicationLifecycle')}
        linkHref={CachedData.confLink('help:sae:configure-application-lifecycle-management-2-0')}
        className="mt-l ml-l mr-l"
        collapsed={
          isEmpty(appConfig?.PostStart) &&
          isEmpty(appConfig?.PreStop) &&
          !appConfig?.TerminationGracePeriodSeconds
        }
      >
        <LifeCycle
          NewSaeVersion={NewSaeVersion}
          appConfig={appConfig}
          microserviceEngineConfig={getValues()?.MicroserviceEngineConfig}
          field={field}
          hasMicroService={hasMicroService}
        />
      </CollapseField>

      <CollapseField
        title={intl(
          'saenext.app-create.micro-app.AdvanceCreator.ApplyOutboundInboundInternetAccess',
        )}
        subTitle={intl(
          'saenext.app-create.micro-app.AdvanceCreator.ApplyOutboundInboundSettingsSuch',
        )}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToConfigureInternetAccess')}
        linkHref={CachedData.confLink('help:sae:configure-and-access-public-network-based-on-eip')}
        collapsed={appConfig.AssociateEip !== true}
        className="mt-l ml-l mr-l"
      >
        <NetworkAccessField name="AssociateEip" />
      </CollapseField>

      <CollapseField
        title={intl('saenext.app-create.micro-app.AdvanceCreator.LogConfiguration')}
        subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.SetLogCollectionRulesTo')}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToConfigureLogCollection')}
        linkHref={CachedData.confLink('help:sae:log-collection-service-2-0')}
        className="mt-l ml-l mr-l"
        collapsed={isEmpty(appConfig.SlsConfigs) && isEmpty(appConfig.KafkaConfigs)}
      >
        <SlsSelectorField
          name="SlsConfigs"
          className="mt-s"
          ref={slsRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return slsRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
        />

        <div className="border-t mt-l mb-l" />

        <KafkaSelectorField
          name="KafkaConfigs"
          vpcId={vpcId}
          className="mt-s"
          ref={kafkaRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return kafkaRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
        />
      </CollapseField>

      <CollapseField
        title={intl('saenext.app-create.micro-app.AdvanceCreator.PersistentStorage')}
        subTitle={intl('saenext.app-create.micro-app.AdvanceCreator.SetPersistentStorageData')}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetPersistentStorage')}
        linkHref={CachedData.confLink('help:sae:persistent-storage')}
        className="mt-l ml-l mr-l"
        collapsed={isEmpty(appConfig.NasConfigs) && !appConfig.OssAkSecret}
      >
        <NasSelectorField
          name="NasConfigs"
          vpcId={vpcId}
          ref={nasRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return nasRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
          NewSaeVersion={NewSaeVersion}
          enableCustomNasMountOptions={enableCustomNasMountOptions}
        />

        <div className="border-t mt-l mb-l" />
        <OssSelectorField
          name="ossConfigs"
          ref={ossRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return ossRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
          NewSaeVersion={NewSaeVersion}
        />
      </CollapseField>

      <If condition={oidc}>
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.IdentityAuthenticationService')}
          subTitle={intl('saenext.basic-info.DeploySlide.DeployForm.SetTheAuthenticationMethodFor')}
          linkText={intl('saenext.basic-info.DeploySlide.DeployForm.HowToSetRrsaOidc')}
          linkHref={CachedData.confLink('help:sae:configure-authentication-service')}
          className="mt-l ml-l mr-l"
          collapsed={isEmpty(appConfig.OidcRoleName)}
        >
          <OidcRole field={field} name="OidcRoleName" />
        </CollapseField>
      </If>

      <If condition={NewSaeVersion !== 'lite' && !hideCpuburst}>
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.CpuBurstConfiguration')}
          subTitle={intl(
            'saenext.basic-info.DeploySlide.DeployForm.SetCpuBurstPerformanceOptimization',
          )}
          linkText={intl('saenext.basic-info.DeploySlide.DeployForm.HowToSetCpuBurst')}
          linkHref={CachedData.confLink('help:sae:enable-cpu-burst-function')}
          className="mt-l ml-l mr-l"
        >
          <CpuBurstField name="burstConfig" disabled={cpu > 4000} />
        </CollapseField>
      </If>
      <CollapseField
        title={intl('saenext.app-create.micro-app.AdvanceCreator.ConfigurationManagement')}
        subTitle={intl(
          'saenext.app-create.micro-app.AdvanceCreator.InjectConfigurationInformationIntoThe',
        )}
        linkText={intl('saenext.app-create.micro-app.AdvanceCreator.HowToManageConfigurations')}
        linkHref={CachedData.confLink('help:sae:injection-configuration-information')}
        className="mt-l ml-l mr-l"
        collapsed={isEmpty(appConfig.ConfigMapMountDesc)}
      >
        <ConfigManageField
          name="ConfigMapMountDesc"
          namespaceId={namespaceId}
          ref={configRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return configRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
        />
      </CollapseField>

      <CollapseField
        title={intl('saenext.basic-info.DeploySlide.DeployForm.ConfidentialDictionary')}
        subTitle={intl(
          'saenext.basic-info.DeploySlide.DeployForm.InjectConfidentialInformationIntoThe',
        )}
        linkText={intl(
          'saenext.basic-info.DeploySlide.DeployForm.HowToManageConfidentialDictionaries',
        )}
        linkHref={CachedData.confLink('help:sae:inject-secret')}
        className="mt-l ml-l mr-l"
        collapsed={isEmpty(appConfig.SecretMountDesc)}
      >
        <SecretField
          name="SecretMountDesc"
          namespaceId={namespaceId}
          ref={secretRef}
          validation={[
            {
              type: 'customValidate',
              param: () => {
                return secretRef.current?.getInstance()?.controlRef?.validate?.();
              },
            },
          ]}
          isPreview={isPreview}
        />
      </CollapseField>

      <If condition={sidecar}>
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.AddSidecarContainers')}
          className="mt-l ml-l mr-l"
          collapsed={isEmpty(appConfig.SidecarContainersConfig)}
        >
          <Form.Item>
            <Sidecar
              field={field}
              name="SidecarContainersConfig"
              defaultValue={[]}
              namespaceId={namespaceId}
              appConfig={appConfig}
              isPreview={isPreview}
            />
          </Form.Item>
        </CollapseField>
      </If>

      <If
        condition={
          enableRateLimit &&
          packageLanguage === LANGUAGE_TYPE.JAVA &&
          jdk !== 'Dragonwell 17' &&
          saeVersion !== 'v2' &&
          !isNotOpenMse_saev1
        }
      >
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.ThrottlingDegradationSettings')}
          subTitle={intl(
            'saenext.basic-info.DeploySlide.DeployForm.SetWhetherTheApplicationEnables',
          )}
          linkText={intl('saenext.basic-info.DeploySlide.DeployForm.HowToSetThrottlingDegradation')}
          linkHref={CachedData.confLink('help:sae:configure-throttling-and-downgrade')}
          className="mt-l ml-l mr-l"
          collapsed={!appConfig.EnableAhas}
        >
          <If condition={!ahas_migrate}>
            <AhasRateLimit
              appId={appConfig.AppId}
              regionId={regionId}
              defaultEnable={appConfig.EnableAhas}
              value={EnableAhas}
            />
          </If>
          <If condition={ahas_migrate}>
            <MseRateLimit
              appId={appConfig.AppId}
              regionId={regionId}
              defaultEnable={appConfig.EnableAhas}
              value={EnableAhas}
            />
          </If>
        </CollapseField>
      </If>

      <If condition={packageLanguage === LANGUAGE_TYPE.JAVA && !isEmpty(serviceTags)}>
        <CollapseField
          title={intl('saenext.basic-info.DeploySlide.DeployForm.GrayscaleLabel')}
          className="mt-l ml-l mr-l"
        >
          <GrayTags />
        </CollapseField>
      </If>

      {/* // TODO: 微服务无损上下线 */}
    </Form>
  );
};

export default memo(forwardRef(DeployForm));
