import React from 'react';
import { Checkbox, Form, intl } from '@ali/cnd';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import C from '~/constants/common';
import { checkPackageChanged } from './utils';

const PackageSkipBuild = (props) => {
  const { field, appConfig } = props;

  const packageKeys = [
    'PackageUrl',
    'Jdk',
    // "JarStartOptions",
    // "JarStartArgs",
    // "WarStartOptions",
    'Timezone',
    'PackageRuntimeCustomBuild',
    'WebContainer',
    'EdasContainerVersion',
    'Php',
    'PhpExtensions',
    'PhpPECLExtensions',
    'Python',
    'PythonModules',
    // "EnableNewArms",
    'Dotnet',
  ];

  const javaStartKeys = ['JarStartOptions', 'JarStartArgs', 'WarStartOptions'];

  const packageValidator = (rule, value, callback) => {
    if (!value) {
      callback();
      return;
    }
    const { deployConfig, EnableNewArms, startCmd } = field.getValues();
    const errors = [];
    if (!!EnableNewArms !== !!appConfig.EnableNewArms) {
      errors.push(
        intl('saenext.basic-info.DeploySlide.PackageSkipBuild.TheMonitoringFunctionHasBeen'),
      );
    }
    if (checkPackageChanged(startCmd, appConfig, javaStartKeys)) {
      errors.push(
        intl('saenext.basic-info.DeploySlide.PackageSkipBuild.TheStartupCommandSettingsHave'),
      );
    }
    if (checkPackageChanged(deployConfig, appConfig, packageKeys)) {
      errors.push(intl('saenext.basic-info.DeploySlide.DeployForm.YourCodePackageHasBeen'));
    }
    if (errors.length > 0) {
      errors.push(intl('saenext.basic-info.DeploySlide.PackageSkipBuild.SkipImageBuildingIsNot'));
      callback(errors.join(intl('sae.common.comma')));
      return;
    }
    callback();
  };

  return (
    <Form.Item
      label={
        <TextWithBalloon
          text={intl('saenext.basic-info.DeploySlide.DeployForm.SkipImageDeployment')}
          tips={intl('saenext.basic-info.DeploySlide.DeployForm.IfYouSelectThisOption')}
        />
      }
      validator={packageValidator}
      {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
    >
      <Checkbox name="skipPackageBuild" />
    </Form.Item>
  );
};

export default PackageSkipBuild;
