import { isObject, isUndefined, has } from "lodash";
import { formatHosts, parseHosts } from "~/components/shared/CustomHostField/utils";
import { DEPLOY_TYPE, PACKAGE_TYPE, UPLOAD_TYPE } from "~/components/shared/DeploySelectorField/constant";
import { formatPvtz } from "~/components/shared/K8sRegistrationField/utils";
import { formatRegistration, parseRegistration } from "~/components/shared/NacosRegistrationField";
import { jsonParse, jsonStringify } from "~/utils/transfer-data";
import { isTypeImage } from '~/utils/global';
import { isEmpty } from "lodash";

export const parseValue = (appConfig) => {
  const {
    RegionId,
    Replicas,
    ProgrammingLanguage,
    PackageType,
    ImageUrl,
    AcrInstanceId,
    EnableImageAccl,
    EnableCpuBurst = false,
    AcrAssumeRoleArn,
    ImagePullSecrets,
    CustomImageNetworkType,
    Jdk,
    Php,
    Python,
    Dotnet,
    PackageUrl,
    Timezone,
    TomcatConfig,
    WebContainer,
    EdasContainerVersion,
    PhpExtensions,
    PhpPECLExtensions,
    PythonModules,
    PackageRuntimeCustomBuild,
    PhpConfig,
    PhpConfigLocation,
    JarStartOptions,
    WarStartOptions,
    JarStartArgs,
    UpdateStrategy,
    PvtzDiscovery,
    SwimlanePvtzDiscovery,
    CustomHostAlias,
    OssAkId,
    OssAkSecret,
    OssMountDescs,
    // MicroserviceEngineConfig = '',
    ...rest
  } = appConfig;

  const regexImage = /^Image/i;

  const dotnetCommand =
    !isTypeImage(PackageType) &&
    (PackageType !== PACKAGE_TYPE.JAR || PackageType !== PACKAGE_TYPE.WAR)
      ? {
          Command: appConfig.Command,
          CommandArgs: appConfig.CommandArgs,
        }
      : {};

  return {
    deployConfig: {
      type: regexImage.test(PackageType) ? DEPLOY_TYPE.IMAGE : DEPLOY_TYPE.MICRO_PACKAGE,
      image: ImageUrl,
      instanceId: AcrInstanceId,
      enableImageAccl: EnableImageAccl,
      acrAssumeRoleArn: AcrAssumeRoleArn,
      imagePullSecrets: ImagePullSecrets,
      customImageNetworkType: CustomImageNetworkType,
      ProgrammingLanguage,
      PackageType,
      Jdk,
      Php,
      Python,
      Dotnet,
      uploadType: UPLOAD_TYPE.UPLOAD,
      PackageUrl,
      Timezone,
      TomcatConfig,
      WebContainer,
      EdasContainerVersion,
      PhpExtensions: jsonParse(PhpExtensions),
      PhpPECLExtensions: jsonParse(PhpPECLExtensions),
      PythonModules: jsonParse(PythonModules),
      PackageRuntimeCustomBuild,
      PhpConfig,
      PhpConfigLocation,
      ...dotnetCommand,
    },
    UpdateStrategy: Replicas > 1 && jsonParse(UpdateStrategy) ? jsonParse(UpdateStrategy) : {},
    burstConfig: {
      enableCpuBurst: EnableCpuBurst
    },
    startCmd: {
      JarStartOptions,
      WarStartOptions,
      JarStartArgs,
    },
    ossConfigs: {
      OssAkId,
      OssAkSecret,
      OssMountDescs,
    },
    hostsArr: parseHosts(CustomHostAlias),
    PvtzDiscoverySvc: formatPvtz(PvtzDiscovery, true),
    SwimlanePvtzDiscoverySvc: formatPvtz(SwimlanePvtzDiscovery),
    ...parseRegistration(appConfig),
    // ..._microserviceEngineConfig,
    ...rest
  }
}

export const formatValue = (values) => {
  const {
    skipPackageBuild,
    deployConfig,
    startCmd,
    ossConfigs,
    hostsArr,
    armsConfig,
    burstConfig,
    MicroserviceEngineConfig = '',
    PrometheusMonitorConfig = {},
    ...rest
  } = values;

  const burstParams = {
    EnableCpuBurst: burstConfig?.enableCpuBurst || false,
  };

  const EnableGreyTagRoute = !!values?.UpdateStrategy?.enableGreyTagRoute;

  let _microServiceEngineConfig = '';
  if(MicroserviceEngineConfig && JSON.parse(MicroserviceEngineConfig)?.Enable ){
    const MseLosslessRule = JSON.parse(MicroserviceEngineConfig)?.MseLosslessRule || {};
    if (has(MicroserviceEngineConfig, 'delayEnable')) {
      delete MicroserviceEngineConfig.delayEnable;
    }
    _microServiceEngineConfig = JSON.stringify({
      Enable: JSON.parse(MicroserviceEngineConfig)?.Enable,
      MseLosslessRule,
    });
  }
  const params = {
    ...rest,
    ...formatDeployValues(deployConfig),
    EnableGreyTagRoute,
    ...startCmd,
    ...ossConfigs,
    ...burstParams,
    CustomHostAlias: formatHosts(hostsArr),
  };

  if(has(values,'MicroserviceEngineConfig')){
    Reflect.set(params,'MicroserviceEngineConfig',_microServiceEngineConfig)
  }

  if (skipPackageBuild) {
    delete params.PackageUrl;
  }
  if(params?.SwimlanePvtzDiscoverySvc && !JSON.parse(params?.SwimlanePvtzDiscoverySvc)?.serviceName){
    delete params.SwimlanePvtzDiscoverySvc;
  }

  if(!isEmpty(PrometheusMonitorConfig)){
    const {EnablePrometheus = false,interval,port,metricPath} = PrometheusMonitorConfig;
    Reflect.set(params, 'EnablePrometheus', EnablePrometheus);
    Reflect.set(params, 'PrometheusConfig', {
      interval,
      port,
      metricPath,
    });
    delete params.PrometheusMonitorConfig;
  }

  formatRegistration(params);

  return params;
}

const formatDeployValues = (deployConfig) => {
  const {
    type,
    image: ImageUrl,
    instanceId: AcrInstanceId,
    acrAssumeRoleArn: AcrAssumeRoleArn,
    imagePullSecrets,
    enableImageAccl: EnableImageAccl,
    ProgrammingLanguage,
    Jdk,
    Php,
    uploadType,
    customImageNetworkType,
    ...rest
  } = deployConfig;

  if (type === DEPLOY_TYPE.IMAGE) {
    return {
      PackageType: deployConfig?.PackageType || 'Image',
      ImageUrl,
      AcrInstanceId,
      EnableImageAccl: AcrInstanceId ? EnableImageAccl : undefined,
      AcrAssumeRoleArn,
      ImagePullSecrets: imagePullSecrets || '',
      ProgrammingLanguage,
      Jdk,
      Php,
      CustomImageNetworkType: customImageNetworkType || '',
      PackageVersion: '',
    };
  } else if (type === DEPLOY_TYPE.MICRO_PACKAGE) {
    return {
      ProgrammingLanguage,
      Jdk,
      Php,
      ...rest,
    }
  }
}

export const checkPackageChanged = (deployConfig, appConfig, keys) => {
  consoleKey(deployConfig, appConfig, keys);
  const packageChanged = keys.some(key => !isValEqual(deployConfig[key], appConfig[key]));
  return packageChanged;
}

const isValEqual = (a, b) => {
  if (isVacant(a) && isVacant(b)) {
    return true;
  }
  return jsonfy(a) === jsonfy(b);
}

const isVacant = (v) => {
  return isUndefined(v) || v === '';
}

const jsonfy = (v) => {
  if (isObject(v) || v === null) {
    return JSON.stringify(v);
  } else {
    return v;
  }
}

const consoleKey = (deployConfig, appConfig, keys) => {
  const unequalKeys: string[] = [];
  for (let key of keys) {
    if (!isValEqual(deployConfig[key], appConfig[key])) {
      unequalKeys.push(key);
    }
  }
  if (unequalKeys.length > 0) {
    console.log("unequalKeys:", unequalKeys, deployConfig, appConfig);
  }
}
