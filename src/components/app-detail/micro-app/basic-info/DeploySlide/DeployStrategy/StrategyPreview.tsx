import { intl } from '@ali/cnd';
import React, { useMemo } from 'react';
import { Icon, Timeline } from '@ali/cnd';
import './index.less';

const StrategyPreview = (props) => {
  const { field, replicas } = props;

  const {
    UpdateStrategy: {
      type: strategy,
      batchUpdate: { batch: batchCount = 1, releaseType = 'auto', batchWaitTime = 0 } = {} as any,
      grayUpdate: { gray: grayCount = 0 } = {} as any,
    } = {} as any,
  } = field.getValues();

  const stratePreviewList = useMemo(() => {
    const restInstanceCount = replicas - grayCount; // 灰度后剩余实例数
    const baseBatchCount = Math.floor(restInstanceCount / batchCount); // 灰度后平均每批实例数
    const restBatchCount = restInstanceCount % batchCount; // 平均后余数

    const strateList = [
      {
        title: intl('saenext.DeploySlide.DeployStrategy.StrategyPreview.StartDeployment'),
        content: '',
      },
    ];

    if (strategy === 'GrayBatchUpdate') {
      strateList.push({
        title: intl(
          'saenext.DeploySlide.DeployStrategy.StrategyPreview.NumberOfGrayLevelsGraycount',
          { grayCount: grayCount },
        ) as string,
        content: '',
      });
    }

    for (let i = 0; i < batchCount; i++) {
      // 灰度后每批实例数, 余数从最后一批往前每个加一个
      const count = i >= batchCount - restBatchCount ? baseBatchCount + 1 : baseBatchCount;
      const hasGaryContent =
        strategy === 'GrayBatchUpdate'
          ? intl('saenext.DeploySlide.DeployStrategy.StrategyPreview.AfterGray')
          : '';
      const batchWaitTimeContent =
        i !== batchCount - 1 && releaseType === 'auto'
          ? intl(
              'saenext.DeploySlide.DeployStrategy.StrategyPreview.BatchIntervalBatchwaittimeMin',
              { batchWaitTime: batchWaitTime },
            ) as string
          : '';
      const serial = i + 1;

      let batchType =
        releaseType === 'auto'
          ? intl('saenext.DeploySlide.DeployStrategy.StrategyPreview.StartAutomatically')
          : intl('saenext.DeploySlide.DeployStrategy.StrategyPreview.StartManually');

      if (i === 0) {
        batchType =
          strategy === 'GrayBatchUpdate'
            ? intl('saenext.DeploySlide.DeployStrategy.StrategyPreview.StartManually')
            : intl('saenext.DeploySlide.DeployStrategy.StrategyPreview.StartAutomatically');
      }

      const item = {
        title: intl(
          'saenext.DeploySlide.DeployStrategy.StrategyPreview.BatchtypeHasgarycontentBatchSerialCount',
          { batchType: batchType, hasGaryContent: hasGaryContent, serial: serial, count: count },
        ) as string,
        content: batchWaitTimeContent,
      };

      strateList.push(item);
    }

    strateList.push({
      title: intl('saenext.DeploySlide.DeployStrategy.StrategyPreview.EndOfDeployment'),
      content: '',
    });

    return strateList;
  }, [replicas, field.getValue('UpdateStrategy')]);

  return (
    <div className="pl-xl border-l">
      <p className="mb">
        {intl(
          'saenext.DeploySlide.DeployStrategy.StrategyPreview.PublishPolicyConfigurationInformation',
        )}
      </p>
      <Timeline className="strategy-preview">
        {stratePreviewList.map((item, idx) => (
          <Timeline.Item
            key={idx}
            title={item.title}
            time={item.content}
            dot={
              <div className="strategy-dot">
                <Icon type="angle-down" size="xxs" />
              </div>
            }
          />
        ))}
      </Timeline>
    </div>
  );
};

export default StrategyPreview;
