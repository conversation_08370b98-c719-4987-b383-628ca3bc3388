import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import { Form, NumberPicker, Select } from '@ali/cnd';
import SimpleCollapse from '~/components/shared/SimpleCollapse';
import C from '~/constants/common';
import { RELEASE_TYPE_LIST, STRATEGY_LIST } from './constant';
import MinReadyInstanceField from '~/components/shared/MinReadyInstance/MinReadyInstanceField';
import StrategyPreview from './StrategyPreview';
import If from '~/components/shared/If';
import GreyRoute from './GreyRoute';

const DeployStrategy = (props) => {
  const {
    field,
    appConfig: { Replicas: replicas, BatchWaitTime },
    autoReset
  } = props;

  useEffect(() => {
    if (autoReset) {
      field.resetToDefault(['UpdateStrategy', 'BatchWaitTime', 'MinReadyInstanceRatio', 'MinReadyInstances'])
    }
  }, [])

  const {
    UpdateStrategy: {
      type: strategy,
      batchUpdate: { batch: batchCount, releaseType = 'auto', batchWaitTime } = {} as any,
      grayUpdate: { gray: grayCount = 0 } = {} as any,
    } = {} as any,
  } = field.getValues();

  const restInstanceCount = replicas - grayCount;
  // 灰度 > 1 或 剩余实例数 > 批次数, 存在批次内有多个实例时, 才展示批次内部署间隔
  const isShowInnerBatchWaitTime = grayCount > 1 || restInstanceCount > batchCount;

  const onStategyChange = (val) => {
    if (val === 'BatchUpdate') {
      field.reset(['UpdateStrategy.grayUpdate.gray']);
    } else {
      // 切到灰度发布时，灰度数量设为默认值，此时表单项还未加载，需要手动设置
      field.setValue('UpdateStrategy.grayUpdate.gray', 1);
    }
    field.resetToDefault(['UpdateStrategy.batchUpdate.batch']);
  };

  return (
    <div className="flex item-start">
      <div style={{ minWidth: 700 }}>
        <Form.Item
          required
          label={intl('saenext.DeploySlide.DeployStrategy.ReleasePolicy')}
          {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        >
          <Select
            name="UpdateStrategy.type"
            defaultValue={replicas > 1 ? 'GrayBatchUpdate' : 'BatchUpdate'}
            dataSource={STRATEGY_LIST}
            onChange={onStategyChange}
            style={{ width: 200 }}
          />
        </Form.Item>

        {strategy === 'GrayBatchUpdate' && (
          <Form.Item
            required
            label={intl('saenext.DeploySlide.DeployStrategy.GrayScaleQuantity')}
            help={intl('saenext.DeploySlide.DeployStrategy.IfAutoElasticityIsEnabled')}
            {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
          >
            <NumberPicker
               // @ts-ignore
              name="UpdateStrategy.grayUpdate.gray"
              defaultValue={1}
              style={{ width: 200 }}
              min={1}
              max={Math.min(Math.floor(replicas / 2), replicas - batchCount)}
            />

            <span className="ml">
              {intl('saenext.DeploySlide.DeployStrategy.TotalNumberOfCurrentApplication', {
                replicas: replicas,
              })}
            </span>
          </Form.Item>
        )}

        <Form.Item
          required
          label={
            strategy === 'GrayBatchUpdate'
              ? intl('saenext.DeploySlide.DeployStrategy.RemainingBatchesAfterGrayScale')
              : intl('saenext.DeploySlide.DeployStrategy.ReleaseBatch')
          }
          help={
            strategy === 'GrayBatchUpdate' &&
            intl('saenext.DeploySlide.DeployStrategy.AfterTheGrayScaleIs')
          }
          {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        >
          <NumberPicker
            // @ts-ignore
            name="UpdateStrategy.batchUpdate.batch"
            defaultValue={1}
            style={{ width: 200 }}
            min={1}
            max={Math.min(replicas - grayCount, 20)}
          />
        </Form.Item>

        {batchCount > 1 && (
          <Form.Item
            required
            label={intl('saenext.DeploySlide.DeployStrategy.InterbatchProcessing')}
            {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
          >
            <Select
              name="UpdateStrategy.batchUpdate.releaseType"
              defaultValue="auto"
              dataSource={RELEASE_TYPE_LIST}
              style={{ width: 200 }}
            />
          </Form.Item>
        )}

        {batchCount > 1 && releaseType === 'auto' && (
          <Form.Item
            required
            label={intl('saenext.DeploySlide.DeployStrategy.BatchInterval')}
            {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
          >
            <NumberPicker
              // @ts-ignore
              name="UpdateStrategy.batchUpdate.batchWaitTime"
              defaultValue={5}
              style={{ width: 200 }}
              min={0}
              max={30}
              // @ts-ignore
              addonTextAfter="min"
            />
          </Form.Item>
        )}

        <SimpleCollapse
          text={intl('saenext.DeploySlide.DeployStrategy.AdvancedOptions')}
          defaultOpen={true}
          lazyLoad={false}
        >
          <div className="pt">
            {isShowInnerBatchWaitTime && (
              <Form.Item
                required
                label={intl('saenext.DeploySlide.DeployStrategy.IntraBatchDeploymentInterval')}
                {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
              >
                <NumberPicker
                   // @ts-ignore
                  name="BatchWaitTime"
                  defaultValue={BatchWaitTime}
                  style={{ width: 200 }}
                  min={0}
                  // @ts-ignore
                  addonTextAfter={intl('saenext.DeploySlide.DeployStrategy.Seconds')}
                />
              </Form.Item>
            )}

            <Form.Item
              required
              label={intl('saenext.DeploySlide.DeployStrategy.MinimumNumberOfSurvivingInstances')}
              help={intl('saenext.DeploySlide.DeployStrategy.WeRecommendThatYouSet')}
              {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
            >
              <MinReadyInstanceField field={field} />
            </Form.Item>

            <If condition={strategy === 'GrayBatchUpdate'}>
              <GreyRoute field={field} />
            </If>
          </div>
        </SimpleCollapse>
      </div>
      <div>
        <StrategyPreview field={field} replicas={replicas} />
      </div>
    </div>
  );
};

export default DeployStrategy;
