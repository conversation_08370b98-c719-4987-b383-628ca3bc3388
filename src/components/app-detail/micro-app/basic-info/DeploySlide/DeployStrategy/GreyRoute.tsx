import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useState } from 'react';
import { Badge, Checkbox, Form, Input, Loading, useService } from '@ali/cnd';
import { ConfigProvider } from '@alifd/next';
import { find, forEach, get, isEmpty, map } from 'lodash';
import If from '~/components/shared/If';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import services from "~/services";
import MicroAppContext from '~/utils/microAppContext';
import FeatureContext from '~/utils/featureContext';
import { PREVIEW_CONFIG } from '~/constants/previewConfig';
import RcIngressGrayRules from '~/components/app-detail/micro-app/msc/RcIngressGrayRules';
import RcMicroGrayRules, { FrameworkTypes } from '~/components/app-detail/micro-app/msc/RcMicroGrayRules';

const GreyRoute = (props) => {
  const { field } = props;

  const {
    UpdateStrategy: { albRuleId = '', enableGreyTagRoute = false },
  } = field.getValues();

  const [ingressGreyChecked, setIngressGreyChecked] = useState(false);

  const { appConfig } = useContext(MicroAppContext);
  const { feature } = useContext(FeatureContext);

  const { AppId, RegionId } = appConfig;
  const { albGray } = feature;

  const getGreyRoute = async () => {
    const { Data: { Result = [] } = {} } =
      (await services.getGrayTagRoutes({
        AppId,
      })) || {};

    const { AlbRules: [albGreyRule] = [], GreyTagRouteId } =
      find(Result, (item) => item.AlbRules?.length > 0) || {};

    const microRoute =
      find(Result, (item) => item.ScRules?.length > 0 || item.DubboRules?.length > 0) || {};
    const microGreyRules = joinMicroRules(microRoute);

    return {
      GreyTagRouteId,
      albGreyRule,
      microGreyRules,
    };
  };

  const joinMicroRules = (record) => {
    const rules = [];
    const scRules = get(record, 'ScRules', []);
    const dubboRules = get(record, 'DubboRules', []);
    forEach(scRules, (rule) => {
      const { items = [] } = rule;
      rules.push({
        ...rule,
        key: Math.random(),
        type: FrameworkTypes.sc,
        scItems: map(items, (item) => ({ ...item })),
      });
    });
    forEach(dubboRules, (rule) => {
      const { items = [] } = rule;
      rules.push({
        ...rule,
        key: Math.random(),
        type: FrameworkTypes.dubbo,
        dubboItems: map(items, (item) => ({ ...item })),
      });
    });
    return rules;
  };

  const {
    loading,
    run,
    data: { GreyTagRouteId, albGreyRule = {}, microGreyRules = [] } = {},
  } = useService(getGreyRoute, {}, { manual: true });

  useEffect(() => {
    if (albRuleId) {
      setIngressGreyChecked(true);
      run();
    }
  }, [albRuleId]);

  useEffect(() => {
    // 启用七层流量灰度规则，查询到规则里 GreyTagRouteId 后，填到表单上
    if (ingressGreyChecked && GreyTagRouteId) {
      field.setValue('UpdateStrategy.albRuleId', GreyTagRouteId);
    }
  }, [GreyTagRouteId, ingressGreyChecked]);

  const onIngressCheck = (checked) => {
    if (!checked) {
      field.setValue('UpdateStrategy.albRuleId', '');
    }
    setIngressGreyChecked(checked);
    onCheck(checked);
  };

  const onCheck = (checked) => {
    if (checked) {
      run();
    }
  };

  return (
    <>
      {albGray && (
        <Form.Item
          help={
            <If condition={!loading && ingressGreyChecked}>
              {isEmpty(albGreyRule) ? (
                intl.html('saenext.DeploySlide.DeployStrategy.GreyRoute.YouHaveNotSetA', {
                  RegionId: RegionId,
                  AppId: AppId,
                })
              ) : (
                <a href={`/${RegionId}/app-list/${AppId}/micro-app/msc-rules`} target="_blank">
                  {intl('saenext.DeploySlide.DeployStrategy.GreyRoute.ModifyGrayscaleRules')}
                </a>
              )}
            </If>
          }
        >
          <Checkbox checked={ingressGreyChecked} onChange={onIngressCheck}>
            <TextWithBalloon
              text={
                <Badge
                  content="BETA"
                  className="mr-xl"
                  style={{
                    backgroundColor: '#f54743',
                    color: '#fff',
                    borderRadius: '10px',
                  }}
                >
                  <span className="mr">
                    {intl(
                      'saenext.DeploySlide.DeployStrategy.GreyRoute.EnableLayerTrafficGrayRules',
                    )}
                  </span>
                </Badge>
              }
              tips={intl('saenext.DeploySlide.DeployStrategy.GreyRoute.EnablingGrayScaleRulesCan')}
            />
          </Checkbox>
          <Input name="UpdateStrategy.albRuleId" style={{ display: 'none' }} />

          <If condition={ingressGreyChecked}>
            <Loading visible={loading} inline={false}>
              <If condition={!isEmpty(albGreyRule)}>
                <ConfigProvider defaultPropsConfig={PREVIEW_CONFIG}>
                  <RcIngressGrayRules
                    value={[albGreyRule]}
                    onChange={() => undefined}
                    appConfig={appConfig}
                    isPreview
                  />
                </ConfigProvider>
              </If>
            </Loading>
          </If>
        </Form.Item>
      )}

      <Form.Item
        help={
          <If condition={!loading && enableGreyTagRoute}>
            {isEmpty(microGreyRules) ? (
              intl.html('saenext.DeploySlide.DeployStrategy.GreyRoute.CurrentlyYouHaveNotSet', {
                RegionId: RegionId,
                AppId: AppId,
              })
            ) : (
              <a href={`/${RegionId}/app-list/${AppId}/micro-app/msc-rules`} target="_blank">
                {intl('saenext.DeploySlide.DeployStrategy.GreyRoute.ModifyGrayscaleRules')}
              </a>
            )}
          </If>
        }
      >
        <Checkbox name="UpdateStrategy.enableGreyTagRoute" onChange={onCheck}>
          <TextWithBalloon
            text={
              <Badge
                content="BETA"
                className="mr-xl"
                style={{
                  backgroundColor: '#f54743',
                  color: '#fff',
                  borderRadius: '10px',
                }}
              >
                <span className="mr">
                  {intl(
                    'saenext.DeploySlide.DeployStrategy.GreyRoute.EnableMicroserviceGrayscaleRulesOnly',
                  )}
                </span>
              </Badge>
            }
            tips={intl('saenext.DeploySlide.DeployStrategy.GreyRoute.EnableGrayRulesToTransfer')}
          />
        </Checkbox>
        <If condition={enableGreyTagRoute}>
          <Loading visible={loading} inline={false}>
            <If condition={!isEmpty(microGreyRules)}>
              <ConfigProvider defaultPropsConfig={PREVIEW_CONFIG}>
                <RcMicroGrayRules
                  value={microGreyRules}
                  onChange={() => undefined}
                  appConfig={appConfig}
                  isPreview
                />
              </ConfigProvider>
            </If>
          </Loading>
        </If>
      </Form.Item>
    </>
  );
};

export default GreyRoute;
