import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Button, Icon, ToolTipCondition } from '@ali/cnd';
import If from '~/components/shared/If';

const EnabledWrap = (props) => {
  const { defaultOpen = false, onOpen, onClose, disabled, disabledMessage, children } = props;

  const [enabled, setEnabled] = useState(defaultOpen);

  const onAdd = () => {
    const res = onOpen?.();
    if (res === false) return;
    setEnabled(true);
  };

  const onDelete = () => {
    const res = onClose?.();
    if (res === false) return;
    setEnabled(false);
  };

  return (
    <>
      <If condition={!enabled}>
        <ToolTipCondition show={disabled} tip={disabledMessage}>
          <Button onClick={onAdd} disabled={disabled}>
            {intl('saenext.basic-info.ScaleRuleSlide.EnabledWrap.AddConfiguration')}
          </Button>
        </ToolTipCondition>
      </If>
      <If condition={enabled}>
        <div className="relative">
          {children}

          <div className="absolute" style={{ right: 0, top: 0 }}>
            <ToolTipCondition show={disabled} tip={disabledMessage} align="l">
              <Button text type="primary" onClick={onDelete} disabled={disabled}>
                <Icon type="delete" />
                {intl('saenext.basic-info.ScaleRuleSlide.EnabledWrap.Delete')}
              </Button>
            </ToolTipCondition>
          </div>
        </div>
      </If>
    </>
  );
};

export default EnabledWrap;
