import React, { useRef, useState } from 'react';
import { intl, Message } from '@ali/cnd';
import SlideWrap from '~/components/shared/SlideWrap';
import { get, isArray, isEmpty, noop, some, union } from 'lodash';
import { SLIDE_TITLE } from '../ScalingRuleSlide/constant';
import ScaleRuleForm from './ScaleRuleForm';
import MultiLinesCollapse from '~/components/shared/MultiLinesCollapse';
import If from '~/components/shared/If';
import services from '~/services';
import { SCALE_RULE_TYPE } from './constant';
import { formatMixTimerSchedules } from './utils';

const ScaleRuleSlide = (props) => {
  const { appId, refresh, handleRefreshScalingRules, type = 'create', ruleData = {}, children } = props;

  const isEdit = type === 'update';

  const [submitLoading, setSubmitLoading] = useState(false);

  const ruleFormRef = useRef(null);

  const onSubmit = async () => {
    setSubmitLoading(true);

    const values = await ruleFormRef.current.validateForm();
    if (values?.ScalingRuleMetric?.metricSource === 'prometheus') {
      delete values?.ScalingRuleMetric?.authMode;
      // delete values?.ScalingRuleMetric?.prometheusType;
    }

    if (!values) {
      setSubmitLoading(false);
      return false;
    }

    const { ScalingRuleTimers, ScalingRuleName, ...otherValues } = values;

    let res;
    if (values.ScalingRuleType === SCALE_RULE_TYPE.MIX && ScalingRuleTimers.length) {
      const rules = ScalingRuleTimers.map((timer, index) => ({
        ...otherValues,
        ScalingRuleTimer: {
          ...timer,
          schedules: formatMixTimerSchedules(timer.schedules, values.ScalingRuleMetric),
        },
        ScalingRuleName: index > 0 ? `${ScalingRuleName}#${index}` : ScalingRuleName,
      }));

      const oldScaleRuleNames = get(ruleData, 'ScaleRuleNames', []);
      const newScaleRuleNames = rules.map((rule) => rule.ScalingRuleName);

      const mergeNames = union(oldScaleRuleNames, newScaleRuleNames);

      res = await Promise.all(
        mergeNames.map((name) => {
          const ruleParams = rules.find((rule) => rule.ScalingRuleName === name);
          if (!oldScaleRuleNames.includes(name) && newScaleRuleNames.includes(name)) {
            return formRequest(ruleParams, 'create');
          }
          if (oldScaleRuleNames.includes(name) && newScaleRuleNames.includes(name)) {
            return formRequest(ruleParams, 'update');
          }
          if (oldScaleRuleNames.includes(name) && !newScaleRuleNames.includes(name)) {
            return formRequest(
              {
                ScalingRuleName: name,
              },
              'delete',
            );
          }
        }),
      );
    } else {
      res = await formRequest({
        ScalingRuleName,
        ...otherValues,
      });
    }
    setSubmitLoading(false);

    if (res && !(isArray(res) && some(res, isEmpty))) {
      Message.success(
        isEdit
          ? intl('saenext.basic-info.ScalingRuleSlide.EditedSuccessfully')
          : intl('saenext.basic-info.ScalingRuleSlide.CreatedSuccessfully'),
      );
      refresh?.();
      handleRefreshScalingRules?.();
      return true;
    } else {
      return false;
    }
  }

  const formRequest = async (values: any, action?) => {
    const actionMap = {
      create: services.createApplicationScalingRule,
      update: services.updateApplicationScalingRule,
      delete: services.deleteApplicationScalingRule,
    };

    const res = await actionMap[action || type]({
      params: {
        AppId: appId,
        ...values,
      },
    });
    return res;
  };

  return (
    <SlideWrap
      title={SLIDE_TITLE[type]}
      width={1200}
      onMaskClick={noop}
      onClose={noop}
      onCancel={noop}
      onOk={onSubmit}
      isProcessing={submitLoading}
      content={
        <>
          <Message type="warning" className="mb">
            <MultiLinesCollapse>
              <div>
                {intl('saenext.basic-info.ScalingRuleSlide.TheNewlyCreatedElasticPolicy')}
              </div>
              <div>
                {intl('saenext.basic-info.ScalingRuleSlide.InstancesScaledOutByThe.1')}
                <a href="https://help.aliyun.com/document_detail/100317.html" target="_blank">
                  {intl('saenext.basic-info.ScalingRuleSlide.OperatingInstructions')}
                </a>
                {intl("saenext.common.full-stop")}
              </div>
              <div>
                {intl('saenext.basic-info.ScalingRuleSlide.AfterTheAutoElasticPolicy')}
              </div>
              {/* <div>
                {intl(
                  'saenext.basic-info.ScalingRuleSlide.ApplicationsWithMonitoringIndicatorPolicies',
                )}
              </div> */}
              <If condition={isEdit}>
                <div>
                  {intl('saenext.basic-info.ScalingRuleSlide.AfterYouModifyAnElastic.1')}
                </div>
              </If>
            </MultiLinesCollapse>
          </Message>
          <ScaleRuleForm
            ref={ruleFormRef}
            type={type}
            ruleData={ruleData}
          />
        </>
      }
    >
      {children}
    </SlideWrap>
  )
}

export default ScaleRuleSlide;
