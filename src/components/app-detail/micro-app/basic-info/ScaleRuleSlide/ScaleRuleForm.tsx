import React, { forwardRef, useEffect, useImperativeHandle, useMemo } from 'react';
import { Button, Collapse, Dialog, Field, Form, Input, intl, Message, Radio } from '@ali/cnd';
import MetricSettings from './MetricSettings';
import EnabledWrap from './EnabledWrap';
import TimerSettingsContainer from './TimerSettings/TimerSettingsContainer';
import { forEach, isEmpty, some } from 'lodash';
import { SCALE_RULE_TYPE } from './constant';
import { parseRule } from './utils';
import TimerPreview from '../ScalingRuleSlide/TimerPreview';

const { Panel } = Collapse;

const ScaleRuleForm = (props, ref) => {
  const { type, ruleData, setRuleType } = props;

  const isEdit = type === 'update';
  const enabled = ruleData?.ScaleRuleEnabled;

  const field = Field.useField({
    parseName: true,
    values: parseRule(ruleData),
  });

  const { ScalingRuleType, ScalingRuleMetric, ScalingRuleTimer, ScalingRuleTimers } =
    field.getValues();
  const metricExist = !!ScalingRuleMetric;
  const timerExist = !!ScalingRuleTimer;
  const timerMixExist = !!ScalingRuleTimers;

  const defaultExpandedKeys = useMemo(() => {
    if (isEmpty(ruleData)) {
      return ['metric'];
    }
    const keys = [];
    if (metricExist) {
      keys.push('metric');
    }
    if (timerExist || timerMixExist) {
      keys.push('timer');
    }
    return keys;
  }, [isEdit, metricExist, timerExist, timerMixExist]);

  useEffect(() => {
    onTypeChange();
  }, [metricExist, timerExist, timerMixExist]);

  useImperativeHandle(ref, () => ({
    validateForm,
  }));

  const validateForm = async () => {
    const { errors, values } = await field.validatePromise();
    console.log('🚀 ~ errors:', errors, values);
    if (errors) {
      return;
    }
    return values;
  };

  const onTypeChange = () => {
    let type;
    if (metricExist && !timerExist && !timerMixExist) {
      type = SCALE_RULE_TYPE.METRIC;
    } else if (!metricExist && timerExist) {
      type = SCALE_RULE_TYPE.TIMING;
    } else if (metricExist && timerMixExist) {
      type = SCALE_RULE_TYPE.MIX;
    }
    field.setValues({
      ScalingRuleType: type,
    });
  };

  const onMetricOpen = () => {
    if (!metricExist && timerExist) {
      clearErrors('ScalingRuleTimer');
      field.remove('ScalingRuleTimer');
      field.setValue('ScalingRuleTimers', [{}]);
    }
    field.setValue('ScalingRuleMetric', {});
  };

  const onMetricClose = () => {
    if (metricExist && timerMixExist) {
      clearErrors('ScalingRuleTimers');
      field.remove('ScalingRuleTimers');
      field.setValue('ScalingRuleTimer', {});
    }
    clearErrors('ScalingRuleMetric');
    field.setValue('ScalingRuleMetric', null);
    field.remove(['ScalingRuleMetric']);
  };

  const onTimerOpen = () => {
    if (metricExist) {
      field.setValue('ScalingRuleTimers', [{}]);
    } else {
      field.setValue('ScalingRuleTimer', {});
    }
  };

  const onTimerClose = () => {
    clearErrors('ScalingRuleTimer');
    clearErrors('ScalingRuleTimers');
    field.setValues({
      ScalingRuleTimer: null,
      ScalingRuleTimers: null,
    });
    field.remove(['ScalingRuleTimer', 'ScalingRuleTimers']);
  };

  const clearErrors = (name) => {
    const errors = field.getErrors();
    forEach(errors, (error, key) => {
      if (key.startsWith(name)) {
        field.remove(key);
      }
    });
  };

  const onPreviewTimer = async (e) => {
    e.stopPropagation();
    const { errors, values } = await field.validatePromise();
    const timerError = some(errors, (error, key) => {
      return key.startsWith('ScalingRuleTimer') || key.startsWith('ScalingRuleTimers');
    });
    if (timerError) {
      return;
    }
    const { ScalingRuleType, ScalingRuleTimer, ScalingRuleTimers } = values;
    const ruleTimer =
      ScalingRuleType === SCALE_RULE_TYPE.MIX ? ScalingRuleTimers : [ScalingRuleTimer];

    Dialog.show({
      title: intl('saenext.basic-info.ScalingRuleSlide.constant.PreviewElasticPolicies'),
      style: { width: 900 },
      closeMode: ['close', 'mask', 'esc'],
      content: (
        <TimerPreview type={ScalingRuleType} ruleTimer={ruleTimer} ruleMetric={ScalingRuleMetric} />
      ),

      footer: false,
    });
  };

  const policyList = [
    {
      label: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.TimingPolicy'),
      value: SCALE_RULE_TYPE.TIMING,
    },
    {
      label: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.MonitoringMetricPolicy'),
      value: SCALE_RULE_TYPE.METRIC,
    },
    {
      label: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.HybridElasticStrategy'),
      value: SCALE_RULE_TYPE.MIX,
    },
  ];

  return (
    <Form field={field}>
      <Form.Item
        label={intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.PolicyType')}
        required
        className="none"
      >
        <Radio.Group
          shape="button"
          size="small"
          name="ScalingRuleType"
          defaultValue={SCALE_RULE_TYPE.METRIC}
          // onChange={onRuleTypeChange}
        >
          {policyList.map((item) => (
            <Radio value={item.value}>{item.label}</Radio>
          ))}
        </Radio.Group>
      </Form.Item>
      <Form.Item
        label={intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.PolicyName')}
        help={intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.EnterAStringOfTo')}
        required
        requiredMessage={intl(
          'saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.EnterAPolicyName',
        )}
        pattern={/^[a-z]([0-9a-z\-]{0,31})$/}
        patternMessage={intl(
          'saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.EnterAStringOfTo.1',
        )}
      >
        <Input
          name="ScalingRuleName"
          placeholder={intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.EnterAPolicyName')}
          disabled={isEdit}
          // style={{ width: '100%' }}
        />
      </Form.Item>
      <Collapse type="card" defaultExpandedKeys={defaultExpandedKeys}>
        <Panel
          title={intl('saenext.basic-info.ScaleRuleSlide.ScaleRuleForm.IndicatorRules')}
          className="mb"
          key="metric"
        >
          <EnabledWrap
            defaultOpen={metricExist || !isEdit}
            onOpen={onMetricOpen}
            onClose={onMetricClose}
            disabled={enabled}
            disabledMessage={intl(
              'saenext.basic-info.ScaleRuleSlide.ScaleRuleForm.TheCurrentElasticPolicyIs',
            )}
          >
            <MetricSettings field={field} />
          </EnabledWrap>
        </Panel>
        <Panel
          title={
            <>
              {intl('saenext.basic-info.ScaleRuleSlide.ScaleRuleForm.TimingRule')}

              <Button
                text
                type="primary"
                className="ml-s"
                style={{ height: 14 }}
                disabled={!timerExist && !timerMixExist}
                onClick={onPreviewTimer}
              >
                {intl('saenext.basic-info.ScaleRuleSlide.ScaleRuleForm.Preview')}
              </Button>
            </>
          }
          key="timer"
        >
          <EnabledWrap
            defaultOpen={timerExist || timerMixExist}
            onOpen={onTimerOpen}
            onClose={onTimerClose}
            disabled={enabled}
            disabledMessage={intl(
              'saenext.basic-info.ScaleRuleSlide.ScaleRuleForm.TheCurrentElasticPolicyIs',
            )}
          >
            <TimerSettingsContainer field={field} name="ScalingRuleTimer" isMix={metricExist} />
          </EnabledWrap>
        </Panel>
      </Collapse>
    </Form>
  );
};

export default forwardRef(ScaleRuleForm);
