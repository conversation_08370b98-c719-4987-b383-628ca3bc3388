import React, { useContext } from 'react';
import ReplicasSettings from './ReplicasSettings';
import MetricsTable from './MetricsTable';
import { Form, intl, Radio, Balloon, Icon } from '@ali/cnd';
import FeatureContext from '~/utils/featureContext';
import MicroAppContext from '~/utils/microAppContext';
import SimpleCollapse from '~/components/shared/SimpleCollapse';
import UpDownRules from '../../ScalingRuleSlide/MetricSettings/UpDownRules';
import PrometheusSettings from './PrometheusSettings';

const RadioGroup = Radio.Group;

const FORM_ITEM_LAYOUT = {
  labelAlign: 'left' as 'left',
  labelTextAlign: 'left' as 'left',
  labelCol: {
    fixedSpan: 8,
  },
  // wrapperCol: {
  //   span: 18,
  // },
};

const MetricSettings = (props) => {
  const { field } = props;
  const { feature } = useContext(FeatureContext);
  const { appConfig } = useContext(MicroAppContext);
  const enablePrometheusMetricsScaling = feature?.enablePrometheusMetricsScaling || false;

  return (
    <>
      <ReplicasSettings field={field} />
      {enablePrometheusMetricsScaling && (
        <Form.Item
          name="ScalingRuleMetric.metricSource"
          label={
            <>
              <span className="mr-s">
                {intl('saenext.ScaleRuleSlide.MetricSettings.IndicatorSource')}
              </span>
              <Balloon align="t" trigger={<Icon size="xs" type="help" />} closable={false}>
                <div>
                  <div>{intl('saenext.ScaleRuleSlide.MetricSettings.SystemIndexRefersToThe')}</div>
                  {appConfig?.NewSaeVersion !== 'lite' && (
                    <div>
                      {intl(
                        'saenext.ScaleRuleSlide.MetricSettings.PrometheusIndicatorRefersToPrometheus',
                      )}
                    </div>
                  )}
                </div>
              </Balloon>
            </>
          }
          {...FORM_ITEM_LAYOUT}
          required
        >
          <RadioGroup
            name="ScalingRuleMetric.metricSource"
            defaultValue="system"
            onChange={(v: string) => {
              field.setValue('ScalingRuleMetric.metricSource', v);
            }}
          >
            <Radio value="system">
              {intl('saenext.ScaleRuleSlide.MetricSettings.SystemMetrics')}
            </Radio>
            {appConfig?.NewSaeVersion !== 'lite' && (
              <Radio value="prometheus">
                {intl('saenext.ScaleRuleSlide.MetricSettings.PrometheusIndicators')}
              </Radio>
            )}
          </RadioGroup>
        </Form.Item>
      )}
      {(field.getValue('ScalingRuleMetric.metricSource') || 'system') === 'system' && (
        <Form.Item>
          <MetricsTable
            field={field}
            name="ScalingRuleMetric.metrics"
            defaultValue={[
              {
                metricType: 'CPU',
                metricTargetAverageUtilization: 50,
              },
            ]}
          />
        </Form.Item>
      )}
      {field.getValue('ScalingRuleMetric.metricSource') === 'prometheus' && (
        <Form.Item>
          <PrometheusSettings field={field} FORM_ITEM_LAYOUT={FORM_ITEM_LAYOUT} />
        </Form.Item>
      )}
      <SimpleCollapse
        type="primary"
        text={intl('saenext.ScalingRuleSlide.MetricSettings.AdvancedSettings')}
        defaultOpen={false}
        lazyLoad={false}
      >
        <div className="pd-card">
          <UpDownRules field={field} />
        </div>
      </SimpleCollapse>
    </>
  );
};

export default MetricSettings;
