import React, { useState, useEffect } from 'react';
import {
  intl,
  Button,
  Input,
  Form,
  Icon,
  NumberPicker,
  LinkButton,
  Balloon,
  Dialog,
  Radio,
} from '@ali/cnd';
import services from '~/services';
import If from '~/components/shared/If';
import ValueTable from '~/components/shared/ValueTable';
import { parser } from '@prometheus-io/lezer-promql';
import moment from 'moment';
import { get, map } from 'lodash';
import { Wline } from '@alife/aisc-widgets';
import CachedData from '~/cache/common';
const validatePromQL = (input) => {
  const tree = parser.parse(input);
  const errors = [];
  tree.iterate({
    enter(node) {
      if (node.type.isError) {
        const errorText =
          input.slice(node.from, node.to) || `Invalid syntax at position[${(node.from, node.to)}]`;
        errors.push({
          pos: [node.from, node.to],
          message: intl(
            'saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.SyntaxErrorErrortext',
            { errorText: errorText },
          ),
          severity: 'error',
        });
      }
    },
  });

  return errors;
};

const PrometheusMetricsTable = (props) => {
  const { field, name, value, onChange, appConfig, regionId } = props;
  const [queryDialogVisible, setQueryDialogVisible] = useState(false);
  const [currentQuery, setCurrentQuery] = useState('');
  const [currentQueryTime, setCurrentQueryTime] = useState('12_hours');
  const [queryRangData, setQueryRangData] = useState([]);
  const prometheusUrl = field.getValue('ScalingRuleMetric.prometheusUrl');
  const prometheusToken = field.getValue('ScalingRuleMetric.prometheusToken');
  useEffect(() => {
    if (prometheusUrl && queryDialogVisible) {
      handleQueryData();
    }
  }, [currentQueryTime, queryDialogVisible, prometheusUrl]);

  // PromQL查询语句合法性测试
  const handleQueryData = async () => {
    setQueryRangData([]);
    const time: any = currentQueryTime.split('_')[0];
    const unit: any = currentQueryTime.split('_')[1];
    let timeDiffInHours: any = time;
    if (unit === 'days') {
      timeDiffInHours = time * 24;
    }
    // 获取秒级时间戳
    const end = moment().unix();
    const start = moment().subtract(time, unit).unix();
    const res = await services.getAppScaleMonitor({
      params: {
        AppId: appConfig?.AppId,
        RegionId: regionId,
        StartTime: start,
        EndTime: end,
        MetricName: 'prometheus',
        Interval: timeDiffInHours < 12 ? 60 : (Math.floor(Number(timeDiffInHours)) / 24) * 60 * 10,
        PrometheusUrl: prometheusUrl,
        PrometheusToken: prometheusToken,
        PrometheusQuery: currentQuery,
        ScalarOnly: true,
      },
    });
    const data = get(res, 'Data', []);
    const newData = map(data, (item) => {
      return [item.Time * 1000, item.Value];
    });
    setQueryRangData([
      {
        name: intl(
          'saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.IndicatorData',
        ),
        data: newData,
      },
    ]);
  };

  const addRender = (onAdd) => {
    return (
      <Button className="mt-s" type="primary" text iconSize={'small'} onClick={() => onAdd()}>
        <Icon type="plus_fill" />
        <span className="ml-xs">
          {intl('saenext.MetricSettings.MetricsTable.AddTriggerCondition')}
        </span>
      </Button>
    );
  };

  const columns = ({ onDelete, onItemChange }) => {
    return [
      {
        title: (
          <>
            <span className="mr-s">
              {intl(
                'saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.IndicatorQueryStatement',
              )}
            </span>
            <Balloon align="t" trigger={<Icon size="xs" type="help" />} closable={false}>
             <div>
               {intl.html('saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.prometheusQuery.tip',{
                queryHref:'https://prometheus.io/docs/prometheus/latest/querying/basics/',
                aliyunPrometheusHref: CachedData.confLink('help:arms:add-and-use-prometheus-data-sources')
               })}
             </div>
           </Balloon>
          </>
        ),

        dataIndex: 'prometheusQuery',
        cell: (val, idx, record) => {
          return (
            <Form.Item
              required
              requiredMessage={intl(
                'saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.EnterAQueryStatement',
              )}
              validator={(rule, val, callback) => {
                if (val) {
                  const errors = validatePromQL(val);
                  if (errors.length) {
                    callback(errors[0].message);
                  } else {
                    callback();
                  }
                } else {
                  callback(
                    intl(
                      'saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.EnterAQueryStatement',
                    ),
                  );
                }
              }}
            >
              <Input
                name={`${name}.${idx}.prometheusQuery`}
                innerAfter={
                  <LinkButton
                    className="mr-s"
                    disabled={!(val && prometheusUrl)}
                    onClick={() => {
                      if (val) {
                        setCurrentQuery(val);
                        setQueryDialogVisible(true);
                      }
                    }}
                  >
                    {intl('saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.Query')}
                  </LinkButton>
                }
              />
            </Form.Item>
          );
        },
      },
      {
        dataIndex: 'equal',
        width: 10,
        cell: (val, idx, record) => '=',
      },
      {
        title: intl('saenext.MetricSettings.MetricsTable.TargetValue'),
        dataIndex: 'targetMetricValue',
        cell: (val, idx, record) => {
          return (
            <Form.Item
              required
              requiredMessage={intl(
                'saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.EnterATargetValue',
              )}
            >
              <NumberPicker
                style={{ width: '100%' }}
                // @ts-ignore
                name={`${name}.${idx}.targetMetricValue`}
              />
            </Form.Item>
          );
        },
      },
      {
        key: 'operation',
        width: 20,
        cell: (val, idx, record) => {
          return (
            <If condition={value.length > 1}>
              <Button
                text
                onClick={() => {
                  onDelete(idx);
                }}
              >
                <Icon type="close" />
              </Button>
            </If>
          );
        },
      },
    ];
  };

  return (
    <React.Fragment>
      <ValueTable
        field={field}
        name={name}
        value={value}
        onChange={onChange}
        maxLength={5}
        columns={columns}
        addRender={addRender}
      />

      <Dialog
        visible={queryDialogVisible}
        title={intl(
          'saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.HistoricalMetricData',
        )}
        onClose={() => {
          setQueryDialogVisible(false);
          setCurrentQuery('');
          setCurrentQueryTime('12_hours');
        }}
        onCancel={() => {
          setQueryDialogVisible(false);
          setCurrentQuery('');
          setCurrentQueryTime('12_hours');
        }}
        footerActions={['cancel']}
        style={{ width: 960 }}
      >
        <div>
          <div style={{ textAlign: 'right' }}>
            <Radio.Group
              shape="button"
              value={currentQueryTime}
              onChange={(val: string) => {
                setCurrentQueryTime(val);
              }}
            >
              <Radio value="12_hours">
                {intl('saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.LastHours')}
              </Radio>
              <Radio value="1_days">
                {intl('saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.LastDay')}
              </Radio>
              <Radio value="3_days">
                {intl(
                  'saenext.MetricSettings.PrometheusSettings.PrometheusMetricsTable.LastThreeDays',
                )}
              </Radio>
            </Radio.Group>
          </div>
          <div>
            <Wline height="300" data={queryRangData} />
          </div>
        </div>
      </Dialog>
    </React.Fragment>
  );
};

export default PrometheusMetricsTable;
