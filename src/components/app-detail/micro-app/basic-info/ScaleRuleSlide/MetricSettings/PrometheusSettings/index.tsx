import React, { useState, useEffect, useContext } from 'react';
import {
  Form,
  intl,
  Radio,
  Input,
  Select,
  Grid,
  LinkButton,
  Message,
  CndStatus,
  ToolTipCondition,
  ConsoleContext,
} from '@ali/cnd';
import services from '~/services';
import MicroAppContext from '~/utils/microAppContext';
import PrometheusMetricsTable from './PrometheusMetricsTable';
import { get, take, filter } from 'lodash';
import moment from 'moment';
const { Row, Col } = Grid;

const RadioGroup = Radio.Group;
const PrometheusSettings = (props) => {
  const { field, FORM_ITEM_LAYOUT, fromPage } = props;
  const prometheusType = field.getValue('ScalingRuleMetric.prometheusType') || 'custom';
  const { appConfig } = useContext(MicroAppContext);
  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const [networkStatus, setNetworkStatus] = useState('');

  useEffect(() => {
    // 编辑时，如果 token 存在，则设置 authMode 为 authorization
    if (
      field.getValue('ScalingRuleMetric.prometheusToken') &&
      field.setValue('ScalingRuleMetric.authMode') !== 'authorization'
    ) {
      field.setValue('ScalingRuleMetric.authMode', 'authorization');
    }
    setNetworkStatus('');
  }, [field.getValue('ScalingRuleMetric.prometheusToken')]);

  useEffect(() => {
    setNetworkStatus('');
  }, [
    field.getValue('ScalingRuleMetric.prometheusUrl'),
    field.getValue('ScalingRuleMetric.prometheusToken'),
    field.getValue('ScalingRuleMetric.authMode'),
  ]);

  const validateOnline = async () => {
    const prometheusUrl = field.getValue('ScalingRuleMetric.prometheusUrl');
    const prometheusToken = field.getValue('ScalingRuleMetric.prometheusToken') || '';
    const end = moment().unix();
    const start = moment().subtract(12, 'hours').unix();
    const res = await services.getAppScaleMonitor({
      params: {
        AppId: appConfig?.AppId,
        RegionId: regionId,
        StartTime: start,
        EndTime: end,
        MetricName: 'prometheus',
        Interval: 300,
        PrometheusUrl: prometheusUrl,
        PrometheusToken: prometheusToken,
        PrometheusQuery: 'time()',
        ScalarOnly: false,
      },
      customErrorHandle: (error, data, callback) => {
        console.log('error', error);
      },
    });
    if (res?.Success) {
      setNetworkStatus('success');
      Message.success(
        intl('saenext.MetricSettings.PrometheusSettings.NetworkConnectivityTestSucceeded'),
      );
    } else {
      setNetworkStatus('error');
      Message.error(
        intl('saenext.MetricSettings.PrometheusSettings.NetworkConnectivityTestFailed'),
      );
    }
  };

  const validatePrometheusMetrics = async (rule, value, callback) => {
    if (field.getValue('ScalingRuleMetric.metricSource') !== 'prometheus') {
      callback();
      return;
    }
    const prometheusUrl = field.getValue('ScalingRuleMetric.prometheusUrl');
    const prometheusToken = field.getValue('ScalingRuleMetric.prometheusToken') || '';
    const end = moment().unix();
    const start = moment().subtract(12, 'hours').unix();
    const res = await Promise.all(
      value.map(async (item) => {
        const res = await services.getAppScaleMonitor({
          params: {
            AppId: appConfig?.AppId,
            RegionId: regionId,
            StartTime: start,
            EndTime: end,
            MetricName: 'prometheus',
            Interval: 300,
            PrometheusUrl: prometheusUrl,
            PrometheusToken: prometheusToken,
            PrometheusQuery: item.prometheusQuery,
            ScalarOnly: true,
          },
        });
        const data = get(res, 'Data', []);
        return {
          query: item.prometheusQuery,
          data: take(data, 5),
        };
      }),
    );
    const errorItems = res.filter((items) => {
      const data = items.data;
      return data.length === 0 || data.some((item) => item.Value === 'NaN');
    });
    if (errorItems.length === 0) {
      callback();
    } else {
      callback(intl('saenext.MetricSettings.PrometheusSettings.validateMetrics.error',{query:errorItems[0]?.query}))
    }
  };

  return (
    <React.Fragment>
      {/* 先不透出，目前只支持自建Prometheus指标 */}
      {/* <Form.Item
        label={intl('saenext.MetricSettings.PrometheusSettings.PrometheusType')}
        {...FORM_ITEM_LAYOUT}
        required
      >
        <RadioGroup name="ScalingRuleMetric.prometheusType" defaultValue="custom" disabled>
          <Radio value="aliyun">
            {intl('saenext.MetricSettings.PrometheusSettings.AlibabaCloudPrometheus')}
          </Radio>
          <Radio value="custom">
            {intl('saenext.MetricSettings.PrometheusSettings.SelfBuiltPrometheus')}
          </Radio>
        </RadioGroup>
      </Form.Item> */}
      {prometheusType === 'custom' && (
        <>
          <Row gutter={8}>
            <Col span="22">
              <Form.Item
                label={intl('saenext.MetricSettings.PrometheusSettings.SelfBuiltPrometheusAddress')}
                {...FORM_ITEM_LAYOUT}
                required
                requiredMessage={intl(
                  'saenext.MetricSettings.PrometheusSettings.EnterPrometheusServerAddress',
                )}
              >
                <Input
                  name="ScalingRuleMetric.prometheusUrl"
                  placeholder={intl(
                    'saenext.MetricSettings.PrometheusSettings.EnterPrometheusServerAddress',
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={8}>
            <Col span="14">
              <Form.Item
                label={intl('saenext.MetricSettings.PrometheusSettings.AuthenticationMode')}
                {...FORM_ITEM_LAYOUT}
                required
              >
                <Select
                  defaultValue="none"
                  name="ScalingRuleMetric.authMode"
                  placeholder={intl(
                    'saenext.MetricSettings.PrometheusSettings.SelectAuthenticationMode',
                  )}
                  dataSource={[
                    {
                      label: 'Authorization',
                      value: 'authorization',
                    },
                    {
                      label: intl('saenext.MetricSettings.PrometheusSettings.NoAuthentication'),
                      value: 'none',
                    },
                  ]}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            {(field.getValue('ScalingRuleMetric.authMode') || 'none') === 'authorization' && (
              <Col span="8">
                <Form.Item
                  required
                  requiredMessage={intl('saenext.MetricSettings.PrometheusSettings.EnterAToken')}
                >
                  <Input
                    name="ScalingRuleMetric.prometheusToken"
                    placeholder={intl('saenext.MetricSettings.PrometheusSettings.EnterAToken')}
                  />
                </Form.Item>
              </Col>
            )}
            <Col style={{ lineHeight: '32px' }}>
              <LinkButton onClick={validateOnline}>
                {intl('saenext.MetricSettings.PrometheusSettings.ConnectionTest')}
              </LinkButton>
              {networkStatus && (
                <ToolTipCondition
                  show={networkStatus ? true : false}
                  tip={
                    networkStatus === 'success'
                      ? intl(
                          'saenext.MetricSettings.PrometheusSettings.NetworkConnectivityTestSucceeded',
                        )
                      : intl(
                          'saenext.MetricSettings.PrometheusSettings.NetworkConnectivityTestFailed',
                        )
                  }
                >
                  <CndStatus
                    type={networkStatus as any}
                    label={' '}
                    className="ml-xs"
                    style={{ display: 'inline-block', verticalAlign: 'middle' }}
                  />
                </ToolTipCondition>
              )}
            </Col>
          </Row>
        </>
      )}
      <Form.Item
        label={
          fromPage === 'old-scale-rule'
            ? intl('saenext.MetricSettings.PrometheusSettings.TriggerCondition')
            : ''
        }
        validator={validatePrometheusMetrics}
        validatorTrigger="submit"
      >
        <PrometheusMetricsTable
          field={field}
          appConfig={appConfig}
          regionId={regionId}
          name="ScalingRuleMetric.prometheusMetrics"
          defaultValue={[{}]}
        />
      </Form.Item>
    </React.Fragment>
  );
};

export default PrometheusSettings;
