import React, { useContext } from 'react';
import { intl } from '@ali/cnd';
import { Form, NumberPicker } from '@ali/cnd';
import MicroAppContext from '~/utils/microAppContext';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import MinReadyInstanceField from '~/components/shared/MinReadyInstance/MinReadyInstanceField';

const FORM_ITEM_LAYOUT = {
  labelAlign: 'left' as 'left',
  labelTextAlign: 'left' as 'left',
  labelCol: {
    fixedSpan: 8,
  },
  // wrapperCol: {
  //   span: 20,
  // },
};

const ReplicasSettings = (props) => {
  const { field } = props;

  const { resourceQuota: { InstancesPerApplication: instancesPerApplication } = 50 } =
    useContext(MicroAppContext);

  const { getValue, getValues } = field;

  const { ScalingRuleMetric: { minReplicas, maxReplicas } = {} as any } = getValues();

  return (
    <>
      <Form.Item
        required
        labelAlign="left"
        label={intl('saenext.ScaleRuleSlide.MetricSettings.ReplicasSettings.NumberOfInstances')}
        {...FORM_ITEM_LAYOUT}
      >
        <div className="flex">
          <Form.Item required requiredMessage=" ">
            <NumberPicker
              // @ts-ignore
              name="ScalingRuleMetric.minReplicas"
              defaultValue={2}
              min={1}
              max={maxReplicas - 1}
              style={{ width: 152 }}
            />
          </Form.Item>
          <span className="ml mr mb">-</span>
          <Form.Item required requiredMessage=" ">
            <NumberPicker
              // @ts-ignore
              name="ScalingRuleMetric.maxReplicas"
              defaultValue={instancesPerApplication}
              min={minReplicas + 1 || 0}
              max={instancesPerApplication}
              style={{ width: 152 }}
            />
          </Form.Item>
        </div>
      </Form.Item>
      {/* <Form.Item
         label={
           <TextWithBalloon
             text={intl('saenext.MetricSettings.ReplicasSettings.MinimumNumberOfSurvivingInstances')}
             tips={intl('saenext.MetricSettings.ReplicasSettings.TheMinimumNumberOfInstances')}
           />
         }
         {...FORM_ITEM_LAYOUT}
        >
         <MinReadyInstanceField field={field} />
        </Form.Item> */}
    </>
  );
};

export default ReplicasSettings;
