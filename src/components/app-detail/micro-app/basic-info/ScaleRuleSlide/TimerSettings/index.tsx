import { Form, intl, Select } from '@ali/cnd';
import { forEach, some } from 'lodash';
import React from 'react';
import SchedulesTable from '../../ScalingRuleSlide/TimerSettings/SchedulesTable';
import MixTimeTable from '../../ScalingRuleSlide/MixSettings/MixTimeTable';
import DateRangeField from './DateRangeField';
import C from '~/constants/common';
import { TimeZoneIds } from '~/components/shared/TimezoneSelect/constants';
import PeriodSelectField from './PeriodSelectField';
import { getFrequency } from './PeriodSelectField/utils';
import { EFrequencyType } from './PeriodSelectField/constant';
import moment from 'moment';

const TimerSettings = (props) => {
  const { field, name, isMix } = props;

  const peroidValidator = (rule, value, callback) => {
    const { curFrequency } = getFrequency(value);

    const { beginDate, endDate } = field.getValue(`${name}`) || {};
    if (beginDate && endDate) {
      const startMoment = moment(beginDate);
      const endMoment = moment(endDate);
      const daysDiff = endMoment.diff(startMoment, 'days');

      if (curFrequency === EFrequencyType.Week && daysDiff < 7) {
        callback(intl('saenext.ScaleRuleSlide.TimerSettings.TheStartAndEndDate'));
      } else if (curFrequency === EFrequencyType.Month && daysDiff < 30) {
        callback(intl('saenext.ScaleRuleSlide.TimerSettings.TheStartAndEndDate.1'));
      } else {
        callback();
      }
    } else {
      callback();
    }
  };

  // 相邻时间间隔不能小于5分钟
  const timeValidator = (rule, value, callback) => {
    const invalid = some(value, (item, index) => {
      const { atTime } = item;
      const time1 = new Date(`2000-01-01 ${atTime}`).getTime();
      return some(value, (item2, index2) => {
        if (index === index2) {
          return false;
        }
        const { atTime: atTime2 } = item2;
        const time2 = new Date(`2000-01-01 ${atTime2}`).getTime();
        if (Math.abs(time1 - time2) < 300000) {
          field.setErrors({
            [`${name}.schedules.${index}.atTime`]: '  ',
            [`${name}.schedules.${index2}.atTime`]: '  ',
          });
          return true;
        } else {
          return false;
        }
      });
    });
    if (invalid) {
      callback(intl('saenext.ScalingRuleSlide.TimerSettings.TheIntervalCannotBeLess'));
    } else {
      const errors = field.getErrors();
      forEach(errors, (error, key) => {
        if (key.startsWith(`${name}.schedules`) && error?.includes('  ')) {
          field.setError(key, '');
        }
      });
      callback();
    }
  };

  const mixTimeValidator = (rule, value, callback) => {
    const invalid = some(value, (item, index) => {
      const { startTime, endTime } = item;
      const start1 = new Date(`2000-01-01 ${startTime}`);
      const end1 = new Date(`2000-01-01 ${endTime}`);

      return some(value, (item2, index2) => {
        if (index === index2) {
          return false;
        }
        const { startTime: startTime2, endTime: endTime2 } = item2;
        const start2 = new Date(`2000-01-01 ${startTime2}`);
        const end2 = new Date(`2000-01-01 ${endTime2}`);
        if (
          (start2 < start1 && start1 < end2) ||
          (start2 < end1 && end1 < end2) ||
          (start1 <= start2 && end2 <= end1)
        ) {
          field.setErrors({
            [`${name}.schedules.${index}.startTime`]: '  ',
            [`${name}.schedules.${index}.endTime`]: '  ',
            [`${name}.schedules.${index2}.startTime`]: '  ',
            [`${name}.schedules.${index2}.endTime`]: '  ',
          });
          return true;
        } else {
          return false;
        }
      });
    });

    if (invalid) {
      callback(intl('saenext.ScalingRuleSlide.TimerSettings.SpecialTimePeriodsCannotOverlap'));
    } else {
      const errors = field.getErrors();
      forEach(errors, (error, key) => {
        if (key.startsWith(`${name}.schedules`) && error?.includes('  ')) {
          field.setError(key, '');
        }
      });
      callback();
    }
  };

  return (
    <>
      <Form.Item label={intl('saenext.ScaleRuleSlide.TimerSettings.TimeZone')} {...C.FORM_LAYOUT}>
        <Select
          name={`${name}.timeZone`}
          dataSource={TimeZoneIds}
          defaultValue={'Asia/Shanghai'}
          style={{ width: 418 }}
        />
      </Form.Item>
      <Form.Item
        label={intl('saenext.ScaleRuleSlide.TimerSettings.ValidityPeriod')}
        {...C.FORM_LAYOUT}
      >
        <DateRangeField field={field} name={name} beginName="beginDate" endName="endDate" />
      </Form.Item>

      <Form.Item
        label={intl('saenext.ScaleRuleSlide.TimerSettings.TimePeriod')}
        {...C.FORM_LAYOUT}
        required
        requiredMessage={intl('saenext.ScalingRuleSlide.TimerSettings.SelectATimePeriod')}
        validator={peroidValidator}
        autoValidate={false}
      >
        <PeriodSelectField name={`${name}.period`} defaultValue="* * *" />
      </Form.Item>

      {!isMix ? (
        <Form.Item
          // label={intl('saenext.ScalingRuleSlide.TimerSettings.TriggerTimeWithinASingle')}
          validator={timeValidator}
          extra={
            <div className="text-description mt">
              {intl('saenext.ScalingRuleSlide.TimerSettings.TheTriggerTimeOfThe')}

              <br />
              {intl('saenext.ScalingRuleSlide.TimerSettings.TheIntervalBetweenTwoAdjacent')}
            </div>
          }
        >
          <SchedulesTable
            field={field}
            defaultValue={[
              { atTime: '08:00', targetReplicas: 10 },
              { atTime: '20:00', targetReplicas: 3 },
            ]}
            name={`${name}.schedules`}
          />
        </Form.Item>
      ) : (
        <Form.Item
          // label={intl('saenext.ScalingRuleSlide.TimerSettings.TriggerTimeWithinASingle')}
          validator={mixTimeValidator}
          extra={
            <div className="text-description mt">
              {intl('saenext.ScalingRuleSlide.TimerSettings.EachTimePeriodCannotCoincide')}

              <br />
              {intl('saenext.ScalingRuleSlide.TimerSettings.EachTriggerIntervalMustBe')}

              <br />
              {intl('saenext.ScalingRuleSlide.TimerSettings.AddUpToSpecialTime')}

              <br />
              {intl('saenext.ScalingRuleSlide.TimerSettings.TheMinimumAndMaximumValues')}

              <br />
            </div>
          }
        >
          <MixTimeTable field={field} name={`${name}.schedules`} defaultValue={[{}]} />
        </Form.Item>
      )}
    </>
  );
};

export default TimerSettings;
