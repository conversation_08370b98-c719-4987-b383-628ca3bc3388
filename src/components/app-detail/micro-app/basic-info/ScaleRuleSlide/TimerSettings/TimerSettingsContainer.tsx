import React from 'react';
import TimerSettings from '.';
import { Button, Icon, intl } from '@ali/cnd';
import If from '~/components/shared/If';

const TimerSettingsContainer = (props) => {
  const { field, isMix } = props;

  const timerName = 'ScalingRuleTimer';
  const mixName = 'ScalingRuleTimers';
  const MixTimer = field.getValue(mixName) || [];

  return (
    <>
      {
        isMix ?
          <>
            <div className="next-form-item-help">
              {intl('saenext.ScalingRuleSlide.MixSettings.SetASpecificMaximumAnd')}
            </div>
            {MixTimer.map?.((item, idx) => {
              const number = idx + 1;
              return (
                <If condition={field.getValue(`${mixName}.${idx}`)}>
                  <div className="border-radius pd-card mt mb">
                    <div className='h3'>
                      {intl('saenext.ScalingRuleSlide.MixSettings.SelectTimeNumber', {
                        number: number,
                      })}
                      <If condition={MixTimer.length > 1}>
                        <Button
                          text
                          type='primary'
                          onClick={() => {
                            field.deleteArrayValue(mixName, idx);
                          }}
                        >
                          <Icon type="delete" />
                        </Button>
                      </If>
                    </div>
                    <TimerSettings field={field} name={`${mixName}.${idx}`} isMix />
                  </div>
                </If>
              );
            })}

            <Button
              disabled={MixTimer.length >= 2}
              onClick={() => {
                field.addArrayValue(mixName, MixTimer.length, {});
              }}
            >
              {intl('saenext.ScalingRuleSlide.MixSettings.AddASpecialTimePeriod')}
            </Button>
          </>
          :
          <TimerSettings field={field} name={timerName} isMix={false} />
      }
    </>
  )
}

export default TimerSettingsContainer;