import { intl } from '@ali/cnd';
import React from 'react';
import { Moment } from 'moment';
import { DatePicker, Radio } from '@ali/cnd';
import moment from 'moment';

const DateRange = (props) => {
  const { value = {}, onChange } = props;

  const { beginDate, endDate, periodSwitch } = value;

  const dateRange: [string | Moment, string | Moment] = [moment(beginDate), moment(endDate)];

  const handlePeriodSwitchChange = (val) => {
    if (val === 'long') {
      onChange({
        beginDate: null,
        endDate: null,
        periodSwitch: val,
      });
    } else {
      onChange({
        beginDate: moment().format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        periodSwitch: val,
      });
    }
  };

  const onRangeChange = ([beginDate = null, endDate = null]) => {
    onChange({
      beginDate: beginDate ? beginDate.format('YYYY-MM-DD') : null,
      endDate: endDate ? endDate.format('YYYY-MM-DD') : null,
      periodSwitch,
    });
  };

  const getDateError = () => {
    if (periodSwitch === 'short') {
      if (!beginDate || !endDate) {
        return 'error' as 'error';
      }
    }
  };

  return (
    <>
      <Radio.Group value={periodSwitch} onChange={handlePeriodSwitchChange}>
        <Radio value={'long'} label={intl('saenext.TimerSettings.DateRange.DateRange.LongTerm')} />
        <Radio value={'short'} label={intl('saenext.TimerSettings.DateRange.DateRange.ShortTerm')} />
      </Radio.Group>
      <div>
        {periodSwitch === 'short' && (
          <DatePicker.RangePicker
            disabledDate={(date) => date.startOf('day').valueOf() < moment().startOf('day').valueOf()}
            value={dateRange}
            onChange={onRangeChange}
            format="YYYY-MM-D"
            state={getDateError()}
          />
        )}
      </div>
      </>
  );
};

export default DateRange;

