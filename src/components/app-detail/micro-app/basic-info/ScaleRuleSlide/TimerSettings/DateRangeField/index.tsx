import { Input } from '@ali/cnd';
import React, { useRef, useState } from 'react';
import DateRange from './DateRange';

const DateRangeField = (props) => {
  const {
    field,
    name,
    beginName,
    endName
  } = props;

  const {
    init
  } = field;

  const beginDataName = `${name}.${beginName}`;
  const endDateName = `${name}.${endName}`;

  const beginDate = field.getValue(beginDataName);
  const endDate = field.getValue(endDateName);

  const [periodSwitch, setPeriodSwitch] = useState(!beginDate && !endDate ? 'long' : 'short');

  const validateDate = (rule, value, callback) => {
    if (periodSwitch === 'short') {
      if (!value) {
        callback('error');
      } else {
        callback();
      }
    } else {
      callback();
    }
  }

  return (
    <>
      <DateRange
        value={{
          beginDate,
          endDate,
          periodSwitch,
        }}
        onChange={(val) => {
          field.setValue(beginDataName, val.beginDate);
          field.setValue(endDateName, val.endDate);
          setPeriodSwitch(val.periodSwitch);
          field.validate(`${name}.period`);
        }}
      />
      <div className="none">
        <Input {...init(beginDataName, {
          initValue: null,
          rules: [
            {
              validator: validateDate,
            },
          ],
        })} />
        <Input {...init(endDateName, {
          initValue: null,
          rules: [
            {
              validator: validateDate,
            },
          ],
        })} />
      </div>
    </>
  )
}

export default DateRangeField;