import { intl } from '@ali/cnd';
import moment from 'moment';
import { EFrequencyType } from './constant';

export const getFrequency = (period = '') => {
  let curFrequency = EFrequencyType.Day;
  let frequencyList = [];

  const formatPeriod = period.split(' ');

  const [pos1, pos2, pos3] = formatPeriod;
  if ((pos1 !== undefined && pos2 !== undefined) || pos3 !== undefined) {
    if (pos1 === '*' && pos2 === '*' && pos3 === '*') {
      curFrequency = EFrequencyType.Day;
      frequencyList = ['*'];
    } else if (pos1 === '*' && pos2 === '*') {
      curFrequency = EFrequencyType.Week;
      frequencyList = pos3.split(',');
    } else if (pos2 === '*' && pos3 === '*') {
      curFrequency = EFrequencyType.Month;
      frequencyList = pos1.split(',');
      frequencyList = frequencyList.map((item) => parseInt(item));
    }
  }
  return {
    curFrequency,
    frequencyList,
  };
};

export const getWeekDayData = (type: EFrequencyType) => {
  const result = [];

  if (type === EFrequencyType.Week) {
    for (let i = 0; i < 7; i++) {
      const day = moment().startOf('week').add(i, 'days');

      const label = day.format('ddd'); // 周一
      const value = day.locale('en').format('ddd'); // Mon
      result.push({
        label,
        value,
      });
    }
  }

  if (type === EFrequencyType.Month) {
    for (let i = 1; i <= 31; i++) {
      result.push({
        label: i,
        value: i,
      });
    }
  }
  return result;
};
