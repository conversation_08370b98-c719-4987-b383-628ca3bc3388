import React, { useMemo } from 'react';
import { Select } from '@ali/cnd';
import { EFrequencyType, FrequencyTypeList } from './constant';
import { getWeekDayData } from './utils';
import { findIndex, sortBy } from 'lodash';

const PeriodSelect = (props) => {
  const { value, onChange } = props;

  const { curFrequency, frequencyList } = value;

  const frequencyData = useMemo(() => {
    const result = getWeekDayData(curFrequency);
    return result;
  }, [curFrequency]);

  const handleFrequencyChange = (val) => {
    onChange({
      curFrequency: val,
      frequencyList: [],
    });
  };

  const onFrequencySelectChange = (val) => {
    const valSort = sortFrequency(val);
    onChange({
      curFrequency,
      frequencyList: valSort,
    });
  };

  const sortFrequency = (val) => {
    if (!val) return val;
    const sortedVal = sortBy(val, item =>
      findIndex(frequencyData, { value: item })
    );
    return sortedVal;
  }

  return (
    <div className='input-border'>
      <Select
        hasBorder={false}
        value={curFrequency}
        dataSource={FrequencyTypeList}
        onChange={handleFrequencyChange}
        className='condition-item'
      />
      {curFrequency !== EFrequencyType.Day && (
        <Select
          hasBorder={false}
          hasClear
          hasSelectAll
          mode='multiple'
          tagInline
          value={frequencyList}
          onChange={onFrequencySelectChange}
          dataSource={frequencyData}
          style={{ width: 316 }}
          popupClassName='select-flex-content'
          popupStyle={{ width: 316 }}
        />
      )}
    </div>
  );
};

export default PeriodSelect;
