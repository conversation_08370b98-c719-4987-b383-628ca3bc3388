import { lowerFirstData } from "~/utils/transfer-data";
import { SCALE_RULE_TYPE } from "./constant";
import { cloneDeep, forEach, isEmpty, last, map } from "lodash";

export const parseRule = (rule) => {
  if (isEmpty(rule)) {
    return {};
  }

  const {
    ScaleRuleName: ScalingRuleName,
    ScaleRuleType: ScalingRuleType,
    ScaleRuleEnabled: ScalingRuleEnable,
    // EnableIdle = false,
    Timer = {},
    Timers = [],
    Metric = {},
  } = rule;

  const ScalingRuleMetric = cloneDeep(Metric);
  delete ScalingRuleMetric.MetricsStatus;

  const timers =
    ScalingRuleType === SCALE_RULE_TYPE.MIX ?
      isEmpty(Timers) ?
        parseTimersToMix({ Timers: [Timer], Metric })
        : parseTimersToMix({ Timers, Metric })
      : [];

  return {
    ScalingRuleName,
    ScalingRuleType,
    ScalingRuleEnable,
    // EnableIdle,
    ScalingRuleTimer: ScalingRuleType === SCALE_RULE_TYPE.TIMING ? lowerFirstData(cloneDeep(Timer)) : null,
    ScalingRuleTimers: ScalingRuleType === SCALE_RULE_TYPE.MIX ? lowerFirstData(cloneDeep(timers)) : null,
    ScalingRuleMetric: ScalingRuleType !== SCALE_RULE_TYPE.TIMING ? lowerFirstData(ScalingRuleMetric) : null,
  }
}

export const parseTimersToMix = (rule) => {
  const { Timers, Metric } = cloneDeep(rule);
  const mixTimers = map(Timers, (timer) => {
    const { Schedules } = timer;
    timer.Schedules = parseMixTimerSchedules(Schedules, Metric);
    return timer;
  })
  return mixTimers;
}

export const formatMixTimerSchedules = (timers = [], {minReplicas:  defaultMin = 0, maxReplicas: defaultMax = 0 }, isChart?: boolean) => {
  timers.sort((a, b) => {
    return parseTime(a.startTime) - parseTime(b.startTime);
  });

  const res = [];
  forEach(timers, (schedule, index) => {
    const {
      startTime,
      endTime,
      minReplicas,
      maxReplicas,
    } = schedule;

    // 时间段开始使用设置的最大最小值
    res.push({
      atTime: startTime,
      minReplicas,
      maxReplicas,
    });

    // 结束时间是下一时间开始，使用下一段时间的值(为了兼容，和1.0保持一致)
    const nextTimer = timers[index + 1];
    if (endTime === nextTimer?.startTime) {
      res.push({
        atTime: endTime,
        minReplicas: nextTimer.minReplicas,
        maxReplicas: nextTimer.maxReplicas,
      });
      return;
    }

    // 23:59特殊传参
    if (endTime === '23:59' && !isChart) {
      res.push({
        atTime: endTime,
        minReplicas,
        maxReplicas,
      });
      return;
    }

    // 时间段外恢复默认值
    res.push({
      atTime: endTime,
      minReplicas: defaultMin,
      maxReplicas: defaultMax,
    });
  })
  return res;
}

export const parseMixTimerSchedules = (schedules = [] as any, metric) => {
  schedules.sort((a, b) => {
    return parseTime(a.AtTime) - parseTime(b.AtTime);
  });

  const res = [];

  forEach(schedules, (item, index) => {
    const {
      AtTime,
      MinReplicas,
      MaxReplicas,
    } = item;

    const lastItem = last(res);
    if (!res.length || lastItem.startTime && lastItem.endTime) {
      res.push({
        startTime: AtTime,
        minReplicas: MinReplicas,
        maxReplicas: MaxReplicas,
      });
    } else if (lastItem.startTime && !lastItem.endTime) {
      if (lastItem.startTime === AtTime) return;
      lastItem.endTime = AtTime;

      // 结束后实例范围未恢复默认，则为新一特殊时间段的开始
      if (MinReplicas !== metric.MinReplicas || MaxReplicas !== metric.MaxReplicas) {
        if (AtTime === '23:59') return;
        res.push({
          startTime: AtTime,
          minReplicas: MinReplicas,
          maxReplicas: MaxReplicas,
        });
      }
    }
  })
  return res;
}

const parseTime = (time) => {
  const [hour, minute] = time.split(':');
  const res = parseInt(hour, 10) * 60 + parseInt(minute, 10);
  return res;
}

/**
 * 生成时间范围
 * @param timeStr: string // '09:00'
 *
 * @returns: string[] // 前后5分钟的数组 ['08:55', '08:56', '08:57', '08:58', '08:59', '09:00', '09:01', '09:02', '09:03', '09:04', '09:05']
 */
export const generate5MinuteTimeRange = (timeStr) => {
  if (!timeStr) return [];
  const [hour, minute] = timeStr.split(':').map(Number);
  const startTime = new Date();
  startTime.setHours(hour);
  startTime.setMinutes(minute - 5);
  startTime.setSeconds(0);

  const result = [];
  for (let i = 0; i <= 10; i++) {
      const formattedMinute = String(startTime.getMinutes()).padStart(2, '0');
      const formattedHour = String(startTime.getHours()).padStart(2, '0');
      const currentTimeStr = `${formattedHour}:${formattedMinute}`;
      result.push(currentTimeStr);
      
      startTime.setMinutes(startTime.getMinutes() + 1);
  }

  return result;
}

