import { intl } from '@ali/cnd';
import React, { useContext } from 'react';
import { Message } from '@ali/cnd';
import If from '~/components/shared/If';
import MicroAppContext from '~/utils/microAppContext';
import CachedData from '~/cache/common';

const AppNotice = (props) => {
  const {
    appConfig,
    appStatus,
  } = props;

  const { mscUpdateStatus } = useContext(MicroAppContext);

  const { LastChangeOrderRunning, CurrentStatus } = appStatus;
  const isAppUnCreated = CurrentStatus === 'UNKNOWN';

  if (isAppUnCreated && !LastChangeOrderRunning) {
    return (
      <Message type="notice" style={{ marginBottom: 16 }}>
        <p>{intl('saenext.basic-info.AppNotice.YourApplicationHasNotBeen')}</p>
      </Message>
    );
  } else {
    return (
      <Message type="notice" style={{ marginBottom: 8 }}>
        <div className='text-line'>
          {intl('saenext.basic-info.AppNotice.ByDefaultApplicationsCannotAccess')}
          <a href={CachedData.confLink('help:sae:configure-a-nat-gateway')} target="_blank">
            {intl('saenext.basic-info.AppNotice.HowDoApplicationsAccessThe')}
          </a>
          {intl("sae.common.comma")}
          <a href={CachedData.confLink('help:sae:best-practices-of-internet-access')} target="_blank">
            {intl('saenext.basic-info.AppNotice.BestExamplesOfCommonInternet')}
          </a>
        </div>
        <div className='text-line'>
          {intl('saenext.basic-info.AppNotice.IfYourApplicationNeedsTo')}

          <a href={CachedData.confLink('help:sae:configure-a-whitelist-for-rds')} target="_blank">
            {intl('saenext.basic-info.AppNotice.HowToConfigureAnRds')}
          </a>
        </div>
        <If condition={appConfig.SaeVersion === 'v1' && mscUpdateStatus === 'enabled'}>
          <div className='text-line'>
            {intl('saenext.basic-info.AppNotice.IfYourApplicationWantsTo')}

            <a
              href={CachedData.confLink('help:mse:microservice-governance-edition-selection')}
              target="_blank"
            >
              {intl('saenext.basic-info.AppNotice.VersionSelection')}
            </a>
            {intl('saenext.basic-info.AppNotice.And')}

            <a
              href={CachedData.confLink('help:mse:billing-overview')}
              target="_blank"
            >
              {intl('saenext.basic-info.AppNotice.BillingOverview')}
            </a>
          </div>
        </If>
      </Message>
    );
  }
};

export default AppNotice;
