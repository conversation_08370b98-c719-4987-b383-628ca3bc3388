import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useState } from 'react';
import FeatureContext from '~/utils/featureContext';
import ScaleList from '../ScaleList';
import ScalingList from '../ScalingGroup';
import { Button } from '@ali/cnd';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';
import { getCookieByKeyName, setCookieByKeyName } from '~/utils/global';

const ScaleListContainer = (props) => {
  const { feature } = useContext(FeatureContext);
  const { newScalingRulePage } = feature;

  const mainUid = CachedData.getMainUserId();

  const defaultShowNew =
    newScalingRulePage && getCookieByKeyName(`${mainUid}-scale-rule-showNew`) !== 'false';

  const [showNew, setShowNew] = useState(defaultShowNew);

  useEffect(() => {
    setShowNew(defaultShowNew);
  }, [defaultShowNew]);

  const onChangeVersion = () => {
    setCookieByKeyName(`${mainUid}-scale-rule-showNew`, `${!showNew}`, 7);
    setShowNew(!showNew);
  };

  const renderList = () => {
    if (showNew) {
      return <ScaleList {...props} />;
    } else {
      return <ScalingList {...props} />;
    }
  };

  return (
    <div className="relative">
      <If condition={newScalingRulePage}>
        <div className="absolute" style={{ right: 10, top: 10, zIndex: 10 }}>
          <Button text type="primary" onClick={onChangeVersion}>
            {showNew
              ? intl('saenext.basic-info.ScaleListContainer.SwitchBackToTheOld')
              : intl('saenext.basic-info.ScaleListContainer.ExperienceTheNewVersion')}
          </Button>
        </div>
      </If>
      {renderList()}
    </div>
  );
};

export default ScaleListContainer;
