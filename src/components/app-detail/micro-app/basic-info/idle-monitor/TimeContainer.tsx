import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { DatePicker } from '@ali/cnd';
import moment from 'moment';

const { RangePicker } = DatePicker;

interface Props {
  field: any;
  initValue: any;
  timeInitValue: any;
  onTimeChanged: Function;
  width?: any;
}
interface State {}

export default class TimeContainer extends Component<Props, State> {
  state = {};

  disabledDate = (date) => {
    return (
      date.valueOf() > moment().valueOf() || date.valueOf() < moment().subtract(1, 'days').valueOf()
    );
  };

  onRangeOk = (value) => {
    const { field, onTimeChanged } = this.props;
    const [start, end] = value;
    onTimeChanged({ start: start.valueOf(), end: end.valueOf(), key: 'custom' });
    field.setValue('time', value);
  };

  render() {
    const { field, initValue, width = 470 } = this.props;
    const { init } = field;
    return (
      <RangePicker
        label={intl('saenext.components.idle-monitor.TimeContainer.TimeRange')}
        style={{ width, height: 32 }}
        {...init('time', {
          initValue: [moment(initValue[0]), moment(initValue[1])],
        })}
        showTime={{ format: 'HH:mm:ss' }}
        disabledDate={this.disabledDate}
        onOk={this.onRangeOk}
      />
    );
  }
}
