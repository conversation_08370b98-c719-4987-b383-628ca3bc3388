import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wpie } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card } from '@ali/cnd';
import { MonitorUnit } from './index';

type Props = {
  monitorUnit: string;
  data: number[];
};

const WpieOptions = {
  area: true,
  legend: {
    visible: true,
    position: 'bottom',
    align: 'left',
    marker: {
      symbol: 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 3,
          lineWidth: 6,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
  },
  symbol: false,
  spline: true,
  slider: false,
  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
  },
  // @ts-ignore yellow green
  colors: ['#FAC31E', '#23b066'],
};

export default (props: Props) => {
  const { data: dataSource, monitorUnit } = props;
  const [data, setData] = useState([]);

  useEffect(() => {
    initChart();
  }, [JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    oneIndicator();
  };

  const oneIndicator = () => {
    const [activeValue, idleValue] = dataSource;

    const _data = [
      {
        name: intl('saenext.components.idle-monitor.CpuUtilizationIndicator.CpuResourceUsage'),
        data: [
          [
            intl('saenext.components.idle-monitor.CpuUtilizationIndicator.IdleCpuResourceUsage'),
            idleValue,
          ],

          [
            intl('saenext.components.idle-monitor.CpuUtilizationIndicator.ActiveCpuResourceUsage'),
            activeValue,
          ],
        ],
      },
    ];

    setData(_data);
  };

  const type =
    monitorUnit === MonitorUnit.App
      ? intl('saenext.components.idle-monitor.CpuUtilizationIndicator.Application')
      : intl('saenext.components.idle-monitor.CpuUtilizationIndicator.Instance');

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={intl(
          'saenext.components.idle-monitor.CpuUtilizationIndicator.TypeCpuResourceUsageWhen',
          { type: type },
        )}
        style={{ flex: 1, marginLeft: 4 }}
      >
        <Wpie
          data={data}
          height={240}
          // @ts-ignore
          config={WpieOptions}
        />
      </Card>
    </>
  );
};
