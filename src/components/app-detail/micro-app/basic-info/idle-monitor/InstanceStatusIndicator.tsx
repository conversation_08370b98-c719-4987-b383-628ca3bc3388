import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wline } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { map, get } from 'lodash';
import { Card } from '@ali/cnd';

type IMetricsProps = {
  timestamp: number;
  Value: number;
  [key: string]: any;
};
type Props = {
  data: {
    AppInstanceTotalIdle?: IMetricsProps[];
    AppInstanceTotalActive?: IMetricsProps[];
  };
};

const WlineOptions = {
  xAxis: {
    type: 'timeCat',
    mask: 'HH:mm',
  },
  yAxis: {
    min: 0,
    max: 1,
    tickMethod: 'integer',
  },
  area: true,
  legend: {
    visible: true,
    position: 'bottom',
    align: 'left',
    marker: {
      symbol: 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 4,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
  },
  symbol: false,
  spline: true,
  slider: false,
  tooltip: {
    visible: true,
  },
  // @ts-ignore purple green yellow
  colors: ['#23b066', '#FAC31E'],
};

export default (props: Props) => {
  const { data: dataSource } = props;
  const [data, setData] = useState([]);

  useEffect(() => {
    initChart();
  }, [JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    oneIndicator();
  };

  const oneIndicator = () => {
    const activeInstances = get(dataSource, 'AppInstanceTotalActive');
    const totalInstances = get(dataSource, 'AppInstanceTotalIdle');
    const _data = [
      {
        name: intl('saenext.components.idle-monitor.InstanceStatusIndicator.Active'),
        type: 'line',
        yAxis: 0,
        data: map(activeInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Value];
        }),
      },
      {
        name: intl('saenext.components.idle-monitor.InstanceStatusIndicator.Idle'),
        type: 'line',
        yAxis: 0,
        data: map(totalInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Value];
        }),
      },
    ];

    setData(_data);
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={intl('saenext.components.idle-monitor.InstanceStatusIndicator.InstanceIdleStatus')}
        style={{ flex: 1, marginRight: 4 }}
      >
        <Wline
          data={data}
          height={240}
          // @ts-ignore
          config={WlineOptions}
        />
      </Card>
    </>
  );
};
