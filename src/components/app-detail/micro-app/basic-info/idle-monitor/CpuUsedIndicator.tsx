import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wline } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card } from '@ali/cnd';
import moment from 'moment';

type Props = {
  data: [number, number][];
};

export default (props: Props) => {
  const { data: dataSource } = props;
  const [data, setData] = useState([]);

  useEffect(() => {
    initChart();
  }, [JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    oneIndicator();
  };

  const oneIndicator = () => {
    const _data = [
      {
        name: intl('saenext.components.idle-monitor.CpuUsedIndicator.InstanceCpu'),
        data: dataSource,
      },
    ];

    setData(_data);
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={intl('saenext.components.idle-monitor.CpuUsedIndicator.CpuUsageCore')}
        style={{ flex: 1, marginRight: 4 }}
      >
        <Wline
          data={data}
          height={240}
          config={{
            symbol: false,
            spline: true,
            slider: false,
            tooltip: {
              visible: true,
              showTitle: true,
              showColon: true,
              titleFormatter: (v, t) => {
                return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
              },
            },
            colors: ['#796fff'],
            legend: {
              visible: true,
              position: 'bottom',
              align: 'left',
              marker: {
                symbol: 'hyphen',
                style(oldStyle) {
                  return {
                    ...oldStyle,
                    r: 3,
                    lineWidth: 6,
                    fill: oldStyle.stroke,
                    lineAppendWidth: 0,
                  };
                },
              },
            },
          }}
        />
      </Card>
    </>
  );
};
