.monitor-status-container-text {
  min-width: 40px;
  box-sizing: border-box;
  display: inline-block;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 2px;
  text-align: center;
  color: #ffffff;
  transition: 0.2s;
  cursor: pointer;
  &.clear-font-styles {
    padding: 0 2px;
    height: 14px !important;
    line-height: 14px !important;
    color: #9b9ea0;
    background: #eee;
  }
  &.solid {
    height: 22px;
    line-height: 22px;
  }
  &.simple {
    height: 22px;
    line-height: 22px;
    &:hover {
      opacity: 0.85;
    }
  }
}

.monitor-status-container-simple-normal {
  color: rgba(0, 0, 0, 0.65);
  background: #fafafa;
  border: 1px solid #d9d9d9;
}

.monitor-bars {
  margin-bottom: 8px;
  .next-input-label {
    padding-left: 0 !important;
    .select-label {
      height: 26px;
      line-height: 26px;
      border-radius: 2px;
      padding: 0 8px;
      margin: 0 2px;
      font-size: 12px;
      color: rgb(51, 51, 51);
      background-color: #F7F9FA;
    }
  }
}

.monitor-aisc {
  width: 100%;
  height: 100%;
  .next-loading-wrap {
    width: 100%;
    height: 100%;
  }
}