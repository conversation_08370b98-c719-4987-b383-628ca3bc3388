import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Select, Loading } from '@ali/cnd';
import services from "~/services";
import { get, map, forEach, find, isEmpty, ceil } from 'lodash';
import MonitorTimeRange, { getTimes } from './TimeRangeMonitor';
import InstanceCountIndicator from './InstanceCountIndicator';
import InstanceStatusIndicator from './InstanceStatusIndicator';
import CpuUtilizationIndicator from './CpuUtilizationIndicator';
import CpuUsedIndicator from './CpuUsedIndicator';
import CachedData from '../../../../../cache/common';
import NetRecvTranIndicator from './NetRecvTranIndicator';
import If from '../../../../shared/If';

type Props = {
  appId: string;
};

const AppMetrics = ['AppTotalActive', 'AppTotalIdle'];
const InstanceMetrics = [
  'AppInstanceTotalActive',
  'AppInstanceTotalIdle',
  'instance_cpu',
  'instance_netRecv',
];
const DivisorTimes = {
  last_15_minutes: 15,
  last_half_hour: 30,
  last_hour: 60,
  last_3_hours: 180,
  last_6_hours: 360,
  custom: 'custom',
};
export const MonitorUnit = {
  App: 'application',
  Instance: 'instance',
};

export default (props: Props) => {
  const { appId } = props;
  const timeInitValue = 'last_15_minutes';
  const [start, end] = getTimes(timeInitValue);
  const [timeTuple, setTimeTuple] = useState({ start, end, key: timeInitValue });
  const [instances, setInstances] = useState([]);
  const [instanceId, setInstanceId] = useState('');
  const [monitorUnit, setMonitorUnit] = useState(MonitorUnit.App);
  const [loading, setLoading] = useState(false);
  const [dataPoints, setDataPoints] = useState({});
  const userId = CachedData.getAliyunConsoleConfig('MAIN_ACCOUNT_PK');

  useEffect(() => {
    getInstances2App();
    getMetricsDatapoints();
  }, []);

  const getInstances2App = async () => {
    const res = await services.getApplicationGroups({
      params: { AppId: appId },
      customErrorHandle(err, data, callback) {
        callback && callback();
      },
    });
    const groups = get(res, 'Data', []);
    const options = [];
    forEach(groups, (group) => {
      const { GroupId } = group;
      options.push(getInstances2Group(GroupId));
    });
    let _instances = await Promise.all(options);
    _instances = _instances.flat();
    setInstances(_instances);
  };

  const getInstances2Group = async (groupId) => {
    const res = await services.getApplicationInstances({
      params: {
        AppId: appId,
        GroupId: groupId,
        CurrentPage: 1,
        PageSize: 100,
        Reverse: false,
      },
      customErrorHandle(err, data, callback) {
        callback && callback();
        return [];
      },
    });
    const instances = get(res, 'Data.Instances', []);
    const _instances = map(instances, (instance) => {
      const { InstanceId } = instance;
      return {
        ...instance,
        label: InstanceId,
        value: InstanceId,
      };
    });
    return _instances;
  };

  const getMetricsDatapoints = async (
    _monitorUnit?: string,
    _instanceId?: string,
    _timeTuple?: { start: number; end: number; key: string },
  ) => {
    setLoading(true);
    const __monitorUnit = _monitorUnit || monitorUnit;
    const __instanceId = _instanceId || instanceId;
    const __timeTuple = _timeTuple || timeTuple;
    const metrics = __monitorUnit === MonitorUnit.App ? AppMetrics : InstanceMetrics;
    const options = [];
    forEach(metrics, async (metricName) => {
      options.push(getDatapoints(metricName, __monitorUnit, __instanceId, __timeTuple));
    });
    const res = await Promise.all(options);
    const _dataPoints = {};
    forEach(res, (item) => {
      const { metric } = item;
      _dataPoints[metric] = item.dimensions;
    });
    if (__monitorUnit === MonitorUnit.App) {
      // 总实例数通过闲置和活跃数相加
      const _totalInstances = getTotalInstances(
        get(_dataPoints, 'AppTotalIdle', []),
        get(_dataPoints, 'AppTotalActive', []),
      );
      _dataPoints['ApplicationInstances'] = _totalInstances;
    }
    setDataPoints(_dataPoints);
    setLoading(false);
  };

  const cpuUsedPoints = React.useMemo(() => {
    if (monitorUnit === MonitorUnit.App) return [];
    const cpuUsedData = get(dataPoints, 'instance_cpu', []).map((val) => {
      return [val.timestamp, val.Maximum];
    });
    return cpuUsedData as [number, number][];
  }, [monitorUnit, dataPoints]);

  const cpuPoints = React.useMemo(() => {
    const idleKey = monitorUnit === MonitorUnit.App ? 'AppTotalIdle' : 'AppInstanceTotalIdle';
    const activeKey = monitorUnit === MonitorUnit.App ? 'AppTotalActive' : 'AppInstanceTotalActive';
    const idlePoints = get(dataPoints, idleKey, []);
    const activePoints = get(dataPoints, activeKey, []);
    let idleValue = 0,
      activeValue = 0;
    if (!isEmpty(idlePoints)) {
      forEach(idlePoints, (point) => {
        idleValue += point.Value;
      });
    }
    if (!isEmpty(activePoints)) {
      forEach(activePoints, (point) => {
        activeValue += point.Value;
      });
    }
    let _idleValue = 0,
      _activeValue = 0;
    const totalValue = idleValue + activeValue;
    if (!totalValue) {
      return [0, 0];
    }
    if (!idleValue) {
      return [100, 0];
    }
    _idleValue = ceil(idleValue / totalValue, 2);
    _idleValue = _idleValue * 100;
    _activeValue = 100 - _idleValue;
    return [_activeValue, _idleValue];
  }, [monitorUnit, dataPoints]);

  const netPoints = React.useMemo(() => {
    if (monitorUnit === MonitorUnit.App) return [];
    const cpuUsedData = get(dataPoints, 'instance_netRecv', []).map((val) => {
      return [val.timestamp, val.Maximum];
    });
    return cpuUsedData as [number, number][];
  }, [monitorUnit, dataPoints]);

  const getDatapoints = async (
    metricName,
    _monitorUnit?: string,
    _instanceId?: string,
    _timeTuple?: { start: number; end: number; key: string },
  ) => {
    const __monitorUnit = _monitorUnit || monitorUnit;
    const __instanceId = _instanceId || instanceId;
    const __timeTuple = _timeTuple || timeTuple;
    let dimensions = {
      userId: userId,
      appId: appId,
    };
    if (__monitorUnit === MonitorUnit.Instance) {
      Reflect.set(dimensions, 'instanceId', __instanceId);
    }
    const res = await services.getMetricsDatapoints({
      params: {
        MetricName: metricName,
        StartTime: __timeTuple.start,
        EndTime: __timeTuple.end,
        Dimensions: dimensions,
      },
      customErrorHandle(err, data, callback) {
        callback && callback();
        setLoading(false);
        return [];
      },
    });
    let _dimensions = get(res, 'Datapoints', '[]');
    return { metric: metricName, dimensions: JSON.parse(_dimensions) };
  };

  const getTotalInstances = (idleInstances, activeInstances) => {
    let totalInstances = [];
    if (isEmpty(idleInstances)) {
      totalInstances = activeInstances;
    }
    if (isEmpty(activeInstances)) {
      totalInstances = idleInstances;
    }

    totalInstances = map(idleInstances, (instance) => {
      const activeInstance = find(activeInstances, { timestamp: instance.timestamp });
      let _value = instance.Value;
      if (activeInstance) {
        _value += activeInstance.Value;
      }
      return {
        ...instance,
        Value: _value,
      };
    });
    return totalInstances;
  };

  const changeMonitorUnit = (value) => {
    setMonitorUnit(value);
    let _instanceId = '';
    if (value === MonitorUnit.Instance) {
      const _instance = instances[0];
      _instanceId = get(_instance, 'InstanceId', '');
    }
    setInstanceId(_instanceId);
    getMetricsDatapoints(value, _instanceId);
  };

  const changeMonitorInstance = (value) => {
    setInstanceId(value);
    getMetricsDatapoints(MonitorUnit.Instance, value);
  };

  const onMonitorTimeChange = (value) => {
    const { start, end, key } = value;
    setTimeTuple({ start, end, key });
    getMetricsDatapoints(undefined, undefined, { start, end, key });
  };

  return (
    <>
      <div className="monitor-bars">
        <Select
          value={monitorUnit}
          style={{ width: 256 }}
          dataSource={[
            { label: intl('saenext.components.idle-monitor.Application'), value: MonitorUnit.App },
            {
              label: intl('saenext.components.idle-monitor.Instance'),
              value: MonitorUnit.Instance,
            },
          ]}
          label={
            <div className="select-label">
              {intl('saenext.components.idle-monitor.ResourceType')}
            </div>
          }
          onChange={changeMonitorUnit}
        />

        {monitorUnit === MonitorUnit.Instance ? (
          <Select
            value={instanceId}
            style={{ width: 486, marginLeft: 10 }}
            dataSource={instances}
            onChange={changeMonitorInstance}
          />
        ) : null}
      </div>
      <MonitorTimeRange
        width={510}
        defaultTime={false}
        timeInitValue={timeInitValue}
        onTimeChanged={onMonitorTimeChange}
      />

      <Loading visible={loading} className="monitor-aisc" style={{ marginTop: 16 }}>
        <div style={{ display: 'flex' }}>
          {monitorUnit === MonitorUnit.App ? (
            <InstanceCountIndicator data={dataPoints} />
          ) : (
            <InstanceStatusIndicator data={dataPoints} />
          )}
          <CpuUtilizationIndicator data={cpuPoints} monitorUnit={monitorUnit} />
        </div>
        <If condition={monitorUnit === MonitorUnit.Instance}>
          <div className="flex mt-xs">
            <CpuUsedIndicator data={cpuUsedPoints} />
            <NetRecvTranIndicator data={netPoints} />
          </div>
        </If>
      </Loading>
    </>
  );
};
