import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wline } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card } from '@ali/cnd';
import moment from 'moment';

type Props = {
  data: [number, number][];
};

function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

export default (props: Props) => {
  const { data: dataSource } = props;
  const [data, setData] = useState([]);

  useEffect(() => {
    initChart();
  }, [JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    oneIndicator();
  };

  const oneIndicator = () => {
    const _data = [
      {
        name: intl('saenext.components.idle-monitor.NetRecvTranIndicator.InstanceReceivedBytes'),
        data: dataSource,
      },
    ];

    setData(_data);
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={intl('saenext.components.idle-monitor.NetRecvTranIndicator.InstanceReceivedBytesBS')}
        style={{ flex: 1, marginLeft: 4 }}
      >
        <Wline
          data={data}
          height={240}
          config={{
            yAxis: {
              labelFormatter: (text) => {
                return formatBytes(Number(text));
              },
            },
            tooltip: {
              visible: true,
              showTitle: true,
              showColon: true,
              titleFormatter: (v, t) => {
                return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
              },
            },
            colors: ['#796fff'],
            symbol: false,
            legend: {
              visible: true,
              position: 'bottom',
              align: 'left',
              marker: {
                symbol: 'hyphen',
                style(oldStyle) {
                  return {
                    ...oldStyle,
                    r: 3,
                    lineWidth: 6,
                    fill: oldStyle.stroke,
                    lineAppendWidth: 0,
                  };
                },
              },
            },
          }}
        />
      </Card>
    </>
  );
};
