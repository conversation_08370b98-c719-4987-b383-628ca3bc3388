import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wline } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { map, get } from 'lodash';
import moment from 'moment';
import { Card } from '@ali/cnd';

type IMetricsProps = {
  timestamp: number;
  Value: number;
  [key: string]: any;
};
type Props = {
  data: {
    AppTotalActive?: IMetricsProps[];
    AppTotalIdle?: IMetricsProps[];
    ApplicationInstances?: IMetricsProps[];
  };
};

const WlineOptions = {
  xAxis: {
    type: 'timeCat',
    mask: 'HH:mm',
  },
  yAxis: {
    min: 0,
    tickMethod: 'integer',
  },
  area: true,
  legend: {
    visible: true,
    position: 'bottom',
    align: 'left',
    marker: {
      symbol: 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 4,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
  },
  symbol: false,
  spline: true,
  slider: false,
  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
    sort: 'dsce',
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
    },
  },
  // @ts-ignore purple green yellow
  colors: ['#6B67E0', '#23b066', '#FAC31E'],
};

export default (props: Props) => {
  const { data: dataSource } = props;
  const [data, setData] = useState([]);

  useEffect(() => {
    initChart();
  }, [JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    oneIndicator();
  };

  const oneIndicator = () => {
    const idleInstances = get(dataSource, 'AppTotalIdle');
    const activeInstances = get(dataSource, 'AppTotalActive');
    const totalInstances = get(dataSource, 'ApplicationInstances');
    const _data = [
      {
        name: intl('saenext.components.idle-monitor.InstanceCountIndicator.TotalInstances'),
        type: 'line',
        yAxis: 0,
        data: map(totalInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Value];
        }),
      },
      {
        name: intl('saenext.components.idle-monitor.InstanceCountIndicator.ActiveInstances'),
        type: 'line',
        yAxis: 0,
        data: map(activeInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Value];
        }),
      },
      {
        name: intl('saenext.components.idle-monitor.InstanceCountIndicator.NumberOfIdleInstances'),
        type: 'line',
        yAxis: 0,
        data: map(idleInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Value];
        }),
      },
    ];

    setData(_data);
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={intl(
          'saenext.components.idle-monitor.InstanceCountIndicator.NumberOfApplicationInstances',
        )}
        style={{ flex: 1, marginRight: 4 }}
      >
        <Wline
          data={data}
          height={240}
          // @ts-ignore
          config={WlineOptions}
        />
      </Card>
    </>
  );
};
