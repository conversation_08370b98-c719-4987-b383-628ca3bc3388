import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Balloon, Field } from '@ali/cnd';
import TimeContainer from './TimeContainer';
import StatusContainer from './StatusContainer';
import moment from 'moment';

const RECENT_OPTION = [
  {
    label: intl('saenext.components.idle-monitor.TimeRangeMonitor.Minutes'),
    value: 'last_15_minutes',
    type: false,
  },
  {
    label: intl('saenext.components.idle-monitor.TimeRangeMonitor.HalfAnHour'),
    value: 'last_half_hour',
    type: false,
  },
  {
    label: intl('saenext.components.idle-monitor.TimeRangeMonitor.Hour'),
    value: 'last_hour',
    type: false,
  },
  {
    label: intl('saenext.components.idle-monitor.TimeRangeMonitor.Hours'),
    value: 'last_3_hours',
    type: false,
  },
  {
    label: intl('saenext.components.idle-monitor.TimeRangeMonitor.Hours.1'),
    value: 'last_6_hours',
    type: false,
  },
];

const RECENT_TIMES = {
  last_5_minutes: () => [moment().valueOf() - 5 * 60 * 1000, moment().valueOf()],
  last_10_minutes: () => [moment().valueOf() - 10 * 60 * 1000, moment().valueOf()],
  last_15_minutes: () => [moment().valueOf() - 15 * 60 * 1000, moment().valueOf()],
  last_half_hour: () => [moment().valueOf() - 30 * 60 * 1000, moment().valueOf()],
  last_hour: () => [moment().valueOf() - 60 * 60 * 1000, moment().valueOf()],
  last_3_hours: () => [moment().valueOf() - 3 * 60 * 60 * 1000, moment().valueOf()],
  last_6_hours: () => [moment().valueOf() - 6 * 60 * 60 * 1000, moment().valueOf()],
};

function isNumber(value) {
  return typeof value === 'number' && isFinite(value);
}

function toSecond(timestamp) {
  if (timestamp === -1) {
    return -1;
  } else if (isNumber(timestamp)) {
    return Math.floor(timestamp / 1000) * 1000;
  } else {
    return timestamp;
  }
}

export function getTimes(key) {
  const getTimes = RECENT_TIMES[key];
  if (getTimes) {
    const [start, end] = getTimes();
    return [toSecond(start), toSecond(end)];
  } else {
    return [];
  }
}

interface ListFormProps {
  onTimeChanged: Function;
  timeInitValue?: any;
  onRef?: any;
  defaultTime?: boolean;
  options?: {
    label: string;
    value: string;
    type: boolean;
  }[];
  width?: any;
}

const TimeRangeMonitor: React.FC<ListFormProps> = ({
  onTimeChanged,
  timeInitValue = undefined,
  width,
}) => {
  const [start, end] = getTimes(timeInitValue);
  const [values, setValues] = useState({ start, end, key: timeInitValue });
  const [refresh, setRefresh] = useState(true);
  const field = Field.useField();
  const { setValue } = field;

  useEffect(() => {
    const _timeInitValue = timeInitValue || 'last_15_minutes';
    const [start, end] = getTimes(_timeInitValue);
    setValues({ start, end, key: _timeInitValue });
  }, []);

  const periodChange = (record) => {
    if (record.type) return false;
    let nextPeriod = record.value;
    const [start, end] = RECENT_TIMES[nextPeriod]();
    if (!RECENT_TIMES[nextPeriod]) return false;
    setValue('time', [moment(start), moment(end)]);
    setValues({ start, end, key: nextPeriod });
    onTimeChanged({ start, end, key: nextPeriod });
  };

  const balloonChange = () => {
    setRefresh(false);
    setRefresh(true);
  };

  const getBalloon = (value) => {
    const [start, end] = getTimes(value);
    let ballStr = `${moment(start).format('YYYY-MM-DD HH:mm:ss')} ~ ${moment(end).format(
      'YYYY-MM-DD HH:mm:ss',
    )} `;
    return ballStr;
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <span style={{ marginRight: 10 }}>
        {RECENT_OPTION.map((item, ids) => {
          return (
            <span key={ids} className={item.type ? 'border-left-none' : ''}>
              <Balloon
                trigger={
                  <span onClick={() => periodChange(item)}>
                    <StatusContainer
                      className={
                        item.value === values.key
                          ? 'color-primary border-primary'
                          : RECENT_OPTION.length - 1 == ids
                          ? 'border-normal'
                          : 'simple-normal'
                      }
                      type="simple"
                      style={{ height: 32, lineHeight: '32px' }}
                    >
                      {item.label}
                    </StatusContainer>
                  </span>
                }
                afterClose={() => balloonChange()}
                closable={false}
              >
                {refresh && getBalloon(item.value)}
              </Balloon>
            </span>
          );
        })}
      </span>
      <TimeContainer
        field={field}
        timeInitValue={timeInitValue}
        initValue={getTimes(timeInitValue)}
        onTimeChanged={(value) => {
          setValues(value);
          onTimeChanged(value);
        }}
        width={width}
      />
    </div>
  );
};

export default TimeRangeMonitor;
