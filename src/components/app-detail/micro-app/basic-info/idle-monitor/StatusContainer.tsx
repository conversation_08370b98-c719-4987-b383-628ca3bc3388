import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import './index.less';

const StatusContainer = props => {
  const { type = 'solid', status = 'normal', className, width, height, text, style, children } = props;
  const classes = classNames('monitor-status-container-text', className, {
    [`monitor-status-container-${type}-${status}`]: !!type && !!status,
    solid: type === 'solid',
    simple: type === 'simple',
    'clear-font-styles': text,
  });
  return (
    <span className={classes} style={{ width, height, ...style }}>
      {children}
    </span>
  );
};

StatusContainer.propTypes = {
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  type: PropTypes.string,
  status: PropTypes.string,
  className: PropTypes.string,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  text: PropTypes.bool,
  // @ts-ignore
  style: PropTypes.shape(),
};

export default StatusContainer;
