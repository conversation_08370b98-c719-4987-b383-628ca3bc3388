import { intl } from '@ali/cnd';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { Dialog, Message, Select, Field, Form, NumberPicker, ToolTipCondition } from '@ali/cnd';
import MinReadyInstanceField from '~/components/shared/MinReadyInstance/MinReadyInstanceField';
import services from '~/services';
import If from '~/components/shared/If';
import MicroAppContext from '~/utils/microAppContext';
import C from '~/constants/common';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import { confFeature } from '@alicloud/console-one-conf';

const hygonSpecs = [
  { label: '2 Core 8 GiB', value: `${2 * 1000}-${8 * 1024}` },
  { label: '4 Core 16 GiB', value: `${4 * 1000}-${16 * 1024}` },
  { label: '8 Core 32 GiB', value: `${8 * 1000}-${32 * 1024}` },
  { label: '16 Core 64 GiB', value: `${16 * 1000}-${64 * 1024}` },
  { label: '32 Core 128 GiB', value: `${32 * 1000}-${128 * 1024}` },
];

const formatSpecLabel = <T extends { Cpu: number; Memory: number }>(spec: T) => {
  return `${spec.Cpu / 1000} Core ${spec.Memory / 1024} GiB`;
};

const SpecDialog = (props) => {
  const { refresh, children, type, appConfig, recommendSpec, isSupportHygon, checkAppInstanceVersion, title } =
    props;

  const field = Field.useField();

  const { AutoEnableApplicationScalingRule } = field.getValues<{
    AutoEnableApplicationScalingRule: boolean;
  }>();

  const [isLoading, setIsLoading] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [specList, setSpecList] = useState([]);
  const diskSize_enable = confFeature('diskSize_enable');

  const {
    // appConfig: { AppId, AssociateEip = false, Cpu, Memory, ResourceType, DiskSize = 20 },
    scaleRuleEnabled,
  } = useContext(MicroAppContext);

  const { AppId, AssociateEip = false, Cpu, Memory, ResourceType, DiskSize = 20 } = appConfig;

  useEffect(() => {
    if (!dialogVisible) return;
    // if (recommendSpec) {
    //   if (!(isSupportHygon && ResourceType === 'haiguang')) {
    //     field.setValues({ spec: recommendSpec });
    //   } else {
    //     if (_.find(hygonSpecs, item => item.value === recommendSpec)) {
    //       field.setValues({ spec: recommendSpec });
    //     }
    //   }
    // }
    setRefreshIndex(refreshIndex + 1);
  }, [dialogVisible]);

  const fetchData = async () => {
    const { Data = [] } = (await services.DescribeInstanceSpecifications()) || {};

    Data.sort((a, b) => a.Memory - b.Memory).sort((a, b) => a.Cpu - b.Cpu);
    const specEnable = _.filter(Data, (item) => item.Enable);
    setSpecList(
      specEnable.map((v) => ({
        label: formatSpecLabel(v),
        value: `${v.Cpu}-${v.Memory}`,
      })),
    );
    Cpu && Memory && field.setValues({ spec: `${Cpu}-${Memory}` });
  };

  const handleSubmit = async () => {
    const { errors, values } = (await field.validatePromise()) as { errors: any; values: any };
    if (errors) {
      return;
    }

    const { MinReadyInstanceRatio, MinReadyInstances, spec = '', DiskSize } = values;
    const [Cpu, Memory] = spec.split('-');
    const params = {
      AppId,
      Cpu: Number(Cpu),
      Memory: Number(Memory),
      MinReadyInstanceRatio,
      MinReadyInstances,
    };
    if (scaleRuleEnabled) {
      Reflect.set(params, 'AutoEnableApplicationScalingRule', true);
    }
    if (diskSize_enable) {
      Reflect.set(params, 'DiskSize', DiskSize);
    }

    setIsLoading(true);
    const res = await services.RescaleApplicationVertically(params);
    setIsLoading(false);

    if (res) {
      Message.success(
        intl('saenext.micro-app.basic-info.SpecDialog.TheSpecificationHasBeenChanged'),
      );
      setDialogVisible(false);
      refresh();
    } else {
      Message.error(intl('saenext.micro-app.basic-info.SpecDialog.FailedToChangeTheSpecification'));
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <AddPropsWrap
        onClick={async () => {
          if (!checkAppInstanceVersion) {
            setDialogVisible(true);
            return;
          }
          const checkResult = await checkAppInstanceVersion(title);
          if (checkResult) {
            setDialogVisible(true);
          }
        }}
      >
        {children}
      </AddPropsWrap>
      <Dialog
        title={intl('saenext.micro-app.basic-info.AppBaseInfo.ChangeSpecifications')}
        visible={dialogVisible}
        onOk={handleSubmit}
        onClose={() => setDialogVisible(false)}
        onCancel={() => setDialogVisible(false)}
        okProps={{
          loading: isLoading,
        }}
        style={{ width: 850 }}
      >
        <>
          <Message type="warning">
            <p>{intl('saenext.micro-app.basic-info.SpecDialog.WhenTheSpecificationIsChanged')}</p>
          </Message>
          <Form field={field} {...C.FORM_LAYOUT_LARGE} className="pt-l">
            <Form.Item
              name="spec"
              label={intl('saenext.micro-app.basic-info.AppBaseInfo.InstanceType')}
              required
              requiredMessage={intl('saenext.micro-app.basic-info.SpecDialog.SelectAnInstanceType')}
              help={
                field.getError('spec') ? (
                  <div>
                    <div style={{ color: '#808080' }}>
                      {intl('saenext.micro-app.basic-info.SpecDialog.CurrentSpec', {
                        currentSpec: formatSpecLabel({ Cpu, Memory }),
                      })}
                    </div>
                    <div style={{ color: 'red' }}>{field.getError('spec')}</div>
                  </div>
                ) : (
                  intl('saenext.micro-app.basic-info.SpecDialog.CurrentSpec', {
                    currentSpec: formatSpecLabel({ Cpu, Memory }),
                  })
                )
              }
            >
              <Select
                className="full-width"
                dataSource={isSupportHygon && ResourceType === 'haiguang' ? hygonSpecs : specList}
              />
            </Form.Item>
            {diskSize_enable && type !== 'job' && (
              <Form.Item
                label={intl('saenext.micro-app.basic-info.SpecDialog.TemporaryDiskSize')}
                name="DiskSize"
                required
                help={intl('saenext.micro-app.basic-info.SpecDialog.TheCurrentTemporaryDiskSize', {
                  DiskSize: DiskSize,
                })}
                validator={(rule, value: number, callback) => {
                  if (value && value > 500) {
                    callback(' ');
                  } else {
                    callback();
                  }
                }}
              >
                <NumberPicker defaultValue={DiskSize} min={20} innerAfter="GiB" />
              </Form.Item>
            )}
            {type !== 'job' &&
              <Form.Item
                label={intl(
                  'saenext.micro-app.basic-info.AppActionBar.MinimumNumberOfSurvivingInstances',
                )}
              >
                <MinReadyInstanceField field={field} />
              </Form.Item>
            }
          </Form>
        </>
      </Dialog>
    </>
  );
};

export default SpecDialog;
