import React, { useEffect, useMemo, useState } from 'react';
import { Button, DateTime, Icon, intl, Loading, Message, Select } from '@ali/cnd';
import services from '~/services';
import dayjs from 'dayjs';
import LifeChart from './LifeChart';
import { find } from 'lodash';

const InstanceLifecycle = (props) => {
  const { regionId, appId, instanceId, createTimeStamp } = props;

  const timeRange = useMemo(() => {
    return [createTimeStamp - 60 * 1000, Math.min(createTimeStamp + 14 * 60 * 1000, Date.now())];
  }, [createTimeStamp]);

  const [loading, setLoading] = useState(false);
  const [eventLevel, setEventLevel] = useState('');
  const [cpuData, setCpuData] = useState([]);
  const [lifecycleData, setLifecycleData] = useState([]);
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    getCpuData();
    getLifecycleData();
  }, [timeRange, refreshIndex]);

  useEffect(() => {});

  const getCpuData = async () => {
    const [startTime, endTime] = timeRange;
    setLoading(true);
    const res = await services.getAppScaleMonitor({
      params: {
        AppId: appId,
        InstanceId: instanceId,
        RegionId: regionId,
        StartTime: dayjs(startTime)?.unix(),
        EndTime: dayjs(endTime)?.unix(),
        Interval: 1,
        MetricName: 'cpu',
      },
      customErrorHandle: (error, data, callback) => {
        setLoading(false);
      },
    });
    setLoading(false);
    if (!res?.Success) return;
    const { Data = [] } = res;
    setCpuData(Data);
  };

  const getLifecycleData = async () => {
    const [startTime, endTime] = timeRange;
    setLoading(true);
    const res = await services.DescribeInstanceLifecycleTimeline({
      params: {
        RegionId: regionId,
        AppId: appId,
        InstanceId: instanceId,
        FromTime: dayjs(startTime)?.unix(),
        ToTime: dayjs(endTime)?.unix(),
        EnableEventAggregation: true,
      },
    });
    setLoading(false);
    if (!res?.Success) return;
    const { Data = [] } = res || {};
    setLifecycleData(Data);
  };

  const onRefresh = () => {
    setRefreshIndex(refreshIndex + 1);
  };

  const cpuFillData = useMemo(() => {
    if (!cpuData.length || !lifecycleData.length) return [];
    const runningEvent = find(lifecycleData, {
      EventStatus: 'Running',
    });
    if (runningEvent) {
      const { Timestamp } = runningEvent;
      cpuData.push({
        Time: Number(Timestamp),
        Value: 0,
      });
    }
    cpuData.sort((a, b) => a.Time - b.Time);
    return cpuData;
  }, [cpuData, lifecycleData]);

  const filterData = useMemo(() => {
    if (eventLevel) {
      return lifecycleData.filter((item) => item.EventLevel === eventLevel);
    } else {
      return lifecycleData;
    }
  }, [lifecycleData, eventLevel]);

  const timeFormat = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false, // 使用24小时制
  };

  return (
    <>
      <Message type="notice" className="mb-s">
        {intl('saenext.basic-info.InstanceDetail.InstanceLifecycle.CurrentlyOnlyEventsWithinDays')}
      </Message>
      <div className="flex justify-between">
        <div>
          <DateTime value={timeRange[0]} format={timeFormat} />

          <span className="ml-s mr-s">-</span>
          <DateTime value={timeRange[1]} format={timeFormat} />
        </div>
        <div>
          <Select
            style={{ width: 200 }}
            value={eventLevel}
            onChange={(v: string) => setEventLevel(v)}
            hasClear
            placeholder={intl('saenext.shared.EventList.SelectAnEventLevel')}
            dataSource={[
              {
                label: 'Warning',
                value: 'Warning',
              },
              {
                label: 'Normal',
                value: 'Normal',
              },
            ]}
          />

          <Button className="ml-s" onClick={onRefresh}>
            <Icon type="refresh" />
          </Button>
        </div>
      </div>
      <Loading visible={loading} className="full-width">
        <LifeChart cpuData={cpuFillData} lifecycleData={filterData} timeRange={timeRange} />
      </Loading>
    </>
  );
};

export default InstanceLifecycle;
