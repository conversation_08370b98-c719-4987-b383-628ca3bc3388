import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Copy, Icon, Tab } from '@ali/cnd';
import { getParams, hasSection, setSearchParams } from '~/utils/global';
import { EInsTab, InsTab } from './constants';
import RealtimeLog from '~/components/app-detail/micro-app/log-control/log-control/RealtimeLog';
import WebshellContent from './WebshellContent';
import ContainerList from '~/components/shared/ContainerList';
import InstanceLifecycle from './InstanceLifecycle';
import If from '~/components/shared/If';
import { isNull } from 'lodash';
import { confFeature } from '@alicloud/console-one-conf';

const InstanceDetail = (props) => {
  const {
    match: {
      params: { regionId, id: appId },
    },
    location: { search },
    history,
    appConfig,
  } = props;

  const [createTimeStamp, setCreateTimeStamp] = useState(null);

  const groupId = getParams('groupId');
  const instanceId = getParams('instanceId');
  const containerId = getParams('container') || '';
  const section = getParams('section');

  const lifecycle_gray = confFeature('lifecycle_gray');

  const activeTab = hasSection(section, EInsTab) ? section : EInsTab.CONTAINER;

  const onGoBack = () => {
    history.push(`/${regionId}/app-list/${appId}/micro-app/base?tab=instance`);
  };

  const onTabChange = (key) => {
    history.push(
      `/${regionId}/app-list/${appId}/micro-app/instance?groupId=${groupId}&instanceId=${instanceId}&container=${containerId}&section=${key}`,
    );
  };

  const key = `${regionId} ${appId} ${groupId} ${instanceId} ${containerId}`;

  return (
    <>
      <div className="text-m text-bold">
        <Icon type="wind-arrow-left" className="mr cursor-pointer" onClick={onGoBack} />
        {intl('saenext.basic-info.InstanceDetail.InstanceId')}

        <Copy className="mb" text={instanceId}>
          {instanceId}
        </Copy>
      </div>
      <Tab
        className="mt"
        shape="wrapped"
        activeKey={activeTab}
        onChange={onTabChange}
        lazyLoad
        contentClassName={'mt mb'}
      >
        <Tab.Item title={InsTab[EInsTab.CONTAINER]} key={EInsTab.CONTAINER}>
          <ContainerList
            regionId={regionId}
            appId={appId}
            groupId={groupId}
            instanceId={instanceId}
            setCreateTimeStamp={setCreateTimeStamp}
          />
          <If condition={!isNull(createTimeStamp) && lifecycle_gray}>
            <InstanceLifecycle
              regionId={regionId}
              appId={appId}
              instanceId={instanceId}
              createTimeStamp={createTimeStamp}
            />
          </If>
        </Tab.Item>
        <Tab.Item title={InsTab[EInsTab.LOG]} key={EInsTab.LOG}>
          <RealtimeLog
            key={`${key} log`}
            regionId={regionId}
            appConfig={appConfig}
            applicationId={appId}
            podId={instanceId}
            containerId={containerId}
            onContainerChange={(v) => {
              setSearchParams({
                container: v,
              });
            }}
            instanceDisabled
          />
        </Tab.Item>
        <Tab.Item title={InsTab[EInsTab.WEBSHELL]} key={EInsTab.WEBSHELL}>
          <WebshellContent
            key={`${key} webshell`}
            regionId={regionId}
            appId={appId}
            groupId={groupId}
            instanceId={instanceId}
            sidecarId={containerId === 'main' ? '' : containerId}
          />
        </Tab.Item>
      </Tab>
    </>
  );
};

export default InstanceDetail;
