import { intl } from '@ali/cnd';
import React, { useEffect, useMemo, useState } from 'react';
import { get, isEmpty, map, max, maxBy, min, minBy } from 'lodash';
import { Wline } from '@alife/aisc-widgets';
import { InsEventMap } from './constants';

const Options = {
  zoom: true,
  xAxis: {
    type: 'time' as 'time',
    mask: 'YYYY/MM/DD HH:mm',
  },
  yAxis: {
    min: 0,
    max: 10,
    unit: '%',
  },
  legend: {
    clickable: false,
  },
  tooltip: {
    valueFormatter: (value, data) => {
      if (data.name === 'cpu') {
        return `${value}%`;
      }
      return value;
    },
  },
  guide: {
    line: [
      {
        status: 'normal' as 'normal',
        axis: 'x' as 'x',
        value: 1742453145000,
      },
    ],
  },
};

const StatusMap = {
  Normal: 'success' as 'success',
  Warning: 'warning' as 'warning',
};

const LifeChart = (props) => {
  const { timeRange, cpuData, lifecycleData } = props;

  const [options, setOptions] = useState(Options);

  useEffect(() => {
    onChangeOptions();
  }, [lifecycleData, cpuData]);

  const chartData = useMemo(() => {
    const [start, end] = timeRange;
    const data = [];
    if (!isEmpty(cpuData)) {
      const cpuSeries = {
        name: 'cpu',
        data: map(cpuData, (item) => [item.Time * 1000, item.Value]),
      };
      // 补零
      for (
        let i = start.valueOf();
        i < (get(minBy(cpuData, 'Time'), 'Time', 0) * 1000 || end.valueOf());
        i += 500
      ) {
        cpuSeries.data.push([i, null]);
      }
      data.push(cpuSeries);
    }
    if (!isEmpty(lifecycleData)) {
      const lifecycleSeries = {
        name: intl('saenext.basic-info.InstanceDetail.LifeChart.Event'),
        data: map(lifecycleData, (item) => [
          item.Timestamp * 1000,
          InsEventMap[item.EventStatus] || item.EventStatus,
        ]),
      };
      data.push(lifecycleSeries);
    }
    return data;
  }, [cpuData, lifecycleData, timeRange]);

  const onChangeOptions = () => {
    const newGuideLine = map(lifecycleData, (item, index) => {
      const { Timestamp, EventStatus, EventLevel } = item;
      return {
        top: true,
        text: {
          title: InsEventMap[EventStatus] || EventStatus,
          offsetY: generateWaveHeight(index),
        },
        status: StatusMap[EventLevel],
        axis: 'x' as 'x',
        value: Timestamp * 1000,
        style: {
          lineDash: [4, 4],
        },
      };
    });

    // 根据cpu数据动态设置y轴最大值
    const cpuMax = get(maxBy(cpuData, 'Value'), 'Value', 0);
    const _yMax = max([cpuMax * 1.5, Options.yAxis.max]);
    const yMax = min([_yMax, 100]);

    setOptions({
      ...options,
      yAxis: { ...options.yAxis, max: yMax },
      guide: { line: newGuideLine },
    });
  };

  const generateWaveHeight = (index, step = 14, waveLength = 20) => {
    const position = index % waveLength;

    if (position <= waveLength / 2) {
      // height下降阶段
      return position * step;
    } else {
      // height上升阶段
      return (waveLength - position) * step;
    }
  };

  return <Wline data={chartData} height={200} config={options} />;
};

export default LifeChart;
