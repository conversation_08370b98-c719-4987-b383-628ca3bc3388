import { intl } from '@ali/cnd';
export enum EInsTab {
  CONTAINER = 'container',
  LOG = 'log',
  WEBSHELL = 'webshell',
}

export const InsTab = {
  [EInsTab.CONTAINER]: intl('saenext.basic-info.InstanceDetail.constants.Container'),
  [EInsTab.LOG]: intl('saenext.basic-info.InstanceDetail.constants.Log'),
  [EInsTab.WEBSHELL]: 'Webshell',
};

export const InsEventMap = {
  Scheduled: intl('saenext.basic-info.InstanceDetail.constants.ResourceScheduling'),
  Pending: intl('saenext.basic-info.InstanceDetail.constants.PreparingTheInstance'),
  Pulling: intl('saenext.basic-info.InstanceDetail.constants.StartPullingImages'),
  Pulled: intl('saenext.basic-info.InstanceDetail.constants.ImagePullEnd'),
  Created: intl('saenext.basic-info.InstanceDetail.constants.ContainerCreated'),
  Started: intl('saenext.basic-info.InstanceDetail.constants.ContainerProcessStarted'),
  Running: intl('saenext.basic-info.InstanceDetail.constants.ContainerRunning'),
  Ready: intl('saenext.basic-info.InstanceDetail.constants.InstanceReady'),
  Unhealthy: intl('saenext.basic-info.InstanceDetail.constants.HealthCheckFailed'),
  Failed: intl('saenext.basic-info.InstanceDetail.constants.Failed'),
  BackOff: intl('saenext.basic-info.InstanceDetail.constants.FailedBackoffRetry'),
};
