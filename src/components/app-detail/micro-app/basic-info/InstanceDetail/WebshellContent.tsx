import React, { useState } from 'react';
import InstanceContainerSelect from '~/components/shared/InstanceContainerSelect.tsx';
import Webshell from '~/components/shared/Webshell';

const WebshellContent = (props) => {
  const { regionId, appId, groupId, instanceId, sidecarId } = props;

  const [insContainer, setInsContainer] = useState({
    instanceId,
    sidecarId,
  });
  
  return (
    <>
      <InstanceContainerSelect
        appId={appId}
        groupId={groupId}
        instanceDisabled
        value={insContainer}
        onChange={setInsContainer}
      />
      <div
        style={{ 
          height: `calc(100% - 80px)`,
        }}
      >
        <Webshell
          key={`${insContainer.instanceId} ${insContainer.sidecarId}`}
          regionId={regionId}
          appId={appId}
          instanceId={insContainer.instanceId}
          containerId={insContainer.sidecarId || undefined}
          fullName
          hideClose
        />
      </div>
    </>
  )
}

export default WebshellContent;