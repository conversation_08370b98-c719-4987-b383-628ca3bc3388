import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';
import {
  Balloon,
  Dialog,
  Field,
  Form,
  intl,
  Loading,
  Message,
  NumberPicker,
  Radio,
  ToolTipCondition
} from '@ali/cnd';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import MinReadyInstance from '~/components/shared/MinReadyInstance';
import MinReadyInstanceField from '~/components/shared/MinReadyInstance/MinReadyInstanceField';
import C from '~/constants/common';
import services from '~/services';
import MicroAppContext from '~/utils/microAppContext';
import If from '~/components/shared/If';
import RangePicker from '~/components/shared/RangePicker';
import CachedData from '~/cache/common';

type RescaleField = {
  MinReadyInstanceRatio: number,
  MinReadyInstances: number,
  // FIXME:
  // Replicas: number,
}

const RescaleDialog = (props) => {
  const { appId, refresh, children, checkAppInstanceVersion } = props;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const field = Field.useField();

  const {
    Replicas: replicasForm,
    MinReadyInstances: minReadyInstancesForm,
    AutoEnableApplicationScalingRule,
  } = field.getValues() as any;

  const {
    appConfig: {
      Replicas = 2,
      AssociateEip = false,
      MinReadyInstances = -1,
      MinReadyInstanceRatio = -1,
    },
    scaleRuleEnabled,
    resourceQuota: { InstancesPerApplication: instancesPerApplication = 50 },
  } = useContext(MicroAppContext);

  useEffect(() => {
    field.validate('Replicas');
  }, [minReadyInstancesForm]);

  const handleRescale = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) {
      return;
    }
    setLoading(true);
    const { Data } =
      (await services.rescaleMicroApplication({
        AppId: appId,
        ...values as RescaleField,
      })) || {};
    setLoading(false);
    if (Data) {
      Message.success(intl('saenext.micro-app.basic-info.RescaleDialog.ScalingSucceeded'));
      refresh();
      toogleVisible();
    } else {
      Message.error(intl('saenext.micro-app.basic-info.RescaleDialog.ScalingFailed'));
    }
  };

  const toogleVisible = () => {
    setVisible(!visible);
  };

  const minReadyInstanceIsByNum = MinReadyInstances > 0 && MinReadyInstanceRatio === -1;

  const replicasValidator = (rule, value, callback) => {
    if (value < minReadyInstancesForm) {
      callback(intl('saenext.micro-app.basic-info.RescaleDialog.TheNumberOfTargetInstances'));
    } else {
      callback();
    }
  };

  return (
    <>
      <AddPropsWrap
        onClick={async () => {
          const checkResult = await checkAppInstanceVersion(intl('saenext.micro-app.basic-info.AppActionBar.ManualScaling'));
          if (checkResult) {
            toogleVisible();
          }
        }}
      >
        {children}
      </AddPropsWrap>
      <Dialog
        title={intl('saenext.micro-app.basic-info.RescaleDialog.ApplicationScaling')}
        visible={visible}
        onOk={handleRescale}
        onClose={toogleVisible}
        onCancel={toogleVisible}
        okProps={{ loading }}
      >
        <Loading visible={loading} style={{ width: 800 }}>
          <If condition={replicasForm === 0}>
            <Message type={'warning'}>
              {intl('saenext.micro-app.basic-info.RescaleDialog.WhenTheApplicationInstanceIs')}
            </Message>
          </If>
          <Form field={field} className="pd-card">
            <Form.Item
              required
              requiredMessage={intl(
                'saenext.micro-app.basic-info.RescaleDialog.EnterTheNumberOfApplication',
              )}
              label={intl(
                'saenext.micro-app.basic-info.RescaleDialog.NumberOfApplicationTargetInstances',
              )}
              extra={intl(
                'saenext.micro-app.basic-info.RescaleDialog.NumberOfCurrentInstancesApplied',
                { Replicas: Replicas },
              )}
              validator={replicasValidator}
              {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
            >
              <RangePicker
                name="Replicas"
                defaultValue={Replicas}
                step={1}
                min={0}
                max={instancesPerApplication}
                marks={5}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl(
                'saenext.micro-app.basic-info.AppActionBar.MinimumNumberOfSurvivingInstances',
              )}
              help={intl('saenext.micro-app.basic-info.RescaleDialog.WeRecommendThatYouSet')}
              {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
              style={!minReadyInstanceIsByNum ? { display: 'none' } : {}}
            >
              <MinReadyInstanceField field={field} />
            </Form.Item>
            <If condition={scaleRuleEnabled}>
              <Form.Item
                label={intl(
                  'saenext.micro-app.basic-info.RescaleDialog.RestoreAutomaticElasticity',
                )}
                help={intl(
                  'saenext.micro-app.basic-info.RescaleDialog.TheCurrentApplicationHasEnabled',
                )}
                required
                {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
                className='none'
              >
                <Radio.Group name="AutoEnableApplicationScalingRule" defaultValue={true}>
                  <Radio id="true" value={true} disabled={AssociateEip}>
                    <ToolTipCondition
                      show={AssociateEip}
                      tip={intl(
                        'saenext.micro-app.basic-info.RescaleDialog.TheApplicationIsBoundTo',
                      )}
                      align="t"
                    >
                      {intl('saenext.micro-app.basic-info.RescaleDialog.AutomaticSystemRecovery')}
                    </ToolTipCondition>
                  </Radio>
                  <Radio id="false" value={false}>
                    {intl('saenext.micro-app.basic-info.RescaleDialog.ManuallyEnableRecovery')}
                  </Radio>
                </Radio.Group>
                <If condition={AutoEnableApplicationScalingRule === true}>
                  {/* TODO: replicas > targetReplicas */}
                  <Message type="notice" className="mt">
                    {intl(
                      'saenext.micro-app.basic-info.RescaleDialog.AfterTheSystemAutomaticallyRestores',
                    )}

                    <br />
                    {intl(
                      'saenext.micro-app.basic-info.RescaleDialog.MonitoringMetricsElasticityAndHybrid',
                    )}

                    <a href={CachedData.confLink('help:sae:configure-an-auto-scaling-policy')} target="_blank">
                      {intl('saenext.micro-app.basic-info.RescaleDialog.ViewDetails')}
                    </a>
                  </Message>
                </If>
                <If condition={AutoEnableApplicationScalingRule === false}>
                  <Message type="notice" className="mt">
                    {intl(
                      'saenext.micro-app.basic-info.RescaleDialog.AfterYouSelectManuallyEnable',
                    )}
                  </Message>
                </If>
              </Form.Item>
            </If>
          </Form>
        </Loading>
      </Dialog>
    </>
  );
};

export default RescaleDialog;
