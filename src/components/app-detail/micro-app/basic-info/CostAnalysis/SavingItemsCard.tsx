import { intl } from '@ali/cnd';
import React from 'react';
import { Card, CndStatus } from '@ali/cnd';
import { toCu } from '~/utils/global';
import If from '~/components/shared/If';

const SavingItemsCard = (props) => {
  const { name, open = false, reducible = false, savingAmount, content } = props;

  // 可降本： 未开启且可优化
  const itemReducible = !open && reducible;

  const { value, unit } = toCu(savingAmount);

  const renderLabel = () => {
    if (open) {
      return intl('saenext.basic-info.CostAnalysis.SavingItemsCard.Enabled');
    }

    return reducible
      ? intl('saenext.basic-info.CostAnalysis.SavingItemsCard.Optimized')
      : intl('saenext.basic-info.CostAnalysis.SavingItemsCard.OptimalConfiguration');
  };

  return (
    <Card
      title={
        <div style={{ fontSize: 12, fontWeight: 400, color: '#555555' }}>
          {!itemReducible
            ? name
            : intl(
                'saenext.basic-info.CostAnalysis.SavingItemsCard.ExpectedCostReductionThroughName',
                { name: name },
              )}
        </div>
      }
      extra={<CndStatus type={!itemReducible ? 'success' : 'stop'} label={renderLabel()} />}
      showHeadDivider={false}
    >
      <div style={{ display: 'flex', alignItems: 'end', height: 34 }}>
        <If condition={itemReducible}>
          <div className="mr">
            <span style={{ fontSize: 28 }}>{value}</span>
            <span style={{ color: '#666666' }}>{unit}</span>
          </div>
        </If>
        {content}
      </div>
    </Card>
  );
};

export default SavingItemsCard;
