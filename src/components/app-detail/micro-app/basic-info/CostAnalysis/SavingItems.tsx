import React, { useContext, useMemo } from 'react';
import { ESaving } from './constant';
import SavingItemsCard from './SavingItemsCard';
import ScalingRuleSlide from '../ScalingRuleSlide';
import { Button, Icon, intl, LinkButton, ToolTipCondition } from '@ali/cnd';
import If from '~/components/shared/If';
import SpecDialog from '../SpecDialog';
import IdleSwitch from '../IdleMode/IdleSwitch';
import SlideWrap from '~/components/shared/SlideWrap';
import IdleIndicatorMonitor from '~/components/app-detail/micro-app/basic-info/idle-monitor';
import { noop, isEmpty } from 'lodash';
import CachedData from '~/cache/common';
import FeatureContext from '~/utils/featureContext';
import MicroAppContext from '~/utils/microAppContext';
import ScaleRuleSlide from '../ScaleRuleSlide';
import ScaleRuleSlideContainer from '../ScaleRuleSlideContainer';

const SavingItems = (props) => {
  const {
    appId,
    appConfig = {},
    refresh,
    CurrentOpenHpa,
    CurrentEnableIdle,
    EnableOpenHpa,
    EnableOpenIdle,
    EnableRescale,
    AppScalingRule = {},
    HpaReduceSum = 0,
    IdleReduceSum = 0,
    RescaleReduceSum = 0,
    ReducedCpu = 0,
    ReducedMem = 0,
    isSupportHygon = false,
    checkAppInstanceVersion,
  } = props;

  const { appStatus } = useContext(MicroAppContext);
  const { LastChangeOrderRunning, CurrentStatus } = appStatus;
  const isAppStart = CurrentStatus === 'RUNNING';
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');

  const { feature } = useContext(FeatureContext);
  const { idle = false } = feature;

  const { Cpu, Memory } = appConfig;

  const recommendCpu = ReducedCpu || Cpu;
  const recommendMem = ReducedMem || Memory;


  const renderDisabledTip = (conditions: Array<string>) => {
    let tip = '';
    if (!isEmpty(conditions)) {
      for (let i = 0; i < conditions.length; i++) {
        const condition = conditions[i];

        if (condition === 'isInDebt' && isInDebt) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.YourAccountIsOverduePlease');
          break;
        }
        if (condition === 'LastChangeOrderRunning' && LastChangeOrderRunning) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.TheApplicationIsChangingPlease');
          break;
        }
        if (condition === 'isAppStart' && !isAppStart) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.TheCurrentApplicationStatusIs');
          break;
        }
      }
    }
    return tip;
  };

  const savingItems = useMemo(() => {
    const items = [
      {
        type: ESaving.HPA,
        name: intl('saenext.basic-info.CostAnalysis.SavingItems.ElasticPolicy'),
        open: CurrentOpenHpa,
        reducible: EnableOpenHpa,
        savingAmount: HpaReduceSum,
        content: (
          <ScaleRuleSlideContainer
            appId={appId}
            ruleData={AppScalingRule}
            refresh={() => {}}
            type={'create'}
          >
            <LinkButton>
              {CurrentOpenHpa
                ? intl('saenext.basic-info.CostAnalysis.SavingItems.ModifyAnElasticPolicy')
                : intl('saenext.basic-info.CostAnalysis.SavingItems.AddAnElasticPolicy')}
            </LinkButton>
          </ScaleRuleSlideContainer>
        ),
      },
      {
        type: ESaving.IDLE,
        name: intl('saenext.basic-info.CostAnalysis.SavingItems.IdleMode'),
        open: CurrentEnableIdle,
        reducible: EnableOpenIdle,
        savingAmount: IdleReduceSum,
        visible: idle,
        content: (
          <>
            <IdleSwitch appId={appId} checked={CurrentEnableIdle} refresh={refresh} />

            <If condition={appConfig.EnableIdle}>
              <SlideWrap
                title={intl('saenext.basic-info.ScalingGroup.IdleMonitorPanel.IdleMonitoring')}
                width={900}
                onMaskClick={noop}
                onClose={noop}
                content={<IdleIndicatorMonitor appId={appId} />}
              >
                <Button type="primary" text size="small" className="ml-s">
                  {intl('saenext.basic-info.CostAnalysis.SavingItems.ViewMonitoring')}

                  <Icon type="chart-line" size="small" />
                </Button>
              </SlideWrap>
            </If>
          </>
        ),
      },
      {
        type: ESaving.SPEC,
        name: intl('saenext.basic-info.CostAnalysis.SavingItems.AdjustSpecifications'),
        open: false,
        reducible: EnableRescale,
        savingAmount: RescaleReduceSum,
        content: (
          <>
            <If condition={EnableRescale && (ReducedCpu || ReducedMem)}>
              <span className="mr">
                {intl('saenext.basic-info.CostAnalysis.SavingItems.Suggestions')}
                {recommendCpu / 1000} Core {recommendMem / 1024} GiB
              </span>
            </If>

            <ToolTipCondition
              show={LastChangeOrderRunning || !isAppStart || isInDebt}
              tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart', 'isInDebt'])}
            >
              <SpecDialog
                appConfig={{}}
                recommendSpec={`${recommendCpu}-${recommendMem}`}
                refresh={() => {}}
                isSupportHygon={isSupportHygon}
                checkAppInstanceVersion={checkAppInstanceVersion}
                title={intl(
                  'saenext.basic-info.CostAnalysis.SavingItems.ConfigurationAdjustmentSpecifications',
                )}
              >
                <LinkButton
                  disabled={LastChangeOrderRunning || !isAppStart || isInDebt }
                >
                  {intl(
                    'saenext.basic-info.CostAnalysis.SavingItems.ConfigurationAdjustmentSpecifications',
                  )}
                </LinkButton>
              </SpecDialog>
            </ToolTipCondition>
          </>
        ),
      },
    ];

    return items.filter((item) => item.visible !== false);
  }, [
    CurrentOpenHpa,
    CurrentEnableIdle,
    EnableOpenHpa,
    EnableOpenIdle,
    EnableRescale,
    HpaReduceSum,
    IdleReduceSum,
    RescaleReduceSum,
    appConfig.EnableIdle,
    recommendCpu,
    recommendMem,
  ]);

  return (
    <div className="flex justify-between mb" style={{ gap: 8 }}>
      {savingItems.map((item) => (
        <div className="" style={{ flexGrow: 1, width: '30%' }}>
          <SavingItemsCard {...item} />
        </div>
      ))}
    </div>
  );
};

export default SavingItems;
