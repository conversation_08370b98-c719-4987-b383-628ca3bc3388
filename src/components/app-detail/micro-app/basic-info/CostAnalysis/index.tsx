import { intl } from '@ali/cnd';
import React, { useMemo, useState } from 'react';
import { Card, DatePicker2, Tab } from '@alifd/next';
import dayjs from 'dayjs';
import ActualSavingContent from './ActualSavingContent';
import SavingCostContent from './SavingCostContent';

const CostAnalysis = (props) => {
  const { appId, appConfig = {}, refresh, isSupportHygon, checkAppInstanceVersion } = props;

  const [time, setTime] = useState<undefined | dayjs.Dayjs>();

  const timeUnix = useMemo(() => {
    return time?.unix();
  }, [time]);

  const extraContent = (
    <DatePicker2
      size="small"
      value={time}
      onChange={setTime}
      disabledDate={(date) => date.isBefore(dayjs().subtract(7, 'day')) || date.isAfter(dayjs())}
    />
  );

  return (
    <Card className="mt" contentHeight={500}>
      <Tab size="small" shape="capsule" lazyLoad extra={extraContent} contentClassName="pt pb">
        <Tab.Item
          key="actual"
          title={intl('saenext.basic-info.CostAnalysis.ActualCostReductionOverview')}
          style={{ height: 24 }}
        >
          <ActualSavingContent appId={appId} time={timeUnix} />
        </Tab.Item>
        <Tab.Item
          key="plan"
          title={intl('saenext.basic-info.CostAnalysis.CostReductionOptimizationItem')}
          style={{ height: 24 }}
        >
          <SavingCostContent
            appId={appId}
            appConfig={appConfig}
            refresh={refresh}
            time={timeUnix}
            isSupportHygon={isSupportHygon}
            checkAppInstanceVersion={checkAppInstanceVersion}
          />
        </Tab.Item>
      </Tab>
    </Card>
  );
};

export default CostAnalysis;
