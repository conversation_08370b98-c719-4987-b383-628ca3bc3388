import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import services from '~/services';
import { convertToChartArr, multiplyByThousand } from '~/utils/transfer-data';
import CostChat from './CostChat';
import { ESaving } from './constant';
import SavingCard from './SavingAmountCard';
import { Loading, useService } from '@ali/cnd';

const ActualSavingContent = (props) => {
  const { appId, time } = props;

  const fetchAcualSaving = async ({ time }) => {
    const res = await services.analysisActualCost({
      params: {
        AppId: appId,
        AnalysisTime: time,
      },
    });

    if (!res?.Data) return {};

    const {
      TotalReduce,
      HpaReduce,
      IdleReduce,
      ActualCostList = [],
      HpaReduceList = [],
      IdleReduceList = [],
    } = res.Data;

    const savingItems = [
      {
        type: ESaving.TOTAL,
        savingAmount: TotalReduce,
      },
      {
        type: ESaving.HPA,
        savingAmount: HpaReduce,
      },
      {
        type: ESaving.IDLE,
        savingAmount: IdleReduce,
      },
    ];

    const activeSaving = savingItems.filter((item) => item.savingAmount);

    const data = [
      {
        name: intl('saenext.basic-info.CostAnalysis.ActualSavingContent.ActualCost'),
        data: convertToChartArr(ActualCostList, ['Time', 'Reduce'], [multiplyByThousand]),
      },
      {
        name: intl('saenext.basic-info.CostAnalysis.ActualSavingContent.ElasticCostReduction'),
        data: convertToChartArr(HpaReduceList, ['Time', 'Reduce'], [multiplyByThousand]),
      },
      {
        name: intl('saenext.basic-info.CostAnalysis.ActualSavingContent.IdleCostReduction'),
        data: convertToChartArr(IdleReduceList, ['Time', 'Reduce'], [multiplyByThousand]),
      },
    ];

    const result = {
      savingItems: activeSaving,
      data,
    };

    if (!HpaReduceList?.length && !IdleReduceList?.length && !ActualCostList?.length) {
      result.data = [];
    }

    return result;
  };

  const {
    loading,
    run,
    data: analysisData = {} as any,
  } = useService(fetchAcualSaving, { time }, { manual: true });
  const { savingItems = [], data = [] } = analysisData;

  useEffect(() => {
    run({ time });
  }, [appId, time]);

  return (
    <Loading visible={loading} className="full-height full-width">
      <div className="flex justify-between mb" style={{ gap: 8 }}>
        {savingItems.map((item) => (
          <div style={{ flexGrow: 1 }}>
            <SavingCard {...item} />
          </div>
        ))}
      </div>
      <CostChat data={data} />
    </Loading>
  );
};

export default ActualSavingContent;
