import React from 'react';
import { Wbar, COLORS } from '@alife/aisc-widgets';
import { formatNumber } from '~/utils/global';

const CostChat = (props) => {
  const { data } = props;

  const options = {
    xAxis: {
      type: 'timeCat' as 'timeCat',
      mask: 'YYYY/MM/DD HH:mm',
      // labelFormatter: intl.date
    },
    yAxis: {
      labelFormatter: (v) => formatNumber(v),
    },
    stack: true,
    // stackReverse: false,
    showStackSum: true,
    legend: {
      visible: true,
      position: 'bottom' as 'bottom',
      align: 'left',
      marker: {
        symbol: 'hyphen' as 'hyphen',
        style(oldStyle) {
          return {
            ...oldStyle,
            r: 3,
            lineWidth: 6,
            lineAppendWidth: 0,
          };
        },
      },
    }
  };

  return (
    <Wbar
      key={data.length}
      height="300"
      config={options}
      data={data}
    />
  )
}

export default CostChat;