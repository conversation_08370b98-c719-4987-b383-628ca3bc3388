import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import services from '~/services';
import { convertToChartArr, multiplyByThousand } from '~/utils/transfer-data';
import SavingItems from './SavingItems';
import CostChat from './CostChat';
import { Loading, Message, useService } from '@ali/cnd';
import { isEmpty } from 'lodash';

const SavingCostContent = (props) => {
  const { appId, appConfig = {}, refresh, time, isSupportHygon, checkAppInstanceVersion } = props;

  const fetchSavingCost = async ({ time }) => {
    const res = await services.analysisReduceCost({
      params: {
        AppId: appId,
        AnalysisTime: time,
      },
    });

    if (!res?.Data) return {};

    const {
      CurrentOpenHpa,
      CurrentEnableIdle,
      EnableOpenHpa,
      EnableOpenIdle,
      EnableRescale,
      AppScalingRule,
      ReducedCpu,
      ReducedMem,
      HpaReduceList,
      IdleReduceList,
      RescaleReduceList,
      HpaReduceSum,
      IdleReduceSum,
      RescaleReduceSum,
    } = res.Data;

    const savingItems = {
      CurrentOpenHpa,
      CurrentEnableIdle,
      EnableOpenHpa,
      EnableOpenIdle,
      EnableRescale,
      AppScalingRule,
      ReducedCpu,
      ReducedMem,
      HpaReduceSum,
      IdleReduceSum,
      RescaleReduceSum,
    };

    const data = [
      {
        name: intl('saenext.basic-info.CostAnalysis.SavingCostContent.ElasticCostReduction'),
        data: convertToChartArr(HpaReduceList, ['Time', 'Reduce'], [multiplyByThousand]),
      },
      {
        name: intl('saenext.basic-info.CostAnalysis.SavingCostContent.IdleCostReduction'),
        data: convertToChartArr(IdleReduceList, ['Time', 'Reduce'], [multiplyByThousand]),
      },
      {
        name: intl(
          'saenext.basic-info.CostAnalysis.SavingCostContent.AdjustSpecificationsAndReduceCosts',
        ),
        data: convertToChartArr(RescaleReduceList, ['Time', 'Reduce'], [multiplyByThousand]),
      },
    ];

    const result = {
      savingItems,
      data,
    };

    if (!HpaReduceList?.length && !IdleReduceList?.length && !RescaleReduceList?.length) {
      result.data = [];
    }

    return result;
  };

  const {
    loading,
    run,
    data: analysisData = {} as any,
  } = useService(fetchSavingCost, { time }, { manual: true });
  const { savingItems = {}, data = [] } = analysisData;

  useEffect(() => {
    run({ time });
  }, [appId, time]);

  return (
    <Loading visible={loading} className="full-height full-width">
      {!isEmpty(savingItems) ? (
        <SavingItems appId={appId} appConfig={appConfig} refresh={refresh} isSupportHygon={isSupportHygon} checkAppInstanceVersion={checkAppInstanceVersion} {...savingItems} />
      ) : (
        <Message type="help">
          {intl.html(
            'saenext.basic-info.CostAnalysis.SavingCostContent.InsufficientHistoricalDataNoAnalysis',
          )}
        </Message>
      )}
      <CostChat data={data} />
    </Loading>
  );
};

export default SavingCostContent;
