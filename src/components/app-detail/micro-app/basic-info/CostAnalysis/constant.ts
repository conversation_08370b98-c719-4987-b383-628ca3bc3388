import { intl } from '@ali/cnd';
export enum ESaving {
  TOTAL = 'TOTAL',
  HPA = 'HPA',
  IDLE = 'IDLE',
  SPEC = 'SPEC',
}

export const SAVING_TITLE = {
  [ESaving.TOTAL]: intl(
    'saenext.basic-info.CostAnalysis.constant.ConfigurationOptimizationAndAdjustment',
  ),
  [ESaving.HPA]: intl('saenext.basic-info.CostAnalysis.constant.ElasticConfiguration'),
  [ESaving.IDLE]: intl('saenext.basic-info.CostAnalysis.constant.IdleConfiguration'),
};

export const SAVING_ITEMS = [
  {
    type: ESaving.HPA,
    name: intl('saenext.basic-info.CostAnalysis.constant.ElasticPolicy'),
  },
  {
    type: ESaving.IDLE,
    name: intl('saenext.basic-info.CostAnalysis.constant.IdleMode'),
  },
  {
    type: ESaving.SPEC,
    name: intl('saenext.basic-info.CostAnalysis.constant.AdjustSpecifications'),
  },
];
