import { Balloon, Card, Icon, intl } from '@ali/cnd';
import React from 'react';
import { ESaving, SAVING_TITLE } from './constant';
import If from '~/components/shared/If';
import { formatNumber, toCu } from '~/utils/global';

const SavingAmountCard = (props) => {
  const { type, savingAmount } = props;

  const title = SAVING_TITLE[type];

  const { value, unit } = toCu(savingAmount);

  return (
    <Card
      title={
        <div className="flex" style={{ fontSize: 12, fontWeight: 400, color: '#555555' }}>
          {intl('saenext.basic-info.CostAnalysis.SavingAmountCard.Pass')}
          {title}
          {intl('saenext.basic-info.CostAnalysis.SavingAmountCard.HelpYouSave')}
          <If condition={type === ESaving.TOTAL}>
            <Balloon
              align="t"
              trigger={<Icon size="xs" type="help" className="ml-s" />}
              closable={false}
            >
              {intl('saenext.basic-info.CostAnalysis.SavingAmountCard.ThisValueIsTheEstimated')}
            </Balloon>
          </If>
        </div>
      }
      showHeadDivider={false}
    >
      <div>
        <span style={{ fontSize: 28, lineHeight: 1.2 }}>{value}</span>
        <span style={{ color: '#666666' }}>{unit}</span>
      </div>
    </Card>
  );
};

export default SavingAmountCard;
