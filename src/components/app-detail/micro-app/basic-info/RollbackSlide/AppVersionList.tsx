import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import CndTable from '@ali/cnd-table';
import services from '~/services';
import { DateTime, LinkButton } from '@ali/cnd';
import DeploySlide from '../DeploySlide';
import If from '~/components/shared/If';
import { cloneDeep, replace } from 'lodash';
import { lowerFirstData } from '~/utils/transfer-data';
import RefreshButton from '~/components/shared/RefreshButton';
import useSingleValue from '~/hooks/useSingleValue';

const AppVersionList = (props) => {
  const { appId } = props;

  const [appConfigVersion, setAppConfigVersion] = useState({});
  const [value, onChange] = useSingleValue(props);

  const fetchData = async (params) => {
    const data = await services.listMicroAppVersions({
      AppId: appId,
    });
    if (data) {
      return {
        data: data.Data,
      };
    }
  };

  const getAppConfigVersion = async (Id) => {
    const data = await services.describeMicroApplicationConfig({
      AppId: appId,
      VersionId: Id,
    });
    if (data) {
      setAppConfigVersion(data.Data);
    }
  };

  const getBuildPackageUrl = async (packageVersion, idx, buildPackageUrl) => {
    const { Data } =
      (await services.getPackageVersionAccessableUrl(
        {
          AppId: appId,
          PackageVersion: packageVersion,
        },
        true,
      )) || {};

    const downloadUrl = Data || buildPackageUrl;
    const downloadUrlHttps = replace(downloadUrl, 'http://', 'https://');
    window.open(downloadUrlHttps);
  };

  return (
    <CndTable
      fetchData={fetchData}
      primaryKey="Id"
      rowSelection={{
        mode: 'single',
        selectedRowKeys: value,
        onChange,
      }}
      columns={[
        {
          title: intl('saenext.basic-info.RollbackSlide.AppVersionList.ReleaseTime'),
          dataIndex: 'CreateTime',
          cell: (val) => <DateTime value={val} />,
        },
        {
          title: intl('saenext.basic-info.RollbackSlide.AppVersionList.PackageVersionImageVersion'),
          dataIndex: 'Type',
          cell: (val, idx, record) => {
            const {
              type,
              warUrl,
              buildPackageUrl,
              id,
              downloadUrlLoading = false,
            } = lowerFirstData(cloneDeep(record));
            const urlMap = {
              image: warUrl,
              upload: replace(warUrl, /.*\//, '').replace(/\?.*/, ''),
              url: replace(buildPackageUrl, /.*\//, '').replace(/\?.*/, ''),
            };
            return (
              <div>
                <span>{urlMap[type]}</span>
                <If condition={type === 'upload'}>
                  <a href={warUrl} target="_blank" className="ml">
                    {intl('saenext.basic-info.RollbackSlide.AppVersionList.Download')}
                  </a>
                </If>
                <If condition={type === 'url' && buildPackageUrl}>
                  <RefreshButton
                    handler={() => getBuildPackageUrl(id, idx, buildPackageUrl)}
                    text
                    label={intl('saenext.basic-info.RollbackSlide.AppVersionList.Download')}
                    className="ml"
                  />
                </If>
              </div>
            );
          },
        },
        {
          title: intl('saenext.basic-info.RollbackSlide.AppVersionList.ConfigurationInformation'),
          dataIndex: 'Id',
          cell: (value) => {
            return (
              // TODO : JOB Rollback
              <DeploySlide
                isPreview
                title={intl(
                  'saenext.basic-info.RollbackSlide.AppVersionList.ViewHistoricalVersionConfigurationInformation',
                )}
                appConfig={appConfigVersion}
                onClose={() => setAppConfigVersion({})}
              >
                <LinkButton onClick={() => getAppConfigVersion(value)}>
                  {intl('saenext.basic-info.RollbackSlide.AppVersionList.ViewDetails')}
                </LinkButton>
              </DeploySlide>
            );
          },
        },
      ]}
      pagination={false}
    />
  );
};

export default AppVersionList;
