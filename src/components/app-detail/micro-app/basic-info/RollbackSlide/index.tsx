import { intl } from '@ali/cnd';
import { Field, Message, SlidePanel } from '@ali/cnd';
import React, { useState } from 'react';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import If from '~/components/shared/If';
import { getParams, removeParams, setSearchParams } from '~/utils/global';
import RollbackForm from './RollbackForm';
import { jsonParse } from '~/utils/transfer-data';
import services from '~/services';

const RollbackSlide = (props) => {
  const { appId, type = 'app', children } = props;

  const defaultOpen = getParams('action') === 'rollback';
  const [slideVisible, setSlideVisible] = useState(defaultOpen);
  const [submitLoading, setSubmitLoading] = useState(false);

  const field = Field.useField({
    parseName: true,
  });

  const toggleVisible = () => {
    if (!slideVisible) {
      setSearchParams({ action: 'rollback' });
    } else {
      removeParams('action');
      field.resetToDefault();
    }
    setSlideVisible(!slideVisible);
  };

  const onSubmit = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) {
      return;
    }

    setSubmitLoading(true);
    const request = type === 'app' ? appRollbackRequest : jobRollbackRequest;
    const res = await request(values);
    setSubmitLoading(false);

    if (res) {
      Message.success(intl('saenext.basic-info.RollbackSlide.RollbackSuccessful'));
      toggleVisible();
    } else {
      Message.error(intl('saenext.basic-info.RollbackSlide.RollbackFailed'));
    }
  };

  const appRollbackRequest = async (values) => {
    const res = await services.rollbackApplicationMicro({
      AppId: appId,
      ...values,
    });
    if (res && res.Data) {
      return true;
    } else {
      return false;
    }
  }

  const jobRollbackRequest = async (values) => {
    const { VersionId } = values;
    const infoRes = await services.DescribeJob({
      params: {
      AppId: appId,
      VersionId,
    }
  });
    if (!infoRes || !infoRes.Data) return false;

    const { Data } = infoRes;

    if (Data.PackageVersion) {
      Data.PackageVersion = new Date().getTime();
    }

    const res = await services.UpdateJob({
      params: {
        ...Data,
      }
    });

    if (res && res.Data) {
      return true;
    } else {
      return false;
    }
  }

  return (
    <>
      <AddPropsWrap onClick={toggleVisible}>{children}</AddPropsWrap>
      <SlidePanel
        title={intl('saenext.basic-info.RollbackSlide.RollbackHistory')}
        isShowing={slideVisible}
        width={1200}
        isProcessing={submitLoading}
        onMaskClick={toggleVisible}
        onClose={toggleVisible}
        onCancel={toggleVisible}
        onOk={onSubmit}
      >
        <If condition={slideVisible}>
          <RollbackForm appId={appId} type={type} field={field} />
        </If>
      </SlidePanel>
    </>
  );
};

export default RollbackSlide;
