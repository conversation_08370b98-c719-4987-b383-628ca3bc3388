import { intl } from '@ali/cnd';
import { Field, Form, Radio, ToolTipCondition } from '@ali/cnd';
import React, { useContext } from 'react';
import AppVersionList from './AppVersionList';
import If from '~/components/shared/If';
import C from '~/constants/common';
import MicroAppContext from '~/utils/microAppContext';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import SimpleCollapse from '~/components/shared/SimpleCollapse';
import DeployStrategy from '../DeploySlide/DeployStrategy';

const RollbackForm = (props) => {
  const { appId, type, field } = props;

  const { appConfig, scaleRuleEnabled } = useContext(MicroAppContext);

  const { AssociateEip = false } = appConfig;

  return (
    <Form field={field} useLabelForErrorMessage>
      <Form.Item
        required
        label={intl('saenext.basic-info.RollbackSlide.RollbackForm.RollbackVersion')}
        help={intl('saenext.basic-info.RollbackSlide.RollbackForm.PleaseSelectAHistoricalVersion')}
      >
        <AppVersionList appId={appId} name="VersionId" />
      </Form.Item>
      <If condition={type !== 'job'}>
        <If condition={scaleRuleEnabled}>
          <Form.Item
            label={intl('saenext.basic-info.RollbackSlide.RollbackForm.RestoreAutomaticElasticity')}
            required
            {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
            className='none'
          >
            <Radio.Group name="AutoEnableApplicationScalingRule" defaultValue={true}>
              <Radio id="true" value={true} disabled={AssociateEip}>
                <ToolTipCondition
                  show={AssociateEip}
                  tip={intl('saenext.basic-info.RollbackSlide.RollbackForm.TheApplicationIsBoundTo')}
                  align="t"
                >
                  <TextWithBalloon
                    text={intl(
                      'saenext.basic-info.RollbackSlide.RollbackForm.AutomaticSystemRecovery',
                    )}
                    tips={
                      <>
                        {intl(
                          'saenext.basic-info.RollbackSlide.RollbackForm.TheSystemAutomaticallyRestoresElasticity',
                        )}
                        <br />
                        {intl(
                          'saenext.basic-info.RollbackSlide.RollbackForm.HoweverWhenTheApplicationFails',
                        )}
                      </>
                    }
                  />
                </ToolTipCondition>
              </Radio>
              <Radio id="false" value={false}>
                {intl('saenext.basic-info.RollbackSlide.RollbackForm.ManuallyEnableRecovery')}
              </Radio>
            </Radio.Group>
          </Form.Item>
        </If>
        <SimpleCollapse
          text={intl('saenext.basic-info.RollbackSlide.RollbackForm.SelectBatchGrayStrategy')}
          defaultOpen={false}
        >
          <DeployStrategy field={field} appConfig={appConfig} autoReset />
        </SimpleCollapse>
      </If>
    </Form>
  );
};

export default RollbackForm;
