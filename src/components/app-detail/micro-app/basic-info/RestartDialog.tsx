import React, { forwardRef, useContext, useImperativeHandle, useState } from 'react';
import { Dialog, Field, Form, intl, Loading, Message, Radio } from '@ali/cnd';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import MinReadyInstanceField from '~/components/shared/MinReadyInstance/MinReadyInstanceField';
import C from '~/constants/common';
import services from '~/services';
import MicroAppContext from '~/utils/microAppContext';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';

type RestartType = {
  MinReadyInstanceRatio: number,
  MinReadyInstances: number,
}
const RestartDialog = (props, ref) => {
  const { appId, refresh, children } = props;
  const [visible, setVisible] = useState(false);
  const [restartLoading, setRestartLoading] = useState(false);
  const field = Field.useField();

  const { AutoEnableApplicationScalingRule } = field.getValues() as any;

  const { scaleRuleEnabled } = useContext(MicroAppContext);

  useImperativeHandle(ref, () => ({
    toogleVisible,
  }));

  const handleRestart = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) {
      return;
    }
    setRestartLoading(true);
    const params={
      AppId: appId,
      ...values as RestartType,
    }
    if(scaleRuleEnabled){
      Reflect.set(params, 'AutoEnableApplicationScalingRule', true);
    }
    const { Data } =
      (await services.restartMicroApplication(params)) || {};
    setRestartLoading(false);
    if (Data) {
      Message.success(intl('saenext.micro-app.basic-info.AppActionBar.RestartSucceeded'));
      refresh();
      toogleVisible();
    } else {
      Message.error(intl('saenext.micro-app.basic-info.AppActionBar.RestartFailed'));
    }
  };

  const toogleVisible = () => {
    setVisible(!visible);
  };

  return (
    <>
      <AddPropsWrap onClick={toogleVisible}>{children}</AddPropsWrap>
      <Dialog
        title={intl('saenext.micro-app.basic-info.AppActionBar.RestartTheApplication')}
        visible={visible}
        onOk={handleRestart}
        onClose={toogleVisible}
        onCancel={toogleVisible}
        okProps={{ loading: restartLoading }}
      >
        <Loading visible={restartLoading} style={{ width: 800 }}>
          <Message type={'notice'}>
            {intl('saenext.micro-app.basic-info.AppActionBar.AfterTheApplicationIsRestarted')}
          </Message>
          <Form field={field} className="pd-card">
            <Form.Item
              required
              label={intl(
                'saenext.micro-app.basic-info.AppActionBar.MinimumNumberOfSurvivingInstances',
              )}
              {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
            >
              <MinReadyInstanceField field={field} />
            </Form.Item>
            {/* <If condition={scaleRuleEnabled}>
              <Form.Item
                label={intl(
                  'saenext.micro-app.basic-info.RestartDialog.RestoreAutomaticElasticity',
                )}
                help={intl(
                  'saenext.micro-app.basic-info.RestartDialog.TheCurrentApplicationHasEnabled',
                )}
                required
                {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
              >
                <Radio.Group name="AutoEnableApplicationScalingRule">
                  <Radio id="true" value={true}>
                    {intl('saenext.micro-app.basic-info.RestartDialog.AutomaticSystemRecovery')}
                  </Radio>
                  <Radio id="false" value={false}>
                    {intl('saenext.micro-app.basic-info.RestartDialog.ManuallyEnableRecovery')}
                  </Radio>
                </Radio.Group>
                <If condition={AutoEnableApplicationScalingRule === true}>
                  <Message type="notice" className="mt">
                    {intl(
                      'saenext.micro-app.basic-info.RestartDialog.AfterTheSystemAutomaticallyRestores',
                    )}

                    <br />
                    {intl(
                      'saenext.micro-app.basic-info.RestartDialog.MonitoringMetricsElasticityAndHybrid',
                    )}

                    <a href={CachedData.confLink('help:sae:configure-an-auto-scaling-policy')} target="_blank">
                      {intl('saenext.micro-app.basic-info.RestartDialog.ViewDetails')}
                    </a>
                  </Message>
                </If>
                <If condition={AutoEnableApplicationScalingRule === false}>
                  <Message type="notice" className="mt">
                    {intl(
                      'saenext.micro-app.basic-info.RestartDialog.AfterYouSelectManuallyEnable',
                    )}
                  </Message>
                </If>
              </Form.Item>
            </If> */}
          </Form>
        </Loading>
      </Dialog>
    </>
  );
};

export default forwardRef(RestartDialog);
