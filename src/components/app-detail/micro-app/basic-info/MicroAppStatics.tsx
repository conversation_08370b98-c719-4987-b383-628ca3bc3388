import React, { memo, useContext, useEffect, useState, useMemo } from 'react';
// docs: https://unpkg.alibaba-inc.com/@ali/cnd-stat-card/build/index.html
import { Balloon, StatCard, intl, CndTable } from '@ali/cnd';
import services from '~/services';
import { isEmpty } from 'lodash';
import FeatureContext from '~/utils/featureContext';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import { isInteger } from '~/utils/global';
import { confFeature } from '@alicloud/console-one-conf';
import If from '~/components/shared/If';
import SwitchIcon from '~/components/shared/SwitchIcon';
import CachedData from '~/cache/common'

type Statics = {
  cu: number;
  cpu: number;
  memory: number;
  activeCpu: number;
  idleCpu: number;
  realTimeCpu: number;
  realTimeMem: number;
  formatInstanceCount: number;
  diskSize: number;
  realDiskSize: number;
};

const Default = '0';
const calcRealTimeMetric = (core, instanceCount) => {
  if (!instanceCount) return Default;

  return instanceCount ? (Math.round((core / instanceCount) * 2) / 2) * instanceCount : Default;
};

const MicroAppStatics = (props) => {
  const { appConfig = {}, hideDisk } = props || {};
  const { Cpu, SlsConfigs = 0 } = appConfig;
  const specCpu = Cpu / 1000;
  // sls计量单位
  const slsInstanceCount = SlsConfigs ? 0.1 : 0;

  const [micorApp, setMicroApp] = useState<Statics>({
    cu: 0,
    cpu: 0,
    memory: 0,
    activeCpu: 0,
    idleCpu: 0,
    realTimeCpu: 0,
    realTimeMem: 0,
    formatInstanceCount: 0,
    diskSize: 0,
    realDiskSize: 0,
  });

  const { feature } = useContext(FeatureContext);
  const { idle: isAllocateIdle } = feature;
  const diskSize_enable = confFeature('diskSize_enable');

  useEffect(() => {
    if (!isEmpty(appConfig)) {
      getWebAppStatics();
    }
  }, [JSON.stringify(appConfig)]);

  const getWebAppStatics = async () => {
    const result = await services.getResourceStatistics({
      AppId: appConfig.AppId,
    });
    const { Summary = {}, RealTimeRes = {} } = result?.Data || {};
    const { Cu = 0, Cpu = 0, Memory = 0, ActiveCpu, IdleCpu, EphemeralStorage = 0 } = Summary;
    const {
      Cpu: RealTimeCpu = 0,
      Memory: RealTimeMem = 0,
      EphemeralStorage: RealDiskSize = 0,
    } = RealTimeRes;

    //  app cu
    // 活跃cpu使用量（核*秒）* 1 + 闲置cpu使用量（核*秒）* 0.2 + 内存（GiB*秒）* 0.25
    // 接口单位：核·分、Gib·分，需要 * 60
    // const _cu = (ActiveCpu * 1 + IdleCpu * 0.2 + Memory * 0.25) * 60;

    const _micorApp = {
      cu: Cu,
      cpu: Cpu,
      memory: Memory,
      activeCpu: ActiveCpu,
      idleCpu: IdleCpu,
      realTimeCpu: Math.round(RealTimeCpu/60),
      realTimeMem: Math.round(RealTimeMem/60),
      // 当前计费实例数
      formatInstanceCount: Math.round(RealTimeCpu / (specCpu + slsInstanceCount)) || 0,
      diskSize: EphemeralStorage,
      realDiskSize: Math.round(RealDiskSize/60),
    };
    setMicroApp(_micorApp);
  };

  const toCu = (value, fixed = 2) => {
    const cu = { value, unit: 'CU' };

    if (value >= 100000000) {
      cu.unit = intl('saenext.micro-app.basic-info.MicroAppStatics.BillionCu');
      const _value = value / 100000000;
      cu.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
      return cu;
    }

    if (value >= 10000) {
      cu.unit = intl('saenext.micro-app.basic-info.MicroAppStatics.Wancu');
      const _value = value / 10000;
      cu.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
      return cu;
    }

    return cu;
  };

  const ActiveIdleCpu = () => {
    const columns = [
      {
        title: '',
        dataIndex: 'type',
      },
      {
        title: '',
        dataIndex: 'usage',
        cell: (value) => (
          <>
            {intl.number(value)}
            <span className="ml-xs mr-xs color-gray">/</span>
            {'Core·min'}
          </>
        ),
      },
    ];

    const dataSource = [
      {
        type: intl('saenext.micro-app.basic-info.MicroAppStatics.ActiveVcpuUsage'),
        usage: micorApp.activeCpu,
      },
      {
        type: intl('saenext.micro-app.basic-info.MicroAppStatics.IdleVcpuUsage'),
        usage: micorApp.idleCpu,
      },
    ];

    return (
      <If condition={isAllocateIdle}>
        <Balloon.Tooltip
          triggerType="click"
          trigger={<SwitchIcon className="ml-xs pointer" size="small" />}
        >
          <CndTable
            dataSource={dataSource}
            hasHeader={false}
            hasBorder={true}
            columns={columns}
            tableWidth={360}
          />
        </Balloon.Tooltip>
      </If>
    );
  };

  const statCardItems = useMemo(() => {
    const items = [
      {
        key: 'cuMonth',
        title: (
          <TextWithBalloon
            text={intl('saenext.micro-app.basic-info.MicroAppStatics.CuUsageThisMonth')}
            tips={intl('saenext.micro-app.basic-info.MicroAppStatics.TheTotalAmountOfCu')}
          />
        ),

        count: [toCu(micorApp.cu).value, toCu(micorApp.cu).unit],
      },
      {
        key: 'cpuMonth',
        title: (
          <TextWithBalloon
            text={intl('saenext.micro-app.basic-info.MicroAppStatics.CpuUsageThisMonth')}
            tips={intl('saenext.micro-app.basic-info.MicroAppStatics.TheTotalCpuResourcesUsed')}
          />
        ),

        count: [
          micorApp.cpu === 0 ? Default : intl.number(micorApp.cpu),
          <>
            {'Core·min'}
            <ActiveIdleCpu />
          </>,
        ],
      },
      {
        key: 'memoryMonth',
        title: (
          <TextWithBalloon
            text={intl('saenext.micro-app.basic-info.MicroAppStatics.MemoryUsageThisMonth')}
            tips={intl('saenext.micro-app.basic-info.MicroAppStatics.TheTotalMemoryResourcesUsed')}
          />
        ),

        count: [micorApp.memory === 0 ? Default : intl.number(micorApp.memory), 'GiB·min'],
      },
      {
        key: 'diskSizeMonth',
        visible: diskSize_enable && !hideDisk,
        title: (
          <TextWithBalloon
            text={intl('saenext.micro-app.basic-info.MicroAppStatics.TemporaryDiskUsageThisMonth')}
            tips={intl('saenext.micro-app.basic-info.MicroAppStatics.TheTotalNumberOfTemporary')}
          />
        ),

        count: [micorApp.diskSize === 0 ? Default : intl.number(micorApp.diskSize), 'GiB·min'],
      },
      {
        key: 'cpuMin',
        title: (
          <TextWithBalloon
            text={intl('saenext.micro-app.basic-info.MicroAppStatics.CpuUsagePerMinute')}
            tips={intl('saenext.micro-app.basic-info.MicroAppStatics.TotalCpuResourcesUsedBy')}
          />
        ),

        count: [micorApp.realTimeCpu, 'Core·min'],
      },
      {
        key: 'memoryMin',
        title: (
          <TextWithBalloon
            text={intl('saenext.micro-app.basic-info.MicroAppStatics.MemoryUsagePerMinute')}
            tips={intl('saenext.micro-app.basic-info.MicroAppStatics.TheTotalAmountOfMem')}
          />
        ),

        count: [micorApp.realTimeMem, 'GiB·min'],
      },
      {
        key: 'diskSizeMin',
        visible: diskSize_enable && !hideDisk,
        title: (
          <TextWithBalloon
            text={intl('saenext.micro-app.basic-info.MicroAppStatics.TemporaryDiskUsagePerMinute')}
            tips={intl('saenext.micro-app.basic-info.MicroAppStatics.TheTotalNumberOfTemporary.1')}
          />
        ),

        count: [micorApp.realDiskSize, 'GiB·min'],
      },
    ];
    return items.filter((item) => item.visible !== false);
  }, [micorApp, diskSize_enable, hideDisk]);

  return (
    <>
      <StatCard
        title={intl('saenext.micro-app.basic-info.MicroAppStatics.MeteringData')}
        extra={
          <a
            className="gray-text ml-s"
            href={CachedData.confLink('help:sae:measurement-method')}
            target="__blank"
          >
            {intl('saenext.micro-app.basic-info.MicroAppStatics.UnderstandMeasurementMethods')}
          </a>
        }
        // @ts-ignore
        items={statCardItems}
      />
    </>
  );
};

export default memo(MicroAppStatics);
