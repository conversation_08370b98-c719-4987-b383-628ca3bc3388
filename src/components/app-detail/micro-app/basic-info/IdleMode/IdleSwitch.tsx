import { intl } from '@ali/cnd';
import { Dialog, Message, Switch } from '@ali/cnd';
import React from 'react';
import services from '~/services';

const IdleSwitch = (props) => {
  const { appId, checked, onChange, refresh, style = {} } = props;

  const updateMode = (val) => {
    Dialog.confirm({
      v2: true,
      centered: true,
      width: 500,
      content: val
        ? intl('saenext.basic-info.IdleMode.IdleSwitch.AfterTheIdleModeIs')
        : intl('saenext.basic-info.IdleMode.IdleSwitch.AfterTheIdleModeIs.1'),
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res = await services.updateAppMode({
            params: {
              AppId: appId,
              EnableIdle: val,
            },
          });
          const action = val
            ? intl('saenext.basic-info.IdleMode.IdleSwitch.Enable')
            : intl('saenext.basic-info.IdleMode.IdleSwitch.Close');
          if (res) {
            onChange?.(val);
            Message.success(
              intl('saenext.basic-info.IdleMode.IdleSwitch.ActionIdleModeSucceeded', {
                action: action,
              }),
            );
            refresh();
            resolve(true);
          } else {
            Message.error(
              intl('saenext.basic-info.IdleMode.IdleSwitch.ActionIdleModeFailed', {
                action: action,
              }),
            );
            reject();
          }
        });
      },
    });
  };

  return <Switch style={style} size="small" checked={checked} onChange={updateMode} />;
};

export default IdleSwitch;
