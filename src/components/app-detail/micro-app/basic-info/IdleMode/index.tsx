import React, { useEffect, useState } from 'react';
import { Collapse, Dialog, Form, intl, Loading, Message, Switch } from '@ali/cnd';
import IdleIndicatorMonitor from '~/components/app-detail/micro-app/basic-info/idle-monitor';
import If from '~/components/shared/If';
import IdleSwitch from './IdleSwitch';

const IdleMode = (props) => {
  const { appConfig, refresh } = props;

  const { AppId } = appConfig;

  const [enableIdle, setEnableIdle] = useState(appConfig.EnableIdle);

  useEffect(() => {
    setEnableIdle(appConfig.EnableIdle);
  }, [appConfig.EnableIdle]);

  return (
    <Collapse defaultExpandedKeys={['0']} className="mt">
      <Collapse.Panel title={intl('saenext.basic-info.ScalingGroup.ScalingList.IdleMode')}>
        <Form inline>
          <Form.Item
            size="small"
            label={intl('saenext.basic-info.ScalingGroup.ScalingList.IdleMode')}
            help={intl('saenext.basic-info.IdleMode.AfterTheIdleModeIs')}
          >
            <IdleSwitch
              style={{ margin: '4px 0' }}
              appId={AppId}
              checked={enableIdle}
              refresh={refresh}
            />
          </Form.Item>
        </Form>
        <If condition={enableIdle}>
          <IdleIndicatorMonitor appId={AppId} />
        </If>
      </Collapse.Panel>
    </Collapse>
  );
};

export default IdleMode;
