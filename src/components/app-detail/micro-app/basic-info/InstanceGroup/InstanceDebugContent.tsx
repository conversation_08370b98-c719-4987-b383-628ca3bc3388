import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { Button, Checkbox, ConsoleContext, Icon, Step, useService } from '@ali/cnd';
import If from '~/components/shared/If';
import modelDecorator from '~/components/shared/Modal/decorator';
import services from '~/services';
import WebShellDialog from '../WebShellDialog';

const InstanceDebugContent = (props) => {
  const { toggleModal, appId, groupId, instanceId, refresh } = props;

  const [curStep, setCurStep] = useState(0);
  const [acceptChecked, setAcceptChecked] = useState(false);
  const [status, setStatus] = useState('');

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const intervalRef = useRef(null);

  const startInterval = () => {
    intervalRef.current = setInterval(async () => {
      queryInstanceList();
    }, 5000);
  };

  const stopInterval = () => {
    clearInterval(intervalRef.current);
  };

  const queryInstanceList = async () => {
    const params = {
      AppId: appId,
      GroupId: groupId,
      CurrentPage: 1,
      PageSize: 100,
      Reverse: false,
    };
    const { Data: { Instances = [], TotalSize = 0 } = {} } =
      (await services.DescribeApplicationInstances(params, true)) || {};

    const curInstance = Instances.find((item) => item.InstanceId === instanceId);
    setStatus(curInstance?.InstanceContainerStatus);
  };

  const debugRequest = async () => {
    const res = await services.DebugInstance({
      AppId: appId,
      InstanceId: instanceId,
    });
    return res;
  };

  const { loading, run, data: { Data } = {} } = useService(debugRequest, {}, { manual: true });

  useEffect(() => {
    if (Data) {
      startInterval();
      setCurStep(1);
    }
  }, [Data]);

  useEffect(() => {
    return () => {
      stopInterval();
    };
  }, []);

  useEffect(() => {
    if (status === 'Running') {
      stopInterval();
      refresh();
    }
  }, [status]);

  const handleDebug = async () => {
    await run();
  };

  const onCancel = () => {
    toggleModal({
      visible: false,
    });
  };

  const STEPS = [
    {
      title: intl(
        'saenext.basic-info.InstanceGroup.InstanceDebugContent.ModifyApplicationConfigurationParameters',
      ),
    },
    { title: intl('saenext.basic-info.InstanceGroup.InstanceDebugContent.DebugAnInstance') },
  ];

  return (
    <>
      <div className="flex flex-start" style={{ height: 300 }}>
        <div className="border-r pd-card" style={{ width: 200 }}>
          <Step current={curStep} direction="ver" shape="circle" animation={false}>
            {STEPS.map((item, index) => (
              <Step.Item key={index} title={item.title} onClick={() => setCurStep(index)} />
            ))}
          </Step>
        </div>
        <div style={{ width: 800, padding: 20 }}>
          <If condition={curStep === 0}>
            <p>
              {intl(
                'saenext.basic-info.InstanceGroup.InstanceDebugContent.OneClickDebuggingIsSpecifically',
              )}
            </p>
            <p>
              {intl(
                'saenext.basic-info.InstanceGroup.InstanceDebugContent.TheFailureOfAnApplication',
              )}
            </p>
            <Checkbox checked={acceptChecked} onChange={() => setAcceptChecked(!acceptChecked)}>
              {intl('saenext.basic-info.InstanceGroup.InstanceDebugContent.IHaveKnownTheSystem')}
            </Checkbox>
          </If>
          <If condition={curStep === 1}>
            <p style={{ fontSize: 12 }}>
              <If condition={status === 'Running'}>
                <span style={{ marginRight: 10 }}>
                  {intl(
                    'saenext.basic-info.InstanceGroup.InstanceDebugContent.TheInstanceHasBeenStarted',
                  )}
                </span>
                <WebShellDialog regionId={regionId} appId={appId} instanceId={instanceId}>
                  <Button type="primary">
                    {intl('saenext.basic-info.InstanceGroup.InstanceDebugContent.DebugAnInstance')}
                  </Button>
                </WebShellDialog>
              </If>
              <If condition={status !== 'Running'}>
                <Icon type="loading" size="xs" style={{ marginRight: 10 }} />
                <span>
                  {intl(
                    'saenext.basic-info.InstanceGroup.InstanceDebugContent.TheInstanceIsStartingIt',
                  )}
                </span>
              </If>
            </p>
          </If>
        </div>
      </div>
      <div className="text-right">
        <If condition={curStep === 0}>
          <Button
            type="primary"
            className="mr-s"
            loading={loading}
            onClick={handleDebug}
            disabled={!acceptChecked}
          >
            {intl('saenext.basic-info.InstanceGroup.InstanceDebugContent.NextStep')}
          </Button>
          <Button onClick={onCancel}>
            {intl('saenext.basic-info.InstanceGroup.InstanceDebugContent.Cancel')}
          </Button>
        </If>
        <If condition={curStep === 1}>
          <Button onClick={onCancel}>
            {intl('saenext.basic-info.InstanceGroup.InstanceDebugContent.Close')}
          </Button>
        </If>
      </div>
    </>
  );
};

export default modelDecorator(InstanceDebugContent);
