import React from 'react';
import {
  ColorMap,
  getStatus,
  healthInstructions,
  UnhealthyMap,
  WithoutHealthConfigMap,
} from './constant';
import {
  BalloonTooltip,
  Button,
  intl,
  Message,
  StatusIndicator,
  Tag,
  ToolTipCondition,
} from '@ali/cnd';
import { confFeature } from '@alicloud/console-one-conf';
import InstanceDebugDialog from '../InstanceDebugDialog';
import ExternalLink from '~/components/shared/ExternalLink';
import If from '~/components/shared/If';
import useRegion from '~/hooks/useRegion';
import CachedData from '~/cache/common';

const InstanceStatus = (props) => {
  const { appId, groupId, associateEip, refresh, record, showHealthTip = true, showRestart = false } = props;
  const {
    InstanceContainerStatus,
    InstanceHealthStatus,
    InstanceId,
    Eip,
    DebugStatus,
    InstanceContainerRestarts,
  } = record;

  const formatVal = getStatus(InstanceContainerStatus);

  const crashTip = CrashTip({ ...props });

  const withoutHealthTip = HealthTip({ ...props, showWithoutHealthConfig: showHealthTip });
  const unHealthyTip = HealthTip({ ...props, showUnhealthy: showHealthTip });

  const tip = crashTip || withoutHealthTip;

  return (
    <>
      <StatusIndicator type={formatVal} style={{ color: ColorMap[formatVal] }}>
        <ToolTipCondition show={!!tip} tip={tip} align="t">
          {InstanceContainerStatus}
        </ToolTipCondition>
      </StatusIndicator>
      <If condition={InstanceContainerStatus === 'Running'}>
        <If condition={!!unHealthyTip}>
          <BalloonTooltip
            align="t"
            trigger={
              <Tag color="red" className="ml">
                {intl('saenext.InstanceGroup.InstanceStatus.HealthCheckFailed')}
              </Tag>
            }
          >
            {unHealthyTip}
          </BalloonTooltip>
        </If>
        <If condition={DebugStatus}>
          <DebugTip />
        </If>
        <If condition={associateEip && !Eip}>
          <EipTip />
        </If>
      </If>
      <CrashTip
        {...props}
        onlyButton
      />
      <If condition={showRestart}>
        <RestartCom InstanceContainerRestarts={InstanceContainerRestarts} />
      </If>
    </>
  );
};

const CrashTip = (props) => {
  const { appId, groupId, refresh, record, onlyButton } = props;
  const { InstanceId, InstanceContainerStatus } = record;
  if (InstanceContainerStatus !== 'CrashLoopBackOff') {
    return null;
  }

  return (
    <>
      <If condition={!onlyButton}>
        {intl('saenext.basic-info.InstanceGroup.InstanceList.TheApplicationInstanceFailedTo')}
      </If>
      <InstanceDebugDialog
        appId={appId}
        groupId={groupId}
        instanceId={InstanceId}
        refresh={refresh}
      >
        <Button type="primary" text>
          {intl('saenext.basic-info.InstanceGroup.InstanceList.OneClickDebugging')}
        </Button>
      </InstanceDebugDialog>
    </>
  );
};

const HealthTip = (props) => {
  const {
    appId,
    groupId,
    refresh,
    record,
    showUnhealthy = false,
    showWithoutHealthConfig = false,
  } = props;
  const { InstanceId, InstanceContainerStatus, InstanceHealthStatus, UnhealthyMessage } = record;

  const regionId = useRegion();

  if (InstanceContainerStatus !== 'Running') {
    return null;
  }

  const unhealthyKey = UnhealthyMap[InstanceHealthStatus];
  const withoutHealthConfigKey = WithoutHealthConfigMap[InstanceHealthStatus];

  if (unhealthyKey && showUnhealthy) {
    return (
      <>
        <StatusIndicator type="error" style={{ color: 'red' }}>
          {intl('saenext.InstanceGroup.InstanceStatus.HealthCheckUnhealthykeyFailed', {
            unhealthyKey: unhealthyKey,
          })}
        </StatusIndicator>
        <div>
          <div>{UnhealthyMessage}</div>
          <div>
            {intl('saenext.InstanceGroup.InstanceStatus.HealthCheckUnhealthykeyFailed', {
              unhealthyKey: unhealthyKey,
            })}
            <ExternalLink
              url={`/${regionId}/app-list/${appId}/micro-app/event?ObjectKind=Pod&ObjectName=${InstanceId}&EventType=Warning`}
            />

            {healthInstructions}
          </div>
        </div>
      </>
    );
  } else if (withoutHealthConfigKey && showWithoutHealthConfig) {
    return (
      <>
        <StatusIndicator type="warning" style={{ color: 'orange' }}>
          {intl('saenext.InstanceGroup.InstanceStatus.WithouthealthconfigkeyHealthCheckIsNot', {
            withoutHealthConfigKey: withoutHealthConfigKey,
          })}
        </StatusIndicator>
        <div>
          {intl(
            'saenext.InstanceGroup.InstanceStatus.ConfigureAHealthCheckWithouthealthconfigkey',
            { withoutHealthConfigKey: withoutHealthConfigKey },
          )}
          <ExternalLink
            url={CachedData.confLink('help:sae:configure-application-health-checks')}
            label={intl('saenext.basic-info.InstanceGroup.InstanceList.ViewDetails')}
          />
        </div>
      </>
    );
  }

  // Healthy, NotCheckedYet
  return null;
};

const DebugTip = () => {
  return (
    <BalloonTooltip
      trigger={
        <Tag color="orange">
          {intl('saenext.basic-info.InstanceGroup.InstanceList.UnundertakenTraffic')}
        </Tag>
      }
    >
      {intl('saenext.basic-info.InstanceGroup.InstanceList.TheOneClickDebuggingFeature')}
      <a href={CachedData.confLink('help:sae:debug-an-application-instance')} target="_blank">
        {intl('saenext.basic-info.InstanceGroup.InstanceList.HowToUseOneClick')}
      </a>
    </BalloonTooltip>
  );
};

const EipTip = () => {
  return (
    <BalloonTooltip
      trigger={
        <Tag color="orange">
          {intl('saenext.basic-info.InstanceGroup.InstanceList.InsufficientAvailableEip')}
        </Tag>
      }
    >
      {intl('saenext.basic-info.InstanceGroup.InstanceList.GoToTheVpcConsole')}
    </BalloonTooltip>
  );
};

const RestartCom = (props) => {
  const { InstanceContainerRestarts } = props;
  return (
    <div className={InstanceContainerRestarts ? 'text-warning' : 'text-description'}>
      {intl('saenext.basic-info.InstanceGroup.InstanceList.RestartInstancecontainerrestartsTimes', {
        InstanceContainerRestarts,
      })}
    </div>
  );
};

export default InstanceStatus;
