import React from "react";
import { intl } from "@ali/cnd";
import { includes } from "lodash";
import CachedData from "~/cache/common";

export const UnhealthyMap = {
  Unhealthy: 'Liveness and Readiness',
  LivenessUnhealthy: 'Liveness',
  ReadinessUnhealthy: 'Readiness'
}
export const WithoutHealthConfigMap = {
  WithoutHealthCheckConfig: 'Liveness and Readiness',
  WithoutLivenessConfig: 'Liveness',
  WithoutReadinessConfig: 'Readiness',
}

export const ColorMap = {
  success: '#090',
  error: '#f00',
  warning: '#f90',
  disabled: '#ccc',
};

export const getStatus = (status) => {
  // 进行中，不是Running  带ing的 都可以是进行中   —黄色
  // Pending
  // ContainerCreating
  // PodInitializing
  // Terminating

  // 正常运行，或者结束     — 绿色
  // Running
  // Completed

  // 失败   —红色
  // Failed
  // Unknown
  // BackOff  //
  // ErrImagePull
  // ImagePullBackOff
  // CrashLoopBackOff

  // 应该是失败      —红色
  // Init:Signal:xxx
  // Init:ExitCode:xxx
  // ExitCode:xxx
  // Signal:xxx
  const successList = ['Running', 'Completed', 'Init:'];
  if (includes(successList, status) || (status.startsWith('Init:') && status!=='Init:CrashLoopBackOff' && status!=='Init:CreateContainerError')) {
    return 'success';
  } else if (includes(status, 'ing')) {
    return 'warning';
  } else if (status === 'stop') {
    return 'disabled';
  } else {
    return 'error';
  }
};

export const healthInstructions = (
  <>
    {intl('saenext.basic-info.InstanceGroup.InstanceList.CommonReasons')}

    <br />
    {intl('saenext.basic-info.InstanceGroup.InstanceList.TheDelayTimeIsToo')}

    <br />
    {intl('saenext.basic-info.InstanceGroup.InstanceList.TheHealthCheckConfigurationIs')}

    <br />
    {intl('saenext.basic-info.InstanceGroup.InstanceList.ExcessiveServicePressureCheckThe')}

    <br />
    {intl('saenext.basic-info.InstanceGroup.InstanceList.TheApplicationItselfFailedTo')}

    <br />
    <a href={CachedData.confLink('help:sae:a-configured-health-check-fails')} target="_blank">
      {intl('saenext.basic-info.InstanceGroup.InstanceList.TroubleshootingGuide')}
    </a>
  </>
);