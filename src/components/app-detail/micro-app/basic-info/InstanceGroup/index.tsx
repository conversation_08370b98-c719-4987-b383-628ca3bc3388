import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { Collapse, Message, Loading } from '@ali/cnd';
import services from '~/services';
import InstanceList from './InstanceList';
import SubTitle from '~/components/shared/SubTitle';
import MicroAppContext from '~/utils/microAppContext';
import { isEqual } from 'lodash';

const GROUP_NAME = {
  _DEFAULT_GROUP: intl('saenext.basic-info.InstanceGroup.DefaultGroup'),
  _GRAY_GROUP: intl('saenext.basic-info.InstanceGroup.GrayscaleGrouping'),
  _BATCH_GROUP: intl('saenext.basic-info.InstanceGroup.BatchGrouping'),
};

const InstanceGroup = (props) => {
  const { appId, appConfig, appStatus, vSwitchList } = props;

  const [isLoading, setIsLoading] = useState(false);
  const [instanceGroup, setInstanceGroup] = useState([]);

  const { setInstanceGroup: setInstanceGroupContext } = useContext(MicroAppContext);

  const intervalRef = useRef(null);

  useEffect(() => {
    initFetch();

    return () => {
      clearInterval(intervalRef.current);
    };
  }, []);

  useEffect(() => {
    clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      getInstanceGroup();
    }, 10000);
  }, [instanceGroup]);

  const initFetch = async () => {
    setIsLoading(true);
    await getInstanceGroup();
    setIsLoading(false);
  };

  const getInstanceGroup = async () => {
    const { Data = [] } =
      (await services.DescribeApplicationGroups(
        {
          AppId: appId,
        },
        true,
      )) || {};
    if (Data.length === 0) return;
    Data.sort((a, b) => b.GroupType - a.GroupType);
    if (isEqual(instanceGroup, Data)) return;
    setInstanceGroup(Data);
    setInstanceGroupContext(Data);
  };

  return (
    <Loading visible={isLoading} className="full-width" style={{ minHeight: 280 }}>
      <Collapse className="mt" defaultExpandedKeys={['0', '1']}>
        {instanceGroup.map((item, index) => {
          const { RunningInstances = 0 } = item;
          return (
            <Collapse.Panel
              title={
                <SubTitle
                  title={GROUP_NAME[item.GroupName]}
                  subTitle={
                    intl(
                      'saenext.basic-info.InstanceGroup.StatusRunninginstancesInstancesAreRunning',
                      { RunningInstances: RunningInstances },
                    ) as string
                  }
                />
              }
            >
              <InstanceList
                appId={appId}
                appConfig={appConfig}
                appStatus={appStatus}
                vSwitchList={vSwitchList}
                groupInfo={item}
              />
            </Collapse.Panel>
          );
        })}
      </Collapse>
    </Loading>
  );
};

export default InstanceGroup;
