import React, { useContext, useState } from 'react';
import {
  Actions,
  Balloon,
  Button,
  ConsoleContext,
  Copy,
  Dialog,
  Icon,
  intl,
  Link,
  LinkButton,
  Message,
  StatusIndicator,
  Table,
  ToolTipCondition,
  Truncate,
  CndTable,
  Tag,
} from '@ali/cnd';
import _, { isEmpty, get, noop } from 'lodash';
import moment from 'moment';
import If from '~/components/shared/If';
import services from '~/services';
import SlideModalWrap from '~/components/shared/SlideModalWrap';
import RealtimeLog from '~/components/app-detail/micro-app/log-control/log-control/RealtimeLog';
import EventList from '~/components/shared/EventList';
import WebshellContent from '../InstanceDetail/WebshellContent';
import CachedData from '~/cache/common';
import InstanceStatus from './InstanceStatus';
import InstanceDebugDialog from './InstanceDebugDialog';
import { confFeature } from '@alicloud/console-one-conf';

const { Tooltip } = Balloon;

const InstanceList = (props) => {
  const { appId, appConfig, appStatus, vSwitchList, groupInfo, jobId, pageType = 'app' } = props;

  const { AppName, NamespaceId, PackageType, AssociateEip, SidecarContainersConfig } = appConfig;

  const { LastChangeOrderRunning } = appStatus;

  const { GroupId = '' } = groupInfo;

  const isInstanceWhiteList = get(
    window,
    'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS.isInstanceWhiteList',
  );

  const [refreshIndex, setRefreshIndex] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const refresh = () => {
    setRefreshIndex((refreshIndex) => refreshIndex + 1);
  };

  const fetchData = async (params) => {
    let _params = {
      AppId: appId,
      GroupId,
      CurrentPage: params.current,
      PageSize: params.pageSize,
      Reverse: params.order === 'default' ? '' : params.order === 'desc',
      InstanceId: params?.instanceId || '',
    };
    if (pageType && pageType === 'job') {
      Reflect.set(_params, 'JobId', jobId);
    }

    const { Data: { Instances = [], TotalSize = 0 } = {} } =
      (await services.DescribeApplicationInstances(_params, true)) || {};

    return {
      data: Instances,
      total: TotalSize,
    };
  };

  const handleRestartInstance = async (InstanceIds) => {
    Dialog.confirm({
      title: intl('saenext.basic-info.InstanceGroup.InstanceList.RestartTheInstance'),
      content: intl('saenext.basic-info.InstanceGroup.InstanceList.AreYouSureYouWant'),
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res =
            (await services.restartMicroInstances({
              AppId: appId,
              InstanceIds,
            })) || {};
          if (res?.Success) {
            resolve(true);
            Message.success(intl('saenext.basic-info.InstanceGroup.InstanceList.RestartSucceeded'));
          }
        });
      },
    });
  };

  const handleDeleteInstance = async (InstanceIds) => {
    Dialog.confirm({
      title: intl('saenext.basic-info.InstanceGroup.InstanceList.DeleteAnInstance'),
      content: intl('saenext.basic-info.InstanceGroup.InstanceList.AfterDeletingTheInstanceThe'),
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res =
            (await services.deleteMicroInstances({
              AppId: appId,
              InstanceIds,
            })) || {};
          if (res?.Success) {
            resolve(true);
            Message.success(
              intl('saenext.basic-info.InstanceGroup.InstanceList.DeletedSuccessfully'),
            );
          }
        });
      },
    });
  };

  const operation = (
    <Message type="warning">
      {intl('saenext.basic-info.InstanceGroup.NoteThePrivateIpAddress')}
      <a href={CachedData.confLink('help:sae:configure-a-nat-gateway')}>
        {intl('saenext.basic-info.InstanceGroup.ReferToDetails')}
      </a>
      {intl('saenext.common.full-stop')}
    </Message>
  );

  const selection = () => {
    return (
      <>
        <Button
          type="primary"
          disabled={selectedRowKeys.length === 0}
          onClick={() => handleRestartInstance(selectedRowKeys.join(','))}
        >
          {intl('saenext.basic-info.InstanceGroup.InstanceList.RestartInstancesInBatches')}
        </Button>
        <span className="ml">
          {intl('saenext.basic-info.InstanceGroup.InstanceList.CurrentlyYouHaveSelectedA', {
            selectedRowKeysLength: selectedRowKeys.length,
          })}
        </span>
      </>
    );
  };

  const search = {
    defaultDataIndex: 'instanceId',
    defaultSelectedDataIndex: 'instanceId',
    options: [
      {
        label: intl('saenext.basic-info.InstanceGroup.InstanceList.InstanceId'),
        dataIndex: 'instanceId',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.basic-info.InstanceGroup.InstanceList.SearchByInstanceId'),
        },
      },
    ],
  };

  return (
    <>
      {pageType !== 'job' && (
        <Message type="warning" className="mb-s">
          {intl('saenext.basic-info.InstanceGroup.NoteThePrivateIpAddress')}
          <a href={CachedData.confLink('help:sae:configure-a-nat-gateway')}>
            {intl('saenext.basic-info.InstanceGroup.ReferToDetails')}
          </a>
          {intl('saenext.common.full-stop')}
        </Message>
      )}
      {pageType === 'job' && (
        <Message type="warning" className="mb-s">
          {intl('saenext.basic-info.InstanceGroup.InstanceList.NoteForCompletedTasksInstance')}

          {/* 详见
          <a href="" target="_blank">
          配置说明
          </a> */}
        </Message>
      )}
      <CndTable
        fetchData={fetchData}
        primaryKey="InstanceId"
        refreshIndex={refreshIndex}
        showRefreshButton
        recordCurrent
        pagination={{ pageSizeList: [10, 20, 50, 100] }}
        loop={{ enable: true, time: 10000, showLoading: false }}
        search={search}
        selection={pageType === 'job' ? null : selection}
        rowSelection={
          pageType === 'job'
            ? null
            : {
                getProps: (record, index) => {
                  if (record.InstanceContainerStatus !== 'Running') {
                    return { disabled: true };
                  }
                },
                selectedRowKeys,
                onChange(selected, records) {
                  setSelectedRowKeys(selected);
                },
              }
        }
      >
        <CndTable.Column
          width={280}
          title={intl('saenext.basic-info.InstanceGroup.InstanceList.InstanceId')}
          lock
          dataIndex="InstanceId"
          cell={(val, index, record) => {
            return (
              <>
                <Link
                  to={
                    pageType === 'job'
                      ? undefined
                      : `/${regionId}/app-list/${appId}/micro-app/instance?groupId=${GroupId}&instanceId=${val}`
                  }
                >
                  <Truncate
                    style={{ display: 'inline-block' }}
                    position="middle"
                    threshold={150}
                    type="width"
                  >
                    {val}
                  </Truncate>
                  <If condition={!isEmpty(record.SidecarContainersStatus)}>
                    <Tooltip
                      trigger={
                        <Tag color="blue" className="ml-s">
                          sidecar
                        </Tag>
                      }
                    >
                      {intl(
                        'saenext.basic-info.InstanceGroup.InstanceList.ClickInstanceDetailsToView',
                      )}
                    </Tooltip>
                  </If>
                </Link>

                <Copy text={val} showIcon></Copy>
              </>
            );
          }}
        />

        <CndTable.Column
          width={250}
          title="vSwitch"
          dataIndex="VSwitchId"
          cell={(val) => {
            const renderVSwitch = (val) => {
              const targetVSwitch = vSwitchList.find((v) => v.VSwitchId === val);

              let label = val;
              if (targetVSwitch) {
                const zoneName = _.toUpper(_.last(targetVSwitch.ZoneId as string));
                label = intl(
                  'saenext.basic-info.InstanceGroup.InstanceList.ZoneZonenameTargetvswitchvswitchname',
                  { zoneName: zoneName, targetVSwitchVSwitchName: targetVSwitch.VSwitchName },
                );
              }

              return (
                <Copy text={val}>
                  <a
                    href={`${CachedData.confLink(
                      'feature:vpc:url',
                    )}/vpc/${regionId}/switches/${val}`}
                    target="_blank"
                  >
                    {label}
                  </a>
                </Copy>
              );
            };

            const vSwitchs = _.split(val, ',');
            return (
              <>
                {_.map(vSwitchs, (v) => {
                  return (
                    <div key={v}>
                      {renderVSwitch(v)}
                      <br />
                    </div>
                  );
                })}
              </>
            );
          }}
        />

        <CndTable.Column
          width={180}
          title={intl('saenext.basic-info.InstanceGroup.InstanceList.IpAddress')}
          dataIndex="InstanceContainerIp"
          // cell={(value, index, record) => this.renderIp(value, index, record, deployIdx)}
          cell={(value, index, record) => {
            const { InstanceContainerIp, Eip } = record;
            return (
              <>
                <If condition={Eip}>
                  <p>
                    {intl('saenext.basic-info.InstanceGroup.InstanceList.EipBomb', { Eip: Eip })}
                  </p>
                </If>
                <p>
                  {intl(
                    'saenext.basic-info.InstanceGroup.InstanceList.InstancecontaineripPrivate',
                    { InstanceContainerIp: InstanceContainerIp },
                  )}
                </p>
              </>
            );
          }}
        />

        <CndTable.Column
          width={180}
          title={intl('saenext.basic-info.InstanceGroup.InstanceList.RunningStatus')}
          sortable
          sortDirections={['desc', 'asc', 'default']}
          dataIndex="InstanceContainerStatus"
          cell={(val, index, record) => {
            return (
              <InstanceStatus
                appId={appId}
                associateEip={AssociateEip}
                groupId={GroupId}
                refresh={refresh}
                record={record}
                showRestart={
                  pageType === 'job'
                    ? record.instanceContainerStatus !== 'Running' &&
                      record.instanceContainerRestarts
                    : confFeature('instance_restarts')
                }
              />
            );
          }}
        />

        {PackageType === 'Image' || _.includes(PackageType, 'IMAGE_PHP') ? (
          <CndTable.Column
            width={250}
            title={intl('saenext.basic-info.InstanceGroup.InstanceList.Image')}
            dataIndex="ImageUrl"
            cell={(val = '') => {
              const reg = /\/.+\/.+$/;
              const url = val.match(reg);
              return <Tooltip trigger={`...${url || val}`}>{val}</Tooltip>;
            }}
          />
        ) : (
          <CndTable.Column
            width={200}
            title={intl('saenext.basic-info.InstanceGroup.InstanceList.Version')}
            dataIndex="PackageVersion"
          />
        )}

        <CndTable.Column
          width={100}
          title={intl('saenext.basic-info.InstanceGroup.InstanceList.Runtime')}
          dataIndex="CreateTimeStamp"
          cell={(val, index, record) => {
            if (!val) {
              return '--';
            } else {
              if (pageType === 'job') {
                const { FinishTimeStamp } = record;
                let exeTime = val;
                if (FinishTimeStamp) {
                  exeTime = Date.now() - FinishTimeStamp + val;
                }
                return moment(exeTime).fromNow(true);
              } else {
                return moment(val).fromNow(true);
              }
            }
          }}
        />

        <CndTable.Column
          width={220}
          lock="right"
          title={intl('saenext.basic-info.InstanceGroup.InstanceList.Operation')}
          cell={(value, index, record) => {
            const { InstanceContainerStatus, DebugStatus, InstanceId } = record;
            return (
              <Actions threshold={3}>
                {!isEmpty(record.SidecarContainersStatus) && (
                  <Link
                    to={`/${regionId}/app-list/${appId}/micro-app/instance?groupId=${GroupId}&instanceId=${InstanceId}`}
                  >
                    {intl('saenext.basic-info.InstanceGroup.InstanceList.Details')}
                  </Link>
                )}

                <SlideModalWrap
                  title={intl('saenext.basic-info.InstanceGroup.InstanceList.RealTimeLog')}
                  width={1200}
                  onMaskClick={noop}
                  onClose={noop}
                  content={
                    <RealtimeLog
                      regionId={regionId}
                      appConfig={appConfig}
                      applicationId={appId}
                      podId={InstanceId}
                    />
                  }
                >
                  <LinkButton>
                    {intl('saenext.basic-info.InstanceGroup.InstanceList.RealTimeLog')}
                  </LinkButton>
                </SlideModalWrap>
                <SlideModalWrap
                  title={'Webshell'}
                  width={1200}
                  onMaskClick={noop}
                  onClose={noop}
                  content={
                    <WebshellContent
                      regionId={regionId}
                      appId={appId}
                      groupId={GroupId}
                      instanceId={InstanceId}
                      sidecarId={''}
                    />
                  }
                >
                  <LinkButton
                    disabled={
                      isEmpty(record.SidecarContainersStatus) &&
                      !(
                        InstanceContainerStatus === 'Running' ||
                        InstanceContainerStatus === 'Terminating'
                      )
                    }
                  >
                    Webshell
                  </LinkButton>
                </SlideModalWrap>
                <SlideModalWrap
                  title={intl('saenext.basic-info.InstanceGroup.InstanceList.Event')}
                  width={1200}
                  onMaskClick={noop}
                  onClose={noop}
                  content={
                    <EventList
                      regionId={regionId}
                      namespaceId={NamespaceId}
                      appId={appId}
                      filters={{
                        ObjectKind: 'Pod',
                        ObjectName: InstanceId,
                      }}
                    />
                  }
                >
                  <LinkButton>
                    {intl('saenext.basic-info.InstanceGroup.InstanceList.Event')}
                  </LinkButton>
                </SlideModalWrap>
                {/* <LinkButton
                      Component={Link}
                      to={`/${regionId}/app-list/${appId}/micro-app/realtime-log?name=${AppName}&podId=${InstanceId}`}
                     >
                      {intl('saenext.basic-info.InstanceGroup.InstanceList.RealTimeLog')}
                     </LinkButton> */}
                {/* <LinkButton
                      Component={Link}
                      to={`/${regionId}/app-list/${appId}/micro-app/event?name=${AppName}&ObjectKind=Pod&ObjectName=${record.InstanceId}`}
                     >
                      {intl('saenext.basic-info.InstanceGroup.InstanceList.Event')}
                     </LinkButton> */}
                {/* <WebShellDialog regionId={regionId} appId={appId} instanceId={InstanceId}>
                     <LinkButton
                       disabled={
                         !(
                           InstanceContainerStatus === 'Running' ||
                           InstanceContainerStatus === 'Terminating'
                         )
                       }
                     >
                       Webshell
                     </LinkButton>
                    </WebShellDialog> */}
                {pageType !== 'job' && (
                  <>
                    <ToolTipCondition
                      show={
                        !isInstanceWhiteList && InstanceContainerStatus !== 'Running'
                      }
                      tip={
                        InstanceContainerStatus !== 'Running'
                          ? intl('saenext.basic-info.InstanceGroup.InstanceList.TheInstanceIsInA')
                          : intl(
                              'saenext.basic-info.InstanceGroup.InstanceList.TheApplicationIsChangingPlease',
                            )
                      }
                      align="t"
                    >
                      <LinkButton
                        type="primary"
                        text
                        disabled={
                          !isInstanceWhiteList && InstanceContainerStatus !== 'Running'
                        }
                        onClick={() => handleRestartInstance(record.InstanceId)}
                      >
                        {intl('saenext.basic-info.InstanceGroup.InstanceList.Restart')}
                      </LinkButton>
                    </ToolTipCondition>
                    <ToolTipCondition
                      show={!isInstanceWhiteList && DebugStatus}
                      tip={
                        DebugStatus
                          ? intl(
                              'saenext.basic-info.InstanceGroup.InstanceList.OneClickDebuggingIsEnabled',
                            )
                          : intl(
                              'saenext.basic-info.InstanceGroup.InstanceList.TheApplicationIsChangingPlease',
                            )
                      }
                      align="t"
                    >
                      <InstanceDebugDialog
                        appId={appId}
                        groupId={GroupId}
                        instanceId={InstanceId}
                        refresh={refresh}
                      >
                        <LinkButton
                          type="primary"
                          text
                          disabled={!isInstanceWhiteList && DebugStatus }
                        >
                          {intl('saenext.basic-info.InstanceGroup.InstanceList.OneClickDebugging')}
                        </LinkButton>
                      </InstanceDebugDialog>
                    </ToolTipCondition>
                    <LinkButton
                        type="primary"
                        text
                        onClick={() => handleDeleteInstance(record.InstanceId)}
                      >
                        {intl('saenext.basic-info.InstanceGroup.InstanceList.Delete')}
                    </LinkButton>
                  </>
                )}
              </Actions>
            );
          }}
        />
      </CndTable>
    </>
  );
};

export default InstanceList;
