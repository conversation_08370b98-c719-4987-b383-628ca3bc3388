import { intl } from '@ali/cnd';
import React from 'react';
import ModalWrap from '~/components/shared/ModalWrap';
import InstanceDebugContent from './InstanceDebugContent';

const InstanceDebugDialog = (props) => {
  const { children, appId, groupId, instanceId, refresh } = props;

  return (
    <ModalWrap
      type="dialog"
      title={intl('saenext.basic-info.InstanceGroup.InstanceDebugDialog.OneClickDebugging')}
      trigger={children}
      width={'max-content'}
      footer={false}
    >
      <InstanceDebugContent
        appId={appId}
        groupId={groupId}
        instanceId={instanceId}
        refresh={refresh}
      />
    </ModalWrap>
  );
};

export default InstanceDebugDialog;
