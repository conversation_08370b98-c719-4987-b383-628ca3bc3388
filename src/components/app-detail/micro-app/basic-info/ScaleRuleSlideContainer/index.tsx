import React, { useContext, useEffect, useState } from 'react';
import FeatureContext from '~/utils/featureContext';
import ScaleRuleSlide from '../ScaleRuleSlide';
import ScalingRuleSlide from '../ScalingRuleSlide';
import CachedData from '~/cache/common';
import { getCookieByKeyName } from '~/utils/global';

const ScaleRuleSlideContainer = (props) => {
  const { feature } = useContext(FeatureContext);
  const { newScalingRulePage } = feature;

  const mainUid = CachedData.getMainUserId();

  const defaultShowNew = newScalingRulePage && getCookieByKeyName(`${mainUid}-scale-rule-showNew`) !== 'false';

  const [showNew, setShowNew] = useState(defaultShowNew);

  useEffect(() => {
    setShowNew(defaultShowNew);
  }, [defaultShowNew])

  if (showNew) {
    return <ScaleRuleSlide {...props} />;
  } else {
    return <ScalingRuleSlide {...props} />;
  }
}

export default ScaleRuleSlideContainer;