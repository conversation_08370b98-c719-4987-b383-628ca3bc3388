import React, { HTMLAttributes, useContext } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Copy,
  DateTime,
  Description,
  Icon,
  intl,
  LinkButton,
  ToolTipCondition,
  Instance,
  Message,
} from '@ali/cnd';
import services from '~/services';
import _ from 'lodash';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';
import SecurityGroupDialog from './SecurityGroupDialog';
import VSwitchDialog from './VSwitchDialog';
import DescriptionDialog from './DescriptionDialog';
import { TagList, withTagIntlProvider } from '@ali/xconsole-rc-tags';
import SpecDialog from './SpecDialog';
import MicroAppContext from '~/utils/microAppContext';
import { useCheckVSwitchZone } from '../utils/useCheckVSwitchZone';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';
import { AppVersionMap } from '~/constants/application';

const TagListWithIntl = withTagIntlProvider(TagList);

interface IAppBaseInfo extends HTMLAttributes<HTMLDivElement> {
  appConfig: object;
  appStatus: object;
  vpc: object;
  vSwitchList: any[];
  securityGroup: object;
  refresh?: Function;
  authedVPC?: boolean;
  isSupportHygon?: boolean;
  checkAppInstanceVersion: Function;
  inDebtData: object;
  allowUpdateAppAliasName: boolean;
  isSupportMultiVersions: boolean;
}

const AppBaseInfo = (props: IAppBaseInfo) => {
  const {
    className,
    appConfig = {} as any,
    appStatus = {} as any,
    vpc = {} as any,
    vSwitchList = [],
    securityGroup = {} as any,
    refresh,
    authedVPC = true,
    isSupportHygon = false,
    checkAppInstanceVersion,
    inDebtData = {} as any,
    allowUpdateAppAliasName,
    isSupportMultiVersions = false,
  } = props;

  const { RegionId: regionId, Replicas = 0 } = appConfig;

  const { LastChangeOrderRunning, CurrentStatus } = appStatus;

  const isAppStart = CurrentStatus === 'RUNNING';
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');

  const { instanceGroup } = useContext(MicroAppContext);

  const multiVersionText =
    instanceGroup?.length > 1 &&
    intl('saenext.micro-app.basic-info.AppBaseInfo.MultipleVersionsCoexistOnline');

  const CardProps = {
    showTitleBullet: false,
    showHeadDivider: false,
    contentHeight: 'auto',
  };

  const renderDisabledTip = (conditions: Array<string>) => {
    let tip = '';
    if (!_.isEmpty(conditions)) {
      for (let i = 0; i < conditions.length; i++) {
        const condition = conditions[i];

        if (condition === 'isInDebt' && isInDebt) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.YourAccountIsOverduePlease');
          break;
        }
        if (condition === 'LastChangeOrderRunning' && LastChangeOrderRunning) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.TheApplicationIsChangingPlease');
          break;
        }
        if (condition === 'isAppStart' && !isAppStart) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.TheCurrentApplicationStatusIs');
          break;
        }
      }
    }
    return tip;
  };
  const handleUpdateAppAliasName = async (val) => {
    if (val === appConfig?.AppName) {
      return;
    }
    Message.loading(intl('saenext.micro-app.basic-info.AppBaseInfo.TheApplicationNameIsBeing'));
    const res = await services.UpdateAppAliasName({
      AppId: appConfig?.AppId,
      AppAliasName: val,
    });
    if (res?.Success) {
      Message.success(intl('saenext.micro-app.basic-info.AppBaseInfo.TheApplicationNameHasBeen'));
      refresh();
    }
  };
  return (
    <Card
      title={intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationInformation')}
      {...CardProps}
      className={className}
    >
      <Description
        dataSource={{ ...appConfig, ...appStatus }}
        items={[
          {
            dataIndex: 'AppName',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationName'),
            render: (value) => {
              return (
                <Instance.Text
                  value={value}
                  showCopy
                  title={intl('saenext.micro-app.basic-info.AppBaseInfo.ModifyTheApplicationName')}
                  rules={[
                    {
                      required: true,
                      message: intl(
                        'saenext.app-create.web-app.BasicCreator.TheApplicationNameCannotBe',
                      ),
                    },
                    {
                      pattern: new RegExp(
                        `^[a-zA-Z]([0-9a-zA-Z\-]{0,${inDebtData?.MaxAppNameLength || 36}})$`,
                      ),
                      message: intl('saenext.app-create.web-app.BasicCreator.MicroNameHelp', {
                        maxLength: inDebtData?.MaxAppNameLength || 36,
                      }),
                    },
                    {
                      validator: async (rule, val, callback) => {
                        if (value !== val) {
                          const checkResult = await services.CheckAppNameV2({
                            NamespaceId: appConfig?.NamespaceId || regionId,
                            AppName: val,
                          });
                          if (
                            checkResult?.Success &&
                            !checkResult?.Data?.Available &&
                            appConfig?.AppId !== checkResult?.Data?.AppId
                          ) {
                            callback(
                              intl('saenext.micro-app.ModifyTheApplicationName.conflict.tip', {
                                appName:
                                  checkResult?.Data?.AppAliasName || checkResult?.Data?.AppName,
                              }),
                            );
                          } else {
                            callback();
                          }
                        } else {
                          callback();
                        }
                      },
                      trigger: ['onBlur', 'onChange'],
                    },
                  ]}
                  onChange={allowUpdateAppAliasName ? handleUpdateAppAliasName : null}
                />
              );
            },
          },
          {
            dataIndex: 'NewSaeVersion',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationVersion'),
            visible: isSupportMultiVersions,
            render: (value) => {
              const item = value ? AppVersionMap[value] : AppVersionMap['std'];
              return (
                <div className="flex items-center">
                  <img src={item.icon} width={14} />
                  <span style={{ marginLeft: 4 }}>{item.label}</span>
                </div>
              );
            },
          },
          {
            dataIndex: 'AppId',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationId'),
            render: (value) => value && <Copy text={value}>{value}</Copy>,
          },
          {
            dataIndex: 'IsStateful',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationType'),
            visible: !!appConfig.IsStateful,
            render: (value) => (
              <>
                {value
                  ? intl('saenext.micro-app.basic-info.AppBaseInfo.StatefulApplications')
                  : intl('saenext.micro-app.basic-info.AppBaseInfo.StatelessApplication')}
              </>
            ),
          },
          {
            dataIndex: 'NamespaceId',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.Namespace'),
            render: (value) =>
              value && (
                <Copy text={value}>
                  <a href={`/${regionId}/namespace/${value}/base`} target="_blank">
                    {value}
                  </a>
                </Copy>
              ),
          },
          {
            dataIndex: 'VpcId',
            label: 'VPC',
            render: (value) =>
              value && (
                <>
                  <If condition={vpc?.VpcName}>
                    <p>
                      <span className="text-description">
                        {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
                      </span>
                      {vpc?.VpcName}
                    </p>
                  </If>
                  <p>
                    <span className="text-description">ID: </span>
                    <Copy text={value}>
                      <a
                        href={CachedData.confLink('feature:vpc:vpcs:url', {
                          regionId: regionId,
                          vpcId: value,
                        })}
                        target="_blank"
                      >
                        {value}
                      </a>
                    </Copy>
                  </p>
                </>
              ),
          },
          {
            dataIndex: 'VSwitchId',
            label: (
              <UnAuthedLabel
                text={
                  <>
                    <span>{intl('saenext.micro-app.basic-info.AppBaseInfo.Switch')}</span>
                    {appConfig?.VSwitchId && _.split(appConfig?.VSwitchId, ',').length === 1 && (
                      <Balloon.Tooltip
                        trigger={<Icon type="warning" size="xs" style={{ marginLeft: 4 }} />}
                        align="t"
                        popupStyle={{ maxWidth: 300 }}
                      >
                        <div style={{ color: '#FFA003' }}>
                          {intl(
                            'saenext.micro-app.basic-info.AppBaseInfo.CurrentlyOnlyOneSwitchVswitch',
                          )}
                        </div>
                      </Balloon.Tooltip>
                    )}
                  </>
                }
                authed={authedVPC}
                authKey="AliyunVPCReadOnlyAccess"
              />
            ),

            render: (value) => {
              if (!value) {
                return;
              }

              const vSwitchs = _.split(value, ',');

              let ipCountSum = 0;
              const zoneList = [];
              const curVSwitchList = _.map(vSwitchs, (vSwitch) => {
                const targetVSwitch = vSwitchList.find((v) => v.VSwitchId === vSwitch);
                if (targetVSwitch) {
                  const zoneName = _.toUpper(_.last(targetVSwitch.ZoneId as string));
                  const label = intl(
                    'saenext.micro-app.basic-info.AppBaseInfo.ZoneZonenameTargetvswitchvswitchname',
                    { zoneName: zoneName, targetVSwitchVSwitchName: targetVSwitch.VSwitchName },
                  );

                  const { AvailableIpAddressCount } = targetVSwitch;
                  if (AvailableIpAddressCount) {
                    ipCountSum += AvailableIpAddressCount;
                  }

                  zoneList.push(targetVSwitch.ZoneId);

                  return {
                    label,
                    value: vSwitch,
                    zoneId: targetVSwitch.ZoneId,
                  };
                }

                return {
                  label: '',
                  value: vSwitch,
                  noExist: vSwitchList.length > 0 && true,
                };
              });

              const { inRecommend, recommendZones } = useCheckVSwitchZone({ zoneList, regionId });

              const zoneWarning = !inRecommend && recommendZones.length > 0;

              return (
                <>
                  {_.map(curVSwitchList, (v) => {
                    const { label, value, noExist, zoneId } = v;
                    return (
                      <div key={value} style={{ marginBottom: 12 }}>
                        <p>
                          <If condition={!!label}>
                            <div>
                              <span className="text-description">
                                {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
                              </span>
                              {label}
                            </div>
                          </If>
                          <div>
                            <span className="text-description">ID: </span>
                            <Copy text={value}>
                              <a
                                href={CachedData.confLink('feature:vpc:switches:url', {
                                  regionId: regionId,
                                  switchId: value,
                                })}
                                target="_blank"
                              >
                                {value}
                              </a>
                            </Copy>
                            <If condition={noExist}>
                              <Balloon.Tooltip
                                trigger={
                                  <Icon type="warning" size="xs" className="text-warning ml-s" />
                                }
                              >
                                {intl(
                                  'saenext.micro-app.basic-info.AppBaseInfo.ThisSwitchHasBeenDeleted',
                                )}
                              </Balloon.Tooltip>
                            </If>
                          </div>
                          {zoneId && zoneId === 'cn-shenzhen-a' && (
                            <div className="text-warning">
                              {intl(
                                'saenext.micro-app.basic-info.AppBaseInfo.ShenzhenZoneAIsAbout',
                              )}
                            </div>
                          )}
                        </p>
                      </div>
                    );
                  })}

                  <div
                    className={
                      ipCountSum > Replicas || vSwitchList.length === 0
                        ? 'text-description'
                        : 'text-warning'
                    }
                  >
                    {intl(
                      'saenext.micro-app.basic-info.AppBaseInfo.AvailableIpAddressesIpcountsum',
                      { ipCountSum: ipCountSum },
                    )}
                    <If
                      condition={ipCountSum <= Replicas && Replicas > 0 && vSwitchList.length > 0}
                    >
                      <Balloon.Tooltip
                        trigger={<Icon type="warning" size="xs" className="text-warning ml" />}
                      >
                        <If condition={ipCountSum === 0}>
                          {intl('saenext.micro-app.basic-info.AppBaseInfo.TheNumberOfAvailableIp')}
                        </If>
                        <If condition={ipCountSum !== 0}>
                          {intl('saenext.micro-app.basic-info.AppBaseInfo.TheNumberOfIpAddresses')}
                        </If>
                      </Balloon.Tooltip>
                    </If>
                  </div>

                  <ToolTipCondition
                    show={LastChangeOrderRunning || !isAppStart || isInDebt}
                    tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart', 'isInDebt'])}
                    align="r"
                  >
                    <VSwitchDialog appConfig={appConfig} refresh={refresh}>
                      <LinkButton
                        disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                        className="ml10"
                      >
                        {zoneWarning
                          ? intl('saenext.micro-app.basic-info.AppBaseInfo.WeRecommendThatYouUse')
                          : intl('saenext.micro-app.basic-info.AppBaseInfo.MultiVswitchDeployment')}
                      </LinkButton>
                    </VSwitchDialog>
                  </ToolTipCondition>
                </>
              );
            },
          },
          {
            dataIndex: 'SecurityGroupId',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.SecurityGroup'),
            render: (value) =>
              value && (
                <>
                  <If condition={securityGroup.SecurityGroupName}>
                    <p>
                      <span className="text-description">
                        {intl('saenext.web-app.basic-info.VpcNetworkSetting.Name')}
                      </span>
                      {securityGroup.SecurityGroupName}
                    </p>
                  </If>
                  <p>
                    <span className="text-description">ID: </span>
                    <Copy text={value}>
                      <a
                        href={`${CachedData.confLink(
                          'feature:ecs:url',
                        )}/securityGroupDetail/region/${regionId}/groupId/${value}/detail/intranetIngress`}
                        target="_blank"
                      >
                        {value}
                      </a>
                    </Copy>
                  </p>
                  <ToolTipCondition
                    show={LastChangeOrderRunning || !isAppStart || isInDebt}
                    tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart', 'isInDebt'])}
                  >
                    <SecurityGroupDialog appConfig={appConfig} refresh={refresh}>
                      <LinkButton
                        disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                        className="ml10"
                      >
                        {intl('saenext.micro-app.basic-info.AppBaseInfo.SwitchSecurityGroups')}
                      </LinkButton>
                    </SecurityGroupDialog>
                  </ToolTipCondition>
                </>
              ),
          },
          {
            dataIndex: 'Cpu',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.InstanceType'),
            render: (value) =>
              value && (
                <>
                  <span>
                    {value / 1000} Core, {appConfig?.Memory / 1024} GiB,
                    <span className="ml-xs">
                      {intl('saenext.micro-app.basic-info.AppBaseInfo.GibSystemDiskSpaceGib.new', {
                        diskSize: appConfig?.DiskSize || 20,
                      })}
                    </span>
                  </span>
                  <ToolTipCondition
                    show={LastChangeOrderRunning || !isAppStart || isInDebt}
                    tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart', 'isInDebt'])}
                    align="r"
                  >
                    <SpecDialog
                      appConfig={appConfig}
                      refresh={refresh}
                      isSupportHygon={isSupportHygon}
                      checkAppInstanceVersion={checkAppInstanceVersion}
                      title={intl('saenext.micro-app.basic-info.AppBaseInfo.ChangeSpecifications')}
                    >
                      <LinkButton
                        disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                        className="ml-s"
                      >
                        {intl('saenext.micro-app.basic-info.AppBaseInfo.ChangeSpecifications')}
                      </LinkButton>
                    </SpecDialog>
                  </ToolTipCondition>
                </>
              ),
          },
          {
            dataIndex: 'RunningInstances',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.NumberOfRunningInstances'),
            render: (value) =>
              value && (
                <>
                  {intl('saenext.micro-app.basic-info.AppBaseInfo.CurrentValueInstances', {
                    value: value,
                  })}
                  {intl(
                    'saenext.micro-app.basic-info.AppBaseInfo.TargetAppconfigreplicasInstances',
                    {
                      appConfigReplicas: appConfig.Replicas,
                    },
                  )}
                </>
              ),
          },
          {
            dataIndex: 'ResourceType',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ResourceType'),
            visible: isSupportHygon && _.has(appConfig, 'ResourceType'),
            render: (value: string) => {
              const typeText =
                value === 'haiguang'
                  ? intl('saenext.micro-app.basic-info.AppBaseInfo.Hygon')
                  : intl('saenext.micro-app.basic-info.AppBaseInfo.Default');
              return <>{typeText}</>;
            },
          },
          {
            dataIndex: 'CreateTime',
            label: intl('instance.prop.create_time.label'),
            render: (value) => value && <DateTime value={Number(value)} />,
          },
          {
            dataIndex: 'ImageUrl',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ImageAddress'),
            visible: !!appConfig?.ImageUrl,
            render: (value) =>
              value && (
                <Copy text={value}>
                  {value} {multiVersionText}
                </Copy>
              ),
          },
          {
            dataIndex: 'PackageUrl',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.PackageName'),
            visible: !!appConfig?.PackageUrl,
            render: (value) => {
              if (!value) {
                return;
              }
              const url = value?.replace(/.*\//, '')?.replace(/\?.*/, '') || '';
              const { PackageVersion = '' } = appConfig;
              const result = `${url}:${PackageVersion}`;
              return (
                <Copy text={result}>
                  {result} {multiVersionText}
                </Copy>
              );
            },
          },
          {
            dataIndex: 'Tags',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationTag'),
            render: (value) => (
              <TagListWithIntl
                regionId={regionId}
                resourceId={appConfig.AppId}
                resourceType={'ALIYUN::SAE::APPLICATION'}
                dataSource={_.map(value, (item) => ({ tagKey: item.Key, tagValue: item.Value }))}
                disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                disabledTip={intl(
                  'saenext.micro-app.basic-info.AppBaseInfo.TheApplicationCannotBeEdited',
                )}
                tagEditorProps={{
                  onFinish: (t, { submitStatus }) => submitStatus === 'success' && refresh(),
                }}
              />
            ),
          },
          {
            dataIndex: 'AppDescription',
            label: intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationDescription'),
            render: (value) => (
              <>
                {value ? (
                  <span style={{ wordBreak: 'break-all' }}>
                    {value}
                    <Copy showIcon text={value} />
                  </span>
                ) : (
                  <span>--</span>
                )}

                <DescriptionDialog appConfig={appConfig} refresh={refresh}>
                  <Button
                    disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                    // type="primary"
                    text
                    iconSize="small"
                    className="ml10"
                  >
                    <Icon type="edit" />
                  </Button>
                </DescriptionDialog>
              </>
            ),
          },
        ]}
      />
    </Card>
  );
};

export default AppBaseInfo;
