import { intl } from '@ali/cnd';
import {
  Actions,
  Dialog,
  LinkButton,
  Message,
  StatusIndicator,
  CndTable
} from '@ali/cnd';
import moment from 'moment';
import React, { useContext } from 'react';
import { SCALE_RULE_TYPE, SCALE_RULE_TYPE_TEXT } from '../ScalingRuleSlide/constant';
import ScalingRuleSlide from '../ScalingRuleSlide';
import { ActionMap } from './constant';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import {
  EMetricType,
  MetricStatusType,
  MetricType,
} from '../ScalingRuleSlide/MetricSettings/MetricsTable/constant';
import { find, get, isArray } from 'lodash';
import MicroAppContext from '~/utils/microAppContext';
import If from '~/components/shared/If';
import IdleMonitorPanel from './IdleMonitorPanel';
import FeatureContext from '~/utils/featureContext';
import { getParams } from '~/utils/global';

const ScalingList = (props) => {
  const { appId, disabled, type, dataSource, refresh } = props;
  const { feature } = useContext(FeatureContext);
  const isAllocateIdle = get(feature, 'idle', false);
  const { appConfig = {}, appStatus = {}, scaleRules = [] } = useContext(MicroAppContext);
  const { RegionId: regionId } = appConfig;
  const { RunningInstances } = appStatus;

  const typeText = SCALE_RULE_TYPE_TEXT[type];

  const renderMetric = (metric = {} as any) => {
    const { Metrics = [] } = metric;
    return Metrics.map((metric) => {
      const { MetricType: metricType, MetricTargetAverageUtilization } = metric;
      const { label, unit } = MetricType[metricType];
      return (
        <div key={metricType}>
          <span className="title-text text-500">{label}: </span>
          <span>{formatRtVal(MetricTargetAverageUtilization, metricType)}</span>
          <span>{unit}</span>
        </div>
      );
    });
  };

  const renderMetricsStatus = (metric = {} as any) => {
    const { Metrics = [], MetricsStatus: { CurrentMetrics = [] } = {} } = metric;
    return Metrics.map((metric) => {
      const { MetricType: metricType } = metric;
      const metricStatusName = MetricStatusType[metricType];
      const currentMetric = find(CurrentMetrics, (item) =>
        isArray(metricStatusName)
          ? metricStatusName.includes(item.Name)
          : metricStatusName === item.Name,
      );

      if (!currentMetric) {
        return <>-</>;
      }

      const { CurrentValue } = currentMetric;

      const { label, unit } = MetricType[metricType];
      return (
        <div key={metricType}>
          <span className="title-text text-500">{label}: </span>
          <span>{formatRtVal(CurrentValue, metricType)}</span>
          <span>{unit}</span>
        </div>
      );
    });
  };

  const renderNextScaleMetrics = (metric = {} as any) => {
    const { Metrics = [], MinReplicas, MaxReplicas } = metric;
    return (
      <>
        <If condition={RunningInstances < MaxReplicas}>
          <div>
            <span>{intl('saenext.basic-info.ScalingGroup.ScalingList.ExpansionSatisfied')}</span>
            {Metrics.map((metric, index) => {
              const { MetricType: metricType, MetricTargetAverageUtilization } = metric;
              const { label, unit } = MetricType[metricType];
              return (
                <span key={metricType}>
                  <If condition={index > 0}>
                    {intl('saenext.basic-info.ScalingGroup.ScalingList.Or')}
                  </If>
                  <span className="title-text text-500">{label}</span>
                  {' > '}
                  <span>
                    {scaleNumber(formatRtVal(MetricTargetAverageUtilization, metricType), 'up')}
                  </span>
                  <span>{unit} </span>
                </span>
              );
            })}
          </div>
        </If>
        <If
          condition={RunningInstances && RunningInstances !== 1 && RunningInstances > MinReplicas}
        >
          <div>
            <span>{intl('saenext.basic-info.ScalingGroup.ScalingList.ShrinkageSatisfied')}</span>
            {Metrics.map((metric, index) => {
              const { MetricType: metricType, MetricTargetAverageUtilization } = metric;
              const { label, unit } = MetricType[metricType];
              return (
                <span key={metricType}>
                  <If condition={index > 0}>
                    {intl('saenext.basic-info.ScalingGroup.ScalingList.Or')}
                  </If>
                  <span className="title-text text-500">{label}</span>
                  {' < '}
                  <span>
                    {scaleNumber(formatRtVal(MetricTargetAverageUtilization, metricType), 'down')}
                  </span>
                  <span>{unit} </span>
                </span>
              );
            })}
          </div>
        </If>
      </>
    );
  };

  const scaleNumber = (num, type) => {
    const scaleMap = {
      up: 1.1,
      down: 0.9,
    };
    if (type === 'down' && RunningInstances && RunningInstances !== 1 && RunningInstances < 10) {
      //实例数小于 10时，只在保证实例 - 1 后，还能维持原目标值num时，才缩容
      return ((num * (RunningInstances - 1)) / RunningInstances).toFixed(0);
    }
    return (num * scaleMap[type]).toFixed(0);
  };

  const formatRtVal = (val, metricType) => {
    if (metricType === EMetricType.RT) {
      return val / 1000;
    } else {
      return val;
    }
  };

  const toggleConfirm = (record, action) => {
    const { name, title, content, service } = ActionMap[action];

    if (action === 'enable' && !preventEnableMultiRule(record)) return;

    Dialog.confirm({
      title,
      content,
      onOk: async () => {
        const { ScaleRuleName, ScaleRuleNames } = record;
        const res = ScaleRuleNames?.length
          ? await Promise.all(
              ScaleRuleNames.map((item) =>
                service({
                  params: {
                    AppId: appId,
                    ScalingRuleName: item,
                  },
                }),
              ),
            )
          : await service({
              params: {
                AppId: appId,
                ScalingRuleName: ScaleRuleName,
              },
            });

        if (res) {
          Message.success(
            intl('saenext.basic-info.ScalingGroup.ScalingList.NameElasticPolicySucceeded', {
              name: name,
            }),
          );
          refresh();
        } else {
          Message.error(
            intl('saenext.basic-info.ScalingGroup.ScalingList.NameElasticPolicyFailed', {
              name: name,
            }),
          );
          throw new Error('network error');
        }
      },
    });
  };

  const preventEnableMultiRule = (record) => {
    const { ScaleRuleType: curType } = record;
    const otherTypeEnableRule = scaleRules.find(
      (item) => item.ScaleRuleType != curType && item.ScaleRuleEnabled,
    );

    if (otherTypeEnableRule) {
      const { ScaleRuleType: otherType } = otherTypeEnableRule;

      const curTypeName = SCALE_RULE_TYPE_TEXT[curType];
      const otherTypeName = SCALE_RULE_TYPE_TEXT[otherType];

      const message = intl(
        'saenext.basic-info.ScalingGroup.ScalingList.YouHaveEnabledOthertypenamePlease',
        { otherTypeName: otherTypeName, curTypeName: curTypeName },
      );
      Message.error(message);
      return false;
    } else {
      return true;
    }
  };

  const handleRedirectToEvent = (record) => {
    const appName = getParams('name');
    const { ScaleRuleName, ScaleRuleType } = record;

    const ObjectKind =
      ScaleRuleType === SCALE_RULE_TYPE.TIMING ? 'CloneSet' : 'HorizontalPodAutoscaler';
    const ObjectName =
      ScaleRuleType === SCALE_RULE_TYPE.TIMING
        ? ''
        : `${appName}-${appId.substring(0, 8)}-${ScaleRuleName}`.toLowerCase();

    const search = `?name=${appName}&ObjectKind=${ObjectKind}&ObjectName=${ObjectName}`;

    window.xconsoleHistory.push(`/${regionId}/app-list/${appId}/micro-app/event${search}`);
  };

  return (
    <>
      <p className="text-bold">
        {intl('saenext.basic-info.ScalingGroup.ScalingList.TypetextList', { typeText: typeText })}
      </p>
      <CndTable
        dataSource={dataSource}
        hasBorder={false}
        pagination={false}
        columns={[
          {
            title: intl('saenext.basic-info.ScalingGroup.ScalingList.PolicyName'),
            dataIndex: 'ScaleRuleName',
            lock: 'left',
            width: 120,
          },
          {
            title: intl('saenext.basic-info.ScalingGroup.ScalingList.Status'),
            dataIndex: 'ScaleRuleEnabled',
            width: 90,
            cell: val => (
              <StatusIndicator type={val ? 'success' : 'disabled'}>
                {val
                  ? intl('saenext.basic-info.ScalingGroup.ScalingList.Enabled')
                  : intl('saenext.basic-info.ScalingGroup.ScalingList.Disabled')}
              </StatusIndicator>
            ),
          },
          {
            title:
              type === SCALE_RULE_TYPE.TIMING
                ? intl('saenext.basic-info.ScalingGroup.ScalingList.TriggerTimeAndNumberOf')
                : intl('saenext.basic-info.ScalingGroup.ScalingList.SpecialTimePeriodAndNumber'),
            dataIndex: 'Timer',
            width: 180,
            type: [SCALE_RULE_TYPE.TIMING, SCALE_RULE_TYPE.MIX],
            cell: (val, idx, record) => (
              <ScalingRuleSlide appId={appId} type={'preview'} ruleData={record}>
                <LinkButton>
                  {type === SCALE_RULE_TYPE.TIMING
                    ? intl('saenext.basic-info.ScalingGroup.ScalingList.PreviewTimingPolicy')
                    : intl('saenext.basic-info.ScalingGroup.ScalingList.PreviewSpecialTimePeriod')}
                </LinkButton>
              </ScalingRuleSlide>
            ),
          },
          {
            title: intl('saenext.basic-info.ScalingGroup.ScalingList.TargetValue'),
            dataIndex: 'Metric',
            width: 160,
            type: [SCALE_RULE_TYPE.METRIC, SCALE_RULE_TYPE.MIX],
            cell: renderMetric,
          },
          {
            title: intl('saenext.basic-info.ScalingGroup.ScalingList.IndicatorCurrentValue'),
            dataIndex: 'Metric',
            width: 160,
            type: [SCALE_RULE_TYPE.METRIC, SCALE_RULE_TYPE.MIX],
            cell: renderMetricsStatus,
          },
          {
            title: (
              <TextWithBalloon
                text={intl(
                  'saenext.basic-info.ScalingGroup.ScalingList.ApplicationScalingTriggerValue',
                )}
                tips={intl(
                  'saenext.basic-info.ScalingGroup.ScalingList.TheNumberOfApplicationTarget',
                )}
              />
            ),

            dataIndex: 'Metric',
            width: 260,
            type: [SCALE_RULE_TYPE.METRIC, SCALE_RULE_TYPE.MIX],
            cell: renderNextScaleMetrics,
          },
          {
            title: intl(
              'saenext.basic-info.ScalingGroup.ScalingList.MaximumNumberOfApplicationInstances',
            ),
            dataIndex: 'Metric',
            width: 120,
            type: [SCALE_RULE_TYPE.METRIC, SCALE_RULE_TYPE.MIX],
            cell: metric => <span>{metric?.MaxReplicas}</span>,
          },
          {
            title: intl(
              'saenext.basic-info.ScalingGroup.ScalingList.MinimumNumberOfApplicationInstances',
            ),
            dataIndex: 'Metric',
            width: 120,
            type: [SCALE_RULE_TYPE.METRIC, SCALE_RULE_TYPE.MIX],
            cell: metric => <span>{metric?.MinReplicas}</span>,
          },
          // {
          //   title: (
          //     <TextWithBalloon
          //       text={intl('saenext.basic-info.ScalingGroup.ScalingList.IdleMode')}
          //       tips={
          //         <>
          //           <div>
          //             {intl('saenext.basic-info.ScalingGroup.ScalingList.WhenTheIdleModeIs')}
          //           </div>
          //           <div>{intl('saenext.basic-info.ScalingGroup.ScalingList.IfTheIdleModeIs')}</div>
          //         </>
          //       }
          //     />
          //   ),

          //   dataIndex: 'EnableIdle',
          //   width: 120,
          //   type: [SCALE_RULE_TYPE.METRIC, SCALE_RULE_TYPE.MIX],
          //   cell: (value) => (
          //     <div>
          //       <StatusIndicator type={value ? 'success' : 'disabled'}>
          //         {value
          //           ? intl('saenext.basic-info.ScalingGroup.ScalingList.Enabled.1')
          //           : intl('saenext.basic-info.ScalingGroup.ScalingList.NotEnabled')}
          //       </StatusIndicator>
          //       <IdleMonitorPanel appId={appId}>
          //         <Icon
          //           type="chart-line"
          //           size="small"
          //           style={{ cursor: 'pointer', marginLeft: 4, color: '#0070cc', fontSize: 14 }}
          //         />
          //       </IdleMonitorPanel>
          //     </div>
          //   ),
          // },
          {
            title: intl('saenext.basic-info.ScalingGroup.ScalingList.CreationTime'),
            dataIndex: 'CreateTime',
            width: 160,
            cell: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
          },
          {
            title: intl('saenext.basic-info.ScalingGroup.ScalingList.TheLastTimeThePolicy'),
            dataIndex: 'LastDisableTime',
            width: 160,
            cell: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
          },
          {
            title: intl('saenext.basic-info.ScalingGroup.ScalingList.Operation'),
            dataIndex: '',
            lock: 'right',
            width: 160,
            cell: (val, idx, record) => (
              <Actions expandTriggerType="hover">
                <LinkButton
                  key={0}
                  onClick={() => toggleConfirm(record, 'enable')}
                  disabled={record.ScaleRuleEnabled || disabled}
                >
                  {intl('saenext.basic-info.ScalingGroup.ScalingList.Enable')}
                </LinkButton>
                <LinkButton
                  key={1}
                  onClick={() => toggleConfirm(record, 'disable')}
                  disabled={!record.ScaleRuleEnabled || disabled}
                >
                  {intl('saenext.basic-info.ScalingGroup.ScalingList.Disable')}
                </LinkButton>
                <ScalingRuleSlide appId={appId} refresh={refresh} type={'update'} ruleData={record}>
                  <LinkButton key={2} disabled={disabled}>
                    {intl('saenext.basic-info.ScalingGroup.ScalingList.Edit')}
                  </LinkButton>
                </ScalingRuleSlide>
                <LinkButton key={3} onClick={() => handleRedirectToEvent(record)}>
                  {intl('saenext.basic-info.ScalingGroup.ScalingList.Event')}
                </LinkButton>
                <LinkButton key={4} onClick={() => toggleConfirm(record, 'delete')}>
                  {intl('saenext.basic-info.ScalingGroup.ScalingList.Delete')}
                </LinkButton>
              </Actions>
            ),
          },
        ].filter(item => {
          if (item.dataIndex === 'EnableIdle') {
            return isAllocateIdle && (!item.type || item.type?.includes(type));
          } else {
            return !item.type || item.type?.includes(type);
          }
        })}
      />
    </>
  );
};

export default ScalingList;
