import { intl } from '@ali/cnd';
import * as services from '~/services';

export const ActionMap = {
  enable: {
    name: intl('saenext.basic-info.ScalingGroup.constant.Enable'),
    title: intl('saenext.basic-info.ScalingGroup.constant.EnableElasticPolicies'),
    content: intl('saenext.basic-info.ScalingGroup.constant.AfterEnablingTheElasticPolicy'),
    service: services.enableApplicationScalingRule,
  },
  disable: {
    name: intl('saenext.basic-info.ScalingGroup.constant.Disable'),
    title: intl('saenext.basic-info.ScalingGroup.constant.DisableElasticPolicies'),
    content: intl('saenext.basic-info.ScalingGroup.constant.AfterTheElasticPolicyIs'),
    service: services.disableApplicationScalingRule,
  },
  delete: {
    name: intl('saenext.basic-info.ScalingGroup.constant.Delete'),
    title: intl('saenext.basic-info.ScalingGroup.constant.DeleteAnElasticPolicy'),
    content: intl('saenext.basic-info.ScalingGroup.constant.AfterYouDeleteAnElastic'),
    service: services.deleteApplicationScalingRule,
  },
};
