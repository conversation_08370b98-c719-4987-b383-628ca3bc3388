import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Button, Collapse, Loading } from '@ali/cnd';
import * as services from '~/services';
import { cloneDeep, every, filter, forEach, map, some } from 'lodash';
import { SCALE_RULE_TYPE } from '../ScalingRuleSlide/constant';
import ScalingList from './ScalingList';
import ScalingRuleSlide from '../ScalingRuleSlide';
import MicroAppContext from '~/utils/microAppContext';
import CachedData from '~/cache/common';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import ExternalLink from '~/components/shared/ExternalLink';
import RefreshButton from '~/components/shared/RefreshButton';
import { parseMixTimerSchedules } from '../ScalingRuleSlide/utils';
import { getParams } from '~/utils/global';
import { TAB_KEY } from '..';

const ScalingGroup = (props) => {
  const { appId = '', isOnlyShenZhenA, handleChangeVswitch, refreshScalingRules } = props;

  const [scaleRules, setScaleRules] = useState([]);
  const [loading, setLoading] = useState(false);

  const { appConfig, appStatus, setScaleRules: setScaleRulesContext, setScaleRuleCounts, setScaleRuleEnabled, scaleRef } = useContext(MicroAppContext);
  const { LastChangeOrderRunning, CurrentStatus } = appStatus;

  const isAppStart = CurrentStatus === 'RUNNING';
  const isAppUnCreated = CurrentStatus === 'UNKNOWN';
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');

  const disabled = LastChangeOrderRunning || (!isAppStart && !isAppUnCreated) || isInDebt;
  const isActive = getParams('tab') === TAB_KEY.HPA;

  const scaleTypeData = useMemo(() => {
    if (!scaleRules.length) {
      setScaleRuleCounts({});
      return [];
    }
    const data = map(SCALE_RULE_TYPE, (type) => {
      const rules = filter(scaleRules, { ScaleRuleType: type }) as any[];
      if (type === SCALE_RULE_TYPE.MIX) {
        if (!rules.length)
          return {
            type,
            rules,
          };
        const MixTimer = map(rules, (rule) => {
          const { Timer } = cloneDeep(rule);
          Timer.Schedules = parseMixTimerSchedules(Timer.Schedules, rule.Metric);
          return Timer;
        });
        const ScaleRuleNames = map(rules, 'ScaleRuleName');
        rules[0].ScaleRuleNames = ScaleRuleNames;
        rules[0].Timer = MixTimer;
        rules.splice(1);
      }
      return {
        type,
        rules,
      };
    });

    const scaleTypeCount = {};
    forEach(data, (item) => {
      const { type, rules } = item;
      scaleTypeCount[type] = rules.length;
    });
    setScaleRuleCounts(scaleTypeCount);

    return filter(data, (item) => item?.rules?.length > 0);
  }, [scaleRules]);

  useEffect(() => {
    getScaleRules();
  }, [refreshScalingRules]);

  useEffect(() => {
    if (!isActive) return;
    getScaleRules();
  }, [isActive]);

  useEffect(() => {
    scaleRef.current.stopAllScaleRule = stopAllScaleRule;
  }, [scaleRules])
  const getScaleRules = async () => {
    setLoading(true);
    const { Data: { ApplicationScalingRules = [] } = {} } =
      (await services.describeApplicationScalingRules({
        params: {
          AppId: appId,
        },
        options: {
          ignoreError: true,
        },
      })) || {};
    setLoading(false);
    setScaleRules(ApplicationScalingRules);
    setScaleRulesContext(ApplicationScalingRules);

    const scaleRuleEnabled = some(ApplicationScalingRules, { ScaleRuleEnabled: true });
    setScaleRuleEnabled(scaleRuleEnabled);
  };

  const stopAllScaleRule = async () => {
    const scaleRuleNames = map(scaleRules, 'ScaleRuleName') || [];
    const res = await Promise.all(
      scaleRuleNames.map((item) =>
        services.disableApplicationScalingRule({
          params: {
            AppId: appId,
            ScalingRuleName: item,
          },
        }),
      ),
    )
    if (every(res, (item) => !!item)) {
      getScaleRules();
      return true;
    }
  };

  return (
    <Collapse defaultExpandedKeys={['0']} className="mt">
      <Collapse.Panel title={intl('saenext.basic-info.ScalingGroup.ElasticScaling')}>
        <div className="mb flex justify-between">
          <div>
            {isOnlyShenZhenA ? (
              <Button className="mr" disabled={disabled} onClick={handleChangeVswitch}>
                {intl('saenext.basic-info.ScalingGroup.AddAnElasticPolicy')}
              </Button>
            ) : (
              <ScalingRuleSlide appId={appId} refresh={getScaleRules} type={'create'}>
                <Button className="mr" disabled={disabled}>
                  {intl('saenext.basic-info.ScalingGroup.AddAnElasticPolicy')}
                </Button>
              </ScalingRuleSlide>
            )}

            <TextWithBalloon
              text={intl('saenext.basic-info.ScalingGroup.InstructionsForUsingElasticPolicies')}
              tips={
                <ul className="list-decimal pd-card">
                  <li>{intl('saenext.basic-info.ScalingGroup.YouCanCreateUpTo')}</li>
                  <li>{intl('saenext.basic-info.ScalingGroup.WhenTheElasticPolicyIs')}</li>
                  <li>{intl('saenext.basic-info.ScalingGroup.YouCannotAddAnElastic')}</li>
                  {/* <li> 单条定时策略内设置的触发时间请勿小于系统当前时间；如果小于，该策略将在下个执行周期内生效。</li> */}
                  <li>{intl('saenext.basic-info.ScalingGroup.TheIntervalBetweenTwoAdjacent')}</li>
                  <li>{intl('saenext.basic-info.ScalingGroup.SetMultipleTimingPoliciesIf')}</li>
                  <li>{intl('saenext.basic-info.ScalingGroup.IfYouSetMultipleTiming')}</li>
                  <li>{intl('saenext.basic-info.ScalingGroup.MultipleSpecialTimePeriodsSet')}</li>
                  <li>{intl('saenext.basic-info.ScalingGroup.InTheHybridElasticPolicy')}</li>
                  <li>
                    {intl('saenext.basic-info.ScalingGroup.ForMoreInformationAboutUsing')}

                    <ExternalLink url={CachedData.confLink('help:sae:configure-an-auto-scaling-policy')} />
                  </li>
                </ul>
              }
            />
          </div>

          <RefreshButton
            handler={getScaleRules}
            type="normal"
            text={false}
            style={{ minWidth: '32px', padding: 0 }}
          />
        </div>

        <Loading visible={loading} style={{ width: '100%' }}>
          {scaleTypeData.map(item => (
            <div className="mb">
              <ScalingList
                appId={appId}
                type={item.type}
                disabled={disabled}
                dataSource={item.rules}
                refresh={getScaleRules}
              />
            </div>
          ))}
        </Loading>
      </Collapse.Panel>
    </Collapse>
  );
};

export default ScalingGroup;
