import { intl } from '@ali/cnd';
import React, { FC, useState, useEffect } from 'react';
import { SlidePanel } from '@ali/cnd';
import IdleIndicatorMonitor from '~/components/app-detail/micro-app/basic-info/idle-monitor';

type Props = {
  appId: string;
};

const IdleMonitorPanel: FC<Props> = (props) => {
  const { children } = props;
  const [isShowing, setIsShowing] = useState(false);

  useEffect(() => {
    if (!isShowing) return;
  }, [isShowing]);

  return (
    <>
      <span onClick={() => setIsShowing((prev) => !prev)}>{children}</span>
      <SlidePanel
        title={intl('saenext.basic-info.ScalingGroup.IdleMonitorPanel.IdleMonitoring')}
        width={800}
        onClose={() => setIsShowing(false)}
        isShowing={isShowing}
      >
        <IdleIndicatorMonitor appId={props.appId} />
      </SlidePanel>
    </>
  );
};

export default IdleMonitorPanel;
