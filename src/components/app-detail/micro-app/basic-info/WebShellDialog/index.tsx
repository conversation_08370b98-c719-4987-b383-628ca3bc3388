import React from 'react';
import ModalWrap from '~/components/shared/ModalWrap';
import Webshell from '~/components/shared/Webshell';
import modelDecorator from '~/components/shared/Modal/decorator';
import './index.less';

const WebShellDialog = (props) => {
  const { toggleModal, children, regionId, appId, instanceId, containerId } = props;

  const toggleVisible = () => {
    toggleModal({
      visible: false,
    });
  }

  return (
    <ModalWrap
      className="terminal-dialog"
      type="dialog"
      title={''}
      trigger={children}
      width={720}
      closeable={false}
      footer={false}
    >
      <Webshell
        regionId={regionId}
        appId={appId}
        instanceId={instanceId}
        containerId={containerId}
        onClose={toggleVisible}
      />
    </ModalWrap>
  )
}

export default modelDecorator(WebShellDialog);