import { intl } from '@ali/cnd';
import React, { useContext, useEffect, useState } from 'react';
import MicroAppStatics from './MicroAppStatics';
import _, { isEmpty, get, includes } from 'lodash';
import AppBaseInfo from './AppBaseInfo';
import AppActionBar from './AppActionBar';
import { Loading, Tab, Dialog, LinkButton } from '@ali/cnd';
import InstanceGroup from './InstanceGroup';
import services from '~/services';
import ScaleList from './ScaleList';
import MicroAppContext from '~/utils/microAppContext';
import ChangeOrderStatus from './ChangeOrderStatus';
import FeatureContext from '~/utils/featureContext';
import AppNotice from './AppNotice';
import StaticsIndicator from '~/components/monitor-indicator/MicroStaticsIndicator';
import IdleMode from './IdleMode';
import If from '~/components/shared/If';
import { isForbidden } from '~/utils/authUtils';
import { getParams, removeParams, setSearchParams } from '~/utils/global';
import CostAnalysis from './CostAnalysis';
import VSwitchDialog from './VSwitchDialog';
import ScalingList from './ScalingGroup/ScalingList';
import ScaleListContainer from './ScaleListContainer';
import YundunCenter from '../sas';
import BindLanesCard from '../bind/BindLanesCard';
import BindRouteCard from '../bind/BindRouteCard';

export const TAB_KEY = {
  INFO: 'info',
  INSTANCE: 'instance',
  HPA: 'hpa',
  COST: 'cost',
};

const BasicInfo = props => {
  const {
    match: {
      params: { regionId, id: appId },
    },
    history,
    appConfig,
    appStatus,
    isArmsMonitor,
    refresh,
    isMultipleNewAccount,
    isSupportMultiVersions,
    appAddonReleaseInfo,
    hasCmsPermission
  } = props;

  const defaultActiveTab = getParams('tab');

  const { VpcId, VSwitchId, SecurityGroupId } = appConfig;

  const [activeTab, setActiveTab] = useState(defaultActiveTab || TAB_KEY.INFO);

  const [authedVPC, setAuthedVPC] = useState(true);

  const [vpc, setVpc] = useState<any>({});
  const [vSwitchList, setVSwitchList] = useState<any>([]);
  const [securityGroup, setSecurityGroup] = useState<any>({});
  const [isSupportHygon, setIsSupportHygon] = useState(false);
  const [isOnlyShenZhenA, setIsOnlyShenZhenA] = useState(false);
  const [refreshScalingRules, setRefreshScalingRules] = useState(0)

  const { setSlbQpsRt, setSlbQpsRtPermission } = useContext(MicroAppContext);
  const { feature, inDebtData, webFeature } = useContext(FeatureContext);
  const { slbWarmup = false, scaleMonitor = false, idle = false, haiguang_pool = false, allowUpdateAppAliasName = false } = feature;
  const hygonSupportInfo = get(window, 'ALIYUN_CONSOLE_GLOBAL.hygonSupportInfo', {
    regions: [],
    zones: [],
  });
  // const hygonSupportInfo = {"regions":["cn-shanghai","cn-hangzhou","cn-chengdu","cn-beijing"],"zones":["cn-shanghai-b","cn-shanghai-g","cn-shanghai-l","cn-hangzhou-j","cn-beijing-i","cn-chengdu-b"]}
  const costAnalysisRegion = get(window, 'ALIYUN_CONSOLE_GLOBAL.costAnalysisRegion', []);

  useEffect(() => {
    return () => {
      removeParams('tab');
    };
  }, []);

  useEffect(() => {
    if (!defaultActiveTab) return;
    setActiveTab(defaultActiveTab);
  }, [defaultActiveTab]);

  useEffect(() => {
    if (!VpcId) return;
    getVpcs();
    getVswitches();
    getSecurityGroups();
  }, [VpcId]);

  useEffect(() => {
    if (!VpcId) return;
    getVswitches();
  }, [VSwitchId]);

  useEffect(() => {
    if (!VpcId) return;
    getSecurityGroups();
  }, [SecurityGroupId]);

  useEffect(() => {
    setIsSupportHygon(haiguang_pool && includes(hygonSupportInfo?.regions, regionId));
  }, [haiguang_pool, hygonSupportInfo]);

  const getVpcs = async () => {
    const { Vpcs: { Vpc = [] } = {} } =
      (await services.DescribeVpcs({
        params: {
          VpcId,
        },
        customErrorHandle: (error, data) => {
          return error;
        },
      })) || {};
    setVpc(_.get(Vpc, '[0]', {}));
  };

  const getVswitches = async () => {
    const { VSwitches: { VSwitch = [] } = {} } =
      (await services.DescribeVSwitches({
        params: {
          VpcId,
          VSwitchId,
        },
        customErrorHandle: (error, _p, cb) => {
          if (isForbidden(error.code)) {
            setAuthedVPC(false);
          } else {
            setAuthedVPC(true);
            cb?.();
          }
        },
      })) || {};
    setVSwitchList(VSwitch);
    const _isOnlyShenZhenA =
      VSwitch.length > 0 &&
      VSwitch.every(item => {
        return item.ZoneId === 'cn-shenzhen-a';
      });
    setIsOnlyShenZhenA(_isOnlyShenZhenA);
  };

  const getSecurityGroups = async () => {
    const { SecurityGroups: { SecurityGroup = [] } = {} } =
      (await services.DescribeSecurityGroups({
        RegionId: regionId,
        VpcId,
        SecurityGroupId,
      })) || {};
    setSecurityGroup(_.get(SecurityGroup, '[0]', {}));
  };

  const onTabChange = key => {
    setActiveTab(key);
    setSearchParams({ tab: key });
  };

  const handleChangeVswitch = () => {
    const dialog = Dialog.alert({
      content: (
        <div>
          {intl('saenext.micro-app.basic-info.ShenzhenZoneAIsAbout')}{' '}
          <VSwitchDialog
            appConfig={appConfig}
            refresh={() => {
              dialog.hide();
              refresh();
            }}
          >
            <LinkButton className="ml10">
              {intl('saenext.micro-app.basic-info.AppBaseInfo.MultiVswitchDeployment')}
            </LinkButton>
          </VSwitchDialog>
          {intl('saenext.micro-app.basic-info.ConfigureSwitchesInOtherZones')}
        </div>
      ),

      style: { width: 640 },
    });
  };

  // 校验应用版本与实例版本是否一致（手动扩缩/重启应用/修改实例规格/开通微服务治理(此处在微服务治理页面单独校验)）
  const checkAppInstanceVersion = async (title) => {
    const res = await services.CheckApplicationInstanceVersion(
      {
        AppId: appId,
      },
      false,
    );
    if(res && !res?.Data?.CheckResult){
      Dialog.alert({
        title: title,
        content:intl('saenext.application-instance.version.checkResult.false'),
        style:{
          width:480
        },
        okProps:{
          children: intl('saenext.commom.close')
        }
      })
    }
    return res?.Data?.CheckResult
  };

  return (
    <Loading visible={isEmpty(appConfig)} className="full-width">
      <AppActionBar
        appId={appId}
        appConfig={appConfig}
        appStatus={appStatus}
        isArmsMonitor={isArmsMonitor}
        refresh={refresh}
        regionId={regionId}
        history={history}
        isOnlyShenZhenA={isOnlyShenZhenA}
        handleChangeVswitch={handleChangeVswitch}
        checkAppInstanceVersion={checkAppInstanceVersion}
        handleRefreshScalingRules={()=>{setRefreshScalingRules(Date.now())}}
        appAddonReleaseInfo={appAddonReleaseInfo}
        hasCmsPermission={hasCmsPermission}
      />

      <ChangeOrderStatus appStatus={appStatus} regionId={regionId} appId={appId} showSlide />

      <AppNotice appConfig={appConfig} appStatus={appStatus} regionId={regionId} appId={appId} />

      <MicroAppStatics appConfig={appConfig} />

      <Tab
        shape="wrapped"
        className="mt"
        activeKey={activeTab}
        onChange={onTabChange}
        lazyLoad={false}
      >
        <Tab.Item
          key={TAB_KEY.INFO}
          title={intl('saenext.micro-app.basic-info.ApplicationInformation')}
        >
          <YundunCenter
            appId={appId}
            regionId={regionId}
            title={intl('saenext.micro-app.basic-info.SecurityCenter')}
            className="next-extra-card mt"
          />

          <AppBaseInfo
            authedVPC={authedVPC}
            appConfig={appConfig}
            appStatus={appStatus}
            vpc={vpc}
            vSwitchList={vSwitchList}
            securityGroup={securityGroup}
            className="next-extra-card mt"
            refresh={refresh}
            isSupportHygon={isSupportHygon}
            checkAppInstanceVersion={checkAppInstanceVersion}
            inDebtData={inDebtData}
            allowUpdateAppAliasName={allowUpdateAppAliasName}
            isSupportMultiVersions={isSupportMultiVersions}
          />

          <BindLanesCard
            appId={appId}
            regionId={regionId}
            appConfig={appConfig}
            appStatus={appStatus}
            targetContainer="v2"
            autoWarmUp={slbWarmup}
            autoHttpsTls={true}
            className="next-extra-card mt"
            boundCallback={() => {
              // 刷新 appData appStatus 接口
            }} // 1.0 可以不传
            setSlbQpsRt={setSlbQpsRt}
            setSlbQpsRtPermission={setSlbQpsRtPermission}
          />

          <BindRouteCard
            appId={appId}
            regionId={regionId}
            appConfig={appConfig}
            className="next-extra-card mt"
            transmitCallback={() => {
              // 跳转网关路由列表
              const namespaceId = get(appConfig, 'NamespaceId', regionId);
              history.push(`/${regionId}/traffic-management/gateway-route/list?namespaceId=${namespaceId}`);
            }}
            toAppCallback={(appId, appName) => {
              // 应用详情
              window.open(`/${regionId}/app-list/${appId}/micro-app/base?name=${appName}`);
            }}
          />
        </Tab.Item>
        <Tab.Item key={TAB_KEY.INSTANCE} title={intl('saenext.micro-app.basic-info.InstanceList')}>
          <InstanceGroup
            appId={appId}
            appConfig={appConfig}
            appStatus={appStatus}
            vSwitchList={vSwitchList}
          />
        </Tab.Item>
        <Tab.Item key={TAB_KEY.HPA} title={intl('saenext.micro-app.basic-info.ElasticScaling')}>
          <StaticsIndicator regionId={regionId} appId={appId} />
          <ScaleListContainer
            appId={appId}
            appConfig={appConfig}
            appStatus={appStatus}
            isOnlyShenZhenA={isOnlyShenZhenA}
            handleChangeVswitch={handleChangeVswitch}
            refreshScalingRules={refreshScalingRules}
          />
          <If condition={
            (!appConfig.IsStateful) && // 非有状态应用
            (isMultipleNewAccount || idle) && // 多版本账号或闲置模式
            ((appConfig.NewSaeVersion !== 'lite') || // 非轻量版
            (appConfig.NewSaeVersion === 'lite' && feature.liteIdle === true)) //加白 轻量版
            }> 
            <IdleMode appConfig={appConfig} refresh={refresh} />
          </If>
        </Tab.Item>
        {costAnalysisRegion.includes(regionId) && (
          <Tab.Item key={TAB_KEY.COST} title={intl('saenext.micro-app.basic-info.CostAnalysis')}>
            <CostAnalysis appId={appId} appConfig={appConfig} refresh={refresh} checkAppInstanceVersion={checkAppInstanceVersion} />
          </Tab.Item>
        )}
      </Tab>
    </Loading>
  );
};

export default BasicInfo;
