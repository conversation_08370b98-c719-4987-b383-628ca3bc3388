import { intl } from '@ali/cnd';
import React, { useContext, useRef, useState } from 'react';
import { Button, Dialog, Loading, MenuButton, Message, ToolTipCondition, Badge, ErrorPrompt2 as errorPrompt } from '@ali/cnd';
import CachedData from '~/cache/common';
import MinReadyInstance from '~/components/shared/MinReadyInstance';
import FeatureContext from '~/utils/featureContext';
import services from '~/services';
import _ ,{ get, includes }from 'lodash';
import ScaleRuleSlide from './ScaleRuleSlide';
import DeploySlide from './DeploySlide';
import MicroAppContext from '~/utils/microAppContext';
import RestartDialog from './RestartDialog';
import RescaleDialog from './RescaleDialog';
import RollbackSlide from './RollbackSlide';
import { isPhpHighVersion } from '~/utils/global';
import { LANGUAGE_TYPE } from '~/components/shared/DeploySelectorField/constant';
import ScaleRuleSlideContainer from './ScaleRuleSlideContainer';
import PrometheusMonitorDialog from '~/components/shared/PrometheusMonitorField/PrometheusMonitorDialog';

const AppActionBar = (props) => {
  const isIntl = CachedData.isSinSite();
  const { appId, isArmsMonitor, refresh = () => {}, regionId, history, isOnlyShenZhenA, handleChangeVswitch, checkAppInstanceVersion, handleRefreshScalingRules,appAddonReleaseInfo={}, hasCmsPermission } = props;

  const { appConfig, appStatus, mseVersion, mscUpdateStatus, scaleRuleEnabled, scaleRef } =
    useContext(MicroAppContext);

  const {
    AppId,
    PackageType,
    ProgrammingLanguage,
    MinReadyInstances,
    MinReadyInstanceRatio,
    Php,
    SaeVersion,
    MseApplicationId,
    NewSaeVersion = ''
  } = appConfig;
  const { LastChangeOrderRunning, CurrentStatus, RunningInstances, ArmsApmInfo } = appStatus;

  const { feature } = useContext(FeatureContext);
  const { ebpf, php_ebpf, monitor17, enableSaeStdVersionNewMse } = feature;

  const restartRef = useRef(null);
  const prometheusMonitorRef = useRef(null);

  const isAppStart = CurrentStatus === 'RUNNING';
  const isAppStopped = CurrentStatus === 'STOPPED' && RunningInstances === 0;
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');

  const hasArmsAgent = ArmsApmInfo !== '';
  const toggleMonitorDisabled = (LastChangeOrderRunning || !isAppStart) && !hasArmsAgent;

  const readinessObj = (appConfig?.Readiness && JSON.parse(appConfig?.Readiness)) || {};

  const isNotOpenMse_saev1 =
    (SaeVersion === 'v1' || !SaeVersion) &&
    (get(readinessObj, 'httpGet.port') != 54199 &&
      !includes(get(appConfig, 'PreStop', ''), '127.0.0.1:54199/offline') &&
      !get(appConfig, 'EnableAhas', false));

  const isNewMsc =
    NewSaeVersion === 'pro' ||
    ((!NewSaeVersion || NewSaeVersion === 'std') && enableSaeStdVersionNewMse &&
      appConfig?.ProgrammingLanguage === LANGUAGE_TYPE.JAVA &&
      (SaeVersion === 'v2' || isNotOpenMse_saev1));
  const goToDeploy = () => {
    history.push(`/${regionId}/app-list/${AppId}/micro-app/deploy`);
  };

  const handleBeforeAppStop = () => {
    if (isAppStart && scaleRuleEnabled) {
      Dialog.confirm({
        content: (
          <p style={{ width: 500 }}>
            {intl('saenext.micro-app.basic-info.AppActionBar.CurrentlyYourApplicationHasA')}
          </p>
        ),

        onOk: () => {
          return new Promise(async (resolve, reject) => {
            const res = await scaleRef.current.stopAllScaleRule?.();
            if (res) {
              Message.success(
                intl('saenext.micro-app.basic-info.AppActionBar.AllElasticPoliciesAreDisabled'),
              );
              resolve(true);
              handleAppStartStop();
            } else {
              reject();
            }
          });
        },
        okProps: { children: intl('saenext.common.dialog.ok') },
        cancelProps: { children: intl('saenext.common.dialog.cancel') },
      });
    } else {
      handleAppStartStop();
    }
  };

  const handleAppStartStop = () => {
    if (isAppStart) {
      Dialog.confirm({
        title: intl('saenext.micro-app.basic-info.AppActionBar.StopApplication'),
        content: (
          <p style={{ width: 500 }}>
            {intl('saenext.micro-app.basic-info.AppActionBar.AfterTheApplicationIsStopped')}
          </p>
        ),

        onOk: () => {
          return new Promise(async (resolve, reject) => {
            const { Data } =
              (await services.stopMicroApplication({
                AppId: appId,
              })) || {};
            if (Data) {
              Message.success(
                intl('saenext.micro-app.basic-info.AppActionBar.StoppedSuccessfully'),
              );
              refresh();
              resolve(true);
            } else {
              Message.error(intl('saenext.micro-app.basic-info.AppActionBar.StopFailed'));
              reject();
            }
          });
        },
        okProps: { children: intl('saenext.common.dialog.ok') },
        cancelProps: { children: intl('saenext.common.dialog.cancel') },
      });
    } else {
      Dialog.confirm({
        title: intl('saenext.micro-app.basic-info.AppActionBar.StartTheApplication'),
        content: (
          <p style={{ width: 500 }}>
            {intl('saenext.micro-app.basic-info.AppActionBar.AfterTheApplicationIsStarted')}
          </p>
        ),

        onOk: () => {
          return new Promise(async (resolve, reject) => {
            const { Data } =
              (await services.startMicroApplication({
                AppId: appId,
              })) || {};
            if (Data) {
              Message.success(
                intl('saenext.micro-app.basic-info.AppActionBar.StartedSuccessfully'),
              );
              refresh();
              resolve(true);
            } else {
              Message.error(intl('saenext.micro-app.basic-info.AppActionBar.FailedToStart'));
              reject();
            }
          });
        },
        okProps: { children: intl('saenext.common.dialog.ok') },
        cancelProps: { children: intl('saenext.common.dialog.cancel') },
      });
    }
  };

  const handleAppArms = () => {
    const isNewLanguage =
      ProgrammingLanguage === 'python' ||
      ProgrammingLanguage === 'other' ||
      (ProgrammingLanguage === 'php' && ebpf && php_ebpf);

    if (isArmsMonitor) {
      const message = !isNewLanguage
        ? intl(
            'saenext.micro-app.basic-info.AppActionBar.AfterStoppingApplicationMonitoringInformation',
          )
        : intl('saenext.micro-app.basic-info.AppActionBar.TurnOffApplicationMonitoringThe');
      Dialog.confirm({
        title: intl('saenext.micro-app.basic-info.AppActionBar.DisableApplicationMonitoring'),
        content: <p style={{ width: 500 }}>{message}</p>,
        onOk: () => {
          return new Promise(async (resolve, reject) => {
            const { Data } =
              (await services.disableMicroAppArms({
                AppId: appId,
              })) || {};
            if (Data) {
              Message.success(
                intl(
                  'saenext.micro-app.basic-info.AppActionBar.ApplicationMonitoringStoppedSuccessfully',
                ),
              );
              refresh();
              resolve(true);
            } else {
              Message.error(
                intl('saenext.micro-app.basic-info.AppActionBar.FailedToStopApplicationMonitoring'),
              );
              reject();
            }
          });
        },
        okProps: { children: intl('saenext.common.dialog.ok') },
        cancelProps: { children: intl('saenext.common.dialog.cancel') },
      });
    } else {
      const message = hasArmsAgent
        ? intl('saenext.micro-app.basic-info.AppActionBar.AreYouSureYouWant')
        : !isNewLanguage
        ? intl('saenext.micro-app.basic-info.AppActionBar.IfYouTurnOnThe')
        : intl('saenext.micro-app.basic-info.AppActionBar.TurnOnTheApplicationMonitoring');

      Dialog.confirm({
        title: intl('saenext.micro-app.basic-info.AppActionBar.EnableApplicationMonitoring'),
        content: <p style={{ width: 500 }}>{message}</p>,
        onOk: () => {
          return new Promise(async (resolve, reject) => {
            const { Data } =
              (await services.enableMicroAppArms({
                AppId: appId,
              })) || {};
            if (Data) {
              Message.success(
                intl('saenext.micro-app.basic-info.AppActionBar.ApplicationMonitoringEnabled'),
              );
              refresh();
              resolve(true);
            } else {
              Message.error(
                intl(
                  'saenext.micro-app.basic-info.AppActionBar.FailedToEnableApplicationMonitoring',
                ),
              );
              reject();
            }
          });
        },
      });
    }
  };

  const handleCloseMsc = () => {
    Dialog.confirm({
      title: intl('saenext.micro-app.basic-info.AppActionBar.AreYouSureYouWant.1'),
      content: (
        <p style={{ width: 500 }}>
          {intl('saenext.micro-app.basic-info.AppActionBar.AfterTheMicroserviceGovernanceFunction')}
        </p>
      ),

      okProps: {
        children: intl('saenext.micro-app.basic-info.AppActionBar.ConfirmShutdown'),
      },
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res = await services.disenableMsc({
            AppId,
          });
          if (res) {
            Message.success(
              intl(
                'saenext.micro-app.basic-info.AppActionBar.MicroserviceGovernanceClosedSuccessfully',
              ),
            );
            refresh();
            resolve(res);
          } else {
            Message.error(res);
            reject();
          }
        });
      },
    });
  };

  const toggleHandleUpdateMsc = () => {
    Dialog.show({
      title: intl('saenext.micro-app.basic-info.AppActionBar.AreYouSureYouWant.2'),
      content: (
        <div style={{ width: 500 }}>
          <p>
            {intl('saenext.micro-app.basic-info.AppActionBar.PremiseBeforeUpgradingYouNeed')}
            <a
              href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=${isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'}`}
              target="_blank"
            >
              {intl('saenext.micro-app.basic-info.AppActionBar.Activate')}
            </a>
            {intl(
              'saenext.micro-app.basic-info.AppActionBar.MseServiceGovernanceProfessionalEdition',
            )}
            <a
              href={CachedData.confLink('help:mse:microservice-governance-edition-selection')}
              target="_blank"
            >
              {intl('saenext.micro-app.basic-info.AppActionBar.VersionSelection')}
            </a>
            {intl('saenext.micro-app.basic-info.AppActionBar.And')}

            <a
              href={CachedData.confLink('help:mse:billing-overview')}
              target="_blank"
            >
              {intl('saenext.micro-app.basic-info.AppActionBar.BillingOverview')}
            </a>
          </p>
          <p style={{ color: 'orange' }}>
            {intl('saenext.micro-app.basic-info.AppActionBar.AfterTheUpgradeMseMicroservice')}
            <br />
            {intl('saenext.micro-app.basic-info.AppActionBar.AnApplicationRestartWillBe')}
          </p>
        </div>
      ),

      okProps: {
        children: intl('saenext.micro-app.basic-info.AppActionBar.ConfirmUpgrade'),
      },
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res = await services.upgradeMsc({
            AppId: appId,
            EnableAhas: mseVersion === 2,
          });
          if (!res) {
            Message.error(
              intl(
                'saenext.micro-app.basic-info.AppActionBar.FailedToUpgradeMicroservicesGovernance',
              ),
            );
            reject();
            return;
          }
          Message.success(
            intl(
              'saenext.micro-app.basic-info.AppActionBar.TheMicroserviceGovernanceBusinessEdition',
            ),
          );
          refresh();
          resolve(true);
        });
      },
    });
  };

  const handleAppDelete = () => {
    Dialog.alert({
      title: intl('saenext.micro-app.basic-info.AppActionBar.DeleteAnApplication'),
      content: (
        <p style={{ width: 500 }}>
          {intl('saenext.micro-app.basic-info.AppActionBar.AfterYouDeleteAnApplication')}
        </p>
      ),

      onOk: async () => {
        try {
          const res = await services.deleteMicroApplication({
            params: {
              AppId: appId,
            },
            options: {
              region: regionId,
            }
          });
          if (res?.Data) {
            Message.success(intl('saenext.micro-app.basic-info.AppActionBar.DeletedSuccessfully'));
            history.replace(`/${regionId}/app-list/micro`);
            return true;
          } else {
            Message.error(intl('saenext.micro-app.basic-info.AppActionBar.FailedToDelete'));
            return false;
          }
        } catch (error) {
          if (error.code === 'FetcherErrorRiskCancelled') {
            // 风控弹窗点取消
            return false;
          } else {
            // 通用控制台弹窗
            errorPrompt(error);
            return false;
          }
        }
      },
      okProps: { children: intl('saenext.common.dialog.ok') }
    });
  };

  const renderDisabledTip = (conditions: Array<string>) => {
    let tip = '';
    if (!_.isEmpty(conditions)) {
      for (let i = 0; i < conditions.length; i++) {
        const condition = conditions[i];

        if (condition === 'isInDebt' && isInDebt) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.YourAccountIsOverduePlease');
          break;
        }
        if (condition === 'LastChangeOrderRunning' && LastChangeOrderRunning) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.TheApplicationIsChangingPlease');
          break;
        }
        if (condition === 'isAppStart' && !isAppStart) {
          tip = intl('saenext.micro-app.basic-info.AppActionBar.TheCurrentApplicationStatusIs');
          break;
        }
      }
    }
    return tip;
  };

  return (
    <>
      <div style={{ position: 'absolute', top: -50, right: 0 }}>
        <div>
          <DeploySlide
            isPreview
            title={intl('saenext.micro-app.basic-info.AppActionBar.ViewApplicationConfigurations')}
            appConfig={appConfig}
            isAppStopped={isAppStopped}
            appAddonReleaseInfo={appAddonReleaseInfo}
          >
            <Button>
              {intl('saenext.micro-app.basic-info.AppActionBar.ViewApplicationConfigurations')}
            </Button>
          </DeploySlide>
          <ToolTipCondition
            show={LastChangeOrderRunning || isInDebt}
            align="t"
            tip={renderDisabledTip(['LastChangeOrderRunning', 'isInDebt'])}
          >
            <Button
              onClick={isOnlyShenZhenA ? handleChangeVswitch : goToDeploy}
              type="primary"
              className="ml-s"
              disabled={LastChangeOrderRunning || isInDebt}
            >
              {!isAppStopped
                ? intl('saenext.micro-app.basic-info.AppActionBar.DeployApplications')
                : intl('saenext.micro-app.basic-info.AppActionBar.ModifyApplicationConfiguration')}
            </Button>
          </ToolTipCondition>
          <ToolTipCondition
            show={LastChangeOrderRunning || isInDebt}
            align="t"
            tip={renderDisabledTip(['LastChangeOrderRunning', 'isInDebt'])}
          >
            {isOnlyShenZhenA ? (
              <Button
                className="ml-s"
                disabled={LastChangeOrderRunning || isInDebt}
                onClick={handleChangeVswitch}
              >
                {intl('saenext.micro-app.basic-info.AppActionBar.RollbackHistory')}
              </Button>
            ) : (
              <RollbackSlide appId={appId} appConfig={appConfig}>
                <Button className="ml-s" disabled={LastChangeOrderRunning || isInDebt}>
                  {intl('saenext.micro-app.basic-info.AppActionBar.RollbackHistory')}
                </Button>
              </RollbackSlide>
            )}
          </ToolTipCondition>
          <ToolTipCondition
            show={LastChangeOrderRunning || !isAppStart || isInDebt}
            align="t"
            tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart', 'isInDebt'])}
          >
            {isOnlyShenZhenA ? (
              <Button
                className="ml-s"
                disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                onClick={handleChangeVswitch}
              >
                {intl('saenext.micro-app.basic-info.AppActionBar.ManualScaling')}
              </Button>
            ) : (
              <RescaleDialog
                appId={appId}
                refresh={refresh}
                checkAppInstanceVersion={checkAppInstanceVersion}
              >
                <Button
                  className="ml-s"
                  disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                >
                  {intl('saenext.micro-app.basic-info.AppActionBar.ManualScaling')}
                </Button>
              </RescaleDialog>
            )}
          </ToolTipCondition>
          <Badge
            content="hot"
            style={{
              backgroundColor: '#D50B16',
              color: '#fff',
              borderRadius: 10,
              marginLeft: -6,
              marginTop: -4,
            }}
          >
            <ToolTipCondition
              show={LastChangeOrderRunning || !isAppStart || isInDebt}
              align="t"
              tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart', 'isInDebt'])}
            >
              {isOnlyShenZhenA ? (
                <Button
                  className="ml-s"
                  disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                  onClick={handleChangeVswitch}
                >
                  {intl('saenext.micro-app.basic-info.AppActionBar.AutomaticScaling')}
                </Button>
              ) : (
                <ScaleRuleSlideContainer
                  appId={appId}
                  // appConfig={appConfig}
                  refresh={refresh}
                  type={'create'}
                  handleRefreshScalingRules={handleRefreshScalingRules}
                >
                  <Button
                    className="ml-s"
                    disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
                  >
                    {intl('saenext.micro-app.basic-info.AppActionBar.AutomaticScaling')}
                  </Button>
                </ScaleRuleSlideContainer>
              )}
            </ToolTipCondition>
          </Badge>
          <ToolTipCondition
            show={LastChangeOrderRunning || (!isAppStart && isInDebt)}
            align="t"
            tip={renderDisabledTip(['LastChangeOrderRunning', 'isInDebt'])}
          >
            <Button
              onClick={handleBeforeAppStop}
              disabled={LastChangeOrderRunning || (!isAppStart && isInDebt)}
              className="ml-s"
            >
              {isAppStart
                ? intl('saenext.micro-app.basic-info.AppActionBar.StopApplication')
                : intl('saenext.micro-app.basic-info.AppActionBar.StartTheApplication')}
            </Button>
          </ToolTipCondition>

          <MenuButton
            autoWidth={false}
            label={intl('saenext.micro-app.basic-info.AppActionBar.More')}
            className="ml-s"
          >
            <MenuButton.Item
              disabled={LastChangeOrderRunning || !isAppStart || isInDebt}
              onClick={async () => {
                if (isOnlyShenZhenA) {
                  handleChangeVswitch();
                } else {
                  const checkResult = await checkAppInstanceVersion(
                    intl('saenext.micro-app.basic-info.AppActionBar.RestartTheApplication'),
                  );
                  if (checkResult) {
                    restartRef.current.toogleVisible();
                  }
                }
              }}
            >
              <ToolTipCondition
                show={LastChangeOrderRunning || !isAppStart || isInDebt}
                align="l"
                tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart', 'isInDebt'])}
              >
                {intl('saenext.micro-app.basic-info.AppActionBar.RestartTheApplication')}
              </ToolTipCondition>
            </MenuButton.Item>

            {(!NewSaeVersion || NewSaeVersion === 'std') &&
              ((ProgrammingLanguage === LANGUAGE_TYPE.JAVA &&
                (appConfig.jdk !== 'Dragonwell 17' || monitor17)) ||
                (ProgrammingLanguage === LANGUAGE_TYPE.PHP &&
                  !isPhpHighVersion(Php) &&
                  ebpf &&
                  php_ebpf)) && (
                <MenuButton.Item disabled={toggleMonitorDisabled} onClick={handleAppArms}>
                  {isArmsMonitor
                    ? intl('saenext.micro-app.basic-info.AppActionBar.StopApplicationMonitoring')
                    : intl('saenext.micro-app.basic-info.AppActionBar.EnableApplicationMonitoring')}
                </MenuButton.Item>
              )}

            {!isNewMsc && SaeVersion === 'v2' && MseApplicationId && (
              <MenuButton.Item
                disabled={LastChangeOrderRunning || !isAppStart}
                onClick={handleCloseMsc}
              >
                <ToolTipCondition
                  show={LastChangeOrderRunning || !isAppStart}
                  align="l"
                  tip={renderDisabledTip(['LastChangeOrderRunning', 'isAppStart'])}
                >
                  {intl('saenext.micro-app.basic-info.AppActionBar.DisableMicroserviceGovernance')}
                </ToolTipCondition>
              </MenuButton.Item>
            )}

            {!isNewMsc && SaeVersion === 'v1' && mscUpdateStatus === 'enabled' && (
              <MenuButton.Item
                disabled={LastChangeOrderRunning || !isAppStart || appConfig.EnableAhas}
                onClick={toggleHandleUpdateMsc}
              >
                {intl(
                  'saenext.micro-app.basic-info.AppActionBar.UpgradeToMseMicroserviceGovernance',
                )}
              </MenuButton.Item>
            )}

            <MenuButton.Item disabled={LastChangeOrderRunning} onClick={handleAppDelete}>
              <ToolTipCondition
                show={LastChangeOrderRunning}
                align="l"
                tip={renderDisabledTip(['LastChangeOrderRunning'])}
              >
                {intl('saenext.micro-app.basic-info.AppActionBar.DeleteAnApplication')}
              </ToolTipCondition>
            </MenuButton.Item>
            {NewSaeVersion !== 'lite' && (
              <MenuButton.Item
                onClick={() => {
                  prometheusMonitorRef.current.toogleVisible();
                }}
              >
                {intl('saenext.shared.PrometheusMonitorField.PrometheusMonitorNotEnable.ConfigurePrometheusMonitoring')}
              </MenuButton.Item>
            )}
          </MenuButton>
        </div>
      </div>

      <RestartDialog ref={restartRef} appId={appId} refresh={refresh} />
      <PrometheusMonitorDialog ref={prometheusMonitorRef} appConfig={appConfig} appAddonReleaseInfo={appAddonReleaseInfo} refresh={refresh} hasCmsPermission={hasCmsPermission} />
    </>
  );
};

export default AppActionBar;
