import React, { useContext, useEffect, useState } from 'react';
import {
  Actions,
  Button,
  CndTable,
  Collapse,
  Dialog,
  intl,
  LinkButton,
  Message,
  StatusIndicator,
  ToolTipCondition,
} from '@ali/cnd';
import MicroAppContext from '~/utils/microAppContext';
import CachedData from '~/cache/common';
import { TAB_KEY } from '..';
import { getParams } from '~/utils/global';
import services from '~/services';
import { every, filter, groupBy, isEmpty, map, some } from 'lodash';
import moment from 'moment';
import ScaleRuleSlide from '../ScaleRuleSlide';
import { SCALE_RULE_TYPE } from '../ScaleRuleSlide/constant';
import { ActionMap } from '../ScalingGroup/constant';
import TimerPreview from '../ScalingRuleSlide/TimerPreview';
import If from '~/components/shared/If';
import { parseRule } from '../ScaleRuleSlide/utils';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import ExternalLink from '~/components/shared/ExternalLink';

const ScaleList = (props) => {
  const { appId = '', refreshScalingRules, isOnlyShenZhenA, handleChangeVswitch } = props;

  const [refreshIndex, setRefreshIndex] = useState(0);

  const {
    appConfig,
    appStatus,
    scaleRules,
    setScaleRules: setScaleRulesContext,
    setScaleRuleCounts,
    scaleRuleEnabled,
    setScaleRuleEnabled,
    scaleRef,
  } = useContext(MicroAppContext);

  const { RegionId: regionId } = appConfig;
  const { LastChangeOrderRunning, CurrentStatus } = appStatus;

  const isAppStart = CurrentStatus === 'RUNNING';
  const isAppUnCreated = CurrentStatus === 'UNKNOWN';
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');

  const disabled = LastChangeOrderRunning || (!isAppStart && !isAppUnCreated) || isInDebt;
  const isActive = getParams('tab') === TAB_KEY.HPA;

  useEffect(() => {
    if (!isActive) return;
    refresh();
  }, [isActive, refreshScalingRules]);

  useEffect(() => {
    scaleRef.current.stopAllScaleRule = stopAllScaleRule;
  }, [scaleRules]);

  const getScaleRules = async () => {
    const res = await services.describeApplicationScalingRules({
      params: {
        AppId: appId,
      },
      options: {
        ignoreError: true,
      },
    });
    if (!res.Success) return;
    const { Data: { ApplicationScalingRules = [] } = {} } = res || {};
    setScaleRulesContext(ApplicationScalingRules);

    const scaleRuleEnabled = some(ApplicationScalingRules, { ScaleRuleEnabled: true });
    setScaleRuleEnabled(scaleRuleEnabled);

    const scaleRuleMerged = mergeRelatedTimers(ApplicationScalingRules);

    return {
      data: scaleRuleMerged,
      total: scaleRuleMerged.length,
    };
  };

  const mergeRelatedTimers = (rules) => {
    const ruleMap = {};

    rules.forEach((item) => {
      // 提取基础规则名（去掉#及后面的部分）
      const baseName = item.ScaleRuleName.split('#')[0];

      const baseItem = ruleMap[baseName];

      if (baseItem) {
        baseItem.ScaleRuleNames.push(item.ScaleRuleName);
        baseItem.Timers = [baseItem.Timer, item.Timer].filter((timer) => !isEmpty(timer));
        delete baseItem.Timer;
      } else {
        ruleMap[baseName] = {
          ...item,
          ScaleRuleNames: [item.ScaleRuleName],
        };
      }
    });

    return Object.values(ruleMap);
  };

  const refresh = () => {
    setRefreshIndex(refreshIndex + 1);
  };

  const stopAllScaleRule = async () => {
    const scaleRuleNames = map(scaleRules, 'ScaleRuleName') || [];
    const res = await Promise.all(
      scaleRuleNames.map((item) =>
        services.disableApplicationScalingRule({
          params: {
            AppId: appId,
            ScalingRuleName: item,
          },
        }),
      ),
    );
    if (every(res, (item) => !!item)) {
      getScaleRules();
      return true;
    }
  };

  const toggleConfirm = (record, action) => {
    const { name, title, content, service } = ActionMap[action];

    Dialog.confirm({
      title,
      content,
      onOk: async () => {
        const { ScaleRuleName, ScaleRuleNames } = record;
        const res = ScaleRuleNames?.length
          ? await Promise.all(
              ScaleRuleNames.map((item) =>
                service({
                  params: {
                    AppId: appId,
                    ScalingRuleName: item,
                  },
                }),
              ),
            )
          : await service({
              params: {
                AppId: appId,
                ScalingRuleName: ScaleRuleName,
              },
            });

        if (res) {
          Message.success(
            intl('saenext.basic-info.ScalingGroup.ScalingList.NameElasticPolicySucceeded', {
              name: name,
            }),
          );
          refresh();
        } else {
          Message.error(
            intl('saenext.basic-info.ScalingGroup.ScalingList.NameElasticPolicyFailed', {
              name: name,
            }),
          );
          throw new Error('network error');
        }
      },
    });
  };

  const actionDisabledTip = () => {
    if (LastChangeOrderRunning) {
      return intl('saenext.basic-info.ScaleList.ApplicationChanging');
    }
    if (!isAppStart && !isAppUnCreated) {
      return intl('saenext.basic-info.ScaleList.ApplicationNotStarted');
    }
    if (isInDebt) {
      return intl('saenext.basic-info.ScaleList.CurrentlyInArrears');
    }
  };

  const preventEnableMultiRule = (record) => {
    if (record.ScaleRuleEnabled) {
      return intl('saenext.basic-info.ScaleList.Enabled');
    }
    const enabledRuleMap = groupBy(filter(scaleRules, { ScaleRuleEnabled: true }), 'ScaleRuleType');
    const { ScaleRuleType } = record;
    if (enabledRuleMap[SCALE_RULE_TYPE.METRIC] || enabledRuleMap[SCALE_RULE_TYPE.MIX]) {
      return intl('saenext.basic-info.ScaleList.CurrentlyExistingMetricRulesAre');
    } else if (enabledRuleMap[SCALE_RULE_TYPE.TIMING]) {
      if (ScaleRuleType !== SCALE_RULE_TYPE.TIMING) {
        return intl('saenext.basic-info.ScaleList.CurrentlyAScheduledRuleIs');
      } else if (enabledRuleMap[SCALE_RULE_TYPE.TIMING]?.length >= 5) {
        return intl('saenext.basic-info.ScaleList.EnableUpToTimingRules');
      }
    }
  };

  const onPreviewTimer = (record) => {
    const ruleFormat = parseRule(record);
    const { ScalingRuleType, ScalingRuleTimers, ScalingRuleTimer, ScalingRuleMetric } = ruleFormat;
    const ruleTimer =
      ScalingRuleType === SCALE_RULE_TYPE.MIX ? ScalingRuleTimers : [ScalingRuleTimer];

    Dialog.show({
      title: intl('saenext.basic-info.ScalingRuleSlide.constant.PreviewElasticPolicies'),
      style: { width: 900 },
      closeMode: ['close', 'mask', 'esc'],
      content: (
        <TimerPreview type={ScalingRuleType} ruleTimer={ruleTimer} ruleMetric={ScalingRuleMetric} />
      ),

      footer: false,
    });
  };

  const handleRedirectToEvent = (record) => {
    const appName = getParams('name');
    const { ScaleRuleName, ScaleRuleType } = record;

    const ObjectKind =
      ScaleRuleType === SCALE_RULE_TYPE.TIMING ? 'CloneSet' : 'HorizontalPodAutoscaler';
    const ObjectName =
      ScaleRuleType === SCALE_RULE_TYPE.TIMING
        ? ''
        : `${appName}-${appId.substring(0, 8)}-${ScaleRuleName}`.toLowerCase();

    const search = `?name=${appName}&ObjectKind=${ObjectKind}&ObjectName=${ObjectName}`;

    window.xconsoleHistory.push(`/${regionId}/app-list/${appId}/micro-app/event${search}`);
  };

  const beforeOpenSlide = () => {
    if (isOnlyShenZhenA) {
      handleChangeVswitch();
      throw new Error('isOnlyShenZhenA');
    }
  };

  const operation = (
    <>
      <ScaleRuleSlide appId={appId} refresh={refresh} type={'create'}>
        <Button type="primary" className="mr" onClick={beforeOpenSlide} disabled={disabled}>
          {intl('saenext.basic-info.ScalingGroup.AddAnElasticPolicy')}
        </Button>
      </ScaleRuleSlide>

      <TextWithBalloon
        text={intl('saenext.basic-info.ScalingGroup.InstructionsForUsingElasticPolicies')}
        tips={
          <ul className="list-decimal pd-card">
            <li>{intl('saenext.basic-info.ScalingGroup.YouCanCreateUpTo')}</li>
            <li>{intl('saenext.basic-info.ScalingGroup.WhenTheElasticPolicyIs')}</li>
            <li>{intl('saenext.basic-info.ScalingGroup.YouCannotAddAnElastic')}</li>
            {/* <li> 单条定时策略内设置的触发时间请勿小于系统当前时间；如果小于，该策略将在下个执行周期内生效。</li> */}
            <li>{intl('saenext.basic-info.ScalingGroup.TheIntervalBetweenTwoAdjacent')}</li>
            <li>{intl('saenext.basic-info.ScalingGroup.SetMultipleTimingPoliciesIf')}</li>
            <li>{intl('saenext.basic-info.ScalingGroup.IfYouSetMultipleTiming')}</li>
            <li>{intl('saenext.basic-info.ScalingGroup.MultipleSpecialTimePeriodsSet')}</li>
            <li>{intl('saenext.basic-info.ScalingGroup.InTheHybridElasticPolicy')}</li>
            <li>
              {intl('saenext.basic-info.ScalingGroup.ForMoreInformationAboutUsing')}

              <ExternalLink url="https://help.aliyun.com/document_detail/134120.html" />
            </li>
          </ul>
        }
      />
    </>
  );

  return (
    <Collapse defaultExpandedKeys={['0']} className="mt">
      <Collapse.Panel title={intl('saenext.basic-info.ScalingGroup.ElasticScaling')}>
        <CndTable
          showRefreshButton
          refreshIndex={refreshIndex}
          operation={operation}
          fetchData={getScaleRules}
          columns={[
            {
              title: intl('saenext.basic-info.ScalingGroup.ScalingList.PolicyName'),
              dataIndex: 'ScaleRuleName',
              lock: 'left',
              width: 120,
            },
            {
              title: intl('saenext.basic-info.ScalingGroup.ScalingList.Status'),
              dataIndex: 'ScaleRuleEnabled',
              width: 90,
              cell: (val) => (
                <StatusIndicator type={val ? 'success' : 'disabled'}>
                  {val
                    ? intl('saenext.basic-info.ScalingGroup.ScalingList.Enabled')
                    : intl('saenext.basic-info.ScalingGroup.ScalingList.Disabled')}
                </StatusIndicator>
              ),
            },
            {
              title: intl('saenext.basic-info.ScaleList.IndicatorRules'),
              dataIndex: 'Metric',
              width: 120,
              cell: (val) => {
                const configed =
                  !isEmpty(val.Metrics) ||
                  (val?.MetricSource === 'prometheus' && !isEmpty(val.PrometheusMetrics));
                return (
                  <StatusIndicator type={configed ? 'success' : 'disabled'}>
                    {configed
                      ? intl('saenext.basic-info.ScaleList.Configured')
                      : intl('saenext.basic-info.ScaleList.NotConfigured')}
                  </StatusIndicator>
                );
              },
            },
            {
              title: intl('saenext.basic-info.ScaleList.TimingRule'),
              dataIndex: 'Timer',
              width: 120,
              cell: (val, idx, record) => {
                const configed = !isEmpty(val) || !isEmpty(record.Timers);
                return (
                  <>
                    <StatusIndicator type={configed ? 'success' : 'disabled'}>
                      {configed
                        ? intl('saenext.basic-info.ScaleList.Configured')
                        : intl('saenext.basic-info.ScaleList.NotConfigured')}
                    </StatusIndicator>
                    <If condition={configed}>
                      <Button
                        text
                        type="primary"
                        className="ml-s"
                        onClick={() => onPreviewTimer(record)}
                      >
                        {intl('saenext.basic-info.ScaleList.Preview')}
                      </Button>
                    </If>
                  </>
                );
              },
            },
            // {
            //   title: intl(
            //     'saenext.basic-info.ScalingGroup.ScalingList.MaximumNumberOfApplicationInstances',
            //   ),
            //   dataIndex: 'Metric',
            //   width: 120,
            //   cell: metric => <span>{metric?.MaxReplicas}</span>,
            // },
            // {
            //   title: intl(
            //     'saenext.basic-info.ScalingGroup.ScalingList.MinimumNumberOfApplicationInstances',
            //   ),
            //   dataIndex: 'Metric',
            //   width: 120,
            //   cell: metric => <span>{metric?.MinReplicas}</span>,
            // },
            {
              title: intl('saenext.basic-info.ScalingGroup.ScalingList.CreationTime'),
              dataIndex: 'CreateTime',
              width: 160,
              cell: (val) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
            },
            // {
            //   title: intl('saenext.basic-info.ScalingGroup.ScalingList.TheLastTimeThePolicy'),
            //   dataIndex: 'LastDisableTime',
            //   width: 160,
            //   cell: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
            // },
            {
              title: intl('saenext.basic-info.ScalingGroup.ScalingList.Operation'),
              dataIndex: '',
              lock: 'right',
              width: 160,
              cell: (val, idx, record) => (
                <Actions expandTriggerType="hover">
                  <ToolTipCondition
                    align="l"
                    show={!!actionDisabledTip() || !!preventEnableMultiRule(record)}
                    tip={actionDisabledTip() || preventEnableMultiRule(record)}
                  >
                    <LinkButton
                      key={0}
                      onClick={() => toggleConfirm(record, 'enable')}
                      disabled={!!actionDisabledTip() || !!preventEnableMultiRule(record)}
                    >
                      {intl('saenext.basic-info.ScalingGroup.ScalingList.Enable')}
                    </LinkButton>
                  </ToolTipCondition>
                  <LinkButton
                    key={1}
                    onClick={() => toggleConfirm(record, 'disable')}
                    disabled={!record.ScaleRuleEnabled || disabled}
                  >
                    {intl('saenext.basic-info.ScalingGroup.ScalingList.Disable')}
                  </LinkButton>
                  <ScaleRuleSlide appId={appId} refresh={refresh} type={'update'} ruleData={record}>
                    <LinkButton key={2} disabled={disabled}>
                      {intl('saenext.basic-info.ScalingGroup.ScalingList.Edit')}
                    </LinkButton>
                  </ScaleRuleSlide>
                  <LinkButton key={3} onClick={() => handleRedirectToEvent(record)}>
                    {intl('saenext.basic-info.ScalingGroup.ScalingList.Event')}
                  </LinkButton>
                  <LinkButton key={4} onClick={() => toggleConfirm(record, 'delete')}>
                    {intl('saenext.basic-info.ScalingGroup.ScalingList.Delete')}
                  </LinkButton>
                </Actions>
              ),
            },
          ]}
        />
      </Collapse.Panel>
    </Collapse>
  );
};

export default ScaleList;
