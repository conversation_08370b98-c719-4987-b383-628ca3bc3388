import { intl, LinkButton } from '@ali/cnd';
import { Link, Message } from '@ali/cnd';
import { noop } from 'lodash';
import React from 'react';
import If from '~/components/shared/If';
import SlideWrap from '~/components/shared/SlideWrap';
import { ChangeOrderBase } from '~/components/app-detail/micro-app/change-order';

const ChangeOrderStatus = (props) => {
  const { appStatus, regionId, appId, showSlide } = props;

  const { LastChangeOrderRunning, SubStatus, LastChangeOrderId } = appStatus;
  const runningButError = SubStatus === 'RUNNING_BUT_HAS_ERROR';

  const changeOrderUrl = `/${regionId}/app-list/${appId}/micro-app/record/${LastChangeOrderId}`;

  return (
    <>
      <If condition={LastChangeOrderRunning}>
        <Message type={runningButError ? 'error' : 'warning'} className="mb-s">
          {runningButError ?
            <span className="text-red">
              {intl('saenext.basic-info.ChangeOrderStatus.ApplicationPublishingFailedWaitingFor')}
            </span>
            :
            intl('saenext.basic-info.ChangeOrderStatus.TheApplicationHasAChange')
          }
          {showSlide ?
            <SlideWrap
              title={intl('saenext.basic-info.ChangeOrderStatus.ChangeDetails')}
              width={1200}
              openParamKey={'change-order'}
              onMaskClick={noop}
              onClose={noop}
              content={
                <div style={{ marginTop: 50 }}>
                  <ChangeOrderBase
                    regionId={regionId}
                    changeOrderId={LastChangeOrderId}
                    appId={appId}
                  />
                </div>
              }
            >
              <LinkButton>
                {intl('saenext.basic-info.ChangeOrderStatus.ChangeDetails')}
              </LinkButton>
            </SlideWrap>
            :
            <Link to={changeOrderUrl}>
              {intl('saenext.basic-info.ChangeOrderStatus.ChangeDetails')}
            </Link>
          }
        </Message>
      </If>
    </>
  );
};

export default ChangeOrderStatus;
