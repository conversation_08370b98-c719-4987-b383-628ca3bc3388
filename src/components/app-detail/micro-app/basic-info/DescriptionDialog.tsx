import { intl } from '@ali/cnd';
import React from 'react';
import { Dialog, Form, Input, Message } from '@ali/cnd';
import services from '~/services';

const DescriptionDialog = (props) => {
  const { children, appConfig, refresh } = props;

  const { AppId = '', AppDescription = '' } = appConfig;

  const handleChangeDialog = () => {
    let initValue = AppDescription;
    Dialog.show({
      title: intl('saenext.micro-app.basic-info.DescriptionDialog.EditApplicationDescription'),
      content: (
        <Form style={{ width: 400 }}>
          <Form.Item
            label={intl('saenext.micro-app.basic-info.DescriptionDialog.ApplicationDescription')}
          >
            <Input.TextArea
              defaultValue={initValue}
              maxLength={256}
              hasLimitHint
              onChange={(value) => (initValue = value)}
            />
          </Form.Item>
        </Form>
      ),

      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res = await services.UpdateApplicationDescription({
            AppId,
            AppDescription: initValue,
          });
          if (res) {
            Message.success(
              intl('saenext.micro-app.basic-info.DescriptionDialog.EditedSuccessfully'),
            );
            refresh();
            resolve(res);
          } else {
            Message.error(intl('saenext.micro-app.basic-info.DescriptionDialog.FailedToEdit'));
            reject(res);
          }
        });
      },
      onCancel: () => {},
      okProps: { children: intl('saenext.common.dialog.ok') },
      cancelProps: { children: intl('saenext.common.dialog.cancel') },
    });
  };

  return (
    <>
      <span onClick={handleChangeDialog} style={{ width: 'max-content' }}>
        {children}
      </span>
    </>
  );
};

export default DescriptionDialog;
