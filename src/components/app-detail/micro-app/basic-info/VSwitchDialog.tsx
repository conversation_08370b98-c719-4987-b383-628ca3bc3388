import { intl } from '@ali/cnd';
import React, { useEffect, useMemo, useState } from 'react';
import { Dialog, Icon, Message } from '@ali/cnd';
import If from '~/components/shared/If';
import CndTable from '@ali/cnd-table';
import _ from 'lodash';
import services from '~/services';

const VSwitchDialog = (props) => {
  const { children, appConfig, refresh } = props;

  const { RegionId: regionId, AppId = '', VpcId = '', VSwitchId = '' } = appConfig;

  const VSwitchIds = VSwitchId.split(',');

  const [dialogVisible, setVSwitchDialog] = useState(false);
  const [curVSwitchIds, setCurVSwitchIds] = useState(VSwitchIds);
  const [recommendZones, setRecommendZones] = useState(null);
  const [vSwitchList, setVSwitchList] = useState([]);
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    if (!dialogVisible) return;
    setCurVSwitchIds(VSwitchIds);
    getRecommendZones();
  }, [dialogVisible]);

  useEffect(() => {
    setRefreshIndex(refreshIndex + 1);
  }, [recommendZones]);

  const getRecommendZones = async () => {
    const { Regions: { Region = [] } = {} } = (await services.DescribeRegions()) || {};
    const regionMatch = _.find(Region, { RegionId: regionId });
    let recommendZone = _.get(regionMatch, ['RecommendZones', 'RecommendZone']);
    if (regionId === 'cn-shenzhen' && _.includes(recommendZone, 'cn-shenzhen-a')) {
      _.remove(recommendZone, (zone) => zone === 'cn-shenzhen-a');
    }
    recommendZone = _.sortBy(recommendZone, (zone) => _.last(zone));
    setRecommendZones(recommendZone);
  };

  const renderZone = (zone) => {
    const zoneName = _.toUpper(_.last(zone as string));
    return intl('saenext.micro-app.basic-info.VSwitchDialog.ZoneZonename', { zoneName: zoneName });
  };

  const renderZoneName = (vSwitch) => {
    const { ZoneId, VSwitchName } = vSwitch;
    return `${renderZone(ZoneId)} ${VSwitchName}`;
  };

  const isRecommend = (zone) => {
    return _.includes(recommendZones, zone);
  };

  const vSwitchSelected = useMemo(() => {
    const selectedVswitch = _.filter(vSwitchList, (vSwitch) =>
      _.includes(curVSwitchIds, vSwitch.VSwitchId),
    );
    let ipCountSum = 0;
    const selectedZone = _.map(selectedVswitch, (vSwitch) => {
      const { AvailableIpAddressCount } = vSwitch;
      if (AvailableIpAddressCount) {
        ipCountSum += AvailableIpAddressCount;
      }
      return (
        renderZoneName(vSwitch) +
        (isRecommend(vSwitch.ZoneId)
          ? intl('saenext.micro-app.basic-info.VSwitchDialog.Recommend')
          : intl('saenext.micro-app.basic-info.VSwitchDialog.NotRecommend'))
      );
    });

    return {
      selectedZone: selectedZone.join(', '),
      ipCountSum,
    };
  }, [vSwitchList, curVSwitchIds]);

  const filterInValidVSwitch = (vSwitchList) => {
    const selectedVswitchIds = _.filter(VSwitchIds, (vSwitch) =>
      _.find(vSwitchList, { VSwitchId: vSwitch }),
    );
    setCurVSwitchIds(selectedVswitchIds);
  };

  const fetchData = async (params) => {
    if (!dialogVisible || !recommendZones) {
      return {
        data: [],
      };
    }

    const { Data = [] } =
      (await services.ListVSwitches({
        RegionId: regionId,
        VpcId: VpcId,
        Force: true,
        VSwitchId: params.vSwitchId,
        VSwitchName: params.vSwitchName,
      })) || {};
    Data.sort((a, b) => Number(isRecommend(b.ZoneId)) - Number(isRecommend(a.ZoneId)));

    if (!params.vSwitchId) {
      //无筛选时，保存所有可用的vswitch
      setVSwitchList(Data);
      filterInValidVSwitch(Data);
    }
    return {
      data: Data,
      total: Data.length,
    };
  };

  const onSelect = (selectedRowKeys) => {
    if (selectedRowKeys.length > 3) {
      Message.error(intl('saenext.micro-app.basic-info.VSwitchDialog.YouCanSelectUpTo'));
      selectedRowKeys.splice(3);
    }
    setCurVSwitchIds(selectedRowKeys);
  };

  const handleSubmit = async () => {
    if (!curVSwitchIds || !curVSwitchIds.length) {
      Message.error(intl('saenext.micro-app.basic-info.VSwitchDialog.SelectVswitch'));
      return;
    }
    const res = await services.UpdateApplicationVswitches({
      AppId,
      VSwitchId: curVSwitchIds.join(','),
    });
    if (res) {
      Message.success(intl('saenext.micro-app.basic-info.VSwitchDialog.ModifiedSuccessfully'));
      setVSwitchDialog(false);
      refresh();
    } else {
      Message.error(intl('saenext.micro-app.basic-info.VSwitchDialog.FailedToModify'));
    }
  };

  const onClose = () => {
    setRecommendZones(null);
    setVSwitchDialog(false);
  };

  const search = {
    defaultDataIndex: 'vSwitchName',
    defaultSelectedDataIndex: 'vSwitchName',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.micro-app.basic-info.VSwitchDialog.VswitchName'),
        dataIndex: 'vSwitchName',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.micro-app.basic-info.VSwitchDialog.EnterAVswitchName'),
        },
      },
      {
        label: 'vSwitch ID',
        dataIndex: 'vSwitchId',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.micro-app.basic-info.VSwitchDialog.EnterVswitchId'),
        },
      },
    ],
  };

  const columns = [
    {
      key: 'VSwitchName',
      title: intl('saenext.micro-app.basic-info.VSwitchDialog.Name'),
      dataIndex: 'VSwitchName',
      width: 100,
      cell: (value, index, record) => (value ? `${value} (${record.VSwitchId})` : record.VSwitchId),
    },
    {
      key: 'ZoneId',
      title: intl('saenext.micro-app.basic-info.VSwitchDialog.Zone'),
      dataIndex: 'ZoneId',
      width: 160,
      cell: (value) => {
        return (
          <>
            {renderZone(value)}
            <If condition={isRecommend(value)}>
              <Icon
                type="smile"
                size="small"
                style={{ color: '#1DC11D', marginLeft: 10, marginRight: 4 }}
              />
              <span style={{ color: '#1DC11D' }}>
                {intl('saenext.micro-app.basic-info.VSwitchDialog.Recommend.1')}
              </span>
            </If>
            <If condition={value === 'cn-shenzhen-a'}>
              <Icon
                type="smile"
                size="small"
                style={{ color: 'red', marginLeft: 10, marginRight: 4 }}
              />
              <span style={{ color: 'red', marginRight: 4 }}>
                {intl('saenext.micro-app.basic-info.VSwitchDialog.NoRecommendShenzhenZoneA')}
              </span>
            </If>
          </>
        );
      },
    },
    {
      key: 'AvailableIp',
      title: intl('saenext.micro-app.basic-info.VSwitchDialog.AvailableIpAddresses'),
      dataIndex: 'AvailableIpAddressCount',
      width: 100,
    },
  ];

  return (
    <>
      <div onClick={() => setVSwitchDialog(true)} style={{ display:'inline-block' }}>
        {children}
      </div>
      <Dialog
        title={intl('saenext.micro-app.basic-info.VSwitchDialog.MultiVswitchDeployment')}
        visible={dialogVisible}
        onOk={handleSubmit}
        onClose={onClose}
        onCancel={onClose}
      >
        <p style={{ fontWeight: 'bold' }}>
          {vSwitchSelected.selectedZone ||
            intl('saenext.micro-app.basic-info.VSwitchDialog.NotSelected')}
        </p>
        <div className="text-description">
          {intl('saenext.micro-app.basic-info.VSwitchDialog.AvailableIpAddresses.1')}
          {vSwitchSelected?.ipCountSum}
        </div>
        <Message type="notice" style={{ margin: '10px 0' }}>
          <p>
            {intl('saenext.micro-app.basic-info.VSwitchDialog.AfterMultipleVswitchAreConfigured')}
          </p>
          <p>
            <span>
              {intl('saenext.micro-app.basic-info.VSwitchDialog.SelectToVswitchUsingMultiple')}
            </span>
            <span style={{ color: 'green' }}>{_.map(recommendZones, renderZone).join(' ')}</span>
          </p>
        </Message>

        {curVSwitchIds.length === 1 && (
          <Message style={{ margin: '10px 0' }} type="warning">
            {intl('saenext.micro-app.basic-info.VSwitchDialog.CurrentlyOnlyOneSwitchVswitch')}
          </Message>
        )}

        <CndTable
          fetchData={fetchData}
          primaryKey="VSwitchId"
          columns={columns}
          search={search}
          showRefreshButton
          rowSelection={{
            onChange: onSelect,
            selectedRowKeys: curVSwitchIds,
            getProps: (record) => {
              const { VSwitchId, ZoneId } = record;
              let disabled = false;
              if (ZoneId === 'cn-shenzhen-a') {
                if (_.includes(VSwitchIds, VSwitchId)) {
                  disabled = false;
                } else {
                  disabled = true;
                }
              }
              return {
                disabled,
              };
            },
          }}
          refreshIndex={refreshIndex}
          pagination={{
            filterLocal: true,
            pageSizeSelector: false,
            pageSize: 5,
          }}
        />
      </Dialog>
    </>
  );
};

export default VSwitchDialog;
