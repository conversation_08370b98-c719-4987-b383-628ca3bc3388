import { intl } from '@ali/cnd';
import React from 'react';
import MetricSettings from '../MetricSettings';
import { Button, Form, Icon } from '@ali/cnd';
import TimerSettings from '../TimerSettings';
import If from '~/components/shared/If';

const MixSettings = (props) => {
  const { field } = props;

  const name = 'ScalingRuleTimer';

  const MixTimer = field.getValue(name) || [];

  return (
    <>
      <Form.Item
        label={
          <span className="text-bold">
            {intl('saenext.ScalingRuleSlide.MixSettings.MonitoringMetricSettings')}
          </span>
        }
      >
        <MetricSettings field={field} />
      </Form.Item>
      <Form.Item
        className="border-t pt-s"
        label={
          <span className="text-bold">
            {intl('saenext.ScalingRuleSlide.MixSettings.SpecialTimePeriodSettings')}
          </span>
        }
      >
        <div className="next-form-item-help">
          {intl('saenext.ScalingRuleSlide.MixSettings.SetASpecificMaximumAnd')}
        </div>
        {MixTimer.map((item, idx) => {
          const number = idx + 1;
          return (
            <If condition={field.getValue(`${name}.${idx}`)}>
              <>
                <div className="mt">
                  {intl('saenext.ScalingRuleSlide.MixSettings.SelectTimeNumber', {
                    number: number,
                  })}
                  <If condition={idx > 0}>
                    <Button
                      text
                      size="large"
                      onClick={() => {
                        field.deleteArrayValue(name, idx);
                      }}
                    >
                      <Icon type="delete" />
                    </Button>
                  </If>
                </div>
                <TimerSettings field={field} name={`${name}.${idx}`} isMix />
              </>
            </If>
          );
        })}

        <Button
          disabled={MixTimer.length >= 2}
          onClick={() => {
            field.addArrayValue(name, MixTimer.length, {});
          }}
        >
          {intl('saenext.ScalingRuleSlide.MixSettings.AddASpecialTimePeriod')}
        </Button>
      </Form.Item>
    </>
  );
};

export default MixSettings;
