import { intl } from '@ali/cnd';
import { Button, Form, Icon, NumberPicker, TimePicker } from '@ali/cnd';
import React, { useContext } from 'react';
import If from '~/components/shared/If';
import ValueTable from '~/components/shared/ValueTable';
import MicroAppContext from '~/utils/microAppContext';

const MixTimeTable = (props) => {
  const { field, name, value, onChange } = props;

  const { resourceQuota: { InstancesPerApplication: instancesPerApplication } = 50 } =
    useContext(MicroAppContext);

  const disabledStartHours = (hour, idx, record) => {
    const { endTime } = record;
    if (!endTime) return false;
    const minStartTime = new Date(`2000-01-01 ${hour}:00`).getTime();
    const endDateTime = new Date(`2000-01-01 ${endTime}`).getTime();
    return endDateTime - minStartTime <= 5 * 60 * 1000;
  };

  const disabledStartMinutes = (minute, idx, record) => {
    const { startTime, endTime } = record;
    if (!startTime) return true;
    if (!startTime || !endTime) return false;
    const curHour = startTime.split(':')[0];
    const minStartTime = new Date(`2000-01-01 ${curHour}:${minute}`).getTime();
    const endDateTime = new Date(`2000-01-01 ${endTime}`).getTime();
    return endDateTime - minStartTime <= 5 * 60 * 1000;
  };

  const disabledEndHours = (hour, idx, record) => {
    const { startTime } = record;
    if (!startTime) return false;
    const maxEndTime = new Date(`2000-01-01 ${hour}:59`).getTime();
    const startDateTime = new Date(`2000-01-01 ${startTime}`).getTime();
    return maxEndTime - startDateTime <= 5 * 60 * 1000;
  };

  const disabledEndMinutes = (minute, idx, record) => {
    const { startTime, endTime } = record;
    if (!endTime) return true;
    if (!startTime || !endTime) return false;
    const curHour = endTime.split(':')[0];
    const maxEndTime = new Date(`2000-01-01 ${curHour}:${minute}`).getTime();
    const startDateTime = new Date(`2000-01-01 ${startTime}`).getTime();
    return maxEndTime - startDateTime <= 5 * 60 * 1000;
  };

  const replicasVlidator = (rule, value, callback, record) => {
    let { minReplicas, maxReplicas } = record;
    const type = rule.field?.endsWith('minReplicas') ? 'minReplicas' : 'maxReplicas';
    if (type === 'minReplicas') {
      minReplicas = value;
    } else {
      maxReplicas = value;
    }
    if ((minReplicas === 0 && maxReplicas !== 0) || (maxReplicas === 0 && minReplicas !== 0)) {
      callback(
        intl('saenext.ScalingRuleSlide.MixSettings.MixTimeTable.TheMinimumNumberOfApplication'),
      );
    } else {
      callback();
    }
  };

  const columns = ({ onDelete, onItemChange }) => [
    {
      key: 'atTime',
      dataIndex: 'atTime',
      title: intl('saenext.ScalingRuleSlide.MixSettings.MixTimeTable.SpecialTimePeriod'),
      cell: (val, idx, record) => {
        return (
          <div className="flex">
            <Form.Item required requiredMessage=" ">
              <TimePicker
                label={<Icon type="clock" />}
                format="HH:mm"
                style={{ width: '100%' }}
                name={`${name}.${idx}.startTime`}
                disabledHours={(hour) => disabledStartHours(hour, idx, record)}
                disabledMinutes={(minute) => disabledStartMinutes(minute, idx, record)}
                onChange={(val: any) => {
                  onItemChange(idx, { startTime: val?.format?.('HH:mm') || val });
                }}
              />
            </Form.Item>
            <span className="ml-s mr-s">-</span>
            <Form.Item required requiredMessage=" ">
              <TimePicker
                label={<Icon type="clock" />}
                format="HH:mm"
                style={{ width: '100%' }}
                name={`${name}.${idx}.endTime`}
                disabledHours={(hour) => disabledEndHours(hour, idx, record)}
                disabledMinutes={(minute) => disabledEndMinutes(minute, idx, record)}
                onChange={(val: any) => {
                  onItemChange(idx, { endTime: val?.format?.('HH:mm') || val });
                }}
              />
            </Form.Item>
          </div>
        );
      },
    },
    {
      key: 'minReplicas',
      dataIndex: 'minReplicas',
      title: intl(
        'saenext.ScalingRuleSlide.MixSettings.MixTimeTable.MinimumNumberOfApplicationInstances',
      ),
      cell: (val, idx, record) => {
        return (
          <Form.Item
            required
            requiredMessage=" "
            validator={(rule, value, callback) => replicasVlidator(rule, value, callback, record)}
          >
            <NumberPicker
              min={0}
              max={record.maxReplicas ?? instancesPerApplication}
              style={{ width: '100%' }}
              // @ts-ignore
              name={`${name}.${idx}.minReplicas`}
              onChange={(val) => {
                onItemChange(idx, { minReplicas: val });
                setTimeout(() => {
                  field.validate([`${name}.${idx}.maxReplicas`]);
                }, 50);
              }}
            />
          </Form.Item>
        );
      },
    },
    {
      key: 'maxReplicas',
      dataIndex: 'maxReplicas',
      title: intl(
        'saenext.ScalingRuleSlide.MixSettings.MixTimeTable.MaximumNumberOfApplicationInstances',
      ),
      cell: (val, idx, record) => {
        return (
          <Form.Item
            required
            requiredMessage=" "
            validator={(rule, value, callback) => replicasVlidator(rule, value, callback, record)}
          >
            <NumberPicker
              min={record.minReplicas ?? 0}
              max={instancesPerApplication}
              style={{ width: '100%' }}
              // @ts-ignore
              name={`${name}.${idx}.maxReplicas`}
              onChange={(val) => {
                onItemChange(idx, { maxReplicas: val });
                setTimeout(() => {
                  field.validate([`${name}.${idx}.minReplicas`]);
                }, 50);
              }}
            />
          </Form.Item>
        );
      },
    },
    {
      key: 'operation',
      title: intl('saenext.ScalingRuleSlide.MixSettings.MixTimeTable.Operation'),
      cell: (val, idx, record) => {
        return (
          <If condition={value.length > 1}>
            <Button
              text
              onClick={() => {
                onDelete(idx);
              }}
            >
              <Icon type="close" />
            </Button>
          </If>
        );
      },
    },
  ];

  const addRender = (onAdd) => {
    return (
      <Button className="mt-s" type="primary" text iconSize={'small'} onClick={() => onAdd()}>
        <Icon type="plus_fill" />
        <span className="ml-xs">
          {intl('saenext.ScalingRuleSlide.MixSettings.MixTimeTable.AddTriggerTime')}
        </span>
      </Button>
    );
  };

  return (
    <ValueTable
      field={field}
      name={name}
      value={value}
      onChange={onChange}
      maxLength={20}
      columns={columns}
      addRender={addRender}
    />
  );
};

export default MixTimeTable;
