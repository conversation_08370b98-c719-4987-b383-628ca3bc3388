import { intl } from '@ali/cnd';
import React, { forwardRef, useContext, useImperativeHandle } from 'react';
import { Field, Form, Input, Radio, ToolTipCondition } from '@ali/cnd';
import { FORM_ITEM_LAYOUT, SCALE_RULE_TYPE } from './constant';
import If from '~/components/shared/If';
import TimerSettings from './TimerSettings';
import MetricSettings from './MetricSettings';
import { parseRule } from './utils';
import { forEach } from 'lodash';
import MixSettings from './MixSettings';
import MicroAppContext from '~/utils/microAppContext';

const ScalingRuleForm = (props, ref) => {
  const { type, ruleData, setRuleType } = props;
  const { scaleRuleCounts } = useContext(MicroAppContext);

  const field = Field.useField({
    parseName: true,
    values: parseRule(ruleData),
  });

  const { ScalingRuleType = SCALE_RULE_TYPE.TIMING } = field.getValues() as any;

  const isEdit = type === 'update';

  useImperativeHandle(ref, () => ({
    validateForm,
  }));

  const validateForm = async () => {
    const { errors, values } = await field.validatePromise();
    if (errors) {
      console.log('🚀 ~ errors:', errors, values);
      return;
    }
    console.log('🚀 ~ values:', values);
    return values;
  };

  const onRuleTypeChange = (value) => {
    setRuleType(value);
    const errors = field.getErrors();
    forEach(errors, (error, key) => {
      if (key.startsWith('ScalingRuleTimer') || key.startsWith('ScalingRuleMetric')) {
        field.remove(key);
      }
    });
    field.remove(['ScalingRuleTimer', 'ScalingRuleMetric']);

    if (value === SCALE_RULE_TYPE.MIX) {
      field.setValue('ScalingRuleTimer', [{}]);
    }
  };

  const policyList = [
    {
      label: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.TimingPolicy'),
      value: SCALE_RULE_TYPE.TIMING,
      disabled: scaleRuleCounts['timing'] >= 5,
      tip: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.YouCanSetUpTo'),
    },
    {
      label: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.MonitoringMetricPolicy'),
      value: SCALE_RULE_TYPE.METRIC,
      disabled: scaleRuleCounts['metric'] >= 1,
      tip: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.YouCanSetUpTo.1'),
    },
    {
      label: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.HybridElasticStrategy'),
      value: SCALE_RULE_TYPE.MIX,
      disabled: scaleRuleCounts['mix'] >= 1,
      tip: intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.YouCanSetUpTo.2'),
    },
  ];

  return (
    <>
      <Form field={field} {...FORM_ITEM_LAYOUT}>
        <Form.Item
          label={intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.PolicyType')}
          required
        >
          <Radio.Group
            shape="button"
            size="small"
            name="ScalingRuleType"
            defaultValue={SCALE_RULE_TYPE.TIMING}
            onChange={onRuleTypeChange}
            disabled={isEdit}
          >
            {policyList.map((item) => (
              <Radio value={item.value} disabled={item.disabled}>
                <ToolTipCondition show={item.disabled} tip={item.tip} style={{ display: 'block' }}>
                  {item.label}
                </ToolTipCondition>
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label={intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.PolicyName')}
          help={intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.EnterAStringOfTo')}
          required
          requiredMessage={intl(
            'saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.EnterAPolicyName',
          )}
          pattern={/^[a-z]([0-9a-z\-]{0,31})$/}
          patternMessage={intl(
            'saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.EnterAStringOfTo.1',
          )}
        >
          <Input
            name="ScalingRuleName"
            placeholder={intl(
              'saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.EnterAPolicyName',
            )}
            maxLength={64}
            disabled={isEdit}
            style={{ width: 540 }}
          />
        </Form.Item>
        <If condition={ScalingRuleType === SCALE_RULE_TYPE.TIMING}>
          <Form.Item
            label={intl('saenext.basic-info.ScalingRuleSlide.ScalingRuleForm.SelectTime')}
            {...FORM_ITEM_LAYOUT}
          >
            <TimerSettings field={field} name="ScalingRuleTimer" />
          </Form.Item>
        </If>
        <If condition={ScalingRuleType === SCALE_RULE_TYPE.METRIC}>
          <MetricSettings field={field} />
        </If>
        <If condition={ScalingRuleType === SCALE_RULE_TYPE.MIX}>
          <MixSettings field={field} />
        </If>
      </Form>
    </>
  );
};

export default forwardRef(ScalingRuleForm);
