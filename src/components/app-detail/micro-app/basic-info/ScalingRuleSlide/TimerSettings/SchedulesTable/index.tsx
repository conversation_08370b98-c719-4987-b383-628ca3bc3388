import { intl } from '@ali/cnd';
import React, { useContext, useMemo } from 'react';
import { Button, Form, Icon, NumberPicker, TimePicker } from '@ali/cnd';
import ValueTable from '~/components/shared/ValueTable';
import MicroAppContext from '~/utils/microAppContext';
import { forEach, map, uniq } from 'lodash';
import { generate5MinuteTimeRange } from '../../utils';
import If from '~/components/shared/If';

const SchedulesTable = (props) => {
  const { field, name, value, onChange } = props;

  const {
    resourceQuota: { InstancesPerApplication: instancesPerApplication = 50 },
  } = useContext(MicroAppContext);

  const getIndexDisabledTime = (idx) => {
    const selectedTime = map(value, 'atTime');
    const disabledTimeAll = [];
    forEach(selectedTime, (time, index) => {
      if (index === idx) return;
      const disabledTimes = generate5MinuteTimeRange(time);
      disabledTimeAll.push(...disabledTimes);
    });
    return uniq(disabledTimeAll);
  };

  // 相邻时间间隔不能小于5分钟
  const disabledMinutes = (minute, val, idx) => {
    if (!val) return true;
    const curHour = val.split(':')[0];
    const formatMinute = minute.toString().padStart(2, '0');
    const disabledTime = getIndexDisabledTime(idx);
    return disabledTime.includes(`${curHour}:${formatMinute}`);
  };

  const columns = ({ onDelete, onItemChange }) => [
    {
      key: 'atTime',
      dataIndex: 'atTime',
      title: intl('saenext.TimerSettings.SchedulesTable.TriggerTime'),
      cell: (val, idx, record) => {
        return (
          <Form.Item required requiredMessage=" ">
            <TimePicker
              label={<Icon type="clock" />}
              format="HH:mm"
              style={{ width: '100%' }}
              name={`${name}.${idx}.atTime`}
              disabledMinutes={(minute) => disabledMinutes(minute, val, idx)}
              onChange={(val: any) => {
                onItemChange(idx, { atTime: val?.format?.('HH:mm') || val });
              }}
            />
          </Form.Item>
        );
      },
    },
    {
      key: 'targetReplicas',
      dataIndex: 'targetReplicas',
      title: intl('saenext.TimerSettings.SchedulesTable.NumberOfInstancesRetainedAfter'),
      cell: (val, idx, record) => {
        return (
          <Form.Item required requiredMessage=" ">
            <NumberPicker
              min={0}
              max={instancesPerApplication}
              style={{ width: '100%' }}
              // @ts-ignore
              name={`${name}.${idx}.targetReplicas`}
            />
          </Form.Item>
        );
      },
    },
    {
      key: 'operation',
      title: intl('saenext.TimerSettings.SchedulesTable.Operation'),
      cell: (val, idx, record) => {
        return (
          <If condition={value.length > 1}>
            <Button
              text
              onClick={() => {
                onDelete(idx);
              }}
            >
              <Icon type="close" />
            </Button>
          </If>
        );
      },
    },
  ];

  const addRender = (onAdd) => {
    return (
      <Button className="mt-s" type="primary" text iconSize={'small'} onClick={() => onAdd()}>
        <Icon type="plus_fill" />
        <span className="ml-xs">{intl('saenext.TimerSettings.SchedulesTable.AddTriggerTime')}</span>
      </Button>
    );
  };

  return (
    <ValueTable
      field={field}
      name={name}
      value={value}
      onChange={onChange}
      maxLength={20}
      columns={columns}
      addRender={addRender}
    />
  );
};

export default SchedulesTable;
