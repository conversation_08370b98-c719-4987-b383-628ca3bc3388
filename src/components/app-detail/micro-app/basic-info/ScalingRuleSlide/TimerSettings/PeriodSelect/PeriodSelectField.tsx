import { Input } from '@ali/cnd';
import React, { useState } from 'react';
import { getFrequency } from './utils';
import PeriodSelect from '.';
import { EFrequencyType } from './constant';

const PeriodSelectField = (props) => {
  const {
    value,
    onChange,
  } = props;

  const { 
    curFrequency: initCurFrequency, 
    frequencyList: initFrequencyList
  } = getFrequency(value);

  const [curFrequency, setCurFrequency] = useState(initCurFrequency);
  const [frequencyList, setFrequencyList] = useState(initFrequencyList);

  const onPeriodChange = (val) => {
    const {
      curFrequency,
      frequencyList
    } = val;

    if (curFrequency !== EFrequencyType.Day && !frequencyList.length) {
      onChange('');
      return;
    }

    const frequencyListString = frequencyList.join(',');

    switch (curFrequency) {
      case EFrequencyType.Day:
        onChange('* * *')
        break;
      case EFrequencyType.Week:
        onChange(`* * ${frequencyListString}`);
        break;
      case EFrequencyType.Month:
        onChange(`${frequencyListString} * *`);
        break;
      default:
        break;
    }
  }

  return (
    <>
      <PeriodSelect
        value={{
          curFrequency,
          frequencyList
        }}
        onChange={(val) => {
          setCurFrequency(val.curFrequency);
          setFrequencyList(val.frequencyList);
          onPeriodChange(val);
        }}
      />
    </>
  )
}

export default PeriodSelectField;