import { intl } from '@ali/cnd';
import React, { useMemo, useState } from 'react';
import { Form, Select } from '@ali/cnd';
import { EFrequencyType, FrequencyTypeList } from './constant';
import { getFrequency, getWeekDayData } from './utils';
import C from '~/constants/common';
import PeriodItemSelect from './PeriodItemSelect';

const PeriodSelect = (props) => {
  const { value, onChange } = props;

  const { curFrequency, frequencyList } = value;

  const frequencyData = useMemo(() => {
    const result = getWeekDayData(curFrequency);
    return result;
  }, [curFrequency]);

  const handleFrequencyChange = (val) => {
    onChange({
      curFrequency: val,
      frequencyList: [],
    });
  };

  const onFrequencySelectChange = (val) => {
    onChange({
      curFrequency,
      frequencyList: val,
    });
  };

  return (
    <>
      <Form.Item
        label={intl('saenext.TimerSettings.PeriodSelect.Cycle')}
        {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
      >
        <Select
          value={curFrequency}
          dataSource={FrequencyTypeList}
          onChange={handleFrequencyChange}
        />
      </Form.Item>
      {curFrequency !== EFrequencyType.Day && (
        <Form.Item
          label={intl('saenext.TimerSettings.PeriodSelect.SelectTime')}
          {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}
        >
          <PeriodItemSelect
            value={frequencyList}
            onChange={onFrequencySelectChange}
            dataSource={frequencyData}
          />
        </Form.Item>
      )}
    </>
  );
};

export default PeriodSelect;
