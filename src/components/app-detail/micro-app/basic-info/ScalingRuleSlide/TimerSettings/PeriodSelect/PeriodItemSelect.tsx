import { intl } from '@ali/cnd';
import React from 'react';
import { Tag } from '@ali/cnd';
import { Button } from '@alifd/next';

const { Group: TagGroup, Selectable: SelectableTag } = Tag;

const PeriodItemSelect = (props) => {
  const { value = [], onChange, dataSource = [] } = props;

  const handleChange = (val, checked) => {
    const next = checked ? [...value, val] : value.filter((n) => n !== val);

    onChange(next);
  };

  return (
    <>
      <div className="mt-s mb">
        <Button
          text
          type="primary"
          onClick={() => {
            onChange(dataSource.map((item) => item.value));
          }}
          style={{marginRight: 8}}
        >
          {intl('saenext.TimerSettings.PeriodSelect.PeriodItemSelect.SelectAll')}
        </Button>
        <Button
          text
          type="primary"
          className="ml-s"
          onClick={() => {
            onChange([]);
          }}
        >
          {intl('saenext.TimerSettings.PeriodSelect.PeriodItemSelect.AllNotSelected')}
        </Button>
      </div>
      <TagGroup className="grid-container-7" style={{ width: 400 }}>
        {dataSource.map((item, i) => {
          const { value: val, label } = item;
          return (
            <SelectableTag
              key={val}
              style={{
                width: 'max-content',
                minWidth: '50px',
                textAlign: 'center',
                borderRadius: 0,
              }}
              checked={value.includes(val)}
              onChange={(checked) => handleChange(val, checked)}
            >
              {label}
            </SelectableTag>
          );
        })}
      </TagGroup>
    </>
  );
};

export default PeriodItemSelect;
