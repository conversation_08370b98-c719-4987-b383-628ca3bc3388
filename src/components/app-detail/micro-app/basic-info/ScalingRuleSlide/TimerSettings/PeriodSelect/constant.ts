import { intl } from '@ali/cnd';
export enum EFrequencyType {
  Day = 'days',
  Week = 'week',
  Month = 'month',
}

export const FrequencyTypeList = [
  {
    label: intl('saenext.TimerSettings.PeriodSelect.constant.EveryDay'),
    value: EFrequencyType.Day,
  },
  { label: intl('saenext.TimerSettings.PeriodSelect.constant.Weekly'), value: EFrequencyType.Week },
  {
    label: intl('saenext.TimerSettings.PeriodSelect.constant.Monthly'),
    value: EFrequencyType.Month,
  },
];
