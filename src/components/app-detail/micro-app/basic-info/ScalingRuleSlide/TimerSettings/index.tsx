import { intl } from '@ali/cnd';
import React, { useEffect } from 'react';
import DateRangeField from './DateRange/DateRangeField';
import PeriodSelectField from './PeriodSelect/PeriodSelectField';
import { Form } from '@ali/cnd';
import SchedulesTable from './SchedulesTable';
import MixTimeTable from '../MixSettings/MixTimeTable';
import { forEach, some, includes } from 'lodash';

const TimerSettings = (props) => {
  const { field, name, isMix } = props;

  // 相邻时间间隔不能小于5分钟
  const timeValidator = (rule, value, callback) => {
    const invalid = some(value, (item, index) => {
      const { atTime } = item;
      const time1 = new Date(`2000-01-01 ${atTime}`).getTime();
      return some(value, (item2, index2) => {
        if (index === index2) {
          return false;
        }
        const { atTime: atTime2 } = item2;
        const time2 = new Date(`2000-01-01 ${atTime2}`).getTime();
        if (Math.abs(time1 - time2) < 300000) {
          field.setErrors({
            [`${name}.schedules.${index}.atTime`]: '  ',
            [`${name}.schedules.${index2}.atTime`]: '  ',
          });
          return true;
        } else {
          return false;
        }
      });
    });
    if (invalid) {
      callback(intl('saenext.ScalingRuleSlide.TimerSettings.TheIntervalCannotBeLess'));
    } else {
      const errors = field.getErrors();
      forEach(errors, (error, key) => {
        if (key.startsWith(`${name}.schedules`) && error?.includes('  ')) {
          field.setError(key, '');
        }
      });
      callback();
    }
  };

  const mixTimeValidator = (rule, value, callback) => {
    const invalid = some(value, (item, index) => {
      const { startTime, endTime } = item;
      const start1 = new Date(`2000-01-01 ${startTime}`);
      const end1 = new Date(`2000-01-01 ${endTime}`);

      return some(value, (item2, index2) => {
        if (index === index2) {
          return false;
        }
        const { startTime: startTime2, endTime: endTime2 } = item2;
        const start2 = new Date(`2000-01-01 ${startTime2}`);
        const end2 = new Date(`2000-01-01 ${endTime2}`);
        if (
          (start2 < start1 && start1 < end2) ||
          (start2 < end1 && end1 < end2) ||
          (start1 <= start2 && end2 <= end1)
        ) {
          field.setErrors({
            [`${name}.schedules.${index}.startTime`]: '  ',
            [`${name}.schedules.${index}.endTime`]: '  ',
            [`${name}.schedules.${index2}.startTime`]: '  ',
            [`${name}.schedules.${index2}.endTime`]: '  ',
          });
          return true;
        } else {
          return false;
        }
      });
    });

    if (invalid) {
      callback(intl('saenext.ScalingRuleSlide.TimerSettings.SpecialTimePeriodsCannotOverlap'));
    } else {
      const errors = field.getErrors();
      forEach(errors, (error, key) => {
        if (key.startsWith(`${name}.schedules`) && error?.includes('  ')) {
          field.setError(key, '');
        }
      });
      callback();
    }
  };

  return (
    <>
      <DateRangeField field={field} name={name} beginName="beginDate" endName="endDate" />

      <div className="border-radius pd-card mt mb">
        <Form.Item
          required
          requiredMessage={intl('saenext.ScalingRuleSlide.TimerSettings.SelectATimePeriod')}
          autoValidate={false}
        >
          <PeriodSelectField name={`${name}.period`} defaultValue="* * *" />
        </Form.Item>

        {!isMix ? (
          <Form.Item
            label={intl('saenext.ScalingRuleSlide.TimerSettings.TriggerTimeWithinASingle')}
            validator={timeValidator}
            extra={
              <div className="text-description mt">
                {intl('saenext.ScalingRuleSlide.TimerSettings.TheTriggerTimeOfThe')}

                <br />
                {intl('saenext.ScalingRuleSlide.TimerSettings.TheIntervalBetweenTwoAdjacent')}
              </div>
            }
          >
            <SchedulesTable
              field={field}
              defaultValue={[
                { atTime: '08:00', targetReplicas: 10 },
                { atTime: '20:00', targetReplicas: 3 },
              ]}
              name={`${name}.schedules`}
            />
          </Form.Item>
        ) : (
          <Form.Item
            label={intl('saenext.ScalingRuleSlide.TimerSettings.TriggerTimeWithinASingle')}
            validator={mixTimeValidator}
            extra={
              <div className="text-description mt">
                {intl('saenext.ScalingRuleSlide.TimerSettings.EachTimePeriodCannotCoincide')}

                <br />
                {intl('saenext.ScalingRuleSlide.TimerSettings.EachTriggerIntervalMustBe')}

                <br />
                {intl('saenext.ScalingRuleSlide.TimerSettings.AddUpToSpecialTime')}

                <br />
                {intl('saenext.ScalingRuleSlide.TimerSettings.TheMinimumAndMaximumValues')}

                <br />
              </div>
            }
          >
            <MixTimeTable field={field} name={`${name}.schedules`} defaultValue={[{}]} />
          </Form.Item>
        )}
      </div>
    </>
  );
};

export default TimerSettings;
