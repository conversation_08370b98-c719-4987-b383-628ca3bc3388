import React, { useContext } from 'react';
import { intl } from '@ali/cnd';
import { Form, NumberPicker, Switch, Message } from '@ali/cnd';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import { get } from 'lodash';
import FeatureContext from '~/utils/featureContext';
import CachedData from '~/cache/common';

const FORM_ITEM_LAYOUT = {
  labelAlign: 'left' as 'left',
  labelTextAlign: 'left' as 'left',
  labelCol: { fixedSpan: 6 },
  wrapperCol: { span: 20 },
};

const UpDownRules = (props) => {
  // const { field } = props;
  // const { getValue } = field;
  // const { feature } = useContext(FeatureContext);
  // const isAllocateIdle = get(feature, 'idle', false);

  return (
    <>
      <div className="flex justify-between item-start">
        <div>
          <Form.Item
            label={
              <TextWithBalloon
                align="tr"
                text={intl('saenext.MetricSettings.UpDownRules.ElasticExpansionStep')}
                tips={intl('saenext.MetricSettings.UpDownRules.TheMaximumNumberOfInstances')}
              />
            }
            {...FORM_ITEM_LAYOUT}
          >
            <NumberPicker
              //@ts-ignore
              name="ScalingRuleMetric.scaleUpRules.step"
              min={1}
              style={{ width: 152 }}
            />
          </Form.Item>
          <Form.Item
            label={
              <TextWithBalloon
                align="tr"
                text={intl('saenext.MetricSettings.UpDownRules.ExpandTheStableWindow')}
                tips={
                  <>
                    {intl('saenext.MetricSettings.UpDownRules.DuringTheStableWindowPeriod')}

                    <a
                      href="https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/#stabilization-window"
                      target="_blank"
                    >
                      {intl('saenext.MetricSettings.UpDownRules.Details')}
                    </a>
                  </>
                }
              />
            }
            {...FORM_ITEM_LAYOUT}
          >
            <NumberPicker
              // @ts-ignore
              name="ScalingRuleMetric.scaleUpRules.stabilizationWindowSeconds"
              defaultValue={0}
              min={0}
              // @ts-ignore
              addonTextAfter={intl('saenext.MetricSettings.UpDownRules.Seconds')}
              style={{ width: 152 }}
            />
          </Form.Item>
          <Form.Item className="none">
            <Switch name="ScalingRuleMetric.scaleUpRules.disabled" defaultChecked={false} />
          </Form.Item>
          {/* 这个地方应该是预留禁止扩容的按钮，为了页面整齐先寄存在这里，后面开放了禁止扩容按钮，再放到下面 */}
          {/* {isAllocateIdle ? (
            <Form.Item
              label={
                <TextWithBalloon
                  align="tr"
                  text={intl('saenext.MetricSettings.UpDownRules.IdleMode')}
                  tips={
                    <>
                      {intl('saenext.MetricSettings.UpDownRules.WhenIdleModeIsEnabled')}
                      <a
                        href={CachedData.confLink('help:sae:charge-by-volume-2-0')}
                        target="_blank"
                      >
                        {intl('saenext.MetricSettings.UpDownRules.ProductBillingDocument')}
                      </a>
                      。
                    </>
                  }
                />
              }
              size="small"
              {...FORM_ITEM_LAYOUT}
            >
              <Switch name="EnableIdle" defaultChecked={false} />
            </Form.Item>
          ) : null} */}
        </div>
        <div>
          <Form.Item
            label={
              <TextWithBalloon
                align="tr"
                text={intl('saenext.MetricSettings.UpDownRules.ElasticScalingStep')}
                tips={intl('saenext.MetricSettings.UpDownRules.MaximumNumberOfInstancesScaled')}
              />
            }
            {...FORM_ITEM_LAYOUT}
          >
            <NumberPicker
              // @ts-ignore
              name="ScalingRuleMetric.scaleDownRules.step"
              min={1}
              style={{ width: 152 }}
            />
          </Form.Item>
          <Form.Item
            label={
              <TextWithBalloon
                align="tr"
                text={intl('saenext.MetricSettings.UpDownRules.ShrinkStableWindow')}
                tips={
                  <>
                    {intl('saenext.MetricSettings.UpDownRules.DuringTheStableWindowPeriod.1')}

                    <a
                      href="https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/#stabilization-window"
                      target="_blank"
                    >
                      {intl('saenext.MetricSettings.UpDownRules.Details')}
                    </a>
                  </>
                }
              />
            }
            {...FORM_ITEM_LAYOUT}
          >
            <NumberPicker
              // @ts-ignore
              name="ScalingRuleMetric.scaleDownRules.stabilizationWindowSeconds"
              defaultValue={300}
              min={0}
              // @ts-ignore
              addonTextAfter={intl('saenext.MetricSettings.UpDownRules.Seconds')}
              style={{ width: 152 }}
            />
          </Form.Item>
        </div>
      </div>
      <Form.Item
        label={
          <TextWithBalloon
            align="tr"
            text={intl('saenext.MetricSettings.UpDownRules.NoShrinkage')}
            tips={intl(
              'saenext.MetricSettings.UpDownRules.AfterConfigurationTheApplicationInstance',
            )}
          />
        }
        size="small"
        {...FORM_ITEM_LAYOUT}
      >
        <Switch name="ScalingRuleMetric.scaleDownRules.disabled" defaultChecked={false} />
      </Form.Item>
      {/* {isAllocateIdle && getValue('EnableIdle') ? (
        <Message type="warning">
          <div className="text-line">
            {intl('saenext.MetricSettings.UpDownRules.IdleModeDoesNotTake')}
          </div>
          <div className="text-line">
            {intl('saenext.MetricSettings.UpDownRules.AfterTheIdleModeIs')}
          </div>
        </Message>
      ) : null} */}
    </>
  );
};

export default UpDownRules;
