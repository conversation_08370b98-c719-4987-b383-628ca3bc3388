import { intl } from '@ali/cnd';
import React, { useContext } from 'react';
import { Form, NumberPicker } from '@ali/cnd';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import MinReadyInstanceField from '~/components/shared/MinReadyInstance/MinReadyInstanceField';
import MicroAppContext from '~/utils/microAppContext';

const FORM_ITEM_LAYOUT = {
  labelAlign: 'left' as 'left',
  labelTextAlign: 'left' as 'left',
  labelCol: {
    fixedSpan: 6,
  },
  wrapperCol: {
    span: 20,
  },
};

const ReplicasSettings = (props) => {
  const { field } = props;

  const { resourceQuota: { InstancesPerApplication: instancesPerApplication } = 50 } =
    useContext(MicroAppContext);

  const { getValue, getValues } = field;

  const { ScalingRuleMetric: { minReplicas, maxReplicas } = {} as any } = getValues();

  const minReplicasValidator = (rule, value, callback) => {
    const MinReadyInstances = getValue('MinReadyInstances');
    if (MinReadyInstances > value) {
      callback(intl('saenext.MetricSettings.ReplicasSettings.TheMinimumNumberOfApplication'));
      return;
    }
    callback();
  };

  return (
    <>
      <div className="flex justify-between item-start">
        <Form.Item
          label={intl(
            'saenext.MetricSettings.ReplicasSettings.MinimumNumberOfApplicationInstances',
          )}
          required
          requiredMessage=" "
          validator={minReplicasValidator}
          style={{ maxWidth: '50%' }}
          {...FORM_ITEM_LAYOUT}
        >
          <NumberPicker
            // @ts-ignore
            name="ScalingRuleMetric.minReplicas"
            defaultValue={2}
            min={1}
            max={maxReplicas - 1}
            style={{ width: 152 }}
          />
        </Form.Item>
        <Form.Item
          label={intl(
            'saenext.MetricSettings.ReplicasSettings.MaximumNumberOfApplicationInstances',
          )}
          required
          requiredMessage=" "
          {...FORM_ITEM_LAYOUT}
        >
          <NumberPicker
            // @ts-ignore
            name="ScalingRuleMetric.maxReplicas"
            defaultValue={instancesPerApplication}
            min={minReplicas + 1}
            max={instancesPerApplication}
            style={{ width: 152 }}
          />
        </Form.Item>
      </div>
      <Form.Item
        label={
          <TextWithBalloon
            text={intl('saenext.MetricSettings.ReplicasSettings.MinimumNumberOfSurvivingInstances')}
            tips={intl('saenext.MetricSettings.ReplicasSettings.TheMinimumNumberOfInstances')}
          />
        }
        {...FORM_ITEM_LAYOUT}
      >
        <MinReadyInstanceField field={field} />
      </Form.Item>
    </>
  );
};

export default ReplicasSettings;
