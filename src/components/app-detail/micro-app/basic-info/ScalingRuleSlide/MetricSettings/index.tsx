import { intl } from '@ali/cnd';
import React, { useContext } from 'react';
import { Form, Radio, Balloon, Icon } from '@ali/cnd';
import FeatureContext from '~/utils/featureContext';
import MicroAppContext from '~/utils/microAppContext';
import { FORM_ITEM_LAYOUT } from '../constant';
import MetricsTable from './MetricsTable';
import ReplicasSettings from './ReplicasSettings';
import SimpleCollapse from '~/components/shared/SimpleCollapse';
import UpDownRules from './UpDownRules';
import CachedData from '~/cache/common';
import PrometheusSettings from '../../ScaleRuleSlide/MetricSettings/PrometheusSettings';

const RadioGroup = Radio.Group;

const MetricSettings = (props) => {
  const { field } = props;
  const { feature } = useContext(FeatureContext);
  const { appConfig } = useContext(MicroAppContext);
  const enablePrometheusMetricsScaling = feature?.enablePrometheusMetricsScaling || false;

  return (
    <>
      <Form.Item label=" " {...FORM_ITEM_LAYOUT}>
        <a
          href={`${CachedData.confLink('help:sae:sae-auto-scaling-best-practices')}#h3-url-1`}
          target="_blank"
        >
          {intl('saenext.ScalingRuleSlide.MetricSettings.HowToConfigureElasticMetrics')}
        </a>
      </Form.Item>
      {enablePrometheusMetricsScaling && (
        <Form.Item
          label={
            <>
              <span className="mr-s">
                {intl('saenext.ScalingRuleSlide.MetricSettings.IndicatorSource')}
              </span>
              <Balloon align="t" trigger={<Icon size="xs" type="help" />} closable={false}>
                <div>
                  <div>
                    {intl('saenext.ScalingRuleSlide.MetricSettings.SystemIndexRefersToThe')}
                  </div>
                  {appConfig?.NewSaeVersion !== 'lite' && (
                    <div>
                      {intl(
                        'saenext.ScalingRuleSlide.MetricSettings.PrometheusIndicatorRefersToPrometheus',
                      )}
                    </div>
                  )}
                </div>
              </Balloon>
            </>
          }
          {...FORM_ITEM_LAYOUT}
        >
          <RadioGroup
            name="ScalingRuleMetric.metricSource"
            defaultValue="system"
            onChange={(v: string) => {
              field.setValue('ScalingRuleMetric.metricSource', v);
            }}
          >
            <Radio value="system">
              {intl('saenext.ScalingRuleSlide.MetricSettings.SystemMetrics')}
            </Radio>
            {appConfig?.NewSaeVersion !== 'lite' && (
              <Radio value="prometheus">
                {intl('saenext.ScalingRuleSlide.MetricSettings.PrometheusIndicators')}
              </Radio>
            )}
          </RadioGroup>
        </Form.Item>
      )}
      {(field.getValue('ScalingRuleMetric.metricSource') || 'system') === 'system' && (
        <Form.Item
          label={intl('saenext.ScalingRuleSlide.MetricSettings.TriggerCondition')}
          help={
            <>
              <div>{intl('saenext.ScalingRuleSlide.MetricSettings.IfTheTriggerConditionIs')}</div>
              <div>
                {intl('saenext.ScalingRuleSlide.MetricSettings.WhenTheActualValueOf')}

                <a
                  href={CachedData.confLink('help:sae:configure-an-auto-scaling-policy')}
                  target="_blank"
                >
                  {intl('saenext.ScalingRuleSlide.MetricSettings.ViewDetails')}
                </a>
              </div>
            </>
          }
          {...FORM_ITEM_LAYOUT}
        >
          <MetricsTable
            field={field}
            name="ScalingRuleMetric.metrics"
            defaultValue={[
              {
                metricType: 'CPU',
              },
              {
                metricType: 'MEMORY',
              },
            ]}
          />
        </Form.Item>
      )}
      {field.getValue('ScalingRuleMetric.metricSource') === 'prometheus' && (
        <Form.Item>
          <PrometheusSettings
            field={field}
            FORM_ITEM_LAYOUT={FORM_ITEM_LAYOUT}
            fromPage="old-scale-rule"
          />
        </Form.Item>
      )}
      <Form.Item
        label={intl('saenext.ScalingRuleSlide.MetricSettings.NumberOfInstances')}
        {...FORM_ITEM_LAYOUT}
      >
        <ReplicasSettings field={field} />
      </Form.Item>
      <SimpleCollapse
        text={intl('saenext.ScalingRuleSlide.MetricSettings.AdvancedSettings')}
        defaultOpen={false}
        lazyLoad={false}
      >
        <div className="pd-card">
          <UpDownRules field={field} />
        </div>
      </SimpleCollapse>
    </>
  );
};

export default MetricSettings;
