import { intl } from '@ali/cnd';
import { Button, ConsoleContext, Form, Icon, NumberPicker, Select } from '@ali/cnd';
import { get, isNil, map, includes } from 'lodash';
import React, { useContext, useMemo } from 'react';
import If from '~/components/shared/If';
import ValueTable from '~/components/shared/ValueTable';
import { EMetricType, MetricType, NetTypeLabel, SlbNetType } from './constant';
import * as services from '~/services';
import MicroAppContext from '~/utils/microAppContext';
import CachedData from '~/cache/common';

const MetricsTable = (props) => {
  const { field, name, value, onChange } = props;

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const { appConfig, slbQpsRt, slbQpsRtPermission } = useContext(MicroAppContext);

  const { AppId } = appConfig;

  const metricTypeList = useMemo(() => {
    const metricTypeSelected = map(value, 'metricType');
    const data = map(MetricType, (item) => ({
      label: item.label,
      value: item.value,
      disabled: metricTypeSelected.includes(item.value),
    }));
    return data;
  }, [value]);

  const metricTypeValidator = async (rule, value, callback) => {
    const validatorRules = {
      [EMetricType.QPS]: qpsRtValidator,
      [EMetricType.RT]: qpsRtValidator,
      [EMetricType.SLB_QPS]: clbQpsRtValidator,
      [EMetricType.SLB_RT]: clbQpsRtValidator,
      [EMetricType.INTRANET_SLB_QPS]: clbQpsRtValidator,
      [EMetricType.INTRANET_SLB_RT]: clbQpsRtValidator,
    };
    (await validatorRules[value]?.(rule, value, callback)) || callback();
  };

  const qpsRtValidator = async (rule, value, callback) => {
    const data = await services.describeApplicationScalingRuleStatus({
      params: {
        AppId,
      },
    });

    const MonitorStatusStr = JSON.stringify(get(data, ['Data', 'MonitorStatus'], {}));

    const typeMap = {
      [EMetricType.QPS]: 'Qps',
      [EMetricType.RT]: 'Rt',
    };

    if (!MonitorStatusStr.includes(typeMap[value])) {
      callback(intl('saenext.MetricSettings.MetricsTable.TheApplicationQpsRtHas'));
    } else {
      callback();
    }
  };

  const clbQpsRtValidator = (rule, value, callback) => {
    const networkType = SlbNetType[value];
    const networkTypeLabel = NetTypeLabel[networkType];
    const ports = slbQpsRt[`${networkType}Ports`];
    const logsDownloadAttribute = slbQpsRt[`${networkType}LogsDownloadAttribute`];

    if (ports) {
      if (logsDownloadAttribute) {
        callback();
      } else {
        if (slbQpsRtPermission) {
          callback(
            <>
              {intl('saenext.MetricSettings.MetricsTable.YouHaveNotActivated')}
              {networkTypeLabel}
              {intl('saenext.MetricSettings.MetricsTable.TheAccessLogOfClb')}
              {networkTypeLabel}
              {intl('saenext.MetricSettings.MetricsTable.ClbQpsResponseTimeTo')}
              <a href={CachedData.confLink('help:slb:overview-of-the-access-log-feature')} target="_blank">
                {intl('saenext.MetricSettings.MetricsTable.ReferenceDocumentation')}
              </a>
              {intl('saenext.MetricSettings.MetricsTable.To')}

              <a
                href={`${CachedData.confLink('feature:slb:url')}/slb/${regionId}/access-log`}
                target="_blank"
              >
                {intl('saenext.MetricSettings.MetricsTable.ClbConsole')}
              </a>
              {intl('saenext.MetricSettings.MetricsTable.Activate')}
            </>,
          );
        } else {
          callback(intl('saenext.MetricSettings.MetricsTable.TheCurrentUserLacksThe'));
        }
      }
    } else {
      callback(
        intl('saenext.MetricSettings.MetricsTable.YourAccountHasNotPurchased', {
          networkTypeLabel: networkTypeLabel,
        }),
      );
    }
  };

  const onClbMetric = (val, idx, onItemChange) => {
    const networkType = SlbNetType[val];
    const ports = slbQpsRt[`${networkType}Ports`];
    const logsDownloadAttribute = slbQpsRt[`${networkType}LogsDownloadAttribute`];
    const {
      LogProject: slbProject,
      LogStore: slbLogstore,
      LoadBalancerId: slbId,
    } = logsDownloadAttribute || {};
    onItemChange(idx, {
      slbProject,
      slbLogstore,
      slbId,
      vport: ports,
    });
  };

  const columns = ({ onDelete, onItemChange }) => [
    {
      key: 'index',
      width: 20,
      cell: (val, idx, record) => (
        <If condition={idx !== 0}>{intl('saenext.MetricSettings.MetricsTable.Or')}</If>
      ),
    },
    {
      title: intl('saenext.MetricSettings.MetricsTable.IndicatorType'),
      dataIndex: 'metricType',
      width: 150,
      cell: (val, idx, record) => (
        <Form.Item required requiredMessage=" " validator={metricTypeValidator}>
          <Select
            style={{ width: '100%' }}
            dataSource={metricTypeList}
            name={`${name}.${idx}.metricType`}
            onChange={(val: string) => {
              onItemChange(idx, {
                metricTargetAverageUtilization: '',
                slbProject: undefined,
                slbLogstore: undefined,
                slbId: undefined,
                vport: undefined,
              });
              if (val.includes('SLB')) {
                onClbMetric(val, idx, onItemChange);
              }
            }}
            placeholder={intl('saenext.MetricSettings.MetricsTable.SelectAMetricType')}
          />
        </Form.Item>
      ),
    },
    {
      title: intl('saenext.MetricSettings.MetricsTable.IndexAggregationMethod'),
      dataIndex: 'desc',
      width: 160,
      cell: (val, idx, record) => MetricType[record.metricType]?.desc,
    },
    {
      dataIndex: 'equal',
      width: 10,
      cell: (val, idx, record) => '=',
    },
    {
      title: intl('saenext.MetricSettings.MetricsTable.TargetValue'),
      dataIndex: 'metricTargetAverageUtilization',
      width: 160,
      cell: (val, idx, record) => {
        const isCpuOrMem = record.metricType === 'CPU' || record.metricType === 'MEMORY';
        const isRt = record.metricType === EMetricType.RT;
        return (
          <Form.Item required requiredMessage=" ">
            <NumberPicker
              style={{ width: '100%', display: isRt ? 'none' : 'block' }}
              min={0}
              max={isCpuOrMem ? 90 : undefined}
              innerAfter={MetricType[record.metricType]?.unit}
              // @ts-ignore
              name={`${name}.${idx}.metricTargetAverageUtilization`}
              disabled={!record?.metricType || field.getError(`${name}.${idx}.metricType`)}
            />

            <If condition={isRt}>
              <NumberPicker
                style={{ width: '100%' }}
                min={0}
                innerAfter={MetricType[record.metricType]?.unit}
                value={val / 1000 || ''}
                onChange={(val) => {
                  onItemChange(idx, {
                    metricTargetAverageUtilization: Number(val) * 1000 || '',
                  });
                }}
              />
            </If>
          </Form.Item>
        );
      },
    },
    {
      key: 'operation',
      title: intl('saenext.MetricSettings.MetricsTable.Operation'),
      width: 80,
      cell: (val, idx, record) => {
        return (
          <If condition={value.length > 1}>
            <Button
              text
              className="scale-medium"
              onClick={() => {
                onDelete(idx);
              }}
            >
              <Icon type="delete" />
            </Button>
          </If>
        );
      },
    },
  ];

  return (
    <ValueTable
      field={field}
      name={name}
      value={value}
      onChange={onChange}
      maxLength={20}
      columns={columns}
    />
  );
};

export default MetricsTable;
