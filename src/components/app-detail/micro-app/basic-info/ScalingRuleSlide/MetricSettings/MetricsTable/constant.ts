import { intl } from '@ali/cnd';
export enum EMetricType {
  CPU = 'CPU',
  MEMORY = 'MEMORY',
  tcpActiveConn = 'tcpActiveConn',
  tcpTotalConn = 'tcpTotalConn',
  QPS = 'QPS',
  RT = 'RT',
  SLB_QPS = 'SLB_QPS',
  SLB_RT = 'SLB_RT',
  INTRANET_SLB_QPS = 'INTRANET_SLB_QPS',
  INTRANET_SLB_RT = 'INTRANET_SLB_RT',
  Prometheus = 'Prometheus',
}

export const MetricType = {
  CPU: {
    value: 'CPU',
    label: intl('saenext.MetricSettings.MetricsTable.constant.CpuUsage'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageCpuUtilizationOfAll'),
    unit: '%',
  },
  MEMORY: {
    value: 'MEMORY',
    label: intl('saenext.MetricSettings.MetricsTable.constant.MemUsage'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageMemUtilizationOfAll'),
    unit: '%',
  },
  tcpActiveConn: {
    value: 'tcpActiveConn',
    label: intl('saenext.MetricSettings.MetricsTable.constant.TcpActiveConnections'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageTcpActiveConnectionsFor'),
    unit: intl('saenext.MetricSettings.MetricsTable.constant.Sec'),
  },
  tcpTotalConn: {
    value: 'tcpTotalConn',
    label: intl('saenext.MetricSettings.MetricsTable.constant.TotalTcpConnections'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageTotalTcpConnectionsFor'),
    unit: intl('saenext.MetricSettings.MetricsTable.constant.Sec'),
  },
  QPS: {
    value: 'QPS',
    label: intl('saenext.MetricSettings.MetricsTable.constant.ApplicationQps'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageQpsOfASingle'),
    unit: '/sec',
  },
  RT: {
    value: 'RT',
    label: intl('saenext.MetricSettings.MetricsTable.constant.ApplicationResponseTimeRt'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageValueOfRtFor'),
    unit: 'ms',
  },
  SLB_QPS: {
    value: 'SLB_QPS',
    label: intl('saenext.MetricSettings.MetricsTable.constant.InternetClbQps'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageQpsOfPublicClb'),
    unit: '/sec',
  },
  SLB_RT: {
    value: 'SLB_RT',
    label: intl('saenext.MetricSettings.MetricsTable.constant.PublicClbResponseTime'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AveragePublicClbRtWithin'),
    unit: 'ms',
  },
  INTRANET_SLB_QPS: {
    value: 'INTRANET_SLB_QPS',
    label: intl('saenext.MetricSettings.MetricsTable.constant.PrivateNetworkClbQps'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageQpsOfPrivateClb'),
    unit: '/sec',
  },
  INTRANET_SLB_RT: {
    value: 'INTRANET_SLB_RT',
    label: intl('saenext.MetricSettings.MetricsTable.constant.PrivateClbResponseTime'),
    desc: intl('saenext.MetricSettings.MetricsTable.constant.AverageRtOfPrivateClb'),
    unit: 'ms',
  },
};

export const MetricStatusType = {
  [EMetricType.CPU]: 'cpu',
  [EMetricType.MEMORY]: 'memory',
  [EMetricType.tcpActiveConn]: 'tcpActiveConn',
  [EMetricType.tcpTotalConn]: 'tcpTotalConn',
  [EMetricType.QPS]: ['arms_incall_qps', 'arms_incall_qps_v2'],
  [EMetricType.RT]: 'arms_incall_rt',
  [EMetricType.SLB_QPS]: 'slb_incall_qps',
  [EMetricType.SLB_RT]: 'slb_incall_rt',
  [EMetricType.INTRANET_SLB_QPS]: 'intranet_slb_incall_qps',
  [EMetricType.INTRANET_SLB_RT]: 'intranet_slb_incall_rt',
};

export enum NetType {
  internet = 'internet',
  intranet = 'intranet',
}

export const NetTypeLabel = {
  [NetType.internet]: intl('saenext.MetricSettings.MetricsTable.constant.PublicNetwork'),
  [NetType.intranet]: intl('saenext.MetricSettings.MetricsTable.constant.PrivateNetwork'),
};

export const SlbNetType = {
  [EMetricType.SLB_QPS]: NetType.internet,
  [EMetricType.SLB_RT]: NetType.internet,
  [EMetricType.INTRANET_SLB_QPS]: NetType.intranet,
  [EMetricType.INTRANET_SLB_RT]: NetType.intranet,
};
