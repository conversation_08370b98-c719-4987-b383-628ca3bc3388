import React, { useMemo } from 'react';
import { Wline } from'@alife/aisc-widgets';
import { getFrequency } from '../TimerSettings/PeriodSelect/utils';
import { EFrequencyType } from '../TimerSettings/PeriodSelect/constant';
import { SCALE_RULE_TYPE } from '../constant';
import { formatMixTimerSchedules } from '../utils';
import { DaysMap, formatSchedule, TypeLines } from './constant';
import moment from 'moment';
import { sortBy } from 'lodash';

const TimerChart = (props) => {
  const {
    type,
    rule,
    ruleMetric,
  } = props;

  const {
    period,
    schedules,
  } = rule;

  const { curFrequency, frequencyList } = getFrequency(period);

  const formatDate = (time, baseDate) => {
    const date = baseDate.format("YYYY-MM-DD");
    return `${date} ${time}`
  }

  const dataSource = useMemo(() => {

    const typeLines = TypeLines[type]; 
    
    const timeSchedules = type === SCALE_RULE_TYPE.MIX ? formatMixTimerSchedules(schedules, ruleMetric, true) : sortBy(schedules, 'atTime');

    const res = typeLines.map(item => {
      const data = [];
  
      // 根据频率类型获取日期
      const getFrequencyDate = (freq) => {
        switch (curFrequency) {
          case EFrequencyType.Day:
            return moment();
          case EFrequencyType.Week:
            return moment().weekday(DaysMap[freq]);
          case EFrequencyType.Month:
            return moment().date(freq);
        }
      };
  
      frequencyList.forEach(freq => {
        const date = getFrequencyDate(freq);
        timeSchedules.forEach(schedule => {
          data.push([
            formatDate(schedule[item.xKey], date),
            schedule[item.yKey],
          ]);
        });
      });
  
      // 增加下一周期起始数据，和周期结尾保持连续
      const nextPeriodData = [
        moment(data[0][0]).add(1, curFrequency).format("YYYY-MM-DD HH:mm"),
        data[0][1],
      ];
      data.push(nextPeriodData);
  
      return {
        name: item.label,
        data,
      };
    });
  
    return res;
  }, [rule])

  const options = {
    step: true,
    zoom: curFrequency === EFrequencyType.Month,
    xAxis: {
      mask: formatSchedule[curFrequency],
    },
    yAxis: {
      min: 0,
      tickInterval: 1,
    },
  };
  
  return (
    <Wline height="300" config={options} data={dataSource}/>
  )
}

export default TimerChart;