import { intl } from '@ali/cnd';
import React from 'react';
import { getFrequency } from '../TimerSettings/PeriodSelect/utils';
import { find } from 'lodash';
import { FrequencyTypeList } from '../TimerSettings/PeriodSelect/constant';
import { DataFields } from '@ali/cnd';

const TimePeroid = (props) => {
  const { rule } = props;

  const { beginDate, endDate, period } = rule;

  const dateRangeShow =
    !beginDate && !endDate
      ? intl('saenext.ScalingRuleSlide.TimerPreview.TimePeroid.LongTerm')
      : `${beginDate} - ${endDate}`;
  const { curFrequency } = getFrequency(period);
  const { label: periodLable } = find(FrequencyTypeList, { value: curFrequency });

  return (
    <>
      <DataFields
        dataSource={{
          dateRangeShow,
          periodLable,
        }}
        items={[
          {
            label: intl('saenext.ScalingRuleSlide.TimerPreview.TimePeroid.TimePeriod'),
            dataIndex: 'dateRangeShow',
            labelLayout: { fixedSpan: 4 },
            span: 8,
          },
          {
            label: intl('saenext.ScalingRuleSlide.TimerPreview.TimePeroid.RepeatPeriod'),
            dataIndex: 'periodLable',
            labelLayout: { fixedSpan: 4 },
            span: 8,
          },
        ]}
      />

      <div style={{ textAlign: 'center', margin: '20px 0' }}>
        {intl('saenext.ScalingRuleSlide.TimerPreview.TimePeroid.PeriodlableChangesInTheNumber', {
          periodLable: periodLable,
        })}
      </div>
    </>
  );
};

export default TimePeroid;
