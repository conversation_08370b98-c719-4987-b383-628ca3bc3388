import { intl } from '@ali/cnd';
import { SCALE_RULE_TYPE } from '../constant';
import { EFrequencyType } from '../TimerSettings/PeriodSelect/constant';

export const TypeLines = {
  [SCALE_RULE_TYPE.TIMING]: [
    {
      type: 'replicas',
      label: intl('saenext.ScalingRuleSlide.TimerPreview.constant.NumberOfInstances'),
      xKey: 'atTime',
      yKey: 'targetReplicas',
    },
  ],

  [SCALE_RULE_TYPE.MIX]: [
    {
      type: 'minReplicas',
      label: intl('saenext.ScalingRuleSlide.TimerPreview.constant.MinimumNumberOfInstances'),
      xKey: 'atTime',
      yKey: 'minReplicas',
    },
    {
      type: 'maxReplicas',
      label: intl('saenext.ScalingRuleSlide.TimerPreview.constant.MaximumNumberOfInstances'),
      xKey: 'atTime',
      yKey: 'maxReplicas',
    },
  ],
};

export const DaysMap = {
  Mon: 0,
  Tue: 1,
  Wed: 2,
  Thu: 3,
  Fri: 4,
  Sat: 5,
  Sun: 6,
};

export const formatSchedule = {
  [EFrequencyType.Day]: 'HH:mm',
  [EFrequencyType.Week]: 'ddd HH:mm',
  [EFrequencyType.Month]: 'DD HH:mm',
};
