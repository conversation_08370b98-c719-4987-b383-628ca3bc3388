import React from 'react';
import TimerChart from './TimerChart';
import TimePeroid from './TimePeroid';

const TimerPreview = (props) => {
  const {
    type,
    ruleTimer = [],
    ruleMetric = {}
  } = props;

  return (
    <>
      {
        ruleTimer.map((rule) => (
          <div className='mt-xl mb-xl'>
            <TimePeroid
              rule={rule}
            />
            <TimerChart
              type={type}
              rule={rule}
              ruleMetric={ruleMetric}
            />
          </div>
        ))
      }
    </>
  )
}

export default TimerPreview;