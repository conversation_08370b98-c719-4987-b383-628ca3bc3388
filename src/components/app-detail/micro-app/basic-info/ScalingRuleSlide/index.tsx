import { intl } from '@ali/cnd';
import React, { useEffect, useRef, useState } from 'react';
import { Button, Message, SlidePanel, Step } from '@ali/cnd';
import If from '~/components/shared/If';
import { SCALE_RULE_TYPE, SLIDE_TITLE } from './constant';
import ScalingRuleForm from './ScalingRuleForm';
import ComShow from '~/components/shared/ComShow';
import * as services from '~/services';
import { formatMixTimerSchedules } from './utils';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import { get, isArray, isEmpty, some, union } from 'lodash';
import TimerPreview from './TimerPreview';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/cache/common';

const ScalingRuleSlide = (props) => {
  const { appId, refresh, type, ruleData, children, handleRefreshScalingRules } = props;

  const isEdit = type === 'update';

  const [ruleType, setRuleType] = useState(SCALE_RULE_TYPE.TIMING);
  const [slideVisible, setSlideVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [ruleTimer, setRuleTimer] = useState([]);
  const [ruleMetric, setRuleMetric] = useState({});

  const ruleFormRef = useRef<any>(null);

  useEffect(() => {
    if (!slideVisible) {
      setCurrentStep(0);
      setSubmitLoading(false);
      return;
    }

    const { ScaleRuleType = SCALE_RULE_TYPE.TIMING } = ruleData || {};
    setRuleType(ScaleRuleType);

    if (type === 'preview') {
      onPreviewRule();
    }
  }, [slideVisible]);

  const onPreviewRule = async () => {
    const values = await ruleFormRef.current.validateForm();
    const { ScalingRuleType, ScalingRuleTimer, ScalingRuleMetric } = values;

    if (ScalingRuleType === SCALE_RULE_TYPE.TIMING) {
      setRuleTimer([ScalingRuleTimer]);
    } else if (ScalingRuleType === SCALE_RULE_TYPE.MIX) {
      setRuleTimer(ScalingRuleTimer);
    }
    setRuleMetric(ScalingRuleMetric);

    setCurrentStep(1);
  };

  const onSubmit = async () => {
    setSubmitLoading(true);

    const values = await ruleFormRef.current.validateForm();

    if (!values) {
      setSubmitLoading(false);
      return;
    }

    if (values?.ScalingRuleMetric?.metricSource === 'prometheus') {
      delete values?.ScalingRuleMetric?.authMode;
      // delete values?.ScalingRuleMetric?.prometheusType;
    }

    const { ScalingRuleTimer, ScalingRuleName, ...otherValues } = values;

    let res;
    if (values.ScalingRuleType === SCALE_RULE_TYPE.MIX && ScalingRuleTimer.length) {
      const rules = ScalingRuleTimer.map((timer, index) => ({
        ScalingRuleTimer: {
          ...timer,
          schedules: formatMixTimerSchedules(timer.schedules, values.ScalingRuleMetric),
        },
        ScalingRuleName: index > 0 ? `${ScalingRuleName}#${index}` : ScalingRuleName,
        ...otherValues,
      }));

      const oldScaleRuleNames = get(ruleData, 'ScaleRuleNames', []);
      const newScaleRuleNames = rules.map((rule) => rule.ScalingRuleName);

      const mergeNames = union(oldScaleRuleNames, newScaleRuleNames);

      res = await Promise.all(
        mergeNames.map((name) => {
          const ruleParams = rules.find((rule) => rule.ScalingRuleName === name);
          if (!oldScaleRuleNames.includes(name) && newScaleRuleNames.includes(name)) {
            return formRequest(ruleParams, 'create');
          }
          if (oldScaleRuleNames.includes(name) && newScaleRuleNames.includes(name)) {
            return formRequest(ruleParams, 'update');
          }
          if (oldScaleRuleNames.includes(name) && !newScaleRuleNames.includes(name)) {
            return formRequest(
              {
                ScalingRuleName: name,
              },
              'delete',
            );
          }
        }),
      );
    } else {
      res = await formRequest(values);
    }
    setSubmitLoading(false);

    if (res && !(isArray(res) && some(res, isEmpty))) {
      Message.success(
        isEdit
          ? intl('saenext.basic-info.ScalingRuleSlide.EditedSuccessfully')
          : intl('saenext.basic-info.ScalingRuleSlide.CreatedSuccessfully'),
      );
      setSlideVisible(false);
      setCurrentStep(0);
      refresh?.();
      handleRefreshScalingRules && handleRefreshScalingRules();
    }
  };

  const formRequest = async (values: any, action?) => {
    const actionMap = {
      create: services.createApplicationScalingRule,
      update: services.updateApplicationScalingRule,
      delete: services.deleteApplicationScalingRule,
    };

    const res = await actionMap[action || type]({
      params: {
        AppId: appId,
        ...values,
      },
    });
    return res;
  };

  const renderFooter = () => {
    if (ruleType === SCALE_RULE_TYPE.METRIC) {
      return (
        <>
          <Button type="primary" loading={submitLoading} onClick={onSubmit}>
            {intl('saenext.basic-info.ScalingRuleSlide.Confirm')}
          </Button>
          <Button
            onClick={() => {
              setSlideVisible(false);
            }}
          >
            {intl('saenext.basic-info.ScalingRuleSlide.Cancel')}
          </Button>
        </>
      );
    }

    if (type === 'preview') {
      return (
        <>
          <Button
            type="primary"
            onClick={() => {
              setSlideVisible(false);
            }}
          >
            {intl('saenext.basic-info.ScalingRuleSlide.Confirm')}
          </Button>
        </>
      );
    }

    if (currentStep === 0) {
      return (
        <>
          <Button type="primary" onClick={onPreviewRule}>
            {intl('saenext.basic-info.ScalingRuleSlide.NextStepPreviewTheTiming')}
          </Button>
          <Button
            onClick={() => {
              setSlideVisible(false);
            }}
          >
            {intl('saenext.basic-info.ScalingRuleSlide.Cancel')}
          </Button>
        </>
      );
    }

    if (currentStep === 1) {
      return (
        <>
          <Button type="primary" loading={submitLoading} onClick={onSubmit}>
            {intl('saenext.basic-info.ScalingRuleSlide.Confirm')}
          </Button>
          <Button
            onClick={() => {
              setCurrentStep(0);
            }}
          >
            {intl('saenext.basic-info.ScalingRuleSlide.Return')}
          </Button>
        </>
      );
    }
  };

  return (
    <>
      <AddPropsWrap onClick={() => setSlideVisible(true)}>{children}</AddPropsWrap>
      <SlidePanel
        title={SLIDE_TITLE[type]}
        isShowing={slideVisible}
        width="large"
        customFooter={renderFooter()}
        onMaskClick={() => {
          setSlideVisible(false);
        }}
        onClose={() => {
          setSlideVisible(false);
        }}
        onCancel={() => {
          setSlideVisible(false);
        }}
        onOk={() => {
          setSlideVisible(false);
        }}
      >
        <Message type="warning" className="mb">
          <div className="text-line">
            {intl('saenext.basic-info.ScalingRuleSlide.TheNewlyCreatedElasticPolicy')}
          </div>
          <div className="text-line">
            {intl('saenext.basic-info.ScalingRuleSlide.InstancesScaledOutByThe.1')}
            <a href={CachedData.confLink('help:sae:configure-a-nat-gateway')} target="_blank">
              {intl('saenext.basic-info.ScalingRuleSlide.OperatingInstructions')}
            </a>
            {intl("saenext.common.full-stop")}
          </div>
          <div className="text-line">
            {intl('saenext.basic-info.ScalingRuleSlide.AfterTheAutoElasticPolicy')}
          </div>
          {/* <div className="text-line">
            {intl(
              'saenext.basic-info.ScalingRuleSlide.ApplicationsWithMonitoringIndicatorPolicies',
            )}
          </div> */}
          <If condition={isEdit}>
            <div className="text-line">
              {intl('saenext.basic-info.ScalingRuleSlide.AfterYouModifyAnElastic.1')}
            </div>
          </If>
        </Message>
        <If condition={slideVisible}>
          <If condition={ruleType !== SCALE_RULE_TYPE.METRIC && type !== 'preview'}>
            <Step current={currentStep} shape="circle" className="mb" labelPlacement="hoz">
              <Step.Item
                key={0}
                title={intl('saenext.basic-info.ScalingRuleSlide.ElasticPolicyConfiguration')}
              />

              <Step.Item
                key={1}
                title={intl('saenext.basic-info.ScalingRuleSlide.PreviewTimingPolicy')}
              />
            </Step>
          </If>
          <ComShow if={currentStep === 0}>
            <ScalingRuleForm
              ref={ruleFormRef}
              appId={appId}
              ruleData={ruleData}
              type={type}
              setRuleType={setRuleType}
            />
          </ComShow>
          <If condition={currentStep === 1}>
            <TimerPreview type={ruleType} ruleTimer={ruleTimer} ruleMetric={ruleMetric} />
          </If>
        </If>
      </SlidePanel>
    </>
  );
};

export default ScalingRuleSlide;
