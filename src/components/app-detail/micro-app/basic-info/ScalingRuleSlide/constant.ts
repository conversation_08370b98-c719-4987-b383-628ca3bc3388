import { intl } from '@ali/cnd';
export const SCALE_RULE_TYPE = {
  TIMING: 'timing',
  METRIC: 'metric',
  MIX: 'mix',
};

export const SCALE_RULE_TYPE_TEXT = {
  [SCALE_RULE_TYPE.TIMING]: intl('saenext.basic-info.ScalingRuleSlide.constant.TimingPolicy'),
  [SCALE_RULE_TYPE.METRIC]: intl(
    'saenext.basic-info.ScalingRuleSlide.constant.MonitoringMetricPolicy',
  ),
  [SCALE_RULE_TYPE.MIX]: intl('saenext.basic-info.ScalingRuleSlide.constant.HybridElasticStrategy'),
};

export const SLIDE_TITLE = {
  create: intl('saenext.basic-info.ScalingRuleSlide.constant.AddAnElasticPolicy'),
  update: intl('saenext.basic-info.ScalingRuleSlide.constant.EditAnElasticPolicy'),
  preview: intl('saenext.basic-info.ScalingRuleSlide.constant.PreviewElasticPolicies'),
};

export const FORM_ITEM_LAYOUT = {
  labelAlign: 'left' as 'left',
  labelTextAlign: 'left' as 'left',
  labelCol: {
    fixedSpan: 8,
  },
  // wrapperCol: {
  //   span: 20,
  // },
};
