import { intl } from '@ali/cnd';
import React, { useEffect, useState, useRef } from 'react';
import { Dialog, Message, Select } from '@ali/cnd';
import _ from 'lodash';
import services from '~/services';
import RestartDialog from './RestartDialog';

const SecurityGroupDialog = (props) => {
  const { children, appConfig, refresh } = props;

  const { RegionId, AppId, VpcId = '', SecurityGroupId = '' } = appConfig;

  const [securityGroupDialog, setSecurityGroupDialog] = useState(false);
  const [curSecurityGroupId, setCurSecurityGroupId] = useState('');
  const [securityGrouponList, setSecurityGrouponList] = useState([]);
  const restartRef = useRef(null);

  useEffect(() => {
    if (!securityGroupDialog) return;
    setCurSecurityGroupId(SecurityGroupId);
    getSecurityGrouponList();
  }, [securityGroupDialog]);

  const handleChangeSecurityGroup = () => {
    setSecurityGroupDialog(true);
  };

  const getSecurityGrouponList = async () => {
    const { SecurityGroups: { SecurityGroup = [] } = {} } =
      (await services.DescribeSecurityGroups({
        RegionId,
        VpcId,
        // SecurityGroupId: key,
        MaxResults:100
      })) || {};

    const _securityGrouponList = _.map(SecurityGroup, (v) => {
      return {
        value: v.SecurityGroupId,
        label: v.SecurityGroupName,
      };
    });
    setSecurityGrouponList(_securityGrouponList);
  };

  const itemRender = (item) => {
    return (
      <>
        <span className="mr-l">{item.label}</span>
        <span className="text-description">{item.value}</span>
      </>
    );
  };

  const handleSubmit = async () => {
    const res = await services.UpdateAppSecurityGroup({
      AppId,
      SecurityGroupId: curSecurityGroupId,
    });
    if (res) {
      setSecurityGroupDialog(false);
      refresh();
      Dialog.confirm({
        title: intl('saenext.micro-app.basic-info.SecurityGroupDialog.TheSecurityGroupHasBeen'),
        content: intl(
          'saenext.micro-app.basic-info.SecurityGroupDialog.AfterSwitchingTheSecurityGroup',
        ),
        onOk: () => {
          restartRef.current.toogleVisible();
        },
        okProps: {
          children: intl('saenext.micro-app.basic-info.SecurityGroupDialog.RestartTheApplication'),
        },
      });
    } else {
      Message.error(
        intl('saenext.micro-app.basic-info.SecurityGroupDialog.SecurityGroupChangeFailed'),
      );
    }
  };

  return (
    <>
      <div onClick={handleChangeSecurityGroup} style={{ width: 'max-content' }}>
        {children}
      </div>
      <Dialog
        title={intl('saenext.micro-app.basic-info.SecurityGroupDialog.SwitchSecurityGroups')}
        visible={securityGroupDialog}
        onOk={handleSubmit}
        onClose={() => setSecurityGroupDialog(false)}
        onCancel={() => setSecurityGroupDialog(false)}
      >
        <>
          <Message type="warning">
            <p>
              {intl('saenext.micro-app.basic-info.SecurityGroupDialog.ToSwitchSecurityGroupsYou')}
            </p>
          </Message>
          <div className="mt">
            {intl('saenext.micro-app.basic-info.SecurityGroupDialog.SecurityGroup')}

            <Select
              style={{ minWidth: 400 }}
              placeholder={intl(
                'saenext.micro-app.basic-info.SecurityGroupDialog.EnterTheFullNameOf',
              )}
              showSearch
              value={curSecurityGroupId}
              dataSource={securityGrouponList}
              itemRender={itemRender}
              onChange={setCurSecurityGroupId as any}
            />
          </div>
        </>
      </Dialog>
      <RestartDialog ref={restartRef} appId={appConfig?.AppId} refresh={refresh} />
    </>
  );
};

export default SecurityGroupDialog;
