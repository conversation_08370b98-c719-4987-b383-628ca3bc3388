import { intl } from '@ali/cnd';
import React from 'react';
import { Message, Tab, Timeline, Collapse } from '@ali/cnd';
import CachedData from '~/cache/common';

type Props = {};

const noticeStyle = {
  padding: 16,
  width: 808,
  color: '#fff',
  lineHeight: 1.5,
  backgroundColor: '#263238',
};
const lineStyle = {
  lineHeight: 1.5,
};
const LatestDebug = (props: Props) => {
  return (
    <>
      <Message type="notice">
        <div style={lineStyle}>
          <span>
            {intl('saenext.components.remote-debug.LatestDebug.ThroughTheRemoteDebuggingFunction')}
          </span>
          <a href={CachedData.confLink('help:sae:remote-debugging')} target="_blank">
            {intl('saenext.components.remote-debug.LatestDebug.HowToUseRemoteDebugging')}
          </a>
        </div>
      </Message>
      <Tab style={{ marginTop: 8 }}>
        <Tab.Item title={intl('saenext.components.remote-debug.LatestDebug.OperationGuide')}>
          <Timeline style={{ marginTop: 16 }}>
            <Timeline.Item
              title={intl(
                'saenext.components.remote-debug.LatestDebug.SpringboardMachineInitialization',
              )}
              key="init"
              content={
                <>
                  <div style={{ marginBottom: 8, marginTop: 8 }}>
                    <div style={lineStyle}>
                      {intl(
                        'saenext.components.remote-debug.LatestDebug.StepCreateASpringboardMachine',
                      )}
                    </div>
                    <div style={{ ...lineStyle, marginTop: 4 }}>
                      {intl(
                        'saenext.components.remote-debug.LatestDebug.StepConfigureTheSecurityGroup',
                      )}
                    </div>
                  </div>
                  <div style={{ ...noticeStyle, marginBottom: 16 }}>
                    {intl(
                      'saenext.components.remote-debug.LatestDebug.TheSystemAutomaticallyCreatesA',
                    )}
                    <a href={CachedData.confLink('help:sae:pay-as-you-go-1')} target="_blank">
                      {intl('saenext.components.remote-debug.LatestDebug.SaePayAsYouGo')}
                    </a>
                    {intl('saenext.components.remote-debug.LatestDebug.And')}
                    <a href={CachedData.confLink('help:eip:pay-as-you-go')} target="_blank">
                      {intl('saenext.components.remote-debug.LatestDebug.PayAsYouGoEip')}
                    </a>
                    {intl("saenext.common.full-stop")}
                  </div>
                </>
              }
              state="process"
            />

            <Timeline.Item
              title={intl(
                'saenext.components.remote-debug.LatestDebug.PrePreparationForInstanceDebugging',
              )}
              key="prepare"
              content={
                <>
                  <div style={{ ...lineStyle, marginBottom: 8, marginTop: 8 }}>
                    {intl('saenext.components.remote-debug.LatestDebug.EnableTheOneClickCopy')}
                  </div>
                  <div style={{ ...noticeStyle, marginBottom: 16 }}>
                    {intl('saenext.components.remote-debug.LatestDebug.RemoteDebuggingIsOnlyUsed')}
                  </div>
                </>
              }
              state="process"
            />

            <Timeline.Item
              title={intl(
                'saenext.components.remote-debug.LatestDebug.RemoteDebuggingOfAnInstance',
              )}
              key="done"
              content={<></>}
              state="process"
            />
          </Timeline>
        </Tab.Item>
        <Tab.Item title={intl('saenext.components.remote-debug.LatestDebug.BestPractices')}>
          <Collapse key="sshroot" style={{ marginTop: 16, width: 840 }} defaultExpandedKeys={['0']}>
            <Collapse.Panel
              title={intl(
                'saenext.components.remote-debug.LatestDebug.ScenarioAccessTheSaeApplication',
              )}
            >
              <div style={{ ...noticeStyle, marginBottom: 8 }}>
                {intl('saenext.components.remote-debug.LatestDebug.SshRootSaeApplicationInstance')}
              </div>
            </Collapse.Panel>
          </Collapse>
          <Collapse key="ssh-l" style={{ marginTop: 8, width: 840 }} defaultExpandedKeys={['0']}>
            <Collapse.Panel
              title={intl(
                'saenext.components.remote-debug.LatestDebug.ScenarioMapTheSpecifiedPort',
              )}
            >
              <div style={{ ...noticeStyle, marginBottom: 8 }}>
                {intl('saenext.components.remote-debug.LatestDebug.SshLLocalDebugPort')}
              </div>
            </Collapse.Panel>
          </Collapse>
          <Collapse
            key="java-debug"
            style={{ marginTop: 8, width: 840 }}
            defaultExpandedKeys={['0']}
          >
            <Collapse.Panel
              title={intl('saenext.components.remote-debug.LatestDebug.ScenarioJavaRemoteDebug')}
            >
              <div style={{ ...noticeStyle, marginBottom: 8 }}>
                <div>
                  {intl(
                    'saenext.components.remote-debug.LatestDebug.StepSetStartupParametersOptions',
                  )}
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    paddingLeft: 40,
                    color: '#06b624',
                  }}
                >
                  <span>
                    {intl(
                      'saenext.components.remote-debug.LatestDebug.AgentlibJdwpTransportDtSocket',
                    )}
                  </span>
                  <span>
                    {intl(
                      'saenext.components.remote-debug.LatestDebug.TransportTheDataTransmissionMethod',
                    )}
                  </span>
                  <span>
                    {intl('saenext.components.remote-debug.LatestDebug.AddressThePortOfThe')}
                    <span style={{ color: 'red' }}>
                      {intl(
                        'saenext.components.remote-debug.LatestDebug.ConsistentWithTheDebugPort',
                      )}
                    </span>
                    {intl('saenext.components.remote-debug.LatestDebug.IfTheJavaEnvironmentIs')}
                  </span>
                </div>
                <div style={{ ...lineStyle, marginTop: 4 }}>
                  {intl('saenext.components.remote-debug.LatestDebug.StepAfterTheDeploymentIs')}
                </div>
                <div style={{ ...lineStyle, marginTop: 4 }}>
                  {intl('saenext.components.remote-debug.LatestDebug.StepMapTheSpecifiedPort')}
                </div>
                <div style={{ paddingLeft: 40, color: '#06b624' }}>
                  {intl('saenext.components.remote-debug.LatestDebug.SshLLocalDebugPort.2')}
                </div>
                <div style={{ ...lineStyle, marginTop: 4 }}>
                  {intl(
                    'saenext.components.remote-debug.LatestDebug.StepLocalConnectionLocalDebug',
                  )}
                </div>
              </div>
            </Collapse.Panel>
          </Collapse>
          <Collapse
            key="php-debug"
            style={{ marginTop: 8, width: 840 }}
            defaultExpandedKeys={['0']}
          >
            <Collapse.Panel
              title={intl(
                'saenext.components.remote-debug.LatestDebug.ScenarioPhpRemoteDebugXdebug',
              )}
            >
              <div style={{ ...noticeStyle, marginBottom: 8 }}>
                <div>
                  {intl('saenext.components.remote-debug.LatestDebug.StepIfTheApplicationUses')}
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    paddingLeft: 40,
                    color: '#06b624',
                  }}
                >
                  <span>{`# PhpStorm Validator`}</span>
                  <span>{`location = /_intellij_phpdebug_validator.php {`}</span>
                  <span style={{ marginLeft: 12 }}>{`root /home/<USER>/app/php;`}</span>
                  <span style={{ marginLeft: 12 }}>{`fastcgi_pass 127.0.0.1:9000;`}</span>
                  <span style={{ marginLeft: 12 }}>{`include conf.d/fastcgi_params;`}</span>
                  <span>{`}`}</span>
                </div>
                <div style={{ ...lineStyle, marginTop: 4 }}>
                  {intl('saenext.components.remote-debug.LatestDebug.StepSetThePhpIni')}
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    paddingLeft: 40,
                    color: '#06b624',
                  }}
                >
                  <span>{`[xdebug]`}</span>
                  <span>{`zend_extension=xdebug`}</span>
                  <span>{`xdebug.mode=debug`}</span>
                  <span>{`xdebug.client_host=127.0.0.1`}</span>
                  <span>{`xdebug.client_port=9003`}</span>
                  <span>{`xdebug.discover_client_host=true`}</span>
                  <span>
                    {intl('saenext.components.remote-debug.LatestDebug.XdebugClientPortTheLocal')}
                    <span style={{ color: 'red' }}>
                      {intl(
                        'saenext.components.remote-debug.LatestDebug.ConsistentWithTheDebugPort',
                      )}
                    </span>
                    {intl("saenext.common.full-stop")}
                  </span>
                </div>
                <div style={{ ...lineStyle, marginTop: 4 }}>
                  {intl('saenext.components.remote-debug.LatestDebug.StepAfterTheDeploymentIs.1')}
                </div>
                <div style={{ ...lineStyle, marginTop: 4 }}>
                  {intl('saenext.components.remote-debug.LatestDebug.StepMapTheSpecifiedPort.1')}
                </div>
                <div style={{ paddingLeft: 40, color: '#06b624' }}>
                  {intl('saenext.components.remote-debug.LatestDebug.SshLLocalDebugPort.2')}
                </div>
                <div style={{ ...lineStyle, marginTop: 4 }}>
                  {intl(
                    'saenext.components.remote-debug.LatestDebug.StepLocalConnectionLocalDebug.1',
                  )}
                </div>
              </div>
            </Collapse.Panel>
          </Collapse>
        </Tab.Item>
      </Tab>
    </>
  );
};

export default LatestDebug;
