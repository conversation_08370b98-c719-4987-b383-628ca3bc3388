import { intl } from '@ali/cnd';
import React from 'react';
import { Button } from '@ali/cnd';

const ArmsNotEnable = (props) => {
  const armsDemoOptions = [
    {
      title: intl('saenext.micro-app.arms-monitor.ArmsNotEnable.ApplicationOverview'),
      imgUrl:
        'https://img.alicdn.com/imgextra/i4/O1CN01yvSwHv222S3QAB2Fh_!!6000000007062-2-tps-2466-763.png',
    },
    {
      title: intl('saenext.micro-app.arms-monitor.ArmsNotEnable.ApplicationTopology'),
      imgUrl:
        'https://img.alicdn.com/imgextra/i4/O1CN01D6J5El1cgal1ZoCls_!!6000000003630-2-tps-2461-1190.png',
    },
    {
      title: intl('saenext.micro-app.arms-monitor.ArmsNotEnable.CallChainAnalysis'),
      imgUrl:
        'https://img.alicdn.com/imgextra/i3/O1CN01ymuWTe1VfUUjD0BBp_!!6000000002680-2-tps-2467-1304.png',
    },
    {
      title: intl('saenext.micro-app.arms-monitor.ArmsNotEnable.InstanceMonitoring'),
      imgUrl:
        'https://img.alicdn.com/imgextra/i1/O1CN01LICoYp1sGySJl1Lnb_!!6000000005740-2-tps-2457-1249.png',
    },
  ];

  return (
    <div>
      <div style={{ fontSize: '14px', fontWeight: 'bold', lineHeight: '24px', marginBottom: 10 }}>
        {intl('saenext.micro-app.arms-monitor.ArmsNotEnable.WelcomeToApplicationMonitoring')}
      </div>
      <div style={{ lineHeight: '20px' }}>
        {intl(
          'saenext.micro-app.arms-monitor.ArmsNotEnable.ServerlessTheApplicationEngineIntegrates',
        )}
      </div>
      <div className="mt-l">
        <Button
          type="primary"
          onClick={() => {
            const { history, regionId, appConfig } = props;
            const search = window?.location?.search
              ? `${window.location.search}&targetPart=armsMonitor`
              : '?targetPart=armsMonitor';
            history.push(`/${regionId}/app-list/${appConfig?.AppId}/micro-app/deploy${search}`);
          }}
        >
          {intl('saenext.micro-app.arms-monitor.ArmsNotEnable.ConfigureApplicationMonitoring')}
        </Button>
      </div>
      <div
        className="mt-xl"
        style={{ display: 'flex', flexWrap: 'wrap', gap: '20px', width: '95%', maxWidth: 1560 }}
      >
        {armsDemoOptions.map((item) => {
          return (
            <div style={{ flex: '1 1 calc(50% - 10px)' }}>
              <div className="mb-s" style={{ fontWeight: 'bold' }}>
                {item.title}
              </div>
              <div
                style={{
                  width: '100%',
                  height: '300px',
                  border: '1px solid #e5e5e5',
                  borderRadius: '4px',
                  backgroundImage: `url(${item.imgUrl})`,
                  backgroundSize: 'contain',
                  backgroundRepeat: 'no-repeat',
                }}
              ></div>
            </div>
            // <div style={{ flex: '1 1 calc(50% - 10px)' }}>
            //   <div className="mb-s" style={{ fontWeight: 'bold' }}>
            //     {item.title}
            //   </div>
            //   <img style={{ width: '100%', height: 'auto' }} src={item.imgUrl} />
            // </div>
          );
        })}
      </div>
    </div>
  );
};

export default ArmsNotEnable;
