import React, { useState, useMemo } from 'react';
import { Loading } from '@ali/cnd';
import ArmsNotEnable from './ArmsNotEnable';
import CachedData from '~/cache/common';

const ConsoleNavHeight = 50;
const BreadcrumbHeight = 52;
const PageNavHeight = 52;
const ReservedSpaceHeight = 16;

const ArmsMonitor = props => {
  const { regionId, appConfig } = props;
  const [loading, setLoading] = useState(true);
  const iframeHeight =
    document.documentElement.clientHeight -
    ConsoleNavHeight -
    BreadcrumbHeight -
    PageNavHeight -
    ReservedSpaceHeight;

  const iframeLoaded = () => {
    setTimeout(() => {
      setLoading(false);
    });
  };

  const renderContent = useMemo(() => {
    return (
      <React.Fragment>
        {!appConfig?.EnableNewArms && <ArmsNotEnable {...props} />}

        {appConfig?.EnableNewArms && (
          <Loading
            visible={loading}
            style={{ width: '100%', minHeight: iframeHeight, position: 'relative' }}
          >
            <iframe
              width="100%"
              height={iframeHeight}
              scrolling="no"
              frameBorder={0}
              onLoad={iframeLoaded}
              src={`${CachedData.confLink('feature:armsnext:url')}/tracing#/tracing/${regionId}?appId=${appConfig?.AppId}&source=TRACE&language=${appConfig?.ProgrammingLanguage}&forbiddenFeats=appConfig-del,samplingSetting,breadcrumb,old,appTable`}
            />
          </Loading>
        )}
      </React.Fragment>
    );
  }, [appConfig, loading]);
  return <div>{renderContent}</div>;
};

export default ArmsMonitor;
