import { useEffect, useState } from "react";
import { checkVSwitchZone } from "./checkVSwitchZone";

export const useCheckVSwitchZone = (options) => {
  const {
    zoneList,
    regionId,
  } = options;

  const [inRecommend, setInRecommend] = useState(true);
  const [recommendZones, setRecommendZones] = useState([]);

  useEffect(() => {
    if (!zoneList.length || !regionId) {
      return;
    }
    getRecommendZones();
  }, [JSON.stringify(zoneList), regionId]);

  const getRecommendZones = async () => {
    const {
      inRecommend,
      recommendZones,
    } = await checkVSwitchZone(zoneList, regionId);
    setInRecommend(inRecommend);
    setRecommendZones(recommendZones);
  }
  
  return {
    inRecommend,
    recommendZones,
  };
}