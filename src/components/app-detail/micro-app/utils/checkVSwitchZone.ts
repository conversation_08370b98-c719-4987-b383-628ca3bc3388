import { find, get } from "lodash";
import services from "~/services";

export const checkVSwitchZone = async (zoneList, regionId) => {
  const {
    Regions: {
      Region = []
    } = {}
  } = await services.DescribeRegions() || {};

  const regionMatch = find(Region, { 'RegionId': regionId });
  const recommendZones = get(regionMatch, ['RecommendZones', 'RecommendZone']);

  const defaultInRecommend = recommendZones.length === 0;

  const inRecommend = defaultInRecommend || zoneList.some(zone => recommendZones.includes(zone));

  return {
    inRecommend,
    recommendZones,
  }
}