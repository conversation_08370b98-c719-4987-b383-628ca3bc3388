import { intl } from '@ali/cnd';
import React from 'react';
import { map, isEmpty } from 'lodash';
import { Collapse, Link, LinkButton } from '@ali/cnd';
import TaskAppStatus, { StatusMap } from './TaskAppStatus';
import EmptyContent from './EmptyContent';
import { getParams } from '../../../../utils/global';

const Panel = Collapse.Panel;

const TaskAppPanel = (props) => {
  const { history, appId, regionId, stage } = props;
  const { TaskList = [] } = stage;

  const handleTaskRealtime = () => {
    history &&
      history.push(
        `/${regionId}/app-list/${appId}/micro-app/realtime-log?name=${getParams('name')}`,
      );
  };

  const handleTaskEvent = () => {
    history &&
      history.push(`/${regionId}/app-list/${appId}/micro-app/event?name=${getParams('name')}`);
  };
  return (
    <div className="astage-apanel">
      {isEmpty(TaskList) ? (
        <EmptyContent
          tip={intl('saenext.components.change-order.TaskPanel.NoEligibleInformationIsFound')}
        />
      ) : (
        <>
          {map(TaskList, (task) => {
            const {
              TaskId,
              TaskName,
              Status: status,
              Message,
              ShowManualIgnore: showManualIgnore = false,
            } = task;
            const { logColor } = StatusMap[status];
            // 执行中的日志信息默认展开
            const defaultExpandedKeys = status === 1 ? [TaskId] : [];
            return (
              <Collapse key={TaskId} defaultExpandedKeys={defaultExpandedKeys}>
                <Panel
                  key={TaskId}
                  title={
                    <div className="title">
                      <span className="mr-xl">{TaskName}</span>
                      <TaskAppStatus value={status} />
                      <RcTaskButton show={status === 3}>
                        <Link to={`/${regionId}/app-list/${appId}/micro-app/base?tab=instance`}>
                          {intl(
                            'saenext.components.change-order.TaskAppPanel.ViewInstanceInformation',
                          )}
                        </Link>
                        {/* <span style={{ color: '#333' }}>
                       {intl('saenext.components.change-order.TaskPanel.PleaseClick')}
                      </span>
                      <LinkButton style={{ margin: '0 4px' }} onClick={handleTaskRealtime}>
                       {intl('saenext.components.change-order.TaskPanel.RealTimeLog')}
                      </LinkButton>
                      <span style={{ color: '#333' }}>
                       {intl('saenext.components.change-order.TaskPanel.Or')}
                      </span>
                      <LinkButton style={{ margin: '0 4px' }} onClick={handleTaskEvent}>
                       {intl('saenext.components.change-order.TaskPanel.ApplicationEvents')}
                      </LinkButton>
                      <span style={{ color: '#333' }}>
                       {intl('saenext.components.change-order.TaskPanel.TroubleshootExceptions')}
                      </span> */}
                      </RcTaskButton>
                      <RcTaskButton show={status === 5}>
                        {intl('saenext.components.change-order.TaskPanel.Retry')}
                      </RcTaskButton>
                      <RcTaskButton show={status === 5}>
                        {intl('saenext.components.change-order.TaskPanel.GiveUp')}
                      </RcTaskButton>
                      <RcTaskButton show={showManualIgnore}>
                        {intl('saenext.components.change-order.TaskPanel.Skip')}
                      </RcTaskButton>
                    </div>
                  }
                >
                  <div
                    style={{ color: logColor }}
                    className="astage-apanel-body"
                    dangerouslySetInnerHTML={{ __html: Message }}
                  >
                    {/* {Message} */}
                  </div>
                </Panel>
              </Collapse>
            );
          })}
        </>
      )}
    </div>
  );
};

const RcTaskButton = (props) => {
  const { show, children, ...restProps } = props;
  if (!show) return null;

  return (
    <div className="actions" style={{ margin: '0 8px' }}>
      <LinkButton {...restProps}>{children}</LinkButton>
    </div>
  );
};

export default TaskAppPanel;
