import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Button, Dialog, Message, Input, Form, Select, Tag } from '@ali/cnd';
import { get, isEmpty, includes, noop, forEach, map } from 'lodash';
import services from '~/services';
import ExternalLink from '~/components/shared/ExternalLink';

const StatusActions = props => {
  const {
    appId,
    changeOrder,
    changeOrderId,
    rollbackHook = noop,
    actionRefreshHook = noop,
    approvalRecordId = '',
  } = props;
  if (isEmpty(changeOrder)) return null;

  // 是否为终态
  const [finalStatus, setFinalStatus] = useState(true);
  // 是否审批通过
  const [isApprovedStatus, setIsApprovedStatus] = useState(false);
  // 是否审批流程的状态
  const [isApprovalStatus, setIsApprovalStatus] = useState(false);
  // 是否支持回滚
  const [isSupportRollback, setIsSupportRollback] = useState(false);

  const [contactData, setContactData] = useState([]);

  /**
   * 1、status = 12 审批通过，展示操作 ‘开始部署’
   * 2、status != 11、12 审批流程，并且变更单支持回滚时，展示操作 ‘立即回滚’
   * 3、status != 11、12 审批流程，并且变更单不支持回滚时，展示操作 ‘终止变更’
   */

  useEffect(() => {
    checkOrderStatus();
  }, [changeOrder]);

  useEffect(() => {
    approvalRecordId && getListContacts();
  }, [approvalRecordId]);


  const checkOrderStatus = () => {
    if (isEmpty(changeOrder)) return;
    const status = get(changeOrder, 'Status', 0);
    const _finalStatus = includes([2, 3, 6, 20], status);
    const _isApprovedStatus = status === 12;
    const _isApprovalStatus = includes([11, 12], status);
    const _isSupportRollback = get(changeOrder, 'SupportRollback', false);
    setFinalStatus(_finalStatus);
    setIsApprovedStatus(_isApprovedStatus);
    setIsApprovalStatus(_isApprovalStatus);
    setIsSupportRollback(_isSupportRollback);
  };

  const handleOrderRedeploy = () => {
    // TODO: 调用接口
    Dialog.alert({
      title: intl('saenext.components.change-order.ChangeOrderActions.StartDeployment'),
      content: (
        <p>{intl('saenext.components.change-order.ChangeOrderActions.AreYouSureYouWant')}</p>
      ),

      onOk: () =>
        new Promise(async (resolve, reject) => {
          const approvalId = get(changeOrder, 'ApprovalId');
          if (isEmpty(approvalId)) reject(false);
          const res = await services.redeployChangeOrder({
            params: {
              ApprovalId: approvalId,
            },
            customErrorHandle: (error, data, callback) => {
              callback && callback();
              reject(false);
            },
          });
          const success = get(res, 'Success');
          if (success) {
            // 请求新的变更单
            resolve(true);
            actionRefreshHook(Date.now());
          } else {
            reject(false);
          }
        }),
    });
  };

  const handleOrderRollback = () => {
    // TODO: 调用接口
    Dialog.alert({
      title: intl('saenext.components.change-order.ChangeOrderActions.RollBackNow'),
      content: (
        <p style={{ width: 500 }}>
          {intl('saenext.components.change-order.ChangeOrderActions.AfterRollbackTheSystemWill')}
        </p>
      ),

      okProps: { children: intl('saenext.components.change-order.ChangeOrderActions.Rollback') },
      onOk: () =>
        new Promise(async (resolve, reject) => {
          if (isEmpty(changeOrderId)) reject(false);
          const res = await services.rollbackChangeOrder({
            params: {
              ChangeOrderId: changeOrderId,
            },
            customErrorHandle: (error, data, callback) => {
              callback && callback();
              reject(false);
            },
          });
          const orderId = get(res, 'Data.ChangeOrderId');
          if (!isEmpty(orderId)) {
            Message.success(
              intl(
                'saenext.components.change-order.ChangeOrderActions.RollbackSucceededChangeOrderNumber',
                { orderId: orderId },
              ),
            );
            // 请求新的变更单
            resolve(true);
            rollbackHook && rollbackHook(orderId);
          } else {
            Message.error(
              intl('saenext.components.change-order.ChangeOrderActions.RollbackFailed'),
            );
            reject(false);
          }
        }),
    });
  };

  const handlerOderAbortRelease = () => {
    // TODO: 调用接口
    Dialog.alert({
      title: intl('saenext.components.change-order.ChangeOrderActions.TerminateTheChange'),
      content: (
        <p>{intl('saenext.components.change-order.ChangeOrderActions.AreYouSureYouWant.1')}</p>
      ),

      onOk: () =>
        new Promise(async (resolve, reject) => {
          if (isEmpty(changeOrderId)) reject(false);
          const res = await services.abortChangeOrder({
            params: {
              ChangeOrderId: changeOrderId,
            },
            customErrorHandle: (error, data, callback) => {
              callback && callback();
              reject(false);
            },
          });
          const orderId = get(res, 'Data.ChangeOrderId');
          if (!isEmpty(orderId)) {
            Message.success(
              intl(
                'saenext.components.change-order.ChangeOrderActions.TerminatedChangeOrderNumberOrderid',
                { orderId: orderId },
              ),
            );
            // 请求新的变更单
            resolve(true);
            actionRefreshHook(Date.now());
          } else {
            Message.error(
              intl('saenext.components.change-order.ChangeOrderActions.TerminationFailed'),
            );
            reject(false);
          }
        }),
    });
  };

  const handlerConfirmSuccess = () => {
    Dialog.alert({
      title: intl(
        'saenext.components.change-order.ChangeOrderActions.ManuallyConfirmThatTheDeployment',
      ),
      content: (
        <>
          <p>
            {intl('saenext.components.change-order.ChangeOrderActions.TheSystemDetectsThatMore')}
          </p>
          <ul className="list-disc">
            <li>
              {intl(
                'saenext.components.change-order.ChangeOrderActions.IfRelevantPerformTheRollback',
              )}
            </li>
            <li>
              {intl.html(
                'saenext.components.change-order.ChangeOrderActions.IfItIsIrrelevantClick.1',
              )}
            </li>
          </ul>
        </>
      ),

      onOk: async () => {
        const res = await services.confirmChangeOrderSuccess({
          params: {
            AppId: appId,
            ChangeOrderId: changeOrderId,
          },
        });
        if (res?.Success) {
          Message.success(
            intl(
              'saenext.components.change-order.ChangeOrderActions.ManuallyConfirmThatTheDeployment',
            ),
          );
          actionRefreshHook(Date.now());
          return true;
        } else {
          Message.error(
            intl(
              'saenext.components.change-order.ChangeOrderActions.ManuallyConfirmDeploymentFailure',
            ),
          );
          return false;
        }
      },
    });
  };

  const getListContacts = async () => {
    const {
      Data: { ContactsInfo = [] },
    } = await services.ListContactsWithoutDetailInfo();
    const list = map(ContactsInfo, v => {
      const { Uid, Name, IsMainAccount } = v;
      return {
        value: Uid,
        label: Name,
        title: IsMainAccount
          ? intl('saenext.enterprise.approval-records.PendingTableList.PrimaryAccount')
          : '',
      };
    });
    setContactData(list);
  };


  const sendApprovalRequest = async ({
    recordId,
    result,
    comment = '',
    otherApprovingOfficer = '',
  }) => {
    const data = await services.ApprovalOperationRequest({
      OperationApprovalRecordId: recordId,
      OperationApprovalResult: result,
      OperationApprovalComment: comment,
      OtherApprovingOfficer: otherApprovingOfficer,
    });
    return data;
  };

  const handleApprovalDialog = result => {
    const dialog = Dialog.confirm({
      title: intl('saenext.enterprise.approval-records.PendingTableList.Prompt'),
      content: (
        <p>{intl('saenext.enterprise.approval-records.PendingTableList.AreYouSureYouWant')}</p>
      ),
      onOk: async () => {
        await Promise.all(
          forEach([approvalRecordId], async v => {
            const params = {
              recordId: v,
              result,
            };
            await sendApprovalRequest(params);
          }),
        );
        Message.success(
          intl('saenext.enterprise.approval-records.PendingTableList.AgreeToApproval'),
        );
        dialog.hide();
        setTimeout(() => {
          actionRefreshHook(Date.now());
        }, 1000);
      },
    });
  };
  const handleRefusedDialog = result => {
    let comment = '';
    const dialog = Dialog.show({
      title: intl('saenext.enterprise.approval-records.PendingTableList.ConfirmApprovalRejection'),
      content: (
        <div style={{ width: 400 }}>
          <Message type="warning">
            {intl(
              'saenext.enterprise.approval-records.PendingTableList.AfterTheApprovalIsRejected',
            )}
          </Message>
          <div>
            <Input.TextArea
              size="large"
              placeholder={intl(
                'saenext.enterprise.approval-records.PendingTableList.ReasonForRejection',
              )}
              maxLength={200}
              hasLimitHint
              onChange={value => (comment = value)}
              style={{ width: '100%', marginTop: '20px' }}
            />
          </div>
        </div>
      ),

      onOk: async () => {
        await Promise.all(
          forEach([approvalRecordId], async v => {
            const params = {
              recordId: v,
              result,
              comment,
            };
            await sendApprovalRequest(params);
          }),
        );
        Message.success(intl('saenext.enterprise.approval-records.PendingTableList.Rejected'));
        comment = '';
        dialog.hide();
        setTimeout(() => {
          actionRefreshHook(Date.now());
        }, 1000);
      },
    });
  };
  const handleHandoverDialog = result => {
    let otherApprovingOfficer = '';
    const dialog = Dialog.show({
      title: intl('saenext.enterprise.approval-records.PendingTableList.ApprovalTransfer'),
      content: (
        <div style={{ width: 500 }}>
          <Message type="notice">
            {intl('saenext.enterprise.approval-records.PendingTableList.AfterTheApprovalTaskIs')}
          </Message>
          <Form inline style={{ marginTop: 20 }}>
            <Form.Item
              label={intl('saenext.enterprise.approval-records.PendingTableList.Approver')}
              required
              requiredMessage={intl(
                'saenext.enterprise.approval-records.PendingTableList.PleaseSelectTransferApprover',
              )}
            >
              <Select
                showSearch
                placeholder=""
                filterLocal={false}
                dataSource={contactData}
                style={{ width: 400 }}
                onChange={(value: string) => (otherApprovingOfficer = value)}
                valueRender={approvingValueRender}
                itemRender={approvingValueRender}
              />
            </Form.Item>
          </Form>
          <div style={{ margin: 20, paddingLeft: 40 }}>
            <p>
              {intl('saenext.enterprise.approval-records.PendingTableList.IfYouFindThatThe')}

              <ExternalLink
                label={intl(
                  'saenext.enterprise.approval-records.PendingTableList.ContactManagement',
                )}
                url="/operations-management/approval?type=concats"
              />
              {intl('saenext.enterprise.approval-records.PendingTableList.AddSettingsToTheMenu')}
            </p>
          </div>
        </div>
      ),

      onOk: async () => {
        if (!otherApprovingOfficer) {
          Message.error(
            intl('saenext.enterprise.approval-records.PendingTableList.SelectAnApprover'),
          );
          return;
        }
        const options = [];
        forEach([approvalRecordId], async v => {
          const params = {
            recordId: v,
            result,
            otherApprovingOfficer,
          };
          options.push(sendApprovalRequest(params));
        });
        const res = await Promise.all(options);
        Message.success(intl('saenext.enterprise.approval-records.PendingTableList.Transferred'));
        otherApprovingOfficer = '';
        dialog.hide();
        setTimeout(() => {
          actionRefreshHook(Date.now());
        }, 1000);
      },
    });
  };

  const approvingValueRender = item => {
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          width: 'max-content',
          height: '100%',
          marginRight: 12,
        }}
      >
        <span> {item.label}</span>
        {item.title ? (
          <Tag color="yellow" style={{ marginLeft: 10 }}>
            {intl('saenext.enterprise.approval-records.PendingTableList.PrimaryAccount')}
          </Tag>
        ) : (
          ''
        )}
      </div>
    );
  };

  // if (!isApprovedStatus && isApprovalStatus) return null;

  if(approvalRecordId && get(changeOrder, 'Status', 0)===11){
    return (
      <div
      style={{
        display: 'flex',
        flexDirection: 'row-reverse',
        justifyContent: 'space-between',
        margin: '-50px 0 16px',
        position: 'absolute',
        right: 0,
      }}
    >
      {approvalRecordId && get(changeOrder, 'Status', 0) === 11 && (
        <>
          <RcButton
            style={{ marginLeft: 8 }}
            show={true}
            onClick={() => {
              handleHandoverDialog('handover');
            }}
          >
            {intl('saenext.enterprise.approval-records.TableProps.Transfer')}
          </RcButton>
          <RcButton
            type="primary"
            warning
            style={{ marginLeft: 8 }}
            show={true}
            onClick={() => {
              handleRefusedDialog('refused');
            }}
          >
            {intl('saenext.enterprise.approval-records.TableProps.Reject')}
          </RcButton>
          <RcButton
            type="primary"
            style={{ marginLeft: 8 }}
            show={true}
            onClick={() => {
              handleApprovalDialog('approved');
            }}
          >
            {intl('saenext.enterprise.approval-records.TableProps.Agree')}
          </RcButton>
        </>
      )}
    </div>
    )
  }else if(!isApprovedStatus && isApprovalStatus){
    return null
  }else{
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'row-reverse',
          justifyContent: 'space-between',
          margin: '-50px 0 16px',
          position: 'absolute',
          right: 0,
        }}
      >
        <RcButton
          type="primary"
          style={{ marginLeft: 8 }}
          show={true}
          onClick={() => actionRefreshHook(Date.now())}
        >
          {intl('saenext.components.change-order.ChangeOrderActions.Refresh')}
        </RcButton>
        <RcButton
          type="primary"
          style={{ marginLeft: 8 }}
          show={isApprovedStatus}
          disabled={!isApprovedStatus}
          onClick={handleOrderRedeploy}
        >
          {intl('saenext.components.change-order.ChangeOrderActions.StartDeployment')}
        </RcButton>
        <RcButton
          warning
          type="primary"
          style={{ marginLeft: 8 }}
          show={isSupportRollback && !isApprovalStatus}
          disabled={finalStatus}
          onClick={handleOrderRollback}
        >
          {intl('saenext.components.change-order.ChangeOrderActions.RollBackNow')}
        </RcButton>
        <RcButton
          warning
          type="primary"
          style={{ marginLeft: 8 }}
          show={!isSupportRollback && !isApprovalStatus}
          disabled={finalStatus}
          onClick={handlerOderAbortRelease}
        >
          {intl('saenext.components.change-order.ChangeOrderActions.TerminateTheChange')}
        </RcButton>
        <RcButton
          type="primary"
          style={{ marginLeft: 8 }}
          show={changeOrder.SupportConfirmSuccess}
          onClick={handlerConfirmSuccess}
        >
          {intl(
            'saenext.components.change-order.ChangeOrderActions.ManuallyConfirmThatTheDeployment',
          )}
        </RcButton>
      </div>
    );
  }

};

export const RcButton = props => {
  const { show, children, ...restProps } = props;
  if (!show) return null;
  return <Button {...restProps}>{children}</Button>;
};

export default StatusActions;
