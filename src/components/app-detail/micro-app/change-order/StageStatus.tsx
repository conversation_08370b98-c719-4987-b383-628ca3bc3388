import { intl } from '@ali/cnd';
import React, { memo } from 'react';
import { StatusIndicator } from '@ali/cnd';

/**
 * 
  stage实例的状态
  0、READY；--就绪
  1、RUNNING；--执行中
  2、SUCCESS；--成功
  3、FAIL；--失败
  4、IGNORE； --忽略
  5、RETRY --重试
  6、CANCLE 取消
*/

export const StatusMap = {
  0: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.StageStatus.Ready'),
  },
  1: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.StageStatus.Running'),
  },
  2: {
    icon: 'success',
    color: '#333333',
    label: intl('saenext.components.change-order.StageStatus.Success'),
  },
  3: {
    icon: 'error',
    color: '#333333',
    label: intl('saenext.components.change-order.StageStatus.Failed'),
  },
  4: {
    icon: 'disabled',
    color: '#333333',
    label: intl('saenext.components.change-order.StageStatus.Ignore'),
  },
  5: {
    icon: 'warning',
    color: '#333333',
    label: intl('saenext.components.change-order.StageStatus.Retry'),
  },
  6: {
    icon: 'error',
    color: '#333333',
    label: intl('saenext.components.change-order.StageStatus.Cancel'),
  },
};

const Status = (props) => {
  const { value = 0 } = props;
  return (
    <StatusIndicator
      type={StatusMap[value].icon}
      shape="dot"
      style={{ color: StatusMap[value].color }}
    />
  );
};

export default memo(Status);
