import { intl } from '@ali/cnd';
import React, { memo } from 'react';
import { StatusIndicator } from '@ali/cnd';

/**
 * 
  task pod 实例的状态
  0、READY；--就绪
  1、RUNNING；--执行中
  2、SUCCESS；--成功
  3、FAIL；--失败
  4、IGNORE 略过
  5、RETRY --等待确认
  6、CANCLE 终止
*/

export const StatusMap = {
  0: {
    icon: 'loading',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskPodStatus.Ready'),
  },
  1: {
    icon: 'loading',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskPodStatus.Running'),
  },
  2: {
    icon: 'success',
    color: '#333333',
    logColor: '#06b624',
    label: intl('saenext.components.change-order.TaskPodStatus.Success'),
  },
  3: {
    icon: 'error',
    color: '#333333',
    logColor: '#f54745',
    label: intl('saenext.components.change-order.TaskPodStatus.Failed'),
  },
  4: {
    icon: 'disabled',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskPodStatus.Skip'),
  },
  5: {
    icon: 'loading',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskPodStatus.WaitingForConfirmation'),
  },
  6: {
    icon: 'error',
    color: '#333333',
    logColor: '#f54745',
    label: intl('saenext.components.change-order.TaskPodStatus.Termination'),
  },
};

const Status = (props) => {
  const { value = 0 } = props;

  return (
    <StatusIndicator type={StatusMap[value].icon} style={{ color: StatusMap[value].color }}>
      {StatusMap[value].label}
    </StatusIndicator>
  );
};

export default memo(Status);
