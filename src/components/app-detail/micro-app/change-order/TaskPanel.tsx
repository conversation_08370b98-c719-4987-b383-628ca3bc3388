import React from 'react';
import TaskAppPanel from './TaskAppPanel';
import TaskAppPanelNew from './TaskAppPanel/index';
import TaskPodPanel from './TaskPodPanel';
import { get, isEqual, noop } from 'lodash';
import { confFeature } from '@alicloud/console-one-conf';


const TaskPanel = (props) => {
  const { history, appId, regionId, changeOrder = {}, pipelineId, stage, rollbackHook = noop } = props;
  const executor = get(stage, 'ExecutorType', 0);

  const lifecycle_gray = confFeature('lifecycle_gray');

  // 这个部分会显示两种情况
  // 1、stage ExecutorType === 1
  // 2、stage ExecutorType === 0

  return (
    <>
      <RcTaskContent
        show={isEqual(executor, 0)}
      >
        {lifecycle_gray ?
          <TaskAppPanelNew
            regionId={regionId}
            appId={appId}
            history={history}
            stage={stage}
            pipelineId={pipelineId}
          />
          :
          <TaskAppPanel
            regionId={regionId}
            appId={appId}
            history={history}
            stage={stage}
            pipelineId={pipelineId}
          />
        }
      </RcTaskContent>
      <RcTaskContent
        show={isEqual(executor, 1)}
      >
        <TaskPodPanel 
          regionId={regionId} 
          appId={appId} 
          history={history}
          changeOrder={changeOrder}
          stage={stage} 
          pipelineId={pipelineId}
          rollbackHook={rollbackHook}
        />
      </RcTaskContent>
    </>
  );
};

const RcTaskContent = (props) => {
  const { show, children } = props;
  if (!show) return null;

  return children;
};

export default TaskPanel;