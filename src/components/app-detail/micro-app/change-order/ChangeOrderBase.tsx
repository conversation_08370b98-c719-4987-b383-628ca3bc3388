import React, { useState, useEffect } from 'react';
import services from "~/services";
import { get,includes, noop } from 'lodash';
import { Loading } from '@ali/cnd';
import './index.less';
import ChangeOrderActions from './ChangeOrderActions';
import ChangeOrderResume from './ChangeOrderResume';
import PipelineBatchPanel from './PipelineBatchPanel';

const ChangeOrderBase = (props) => {
  const { history, regionId, appId, changeOrderId: _changeOrderId, refreshBreadcrumbs = noop, approvalRecordId = '' } = props;
  const [loading, setLoading] = useState(false);
  const [changeOrderId, setChangeOrderId] = useState(_changeOrderId);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [changeOrder, setChangeOrder] = useState({});
  const timer = React.useRef(null);
  const finalCount = React.useRef(0);

  useEffect(() => {
    getAppChangeOrder();
    return () => {
      timer.current && clearInterval(timer.current);
      timer.current = null;
    };
  }, [changeOrderId, refreshIndex]);

  // 获取变更单包含的流水线
  const getAppChangeOrder = async () => {
    if (!changeOrderId) return;
    setLoading(true);
    await probableLoopRefreshOrder();
    setLoading(false);
  };

  const probableLoopRefreshOrder = async () => {
    const res = await services.getAppChangeOrder({
      params: {
        RegionId: regionId,
        ChangeOrderId: changeOrderId,
      },
      customErrorHandle(err, data, callback) {
        setLoading(false);
        timer.current && clearInterval(timer.current);
        timer.current = null;
        callback && callback();
      },
    });
    const { Data: _changeOrder = {} } = res || {};
    // _status = 2、3、6、20是终态 不用轮训
    const _status = get(_changeOrder, 'Status', 0);
    const isLoop = !includes([2, 3, 6, 20], _status);
    if (isLoop) {
      // 不是终态
      finalCount.current = 0;
      if (!timer.current) {
        timer.current = setInterval(() => {
          probableLoopRefreshOrder();
        }, 1000 * 5);
      }
    } else {
      // 是终态
      finalCount.current += 1;
      // 当连续两次是 终态时 取消计时器
      // 这里考虑的点是 比如之前等待审批中的变更单正在轮训，轮训中一次请求已经发出，此时执行回滚，之前的变更将终止，处于终态，这时不需要取消计时器，需要继续执行回滚产生变更单的轮训
      if (finalCount.current >= 2) {
        timer.current && clearInterval(timer.current);
        timer.current = null;
      }
    }
    setChangeOrder(_changeOrder);
    setLoading(false);
  };

  // 回滚、重启、删除 pod产生的变更单回调
  // 产生新的变更单需要重新获取新的展示
  const rollbackBeenHook = (orderId) => {
    // 改变当前的变更单触发changeOrder
    // 不必清除之前旧的计时器
    setChangeOrderId(orderId);
    refreshBreadcrumbs(orderId);
  };


  return (
    <Loading
      visible={loading}
      style={{ position: 'relative' }}
      className="arevision"
    >
      <ChangeOrderActions
        appId={appId}
        changeOrder={changeOrder}
        changeOrderId={changeOrderId}
        actionRefreshHook={setRefreshIndex}
        rollbackHook={rollbackBeenHook}
        approvalRecordId={approvalRecordId}
      />
      <ChangeOrderResume
        changeOrder={changeOrder}
        changeOrderId={changeOrderId}
      />
      <PipelineBatchPanel
        history={history}
        appId={appId}
        regionId={regionId}
        changeOrder={changeOrder}
        changeOrderId={changeOrderId}
        rollbackHook={rollbackBeenHook}
      />
    </Loading>
  )
};

export default ChangeOrderBase;
