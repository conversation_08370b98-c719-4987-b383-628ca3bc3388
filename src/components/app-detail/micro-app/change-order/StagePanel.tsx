import React, { useState, useEffect } from 'react';
import { map, get, head, noop } from 'lodash';
import { Timeline } from '@ali/cnd';
import StageStatus from './StageStatus';
import TaskPanel from './TaskPanel';
import { confFeature } from '@alicloud/console-one-conf';

const StagePanel = (props) => {
  const { history, appId, regionId, changeOrder = {}, stageList = [], pipelineId, rollbackHook = noop } = props;
  const [onlyStage, setOnlyStage] = useState(true);
  const [stage, setStage] = useState({});

  useEffect(() => {
    checkOnlyStage();
  }, [stageList]);

  const checkOnlyStage = () => {
    if (!stageList.length) return;
    let _stage = head(stageList);
    let _onlyStage = !(stageList.length > 1);
    setStage(_stage);
    setOnlyStage(_onlyStage);
  };

  return (
    <>
      <RcOneStage 
        stage={stage}
        show={onlyStage} 
      >
        <TaskPanel 
          regionId={regionId} 
          appId={appId} 
          history={history}
          changeOrder={changeOrder}
          stage={stage} 
          pipelineId={pipelineId} 
          rollbackHook={rollbackHook}
        />
      </RcOneStage>
      <RcBatchStage 
        show={!onlyStage} 
        stageList={stageList} 
        renderStage={stage => (
          <TaskPanel 
            regionId={regionId} 
            appId={appId} 
            history={history}
            changeOrder={changeOrder}
            stage={stage} 
            pipelineId={pipelineId} 
            rollbackHook={rollbackHook}
          />
        )}
      />
    </>
  )
}

export default StagePanel;

const RcOneStage = (props) => {
  const { show, stage, children } = props;

  const lifecycle_gray = confFeature('lifecycle_gray');
  
  if (!show) return null;
  if (lifecycle_gray) return children;

  return (
    <div className='astage-root'>
      {/* 左边步骤名称 */}
      <div className='astage-status'>
        <StageStatus value={get(stage, 'Status')} />
        <div 
          className='title' 
          style={{ color: '#0070cc' }}
        >
          <span>{get(stage, 'StageName')}</span>
        </div>
      </div>
      {/* 右边具体步骤 */}
      <div style={{ flex: 1 }}>{children}</div>
    </div>
  )
};

const RcBatchStage = (props) => {
  const { show, stageList, renderStage } = props;
  if (!show) return null;
  return (
    <Timeline className="astage-timeline">
      {
        map(stageList, (stage) => {
          const { Status: status } = stage;
          return (
            <Timeline.Item
              key={stage.StageId}
              title={
                <div style={{ color: '#0070cc' }}>
                  <span>{stage.StageName}</span>
                </div>
              }
              dot={<StageStatus value={status} />}
              content={renderStage(stage)}
            />
          );
        })
      }
    </Timeline>
  )
};