import React from 'react';
import { isEmpty } from 'lodash';
import { Truncate } from '@ali/cnd';

type Props = {
  value: string;
  limit?: number;
  link?: string;
};

class RcTruncate extends React.PureComponent<Props> {
  render() {
    const { value, limit, link = '' } = this.props;
    if (isEmpty(value)) return '--';
    return (
      <Truncate type="width" threshold={limit || 'auto'}>
        <span>{value}</span>
      </Truncate>
    );
  }
}

export default RcTruncate;