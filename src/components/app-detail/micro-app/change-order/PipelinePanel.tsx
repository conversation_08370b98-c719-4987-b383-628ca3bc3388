import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { get, includes, find, isEmpty, noop } from 'lodash';
import services from "~/services";
import { Loading } from '@ali/cnd';
import StagePanel from './StagePanel';
import EmptyContent from './EmptyContent';

const PipelinePanel = (props, ref) => {
  const {
    history,
    appId,
    regionId,
    changeOrder = {},
    changeOrderId,
    pipeline = {},
    rollbackHook = noop,
    updateShowBatch,
  } = props;
  const { PipelineId: pipelineId } = pipeline;
  const [loading, setLoading] = useState(false);
  const [stageList, setStageList] = useState([]);
  // 是否有下一批
  const [showBatch, setShowBatch] = useState(false);
  const timer = React.useRef(null);
  const finalCount = React.useRef(0);

  useEffect(() => {
    getAppPipeline();
    return () => {
      timer.current && clearInterval(timer.current);
      timer.current = null;
    };
  }, [JSON.stringify(pipeline)]);

  React.useImperativeHandle(ref, () => ({
    refreshPipeline: getAppPipeline,
  }));

  // 获取流水线包含的stageList
  const getAppPipeline = async () => {
    if (!pipelineId) return;
    setLoading(true);
    await probableLoopRefreshPipeline();
    setLoading(false);
  };

  const probableLoopRefreshPipeline = async () => {
    const res = await services.getAppPipeline({
      params: {
        ChangeOrderId: changeOrderId,
        PipelineId: pipelineId,
      },
      customErrorHandle(err, data, callback) {
        setLoading(false);
        timer.current && clearInterval(timer.current);
        timer.current = null;
        callback && callback();
      },
    });
    const { Data: _pipeline = {} } = res || {};
    const _stageList = get(_pipeline, 'StageList', []);
    const _showBatch = get(_pipeline, 'ShowBatch', false);
    // _status = 2、3、7、10是终态 不用轮训 手动分批发布情况下不用轮询
    const _status = get(_pipeline, 'PipelineStatus', 0);
    let finalStatus = includes([2, 3, 7, 10], _status) || get(changeOrder, 'Status', 0) === 8;
    if (_status === 6) {
      // pipeline 处于终止状态
      // 查下面stage，如果stage 有处于1、5的状态 就继续轮训
      finalStatus = isEmpty(
        find(_stageList, (item) => {
          return includes([1, 5], get(item, 'Status', 0));
        }),
      );
    }
    const isLoop = !finalStatus;
    if (isLoop) {
      // 不是终态
      finalCount.current = 0;
      if (!timer.current) {
        timer.current = setInterval(() => {
          probableLoopRefreshPipeline();
        }, 1000 * 5);
      }
    } else {
      // 是终态
      finalCount.current += 1;
      // 当连续两次是 终态时 取消计时器
      if (finalCount.current >= 2) {
        timer.current && clearInterval(timer.current);
        timer.current = null;
      }
    }
    setStageList(_stageList);
    setShowBatch(_showBatch);
    updateShowBatch({ [pipelineId]: _showBatch });
  };

  return (
    <Loading visible={loading} style={{ minHeight: 100 }} className="apipeline">
      <div>
        {isEmpty(stageList) ? (
          <EmptyContent
            tip={intl(
              'saenext.components.change-order.PipelinePanel.NoEligiblePipelineInformationIs',
            )}
          />
        ) : (
          <StagePanel
            regionId={regionId}
            appId={appId}
            history={history}
            changeOrder={changeOrder}
            stageList={stageList}
            pipelineId={pipelineId}
            rollbackHook={rollbackHook}
          />
        )}
      </div>
    </Loading>
  );
};

export default React.forwardRef(PipelinePanel);
