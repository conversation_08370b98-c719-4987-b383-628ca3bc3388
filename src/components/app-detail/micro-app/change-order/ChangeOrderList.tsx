import React from 'react';
import CndTable, { ISearch } from '@ali/cnd-table';
import { get, map } from 'lodash';
import { Actions } from '@ali/cnd';
import { intl } from '@ali/cnd';
import services from "~/services";
import ChangeOrderStatus from './ChangeOrderStatus';
import ChangeOrderMetric from './ChangeOrderMetric/index';
import RcTruncate from './RcTruncate';
import { objToDataSource } from '../../../../utils/transfer-data';
import { getParams, removeParams, setSearchParams } from '../../../../utils/global';
import { confFeature } from '@alicloud/console-one-conf';

const { LinkButton } = Actions;
const CoTypeCode = {
  CoBindSlb: intl('saenext.components.change-order.ChangeOrderList.BindSlb'),
  CoUnbindSlb: intl('saenext.components.change-order.ChangeOrderList.UnbindSlb'),
  CoUpdateSlb: intl('saenext.components.change-order.ChangeOrderList.EditSlb'),
  CoCreateApp: intl('saenext.components.change-order.ChangeOrderList.CreateAnApplication'),
  CoDeleteApp: intl('saenext.components.change-order.ChangeOrderList.DeleteAnApplication'),
  CoDeploy: intl('saenext.components.change-order.ChangeOrderList.DeployApplications'),
  CoRestartApplication: intl(
    'saenext.components.change-order.ChangeOrderList.RestartTheApplication',
  ),
  CoRollback: intl('saenext.components.change-order.ChangeOrderList.RollBackApplications'),
  CoRescaleApplicationVertically: intl(
    'saenext.components.change-order.ChangeOrderList.ModifyInstanceSpecifications',
  ),
  CoScaleIn: intl('saenext.components.change-order.ChangeOrderList.ApplicationScaling'),
  CoScaleOut: intl('saenext.components.change-order.ChangeOrderList.ApplicationExpansion'),
  CoStartApplication: intl('saenext.components.change-order.ChangeOrderList.StartTheApplication'),
  CoStopApplication: intl('saenext.components.change-order.ChangeOrderList.StopApplication'),
};

export const TypeDataSource = objToDataSource(CoTypeCode);

const ChangeOrderList = (props) => {
  const {
    regionId,
    appId,
    history,
    // @ts-ignore
    location: { search: urlSearch },
  } = props;
  
  const lifecycle_gray = confFeature('lifecycle_gray');

  const filterType = getParams('CoType');

  const fetchData = async (params) => {
    const { pageSize, current, order, orderBy, ...filterParams } = params;
    const orderParams = formatOrderParams({ order, orderBy });
    onSearch(filterParams);
    const res = await services.getAppChangeRecord({
      params: {
        ...filterParams,
        RegionId: regionId,
        AppId: appId,
        PageSize: pageSize,
        CurrentPage: current,
        ...orderParams,
      },
      customErrorHandle(err, data, callback) {
        callback && callback();
        return { data: [], total: 0 };
      },
    });
    const { Data = {} } = res || {};
    const { ChangeOrderList = [], TotalSize } = Data;
    return {
      data: map(ChangeOrderList, (item) => ({ ...item, primaryKey: item.ChangeOrderId })),
      total: TotalSize,
    };
  };

  const formatOrderParams = ({ order, orderBy }) => {
    if (!orderBy || order === 'default') {
      return {};
    }
    const orderByMap = {
      Duration: '`update_time` - `create_time`',
    };
    const reverseMap = {
      asc: false,
      desc: true,
    };
    const orderParams = {
      OrderBy: orderByMap[orderBy],
      Reverse: reverseMap[order],
    };
    return orderParams;
  };

  const onSearch = (params) => {
    const type = get(params, 'CoType');
    if (type) {
      setSearchParams({ CoType: type });
    } else {
      removeParams('CoType');
    }
    const { pathname, search } = window.location;
    history.push(pathname + search);
  };

  const columns = [
    // {
    //   key: 'ChangeOrderId',
    //   title: intl('saenext.namespace.revision.ChangeId'),
    //   dataIndex: 'ChangeOrderId',
    //   width: 300,
    //   lock: 'left',
    //   cell: (value) => (
    //     <Link
    //       to={`/${regionId}/app-list/${appId}/micro-app/record/${value}${urlSearch}`}
    //     >
    //       <Copy text={value}>{value}</Copy>
    //     </Link>
    //   ),
    // },
    {
      key: 'CreateTime',
      title: intl('saenext.namespace.revision.CreationTime'),
      dataIndex: 'CreateTime',
      width: 170,
    },
    {
      key: 'FinishTime',
      title: intl('saenext.namespace.revision.EndTime'),
      dataIndex: 'FinishTime',
      width: 170,
    },
    {
      key: 'Duration',
      title: intl('saenext.components.change-order.ChangeOrderList.ExecutionTimeSeconds'),
      dataIndex: 'Duration',
      sortable: true,
      sortDirections: ['asc', 'desc', 'default'],
      width: 140,
      cell: (value, idx, record) => {
        const { FinishTime, CreateTime } = record;
        if (!FinishTime || !CreateTime || FinishTime === '-' || CreateTime === '-') return '-';
        const finishTimestamp = new Date(FinishTime).getTime();
        const createTimestamp = new Date(CreateTime).getTime();
        return (finishTimestamp - createTimestamp) / 1000;
      },
    },
    {
      key: 'CoTypeCode',
      title: intl('saenext.namespace.revision.ChangeType'),
      dataIndex: 'CoTypeCode',
      width: 140,
      cell: (value, idx, record) => get(CoTypeCode, value) || record.CoType,
    },
    {
      key: 'Status',
      title: intl('saenext.namespace.revision.ChangeStatus'),
      dataIndex: 'Status',
      width: 180,
      cell: (value) => <ChangeOrderStatus value={value} />,
    },
    {
      key: 'Description',
      title: intl('saenext.namespace.revision.Description'),
      dataIndex: 'Description',
      width: 180,
      cell: (value) => <RcTruncate limit={140} value={value} />,
    },
    {
      key: 'Source',
      title: intl('saenext.namespace.revision.Source'),
      dataIndex: 'Source',
      width: 100,
    },
    {
      key: 'CreateUserId',
      title: intl('saenext.namespace.revision.Operator'),
      dataIndex: 'CreateUserId',
      width: 160,
      cell: (val, idx, record) => {
        const { CreateUserId, UserId } = record;
        const isPrimaryCount = CreateUserId === UserId;
        return (
          <span>
            {isPrimaryCount ? (
              <span>{intl('saenext.namespace.revision.PrimaryAccount')}</span>
            ) : (
              <RcTruncate limit={140} value={CreateUserId} />
            )}
          </span>
        );
      },
    },
    {
      key: 'ChangeOrderId',
      title: intl('saenext.components.change-order.ChangeOrderList.Operation'),
      dataIndex: 'ChangeOrderId',
      width: 120,
      lock: 'right',
      cell: (value) => {
        return (
          <Actions>
            <LinkButton
              onClick={() => {
                history.push(
                  `/${regionId}/app-list/${appId}/micro-app/record/${value}${urlSearch}`,
                );
              }}
            >
              {intl('saenext.components.change-order.ChangeOrderList.ChangeDetails')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];

  const search = {
    defaultDataIndex: 'Key',
    defaultSelectedDataIndex: 'Key',
    onlySupportOne: false,
    options: [
      {
        label: intl('saenext.components.change-order.ChangeOrderList.ChangeType'),
        dataIndex: 'CoType',
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.components.change-order.ChangeOrderList.SelectAChangeType'),
          dataSource: TypeDataSource,
        },
        defaultValue: filterType,
      },
      {
        label: intl('saenext.components.change-order.ChangeOrderList.ChangeStatus'),
        dataIndex: 'CoStatus',
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.components.change-order.ChangeOrderList.SelectChangeStatus'),
          dataSource: [
            {
              label: intl('saenext.components.change-order.ChangeOrderList.NotExecuted'),
              value: '0',
            },
            { label: intl('saenext.components.change-order.ChangeOrderList.Running'), value: '1' },
            {
              label: intl('saenext.components.change-order.ChangeOrderList.SuccessfulExecution'),
              value: '2',
            },
            {
              label: intl('saenext.components.change-order.ChangeOrderList.ExecutionFailed'),
              value: '3',
            },
            {
              label: intl('saenext.components.change-order.ChangeOrderList.ExecutionTermination'),
              value: '6',
            },
          ],
        },
        defaultValue: '',
      },
      {
        label: intl('saenext.components.change-order.ChangeOrderList.ChangeDescription'),
        dataIndex: 'Key',
        template: 'input',
        templateProps: {
          placeholder: intl(
            'saenext.components.change-order.ChangeOrderList.EnterAChangeDescription',
          ),
        },
      },
    ],
  };

  return (
    <>
      {lifecycle_gray && <ChangeOrderMetric regionId={regionId} appId={appId} />}

      <CndTable
        key={`filterType-${filterType}`}
        primaryKey="ChangeOrderId"
        columns={columns as []}
        fetchData={fetchData as any}
        recordCurrent
        showRefreshButton
        pagination={{
          hideOnlyOnePage: true,
          pageSizeList: [10, 20, 50, 100],
        }}
        search={search as ISearch}
      />
    </>
  );
};

export default ChangeOrderList;
