import React, { useMemo } from 'react';
import { Wbar } from '@alicloud/cloud-charts';
import { intl, Select } from '@ali/cnd';
import { TypeDataSource } from '../ChangeOrderList';
import { chain, cloneDeep, meanBy, reverse, round, toPairs } from 'lodash';

const TaskCost = (props) => {
  const { data: costData, type, setType } = props;

  const data = useMemo(() => {
    return (
      chain(costData)
        // 将对象转换为 [key, value] 对数组
        .map((obj) => toPairs(obj)[0])
        // 按照 key 分组
        .groupBy(0)
        // 计算每组的平均值
        .map((group, key) => [
          key,
          round(
            meanBy(group, (item) => (item[1] as number) / 1000), // 除以1000转换为秒再取组内平均
            1,
          ),
        ])
        // 按照值（第二个元素）排序
        .sortBy(1)
        // 取最后5个
        .takeRight(5)
        .value()
    );
  }, [costData]);

  const dataSource = [
    {
      name: '',
      data,
    },
  ];

  const labelData = reverse(cloneDeep(data) as any);

  const options = {
    appendPadding: [0, 40, 0, 0],
    column: false,
    legend: {
      visible: false,
    },
    label: {
      visible: false,
    },
    columnWidthRatio: 0.2,
  };

  return (
    <>
      <div className="flex">
        <div className="text-bold mr" style={{ fontSize: 14 }}>
          {intl('saenext.change-order.ChangeOrderMetric.TaskCost.Top')}
        </div>
        <Select size="small" dataSource={TypeDataSource} value={type} onChange={setType} />
      </div>
      <div>
        <Wbar height={164} config={options} data={dataSource}>
          <div className="text-label">
            {labelData.map((item, i) => (
              <div className="block">
                <span className="text-bold mr-xs">{item[1]}</span>
                {intl('general.second')}
              </div>
            ))}
          </div>
        </Wbar>
      </div>
    </>
  );
};

export default TaskCost;
