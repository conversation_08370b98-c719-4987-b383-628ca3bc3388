import { intl } from '@ali/cnd';
import React from 'react';
import { Wbar, Wnumberoverview } from '@alicloud/cloud-charts';
import { isEmpty, round } from 'lodash';
import { Select } from '@ali/cnd';
import { TypeDataSource } from '../ChangeOrderList';

const OrderCount = (props) => {
  const { data, type, setType } = props;
  const { Error, ErrorPercent, Total, AvgTimeCostMs, MaxTimeCostMs } = data || {};

  const percentData = !isEmpty(data)
    ? [
        {
          name: intl('saenext.change-order.ChangeOrderMetric.OrderCount.Success'),
          data: [
            [
              intl('saenext.change-order.ChangeOrderMetric.OrderCount.TotalTotal', {
                Total: Total,
              }),

              Total - Error,
            ],
          ],
        },
        {
          name: intl('saenext.change-order.ChangeOrderMetric.OrderCount.Failed'),
          data: [
            [
              intl('saenext.change-order.ChangeOrderMetric.OrderCount.TotalTotal', {
                Total: Total,
              }),

              Error,
            ],
          ],
        },
      ]
    : [];

  const options = {
    stack: true,
    // showStackSum: true,
    column: false,
    xAxis: {
      visible: false,
    },
    yAxis: {
      visible: false,
      min: 0,
      maxLimit: Total,
    },
    label: {
      visible: false,
    },
    legend: {
      nameFormatter: (name, data, index) => {
        const count =
          name === intl('saenext.change-order.ChangeOrderMetric.OrderCount.Success')
            ? Total - Error
            : Error;
        return `${name} ${count}`;
      },
    },
    columnWidthRatio: 0.3,
    colors: ['#6453F9', '#D50E1A'],
  };

  const overviewData = !isEmpty(data)
    ? [
        {
          label: intl('saenext.change-order.ChangeOrderMetric.OrderCount.ReleaseSuccessRate'),
          value: round((1 - ErrorPercent) * 100),
          unit: '%',
        },
        {
          label: intl('saenext.change-order.ChangeOrderMetric.OrderCount.AveragePublishingTime'),
          value: round(AvgTimeCostMs / 1000),
          unit: intl('saenext.change-order.ChangeOrderMetric.OrderCount.Seconds'),
        },
        {
          label: intl('saenext.change-order.ChangeOrderMetric.OrderCount.MaximumTimeToPublish'),
          value: round(MaxTimeCostMs / 1000),
          unit: intl('saenext.change-order.ChangeOrderMetric.OrderCount.Seconds'),
        },
      ]
    : ([] as any);

  return (
    <>
      <div className="flex">
        <div className="text-bold mr" style={{ fontSize: 14 }}>
          {intl('saenext.change-order.ChangeOrderMetric.OrderCount.NumberOfPublishedOrders')}
        </div>
        <Select size="small" dataSource={TypeDataSource} value={type} onChange={setType} />
      </div>
      <div className="relative">
        <Wbar height={80} config={options} data={percentData} />

        <div className="absolute" style={{ right: 0, bottom: 0, color: '#808080' }}>
          {intl('saenext.change-order.ChangeOrderMetric.OrderCount.Total')}
          {Total}
        </div>
      </div>
      <div className="mt">
        <Wnumberoverview data={overviewData} />
      </div>
    </>
  );
};

export default OrderCount;
