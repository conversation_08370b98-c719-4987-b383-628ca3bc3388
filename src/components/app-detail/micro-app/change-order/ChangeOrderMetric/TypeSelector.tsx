import { intl } from '@ali/cnd';
import React from 'react';
import { RadioGroup } from '@ali/cnd';

const TypeSelector = (props) => {
  const list = [
    {
      label: intl('saenext.change-order.ChangeOrderMetric.TypeSelector.Start'),
      value: 'CoStartApplication',
    },
    {
      label: intl('saenext.change-order.ChangeOrderMetric.TypeSelector.Deployment'),
      value: 'CoDeploy',
    },
    {
      label: intl('saenext.change-order.ChangeOrderMetric.TypeSelector.Stop'),
      value: 'CoStopApplication',
    },
    {
      label: intl('saenext.change-order.ChangeOrderMetric.TypeSelector.Shrinkage'),
      value: 'CoScaleIn',
    },
  ];

  return <RadioGroup size="small" dataSource={list} shape="button" {...props} />;
};

export default TypeSelector;
