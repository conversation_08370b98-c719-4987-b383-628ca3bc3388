import React, { useEffect, useState } from 'react'
import OrderCount from './OrderCount';
import TaskCost from './TaskCost';
import services from '../../../../../services';
import dayjs from 'dayjs';
import { jsonParse } from '../../../../../utils/transfer-data';
import { getParams, removeParams, setSearchParams } from '../../../../../utils/global';
import { Loading } from '@alifd/next';
import './index.less';
import { useHistory } from '@ali/cnd';

const ChangeOrderMetric = (props) => {
  const {
    regionId,
    appId,
  } = props;

  const defaultType = getParams('CoType') || 'CoDeploy';

  const [metricData, setMetricData] = useState<any>({});
  const [taskTimeCost, setTaskTimeCost] = useState<any>({});
  const [type, setType] = useState<string>(defaultType);
  const [loading, setLoading] = useState<boolean>(false);

  const history = useHistory();

  useEffect(() => {
    setType(defaultType);
  }, [defaultType])
  
  useEffect(() => {
    return () => {
      removeParams('CoType');
    }
  }, [])

  useEffect(() => {
    fetchChangeOrderMetric()
  }, [type])

  const fetchChangeOrderMetric = async () => {
    setLoading(true);
    const res = await services.getChangeOrderErrorMetric({
      RegionId: regionId as string,
      AppId: appId as string,
      CreateTime: dayjs().subtract(7, 'days').valueOf(),
      Limit: 100,
      CoType: type,
      OrderBy: '',
    })
    setLoading(false);
    if (!res?.Success) return;
    const { Data = [] } = res || {}
    setMetricData(Data[0]);

    const {
      TaskTimeCostMsAvg
    } = Data[0] || {};
    const taskTimeCost = jsonParse(TaskTimeCostMsAvg);
    setTaskTimeCost(taskTimeCost);
  }

  const onChangeType = (value) => {
    setType(value);
    setSearchParams({
      CoType: value,
    })
    const { pathname, search } = window.location;
    history.push(pathname + search);
  }

  return (
    <Loading visible={loading} className='full-width'>
      <div className='flex item-start border pd-card mb-l'>
        <div className='w-50'>
          <OrderCount data={metricData} type={type} setType={onChangeType} />
        </div>
        <div className='task-cost ml-xxxl w-50'>
          <TaskCost data={taskTimeCost} type={type} setType={onChangeType} />
        </div>
      </div>
    </Loading>
  )
}

export default ChangeOrderMetric