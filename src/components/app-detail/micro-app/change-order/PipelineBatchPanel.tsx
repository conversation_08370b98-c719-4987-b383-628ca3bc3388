import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import services from "~/services";
import { get, map, head, includes, isEmpty, noop } from 'lodash';
import { Tab, Dialog, Message, Icon } from '@ali/cnd';
import PipelinePanel from './PipelinePanel';
import EmptyContent from './EmptyContent';
import { RcButton } from './ChangeOrderActions';

const PipelineBatchPanel = (props) => {
  const { history, regionId, appId, changeOrder = {}, changeOrderId, rollbackHook = noop } = props;
  const [pipelines, setPipelines] = useState([]);
  const [activePipelineId, setCurrentPipelineId] = useState('');
  // 变更单是否审批流程的状态
  const [isApprovalStatus, setIsApprovalStatus] = useState(true);
  // 是否有下一批
  const [showBatch, setShowBatch] = useState({});
  const pipelinePanelRef = React.useRef(null);

  useEffect(() => {
    initializrPipeline();
  }, [changeOrder]);

  const initializrPipeline = () => {
    let _pipelines = get(changeOrder, 'Pipelines', []);
    const _activePipelineId =
      get(changeOrder, 'CurrentPipelineId') || get(head(_pipelines), 'PipelineId');
    const _status = get(changeOrder, 'Status', 0);
    const _isApprovalStatus = includes([11, 12], _status);
    setPipelines(_pipelines);
    setCurrentPipelineId(_activePipelineId);
    setIsApprovalStatus(_isApprovalStatus);
  };

  // 开始下一批
  const handleBatchPipeline = () => {
    // 当前的流水线
    const pipelineId = activePipelineId || get(head(pipelines), 'PipelineId');

    let nextPipelineId;
    for (let i = 0; i < pipelines.length; i++) {
      if (pipelines[i].PipelineId === pipelineId) {
        const nextPipeline = pipelines[i + 1];
        if (nextPipeline) {
          nextPipelineId = nextPipeline.PipelineId;
          break;
        }
      }
    }
    if (isEmpty(nextPipelineId)) return;
    Dialog.alert({
      title: intl('saenext.components.change-order.PipelineBatchPanel.StartTheNextBatch'),
      content: (
        <p>{intl('saenext.components.change-order.PipelineBatchPanel.TheCurrentChangeOrderIs')}</p>
      ),

      onOk: () =>
        new Promise(async (resolve, reject) => {
          const res = await services.getNextBatchPipeline({
            params: {
              Confirm: true,
              PipelineId: nextPipelineId,
            },
            customErrorHandle: (error, data, callback) => {
              callback && callback();
              reject(false);
            },
          });
          const pipelineId = get(res, 'Data.PipelineId');
          if (!isEmpty(pipelineId)) {
            resolve(true);
            nextPipelineHook(pipelineId);
          } else {
            Message.error(
              intl('saenext.components.change-order.PipelineBatchPanel.ManualConfirmationFailed'),
            );
            reject(false);
          }
        }),
    });
  };

  // 开始下一批回调
  // 设置当前流水线为下一个 并且继续轮训
  const nextPipelineHook = (pipelineId) => {
    // 将之前的流水线的下一批显示为不可见
    setCurrentPipelineId((prePipelineId) => {
      let _prePipelineId = prePipelineId || get(head(pipelines), 'PipelineId');
      setShowBatch({ ...showBatch, [_prePipelineId]: false });
      return pipelineId;
    });
  };

  if (isApprovalStatus) {
    return (
      <EmptyContent
        tip={intl(
          'saenext.components.change-order.PipelineBatchPanel.NoEligiblePipelineInformationIs',
        )}
      />
    );
  }
  return (
    <Tab
      shape="wrapped"
      style={{ marginTop: 16 }}
      activeKey={activePipelineId}
      // @ts-ignore
      onChange={(val) => setCurrentPipelineId(val)}
      extra={
        <>
          <RcButton type="primary" show={showBatch[activePipelineId]} onClick={handleBatchPipeline}>
            {intl('saenext.components.change-order.PipelineBatchPanel.StartTheNextBatch')}
          </RcButton>
          <RcButton
            // type='primary'
            show={true}
            style={{ marginLeft: 8 }}
            onClick={() => {
              pipelinePanelRef.current && pipelinePanelRef.current.refreshPipeline();
            }}
          >
            <Icon type="refresh" />
          </RcButton>
        </>
      }
    >
      {map(pipelines, (pipeline) => {
        return (
          <Tab.Item key={pipeline.PipelineId} title={pipeline.PipelineName}>
            <PipelinePanel
              ref={pipelinePanelRef}
              regionId={regionId}
              appId={appId}
              history={history}
              changeOrder={changeOrder}
              changeOrderId={changeOrderId}
              pipeline={pipeline}
              rollbackHook={rollbackHook}
              updateShowBatch={(val) => setShowBatch({ ...showBatch, ...val })}
            />
          </Tab.Item>
        );
      })}
    </Tab>
  );
};

export default PipelineBatchPanel;
