import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { CndForm } from '@ali/cnd';
import { get, isEqual, isEmpty, includes, noop } from 'lodash';
import RcTruncate from './RcTruncate';
import ChangeOrderStatus from './ChangeOrderStatus';
import CachedData from '../../../../cache/common';

const CoTypeCode = {
  CoBindSlb: intl('saenext.components.change-order.ChangeOrderResume.BindSlb'),
  CoUnbindSlb: intl('saenext.components.change-order.ChangeOrderResume.UnbindSlb'),
  CoUpdateSlb: intl('saenext.components.change-order.ChangeOrderResume.EditSlb'),
  CoCreateApp: intl('saenext.components.change-order.ChangeOrderResume.CreateAnApplication'),
  CoDeleteApp: intl('saenext.components.change-order.ChangeOrderResume.DeleteAnApplication'),
  CoDeploy: intl('saenext.components.change-order.ChangeOrderResume.DeployApplications'),
  CoRestartApplication: intl(
    'saenext.components.change-order.ChangeOrderResume.RestartTheApplication',
  ),
  CoRollback: intl('saenext.components.change-order.ChangeOrderResume.RollBackApplications'),
  CoRescaleApplicationVertically: intl(
    'saenext.components.change-order.ChangeOrderResume.ModifyInstanceSpecifications',
  ),
  CoScaleIn: intl('saenext.components.change-order.ChangeOrderResume.ApplicationScaling'),
  CoScaleOut: intl('saenext.components.change-order.ChangeOrderResume.ApplicationExpansion'),
  CoStartApplication: intl('saenext.components.change-order.ChangeOrderResume.StartTheApplication'),
  CoStopApplication: intl('saenext.components.change-order.ChangeOrderResume.StopApplication'),
};

const ChangeOrderResume = (props) => {
  const { changeOrder, changeOrderId } = props;
  const [failStatus, setFailStatus] = useState(false);

  useEffect(() => {
    if (isEmpty(changeOrder)) return;
    const status = get(changeOrder, 'Status', 0);
    const _failStatus = includes([3, 10], status);
    setFailStatus(_failStatus);
  }, [changeOrder]);

  return (
    <CndForm
      formType="detail"
      dataSource={changeOrder}
      items={[
        {
          dataIndex: 'ChangeOrderId',
          label: intl('saenext.components.change-order.ChangeOrderResume.ChangeProcessId'),
          span: 8
        },
        {
          dataIndex: 'BatchCount',
          label: intl('saenext.components.change-order.ChangeOrderResume.NumberOfPublishedBatches'),
          span: 8,
          render: value => value || '--',
        },
        {
          dataIndex: 'BatchType',
          label: intl('saenext.components.change-order.ChangeOrderResume.InterbatchProcessing'),
          span: 8,
          render: value => value || '--',
        },
        {
          dataIndex: 'Status',
          label: intl('saenext.components.change-order.ChangeOrderResume.ExecutionStatus'),
          span: 8,
          render: value => {
            return (
              <div className="content-text">
                {get(changeOrder, 'Status') >= 0 ? (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <ChangeOrderStatus
                      value={get(changeOrder, 'Status')}
                      isImplementationFailed={isEqual(get(changeOrder, 'SubStatus'), 1)}
                      supportConfirmSuccess={changeOrder.SupportConfirmSuccess}
                    />

                    <>
                      {failStatus ? (
                        <a
                          href={CachedData.confLink('help:sae:troubleshoot-error-related-to-change-orders')}
                          target="__blank"
                          style={{ marginLeft: 4 }}
                        >
                          {intl(
                            'saenext.components.change-order.ChangeOrderResume.TroubleshootingGuideForChangeProcess',
                          )}
                        </a>
                      ) : null}
                    </>
                  </div>
                ) : (
                  <span>--</span>
                )}
              </div>
            );
          },
        },
        {
          dataIndex: 'CoTypeCode',
          label: intl('saenext.components.change-order.ChangeOrderResume.ChangeType'),
          span: 8,
          render: value => get(CoTypeCode, value) || '--',
        },
        {
          dataIndex: 'CreateTime',
          label: intl('saenext.components.change-order.ChangeOrderResume.ReleaseTime'),
          span: 8,
          render: value => value || '--',
        },
        {
          dataIndex: 'Description',
          label: intl('saenext.components.change-order.ChangeOrderResume.Description'),
          span: 8,
          render: value => {
            return ( value ? <div>{get(changeOrder, 'Description')}</div> : '--')
          }
        },
        {
          dataIndex: 'CoTargets',
          label: intl('saenext.components.change-order.ChangeOrderResume.ChangeObject'),
          span: 8,
          render: value => {
            return ( value.length ? <RcTruncate value={get(changeOrder, 'CoTargets').join(',')} /> : '--')
          }
        }
      ] as any}
    />
  );
};

export default ChangeOrderResume;
