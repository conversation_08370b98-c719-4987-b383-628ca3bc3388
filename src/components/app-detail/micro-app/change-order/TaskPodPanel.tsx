import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import CndTable, { ISearch } from '@ali/cnd-table';
import services from "~/services";
import TaskPodStatus from './TaskPodStatus';
import TaskAppPanel from './TaskAppPanel';
import { Button, Dialog, Message } from '@ali/cnd';
import { map, get, filter, isEmpty, noop } from 'lodash';

const TaskPodPanel = (props) => {
  const {
    history,
    regionId,
    appId,
    changeOrder = {},
    stage,
    pipelineId,
    rollbackHook = noop,
  } = props;
  const stageId = get(stage, 'StageId');
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [failurePods, setFailurePods] = useState([]);

  useEffect(() => {
    setRefreshIndex(Date.now());
  }, [JSON.stringify(stage)]);

  const getStageInstances = async (params) => {
    const res = await services.getStageInstances({
      params: {
        PipelineId: pipelineId,
        StageId: stageId,
        PageSize: params.pageSize,
        CurrentPage: params.current,
        QueryStatus: params.QueryStatus === 'all' ? '' : Number(params.QueryStatus),
      },
      customErrorHandle(err, data, callback) {
        callback && callback();
        return {
          data: [],
          total: 0,
        };
      },
    });
    const { Data = {} } = res || {};
    const { InstanceList = [], TotalSize: _totalSize } = Data;
    const _failurePods = filter(InstanceList, (item) => item.Status === 3);
    setFailurePods(_failurePods);
    return {
      data: map(InstanceList, (item) => ({ ...item, primaryKey: item.InstanceId })),
      total: _totalSize,
    };
  };

  const handleRestartPod = () => {
    const coTypeCode = get(changeOrder, 'CoTypeCode', 'CoRestartInstances');
    const handleMethods = {
      CoRestartInstances: {
        title: intl('saenext.components.change-order.TaskPodPanel.RestartTheInstance'),
        content: intl('saenext.components.change-order.TaskPodPanel.AreYouSureYouWant'),
        service: services.restartPodInstances,
        success: () =>
          Message.success(
            intl('saenext.components.change-order.TaskPodPanel.RestartSucceededChangeOrderNumber'),
          ),
        fail: () =>
          Message.success(
            intl('saenext.components.change-order.TaskPodPanel.FailedToRestartTheInstance'),
          ),
      },
      CoDeleteInstances: {
        title: intl('saenext.components.change-order.TaskPodPanel.DeleteAnInstance'),
        content: intl('saenext.components.change-order.TaskPodPanel.AfterDeletingTheInstanceThe'),
        service: services.deletePodInstances,
        success: () =>
          Message.success(
            intl('saenext.components.change-order.TaskPodPanel.DeletedChangeOrderNumberOrderid'),
          ),
        fail: () =>
          Message.success(
            intl('saenext.components.change-order.TaskPodPanel.FailedToDeleteTheInstance'),
          ),
      },
    };
    const execTask = handleMethods[coTypeCode];
    if (isEmpty(execTask)) return;

    Dialog.alert({
      title: execTask.title,
      content: execTask.content,
      onOk: () =>
        new Promise(async (resolve, reject) => {
          const res = await execTask.services({
            params: {
              AppId: appId,
              InstanceIds: map(failurePods, (pod) => pod.InstanceId).join(','),
            },
            customErrorHandle: (error, data, callback) => {
              callback && callback();
              reject(false);
            },
          });
          if (res?.Succes) {
            execTask.success();
            // 请求新的变更单
            resolve(true);
          } else {
            execTask.fail();
            reject(false);
          }
        }),
    });
  };

  const columns = [
    {
      key: 'InstanceId',
      title: intl('saenext.components.change-order.TaskPodPanel.InstanceId'),
      width: '44%',
      dataIndex: 'InstanceId',
    },
    {
      key: 'Status',
      title: intl('saenext.components.change-order.TaskPodPanel.RunningStatus'),
      width: '28%',
      dataIndex: 'Status',
      cell: (value) => <TaskPodStatus value={value} />,
    },
    {
      key: 'Cpu',
      title: intl('saenext.components.change-order.TaskPodPanel.ConfigurationSpecifications'),
      width: '28%',
      dataIndex: 'Cpu',
      cell: (value, index, record) => {
        const cpu = get(record, 'Cpu', '-');
        const memory = get(record, 'Memory', '-');
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>
              <span style={{ color: '#555' }}>CPU: </span>
              {intl('saenext.components.change-order.TaskPodPanel.CpuCore', { cpu: cpu })}
            </span>
            <span>
              <span style={{ color: '#555', marginLeft: 16 }}>
                {intl('saenext.components.change-order.TaskPodPanel.Memory')}
              </span>
              {`${memory} MB`}
            </span>
          </div>
        );
      },
    },
  ];

  const search = {
    defaultDataIndex: 'QueryStatus',
    defaultSelectedDataIndex: 'QueryStatus',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.components.change-order.TaskPodPanel.ExecutionStatus'),
        dataIndex: 'QueryStatus',
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.components.change-order.TaskPodPanel.SelectExecutionStatus'),
          // 测试发现 value 为数值型的时候 defalutValue 不生效， 提交时进行转换
          dataSource: [
            { label: intl('saenext.components.change-order.TaskPodPanel.All'), value: 'all' },
            { label: intl('saenext.components.change-order.TaskPodPanel.Ready'), value: '0' },
            { label: intl('saenext.components.change-order.TaskPodPanel.Running'), value: '1' },
            { label: intl('saenext.components.change-order.TaskPodPanel.Success'), value: '2' },
            { label: intl('saenext.components.change-order.TaskPodPanel.Failed'), value: '3' },
            { label: intl('saenext.components.change-order.TaskPodPanel.Skip'), value: '4' },
            {
              label: intl('saenext.components.change-order.TaskPodPanel.WaitingForConfirmation'),
              value: '5',
            },
            { label: intl('saenext.components.change-order.TaskPodPanel.Termination'), value: '6' },
          ],
        },
        defaultValue: 'all',
      },
    ],

    children: (
      <div style={{ marginRight: 8 }}>
        <Button type="primary" disabled={isEmpty(failurePods)} onClick={handleRestartPod}>
          {intl('saenext.components.change-order.TaskPodPanel.InstancesWithFailedBatchRetry')}
        </Button>
      </div>
    ),
  };

  return (
    <div className="astage-apanel">
      <CndTable
        primaryKey="primaryKey"
        columns={columns as []}
        isZebra={false}
        className="apod-apanel"
        // hasBorder={true}
        fetchData={getStageInstances as any}
        recordCurrent
        refreshIndex={refreshIndex}
        showRefreshButton
        pagination={{
          hideOnlyOnePage: true,
          pageSizeList: [20, 50, 100],
        }}
        search={search as ISearch}
        expandedRowRender={(record) => {
          const taskList = get(record, 'TaskList', []);
          return (
            <TaskAppPanel
              regionId={regionId}
              appId={appId}
              history={history}
              pipelineId={pipelineId}
              stage={{ TaskList: taskList }}
            />
          );
        }}
      />
    </div>
  );
};

export default TaskPodPanel;
