import { Icon } from '@ali/cnd';
import React, { useEffect, useState } from 'react'
import If from '../../../../shared/If';

const FloatCollapse = (props) => {
  const { defaultOpen = false, onClick, children } = props;
  const [opened, setOpened] = useState(defaultOpen); // 是否打开过，用来懒加载
  const [show, setShow] = useState(defaultOpen);

  const showClass = show ? 'block' : 'none'

  useEffect(() => {
    if (show) {
      setOpened(true);
    }
  }, [show])

  const onClickHandler = () => {
    setShow(!show)
    onClick && onClick();
  }
    
  return (
    <>
      <div
          className='collapse-icon pointer'
          style={{ position: 'absolute', left: 4, top: 8 }}
          onClick={onClickHandler}
        >
          {
            show ?
              <Icon
                type="caret-down"
                size='xs'
              />
              :
              <Icon
                type="caret-right"
                size='xs'
              />
          }
        </div>
        <If condition={opened}>
          <div className={`${showClass} pl`}>
            {children}
          </div>
        </If>
    </>
  )
}

export default FloatCollapse