import React, { useState } from 'react';
import { CndStatus, Copy, intl, Link } from '@ali/cnd';
import { StatusMap } from '../TaskAppStatus';
import TaskBar from './TaskBar';
import TaskInstances from './TaskInstances';
import TaskInstanceLife from './TaskInstanceLife';
import If from '../../../../shared/If';
import FloatCollapse from './FloatCollapse';
import './index.less';

const TaskItem = (props) => {
  const { task, regionId, appId, pipelineId, start, end, barColor } = props;

  const isDeploy =
    task.TaskName ===
    intl('saenext.change-order.TaskAppPanel.TaskItem.PerformApplicationDeployment');

  const containerEvents = ['Created', 'Started', 'Running', 'Ready', 'Unhealthy', 'Failed', 'BackOff'];

  const [customTimeRange, setCustomTimeRange] = useState({
    StartTime: null,
    UpdateTime: null,
  });
  const [instanceStatus, setInstanceStatus] = useState(null);

  const [classOpen, setClassOpen] = useState(isDeploy ? 'open' : 'close');

  const toggleOpen = () => {
    setClassOpen(classOpen === 'close' ? 'open' : 'close');
  };
  
  const formatMessage = (message = '') => {
    const lines = message.split('<br/>');
    return lines.map((line, index) => (
      <>
        {line}
        {index < lines.length - 1 && <br />}
      </>
    ));
  };

  const {
    TaskId,
    TaskName,
    Status,
    Message,
    StartTime,
    UpdateTime,
    EventStatus,
    name,
    instanceId,
    type,
    icon = 'success',
    color = '#06b624',
    messageColor = '',
  } = task;
  const actualStatus = instanceStatus || Status;
  const status = StatusMap[actualStatus];
  const eventColor = containerEvents.includes(EventStatus) ?
      ['#4C7FE6']
      : 'black';
  const barColors =
    actualStatus === 3 || actualStatus === 6
      ? ['red']
      // : isDeploy
      // ? ['#72E3DC']
      // : pipelineId
      // ? [barColor]
      : undefined;
  return (
    <div className="task-item-box relative border-t">
      <div
        className={`task-item task-item-${classOpen} pt-s pb-s pl-xl flex justify-between item-start`}
      >
        <div className="task-item-info" style={{ width: '30%' }}>
          <div className="flex">
            <CndStatus
              type={status?.icon || icon}
              // @ts-ignore
              label={<span className="text-bold" style={{ color: eventColor }}>{TaskName || name}</span>}
            />

            <span className="ml-s mr-s" style={{ color: status?.messageColor || color }}>
              {status?.label}
            </span>
            {pipelineId && Status === 3 && (
              <Link to={`/${regionId}/app-list/${appId}/micro-app/base?tab=instance`}>
                {intl('saenext.components.change-order.TaskAppPanel.ViewInstanceInformation')}
              </Link>
            )}
          </div>
          <span
            className="text-description"
            style={{ color: status?.messageColor || messageColor }}
          >
            {formatMessage(Message)}
          </span>
          <If condition={Message}>
            <Copy text={Message} showIcon />
          </If>
        </div>
        <div style={{ width: '60%' }}>
          <TaskBar
            start={start}
            end={end}
            StartTime={StartTime || customTimeRange.StartTime}
            UpdateTime={UpdateTime || customTimeRange.UpdateTime}
            colors={barColors}
          />
        </div>
      </div>
      <If condition={isDeploy}>
        <FloatCollapse defaultOpen onClick={toggleOpen}>
          <TaskInstances
            appId={appId}
            pipelineId={pipelineId}
            start={start}
            end={end}
            pipelineStatus={Status}
          />
        </FloatCollapse>
      </If>
      <If condition={type === 'instance'}>
        <FloatCollapse onClick={toggleOpen}>
          <TaskInstanceLife
            regionId={regionId}
            appId={appId}
            instanceId={instanceId}
            start={start}
            end={end}
            setCustomTimeRange={setCustomTimeRange}
            setInstanceStatus={setInstanceStatus}
            pipelineStatus={Status}
          />
        </FloatCollapse>
      </If>
    </div>
  );
};

export default TaskItem;
