import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import services from "~/services";
import { get, isEmpty, map, sortBy } from 'lodash';
import TaskItem from './TaskItem';
import { Loading } from '@ali/cnd';

const TaskInstances = (props) => {
  const { regionId, appId, pipelineId, start, end, pipelineStatus } = props;

  const [loading, setLoading] = useState(false);
  const [instances, setInstances] = useState([]);

  useEffect(() => {
    getPipelineInstances();
  }, [start, end, pipelineStatus]);

  const getPipelineInstances = async () => {
    setLoading(true);
    const res = await services.getApplicationInstances({
      params: {
        AppId: appId,
        GroupId: '',
        PipelineId: pipelineId,
        CurrentPage: 1,
        PageSize: 100,
        Reverse: false,
      },
    });
    setLoading(false);
    if (!res?.Success) return;
    const instances = get(res, 'Data.Instances', []);
    const instancesData = sortBy(instances, ['CreateTimeStamp']);
    const instanceArr = map(instancesData, (instance, index) => {
      const { InstanceId } = instance;
      return {
        ...instance,
        name: intl('saenext.change-order.TaskAppPanel.TaskInstances.PublishInstanceIndex', {
          index: index,
        }),
        Status: pipelineStatus,
        Message: InstanceId,
        instanceId: InstanceId,
        type: 'instance',
      };
    });
    setInstances(instanceArr);
  };

  return (
    <Loading visible={loading} className="full-width">
      {map(instances, (instance) => (
        <TaskItem
          key={instance.InstanceId}
          task={instance}
          regionId={regionId}
          appId={appId}
          start={start}
          end={end}
          defaultOpen
        />
      ))}
      {isEmpty(instances) && (
        <div className="border pt pb" style={{ textAlign: 'center' }}>
          {intl('saenext.change-order.TaskAppPanel.TaskInstances.NoData')}
        </div>
      )}
    </Loading>
  );
};

export default TaskInstances;
