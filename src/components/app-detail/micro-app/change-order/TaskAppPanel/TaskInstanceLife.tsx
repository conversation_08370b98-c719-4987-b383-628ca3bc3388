import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import services from "~/services";
import { get, map } from 'lodash';
import TaskItem from './TaskItem';
import { Loading } from '@ali/cnd';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { InsEventMap } from '../../basic-info/InstanceDetail/constants';

const PendingMessage = intl(
  'saenext.change-order.TaskAppPanel.TaskInstanceLife.PlatformSideOperationsSuchAs',
);

const TaskInstanceLife = (props) => {
  const {
    regionId,
    appId,
    instanceId,
    start,
    end,
    setCustomTimeRange,
    setInstanceStatus,
    pipelineStatus,
  } = props;

  const [loading, setLoading] = useState(false);
  const [lifeData, setLifeData] = useState([]);

  useEffect(() => {
    getLifecycleData();
  }, [start, end]);

  const getLifecycleData = async () => {
    setLoading(true);
    const res = await services.DescribeInstanceLifecycleTimeline({
      params: {
        RegionId: regionId,
        AppId: appId,
        InstanceId: instanceId,
        FromTime: dayjs(start).unix(),
        ToTime: dayjs(end).unix(),
      },
    });
    setLoading(false);
    if (!res?.Success) return;
    const { Data = [] } = res || {};
    Data.sort((a, b) => a.Timestamp - b.Timestamp);
    let ready = false;
    const data = Data.map((item) => {
      const { EventLevel, EventStatus, Timestamp, LastTimestamp, InstanceReadyStatus, Message } =
        item;
      const { PodReady, ContainersReady } = InstanceReadyStatus || {};
      const isReady = PodReady && ContainersReady;
      if (isReady) {
        ready = true;
        setInstanceStatus?.(2);
      }

      const eventName = isReady ? 'Ready' : EventStatus;

      return {
        ...item,
        name: InsEventMap[eventName] || eventName,
        Message: EventStatus === 'Pending' ? PendingMessage : Message,
        StartTime: Timestamp * 1000,
        UpdateTime: (LastTimestamp && LastTimestamp * 1000) || Timestamp * 1000,
        Status: EventLevel === 'Warning' ? 3 : 2,
        icon: EventLevel === 'Warning' ? 'error' : 'success',
        color: EventLevel === 'Warning' ? '#f54745' : '#06b624',
        messageColor: EventLevel === 'Warning' ? 'red' : '',
      };
    });
    setLifeData(data);

    const totalStart = get(data, '0.StartTime');
    const totalEnd =
      pipelineStatus === 1 && !ready ? end - 1 : get(data, `${data.length - 1}.UpdateTime`);
    setCustomTimeRange?.({
      StartTime: totalStart,
      UpdateTime: totalEnd,
    });
  };

  return (
    <Loading visible={loading} className="full-width">
      {map(lifeData, (instance) => (
        <TaskItem task={instance} regionId={regionId} appId={appId} start={start} end={end} />
      ))}
      {isEmpty(lifeData) && (
        <div className="border pt pb" style={{ textAlign: 'center' }}>
          {intl('saenext.change-order.TaskAppPanel.TaskInstanceLife.NoData')}
        </div>
      )}
    </Loading>
  );
};

export default TaskInstanceLife;
