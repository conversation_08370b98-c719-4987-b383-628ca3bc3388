.task-item {
  transition: background-color 0.5s ease-in-out;

  &-close {
    background-color: #fff;
  }

  &-open {
    background-color: #F6F6F6;
  }

  &:hover {
    background-color: #E6E6E6;
  }
}

.task-item-open {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 16px;
    left: 10px;
    width: 1px;
    height: calc(100% - 16px);
    background-color: #E0E0E0;
    transform: translateX(-50%);
  }
  
  & ~ div {
    .task-item-box {
      &:not(:last-child) {
        &::after {
          content: '';
          position: absolute;
          top: -1px;
          left: -6px;
          width: 1px;
          height: calc(100% + 2px);
          background-color: #E0E0E0;
          transform: translateX(-50%);
        }

        .task-item {
          &::after {
            content: '';
            position: absolute;
            top: -1px;
            left: -6px;
            width: 1px;
            height: calc(100% + 2px);
            background-color: #E0E0E0;
            transform: translateX(-50%);
          }
        }
      }

      &:last-child {
        .task-item {
          &::after {
            content: '';
            position: absolute;
            top: 0px;
            left: -6px;
            width: 1px;
            height: 16px;
            background-color: #E0E0E0;
            transform: translateX(-50%);
          }
        }
      }
      
      .task-item-info {
        &::before {
          content: '';
          position: absolute;
          top: 16px;
          left: -6px;
          width: 10px;
          height: 1px;
          background-color: #E0E0E0;
          transform: translateY(-50%);
        }
      }
    }
  }
}