import { get, isEmpty, map } from 'lodash';
import React, { useMemo } from 'react';
import { intl } from '@ali/cnd';
import TaskItem from './TaskItem';
import EmptyContent from '../EmptyContent';
import { last } from 'lodash';
import TaskBar from './TaskBar';

const TaskAppPanel = (props) => {
  const { appId, regionId, pipelineId, stage } = props;
  const { TaskList = [] } = stage;

  // const pipeColors = ['#AAB1FF', '#8FD0FE', '#9BE2FD', '#AEEEFB', '#C5F4FA'];

  const eventLegends = [
    {
      name: intl('saenext.change-order.TaskAppPanel.PlatformSideCorrelation'),
      color: 'black',
    },
    {
      name: intl('saenext.change-order.TaskAppPanel.UserSideCorrelation'),
      color: '#4C7FE6',
    },
  ];

  // 坐标轴起始时间为整数秒
  const removeDigits = (number, type) => {
    if (type === 'start') {
      return Math.ceil(number / 1000) * 1000;
    }
    return Math.floor(number / 1000) * 1000;
  };

  const { start, end } = useMemo(() => {
    const nowTime = new Date().getTime();
    const start = get(TaskList, '[0].StartTime');
    const end = get(last(TaskList), 'UpdateTime', nowTime);

    return {
      start: removeDigits(start, 'start'),
      end: removeDigits(end, 'end'),
    };
  }, [TaskList]);

  if (isEmpty(TaskList)) {
    return (
      <EmptyContent
        tip={intl('saenext.components.change-order.TaskPanel.NoEligibleInformationIsFound')}
      />
    );
  }

  return (
    <div className="pd-card">
      <div className={`pl-xl flex justify-between item-start`}>
        <div className="flex" style={{ width: '30%' }}>
          {eventLegends.map((item) => (
            <div className="flex mr">
              <span
                style={{
                  backgroundColor: item.color,
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  marginRight: 5,
                }}
              ></span>
              <span>{item.name}</span>
            </div>
          ))}
        </div>
        <div style={{ width: 'calc(60% + 20px)', marginRight: -12 }}>
          <TaskBar start={start} end={end} StartTime={start} UpdateTime={end} onlyYAxis />
        </div>
      </div>
      <div>
        {map(TaskList, (task, index) => (
          <TaskItem
            task={task}
            regionId={regionId}
            appId={appId}
            pipelineId={pipelineId}
            start={start}
            end={end}
            // barColor={pipeColors[index]}
          />
        ))}
      </div>
    </div>
  );
};

export default TaskAppPanel;
