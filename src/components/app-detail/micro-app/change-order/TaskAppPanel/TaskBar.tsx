import React, { useMemo } from 'react'
import { Wbar } from '@alife/aisc-widgets'
import { round } from 'lodash';

const TaskBar = (props) => {
  const { start, end, StartTime, UpdateTime, onlyYAxis = false, colors = ['#72E3DC'] } = props;
  
  const costTime = useMemo(() => 
    round((UpdateTime - StartTime) / 1000, 2), 
  [StartTime, UpdateTime]);

  const options = {
    animate: false,
    colors,
    xAxis: {
      visible: false
    },
    yAxis: onlyYAxis ? {
      type: 'linear' as 'linear',
      tickCount: 11,
      min: start,
      max: end,
      minLimit: start,
      maxLimit: end,
      labelFormatter: (value, data, index) => {
        const cost = round((value - start) / 1000, 1);
        return cost + ' s';
      }
    } : {
      visible: false,
      type: 'linear' as 'linear',
      min: start,
      max: end,
      minLimit: start,
      maxLimit: end,
    },
    column: false,
    legend: {
      visible: false
    },
    label: {
      visible: !onlyYAxis,
      position: 'left' as 'left',
      offsetX: (costTime < 1.5 && (StartTime - start < 1000)) ? 12 : 0,
      offsetY: 12,
      labelFormatter: (value, data, index) => costTime + ' s',
    },
    tooltip: {
      visible: false,
    },
    columnWidthRatio: onlyYAxis ? 0.01 : 1,
  };

  const data = useMemo(() => [
    {
      name: ' ',
      data: [
        [costTime + ' s', [StartTime, UpdateTime]]
      ]
    }
  ], [StartTime, UpdateTime, costTime])

  return (
    <>
      <Wbar key={costTime} height={onlyYAxis ? 58 : 40} config={options} data={data} />
    </>
  )
}

export default TaskBar