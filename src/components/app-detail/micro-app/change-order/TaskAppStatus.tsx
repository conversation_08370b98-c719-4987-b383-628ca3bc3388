import { intl } from '@ali/cnd';
import React, { memo } from 'react';
import { StatusIndicator } from '@ali/cnd';

/**
 * 
  task app 实例的状态
  0、READY；--就绪
  1、RUNNING；--执行中
  2、SUCCESS；--成功
  3、FAIL；--失败
  5、RETRY --等待确认
  6、CANCLE 终止
  11、IGNORE 略过
  12、IGNORE 略过
  31、FAIL_IGNORE 警告
*/

export const StatusMap = {
  0: {
    icon: 'loading',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskStatus.Ready'),
  },
  1: {
    icon: 'loading',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskStatus.Running'),
  },
  2: {
    icon: 'success',
    color: '#333333',
    logColor: '#06b624',
    label: intl('saenext.components.change-order.TaskStatus.Success'),
  },
  3: {
    icon: 'error',
    color: '#333333',
    logColor: '#f54745',
    messageColor: 'red',
    label: intl('saenext.components.change-order.TaskStatus.Failed'),
  },
  5: {
    icon: 'loading',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskStatus.WaitingForConfirmation'),
  },
  6: {
    icon: 'error',
    color: '#333333',
    logColor: '#f54745',
    messageColor: 'red',
    label: intl('saenext.components.change-order.TaskStatus.Termination'),
  },
  11: {
    icon: 'disabled',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskStatus.Skip'),
  },
  12: {
    icon: 'disabled',
    color: '#333333',
    logColor: '#ffffff',
    label: intl('saenext.components.change-order.TaskStatus.Skip'),
  },
  31: {
    icon: 'warning',
    color: '#333333',
    logColor: '#ffce03',
    label: intl('saenext.components.change-order.TaskStatus.Warning'),
  },
};

const Status = (props) => {
  const { value = 0 } = props;

  return (
    <StatusIndicator type={StatusMap[value].icon} style={{ color: StatusMap[value].color }}>
      {StatusMap[value].label}
    </StatusIndicator>
  );
};

export default memo(Status);
