.arevision {
  width: 100%;
  height: 100%;
  .arecored-desc {
    background-color: #fff; 
    border-collapse: collapse;
    .tr {
      border: 1px solid #ddd;
    }
    .td {
      padding-left: 16px;
      padding-right: 16px;
      padding-top: 8px;
      padding-bottom: 8px;
      .title-text {
        color: #555555;
      }
      .content-text {
        color: #333333;
        .wind-rc-status-indicator {
          .wind-rc-status-indicator-icon:before {
            width: auto !important;
            font-size: 12px !important;
          }
        }
      }
    }
  }
  .apipeline {
    width: 100%;
    height: 100%;
    .astage-root {
      display: flex;
      align-items: center;
      border: 1px solid #dcdfe6;
      .astage-status {
        display: flex;
        align-items: center;
        padding: 16px;
        .title {
          // font-weight: bold; 
          margin-left: 8px;
        }
        .wind-rc-status-indicator {
          .wind-rc-status-indicator-icon:before {
            width: auto !important;
            font-size: 12px !important;
          }
        }
      }
    }
    .astage-timeline {
      .next-timeline-item-timeline {
        width: 24px;
        .next-timeline-item-tail {
          margin-left: 2px;
        }
        .next-timeline-item-node {
          .wind-rc-status-indicator {
            margin-left: 6px;
            .wind-rc-status-indicator-icon:before {
              width: auto !important;
              font-size: 12px !important;
            }
          }
        }
      }
      .next-timeline-item-content {
        width: calc(100% - 24px);
        margin-bottom: 16px;
        .next-timeline-item-title {
          height: 28px;
          line-height: 28px;
          margin-top: 0;
          margin-bottom: 16px;
        }
      }
    }
    .astage-apanel {
      padding: 8px;
      margin-left: 16px;
      background: rgb(245, 246, 250);
      .next-collapse {
        margin-bottom: 8px;
        &:last-child {
          margin-bottom: 0;
        }
        .next-collapse-panel-title {
          background-color: #fff;
          padding: 8px 0 8px 36px;
          &:hover {
            background-color: #fff;
          }
          .title {
            display: flex;
            align-items: center;
            .mr-xl {
              margin-right: 24px;
            }
            .wind-rc-status-indicator {
              .wind-rc-status-indicator-icon:before {
                width: auto !important;
                font-size: 12px !important;
              }
            }
            .actions {
              display: flex;
              align-items: center;
            }
          }
        }
        .next-collapse-panel-content {
          padding: 0;
          .astage-apanel-body {
            padding: 12px 12px 12px 36px;
            background: rgb(4, 43, 53);
          }
        }
      }
      .apod-apanel {
        .cnd-search {
          .flex {
            width: 100%;
            .flex {
              width: 100%;
              justify-content: space-between;
            }
          }
        }
      }
    }
    .no-pipeline {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 60px 0;
    }
  }
  .no-pipeline {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60px 0;
  }
}


