import { intl } from '@ali/cnd';
import React, { memo } from 'react';
import { StatusIndicator } from '@ali/cnd';

// error #f54745
// success #06b624
// warning #ffce03

export const StatusMap = {
  0: {
    icon: 'disabled',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.NotExecuted'),
  },
  1: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.Running'),
  },
  2: {
    icon: 'success',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.SuccessfulExecution'),
  },
  3: {
    icon: 'error',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.ExecutionFailed'),
  },
  5: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.WaitForRetry'),
  },
  6: {
    icon: 'error',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.ExecutionTermination'),
  },
  8: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.WaitForManualConfirmation'),
  },
  9: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.WaitForAutomaticExecution'),
  },
  10: {
    icon: 'error',
    color: '#333333',
    label: intl(
      'saenext.components.change-order.ChangeOrderStatus.SystemExceptionCausesExecutionFailure',
    ),
  },
  11: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.PendingApproval'),
  },
  12: {
    icon: 'success',
    color: '#333333',
    label: intl('saenext.components.change-order.ChangeOrderStatus.Approved'),
  },
  20: {
    icon: 'success',
    color: '#333333',
    label: intl(
      'saenext.components.change-order.ChangeOrderStatus.ManuallyConfirmThatTheDeployment',
    ),
  },
  100: {
    icon: 'error',
    color: '#333333',
    label: intl(
      'saenext.components.change-order.ChangeOrderStatus.BatchPublishingFailedPleasePerform',
    ),
  },
  101: {
    icon: 'error',
    color: '#333333',
    label: intl(
      'saenext.components.change-order.ChangeOrderStatus.BatchPublishingFailedPleaseRoll',
    ),
  },
};

const Status = (props) => {
  const { value = 0, isImplementationFailed = false, supportConfirmSuccess = false } = props;
  let _value = value;
  if (isImplementationFailed) {
    _value = 100;
    if (supportConfirmSuccess) {
      _value = 101;
    }
  }
  const { icon, color, label } = StatusMap[_value] || {};
  return (
    <StatusIndicator type={icon} style={{ color: color }}>
      {label}
    </StatusIndicator>
  );
};

export default memo(Status);
