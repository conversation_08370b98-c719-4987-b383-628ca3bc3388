import { intl } from '@ali/cnd';
import React, { memo } from 'react';
import { StatusIndicator } from '@ali/cnd';

/**
 * 
 * pipeline发布的状态(UI上没有展示)
  0、ready； --就绪
  1、running；--执行中
  2、success；--成功
  3、fail；--失败
  5、suspend 暂时没用 --暂停
  6、abort --终止
  7、partial_success； -- 部分成功
  10、system_fail --系统异常导致失败
 */

export const StatusMap = {
  0: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.PipelineStatus.Ready'),
  },
  1: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.components.change-order.PipelineStatus.Running'),
  },
  2: {
    icon: 'success',
    color: '#333333',
    label: intl('saenext.components.change-order.PipelineStatus.SuccessfulExecution'),
  },
  3: {
    icon: 'error',
    color: '#333333',
    label: intl('saenext.components.change-order.PipelineStatus.ExecutionFailed'),
  },
  5: {
    icon: 'disabled',
    color: '#333333',
    label: intl('saenext.components.change-order.PipelineStatus.Pause'),
  },
  6: {
    icon: 'error',
    color: '#333333',
    label: intl('saenext.components.change-order.PipelineStatus.ExecutionTermination'),
  },
  7: {
    icon: 'warning',
    color: '#333333',
    label: intl('saenext.components.change-order.PipelineStatus.PartialSuccess'),
  },
  10: {
    icon: 'error',
    color: '#333333',
    label: intl(
      'saenext.components.change-order.PipelineStatus.SystemExceptionCausesExecutionFailure',
    ),
  },
};

const Status = (props) => {
  const { value = 0 } = props;
  return (
    <StatusIndicator type={StatusMap[value].icon} style={{ color: StatusMap[value].color }}>
      {StatusMap[value].label}
    </StatusIndicator>
  );
};

export default memo(Status);
