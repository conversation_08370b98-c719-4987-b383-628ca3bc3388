import React, { memo, useContext, useRef, useState, useEffect } from 'react';
import { Button, Dialog, intl, Loading, Message, ToolTipCondition } from '@ali/cnd';
import { isEmpty, get, has, isEqual, cloneDeep } from 'lodash';
import If from '~/components/shared/If';
import DeployForm from '../basic-info/DeploySlide/DeployForm';
import './index.less';
import services from '~/services';
import { AES_CONSTANT, trackMicroAppMsc } from '~/tracker';
import ExternalLink from '~/components/shared/ExternalLink';
import { TAB_KEY } from '../basic-info';
import CachedData from '~/cache/common';
import { PrometheusPolicyName } from '~/constants/ignoreMust';

const Deploy = (props) => {
  const { history, appConfig, appStatus, refresh, appAddonReleaseInfo={}, hasCmsPermission } = props;
  const [submitLoading, setSubmitLoading] = useState(false);
  const [hasMseRole, setHasMseRole] = useState(false);
  const [hasGrayApp, setHasGrayApp] = useState(false);
  const [enableCustomNasMountOptions, setEnableCustomNasMountOptions] = useState(false);
  const [agentDownloadUrl, setAgentDownloadUrl] = useState('');

  const { RegionId, AppId } = appConfig;

  const { CurrentStatus, RunningInstances, LastChangeOrderRunning } = appStatus;

  const isAppStopped = CurrentStatus === 'STOPPED' && RunningInstances === 0;

  const formRef = useRef(null);

  const isInDebt = CachedData.getOpenStatus('inDebtStatus');

  useEffect(() => {
    getProductFeature();
    getAgentDownloadUrl();
  }, [appConfig?.AppId]);

  useEffect(() => {
    if (appConfig?.NewSaeVersion === 'pro') {
      // 判断是有MSE服务关联角色AliyunServiceRoleForMSE
      handleCheckMseRole();
    }
  }, [appConfig?.NewSaeVersion]);

  useEffect(() => {
    if (!appConfig?.BaseAppId && appConfig?.AppName) {
      checkHasGrayApp();
    }
  }, [appConfig]);

  const getProductFeature = async () => {
    if (!appConfig?.AppId) return;
    const featureConfig = await services.CheckFeatureConfig({
      AppId: appConfig?.AppId,
    });
    setEnableCustomNasMountOptions(featureConfig?.Data?.enableCustomNasMountOptions);
  };

  // 查询应用级别agentVersion
  const getAgentDownloadUrl = async () => {
    const res = await services.checkInDebt({
      AppId: appConfig?.AppId,
    });
    setAgentDownloadUrl(res?.Data?.AgentDownloadUrl);
  };

  const handleCheckMseRole = async () => {
    const res = await services.checkMseRole();
    const HasServiceLinkRole = get(res, 'Data.HasServiceLinkRole', false);
    setHasMseRole(HasServiceLinkRole);
  };

  const checkHasGrayApp = async () => {
    const res = await services.listMicroApplications({
      params: {
        NamespaceId: appConfig?.NamespaceId,
        AppName: appConfig?.AppName,
      },
    });
    const apps = get(res, 'Data.Applications', []);
    const grayApps = apps[0]?.Children || [];
    setHasGrayApp(grayApps.length > 0);
  };

  const beforeSubmit = (onOk) => {
    if (!isAppStopped) {
      onOk();
      return;
    }

    Dialog.show({
      title: intl('saenext.basic-info.DeploySlide.ModifyApplicationConfiguration'),
      content: intl('saenext.basic-info.DeploySlide.BecauseYourCurrentNumberOf'),
      footerActions: ['ok'],
      okProps: {
        children: intl('saenext.basic-info.DeploySlide.ConfirmIAlreadyKnow'),
      },
      onOk: onOk,
    });
  };

  const onSubmit = async () => {
    setSubmitLoading(true);

    const values = await formRef.current?.validateForm();

    if (!values) {
      setSubmitLoading(false);
      return;
    }

    if (has(values, 'NewSaeVersion') && values?.NewSaeVersion === '') {
      values.NewSaeVersion = 'std';
    }

    if (isAppStopped) {
      // 应用已停止，只修改配置不进行部署
      values.Deploy = false;
    }
    let PrometheusConfig = {};
    const EnablePrometheus = values?.EnablePrometheus;
    if (values?.PrometheusConfig) {
      PrometheusConfig = cloneDeep(values?.PrometheusConfig);
      delete values?.EnablePrometheus;
      delete values?.PrometheusConfig;
    }

    const { Data: { AppId, ChangeOrderId, IsNeedApproval } = {} as any } =
      (await services.deployMicroApplication({
        params: values,
      })) || {};

    setSubmitLoading(false);

    if (!AppId) {
      Message.error(intl('saenext.basic-info.DeploySlide.DeploymentFailed'));
      return;
    }
    // promethues采集指标相关处理
    handlePrometheusMonitor(EnablePrometheus, PrometheusConfig);

    // 专业版应用开启了MSE微服务治理 && 未授权MSE服务关联角色 --- 主动进行授权
    if (
      appConfig?.NewSaeVersion === 'pro' &&
      values?.MicroserviceEngineConfig &&
      JSON.parse(values?.MicroserviceEngineConfig)?.Enable &&
      !hasMseRole
    ) {
      // 不用等待请求结果，异步执行
      services.createMseRole();
    }

    let originReadiness = appConfig?.Readiness || '';
    let currentReadiness = values?.Readiness || '';
    if (originReadiness) {
      const originPath = JSON.parse(originReadiness)?.httpGet?.path;
      const originPort = JSON.parse(originReadiness)?.httpGet?.port;
      const curPath = JSON.parse(currentReadiness)?.httpGet?.path;
      const curPort = JSON.parse(currentReadiness)?.httpGet?.port;
      // 应用配了/health 54199 又修改了 进行埋点
      if (
        originPath === '/health' &&
        originPort === 54199 &&
        (curPath !== '/health' || curPort !== 54199)
      ) {
        trackMicroAppMsc({
          behavior: 'VIEW',
          stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
          appId: appConfig?.AppId,
          section: 'base',
          trackType: 'HEALTH_READINESS',
        });
      }
    }

    if (IsNeedApproval) {
      Dialog.show({
        title: intl('saenext.basic-info.DeploySlide.ApplicationDeployment'),
        footerActions: ['ok'],
        content: (
          <Message type="notice">
            {intl('saenext.basic-info.DeploySlide.YourApplicationForDeploymentHas')}
            <br />
            {intl('saenext.micro-app.deploy.YouCanGoToApproval')}

            <ExternalLink
              url={'/operations-management/approval?type=approval-records&activeTab=mine'}
            />
            <br />
            {intl('saenext.basic-info.DeploySlide.YouCanAlsoGo')}
            <span className="bold">{intl('saenext.basic-info.DeploySlide.ContactManagement')}</span>
            {intl('saenext.basic-info.DeploySlide.SetYourContactInformationAfter')}
            <ExternalLink url={'/operations-management/approval?type=concats'} />
          </Message>
        ),
      });
    } else {
      Message.success(intl('saenext.basic-info.DeploySlide.SuccessfullyDeployed'));
    }
    refresh();
    goToBasicInfo(TAB_KEY.INSTANCE);
  };

  const goToBasicInfo = (tab?) => {
    const paramsObj = {};
    if (tab) {
      paramsObj['tab'] = tab;
    }
    const searchStr = new URLSearchParams(paramsObj).toString();
    history.push(`/${RegionId}/app-list/${AppId}/micro-app/base?${searchStr}`);
  };

  // prometheus监控采集指标处理
  const handlePrometheusMonitor = async (enable, config) => {
    const policyId = get(appAddonReleaseInfo, 'policyId', '');
    const addonVersion = get(appAddonReleaseInfo, 'addonVersion', '');
    const originEnable = get(appAddonReleaseInfo, 'enablePrometheus', false);
    // 关闭->开启
    if (!originEnable && enable) {
      await services.CreateAddonRelease({
        params: {
          policyId,
        },
        content: {
          addonName: 'cloud-sae-custom',
          envType: 'ECS',
          releaseName: `sae-custom-${AppId}`,
          version: '*',
          values: JSON.stringify({
            _entity: {
              instance_id: AppId,
              vpc_id: appConfig?.VpcId,
            },
            ...config,
          }),
        },
      });
      return;
    }
    // 开启->关闭
    if (originEnable && !enable) {
      await services.DeleteAddonRelease({
        params: {
          policyId,
          releaseName: `sae-custom-${AppId}`,
        },
      });
      return;
    }
    // 采集指标编辑
    if (originEnable && enable && appAddonReleaseInfo?.config) {
      if (
        config.interval !== appAddonReleaseInfo?.config?.interval ||
        config.port !== appAddonReleaseInfo?.config?.port ||
        config.metricPath !== appAddonReleaseInfo?.config?.metricPath
      ) {
        await services.UpdateAddonRelease({
          params: {
            policyId,
            releaseName: `sae-custom-${AppId}`,
          },
          content: {
            addonVersion,
            values: JSON.stringify({
              _entity: {
                instance_id: AppId,
                vpc_id: appConfig?.VpcId,
              },
              ...config,
            }),
          },
        });
      }
    }
  };

  return (
    <div>
      <h3 className="deploy-title">
        {!isAppStopped
          ? intl('saenext.micro-app.basic-info.AppActionBar.DeployApplications')
          : intl('saenext.micro-app.basic-info.AppActionBar.ModifyApplicationConfiguration')}
      </h3>
      <Loading visible={isEmpty(appConfig)} className="full-width full-height">
        <If condition={!isEmpty(appConfig)}>
          <DeployForm
            ref={formRef}
            appConfig={appConfig}
            isAppStopped={isAppStopped}
            hasMseRole={hasMseRole}
            hasGrayApp={hasGrayApp}
            enableCustomNasMountOptions={enableCustomNasMountOptions}
            agentDownloadUrl={agentDownloadUrl}
            appAddonReleaseInfo={appAddonReleaseInfo}
            hasCmsPermission={hasCmsPermission}
          />

          <div className="deploy-footer">
            <ToolTipCondition
              show={LastChangeOrderRunning || isInDebt}
              align="t"
              tip={
                LastChangeOrderRunning
                  ? intl('saenext.micro-app.basic-info.AppActionBar.TheApplicationIsChangingPlease')
                  : intl('saenext.micro-app.basic-info.AppActionBar.YourAccountIsOverduePlease')
              }
            >
              <Button
                type="primary"
                disabled={LastChangeOrderRunning || isInDebt}
                loading={submitLoading}
                onClick={() => beforeSubmit(onSubmit)}
              >
                {intl('saenext.micro-app.deploy.Ok')}
              </Button>
            </ToolTipCondition>
            <Button className="ml" onClick={() => goToBasicInfo()}>
              {intl('saenext.micro-app.deploy.Cancel')}
            </Button>
          </div>
        </If>
      </Loading>
    </div>
  );
};

export default memo(Deploy);
