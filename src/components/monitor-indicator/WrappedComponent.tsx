import React, { Component } from 'react';
import { Dialog } from '@ali/cnd';

type State = {
  visible: boolean
};
type Props = {};

const WrappedComponent = (title) => {
  return (IndicatorComponent) => {
    class ComposeComponent extends Component<Props, State> {
      constructor(props) {
        super(props);
        this.state = {
          visible: false,
        }
        this.setShow = this.setShow.bind(this);
      }

      setShow() {
        this.setState({
          visible: true,
        });
      }

      render() {
        const { visible } = this.state;
        return (
          <>
           <IndicatorComponent
             {...this.props}
             setShow={this.setShow}
           />
           <Dialog
            title={title}
            visible={visible}
            style={{ width: 600, height: 400 }}
            footerAlign="right"
            onOk={() => this.setState({ visible: false })}
            onCancel={() => this.setState({ visible: false })}
            onClose={() => this.setState({ visible: false }) }
           >
            <IndicatorComponent />
           </Dialog>
          </>
        )
      }
    }
    return ComposeComponent;
  };
};



export default WrappedComponent;