import { intl } from '@ali/cnd';
import React, { useEffect, useState, useRef, useContext, useMemo } from 'react';
import { Wline, COLORS } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card, Loading, Button, Icon, Message, Radio } from '@ali/cnd';
import services from '~/services';
import {
  get,
  forEach,
  isEmpty,
  isNumber,
  find,
  filter,
  cloneDeep,
  map,
  max,
  indexOf,
  values,
} from 'lodash';
import TimeRangeSelector, { getTimes } from '~/components/shared/TimeRangeSelector';
import MicroAppContext from '~/utils/microAppContext';
import { jsonParse } from '~/utils/transfer-data';
import { EMetricType } from '../app-detail/micro-app/basic-info/ScalingRuleSlide/MetricSettings/MetricsTable/constant';
import { formatDecimalStr } from '~/utils/global';

type Props = {
  appId: string;
  regionId: string;
};
const RadioGroup = Radio.Group;
const ChartYAxis0 = {
  Cpu: 'cpu',
  Memory: 'memory',
  Tcp: 'tcpActiveConn',
  Qps: 'qps',
  Rt: 'rt',
  Instances: 'instances',
  Prometheus: 'prometheus',
};
const MetricType = {
  [ChartYAxis0.Cpu]: EMetricType.CPU,
  [ChartYAxis0.Memory]: EMetricType.MEMORY,
  [ChartYAxis0.Tcp]: EMetricType.tcpActiveConn,
  [ChartYAxis0.Qps]: EMetricType.QPS,
  [ChartYAxis0.Rt]: EMetricType.RT,
  [ChartYAxis0.Prometheus]: EMetricType.Prometheus,
};
const ChartYAxis0Name = {
  [ChartYAxis0.Instances]: intl(
    'saenext.components.monitor-indicator.MicroStaticsIndicator.NumberOfApplicationInstances',
  ),
  [ChartYAxis0.Cpu]: intl('saenext.components.monitor-indicator.MicroStaticsIndicator.CpuUsage'),
  [ChartYAxis0.Memory]: intl(
    'saenext.components.monitor-indicator.MicroStaticsIndicator.MemoryUsage',
  ),
  [ChartYAxis0.Tcp]: intl(
    'saenext.components.monitor-indicator.MicroStaticsIndicator.TcpActiveConnections',
  ),
  [ChartYAxis0.Qps]: intl(
    'saenext.components.monitor-indicator.MicroStaticsIndicator.ServiceRequests',
  ),
  [ChartYAxis0.Rt]: intl(
    'saenext.components.monitor-indicator.MicroStaticsIndicator.AverageResponseTime',
  ),
};
const MetricUnit = {
  [ChartYAxis0.Cpu]: '%',
  [ChartYAxis0.Memory]: '%',
  [ChartYAxis0.Tcp]: '',
  [ChartYAxis0.Qps]: '/s',
  [ChartYAxis0.Rt]: 'ms',
  [ChartYAxis0.Instances]: '',
};

const ScaleUp = intl(
  'saenext.components.monitor-indicator.MicroStaticsIndicator.ExpansionInterval',
);

const ScaleDown = intl(
  'saenext.components.monitor-indicator.MicroStaticsIndicator.ContractionInterval',
);

const MetricTarget = intl('saenext.components.monitor-indicator.MicroStaticsIndicator.TargetValue');

const EventData = intl('saenext.components.monitor-indicator.MicroStaticsIndicator.ElasticEvent');

const metricOrder = values(ChartYAxis0Name);

const sortOrder = [...metricOrder, MetricTarget, ScaleUp, ScaleDown, EventData];

const Options = {
  grid: true,
  // @ts-ignore
  lineColors: [COLORS.widgetsColorGreen],
  // @ts-ignore
  barColors: [COLORS.widgetsColorPurple],
  spline: true,
  legend: {
    visible: true,
    position: 'bottom' as 'bottom',
    align: 'left',
    marker: {
      symbol: 'hyphen' as 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 3,
          lineWidth: 6,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
    nameFormatter: (v) => {
      switch (v) {
        case intl('saenext.components.monitor-indicator.MicroStaticsIndicator.CpuUsage'):
        case intl('saenext.components.monitor-indicator.MicroStaticsIndicator.MemoryUsage'):
          return intl('saenext.components.monitor-indicator.MicroStaticsIndicator.VUnit', { v: v });
        case intl(
          'saenext.components.monitor-indicator.MicroStaticsIndicator.TcpActiveConnections',
        ):
        case intl(
          'saenext.components.monitor-indicator.MicroStaticsIndicator.NumberOfApplicationInstances',
        ):
          return intl('saenext.components.monitor-indicator.MicroStaticsIndicator.VUnitUnit', {
            v: v,
          });
        case intl('saenext.components.monitor-indicator.MicroStaticsIndicator.ServiceRequests'):
          return intl(
            'saenext.components.monitor-indicator.MicroStaticsIndicator.VUnitTimesSecond',
            { v: v },
          );
        case intl('saenext.components.monitor-indicator.MicroStaticsIndicator.AverageResponseTime'):
          return intl(
            'saenext.components.monitor-indicator.MicroStaticsIndicator.VInMilliseconds',
            { v: v },
          );
        default:
          return v;
      }
    },
  },
  area: {
    geomStyle(x, y, type, extra) {
      if (type === ScaleUp) {
        return {
          fill: 'blue',
        };
      } else if (type === ScaleDown) {
        return {
          fill: 'orange',
        };
      } else {
        return {
          fill: 'transparent',
        };
      }
    },
  },
  geomStyle(x, y, type, extra) {
    if (type === ScaleUp || type === ScaleDown || type === MetricTarget) {
      return {
        opacity: 0,
        stroke: 'transparent',
      };
    }
    return {};
  },
  xAxis: {
    type: 'time' as 'time',
    mask: 'YYYY/MM/DD HH:mm',
  },
  yAxis: [
    {
      min: 0,
    },
    {
      min: 0,
      tickMethod: 'integer',
    },
  ],

  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
    titleFormatter: (v, t) => {
      return intl.date(t.x);
    },
    valueFormatter: (value, data, index, rawData) => {
      const val = formatDecimalStr(value);
      const unit = MetricUnit[data?.key] || '';
      return `${val}${unit}`;
    },
    sort: (a, b) => {
      const indexA =
        indexOf(sortOrder, a.name) !== -1 ? indexOf(sortOrder, a.name) : sortOrder.length;
      const indexB =
        indexOf(sortOrder, b.name) !== -1 ? indexOf(sortOrder, b.name) : sortOrder.length;
      return indexA - indexB;
    },
    reactContent: null,
  },
  guide: {
    line: [],
  },
};

export default (props: Props) => {
  const { appId, regionId } = props;
  const timeInitValue = 'last_3_hours';
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [options, setOptions] = useState(Options);
  const child = useRef(null);
  const [selectedTimeKey, updateSelectedTimeKey] = useState(timeInitValue);
  const [timeLine, setTimeLine] = useState('');
  const [yAxis0Value, updateYAxis0Value] = useState(ChartYAxis0.Cpu);
  const [metricTarget, setMetricTarget] = useState();
  const [eventData, setEventData] = useState([]);
  const [prometheusMetric, setPrometheusMetric] = useState<any>({});
  const customTimeRef = useRef([]);

  const { scaleRules } = useContext(MicroAppContext);

  const metricUnit = MetricUnit[yAxis0Value];

  useEffect(() => {
    initChart();
  }, [yAxis0Value, metricTarget, selectedTimeKey, timeLine]);

  useEffect(() => {
    getMetricTarget();
  }, [yAxis0Value, scaleRules]);

  useEffect(() => {
    setNewOptions();
  }, [metricTarget, eventData, yAxis0Value]);

  useEffect(() => {
    if (!isEmpty(prometheusMetric)) {
      updateYAxis0Value(ChartYAxis0.Prometheus);
    } else {
      updateYAxis0Value(ChartYAxis0.Cpu);
    }
  }, [prometheusMetric]);

  const initChart = async () => {
    setIsLoading(true);
    await getCombinationIndicator();
    setIsLoading(false);
  };

  const getCombinationIndicator = async () => {
    const _selectedTimeKey = selectedTimeKey;
    const [start, end] =
      _selectedTimeKey !== 'custom' ? getTimes(_selectedTimeKey) : customTimeRef.current;
    if (start && end) {
      child?.current.onChild([start, end]);
    }

    const _metric = yAxis0Value;
    const _start = isNumber(start) ? start / 1000 : 0;
    const _end = isNumber(end) ? end / 1000 : 0;
    const res = (await getScaleMonitor(_metric, _start, _end)) || new Map();

    const leftData = res.get(_metric) || [];
    const rightData = res.get(ChartYAxis0.Instances) || [];

    const eventData = filter(rightData, 'Ext');

    if (!isEmpty(eventData)) {
      setEventData(eventData);
    } else {
      setEventData([]);
    }

    if (yAxis0Value !== 'prometheus') {
      const _data = [
        {
          key: _metric,
          name: ChartYAxis0Name[_metric],
          type: 'line',
          yAxis: 0,
          data: fillGaps(leftData).map((r: { Value: number; Time: number }) => {
            return [
              r.Time * 1000,
              formatMetricVal({
                val: r.Value,
                time: r.Time,
                type: _metric,
                instanceData: rightData,
              }),
            ];
          }),
        },
        {
          key: ChartYAxis0.Instances,
          name: ChartYAxis0Name[ChartYAxis0.Instances],
          type: 'line',
          yAxis: 1,
          data: fillGaps(rightData).map((r: { Value: number; Time: number }) => {
            return [r.Time * 1000, r.Value];
          }),
        },
        {
          key: 'scaleUp',
          name: ScaleUp,
          yAxis: 0,
          data: rightData
            .map((r: { ScalingMap: any; Time: number }) => {
              const { scalingUpValue } = get(r.ScalingMap, MetricType[yAxis0Value], {});
              if (scalingUpValue === undefined) return [];
              return [r.Time * 1000, [formatRtVal(scalingUpValue, yAxis0Value), 1e4]];
            })
            .filter((item) => !isEmpty(item)),
        },
        {
          key: 'scaleDown',
          name: ScaleDown,
          yAxis: 0,
          data: rightData
            .map((r: { ScalingMap: any; Time: number }) => {
              const { scalingDownValue } = get(r.ScalingMap, MetricType[yAxis0Value], {});
              if (scalingDownValue === undefined) return [];
              return [r.Time * 1000, [0, formatRtVal(scalingDownValue, yAxis0Value)]];
            })
            .filter((item) => !isEmpty(item)),
        },
      ];

      if (!isEmpty(eventData)) {
        _data.push({
          key: 'event',
          name: EventData,
          type: 'bar',
          yAxis: 1,
          data: eventData.map((r: { Time: number; Ext: string }) => {
            const { reason = '', detail = '' } = jsonParse(r.Ext);
            return [r.Time * 1000, `${reason}, ${detail}`];
          }),
        });
      }
      setData(_data);
    } else {
      const prometheusMetrics = map(
        prometheusMetric?.Metric?.PrometheusMetrics || [],
        (item: any, index) => {
          return {
            key: item.PrometheusQuery,
            name: item.PrometheusQuery,
            type: 'line',
            yAxis: 0,
            data: fillGaps(res.get(item.PrometheusQuery) || [], _start, _end).map(
              (r: { Value: number; Time: number }) => {
                return [r.Time * 1000, r.Value];
              },
            ),
          };
        },
      );

      setData([
        ...prometheusMetrics,
        {
          key: ChartYAxis0.Instances,
          name: ChartYAxis0Name[ChartYAxis0.Instances],
          type: 'line',
          yAxis: 1,
          data: fillGaps(rightData).map((r: { Value: number; Time: number }) => {
            return [r.Time * 1000, r.Value];
          }),
        },
      ]);
    }
  };

  const fillGaps = (arr, start?, end?) => {
    if (!arr || arr.length < 2) return arr;

    const result = [arr[0]];

    for (let i = 1; i < arr.length; i++) {
      const prevTime = arr[i - 1].Time;
      const currentTime = arr[i].Time;

      // 计算需要填充的次数
      const timeDiff = currentTime - prevTime;
      let fillCount = Math.floor(timeDiff / 60) - 1;
      // 兼容prometheus自定义指标————为避免时间范围过大返回的数据点过于密集，根据时间范围长度动态调整Interval
      if (start && end) {
        const timeDiffInHours = Math.round((end - start) / 3600);
        const step = (Math.floor(Number(timeDiffInHours)) / 24) * 60 * 10;
        if (timeDiffInHours <= 12) {
          fillCount = Math.floor(timeDiff / 60) - 1;
        } else {
          fillCount = Math.floor(timeDiff / step) - 1;
        }
      }

      // 如果需要填充
      if (fillCount > 0) {
        // 循环填充每个60秒的间隔点
        for (let j = 1; j <= fillCount; j++) {
          result.push({
            Value: 0,
            Time: prevTime + 60 * j,
          });
        }
      }

      result.push(arr[i]);
    }

    return result;
  };

  const formatMetricVal = ({ val, time, type, instanceData }) => {
    if (type === ChartYAxis0.Qps) {
      const { Value: instanceCount = 1 } = find(instanceData, { Time: time }) || {};
      return val / instanceCount;
    }
    return val;
  };

  const dataWithTarget = useMemo(() => {
    if (!metricTarget) return data;

    const instanceData = find(data, { key: 'instances' });
    const targetData = map(instanceData?.data, (item, index) => {
      return [item[0], metricTarget];
    });
    const metricTargetData = {
      key: 'metricTarget',
      name: MetricTarget,
      type: 'line',
      yAxis: 0,
      data: targetData,
    };

    return [...data, metricTargetData];
  }, [data, metricTarget]);

  const getScaleMonitor = async (metric, start, end) => {
    let dataSource = [metric, ChartYAxis0.Instances];
    if (metric === 'prometheus') {
      const querys = prometheusMetric?.Metric?.PrometheusMetrics || [];
      dataSource = [...map(querys, 'PrometheusQuery'), ChartYAxis0.Instances];
    }
    const getMonitorPoints = async (_metric) => {
      let params: any = {
        AppId: appId,
        RegionId: regionId,
        StartTime: start,
        EndTime: end,
        Interval: 60,
        MetricName: _metric,
      };
      if (metric === 'prometheus' && _metric !== ChartYAxis0.Instances) {
        const timeDiffInHours = Math.round((end - start) / 3600);
        params = {
          ...params,
          Interval:
            timeDiffInHours <= 12 ? 60 : (Math.floor(Number(timeDiffInHours)) / 24) * 60 * 10,
          MetricName: 'prometheus',
          PrometheusUrl: prometheusMetric?.Metric?.PrometheusUrl,
          PrometheusToken: prometheusMetric?.Metric?.PrometheusToken,
          PrometheusQuery: _metric,
          ScalarOnly: true,
        };
      }
      const res = await services.getAppScaleMonitor({
        params,
        customErrorHandle: (error, data, callback) => {
          setIsLoading(false);
          // callback && callback();
        },
      });
      const data = get(res, 'Data', []);
      if (!isEmpty(data)) {
        if (_metric === ChartYAxis0.Qps) {
          forEach(data, (item) => {
            item.Value = item.Value ? Number((item.Value / 60).toFixed(2)) : 0;
          });
        }
        if (_metric === ChartYAxis0.Memory) {
          forEach(data, (item) => {
            item.Value = item.Value ? Number((item.Value * 100).toFixed(2)) : 0;
          });
        }
      }
      return { metric: _metric, data };
    };
    const options = [];
    forEach(dataSource, (_metric) => {
      options.push(getMonitorPoints(_metric));
    });
    const metricPoints = new Map();
    const res = await Promise.all(options);
    if (!isEmpty(res)) {
      // 转化成对象
      forEach(res, (item) => {
        metricPoints.set(item.metric, item.data);
      });
    }
    return metricPoints;
  };

  const getMetricTarget = () => {
    if (isEmpty(scaleRules)) return;

    const _prometheusMetrics = find(scaleRules, (item) => {
      return item?.ScaleRuleEnabled && item?.Metric?.MetricSource === 'prometheus';
    });
    setPrometheusMetric(_prometheusMetrics);
    const metricRule = find(scaleRules, { ScaleRuleType: 'metric', ScaleRuleEnabled: true });
    const metricMixRule = find(scaleRules, { ScaleRuleType: 'mix', ScaleRuleEnabled: true });

    const metricList = get(metricRule, 'Metric.Metrics', []);
    const metricMixList = get(metricMixRule, 'Metric.Metrics', []);

    const allMetricList = [...metricList, ...metricMixList];

    const curMetricRule = find(allMetricList, { MetricType: MetricType[yAxis0Value] });

    if (curMetricRule) {
      const { MetricTargetAverageUtilization } = curMetricRule;
      const target =
        yAxis0Value === 'rt'
          ? MetricTargetAverageUtilization / 1000
          : MetricTargetAverageUtilization;
      setMetricTarget(target);
    } else {
      setMetricTarget(null);
    }
  };

  const setNewOptions = () => {
    let newOptions = cloneDeep(Options) as any;

    if (yAxis0Value === ChartYAxis0.Cpu || yAxis0Value === ChartYAxis0.Memory) {
      newOptions.yAxis[0] = {
        min: 0,
        max: 100,
        unit: '%',
        metricTarget, // 让yAxis配置改变，确保图表刷新
      };
    } else {
      newOptions.yAxis[0] = {
        max: metricTarget ? metricTarget * 1.2 : null,
        unit: metricUnit,
      };
    }

    if (metricTarget) {
      newOptions = {
        ...newOptions,
        guide: {
          line: [
            {
              text: {
                title: intl(
                  'saenext.components.monitor-indicator.MicroStaticsIndicator.ScaleOutAreaWhenThe',
                ),
                position: 'end' as 'end',
                align: 'end',
                offsetX: -650,
                offsetY: 20,
              },
              status: 'normal',
              axis: 'y0',
              value: 'max',
              style: {
                opacity: 0,
              },
            },
            {
              text: {
                title: intl(
                  'saenext.components.monitor-indicator.MicroStaticsIndicator.ScaleInAreaWhenThe',
                ),
                position: 'end' as 'end',
                align: 'end',
                offsetX: -650,
                offsetY: -10,
              },
              status: 'warning',
              axis: 'y0',
              value: 0,
              style: {
                opacity: 0,
              },
            },
          ],
        },
        tooltip: {
          ...options.tooltip,
          valueFormatter,
        },
      };
    }

    if (!isEmpty(eventData)) {
      const guideLine = get(newOptions, 'guide.line', []);
      addEventGuide(guideLine);
    }

    setOptions(newOptions);
  };

  const addEventGuide = (guideLine) => {
    forEach(eventData, (item) => {
      const { Time, Ext } = item;
      if (find(guideLine, { value: Time * 1000 })) return;
      const { reason = '', detail = '' } = jsonParse(Ext);
      const title = intl(
        'saenext.components.monitor-indicator.MicroStaticsIndicator.ElasticEventReasonDetail',
        { reason: reason, detail: detail },
      );
      guideLine.push({
        status: 'normal',
        axis: 'x',
        value: Time * 1000,
        style: {
          lineDash: [4, 4],
        },
      });
    });
  };

  const valueFormatter = (value, data, index, rawData) => {
    const val = tooltipValueFormatter(value, data?.key);
    const unit = unitFormater(data?.key);
    return `${val}${unit}`;
  };

  const tooltipValueFormatter = (value, type) => {
    if (type === 'scaleUp') {
      const scalingUpValue = value?.split('-')?.[0];
      const res = intl(
        'saenext.components.monitor-indicator.MicroStaticsIndicator.GreaterThanValue',
        { value: scalingUpValue },
      );
      return res;
    }
    return formatDecimalStr(value);
  };

  const unitFormater = (type) => {
    if (type === 'scaleUp' || type === 'scaleDown' || type === 'metricTarget') {
      return metricUnit;
    }
    return MetricUnit[type] || '';
  };

  const formatRtVal = (val, metricType) => {
    if (MetricType[metricType] === EMetricType.RT) {
      return val / 1000;
    } else {
      return val;
    }
  };

  const onMonitorTimeChange = async (value) => {
    const { start: newStart, end: newEnd, key } = value;
    if (key === 'custom') {
      customTimeRef.current = [newStart, newEnd];
    }
    const [start, end] = getTimes(selectedTimeKey);
    if (start === newStart && end === newEnd) return;
    updateSelectedTimeKey(key);
    setTimeLine(`${newStart}-${newEnd}`);
  };

  const yAxis0ValueChange = async (_yAxis0Value) => {
    updateYAxis0Value(_yAxis0Value);
  };

  return (
    <Card
      contentHeight="auto"
      className="mt statics"
      showTitleBullet={false}
      showHeadDivider={false}
      title={intl(
        'saenext.components.monitor-indicator.MicroStaticsIndicator.TrendChartOfApplicationInstances',
      )}
      extra={
        <div style={{ all: 'initial', display: 'flex' }}>
          <div className="flex">
            <TimeRangeSelector
              onRef={child}
              defaultTime={false}
              disabledDate={[7, 'days']}
              timeInitValue={timeInitValue}
              periodInitValue={60}
              onTimeChanged={onMonitorTimeChange}
            />

            <Message type="notice" size="medium" className="flex ml-s" style={{ height: 32 }}>
              {intl(
                'saenext.components.monitor-indicator.StaticsIndicator.MonitoringDataCollectionIsSlightly',
              )}
            </Message>
          </div>
          <Button className="ml-s" onClick={initChart}>
            <Icon type="refresh" />
          </Button>
        </div>
      }
    >
      <div className="flex justify-between mb">
        <RadioGroup
          size="small"
          shape="button"
          value={yAxis0Value}
          className="mr-l"
          onChange={yAxis0ValueChange}
        >
          {!isEmpty(prometheusMetric) && (
            <Radio key={ChartYAxis0.Prometheus} value={ChartYAxis0.Prometheus}>
              {intl(
                'saenext.components.monitor-indicator.MicroStaticsIndicator.CustomPrometheusMetrics',
              )}
            </Radio>
          )}
          <Radio key={ChartYAxis0.Cpu} value={ChartYAxis0.Cpu}>
            {intl('saenext.components.monitor-indicator.MicroStaticsIndicator.CpuUsage')}
          </Radio>
          <Radio key={ChartYAxis0.Memory} value={ChartYAxis0.Memory}>
            {intl('saenext.components.monitor-indicator.MicroStaticsIndicator.MemoryUsage')}
          </Radio>
          <Radio key={ChartYAxis0.Tcp} value={ChartYAxis0.Tcp}>
            {intl(
              'saenext.components.monitor-indicator.MicroStaticsIndicator.TcpActiveConnections',
            )}
          </Radio>
          <Radio key={ChartYAxis0.Qps} value={ChartYAxis0.Qps}>
            {intl('saenext.components.monitor-indicator.MicroStaticsIndicator.ServiceRequests')}
          </Radio>
          <Radio key={ChartYAxis0.Rt} value={ChartYAxis0.Rt}>
            {intl('saenext.components.monitor-indicator.MicroStaticsIndicator.AverageResponseTime')}
          </Radio>
        </RadioGroup>
      </div>
      <Loading visible={isLoading} className="full-width mb-l">
        <Wline force data={dataWithTarget} height={200} config={options} />
      </Loading>
    </Card>
  );
};
