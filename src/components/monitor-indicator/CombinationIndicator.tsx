import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wline, COLORS } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card, Loading, Button, Icon, Radio } from '@ali/cnd';
import moment from 'moment';
import _, { each } from 'lodash';
import { isInteger } from '~/utils/global';
import { IMetricsProps } from './Constant';

type Props = {
  dataSource: {
    applicationInstances: IMetricsProps[];
    applicationRequests: IMetricsProps[];
    applicationCPUUsage: IMetricsProps[];
  };
  urlInternet: string;
};
const RadioGroup = Radio.Group;
const ChartYAxis0 = {
  Request: 'yAxisRequest',
  Instance: 'yAxisInstance',
};
const Options = {
  grid: true,
  // @ts-ignore
  lineColors: [COLORS.widgetsColorGreen],
  // @ts-ignore
  barColors: [COLORS.widgetsColorPurple],
  spline: true,
  legend: {
    visible: true,
    position: 'bottom',
    align: 'left',
    marker: {
      symbol: 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 3,
          lineWidth: 6,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
    nameFormatter: (v) => {
      switch (v) {
        case intl('saenext.components.monitor-indicator.CombinationIndicator.CpuUsage'):
          return intl(
            'saenext.components.monitor-indicator.CombinationIndicator.VUnitCoreSeconds',
            { v: v },
          );
        case intl('saenext.components.monitor-indicator.CombinationIndicator.NumberOfRequests'):
          return intl('saenext.components.monitor-indicator.CombinationIndicator.VUnitTimes', {
            v: v,
          });
        case intl(
          'saenext.components.monitor-indicator.CombinationIndicator.NumberOfApplicationInstances',
        ):
          return intl('saenext.components.monitor-indicator.CombinationIndicator.VUnitUnit', {
            v: v,
          });
        default:
          return v;
      }
    },
  },
  area: true,
  xAxis: {
    type: 'timeCat',
    mask: 'YYYY/MM/DD HH:mm',
  },
  yAxis: [
    {
      min: 0,
      tickMethod: 'integer',
      labelFormatter: (value) => {
        // @ts-ignore
        return value >= 1000 ? parseInt(value / 1000) + 'x10³' : value;
      },
    },
    {
      min: 0,
      labelFormatter: (v) => {
        const bytes = parseInt(v, 10);
        if (bytes > 1000000000) {
          const count = (bytes / 1000000000).toFixed(2);
          const order = isInteger(count) ? parseInt(count) : count;
          return `${order}x10⁹`;
        }
        if (bytes > 1000000) {
          const count = (bytes / 1000000).toFixed(2);
          const order = isInteger(count) ? parseInt(count) : count;
          return `${order}x10⁶`;
        }
        if (bytes > 1000) {
          const count = (bytes / 1000).toFixed(2);
          const order = isInteger(count) ? parseInt(count) : count;
          return `${order}x10³`;
        }
        return v;
      },
    },
  ],

  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
    },
  },
};

export default (props: Props) => {
  const { dataSource, urlInternet } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [options, setOptions] = useState(Options);
  const [maxRequestCount, updateRequestCount] = useState(1);
  const [yAxisData, updateYAxisData] = useState({ request: [], instance: [], cpu: [] });
  const [yAxis0Value, updateYAxis0Value] = useState(ChartYAxis0.Instance);
  const [yAxis0MaxVal, updateYAxis0MaxVal] = useState({ request: 0, instance: 0 });

  useEffect(() => {
    initChart();
    console.log(
      maxRequestCount,
      typeof yAxisData,
      typeof updateYAxis0Value,
      typeof yAxis0MaxVal,
      typeof setIsLoading,
    );
  }, [dataSource]);

  const initChart = () => {
    const {
      applicationInstances = [] as IMetricsProps[],
      applicationRequests = [] as IMetricsProps[],
      applicationCPUUsage = [] as IMetricsProps[],
    } = dataSource;
    let _maxRequestCount = 0;
    let _maxInstanceCount = 0;
    each(applicationRequests, (r: IMetricsProps) => {
      if (_maxRequestCount < r.Count) {
        _maxRequestCount = r.Count;
      }
    });
    each(applicationInstances, (r: IMetricsProps) => {
      if (_maxInstanceCount < r.Sum) {
        _maxInstanceCount = r.Sum;
      }
    });
    updateRequestCount(_maxRequestCount);

    let maxValy0,
      maxValy1 = 0;
    if (_maxRequestCount < 5) {
      maxValy0 = 5;
    } else if (_maxRequestCount < 10) {
      maxValy0 = 10;
    } else if (_maxRequestCount < 20) {
      maxValy0 = 20;
    } else {
      maxValy0 = _maxRequestCount;
    }
    if (_maxInstanceCount < 5) {
      maxValy1 = 5;
    } else if (_maxInstanceCount < 10) {
      maxValy1 = 10;
    } else if (_maxInstanceCount < 20) {
      maxValy1 = 20;
    } else {
      maxValy1 = 50;
    }
    updateYAxis0MaxVal({
      request: maxValy0,
      instance: maxValy1,
    });
    setOptions({
      ...Options,
      yAxis: [
        {
          ...Options?.yAxis[0],
          // @ts-ignore
          max: yAxis0Value === ChartYAxis0.Request ? maxValy0 : maxValy1,
        },
        {
          ...Options?.yAxis[1],
        },
      ],
    });
    const _data =
      yAxis0Value === ChartYAxis0.Request
        ? [
            {
              name: intl(
                'saenext.components.monitor-indicator.CombinationIndicator.NumberOfRequests',
              ),
              type: 'line',
              yAxis: 0,
              data: applicationRequests.map((r: IMetricsProps) => {
                return [r.timestamp, r.Count];
              }),
            },
          ]
        : [
            {
              name: intl(
                'saenext.components.monitor-indicator.CombinationIndicator.NumberOfApplicationInstances',
              ),
              type: 'line',
              yAxis: 0,
              data: applicationInstances.map((r: IMetricsProps) => {
                return [r.timestamp, r.Sum];
              }),
            },
          ];

    _data.push({
      name: intl('saenext.components.monitor-indicator.CombinationIndicator.CpuUsage'),
      type: 'line',
      yAxis: 1,
      data: applicationCPUUsage.map((r: IMetricsProps) => {
        return [
          r.timestamp,
          // 在这里换算单位 接口返回原始单位 毫核 * 毫秒 转化成 核 * 秒
          r.Sum ? Math.ceil(r.Sum / 1000 / 1000) : 0,
        ];
      }),
    });

    setData(_data);
    updateYAxisData({
      request: applicationRequests,
      instance: applicationInstances,
      cpu: applicationCPUUsage,
    });
  };

  const yAxis0ValueChange = (_yAxis0Value) => {
    setIsLoading(true);
    updateYAxis0Value(_yAxis0Value);
    const {
      request: applicationRequests,
      instance: applicationInstances,
      cpu: applicationCPUUsage,
    } = yAxisData;
    let _maxVal = yAxis0MaxVal.request;
    let _data = [
      {
        name: intl('saenext.components.monitor-indicator.CombinationIndicator.NumberOfRequests'),
        type: 'line',
        yAxis: 0,
        data: applicationRequests.map((r: IMetricsProps) => {
          return [r.timestamp, r.Count];
        }),
      },
    ];

    if (_yAxis0Value === ChartYAxis0.Instance) {
      _maxVal = yAxis0MaxVal.instance;
      _data = [
        {
          name: intl(
            'saenext.components.monitor-indicator.CombinationIndicator.NumberOfApplicationInstances',
          ),
          type: 'line',
          yAxis: 0,
          data: applicationInstances.map((r: IMetricsProps) => {
            return [r.timestamp, r.Sum];
          }),
        },
      ];
    }
    _data.push({
      name: intl('saenext.components.monitor-indicator.CombinationIndicator.CpuUsage'),
      type: 'line',
      yAxis: 1,
      data: applicationCPUUsage.map((r: IMetricsProps) => {
        return [
          r.timestamp,
          // 在这里换算单位 接口返回原始单位 毫核 * 毫秒 转化成 核 * 秒
          r.Sum ? Math.ceil(r.Sum / 1000 / 1000) : 0,
        ];
      }),
    });
    setOptions({
      ...Options,
      yAxis: [
        {
          ...Options?.yAxis[0],
          // @ts-ignore
          max: _maxVal,
        },
        {
          ...Options?.yAxis[1],
        },
      ],
    });
    setData(_data);
    setIsLoading(false);
  };

  return (
    <Card
      contentHeight="auto"
      className="mt statics"
      showTitleBullet={false}
      showHeadDivider={false}
      title={
        <div className="flex">
          <span>
            {intl(
              'saenext.components.monitor-indicator.CombinationIndicator.TrendChartOfApplicationInstances',
            )}
          </span>
          <Button
            text
            type="primary"
            className="ml-s"
            onClick={() => window.open(urlInternet, '_blank')}
          >
            {intl('saenext.components.monitor-indicator.CombinationIndicator.AccessApplications')}

            <Icon type="external_link" />
          </Button>
        </div>
      }
    >
      <div className="flex justify-between mb">
        <RadioGroup size="small" shape="button" value={yAxis0Value} onChange={yAxis0ValueChange}>
          <Radio key={ChartYAxis0.Instance} value={ChartYAxis0.Instance}>
            {intl(
              'saenext.components.monitor-indicator.CombinationIndicator.NumberOfApplicationInstances',
            )}
          </Radio>
          <Radio key={ChartYAxis0.Request} value={ChartYAxis0.Request}>
            {intl('saenext.components.monitor-indicator.CombinationIndicator.NumberOfRequests')}
          </Radio>
        </RadioGroup>
        {/* <span>CPU使用量（Core·秒）</span> */}
      </div>
      <Loading visible={isLoading} className="full-width mb-l">
        <Wline
          data={data}
          height={200}
          // @ts-ignore
          config={options}
        />

        {/* <div className='legend' style={{ marginTop: 12 }}>
           {
             (yAxis0Value === ChartYAxis0.Request) ? (
               <span className='legend-req'>请求数</span>
             ) : (
               <span className='legend-ins'>应用实例数</span>
             )
           }
           <span className='legend-cpu'>CPU使用量</span>
          </div> */}
      </Loading>
    </Card>
  );
};
