import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wline, COLORS } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card, Loading, Icon, Button, Dialog } from '@ali/cnd';
import isStrictObject, { IMetricsProps } from './Constant';
import { map } from 'lodash';
import moment from 'moment';
import TextWithBalloon from '~/components/shared/TextWithBalloon';

type Props = {
  data:
    | {
        [key: string]: IMetricsProps[];
      }
    | IMetricsProps[];
  isCompare?: boolean;
};
const EnlargeWidth = 860;
const EnlargeHeight = 340;

const Options = {
  xAxis: {
    type: 'timeCat',
    mask: 'HH:mm',
  },
  yAxis: {
    min: 0,
    tickMethod: 'integer',
    labelFormatter: (value) => {
      // @ts-ignore
      return value >= 1000 ? parseInt(value / 1000) + 'x10³' : value;
    },
  },
  area: true,
  legend: {
    visible: false,
    position: 'bottom',
    align: 'left',
    marker: {
      symbol: 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 4,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
  },
  symbol: false,
  spline: true,
  slider: false,
  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
    sort: 'asce',
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
    },
  },
  // @ts-ignore
  colors: [COLORS.widgetsColorPurple, COLORS.widgetsColorGreen],
};

export default (props: Props) => {
  const { data: dataSource, isCompare = false } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [options, setOptions] = useState(Options);
  const [height, setChartHeight] = useState(240);

  const [enlargeVisible, setEnlargeVisible] = useState(false);

  useEffect(() => {
    initChart();
  }, [isCompare, JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    setIsLoading(false);
    if (Array.isArray(dataSource)) {
      // 初始化单个指标数据
      oneIndicator();
      return;
    }
    if (isCompare && isStrictObject(dataSource)) {
      compareIndicator();
    }
  };

  const oneIndicator = () => {
    setOptions(Options);
    setChartHeight(240);
    setData([
      {
        name: intl('saenext.components.monitor-indicator.RequestCountIndicator.NumberOfRequests'),
        data: map(dataSource, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
    ]);
  };

  const compareIndicator = () => {
    setOptions({
      ...Options,
      area: false,
      legend: {
        ...Options?.legend,
        visible: true,
      },
    });
    setChartHeight(306);
    const indicators = Object.keys(dataSource);
    const [v1, v2] = indicators;
    setData([
      {
        name: v1,
        data: map(dataSource[v1], (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: v2,
        data: map(dataSource[v2], (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
    ]);
  };

  const renderChartContent = (isEnlarge) => {
    return (
      <Wline
        data={data}
        height={isEnlarge ? EnlargeHeight : height}
        // @ts-ignore
        config={{
          ...options,
          // @ts-ignore
          legend: {
            ...options.legend,
            align: isEnlarge ? 'right' : 'left',
          },
        }}
      />
    );
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={
          <TextWithBalloon
            text={intl(
              'saenext.components.monitor-indicator.RequestCountIndicator.NumberOfRequests',
            )}
            tips={intl(
              'saenext.components.monitor-indicator.RequestCountIndicator.TheNumberOfHttpRequests',
            )}
          />
        }
        extra={
          <>
            <Button
              text
              onClick={(v) => {
                setEnlargeVisible(true);
              }}
            >
              <Icon type="enlarge" />
            </Button>
          </>
        }
      >
        <Loading visible={isLoading} className="full-width">
          {renderChartContent(false)}
        </Loading>
      </Card>
      <Dialog
        title={
          <TextWithBalloon
            align="r"
            text={intl(
              'saenext.components.monitor-indicator.RequestCountIndicator.NumberOfRequests',
            )}
            tips={intl(
              'saenext.components.monitor-indicator.RequestCountIndicator.TheNumberOfHttpRequests',
            )}
          />
        }
        visible={enlargeVisible}
        footer={<></>}
        onClose={() => setEnlargeVisible(false)}
        style={{ width: EnlargeWidth, height: EnlargeHeight }}
      >
        {renderChartContent(true)}
      </Dialog>
    </>
  );
};
