import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wbar } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card, Loading, Icon, Button, Dialog } from '@ali/cnd';
import isStrictObject, { Mode, IMetricsProps, BytesTheme } from './Constant';
import { map, filter } from 'lodash';
import moment from 'moment';
import { yAxisFormatter } from '~/utils/global';
import TextWithBalloon from '~/components/shared/TextWithBalloon';

type Props = {
  data:
    | {
        [key: string]: IMetricsProps[][];
      }
    | IMetricsProps[][];
  isCompare?: boolean;
};

const Character = '-';
const EnlargeWidth = 860;
const EnlargeHeight = 340;
const NarrowHeight = 240;

const Options = {
  xAxis: {
    type: 'timeCat',
    mask: 'HH:mm',
  },
  yAxis: {
    labelFormatter: yAxisFormatter,
  },
  facet: false,
  legend: {
    visible: true,
    align: 'left',
    position: 'bottom',
  },
  tooltip: {
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
    },
    valueFormatter: (v) => {
      return `${v} Bytes`;
    },
  },
  // @ts-ignore
  colors: [
    BytesTheme.buleColor,
    BytesTheme.greenColor,
    BytesTheme.buleColor,
    BytesTheme.greenColor,
  ],
};

// data
// 1、app指标显示传入二维数组 [rxBytes, txBytes],
// 2、版本对比传入 { v1: [rxBytes, txBytes], v2: [rxBytes, txBytes] }

export default (props: Props) => {
  const { data: dataSource, isCompare = false } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [options, setOptions] = useState(Options);
  const [chartMode, setChartMode] = useState(Mode.one);
  // 标记实心 条纹版本
  const [discern, setDiscern] = useState({ solid: '', stripe: '' });

  const [enlargeVisible, setEnlargeVisible] = useState(false);

  useEffect(() => {
    initChart();
  }, [isCompare, JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    if (Array.isArray(dataSource)) {
      // 初始化单个指标数据
      oneIndicator();
      return;
    }
    if (isCompare && isStrictObject(dataSource)) {
      compareIndicator();
    }
  };

  const oneIndicator = () => {
    // [[收], [发]]
    setChartMode(Mode.one);
    setOptions(Options);
    const [rxBytes, txBytes] = dataSource as IMetricsProps[][];
    setData([
      {
        name: intl('saenext.components.monitor-indicator.NetworkPacketIndicator.Collection'),
        data: map(rxBytes, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl('saenext.components.monitor-indicator.NetworkPacketIndicator.Hair'),
        data: map(txBytes, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
    ]);
  };

  const geomStyle = (v, y, type) => {
    if (
      type ===
        intl('saenext.components.monitor-indicator.NetworkPacketIndicator.ReceiveCharacterV', {
          Character: Character,
          v: v,
        }) &&
      y !== 0
    ) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        // lineDash: [1, 1],
        fill: BytesTheme.blueFill,
        stroke: BytesTheme.buleColor,
      };
    }
    if (
      type ===
        intl('saenext.components.monitor-indicator.NetworkPacketIndicator.SendCharacterV', {
          Character: Character,
          v: v,
        }) &&
      y !== 0
    ) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        // lineDash: [1, 1],
        fill: BytesTheme.greenFill,
        stroke: BytesTheme.greenColor,
      };
    }
    return {};
  };

  const reactContent = (v1, v2, title, data) => {
    if (!title || !data.length) {
      return null;
    }
    const { data: t = {} } = data[0];
    const points = map(data, (p) => {
      const name = p?.name;
      const [_name] = name.split(Character);
      return {
        ...p?.data,
        color: p?.color,
        name: _name,
      };
    });
    const v1ps = filter(points, (p) => p?.facet === v1);
    const v2ps = filter(points, (p) => p?.facet === v2);
    const collects = [
      {
        key: intl('saenext.components.monitor-indicator.NetworkPacketIndicator.VersionV', {
          v: v1,
        }),
        values: v1ps,
      },
      {
        key: intl('saenext.components.monitor-indicator.NetworkPacketIndicator.VersionV', {
          v: v2,
        }),
        values: v2ps,
      },
    ];

    return (
      <>
        <div className="g2-tooltip-title">{moment(t.x).format('YYYY/MM/DD HH:mm:ss')}</div>
        {map(collects, (kv) => {
          const { key, values } = kv;
          return (
            <div className="g2-container">
              <span className="g2-tooltip-name mr-l">{key}</span>
              <ul className="g2-tooltip-list">
                {map(values, (p) => {
                  const {} = p;
                  return (
                    <li className="g2-tooltip-list-item">
                      <span
                        className="g2-tooltip-marker"
                        style={{ backgroundColor: `${p.color}` }}
                      ></span>
                      <span className="g2-tooltip-name">{p?.name}</span>
                      <span className="g2-tooltip-value">{p?.y} Bytes</span>
                    </li>
                  );
                })}
              </ul>
            </div>
          );
        })}
      </>
    );
  };

  const compareIndicator = () => {
    // { v1: [[收], [发]], v2: [[收], [发]] }
    setChartMode(Mode.compare);
    const indicators = Object.keys(dataSource);
    const [v1, v2] = indicators;
    const [v1rxBytes, v1txBytes] = dataSource[v1] as IMetricsProps[][];
    const [v2rxBytes, v2txBytes] = dataSource[v2] as IMetricsProps[][];
    setDiscern({ solid: v1, stripe: v2 });
    setOptions({
      ...Options,
      facet: true,
      // @ts-ignore
      legend: false,
      padding: [0, -2, 0, 36],
      geomStyle: (x, y, type) => geomStyle(v2, y, type),
      tooltip: {
        // @ts-ignore
        reactContent: (title, data) => reactContent(v1, v2, title, data),
      },
    });
    setData([
      {
        name: intl(
          'saenext.components.monitor-indicator.NetworkPacketIndicator.ReceiveCharacterV',
          { Character: Character, v: v1 },
        ),
        facet: v1,
        data: map(v1rxBytes, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl('saenext.components.monitor-indicator.NetworkPacketIndicator.SendCharacterV', {
          Character: Character,
          v: v1,
        }),
        facet: v1,
        data: map(v1txBytes, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl(
          'saenext.components.monitor-indicator.NetworkPacketIndicator.ReceiveCharacterV',
          { Character: Character, v: v2 },
        ),
        facet: v2,
        data: map(v2rxBytes, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl('saenext.components.monitor-indicator.NetworkPacketIndicator.SendCharacterV', {
          Character: Character,
          v: v2,
        }),
        facet: v2,
        data: map(v2txBytes, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
    ]);
  };

  const renderChartContent = (isEnlarge) => {
    return (
      <>
        {chartMode === Mode.compare ? (
          <div className="discern">
            <span className="solid">
              {intl(
                'saenext.components.monitor-indicator.NetworkPacketIndicator.VersionDiscernsolid',
                { discernSolid: discern.solid },
              )}
            </span>
            <span className="stripe">
              {intl(
                'saenext.components.monitor-indicator.NetworkPacketIndicator.VersionDiscernstripe',
                { discernStripe: discern.stripe },
              )}
            </span>
          </div>
        ) : null}

        <Wbar
          data={data}
          height={isEnlarge ? EnlargeHeight : NarrowHeight}
          // @ts-ignore
          config={{
            ...options,
            // @ts-ignore
            legend: {
              ...options.legend,
              visible: !(chartMode === Mode.compare),
              align: isEnlarge ? 'right' : 'left',
            },
          }}
        />

        {chartMode === Mode.compare ? (
          <div
            className="legend"
            style={{
              justifyContent: isEnlarge ? 'end' : 'start',
            }}
          >
            <span className="legend-rx">
              {intl('saenext.components.monitor-indicator.NetworkPacketIndicator.Collection')}
            </span>
            <span className="legend-tx">
              {intl('saenext.components.monitor-indicator.NetworkPacketIndicator.Hair')}
            </span>
          </div>
        ) : null}
      </>
    );
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={
          <TextWithBalloon
            text={intl(
              'saenext.components.monitor-indicator.NetworkPacketIndicator.NetworkPacketsReceivedAndSent',
            )}
            tips={intl(
              'saenext.components.monitor-indicator.NetworkPacketIndicator.TheInboundAndOutboundBytes',
            )}
          />
        }
        extra={
          <>
            <Button
              text
              onClick={(v) => {
                setIsLoading(false);
                setEnlargeVisible(true);
              }}
            >
              <Icon type="enlarge" />
            </Button>
          </>
        }
      >
        <Loading visible={isLoading} className="full-width">
          {renderChartContent(false)}
        </Loading>
      </Card>
      <Dialog
        title={
          <TextWithBalloon
            align="r"
            text={intl(
              'saenext.components.monitor-indicator.NetworkPacketIndicator.NetworkPacketsReceivedAndSent',
            )}
            tips={intl(
              'saenext.components.monitor-indicator.NetworkPacketIndicator.TheInboundAndOutboundBytes',
            )}
          />
        }
        visible={enlargeVisible}
        footer={<></>}
        onClose={() => setEnlargeVisible(false)}
        style={{ width: EnlargeWidth, height: EnlargeHeight }}
      >
        {renderChartContent(true)}
      </Dialog>
    </>
  );
};
