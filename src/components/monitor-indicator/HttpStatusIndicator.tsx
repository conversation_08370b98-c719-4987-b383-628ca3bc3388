import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wbar } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card, Loading, Icon, Button, Dialog } from '@ali/cnd';
import isStrictObject, { Mode, IMetricsProps, HttpThems } from './Constant';
import { map, filter } from 'lodash';
import moment from 'moment';

type Props = {
  data:
    | {
        [key: string]: IMetricsProps[][];
      }
    | IMetricsProps[][];
  isCompare?: boolean;
};

const Character = '-';
const EnlargeWidth = 860;
const EnlargeHeight = 340;
const NarrowHeight = 240;

const Options = {
  dodgeStack: true,
  xAxis: {
    type: 'timeCat',
    mask: 'HH:mm',
  },
  yAxis: {
    min: 0,
    tickMethod: 'integer',
    labelFormatter: (value) => {
      // @ts-ignore
      return value >= 1000 ? parseInt(value / 1000) + 'x10³' : value;
    },
  },
  legend: {
    visible: true,
    align: 'left',
    position: 'bottom',
  },
  tooltip: {
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
    },
  },
  // @ts-ignore
  colors: [
    HttpThems.greenColor,
    HttpThems.yellowColor,
    HttpThems.orangeColor,
    HttpThems.redColor,
    HttpThems.greenColor,
    HttpThems.yellowColor,
    HttpThems.orangeColor,
    HttpThems.redColor,
  ],
};

export default (props: Props) => {
  const { data: dataSource, isCompare = false } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [options, setOptions] = useState(Options);
  const [chartMode, setChartMode] = useState(Mode.one);
  // 标记实心 条纹版本
  const [discern, setDiscern] = useState({ solid: '', stripe: '' });

  const [enlargeVisible, setEnlargeVisible] = useState(false);

  useEffect(() => {
    initChart();
  }, [isCompare, JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    if (Array.isArray(dataSource)) {
      // 初始化单个指标数据
      oneIndicator();
      return;
    }
    if (isCompare && isStrictObject(dataSource)) {
      compareIndicator();
    }
  };

  const oneIndicator = () => {
    // [[2xx], [3xx], [4xx], [5xx]]
    setChartMode(Mode.one);
    setOptions(Options);

    const [httpStatus2xx, httpStatus3xx, httpStatus4xx, httpStatus5xx] =
      dataSource as IMetricsProps[][];
    setData([
      {
        name: '2xx',
        data: map(httpStatus2xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: '3xx',
        data: map(httpStatus3xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: '4xx',
        data: map(httpStatus4xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: '5xx',
        data: map(httpStatus5xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
    ]);
  };

  const geomStyle = (v, y, type) => {
    if (type === `2xx${Character}${v}` && y !== 0) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        fill: HttpThems.greenFill,
        stroke: HttpThems.greenColor,
      };
    }
    if (type === `3xx${Character}${v}` && y !== 0) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        fill: HttpThems.yellowFill,
        stroke: HttpThems.yellowColor,
      };
    }
    if (type === `4xx${Character}${v}` && y !== 0) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        fill: HttpThems.orangeFill,
        stroke: HttpThems.orangeColor,
      };
    }
    if (type === `5xx${Character}${v}` && y !== 0) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        fill: HttpThems.redFill,
        stroke: HttpThems.redColor,
      };
    }
    return {};
  };

  const reactContent = (v1, v2, title, data) => {
    if (!title || !data.length) {
      return null;
    }
    const { data: t = {} } = data[0];
    const points = map(data, (p) => {
      const name = p?.name;
      const [_name] = name.split(Character);
      return {
        ...p?.data,
        color: p?.color,
        name: _name,
      };
    });
    const v1ps = filter(points, (p) => p?.dodge === v1);
    const v2ps = filter(points, (p) => p?.dodge === v2);
    const collects = [
      {
        key: intl('saenext.components.monitor-indicator.HttpStatusIndicator.VersionV', { v: v1 }),
        values: v1ps,
      },
      {
        key: intl('saenext.components.monitor-indicator.HttpStatusIndicator.VersionV', { v: v2 }),
        values: v2ps,
      },
    ];

    return (
      <div style={{ height: 300 }}>
        <div className="g2-tooltip-title">{moment(t.x).format('YYYY/MM/DD HH:mm:ss')}</div>
        {map(collects, (kv) => {
          const { key, values } = kv;
          return (
            <div className="g2-container">
              <span className="g2-tooltip-name mr-l">{key}</span>
              <ul className="g2-tooltip-list">
                {map(values, (p) => {
                  const {} = p;
                  return (
                    <li className="g2-tooltip-list-item">
                      <span
                        className="g2-tooltip-marker"
                        style={{ backgroundColor: `${p.color}` }}
                      ></span>
                      <span className="g2-tooltip-name">{p?.name}</span>
                      <span className="g2-tooltip-value">{p?.y}</span>
                    </li>
                  );
                })}
              </ul>
            </div>
          );
        })}
      </div>
    );
  };

  const compareIndicator = () => {
    // { v1: [[2xx], [3xx], [4xx], [5xx]], v2: [[2xx], [3xx], [4xx], [5xx]] }
    setChartMode(Mode.compare);
    const indicators = Object.keys(dataSource);
    const [v1, v2] = indicators;
    const [v1http2xx, v1http3xx, v1http4xx, v1http5xx] = dataSource[v1] as IMetricsProps[][];
    const [v2http2xx, v2http3xx, v2http4xx, v2http5xx] = dataSource[v2] as IMetricsProps[][];
    setDiscern({ solid: v1, stripe: v2 });
    setOptions({
      ...Options,
      // @ts-ignore
      legend: false,
      geomStyle: (x, y, type) => geomStyle(v2, y, type),
      tooltip: {
        // @ts-ignore
        reactContent: (title, data) => reactContent(v1, v2, title, data),
      },
    });
    setData([
      {
        name: `2xx${Character}${v1}`,
        dodge: v1,
        data: map(v1http2xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: `3xx${Character}${v1}`,
        dodge: v1,
        data: map(v1http3xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: `4xx${Character}${v1}`,
        dodge: v1,
        data: map(v1http4xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: `5xx${Character}${v1}`,
        dodge: v1,
        data: map(v1http5xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: `2xx${Character}${v2}`,
        dodge: v2,
        data: map(v2http2xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: `3xx${Character}${v2}`,
        dodge: v2,
        data: map(v2http3xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: `4xx${Character}${v2}`,
        dodge: v2,
        data: map(v2http4xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
      {
        name: `5xx${Character}${v2}`,
        dodge: v2,
        data: map(v2http5xx, (point: IMetricsProps) => {
          return [point.timestamp, point.Count];
        }),
      },
    ]);
  };

  const renderChartContent = (isEnlarge) => {
    return (
      <>
        {chartMode === Mode.compare ? (
          <div className="discern">
            <span className="solid">
              {intl(
                'saenext.components.monitor-indicator.HttpStatusIndicator.VersionDiscernsolid',
                { discernSolid: discern.solid },
              )}
            </span>
            <span className="stripe">
              {intl(
                'saenext.components.monitor-indicator.HttpStatusIndicator.VersionDiscernstripe',
                { discernStripe: discern.stripe },
              )}
            </span>
          </div>
        ) : null}

        <Wbar
          data={data}
          height={isEnlarge ? EnlargeHeight : NarrowHeight}
          // @ts-ignore
          config={{
            ...options,
            // @ts-ignore
            legend: {
              ...options.legend,
              visible: !(chartMode === Mode.compare),
              align: isEnlarge ? 'right' : 'left',
            },
          }}
        />

        {chartMode === Mode.compare ? (
          <div
            className="legend"
            style={{
              justifyContent: isEnlarge ? 'end' : 'start',
            }}
          >
            <span className="legend-2xx">2xx</span>
            <span className="legend-3xx">3xx</span>
            <span className="legend-4xx">4xx</span>
            <span className="legend-5xx">5xx</span>
          </div>
        ) : null}
      </>
    );
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={intl('saenext.components.monitor-indicator.HttpStatusIndicator.HttpStatusCode')}
        extra={
          <>
            <Button
              text
              onClick={(v) => {
                setIsLoading(false);
                setEnlargeVisible(true);
              }}
            >
              <Icon type="enlarge" />
            </Button>
          </>
        }
      >
        <Loading visible={isLoading} className="full-width">
          {renderChartContent(false)}
        </Loading>
      </Card>
      <Dialog
        title={intl('saenext.components.monitor-indicator.HttpStatusIndicator.HttpStatusCode')}
        visible={enlargeVisible}
        footer={<></>}
        onClose={() => setEnlargeVisible(false)}
        style={{ width: EnlargeWidth, height: EnlargeHeight }}
      >
        {renderChartContent(true)}
      </Dialog>
    </>
  );
};
