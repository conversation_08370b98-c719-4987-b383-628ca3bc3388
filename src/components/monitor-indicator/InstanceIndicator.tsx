import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wline, Wbar } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card, Loading, Icon, Button, Dialog } from '@ali/cnd';
import isStrictObject, { IMetricsProps, InstanceThems, Mode } from './Constant';
import { map, filter } from 'lodash';
import moment from 'moment';
import TextWithBalloon from '~/components/shared/TextWithBalloon';

type Props = {
  data:
    | {
        [key: string]: IMetricsProps[][];
      }
    | IMetricsProps[][];
  isCompare?: boolean;
};

const Character = '-';
const EnlargeWidth = 860;
const EnlargeHeight = 340;
const NarrowHeight = 240;

const WlineOptions = {
  xAxis: {
    type: 'timeCat',
    mask: 'HH:mm',
  },
  yAxis: {
    min: 0,
    tickMethod: 'integer',
  },
  area: true,
  legend: {
    visible: true,
    position: 'bottom',
    align: 'left',
    marker: {
      symbol: 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 4,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
  },
  symbol: false,
  spline: true,
  slider: false,
  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
    sort: 'dsce',
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
    },
  },
  // @ts-ignore
  colors: [InstanceThems.purpleColor, InstanceThems.greenColor, InstanceThems.yellowColor],
};

const WbarOptions = {
  xAxis: {
    type: 'timeCat',
    mask: 'HH:mm',
  },
  yAxis: {
    min: 0,
    tickMethod: 'integer',
  },
  facet: true,
  legend: false,
  padding: [0, -2, 0, 20],
  tooltip: {
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
    },
  },
  // @ts-ignore
  colors: [
    InstanceThems.purpleColor,
    InstanceThems.greenColor,
    InstanceThems.yellowColor,
    InstanceThems.purpleColor,
    InstanceThems.greenColor,
    InstanceThems.yellowColor,
  ],
};

export default (props: Props) => {
  const { data: dataSource, isCompare = false } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [options, setOptions] = useState(WbarOptions);
  const [chartMode, setChartMode] = useState(Mode.one);
  // 标记实心 条纹版本
  const [discern, setDiscern] = useState({ solid: '', stripe: '' });

  const [enlargeVisible, setEnlargeVisible] = useState(false);

  useEffect(() => {
    initChart();
  }, [isCompare, JSON.stringify(dataSource)]);

  const initChart = () => {
    if (!dataSource) return;
    setIsLoading(false);
    if (Array.isArray(dataSource)) {
      // 初始化单个指标数据
      oneIndicator();
      return;
    }
    if (isCompare && isStrictObject(dataSource)) {
      compareIndicator();
    }
  };

  const oneIndicator = () => {
    // [[总实例数], [活跃实例数], [空闲实例数]]
    setChartMode(Mode.one);
    const [totalInstances, activeInstances, idleInstances] = dataSource as IMetricsProps[][];
    setData([
      {
        name: intl('saenext.components.monitor-indicator.InstanceIndicator.TotalInstances'),
        type: 'line',
        yAxis: 0,
        data: map(totalInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl('saenext.components.monitor-indicator.InstanceIndicator.ActiveInstances'),
        type: 'line',
        yAxis: 0,
        data: map(activeInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl('saenext.components.monitor-indicator.InstanceIndicator.NumberOfIdleInstances'),
        type: 'line',
        yAxis: 0,
        data: map(idleInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
    ]);
  };

  const compareIndicator = () => {
    // { v1: [[总实例数], [活跃实例数], [空闲实例数]], v2: [[总实例数], [活跃实例数], [空闲实例数]] }
    setChartMode(Mode.compare);
    const indicators = Object.keys(dataSource);
    const [v1, v2] = indicators;
    const [v1totalInstances, v1activeInstances, v1idleInstances] = dataSource[
      v1
    ] as IMetricsProps[][];
    const [v2totalInstances, v2activeInstances, v2idleInstances] = dataSource[
      v2
    ] as IMetricsProps[][];
    setDiscern({ solid: v1, stripe: v2 });
    setOptions({
      ...WbarOptions,
      geomStyle: (x, y, type) => geomStyle(v2, y, type),
      tooltip: {
        // @ts-ignore
        reactContent: (title, data) => reactContent(v1, v2, title, data),
      },
    });
    setData([
      {
        name: intl(
          'saenext.components.monitor-indicator.InstanceIndicator.TotalNumberOfInstancesCharacter',
          { Character: Character, v: v1 },
        ),
        facet: v1,
        data: map(v1totalInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl(
          'saenext.components.monitor-indicator.InstanceIndicator.NumberOfActiveInstancesCharacter',
          { Character: Character, v: v1 },
        ),
        facet: v1,
        data: map(v1activeInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl(
          'saenext.components.monitor-indicator.InstanceIndicator.NumberOfIdleInstancesCharacter',
          { Character: Character, v: v1 },
        ),
        facet: v1,
        data: map(v1idleInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl(
          'saenext.components.monitor-indicator.InstanceIndicator.TotalNumberOfInstancesCharacter',
          { Character: Character, v: v2 },
        ),
        facet: v2,
        data: map(v2totalInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl(
          'saenext.components.monitor-indicator.InstanceIndicator.NumberOfActiveInstancesCharacter',
          { Character: Character, v: v2 },
        ),
        facet: v2,
        data: map(v2activeInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
      {
        name: intl(
          'saenext.components.monitor-indicator.InstanceIndicator.NumberOfIdleInstancesCharacter',
          { Character: Character, v: v2 },
        ),
        facet: v2,
        data: map(v2idleInstances, (point: IMetricsProps) => {
          return [point.timestamp, point.Sum];
        }),
      },
    ]);
  };

  const geomStyle = (v, y, type) => {
    if (
      type ===
        intl(
          'saenext.components.monitor-indicator.InstanceIndicator.TotalNumberOfInstancesCharacter',
          { Character: Character, v: v },
        ) &&
      y !== 0
    ) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        fill: InstanceThems.purpleFill,
        stroke: InstanceThems.purpleColor,
      };
    }
    if (
      type ===
        intl(
          'saenext.components.monitor-indicator.InstanceIndicator.NumberOfActiveInstancesCharacter',
          { Character: Character, v: v },
        ) &&
      y !== 0
    ) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        fill: InstanceThems.greenFill,
        stroke: InstanceThems.greenColor,
      };
    }
    if (
      type ===
        intl(
          'saenext.components.monitor-indicator.InstanceIndicator.NumberOfIdleInstancesCharacter',
          { Character: Character, v: v },
        ) &&
      y !== 0
    ) {
      return {
        lineWidth: 0,
        fillOpacity: 1,
        fill: InstanceThems.yellowFill,
        stroke: InstanceThems.yellowColor,
      };
    }
    return {};
  };

  const reactContent = (v1, v2, title, data) => {
    if (!title || !data.length) {
      return null;
    }
    const { data: t = {} } = data[0];
    const points = map(data, (p) => {
      const name = p?.name;
      const [_name] = name.split(Character);
      return {
        ...p?.data,
        color: p?.color,
        name: _name,
      };
    });
    const v1ps = filter(points, (p) => p?.facet === v1);
    const v2ps = filter(points, (p) => p?.facet === v2);
    const collects = [
      {
        key: intl('saenext.components.monitor-indicator.InstanceIndicator.VersionV', { v: v1 }),
        values: v1ps,
      },
      {
        key: intl('saenext.components.monitor-indicator.InstanceIndicator.VersionV', { v: v2 }),
        values: v2ps,
      },
    ];

    return (
      <>
        <div className="g2-tooltip-title">{moment(t.x).format('YYYY/MM/DD HH:mm:ss')}</div>
        {map(collects, (kv) => {
          const { key, values } = kv;
          return (
            <div className="g2-container">
              <span className="g2-tooltip-name mr-l">{key}</span>
              <ul className="g2-tooltip-list">
                {map(values, (p) => {
                  const {} = p;
                  return (
                    <li className="g2-tooltip-list-item">
                      <span
                        className="g2-tooltip-marker"
                        style={{ backgroundColor: `${p.color}` }}
                      ></span>
                      <span className="g2-tooltip-name">{p?.name}</span>
                      <span className="g2-tooltip-value">{p?.y}</span>
                    </li>
                  );
                })}
              </ul>
            </div>
          );
        })}
      </>
    );
  };

  const renderChartContent = (isEnlarge) => {
    switch (chartMode) {
      case Mode.one:
        return (
          <Wline
            data={data}
            height={isEnlarge ? EnlargeHeight : NarrowHeight}
            // @ts-ignore
            config={{
              ...WlineOptions,
              // @ts-ignore
              legend: {
                ...WlineOptions.legend,
                align: isEnlarge ? 'right' : 'left',
              },
            }}
          />
        );

      case Mode.compare:
        return (
          <>
            <div className="discern">
              <span className="solid">
                {intl(
                  'saenext.components.monitor-indicator.InstanceIndicator.VersionDiscernsolid',
                  { discernSolid: discern.solid },
                )}
              </span>
              <span className="stripe">
                {intl(
                  'saenext.components.monitor-indicator.InstanceIndicator.VersionDiscernstripe',
                  { discernStripe: discern.stripe },
                )}
              </span>
            </div>
            <Wbar
              data={data}
              height={isEnlarge ? EnlargeHeight : NarrowHeight}
              // @ts-ignore
              config={options}
            />

            <div
              className="legend"
              style={{
                justifyContent: isEnlarge ? 'end' : 'start',
              }}
            >
              <span className="legend-total">
                {intl('saenext.components.monitor-indicator.InstanceIndicator.TotalInstances')}
              </span>
              <span className="legend-active">
                {intl('saenext.components.monitor-indicator.InstanceIndicator.ActiveInstances')}
              </span>
              <span className="legend-idle">
                {intl(
                  'saenext.components.monitor-indicator.InstanceIndicator.NumberOfIdleInstances',
                )}
              </span>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={
          <TextWithBalloon
            text={intl('saenext.components.monitor-indicator.InstanceIndicator.NumberOfInstances')}
            tips={intl(
              'saenext.components.monitor-indicator.InstanceIndicator.TheNumberOfActiveInstances',
            )}
          />
        }
        extra={
          <>
            <Button
              text
              onClick={(v) => {
                setEnlargeVisible(true);
              }}
            >
              <Icon type="enlarge" />
            </Button>
          </>
        }
      >
        <Loading visible={isLoading} className="full-width">
          {renderChartContent(false)}
        </Loading>
      </Card>
      <Dialog
        title={
          <TextWithBalloon
            align="r"
            text={intl('saenext.components.monitor-indicator.InstanceIndicator.NumberOfInstances')}
            tips={intl(
              'saenext.components.monitor-indicator.InstanceIndicator.TheNumberOfActiveInstances',
            )}
          />
        }
        visible={enlargeVisible}
        footer={<></>}
        onClose={() => setEnlargeVisible(false)}
        style={{ width: EnlargeWidth, height: EnlargeHeight }}
      >
        {renderChartContent(true)}
      </Dialog>
    </>
  );
};
