import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Wline, COLORS } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import { Card, Loading, Radio, Icon, Button, Dialog } from '@ali/cnd';
import isStrictObject, { Latitude, LATIDATA, IMetricsProps } from './Constant';
import { map } from 'lodash';
import moment from 'moment';
import TextWithBalloon from '~/components/shared/TextWithBalloon';

const RadioGroup = Radio.Group;
type Props = {
  data:
    | {
        [key: string]: IMetricsProps[];
      }
    | IMetricsProps[];
  isCompare?: boolean;
};

const EnlargeWidth = 860;
const EnlargeHeight = 340;

const Options = {
  xAxis: {
    type: 'timeCat',
    mask: 'HH:mm',
  },
  yAxis: {
    min: 0,
    labelFormatter: (v) => {
      return `${v}%`;
    },
  },
  area: true,
  legend: {
    visible: false,
    position: 'bottom',
    align: 'left',
    marker: {
      symbol: 'hyphen',
      style(oldStyle) {
        return {
          ...oldStyle,
          r: 4,
          fill: oldStyle.stroke,
          lineAppendWidth: 0,
        };
      },
    },
  },
  symbol: false,
  spline: true,
  slider: false,
  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
    sort: 'asce',
    valueFormatter: (v) => {
      return `${v} %`;
    },
    titleFormatter: (v, t) => {
      return moment(t.x).format('YYYY/MM/DD HH:mm:ss');
    },
  },
  // @ts-ignore
  colors: [COLORS.widgetsColorPurple, COLORS.widgetsColorGreen],
};

export default (props: Props) => {
  const { data: dataSource, isCompare = false } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [value, setValue] = useState(Latitude.avg);
  const [options, setOptions] = useState(Options);
  const [height, setChartHeight] = useState(200);

  const [enlargeVisible, setEnlargeVisible] = useState(false);

  useEffect(() => {
    initChart();
  }, [isCompare, JSON.stringify(dataSource)]);

  const initChart = (latitude?: string) => {
    if (!dataSource) return;
    if (Array.isArray(dataSource)) {
      // 初始化单个指标数据
      oneIndicator(latitude || value);
      return;
    }
    if (isCompare && isStrictObject(dataSource)) {
      compareIndicator(latitude || value);
    }
  };

  const oneIndicator = (latitude?: string) => {
    setOptions(Options);
    setChartHeight(200);
    setData([
      {
        name: intl('saenext.components.monitor-indicator.MemUtilizationIndicator.Usage'),
        type: 'line',
        yAxis: 0,
        data: map(dataSource, (point: IMetricsProps) => {
          return [point.timestamp, point[latitude]];
        }),
      },
    ]);
  };

  const compareIndicator = (latitude?: string) => {
    setOptions({
      ...Options,
      area: false,
      // @ts-ignore
      legend: {
        ...Options?.legend,
        visible: true,
      },
    });
    setChartHeight(266);
    const indicators = Object.keys(dataSource);
    const [v1, v2] = indicators;
    setData([
      {
        name: v1,
        type: 'line',
        yAxis: 0,
        data: map(dataSource[v1], (point: IMetricsProps) => {
          return [point.timestamp, point[latitude]];
        }),
      },
      {
        name: v2,
        type: 'line',
        yAxis: 0,
        data: map(dataSource[v2], (point: IMetricsProps) => {
          return [point.timestamp, point[latitude]];
        }),
      },
    ]);
  };

  const renderChartContent = (isEnlarge) => {
    return (
      <>
        <RadioGroup
          shape="button"
          size="small"
          value={value}
          className="mb block"
          dataSource={LATIDATA}
          onChange={(v) => {
            setValue(v as string);
            setIsLoading(false);
            initChart(v as string);
          }}
        />

        <Loading visible={isLoading} className="full-width">
          <Wline
            data={data}
            height={isEnlarge ? EnlargeHeight : height}
            // @ts-ignore
            config={{
              ...options,
              // @ts-ignore
              legend: {
                ...options.legend,
                align: isEnlarge ? 'right' : 'left',
              },
            }}
          />
        </Loading>
      </>
    );
  };

  return (
    <>
      <Card
        contentHeight="auto"
        showTitleBullet={false}
        showHeadDivider={false}
        title={
          <TextWithBalloon
            text={intl('saenext.components.monitor-indicator.MemUtilizationIndicator.MemoryUsage')}
            tips={intl(
              'saenext.components.monitor-indicator.MemUtilizationIndicator.TheMinimumMemoryUsageMaximum',
            )}
          />
        }
        extra={
          <>
            <Button
              text
              onClick={(v) => {
                setEnlargeVisible(true);
              }}
            >
              <Icon type="enlarge" />
            </Button>
          </>
        }
      >
        {renderChartContent(false)}
      </Card>
      <Dialog
        title={
          <TextWithBalloon
            align="r"
            text={intl('saenext.components.monitor-indicator.MemUtilizationIndicator.MemoryUsage')}
            tips={intl(
              'saenext.components.monitor-indicator.MemUtilizationIndicator.TheMinimumMemoryUsageMaximum',
            )}
          />
        }
        visible={enlargeVisible}
        footer={<></>}
        onClose={() => setEnlargeVisible(false)}
        style={{ width: EnlargeWidth, height: EnlargeHeight }}
      >
        {renderChartContent(true)}
      </Dialog>
    </>
  );
};
