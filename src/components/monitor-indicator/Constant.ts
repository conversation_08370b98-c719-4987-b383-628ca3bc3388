import { intl } from '@ali/cnd';
import { find } from 'lodash';
// import moment from 'moment';

export const Latitude = {
  avg: 'Average',
  max: 'Maximum',
  min: 'Minimum',
};

export const LATIDATA = [
  {
    value: 'Average',
    label: intl('saenext.components.monitor-indicator.Constant.Average'),
  },
  {
    value: 'Maximum',
    label: intl('saenext.components.monitor-indicator.Constant.Maximum'),
  },
  {
    value: 'Minimum',
    label: intl('saenext.components.monitor-indicator.Constant.MinimumValue'),
  },
];

export const Mode = {
  one: 'ONE',
  compare: 'COMPARE',
};

export interface IMetricsProps {
  timestamp: number;
  Count?: number;
  Sum?: number;
  Average?: number;
  Maximum?: number;
  Minimum?: number;
}

export const BytesTheme = {
  buleColor: '#0064C8',
  greenColor: '#42B3A4',
  greenFill:
    'p(a)data:image/svg+xml;base64,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',
  blueFill:
    'p(a)data:image/svg+xml;base64,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',
};

export const HttpThems = {
  greenColor: '#23b066',
  yellowColor: '#FAC31E',
  orangeColor: '#f98e1a',
  redColor: '#e84738',
  greenFill:
    'p(a)data:image/svg+xml;base64,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',
  yellowFill:
    'p(a)data:image/svg+xml;base64,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',
  orangeFill:
    'p(a)data:image/svg+xml;base64,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',
  redFill:
    'p(a)data:image/svg+xml;base64,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',
};

export const InstanceThems = {
  purpleColor: '#6B67E0',
  greenColor: '#23b066',
  yellowColor: '#FAC31E',
  purpleFill:
    'p(a)data:image/svg+xml;base64,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',
  greenFill:
    'p(a)data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDhweCIgaGVpZ2h0PSIyMDBweCIgdmlld0JveD0iMCAwIDQ4IDIwMCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDx0aXRsZT7nm7Tnur8gM+Wkh+S7vSAzNjwvdGl0bGU+CiAgICA8ZyBpZD0i5bqU55So55uR5o6nIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iNCIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIiBzdHJva2UtbGluZWNhcD0ic3F1YXJlIj4KICAgICAgICA8ZyBpZD0i5bqU55So55uR5o6nLeWPjOeJiOacrOWvueavlCIgc3Ryb2tlPSIjMjNiMDY2Ij4KICAgICAgICAgICAgPGxpbmUgeDE9IjAiIHkxPSI0IiB4Mj0iNDgiIHkyPSI0IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSI5IiB4Mj0iNDgiIHkyPSI5IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIxNCIgeDI9IjQ4IiB5Mj0iMTQiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjE5IiB4Mj0iNDgiIHkyPSIxOSIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMjQiIHgyPSI0OCIgeTI9IjI0IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIyOSIgeDI9IjQ4IiB5Mj0iMjkiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjM0IiB4Mj0iNDgiIHkyPSIzNCIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMzkiIHgyPSI0OCIgeTI9IjM5IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSI0NCIgeDI9IjQ4IiB5Mj0iNDQiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjQ5IiB4Mj0iNDgiIHkyPSI0OSIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iNTQiIHgyPSI0OCIgeTI9IjU0IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSI1OSIgeDI9IjQ4IiB5Mj0iNTkiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjY0IiB4Mj0iNDgiIHkyPSI2NCIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iNjkiIHgyPSI0OCIgeTI9IjY5IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSI3NCIgeDI9IjQ4IiB5Mj0iNzQiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9Ijc5IiB4Mj0iNDgiIHkyPSI3OSIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iODQiIHgyPSI0OCIgeTI9Ijg0IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSI4OSIgeDI9IjQ4IiB5Mj0iODkiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9Ijk0IiB4Mj0iNDgiIHkyPSI5NCIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iOTkiIHgyPSI0OCIgeTI9Ijk5IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIxMDQiIHgyPSI0OCIgeTI9IjEwNCIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMTA5IiB4Mj0iNDgiIHkyPSIxMDkiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjExNCIgeDI9IjQ4IiB5Mj0iMTE0IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIxMTkiIHgyPSI0OCIgeTI9IjExOSIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMTI0IiB4Mj0iNDgiIHkyPSIxMjQiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjEyOSIgeDI9IjQ4IiB5Mj0iMTI5IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIxMzQiIHgyPSI0OCIgeTI9IjEzNCIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMTM5IiB4Mj0iNDgiIHkyPSIxMzkiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjE0NCIgeDI9IjQ4IiB5Mj0iMTQ0IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIxNDkiIHgyPSI0OCIgeTI9IjE0OSIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMTU0IiB4Mj0iNDgiIHkyPSIxNTQiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjE1OSIgeDI9IjQ4IiB5Mj0iMTU5IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIxNjQiIHgyPSI0OCIgeTI9IjE2NCIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMTY5IiB4Mj0iNDgiIHkyPSIxNjkiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjE3NCIgeDI9IjQ4IiB5Mj0iMTc0IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIxNzkiIHgyPSI0OCIgeTI9IjE3OSIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMTg0IiB4Mj0iNDgiIHkyPSIxODQiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgoJCQk8bGluZSB4MT0iMCIgeTE9IjE4OSIgeDI9IjQ4IiB5Mj0iMTg5IiBpZD0i55u057q/LTPlpIfku70tMzYiPjwvbGluZT4KCQkJPGxpbmUgeDE9IjAiIHkxPSIxOTQiIHgyPSI0OCIgeTI9IjE5NCIgaWQ9IuebtOe6vy0z5aSH5Lu9LTM2Ij48L2xpbmU+CgkJCTxsaW5lIHgxPSIwIiB5MT0iMTk5IiB4Mj0iNDgiIHkyPSIxOTkiIGlkPSLnm7Tnur8tM+Wkh+S7vS0zNiI+PC9saW5lPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+',
  yellowFill:
    'p(a)data:image/svg+xml;base64,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',
};

export const DeafultData = {
  latency: null,
  rxBytes: null,
  txBytes: null,
  networkPacket: null,
  httpStatus2xx: null,
  httpStatus3xx: null,
  httpStatus4xx: null,
  httpStatus5xx: null,
  httpStatus: null,
  cpuUtilizations: null,
  memUtilizations: null,
  systemLoads: null,
  diskUtilizations: null,
  requests: null,
  totalInstances: null,
  activeInstances: null,
  idleInstances: null,
  instances: null,
};

export function unshiftChart(
  start: number,
  end: number,
  values: { [key: string]: any; timestamp: number }[],
  keys: string[],
) {
  // 判断 时间间隔是否大于12小时 12小时之内 点位间隔1分钟，12小时之后 点位间隔5分钟
  const isSpan5 = end - start > 1000 * 60 * 60 * 12;
  // 默认时间间隔为 60000，1分钟
  const interval = isSpan5 ? 300000 : 60000;

  // console.log('开始时间： ', start, moment(start).format('YYYY-MM-DD HH:mm:ss'));
  // console.log('结束时间： ', end, moment(end).format('YYYY-MM-DD HH:mm:ss'));
  // console.log('时间间隔 interval：', interval);
  // console.log('原始值：', values);

  // 标准开始时间
  const _start = Math.ceil(start / interval) * interval;
  // 标准结束时间
  const _end = Math.floor(end / interval) * interval;

  // console.log('标准开始时间： ', _start, moment(_start).format('YYYY-MM-DD HH:mm:ss'));
  // console.log('标准结束时间： ', _end, moment(_end).format('YYYY-MM-DD HH:mm:ss'));

  const _len = values.length;
  let _values = [];
  const kv0 = keys2Ob(keys);

  if (_len) {
    // 有数据时
    const first = values[0];
    const final = values[_len - 1];
    const { timestamp: firstTime } = first;
    const { timestamp: finalTime } = final;
    let _timestamp = _start;

    if (firstTime === _start) {
      // 由于末尾不补0 可以直接返回
      return values;
    }

    // 末尾不补0 结束的点就是接口返回的最后一个点
    while (_timestamp <= finalTime) {
      if (_timestamp < firstTime) {
        // 在返回的第一个点之前 直接补0
        _values.push({
          ...kv0,
          timestamp: _timestamp,
        });
      } else {
        // _timestamp == firstTime 或者 _timestamp >= firstTime
        // 从values里面查找替换 没有补0
        const _point = find(values, (p) => p?.timestamp === _timestamp);
        if (_point) {
          _values.push(_point);
        } else {
          _values.push({
            ...kv0,
            timestamp: _timestamp,
          });
        }
      }
      _timestamp += interval;
    }
  } else {
    // 空数据直接补0
    // 需要优化 如果返回是个空 不需要按照时间段补0 尽量减少循环次数, 最多补充30个点位
    let _timestamp = _start;
    while (_timestamp <= _end) {
      _values.push({
        ...kv0,
        timestamp: _timestamp,
      });
      _timestamp += interval;
    }
  }
  return _values;
}

export function unshiftComposeChart(
  start: number,
  end: number,
  ...args: {
    keys: string[];
    data: { [key: string]: any; timestamp: number }[];
  }[]
) {
  // [
  //   {
  //     keys: ['Count'],
  //     data: [{ Count: 0, timestamp: 1692601440000 }]
  //   },
  //   {
  //     keys: ['Sum'],
  //     data: [{ Sum: 0, timestamp: 1692601500000 }]
  //   }
  // ]

  // 判断 时间间隔是否大于12小时 12小时之内 点位间隔1分钟，12小时之后 点位间隔5分钟
  const isSpan5 = end - start > 1000 * 60 * 60 * 12;
  // 默认时间间隔为 60000，1分钟
  const interval = isSpan5 ? 300000 : 60000;
  const _start = Math.ceil(start / interval) * interval;
  const _end = Math.floor(end / interval) * interval;

  let deadline = 0;
  for (let i = 0; i < args.length; i++) {
    const indicator = args[i];
    const { data = [] } = indicator;
    const _len = data.length;
    let final = data[_len - 1];
    // final 可能为undefined
    if (!final) {
      final = { timestamp: 0 };
    }
    const { timestamp } = final;
    // 找出时间最迟的点 因为要以最后面的点为结束点开始补0，这是为了让组合指标的点位数量一致
    if (timestamp > deadline) {
      deadline = timestamp;
    }
  }

  if (!deadline) {
    // 说明都是空
    deadline = _end;
  }

  const indicators = [];
  for (let i = 0; i < args.length; i++) {
    const indicator = args[i];
    const { keys = [], data = [] } = indicator;
    const kv0 = keys2Ob(keys);
    const _data = [];
    const _len = data.length;
    if (_len) {
      const first = data[0];
      const { timestamp: firstTime } = first;
      let _timestamp = _start;
      if (firstTime === _start) {
        // 如果补0数组 第一个元素已经等于 标准开始时间 直接跳过本次循环即可
        indicators.push(data);
        continue;
      }
      while (_timestamp <= deadline) {
        if (_timestamp < firstTime) {
          // 直接补0
          _data.push({
            ...kv0,
            timestamp: _timestamp,
          });
        } else {
          const _point = find(data, (p) => p?.timestamp === _timestamp);
          if (_point) {
            _data.push(_point);
          } else {
            _data.push({
              ...kv0,
              timestamp: _timestamp,
            });
          }
        }
        _timestamp += interval;
      }
    } else {
      let _timestamp = _start;
      while (_timestamp <= deadline) {
        _data.push({
          ...kv0,
          timestamp: _timestamp,
        });
        _timestamp += interval;
      }
    }

    indicators.push(_data);
  }
  return indicators;
}

function keys2Ob(arr: string[]) {
  const keys = {};
  for (let i = 0; i < arr.length; i++) {
    Object.assign(keys, { [arr[i]]: 0 });
  }
  return keys;
}

export default function isStrictObject(o) {
  return Object.prototype.toString.call(o) === '[object Object]';
}
