import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { useRoaApi, Button, Icon, Loading, Select, Message, Actions, Dialog } from '@ali/cnd';
import { getParams } from '~/utils/global';
import { LogEditor } from '@ali/cnd';
import _ from 'lodash';
import services from '~/services';
import TimeRangeSelector, { getTimes } from '~/components/shared/TimeRangeSelector';
import InstanceStatus from '../instance-list/InstanceStatus';

// const DEFAULT_ALL_INSTANCE = { label: '所有实例', value: '' };
const OPENSLS_CONST = 'OPENSLS_CONST';
const { LinkButton } = Actions;

const LogControl = (props) => {
  const {
    match: {
      params: { regionId, id: applicationID },
    },
    appData,
    versionId,
  } = props;
  const timeInitValue = 'last_15_minutes';
  const [start, end] = getTimes(timeInitValue);
  const [startTime, setStartTime] = useState(start);
  const [endTime, setEndTime] = useState(end);
  const [instanceId, setInstanceId] = useState('');
  const [instanceStatus, setInstanceStatus] = useState('');
  const [instancesData, setInstancesData] = useState([]);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpensls, setIsOpensls] = useState(true);
  const applicationName = getParams('name');
  const [value, setVaule] = useState();
  const cacheOpensls = useRef<string | boolean>(OPENSLS_CONST);
  const child = useRef(null);
  const [selectedTimeKey, updateSelectedTimeKey] = useState(timeInitValue);

  const params = { applicationID, applicationName };
  const content = {
    applicationID,
    applicationName,
    startTime,
    endTime,
    versionId,
    instanceId,
  };

  const { run } = useRoaApi(
    'serverless',
    'GetWebApplicationLogs',
    {
      params,
      content,
    },
    {
      manual: true,
      disableErrorPrompt: true,
    },
  );

  useEffect(() => {
    if (!refreshIndex || _.isEmpty(appData)) return;
    initInstanceLogData();
  }, [refreshIndex, appData]);

  const initInstanceLogData = async () => {
    setIsLoading(true);
    // app 是否开始sls
    if (typeof cacheOpensls.current === 'string') {
      const { logConfig = { logstore: '', project: '' } } = appData;
      const { logstore, project } = logConfig;
      cacheOpensls.current = !!(logstore && project);
      const _isOpensls = cacheOpensls.current;
      setIsOpensls(_isOpensls);
    }
    const _instancesData = await getAppVersionInstances();
    const instance = _instancesData[0];
    if (instance) {
      const _instanceId = instance.value;
      const _instanceStatus = instance.status;
      setInstanceId(_instanceId);
      setInstanceStatus(_instanceStatus);
      getAppVersionLogData({
        params,
        content: { ...content, instanceId: _instanceId },
      });
    }
    setIsLoading(false);
  };

  const getAppVersionLogData = async (params) => {
    const data = await run(params);

    if (data instanceof Error) {
      onError(data);
      return;
    }

    const logData = _.map(data?.logEntrys, (val) => val.message.replace(/\/r$/, ''));
    const _value = logData.join('');
    // @ts-ignore
    setVaule(_value);
  };

  const getAppVersionInstances = async () => {
    const data = await services.getAppVersionInstances({
      applicationID,
      applicationName,
      startTime,
      endTime,
      qualifier: versionId,
    });
    const { instances = [] } = data;
    const instancesData = _.map(instances, (instance) => ({
      value: instance.instanceId,
      status: instance.status,
      label: (
        <>
          <InstanceStatus value={instance.status} />
          <span style={{ marginLeft: 10 }}>{instance.instanceId}</span>
        </>
      ),
    }));
    setInstancesData(instancesData);
    return instancesData;
  };

  const onLogTimeChange = (value) => {
    const { start, end, key } = value;
    setEndTime(end);
    setStartTime(start);
    updateSelectedTimeKey(key);
    setRefreshIndex(Date.now());
  };

  const onInstanceChange = async (_instanceId, __, recored) => {
    setIsLoading(true);
    const { status } = recored;
    setInstanceId(_instanceId);
    setInstanceStatus(status);
    // setSearchParams({ instance: _instanceId })
    // @ts-ignore
    setVaule('');
    await getAppVersionLogData({
      params,
      content: { ...content, instanceId: _instanceId },
    });
    setIsLoading(false);
  };

  const onInstanceShell = () => {
    if (!instanceId) return;
    const uri = `/${regionId}/monitor/${applicationID}/${versionId}/${instanceId}?name=${applicationName}`;
    window.open(uri, '_blank');
  };

  const createLogIndex = async () => {
    const promise = new Promise(async (resolve, reject) => {
      try {
        const {
          logConfig: { project, logstore },
        } = appData || {};
        const result = await services.CreateWebLogIndex({
          params: {
            applicationID,
            applicationName,
          },
          content: {
            project,
            logstore,
          },
        });
        if (result) {
          Dialog.confirm({
            title: intl('saenext.version-detail.log-control.FixedSuccessfully'),
            className: 'fc-dialog-body',
            content: (
              <div className="mt-s" style={{ lineHeight: '22px' }}>
                {intl('saenext.version-detail.log-control.CongratulationsYourConfigurationHasBeen')}
                <br />
                <span className="color-orange">
                  {intl('saenext.version-detail.log-control.NoteTheRepairOperationTakes')}
                </span>
              </div>
            ),

            okProps: { children: intl('saenext.version-detail.log-control.Confirm') },
            footerActions: ['ok'],
          });
          resolve(result);
        } else {
          reject();
        }
      } catch (err) {
        console.log(err);
        reject(err);
      }
    });
    return promise;
  };

  const onError = (error) => {
    const { code } = error;

    if (code === 'IndexConfigNotExist' || code === 'IndexForInstanceMetricsNotExists') {
      Dialog.alert({
        title: intl('saenext.version-detail.log-control.QueryFailed'),
        content: (
          <>
            <p>{intl('saenext.version-detail.log-control.TheIndexInTheLogstore')}</p>
            <p className="color-orange">
              {intl('saenext.version-detail.log-control.YouCanClickTheFix')}
            </p>
          </>
        ),

        onOk: createLogIndex,
        okProps: { children: intl('saenext.version-detail.log-control.FixNow') },
      });
    }
  };

  const refreshLog = () => {
    const [start, end] = getTimes(selectedTimeKey);
    if (isOpensls) {
      child?.current.onChild([start, end]);
    }
    setEndTime(end);
    setStartTime(start);
    setRefreshIndex(Date.now());
  };

  return (
    <>
      {!isOpensls ? (
        <Message type="notice" className="mb-s mt-l">
          {intl('saenext.version-detail.log-control.AMaximumOfLogsAt')}

          <LinkButton className="ml-s mr-s" disabled={!instanceId} onClick={onInstanceShell}>
            Webshell
          </LinkButton>
          {intl('saenext.version-detail.log-control.ViewLogsOnline')}
        </Message>
      ) : null}

      {instanceStatus === 'Idle' ? (
        <Message type="warning" className="mb-s mt-s">
          {intl('saenext.version-detail.log-control.TheInstanceDoesNotProcess')}
        </Message>
      ) : null}

      <div className="log-panel" style={{ marginTop: !isOpensls ? 8 : 16 }}>
        <div style={{ flexWrap: 'wrap' }} className="flex full-width">
          {isOpensls ? (
            <TimeRangeSelector
              onRef={child}
              defaultTime={false}
              timeInitValue={timeInitValue}
              periodInitValue={60}
              onTimeChanged={onLogTimeChange}
            />
          ) : null}

          <Select
            label={intl('saenext.version-detail.log-control.VersionInstance')}
            className="mb-s"
            style={{ minWidth: 400 }}
            value={instanceId}
            dataSource={instancesData}
            onChange={onInstanceChange}
          />
        </div>
        <Button className="ml-l" onClick={refreshLog}>
          <Icon type={'refresh'} />
        </Button>
      </div>
      <Loading
        className="gray-log mt-s"
        style={{
          width: '100%',
          overflow: 'auto',
          height: `calc(100% - ${isOpensls ? 100 : 160}px)`,
        }}
        visible={isLoading}
      >
        <LogEditor value={value} />
      </Loading>
    </>
  );
};

export default LogControl;
