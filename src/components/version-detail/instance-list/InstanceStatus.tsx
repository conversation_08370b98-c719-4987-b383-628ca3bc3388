import { intl } from '@ali/cnd';
import { StatusIndicator } from '@ali/cnd';
import React, { memo } from 'react';

export const MAP = {
  DESTROYED: {
    type: 'disabled',
    text: intl('saenext.version-detail.instance-list.InstanceStatus.Destroyed'),
  },
  RUNNING: {
    type: 'success',
    text: intl('saenext.version-detail.instance-list.InstanceStatus.Running'),
  },
  Busy: {
    type: 'success',
    text: intl('saenext.version-detail.instance-list.InstanceStatus.RunningTransientRequest'),
  },
  Idle: {
    type: 'warning',
    text: intl('saenext.version-detail.instance-list.InstanceStatus.RunningInstantNoRequest'),
  },
  Destroyed: {
    type: 'disabled',
    text: intl('saenext.version-detail.instance-list.InstanceStatus.Destroyed'),
  },
};

export const Status = {
  Busy: 'Busy',
  Idle: 'Idle',
  Destroyed: 'Destroyed',
};

const InstanceStatus = ({ value }) => {
  const { type, text } = MAP[value] || {};
  if (!type) {
    return <span>--</span>;
  }
  return (
    <StatusIndicator type={type} shape="dot">
      {text}
    </StatusIndicator>
  );
};

export default memo(InstanceStatus);
