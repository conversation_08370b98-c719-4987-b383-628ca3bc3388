/* eslint react/prop-types: 0 */
import React, { useState, useEffect, useRef } from 'react';
import { intl } from '@ali/xconsole';
import { CndTable, Actions, Button, Icon, Switch, Copy, Dialog } from '@ali/cnd';
import services from '~/services';
import { getParams, isInteger } from '~/utils/global';
import TimeRangeSelector, { getTimes } from '~/components/shared/TimeRangeSelector';
import InstanceStatus, { Status } from './InstanceStatus';
import { each } from 'lodash';
import { AES_CONSTANT, trackOpt } from '~/tracker';
import CachedData from '~/cache/common';

const { LinkButton } = Actions;

export default (props) => {
  const {
    match: {
      params: { regionId, id: applicationID },
    },
    history,
    appData,
    versionId,
  } = props;
  const timeInitValue = 'last_15_minutes';
  const [start, end] = getTimes(timeInitValue);
  const [startTime, setStartTime] = useState(start);
  const [endTime, setEndTime] = useState(end);
  // onlyDestroyed true 已销毁   false 运行中
  const [onlyDestroyed, setOnlyDestroyed] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [versionInstances, setVersionInstances] = useState([]);
  const [instanceCpuMem, setInstanceCpuMem] = useState(new Map());
  const [refreshIndex, setRefreshIndex] = useState(0);
  const applicationName = getParams('name');
  // 默认应用打开了sls
  const [isOpensls, setIsOpensls] = useState(true);
  console.log(typeof instanceCpuMem);
  const child = useRef(null);
  const [selectedTimeKey, updateSelectedTimeKey] = useState(timeInitValue);

  useEffect(() => {
    const { logConfig = { logstore: '', project: '' } } = appData;
    const { logstore, project } = logConfig;
    setIsOpensls(!!(logstore && project));
  }, []);

  useEffect(() => {
    if (!refreshIndex) return;
    // window.localStorage.setItem(
    //   LOCALSTORAGE_KEY,
    //   JSON.stringify({ start, end, period: 60, recentTiem: timeInitValue }),
    // );
    getAppVersionInstances();
    // 需要同时刷新配额
    // getInstancesCpuMem();
    console.log(typeof getInstancesCpuMem);
  }, [refreshIndex]);

  const getAppVersionInstances = async () => {
    setLoading(true);
    const data = await services.getAppVersionInstances({
      applicationID,
      applicationName,
      statuses: onlyDestroyed ? [Status.Destroyed] : [Status.Busy, Status.Idle],

      startTime,
      endTime,
      qualifier: versionId,
    });
    const { instances = [] } = data;
    setVersionInstances(instances);
    setLoading(false);
  };

  const getInstancesCpuMem = async () => {
    const data = await services.getAppInstanceCpuMem({
      applicationID,
      applicationName,
      startTime,
      endTime,
      pageNumber: 1,
      pageSize: 100,
      versionID: versionId,
    });
    const { metricsList = [] } = data;
    const _instanceCpuMem = new Map();

    each(metricsList, (instance) => {
      const {
        instanceID,
        memoryUsageMB = 0,
        memoryLimitMB = 0,
        cpuPercent = 0,
        cpuQuotaPercent = 0,
      } = instance;
      const _memory = quotaFormatBinary(memoryUsageMB, 1024);
      const _memoryQuota = quotaFormatBinary(memoryLimitMB, 1024);
      const _cpu = quotaFormatBinary(cpuPercent, 100);
      const _cpuQuota = quotaFormatBinary(cpuQuotaPercent, 100);

      _instanceCpuMem.set(instanceID, {
        instanceID,
        memory: isInteger(_memory) ? parseInt(_memory) : _memory,
        memoryQuota: isInteger(_memoryQuota) ? parseInt(_memoryQuota) : _memoryQuota,
        cpu: isInteger(_cpu) ? parseInt(_cpu) : _cpu,
        cpuQuota: isInteger(_cpuQuota) ? parseInt(_cpuQuota) : _cpuQuota,
      });
    });
    setInstanceCpuMem(_instanceCpuMem);
  };

  const quotaFormatBinary = (value, binary) => {
    return isNaN(parseFloat(value)) ? value : (parseFloat(value) / binary).toFixed(2);
  };

  const onInstanceTimeChange = (value) => {
    const { start, end, key } = value;
    setEndTime(end);
    setStartTime(start);
    updateSelectedTimeKey(key);
    setRefreshIndex(Date.now());
  };

  const onInstaneStatusChange = (value) => {
    if (!isOpensls) {
      // 未开通
      Dialog.alert({
        title: intl('saenext.version-detail.instance-list.LogServiceSlsHasNot'),
        content: (
          <div style={{ width: 520, lineHeight: '20px' }}>
            {intl('saenext.version-detail.instance-list.LogServiceSlsIsNot')}
          </div>
        ),

        okProps: { children: intl('saenext.version-detail.instance-list.ActivateNow') },
        onOk: () => {
          window.open(`${CachedData.confLink('feature:common-buy')}/?commodityCode=sls`);
        },
        cancelProps: { children: intl('saenext.version-detail.instance-list.Cancel') },
      });
      return;
    }
    setOnlyDestroyed(value);
    setRefreshIndex(Date.now());
  };

  const onInstanceLog = (record) => {
    const { instanceId } = record;
    const newSearch = `?name=${applicationName}&instance=${instanceId}`;
    history.replace(
      `/${regionId}/app-list/${applicationID}/version-detail/${versionId}/log${newSearch}`,
    );
  };

  const onInstanceMonitor = (record) => {
    const { instanceId } = record;
    const newSearch = `?name=${applicationName}&instance=${instanceId}`;
    history.replace(
      `/${regionId}/app-list/${applicationID}/version/${versionId}/monitor${newSearch}`,
    );
  };

  const onInstanceShell = (record) => {
    trackOpt({
      behavior: AES_CONSTANT.OPT_BEHAVIOR_TYPE.WEB_SHELL,
      stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
    });
    const { instanceId } = record;
    const uri = `/${regionId}/monitor/${applicationID}/${versionId}/${instanceId}?name=${applicationName}`;
    window.open(uri, '_blank');
  };

  const refreshInstance = () => {
    const [start, end] = getTimes(selectedTimeKey);
    child?.current.onChild([start, end]);
    setEndTime(end);
    setStartTime(start);
    setRefreshIndex(Date.now());
  };

  return (
    <>
      <CndTable
        loading={loading}
        dataSource={versionInstances}
        operation={
          <div className="flex mt-l">
            <TimeRangeSelector
              onRef={child}
              defaultTime={false}
              timeInitValue={timeInitValue}
              periodInitValue={60}
              onTimeChanged={onInstanceTimeChange}
            />

            <Switch
              checked={onlyDestroyed}
              className="ml-s"
              checkedChildren="on"
              unCheckedChildren="off"
              // disabled={!isOpensls}
              onChange={onInstaneStatusChange}
            />

            <span style={{ marginLeft: 6 }}>
              {intl('saenext.version-detail.instance-list.DisplaysDestroyedInstances')}
            </span>
          </div>
        }
        secondaryOperation={
          <Button type="normal" className='mt-l' style={{ padding: '0 9px' }} onClick={refreshInstance}>
            <Icon type="refresh" style={{ color: '#666' }} />
          </Button>
        }
        // @ts-ignore
        columns={[
          {
            title: intl('saenext.version-detail.instance-list.InstanceId'),
            dataIndex: 'instanceId',
            width: 300,
            // lock: 'left',
            cell: value => <Copy text={value}>{value}</Copy>,
          },
          {
            title: intl('saenext.version-detail.instance-list.InstanceStatus'),
            dataIndex: 'status',
            width: 170,
            cell: value => {
              return <InstanceStatus value={value} />;
            },
          },
          {
            title: intl('saenext.version-detail.instance-list.Image'),
            dataIndex: 'imageUrl',
            // width: 380,
            cell: value => (
              // <Truncate
              //   type='width'
              //   threshold='auto'
              //   // @ts-ignore
              //   align='top'
              // >
              //   <Copy text={value}>{value}</Copy>
              // </Truncate>
              <Copy text={value}>{value}</Copy>
            ),
          },
          // {
          //   title: 'VCPU使用量/配额',
          //   dataIndex: 'instanceId',
          //   width: 180,
          //   cell: (value) => {
          //     const instance = instanceCpuMem.get(value);
          //     if (instance) {
          //       const { cpu, cpuQuota } = instance;
          //       return (
          //         <>
          //           <span className='mr-s'>{cpu}</span>
          //           <span>Core</span>
          //           <span className='mr-s ml-s'>/</span>
          //           <span className='mr-s'>{cpuQuota}</span>
          //           <span>Core</span>
          //         </>
          //       );
          //     }
          //     return (<span>--</span>);
          //   }
          // },
          // {
          //   title: '内存使用量/配额',
          //   dataIndex: 'instanceId',
          //   width: 180,
          //   cell: (value) => {
          //     const instance = instanceCpuMem.get(value);
          //     if (instance) {
          //       const { memory, memoryQuota } = instance;
          //       return (
          //         <>
          //           <span className='mr-s'>{memory}</span>
          //           <span>GiB</span>
          //           <span className='mr-s ml-s'>/</span>
          //           <span className='mr-s'>{memoryQuota}</span>
          //           <span>GiB</span>
          //         </>
          //       );
          //     }
          //     return (<span>--</span>);
          //   }
          // },
          {
            title: intl('saenext.components.version-list.Operation'),
            width: 240,
            cell: (value, index, record) => (
              // @ts-ignore
              <Actions threshold={3}>
                <LinkButton onClick={() => onInstanceLog(record)}>
                  {intl('saenext.version-detail.instance-list.RealTimeLog')}
                </LinkButton>
                <LinkButton
                  disabled={record.status === 'DESTROYED'}
                  onClick={() => onInstanceShell(record)}
                >
                  Webshell
                </LinkButton>
                <LinkButton onClick={() => onInstanceMonitor(record)}>
                  {intl('saenext.version-detail.instance-list.RealTimeMonitoring')}
                </LinkButton>
              </Actions>
            ),
          },
        ]}
      />
    </>
  );
};
