import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Description, Button, CpuMemSelector } from '@ali/cnd';
import _ from 'lodash';

type Props = {
  appData: {
    [key: string]: any;
  };
  onEdit: () => void;
};
const { Preview: CpumemPreview } = CpuMemSelector;

export default ({ appData, onEdit }: Props) => {
  // @ts-ignore
  const [items, setItems] = useState([
    {
      dataIndex: 'cpumem',
      label: intl('saenext.configure-info.VolumeInfo.VolumeInfoFields.SingleInstanceType'),
      // @ts-ignore
      render: (value) => (value ? <CpumemPreview value={value} /> : <span>-</span>),
    },
    {
      dataIndex: 'instanceConcurrency',
      label: intl(
        'saenext.configure-info.VolumeInfo.VolumeInfoFields.ConcurrentRequestsPerSecondFor',
      ),
    },
  ]);
  const [dataSource, setDataSource] = useState({});

  useEffect(() => {
    initializeRender();
  }, [appData]);

  const initializeRender = () => {
    if (appData) {
      const { cpu, memorySize, instanceConcurrency } = appData;
      const _dataSource = {
        cpumem: {
          cpu,
          memory: Math.ceil(parseInt(memorySize, 10) / 1024),
        },
        instanceConcurrency,
      };
      setDataSource(_dataSource);
    }
  };

  return (
    <Description
      title={intl('saenext.configure-info.VolumeInfo.VolumeInfoFields.CapacitySettings')}
      items={items}
      dataSource={dataSource}
      actions={
        <Button
          text
          type="primary"
          style={{ fontWeight: 'normal' }}
          onClick={() => {
            onEdit && onEdit();
          }}
        >
          {intl('saenext.configure-info.VolumeInfo.VolumeInfoFields.DeployANewVersion')}
        </Button>
      }
    />
  );
};
