import { intl } from '@ali/cnd';
import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  Component,
} from 'react';
import { SlidePanel, Loading, Message, Button, CpuMemSelectorField } from '@ali/cnd';
import { unshiftZero } from '~/utils/global';
import Form from '@ali/deep-form';
import { NumberField } from '@ali/deep';
import ProcessField from '~/components/version-list/version-create/ProcessField';
import { DEPLOY_TYPE, PIPELINE_STATUS, PACKAGE_TYPE } from '~/constants/application';
import _ from 'lodash';
import cls from 'classnames';
import services from '~/services';
import { CpuMemDataSource } from '~/components/app-create/constants';

type Props = {
  history: any;
  urlSearch: any;
  regionId: string;
  appData: {
    [key: string]: any;
  };
  versionId: string;
  deployType: DEPLOY_TYPE;
  packageValues: any;
  repoistoryValues: any;
  callback?: () => void;
};

export default forwardRef(
  (
    {
      history,
      urlSearch,
      regionId,
      appData,
      versionId,
      deployType,
      packageValues,
      repoistoryValues,
      callback,
    }: Props,
    ref,
  ) => {
    const { applicationID: applicationId, applicationName } = appData;
    const field = useRef(null);
    const paramsRef = useRef({});
    const [visible, setVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [isUpdateSuccess, setIsUpdateSuccess] = useState(false);
    const [pipelineRunId, setPipelineRunId] = useState('');
    const [buildStatus, setBuildStatus] = useState(PIPELINE_STATUS.FAIL);

    useEffect(() => {
      if (!visible) return;
      initVolumeData();
    }, [visible]);

    useEffect(() => {
      if (Object.keys(appData).length === 0) return;
      initRequestParams();
    }, [appData]);

    const initVolumeData = () => {
      if (appData) {
        const { cpu, memorySize, instanceConcurrency } = appData;
        const values = {
          spec: {
            cpu,
            memory: Math.ceil(parseInt(memorySize, 10) / 1024),
          },
          instanceConcurrency,
        };
        field?.current.setValues(values);
      }
    };

    const initRequestParams = () => {
      setIsLoading(true);
      paramsRef.current = {
        instanceConcurrency: appData?.instanceConcurrency,
        caPort: appData?.caPort,
        cpu: appData?.cpu,
        memorySize: appData?.memorySize,
        timeout: appData?.timeout,
        // imageConfig: data?.imageConfig,
        // 更新传null 表示之前配置不变, 传{}表示要去掉之前的配置
        imageConfig: _.get(appData, 'imageConfig', null),
        nasConfig: _.get(appData, 'nasConfig', null),
        ossMountConfig: _.get(appData, 'ossMountConfig', null),
        environmentVariables: _.get(appData, 'environmentVariables', null),
        logConfig: _.get(appData, 'logConfig', null),
        slsConfig: _.get(appData, 'slsConfig', null),
        startupProbe: _.get(appData, 'startupProbe', null),
        livenessProbe: _.get(appData, 'livenessProbe', null),
      };
      setIsLoading(false);
    };

    useImperativeHandle(ref, () => ({
      show: () => setVisible(true),
      hide: () => setVisible(false),
    }));

    const handleClose = () => {
      setVisible(false);
      setTimeout(() => {
        setIsProcessing(false);
        setIsUpdateSuccess(false);
        if (pipelineRunId) {
          setPipelineRunId('');
        }
      }, 200);
    };

    const handleSubmit = async () => {
      const values = await field?.current.getValues();
      const { params } = values;

      switch (deployType) {
        case DEPLOY_TYPE.IMAGE:
          // 镜像部署 直接调用更新应用接口
          webAppImageDeploy(params);
          break;
        case DEPLOY_TYPE.WEB_PACKAGE:
          // 调用 updateBuildPipeline
          webAppPackageDeploy(params);
          break;
        case DEPLOY_TYPE.REPOISTORY:
          webAppRepoistoryDeploy(params);
          break;
        default:
          break;
      }
    };

    const webAppImageDeploy = async (newParams) => {
      setIsProcessing(true);
      try {
        const result = await services.updateAppVersions(
          applicationId,
          applicationName,
          // @ts-ignore
          {
            ...paramsRef.current,
            // 加上健康检查的新值
            ...newParams,
          },
        );
        if (result) {
          setVisible(false);
          callback && callback();
        }
      } catch (err) {
        Message.error(err.message);
      }
      setIsProcessing(false);
    };

    const webAppPackageDeploy = async (newParams) => {
      setIsProcessing(true);
      const {
        Jdk,
        PackageType,
        PackageUrl,
        FileName,
        PackageVersion,
        RunCommand = '',
        Container,
      } = packageValues;
      const buildConfig = {
        RunCommand,
        BuildType: 'Buildpack',
        RuntimeVersion: Jdk,
        RuntimeType: 'java',
      };
      if (PackageType === PACKAGE_TYPE.WAR) {
        Reflect.set(buildConfig, 'TomcatConfig', { Version: Container });
      }
      const result = await services.updateBuildPipeline({
        params: {
          content: {
            ApplicationId: applicationId,
            ApplicationName: applicationName,
            BuildConfig: buildConfig,
            PackageConfig: {
              PackageType,
              PackageUrl,
              PackageVersion, // 包的版本
              PackageName: FileName, // 文件名
            },
            ImageConfig: {
              InstanceType: 'SHARE_ACREE',
            },
            DeployConfig: {
              UpdateApplicationInput: JSON.stringify({
                ...paramsRef.current,
                // 加上健康检查的新值
                ...newParams,
              }),
            },
          },
        },
        customErrorHandle: (err, data, callback) => {
          setIsProcessing(false);
          setIsUpdateSuccess(false);
          setPipelineRunId('');
          callback && callback();
        },
      });
      const { PipelineRunId: _pipelineRunId } = result;
      setIsProcessing(false);
      setPipelineRunId(_pipelineRunId);
      setIsUpdateSuccess(true);
    };

    const webAppRepoistoryDeploy = async (newParams) => {
      setIsProcessing(true);
      const { BuildConfig, ImageConfig, TriggerConfig, CodeConfig } = repoistoryValues;
      const result = await services.updateBuildPipeline({
        params: {
          content: {
            BuildConfig,
            ImageConfig,
            TriggerConfig,
            CodeConfig,
            DeployConfig: {
              UpdateApplicationInput: JSON.stringify({
                ...paramsRef.current,
                // 加上健康检查的新值
                ...newParams,
              }),
            },
            Enabled: true,
            ApplicationId: applicationId,
            ApplicationName: applicationName,
          },
        },
        customErrorHandle: (err, data, callback) => {
          setIsProcessing(false);
          setIsUpdateSuccess(false);
          setPipelineRunId('');
          callback && callback();
        },
      });
      const { PipelineRunId: _pipelineRunId } = result;
      setIsProcessing(false);
      setPipelineRunId(_pipelineRunId);
      setIsUpdateSuccess(true);
    };

    const title = React.useMemo(() => {
      const version = unshiftZero(versionId);
      let _title = intl(
        'saenext.configure-info.VolumeInfo.VolumeInfoSlide.VersionVersionCapacitySettings',
        { version: version },
      );
      if (isUpdateSuccess) {
        _title = intl(
          'saenext.configure-info.VolumeInfo.VolumeInfoSlide.PipelinerunidProgressPreview',
          { pipelineRunId: pipelineRunId },
        );
      }
      return _title;
    }, [versionId, isUpdateSuccess]);

    const customFooter = React.useMemo(() => {
      if (isUpdateSuccess) {
        return (
          <>
            <Button
              type="primary"
              onClick={() => {
                setIsUpdateSuccess(false);
              }}
            >
              {intl('saenext.configure-info.VolumeInfo.VolumeInfoSlide.ReturnConfiguration')}
            </Button>
            <Button
              onClick={() => {
                setVisible(false);
                setIsUpdateSuccess(false);
                setPipelineRunId('');
              }}
            >
              {intl('saenext.configure-info.VolumeInfo.VolumeInfoSlide.Close')}
            </Button>
          </>
        );
      }
      return (
        <div>
          <Button type="primary" loading={isProcessing} onClick={handleSubmit}>
            {intl('saenext.configure-info.VolumeInfo.VolumeInfoSlide.Ok')}
          </Button>
          <Button className="ml-s" onClick={handleClose}>
            {intl('saenext.configure-info.VolumeInfo.VolumeInfoSlide.Cancel')}
          </Button>
        </div>
      );
    }, [appData, packageValues, repoistoryValues, isUpdateSuccess, isProcessing, buildStatus]);

    return (
      <SlidePanel
        title={title}
        width="large"
        isShowing={visible}
        onClose={handleClose}
        customFooter={customFooter}
      >
        <Loading visible={isLoading} className="full-width full-height">
          <SlideField
            ref={field}
            // @ts-ignore
            style={{
              display: isUpdateSuccess && deployType !== DEPLOY_TYPE.IMAGE ? 'none' : 'block',
            }}
          />

          <ProcessField
            history={history}
            urlSearch={urlSearch}
            regionId={regionId}
            deployType={deployType}
            pipelineRunId={pipelineRunId}
            applicationId={applicationId}
            applicationName={applicationName}
            setBuildStatus={(_buildStatus) => {
              setBuildStatus(_buildStatus);
            }}
            style={{
              display: isUpdateSuccess && deployType !== DEPLOY_TYPE.IMAGE ? 'block' : 'none',
            }}
          />
        </Loading>
      </SlidePanel>
    );
  },
);

class SlideField extends Component {
  private field = React.createRef() as any;

  constructor(props) {
    super(props);
    this.setValues = this.setValues.bind(this);
    this.getValues = this.getValues.bind(this);
  }

  setValues(value) {
    this.field.setValue(value);
  }

  getValues() {
    return new Promise((resolve, reject) => {
      this.field.validate(async (error, values) => {
        if (error) return;
        const { instanceConcurrency, spec } = values;
        const params = {
          instanceConcurrency,
          cpu: spec.cpu,
          memorySize: spec.memory * 1024,
        };
        resolve({ params });
      });
    });
  }

  render() {
    return (
      <Form
        className="pb-xl"
        // @ts-ignore
        style={this.props?.style}
        ref={(c) => {
          if (c) {
            this.field = c.getInstance();
          }
        }}
      >
        <CpuMemSelectorField
          required
          hasClear
          name="spec"
          label={intl('saenext.configure-info.VolumeInfo.VolumeInfoSlide.SingleInstanceType')}
          dataSource={CpuMemDataSource}
          className={cls({ 'full24-width': true })}
          validation={[
            {
              type: 'required',
              message: intl(
                'saenext.configure-info.VolumeInfo.VolumeInfoSlide.SelectASingleInstanceType',
              ),
            },
          ]}
        />

        <NumberField
          required
          name="instanceConcurrency"
          label={intl(
            'saenext.configure-info.VolumeInfo.VolumeInfoSlide.NumberOfConcurrentRequestsFor',
          )}
          min={1}
          max={200}
          className={cls({ 'full24-width': true })}
          innerAfter={
            <span>{intl('saenext.configure-info.VolumeInfo.VolumeInfoSlide.Times')}</span>
          }
          validation={[
            {
              type: 'required',
              message: intl(
                'saenext.configure-info.VolumeInfo.VolumeInfoSlide.EnterTheNumberOfConcurrent',
              ),
            },
          ]}
        />
      </Form>
    );
  }
}
