import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Description, Copy, Button, Icon } from '@ali/cnd';
import _ from 'lodash';

type Props = {
  appData: {
    [key: string]: any;
  };
  onEdit: () => void;
};

export default ({ appData, onEdit }: Props) => {
  const [items, setItems] = useState([
    {
      dataIndex: 'enable',
      label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.HealthCheck'),
      // @ts-ignore
      render: (_) => (
        <div className="flex">
          <Icon type="warning_fill" size="small" style={{ color: '#ffce03' }} />
          <span className="ml-s">
            {intl('saenext.configure-info.HealthCheck.HealthCheckFields.NotEnabled')}
          </span>
        </div>
      ),
    },
  ]);
  const [dataSource, setDataSource] = useState({});

  useEffect(() => {
    initializeRender();
  }, [appData]);

  const initializeRender = () => {
    if (appData.startupProbe || appData.livenessProbe) {
      // 回显 card
      const _dataSource = _.cloneDeep(
        _.get(appData, 'startupProbe', null) || _.get(appData, 'livenessProbe', null) || {},
      );
      const checks = [];
      if (appData.startupProbe) {
        checks.push(
          intl(
            'saenext.configure-info.HealthCheck.HealthCheckFields.StartupCheckStartupConfiguration',
          ),
        );
      }
      if (appData.livenessProbe) {
        checks.push(
          intl(
            'saenext.configure-info.HealthCheck.HealthCheckFields.InstanceSurvivalCheckLivenessConfiguration',
          ),
        );
      }
      Object.assign(_dataSource, {
        enable: !!checks.length,
        checkType: checks.join(','),
      });

      const _items = [
        {
          dataIndex: 'checkType',
          label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.CheckType'),
          // @ts-ignore
          render: (value) => <span>{value}</span>,
        },
        {
          dataIndex: 'probeHandler',
          label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.InspectionMethod'),
          // @ts-ignore
          render: (value) => {
            if (!value) {
              return <span>-</span>;
            }
            const [checkType] = Object.keys(value);
            if (checkType === 'tcpSocket') {
              return (
                <span>
                  {intl('saenext.configure-info.HealthCheck.HealthCheckFields.TcpPortCheck')}
                </span>
              );
            }
            return (
              <span>
                {intl('saenext.configure-info.HealthCheck.HealthCheckFields.HttpRequestCheck')}
              </span>
            );
          },
        },
        {
          dataIndex: 'probeHandler',
          label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.Path'),
          // @ts-ignore
          render: (value) => {
            if (!value) {
              return <span>-</span>;
            }
            const [checkType] = Object.keys(value);
            const path = value[checkType]?.path;
            return <Copy text={path}>{path}</Copy>;
          },
        },
        {
          dataIndex: 'probeHandler',
          label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.Port'),
          // @ts-ignore
          render: (value) => {
            if (!value) {
              return <span>-</span>;
            }
            const [checkType] = Object.keys(value);
            const port = value[checkType]?.port;
            return <Copy text={port}>{port}</Copy>;
          },
        },
        {
          dataIndex: 'initialDelaySeconds',
          label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.InitialDelayTime'),
          // @ts-ignore
          render: (value) => (
            <span>
              {intl('saenext.configure-info.HealthCheck.HealthCheckFields.ValueSeconds', {
                value: value,
              })}
            </span>
          ),
        },
        {
          dataIndex: 'timeoutSeconds',
          label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.Timeout'),
          // @ts-ignore
          render: (value) => (
            <span>
              {intl('saenext.configure-info.HealthCheck.HealthCheckFields.ValueSeconds', {
                value: value,
              })}
            </span>
          ),
        },
        {
          dataIndex: 'periodSeconds',
          label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.CheckCycle'),
          // @ts-ignore
          render: (value) => (
            <span>
              {intl('saenext.configure-info.HealthCheck.HealthCheckFields.ValueSeconds', {
                value: value,
              })}
            </span>
          ),
        },
        {
          dataIndex: 'failureThreshold',
          label: intl('saenext.configure-info.HealthCheck.HealthCheckFields.FailureThreshold'),
          // @ts-ignore
          render: (value) => (
            <span>
              {intl('saenext.configure-info.HealthCheck.HealthCheckFields.ValueTimes', {
                value: value,
              })}
            </span>
          ),
        },
      ];

      setItems(_items);
      setDataSource(_dataSource);
    }
  };

  return (
    <Description
      title={intl('saenext.configure-info.HealthCheck.HealthCheckFields.HealthCheck')}
      items={items}
      dataSource={dataSource}
      actions={
        <Button
          text
          type="primary"
          style={{ fontWeight: 'normal' }}
          onClick={() => {
            onEdit && onEdit();
          }}
        >
          {intl('saenext.configure-info.HealthCheck.HealthCheckFields.DeployANewVersion')}
        </Button>
      }
    />
  );
};
