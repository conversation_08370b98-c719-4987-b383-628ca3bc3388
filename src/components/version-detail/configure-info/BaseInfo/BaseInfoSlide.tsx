/* eslint react/prop-types: 0 */
import React, { useRef, forwardRef, useImperativeHandle } from 'react';
import { DEPLOY_TYPE } from '~/constants/application';
import UpdateImagePanel from '~/components/version-list/UpdateImagePanel';
import UpdateRepoistoryPanel from '~/components/version-list/UpdateRepoistoryPanel';
import UpdatePackagePanel from '~/components/version-list/UpdatePackagePanel';

type Props = {
  history: any;
  urlSearch: any;
  regionId: string;
  versionId: string;
  applicationId: string;
  applicationName: string;
  deployType: DEPLOY_TYPE;
}



export default forwardRef(({ history, urlSearch, regionId, versionId, applicationId, applicationName, deployType }: Props, ref) => {
  const refImage = useRef(null);
  const refRepoistory = useRef(null);
  const refPackage = useRef(null);

  useImperativeHandle(ref, () => ({
    show: () => {
      let ref = refImage;
      switch (deployType) {
        case DEPLOY_TYPE.IMAGE:
          ref = refImage;
          break;
        case DEPLOY_TYPE.WEB_PACKAGE:
          ref = refPackage;
          break;
        case DEPLOY_TYPE.REPOISTORY:
          ref = refRepoistory;
          break;
        default:
          break;
      }
      ref.current?.show();
    },
  }), [deployType]);

  const renderSlide = () => {
    switch (deployType) {
      case DEPLOY_TYPE.IMAGE:
        return (
          <UpdateImagePanel 
            ref={refImage} 
            regionId={regionId}
            appVersionId={versionId}
            applicationID={applicationId}
            setRefreshIndex={() => {
              const url = `/${regionId}/app-list/${applicationId}/web-app/version?name=${applicationName}`;
              history.push(url);
            }}
          />
        );
      case DEPLOY_TYPE.WEB_PACKAGE:
        return (
          <UpdatePackagePanel 
            ref={refPackage}
            history={history}
            urlSearch={urlSearch}
            regionId={regionId}
            appVersionId={versionId}
            applicationID={applicationId}
            setRefreshIndex={() => {
              const url = `/${regionId}/app-list/${applicationId}/web-app/version?name=${applicationName}`;
              history.push(url);
            }}
          />
        );
      case DEPLOY_TYPE.REPOISTORY:
        return (
          <UpdateRepoistoryPanel
            ref={refRepoistory}
            history={history}
            urlSearch={urlSearch}
            regionId={regionId}
            appVersionId={versionId}
            applicationID={applicationId}
            setRefreshIndex={() => {
              const url = `/${regionId}/app-list/${applicationId}/web-app/version?name=${applicationName}`;
              history.push(url);
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      {renderSlide()}
    </>
  );
});

