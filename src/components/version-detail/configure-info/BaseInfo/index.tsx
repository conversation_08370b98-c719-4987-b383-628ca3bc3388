/* eslint react/prop-types: 0 */
import React, { useState, useRef } from 'react';
import BaseInfoFields from './BaseInfoFields';
import BaseInfoSlide from './BaseInfoSlide';
import { DEPLOY_TYPE } from '~/constants/application';
import { Loading } from '@ali/cnd';


type Props = {
  history: any;
  urlSearch: any;
  regionId: string;
  appData: {
    [key: string]: any;
  };
  versionId: string;
  deployType: DEPLOY_TYPE;
  packageValues: any;
  repoistoryValues: any;
}


export default (props: Props) => {
  const { regionId, appData, versionId, deployType } = props;
  const { applicationID: applicationId, applicationName } = appData;
  const refSlide = useRef(null);
  // @ts-ignore
  const [isLoading, setIsLoading] = useState(false);

  return (
    <div className="border-b pb-l">
      <Loading
        visible={isLoading}
        style={{ display: "block", height: '100%' }}
      >
        <BaseInfoFields 
          {...props} 
          onEdit={() => {
            refSlide?.current.show()
          }} 
        />
        <BaseInfoSlide
          ref={refSlide}
          regionId={regionId} 
          history={props.history} 
          urlSearch={props.urlSearch}
          versionId={versionId} 
          deployType={deployType}
          applicationId={applicationId}
          applicationName={applicationName} 
        />
      </Loading>
    </div>
  );
};