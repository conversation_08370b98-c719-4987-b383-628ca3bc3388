import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Description, Button } from '@ali/cnd';
import { EnvEditorPreview } from '@ali/cnd';
import _ from 'lodash';

type Props = {
  appData: {
    [key: string]: any;
  };
  onEdit: () => void;
};

export default ({ appData, onEdit }: Props) => {
  const [dataSource, setDataSource] = useState({});

  useEffect(() => {
    initializeRender();
  }, [appData]);

  const initializeRender = () => {
    if (Object.getOwnPropertyNames(appData.environmentVariables || {}).length) {
      const _dataSource = Object.keys(appData.environmentVariables).map((key) => {
        const { environmentVariables } = appData;
        const value = environmentVariables[key];
        return { key, value };
      });
      setDataSource(_dataSource);
    }
  };

  return (
    <Description
      title={intl('saenext.configure-info.Environment.EnvironmentFields.EnvironmentVariables')}
      actions={
        <Button
          text
          type="primary"
          style={{ fontWeight: 'normal' }}
          onClick={() => {
            onEdit && onEdit();
          }}
        >
          {intl('saenext.configure-info.Environment.EnvironmentFields.DeployANewVersion')}
        </Button>
      }
      extra={
        <>
          <span className="inline-block gray-text mb-l">
            {intl('saenext.configure-info.Environment.EnvironmentFields.SetSomeVariablesInThe')}
          </span>
          <EnvEditorPreview value={dataSource} />
        </>
      }
    />
  );
};
