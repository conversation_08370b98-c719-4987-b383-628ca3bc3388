/* eslint react/prop-types: 0 */
import React, { useState, useEffect, useRef } from 'react';
import { Loading } from '@ali/cnd';
import _ from 'lodash';
import { DEPLOY_TYPE } from '~/constants/application';
import EnvironmentFields from './EnvironmentFields';
import EnvironmentSlide from './EnvironmentSlide';

type Props = {
  history: any;
  urlSearch: any;
  regionId: string;
  appData: {
    [key: string]: any;
  };
  versionId: string;
  deployType: DEPLOY_TYPE;
  packageValues: any;
  repoistoryValues: any;
  callback?: () => void;
}


export default (props: Props) => {
  const { history, urlSearch, regionId, appData, versionId, deployType, packageValues, repoistoryValues } = props;
  const refSlide = useRef(null);
  // @ts-ignore
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
  }, [appData]);

  return (
    <div className="mt-l">
      <Loading
        visible={isLoading}
        style={{ display: "block", height: '100%' }}
      >
        <EnvironmentFields 
          appData={appData} 
          onEdit={() => {
            refSlide?.current.show();
          }}
        />
        <EnvironmentSlide 
          ref={refSlide}
          history={history}
          urlSearch={urlSearch}
          regionId={regionId}
          appData={appData}
          versionId={versionId}
          deployType={deployType}
          packageValues={packageValues}
          repoistoryValues={repoistoryValues}
          callback={props.callback && props.callback}
        />
      </Loading>
    </div>
  );
};

