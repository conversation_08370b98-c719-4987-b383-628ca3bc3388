import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Description, Copy, Button, Icon } from '@ali/cnd';
import _ from 'lodash';

type Props = {
  appData: {
    [key: string]: any;
  };
  onEdit: () => void;
};

export default ({ appData, onEdit }: Props) => {
  const [items, setItems] = useState([
    {
      dataIndex: 'enable',
      label: intl('saenext.configure-info.StartCmd.StartCmdFields.CommandLine'),
      // @ts-ignore
      render: (_) => (
        <div className="flex">
          <Icon type="warning_fill" size="small" style={{ color: '#ffce03' }} />
          <span className="ml-s">
            {intl('saenext.configure-info.StartCmd.StartCmdFields.NotSet')}
          </span>
        </div>
      ),
    },
  ]);
  const [dataSource, setDataSource] = useState({});

  useEffect(() => {
    initializeRender();
  }, [appData]);

  const initializeRender = () => {
    if (appData.command && appData.args) {
      const _dataSource = {
        command: `${appData.command}: ${appData.args}`,
      };

      const _items = [
        {
          dataIndex: 'command',
          label: intl('saenext.configure-info.StartCmd.StartCmdFields.StartCommand'),
          // @ts-ignore
          render: (value) => <Copy text={value}>{value}</Copy>,
        },
      ];

      setItems(_items);
      setDataSource(_dataSource);
    }
  };

  return (
    <Description
      title={intl('saenext.configure-info.StartCmd.StartCmdFields.StartCommand')}
      items={items}
      dataSource={dataSource}
      actions={
        <Button
          text
          type="primary"
          style={{ fontWeight: 'normal' }}
          onClick={() => {
            onEdit && onEdit();
          }}
        >
          {intl('saenext.configure-info.StartCmd.StartCmdFields.DeployANewVersion')}
        </Button>
      }
    />
  );
};
