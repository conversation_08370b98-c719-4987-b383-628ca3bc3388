/* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Loading } from '@ali/cnd';
import services from '~/services';
import { getParams } from '~/utils/global';
import _ from 'lodash';
import BaseInfo from './BaseInfo';
import VolumeInfo from './VolumeInfo';
import StartCmd from './StartCmd';
import Environment from './Environment';
import HealthCheck from './HealthCheck';
import NetworkCard from './NetworkCard';
import { DEPLOY_TYPE } from '~/constants/application';

type ChildProps = {
  history: any;
  urlSearch: any;
  regionId: string;
  appData: {
    [key: string]: any;
  };
  versionId: string;
  deployType: DEPLOY_TYPE;
  packageValues: any;
  repoistoryValues: any;
  callback?: () => void;
};

export default (props) => {
  const {
    match: {
      params: {
        regionId, 
        id: applicationID, 
        versionId
      },
    },
    history,
    location: { search },
    appData,
    deployType,
    setRefreshIndex,
  } = props;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const applicationName = getParams('name');
  // @ts-ignore
  const [showHealthCheck, setShowHealthCheck] = useState(false);
  const [networkConfigure, setNetworkConfigure] = useState({enableVpc: false});
  const [childProps, setChildProps] = useState({ history, urlSearch: search, regionId, appData, versionId, deployType, packageValues: {}, repoistoryValues: {} });

  useEffect(() => {
    const _asiWhiteFeature = _.get(window, 'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS.asi-enable');
    const _asiRegionFeature = _.get(window, 'ALIYUN_CONSOLE_GLOBAL.onAsiFeature', []);
    let _showHealthCheck = !!_asiRegionFeature.length;
    if (_asiWhiteFeature && _asiRegionFeature.length) {
      const regions = _.map(_asiRegionFeature, (item) => item.id);
      if (!regions.includes(regionId)) {
        _showHealthCheck = false;
      }
    }

    setShowHealthCheck(_asiWhiteFeature && _showHealthCheck);

    initAppVersionDetail();
  }, [appData]);


  const initAppVersionDetail = async () => {
    setIsLoading(true);
    let _packageValues, _repoistoryValues = {};
    switch (deployType) {
      case DEPLOY_TYPE.WEB_PACKAGE:
        _packageValues = await initWebPackageValues();
        break;
      case DEPLOY_TYPE.REPOISTORY:
        _repoistoryValues = await initRepoistoryValues();
        break;
    }
    const _networkConfigure = { enableVpc: false };
    if (appData.vpcConfig && appData.vpcConfig?.vpcId) {
      // 回显card
      Object.assign(_networkConfigure, {
        ...appData.vpcConfig,
        enableVpc: true,
      });
    }
    setNetworkConfigure(_networkConfigure);
    setChildProps({
      history: history,
      urlSearch: search,
      regionId: regionId,
      appData: appData,
      versionId: versionId,
      deployType: deployType,
      packageValues: _packageValues,
      repoistoryValues: _repoistoryValues,
    });
    setIsLoading(false);
  };

  const initWebPackageValues = async () => {
    // 对应版本的代码包信息
    const res = await services.DescribeBuildPipelineRun({
      VersionId: versionId,
      ApplicationId: applicationID,
      ApplicationName: applicationName,
    });
    const { Data = {} } = res;
    const { PackageConfig = {}, BuildConfig = {} } = Data;
    return {
      PackageType: PackageConfig?.PackageType,
      UploadType: 'upload',
      PackageUrl: PackageConfig?.PackageUrl,
      FileName: PackageConfig?.PackageName,
      PackageVersion: PackageConfig?.PackageVersion,
      RunCommand: BuildConfig?.RunCommand,
      // 缺少 Jdk Container,
      Jdk: BuildConfig?.RuntimeVersion,
      Container: _.get(BuildConfig, 'TomcatConfig.Version'),
    };
  };

  const initRepoistoryValues = async () => {
    const res = await services.DescribeBuildPipelineRun({
      VersionId: versionId,
      ApplicationId: applicationID,
      ApplicationName: applicationName,
    });
    const { Data = {} } = res;
    const { BuildConfig = {}, ImageConfig = {}, TriggerConfig = {}, CodeConfig = {} } = Data;
    return {
      BuildConfig,
      ImageConfig,
      TriggerConfig,
      CodeConfig,
      Provider: CodeConfig?.Provider,
      RepoFullName: CodeConfig?.RepoFullName,
      BranchName: CodeConfig?.BranchName,
      CommitId: CodeConfig?.CommitId,
      CommitUrl: CodeConfig?.CommitUrl
    };
  };

  // @ts-ignore
  const updateWebAttributes = (values, resolve, reject) => {
    services.updateWebAttributes(
      applicationID,
      applicationName, 
      values
    ).then(res => {
      if (!res) {
        reject();
        return;
      }
      resolve(res);
      setRefreshIndex(Date.now());
     })
     .catch(e => {
      reject(e);
     })
  };

  const callback = () => {
    const url = `/${regionId}/app-list/${applicationID}/web-app/version?name=${applicationName}`;
    history.push(url);
  };


  return (
    <Loading
      visible={isLoading}
      className="next-extra-card"
      style={{ display: "block", height: '100%' }}
    >
      <BaseInfo 
        {...childProps as ChildProps} 
      />
      <VolumeInfo 
        {...childProps as ChildProps} 
        callback={() => callback()} 
      />
      <StartCmd 
        {...childProps as ChildProps} 
        callback={() => callback()} 
      />
      <Environment 
        {...childProps as ChildProps} 
        callback={() => callback()}
      />
      {
        showHealthCheck ? (
          <HealthCheck
            {...childProps as ChildProps}
            callback={() => callback()}
          />
        ) : null
      }
      <NetworkCard
        network={networkConfigure}
      />
    </Loading>
  );
};

