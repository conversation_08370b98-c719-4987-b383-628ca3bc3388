import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { Component } from 'react';
import { Description, Loading, Copy, Icon, Tag } from '@ali/cnd';
import _ from 'lodash';

interface INetworkProps {
  network: {
    enableVpc: boolean;
    vpcId?: string;
    vSwitchIds?: string[];
    securityGroupId?: string;
  };
}

interface INetworkState {
  isLoading: boolean;
}

class NetworkCard extends Component<INetworkProps, INetworkState> {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
    };
  }

  render(): React.ReactNode {
    const { isLoading } = this.state;
    const { network } = this.props;

    return (
      <div className="mt-l border-b pb-l">
        <Loading visible={isLoading} style={{ display: 'block', height: '100%' }}>
          <Description
            title={intl('saenext.version-detail.configure-info.NetworkCard.NetworkSettings')}
            dataSource={network}
            items={[
              {
                dataIndex: 'enableVpc',
                label: intl(
                  'saenext.version-detail.configure-info.NetworkCard.AllowApplicationsToAccessVpc',
                ),
                // @ts-ignore
                render: (value) => (
                  <div>
                    {value ? (
                      <div>
                        <Icon type="check_fill" size="small" style={{ color: '#009431' }} />
                        <span className="ml-s">
                          {intl('saenext.version-detail.configure-info.NetworkCard.Allow')}
                        </span>
                      </div>
                    ) : (
                      <div className="flex">
                        <Icon type="warning_fill" size="small" style={{ color: '#ffce03' }} />
                        <span className="ml-s">
                          {intl('saenext.version-detail.configure-info.NetworkCard.NotAllowed')}
                        </span>
                      </div>
                    )}
                  </div>
                ),
              },
              {
                dataIndex: 'vpcId',
                label: 'VPC',
                // @ts-ignore
                render: (value) => (value ? <Copy text={value}>{value}</Copy> : '-'),
              },
              {
                dataIndex: 'vSwitchIds',
                label: 'vSwitch',
                render: (value) =>
                  // @ts-ignore
                  value?.length ? (
                    <Tag.Group>
                      {value.map((item) => (
                        <Tag key={item}>{item}</Tag>
                      ))}
                    </Tag.Group>
                  ) : '-',
              },
              {
                dataIndex: 'securityGroupId',
                label: intl('saenext.version-detail.configure-info.NetworkCard.SecurityGroup'),
                // @ts-ignore
                render: (value) => (value ? <Copy text={value}>{value}</Copy> : '-'),
              },
            ]}
          />
        </Loading>
      </div>
    );
  }
}

export default NetworkCard;
