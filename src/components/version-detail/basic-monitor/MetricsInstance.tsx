import React, { useState, useEffect } from 'react';
import { Grid } from '@ali/cnd';
import services from '~/services';
import { 
  IMetricsProps, 
} from '~/components/monitor-indicator/Constant';
import RequestCountIndicator from '../../monitor-indicator/RequestCountIndicator';
import LatencyIndicator from '../../monitor-indicator/LatencyIndicator';
import HttpStatusIndicator from '../../monitor-indicator/HttpStatusIndicator';
import CpuUtilizationIndicator from '../../monitor-indicator/CpuUtilizationIndicator';
import MemUtilizationIndicator from '../../monitor-indicator/MemUtilizationIndicator';
import DiskUtilizationIndicator from '../../monitor-indicator/DiskUtilizationIndicator';
import NetworkPacketIndicator from '../../monitor-indicator/NetworkPacketIndicator';
import SystemLoadIndicator from '../../monitor-indicator/SystemLoadIndicator';


const { Row, Col } = Grid;
type Props = {
  instanceId: string;
  applicationID: string;
  applicationName: string;
  refreshIndex: number;
  timeTuple: {
    start: number,
    end: number,
    key: string,
    period: number,
  };
  setIsLoading: (loading: boolean) => void;
};

export default (props: Props) => {
  const { timeTuple, refreshIndex, setIsLoading } = props;
  const [data, setData] = useState<{[key: string]: any[]}>();

  useEffect(() => {
    // if (!refreshIndex) return;
    getAppVersionInstanceMetrics();
  }, [refreshIndex, JSON.stringify(timeTuple)]);


  // 版本实例 基础监控
  const getAppVersionInstanceMetrics = async() => {
    setIsLoading && setIsLoading(true);
    const { applicationName, applicationID, instanceId } = props;
    const data = await services.getAppVersionInstanceMetrics({
      applicationID,
      applicationName,
      startTime: timeTuple.start,
      endTime: timeTuple.end,
      period: timeTuple.period,
      metrics: [
        'InstanceRequestsV2',                      // 请求次数（Request Count）
        'InstanceRequestsLatencyV2',                       // 请求响应时间（Request Latency）
        'InstanceHTTPStatus2xxV2',                 // HTTP 错误（HTTP Error）
        'InstanceHTTPStatus3xxV2',
        'InstanceHTTPStatus4xxV2',
        'InstanceHTTPStatus5xxV2',
        'InstanceCPUUtilizations',                // CPU使用率（CPU Utilization）
        'InstanceMemoryUtilizations',             // 内存使用率（Memory Utilization）
        'InstanceLoad1m',                         // 系统平均负载（1min）
        'InstanceDiskUsageUtilizations',          // 磁盘使用率（Disk Utilization）
        'InstanceNetworkRXBytes',                 // 字节流量（入 + 出）
        'InstanceNetworkTXBytes',  
        // 'InstanceNetworkTCPTotalConnections',     // TCP连接数（Count）
        // 'InstanceNetworkTCPActiveConnections',
        // 'InstanceNetworkTCPInactiveConnections',
        // 'InstanceNetworkTCPInuse',
        // 'InstanceNetworkTCPOrphan',
        // 'InstanceNetworkTCPTw',
        // 'InstanceNetworkTCPAlloc',
      ].join(','),
      // 监控补0
      fillZero: true,
      instanceID: instanceId,
    });

    const { metrics = {} } = data;
    const {
      InstanceRequestsV2 = [] as IMetricsProps[],
      InstanceRequestsLatencyV2 = [] as IMetricsProps[],
      InstanceHTTPStatus2xxV2 = [] as IMetricsProps[],
      InstanceHTTPStatus3xxV2 = [] as IMetricsProps[],
      InstanceHTTPStatus4xxV2 = [] as IMetricsProps[],
      InstanceHTTPStatus5xxV2 = [] as IMetricsProps[],
      InstanceCPUUtilizations = [] as IMetricsProps[],
      InstanceMemoryUtilizations = [] as IMetricsProps[],
      InstanceLoad1m = [] as IMetricsProps[],
      InstanceDiskUsageUtilizations = [] as IMetricsProps[],
      InstanceNetworkRXBytes = [] as IMetricsProps[],
      InstanceNetworkTXBytes = [] as IMetricsProps[],
      // InstanceNetworkTCPActiveConnections = [] as IMetricsProps[],
      // InstanceNetworkTCPAlloc = [] as IMetricsProps[],
      // InstanceNetworkTCPInactiveConnections = [] as IMetricsProps[],
      // InstanceNetworkTCPInuse = [] as IMetricsProps[],
      // InstanceNetworkTCPOrphan = [] as IMetricsProps[],
      // InstanceNetworkTCPTotalConnections = [] as IMetricsProps[],
      // InstanceNetworkTCPTw = [] as IMetricsProps[],
    } = metrics;
    const _data = {};
    Object.assign(_data, {
      requests: InstanceRequestsV2,
      latency: InstanceRequestsLatencyV2,
      httpStatus: [
        InstanceHTTPStatus2xxV2,
        InstanceHTTPStatus3xxV2,
        InstanceHTTPStatus4xxV2,
        InstanceHTTPStatus5xxV2,
      ],
      cpuUtilizations: InstanceCPUUtilizations,
      memUtilizations: InstanceMemoryUtilizations,
      diskUtilizations: InstanceDiskUsageUtilizations,
      systemLoads: InstanceLoad1m,
      networkPacket: [
        InstanceNetworkRXBytes,
        InstanceNetworkTXBytes,
      ],
    });
    setData(_data);
    setIsLoading && setIsLoading(false);
  }

  return (
    <>
      <Row gutter={24}>
        <Col span={8}>
          <RequestCountIndicator data={data?.requests} />
        </Col>
        <Col span={8}>
          <LatencyIndicator data={data?.latency} />
        </Col>
        <Col span={8}>
          <HttpStatusIndicator data={data?.httpStatus} />
        </Col>
      </Row>
      <Row gutter={24} className="mt-xl">
        <Col span={8}>
          <CpuUtilizationIndicator data={data?.cpuUtilizations} />
        </Col>
        <Col span={8}>
          <MemUtilizationIndicator data={data?.memUtilizations} />
        </Col>
        <Col span={8}>
          <SystemLoadIndicator data={data?.systemLoads} />
        </Col>
      </Row>
      <Row gutter={24} className="mt-xl">
        <Col span={8}>
          <DiskUtilizationIndicator data={data?.diskUtilizations} />
        </Col>
        <Col span={8}>
          <NetworkPacketIndicator data={data?.networkPacket} />
        </Col>
      </Row>
    </>
  );
};