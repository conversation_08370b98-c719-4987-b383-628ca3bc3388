import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Icon, Select } from '@ali/cnd';
import { getParams } from '~/utils/global';
import services from '~/services';
import MonitorTimeRange, { getTimes } from '~/components/shared/TimeRangeMonitor';
import Metrics, { MONITOR_VER_TYPE, MONITOR_INS_TYPE, MONITOR_VER_VALUE } from './Metrics';
import InstanceStatus from '../instance-list/InstanceStatus';

const DEFAULT_ALL_INSTANCE = {
  label: intl('saenext.version-detail.basic-monitor.AllInstances'),
  value: MONITOR_VER_VALUE,
  type: MONITOR_VER_TYPE,
};

export default (props) => {
  const { applicationID, versionId } = props;
  const applicationName = getParams('name');
  const _instanceId = getParams('instance');
  const _monitorType = _instanceId ? MONITOR_INS_TYPE : MONITOR_VER_TYPE;

  const timeInitValue = 'last_half_hour';
  const initPeriod = 60;
  const [start, end] = getTimes(timeInitValue);
  const [timeTuple, setTimeTuple] = useState({
    start,
    end,
    key: timeInitValue,
    period: initPeriod,
  });
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [instances, setInstances] = useState([DEFAULT_ALL_INSTANCE]);
  const [instanceId, setInstanceId] = useState(_instanceId || MONITOR_VER_VALUE);
  const [monitorType, setMonitorType] = useState(_monitorType);
  const child = useRef(null);
  const [selectedTimeKey, updateSelectedTimeKey] = useState(timeInitValue);

  useEffect(() => {
    // if (!refreshIndex) return;
    getAppVersionInstances();
  }, [refreshIndex]);

  const getAppVersionInstances = async () => {
    const data = await services.getAppVersionInstances({
      applicationID,
      applicationName,
      startTime: timeTuple.start,
      endTime: timeTuple.end,
      qualifier: versionId,
    });
    const { instances = [] } = data;
    const _instances = instances.map((instance) => {
      const { instanceId, status } = instance;
      return {
        label: (
          <>
            <InstanceStatus value={status} />
            <span style={{ marginLeft: 10 }}>{instanceId}</span>
          </>
        ),

        value: instanceId,
        type: MONITOR_INS_TYPE,
      };
    });
    _instances.unshift(DEFAULT_ALL_INSTANCE);
    setInstances(_instances);
  };

  const onMonitorTimeChange = (value) => {
    const { start, end, key, period } = value;
    updateSelectedTimeKey(key);
    setTimeTuple({ start, end, key, period });
  };

  const onMonitorTypeChange = (value, _, item) => {
    const { type } = item;
    setInstanceId(value);
    setMonitorType(type);
    setRefreshIndex(Date.now());
  };

  const refreshChart = () => {
    const [start, end] = getTimes(selectedTimeKey);
    let _start = start;
    let _end = end;
    if (selectedTimeKey === 'custom') {
      // 当为自定义时间时 timeTuple 值不变不会引起刷新
      _start = timeTuple.start;
      _end = timeTuple.end + 1000;
    }
    child?.current.onChild([_start, _end]);
    setTimeTuple({
      start: _start,
      end: _end,
      key: selectedTimeKey,
      period: timeTuple.period,
    });
  };

  return (
    <>
      <div className="mt-l">
        <div className="flex justify-between">
          <MonitorTimeRange
            onRef={child}
            defaultTime={false}
            timeInitValue={timeInitValue}
            onTimeChanged={onMonitorTimeChange}
          />

          <Button type="normal" style={{ padding: '0 9px' }} onClick={refreshChart}>
            <Icon type="refresh" style={{ color: '#666' }} />
          </Button>
        </div>
        <Select
          label={intl('saenext.version-detail.basic-monitor.ResourceType')}
          className="mt-s"
          style={{ minWidth: 444 }}
          value={instanceId}
          dataSource={instances}
          onChange={onMonitorTypeChange}
        />
      </div>
      <Metrics
        monitorType={monitorType}
        timeTuple={timeTuple}
        versionId={versionId}
        instanceId={instanceId}
        applicationID={applicationID}
        applicationName={applicationName}
        refreshIndex={refreshIndex}
      />
    </>
  );
};
