import React, { useState, useEffect } from 'react';
import { Grid } from '@ali/cnd';
import services from '~/services';
import { IMetricsOverview } from './type';
import { each } from 'lodash';
import { IMetricsProps } from '~/components/monitor-indicator/Constant';
// import TcpConnectIndex from './TcpConnectIndex';
import RequestCountIndicator from '../../monitor-indicator/RequestCountIndicator';
import LatencyIndicator from '../../monitor-indicator/LatencyIndicator';
import InstanceIndicator from '../../monitor-indicator/InstanceIndicator';
import CpuUtilizationIndicator from '../../monitor-indicator/CpuUtilizationIndicator';
import MemUtilizationIndicator from '../../monitor-indicator/MemUtilizationIndicator';
import DiskUtilizationIndicator from '../../monitor-indicator/DiskUtilizationIndicator';
import HttpStatusIndicator from '../../monitor-indicator/HttpStatusIndicator';
import NetworkPacketIndicator from '../../monitor-indicator/NetworkPacketIndicator';
import SystemLoadIndicator from '../../monitor-indicator/SystemLoadIndicator';
import StartupLatencyIndicator from '../../monitor-indicator/StartupLatencyIndicator';


const { Row, Col } = Grid;
type Props = {
  versionId: string;
  applicationID: string;
  applicationName: string;
  refreshIndex: number;
  timeTuple: {
    start: number,
    end: number,
    key: string,
    period: number,
  };
  setOverview: (overview: IMetricsOverview) => void;
  setIsLoading: (loading: boolean) => void;
};


export default (props: Props) => {
  const { timeTuple, refreshIndex, setIsLoading, setOverview } = props;
  const [data, setData] = useState<{[key: string]: any[]}>();
  let requestCount = 0;
  let maxInstanceCount = 0;

  useEffect(() => {
    // if (!refreshIndex) return;
    getAppVersionMetrics();
  }, [refreshIndex, JSON.stringify(timeTuple)]);

  // 版本 基础监控
  const getAppVersionMetrics = async() => {
    setIsLoading && setIsLoading(true);
    const { applicationID, applicationName, versionId } = props;
    const data = await services.getAppVersionMetrics({
      applicationID,
      startTime: timeTuple.start,
      endTime: timeTuple.end,
      period: timeTuple.period,
      metrics: [
        'ApplicationVersionRequestsV2',                    // 请求次数（Request Count）
        'ApplicationVersionRequestsLatencyV2',                     // 请求响应时间（Request Latency）
        'ApplicationVersionHTTPStatus2xxV2',               // HTTP 状态码（HTTP Status）
        'ApplicationVersionHTTPStatus3xxV2',
        'ApplicationVersionHTTPStatus4xxV2',
        'ApplicationVersionHTTPStatus5xxV2',
        'ApplicationVersionInstances',                    // 实例数（Active Instancesount）
        'ApplicationVersionInstancesActive',
        'ApplicationVersionInstancesIdle',
        'ApplicationVersionCPUUtilizations',              // CPU使用率（CPU Utilization）
        'ApplicationVersionMemoryUtilizations',           // 内存使用率（Memory Utilization）
        'ApplicationVersionNetworkRXBytes',               // 字节流量（入 + 出）
        'ApplicationVersionNetworkTXBytes',
        'ApplicationVersionLoad1m',                       // 系统平均负载（1min）
        'ApplicationVersionDiskUsageUtilizations',        // 磁盘使用率（Disk Utilization）
        'ApplicationVersionStartupLatency',
        // 'ApplicationVersionNetworkTCPTotalConnections',   // TCP连接数（Count）
        // 'ApplicationVersionNetworkTCPActiveConnections',
        // 'ApplicationVersionNetworkTCPInactiveConnections',
        // 'ApplicationVersionNetworkTCPInuse',
        // 'ApplicationVersionNetworkTCPOrphan',
        // 'ApplicationVersionNetworkTCPTw',
        // 'ApplicationVersionNetworkTCPAlloc',
      ].join(','),
      // 监控补0
      fillZero: true,
      applicationName,
      versionID: versionId,
    });
    const { metrics = {} } = data;
    const {
      ApplicationVersionRequestsV2 = [] as IMetricsProps[],
      ApplicationVersionRequestsLatencyV2 = [] as IMetricsProps[],
      ApplicationVersionHTTPStatus2xxV2 = [] as IMetricsProps[],
      ApplicationVersionHTTPStatus3xxV2 = [] as IMetricsProps[],
      ApplicationVersionHTTPStatus4xxV2 = [] as IMetricsProps[],
      ApplicationVersionHTTPStatus5xxV2 = [] as IMetricsProps[],
      ApplicationVersionInstances = [] as IMetricsProps[],
      ApplicationVersionInstancesActive = [] as IMetricsProps[],
      ApplicationVersionInstancesIdle = [] as IMetricsProps[],
      ApplicationVersionCPUUtilizations = [] as IMetricsProps[],
      ApplicationVersionMemoryUtilizations = [] as IMetricsProps[],
      ApplicationVersionLoad1m = [] as IMetricsProps[],
      ApplicationVersionDiskUsageUtilizations = [] as IMetricsProps[],
      ApplicationVersionNetworkRXBytes = [] as IMetricsProps[],
      ApplicationVersionNetworkTXBytes = [] as IMetricsProps[],
      ApplicationVersionStartupLatency = [] as IMetricsProps[],
      // ApplicationVersionNetworkTCPActiveConnections = [] as IMetricsProps[],
      // ApplicationVersionNetworkTCPAlloc = [] as IMetricsProps[],
      // ApplicationVersionNetworkTCPInactiveConnections = [] as IMetricsProps[],
      // ApplicationVersionNetworkTCPInuse = [] as IMetricsProps[],
      // ApplicationVersionNetworkTCPOrphan = [] as IMetricsProps[],
      // ApplicationVersionNetworkTCPTotalConnections = [] as IMetricsProps[],
      // ApplicationVersionNetworkTCPTw = [] as IMetricsProps[],
    } = metrics;
    each(ApplicationVersionRequestsV2, (r: IMetricsProps) => {
      requestCount = requestCount + r.Count;
    });
    each(ApplicationVersionInstances, (r: IMetricsProps) => {
      if (maxInstanceCount < r.Sum) {
        maxInstanceCount = r.Sum;
      }
    });
    const _data = {};
    Object.assign(_data, {
      requests: ApplicationVersionRequestsV2,
      latency: ApplicationVersionRequestsLatencyV2,
      httpStatus: [
        ApplicationVersionHTTPStatus2xxV2,
        ApplicationVersionHTTPStatus3xxV2,
        ApplicationVersionHTTPStatus4xxV2,
        ApplicationVersionHTTPStatus5xxV2,
      ],
      instances: [
        ApplicationVersionInstances,
        ApplicationVersionInstancesActive,
        ApplicationVersionInstancesIdle,
      ],
      cpuUtilizations: ApplicationVersionCPUUtilizations,
      memUtilizations: ApplicationVersionMemoryUtilizations,
      systemLoads: ApplicationVersionLoad1m,
      diskUtilizations: ApplicationVersionDiskUsageUtilizations,
      networkPacket: [
        ApplicationVersionNetworkRXBytes,
        ApplicationVersionNetworkTXBytes,
      ],
      startupLatency: ApplicationVersionStartupLatency,
    });
    setData(_data);
    // setTcpConnects({
    //   tcpActive: ApplicationVersionNetworkTCPActiveConnections,
    //   tcpAlloc: ApplicationVersionNetworkTCPAlloc,
    //   tcpInactive: ApplicationVersionNetworkTCPInactiveConnections,
    //   tcpInuse: ApplicationVersionNetworkTCPInuse,
    //   tcpOrphan: ApplicationVersionNetworkTCPOrphan,
    //   tcpTotal: ApplicationVersionNetworkTCPTotalConnections,
    //   tcpTw: ApplicationVersionNetworkTCPTw,
    // });
    setOverview && setOverview({
      requestCount,
      maxInstanceCount,
    });
    setIsLoading && setIsLoading(false);
  };

  return (
    <>
      <Row gutter={24}>
        <Col span={8}>
          <RequestCountIndicator data={data?.requests} />
        </Col>
        <Col span={8}>
          <LatencyIndicator data={data?.latency} />
        </Col>
        <Col span={8}>
          <HttpStatusIndicator data={data?.httpStatus} />
        </Col>
      </Row>
      <Row gutter={24} className="mt-xl">
        <Col span={8}>
          <InstanceIndicator data={data?.instances} />
        </Col>
        <Col span={8}>
          <CpuUtilizationIndicator data={data?.cpuUtilizations} />
        </Col>
        <Col span={8}>
          <MemUtilizationIndicator data={data?.memUtilizations} />
        </Col>
      </Row>
      <Row gutter={24} className="mt-xl">
        <Col span={8}>
          <SystemLoadIndicator data={data?.systemLoads} />
        </Col>
        <Col span={8}>
          <DiskUtilizationIndicator data={data?.diskUtilizations} />
        </Col>
        <Col span={8}>
          <NetworkPacketIndicator data={data?.networkPacket} />
        </Col>
      </Row>
      <Row gutter={24} className="mt-xl">
        <Col span={8}>
          <StartupLatencyIndicator data={data?.startupLatency} />
        </Col>
      </Row>
    </>
  );
};