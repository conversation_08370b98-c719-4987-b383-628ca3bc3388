import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Card, Loading, Select } from '@ali/cnd';
import { Wline } from '@alife/aisc-widgets';
import '@alife/aisc-widgets/build/index.css';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import { IWlineItem, IMetricsTCP, MetricAtom } from './type';

const { Option } = Select;
type Props = {
  axisMask: string;
  tcpConnects: { [key: string]: IMetricsTCP[] } | null;
};

const WlineOptions = {
  xAxis: {
    type: 'time',
    mask: 'HH:mm',
  },
  yAxis: {
    min: 0,
  },
  legend: {
    position: 'bottom',
    align: 'left',
  },
  symbol: false,
  spline: true,
  slider: false,
  zoom: true,
  tooltip: {
    visible: true,
    showTitle: true,
    showColon: true,
    sort: 'asce',
  },
};

export default (props: Props) => {
  const { tcpConnects = {}, axisMask } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [value, setValue] = useState(MetricAtom.Average);
  const [dataSource, setDataSource] = useState<IWlineItem[]>([]);

  useEffect(() => {
    initChartData();
  }, [JSON.stringify(tcpConnects), value]);

  const initChartData = () => {
    setIsLoading(true);
    const {
      tcpActive = [] as IMetricsTCP[],
      tcpAlloc = [] as IMetricsTCP[],
      tcpInactive = [] as IMetricsTCP[],
      tcpInuse = [] as IMetricsTCP[],
      tcpOrphan = [] as IMetricsTCP[],
      tcpTotal = [] as IMetricsTCP[],
      tcpTw = [] as IMetricsTCP[],
    } = tcpConnects;
    const atomKey = MetricAtom[value];
    setDataSource([
      {
        name: intl('saenext.version-detail.basic-monitor.TcpConnectIndex.TotalTcpConnections'),
        data: tcpTotal.map((r: IMetricsTCP) => [r.timestamp, r[atomKey]]),
      },
      {
        name: intl('saenext.version-detail.basic-monitor.TcpConnectIndex.ActiveTcpConnections'),
        data: tcpActive.map((r: IMetricsTCP) => [r.timestamp, r[atomKey]]),
      },
      {
        name: intl('saenext.version-detail.basic-monitor.TcpConnectIndex.InactiveTcpConnections'),
        data: tcpInactive.map((r: IMetricsTCP) => [r.timestamp, r[atomKey]]),
      },
      {
        name: 'TCPInuse',
        data: tcpInuse.map((r: IMetricsTCP) => [r.timestamp, r[atomKey]]),
      },
      {
        name: 'TCPTw',
        data: tcpTw.map((r: IMetricsTCP) => [r.timestamp, r[atomKey]]),
      },
      {
        name: 'TCPAlloc',
        data: tcpAlloc.map((r: IMetricsTCP) => [r.timestamp, r[atomKey]]),
      },
      {
        name: 'TCPOrphan',
        data: tcpOrphan.map((r: IMetricsTCP) => [r.timestamp, r[atomKey]]),
      },
    ]);
    setIsLoading(false);
  };

  return (
    <>
      <Card
        contentHeight="auto"
        title={
          <div className="flex multiple-chart">
            <TextWithBalloon
              text={intl(
                'saenext.version-detail.basic-monitor.TcpConnectIndex.TcpConnectionsCount',
              )}
              tips={intl(
                'saenext.version-detail.basic-monitor.TcpConnectIndex.TheNumberOfTcpConnections',
              )}
            />

            <Select
              size="small"
              className="ml-l"
              value={value}
              onChange={(_value: MetricAtom) => setValue(_value)}
              valueRender={(item) => <span className="color-primary">{item.label}</span>}
            >
              <Option key="average" value={MetricAtom.Average}>
                {intl('saenext.version-detail.basic-monitor.TcpConnectIndex.Average')}
              </Option>
              <Option key="min" value={MetricAtom.Minimum}>
                {intl('saenext.version-detail.basic-monitor.TcpConnectIndex.MinimumValue')}
              </Option>
              <Option key="max" value={MetricAtom.Maximum}>
                {intl('saenext.version-detail.basic-monitor.TcpConnectIndex.Maximum')}
              </Option>
            </Select>
          </div>
        }
      >
        <Loading visible={isLoading} className="full-width">
          <Wline
            data={dataSource}
            config={{
              ...WlineOptions,
              // @ts-ignore
              xAxis: {
                ...WlineOptions?.xAxis,
                mask: axisMask,
              },
              yAxis: {
                ...WlineOptions?.yAxis,
                tickMethod: 'integer',
              },
            }}
          />
        </Loading>
      </Card>
    </>
  );
};
