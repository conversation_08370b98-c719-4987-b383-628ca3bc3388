import { intl } from '@ali/cnd';
import React, { PureComponent } from 'react';
import '@alife/aisc-widgets/build/index.css';
import MetricsBeta from './MetricsBeta';
import MetricsInstance from './MetricsInstance';
import { IMetricsOverview } from './type';
import { Grid, StatCard, Loading } from '@ali/cnd';

type Props = {
  monitorType: symbol;
  versionId: string;
  instanceId: string;
  applicationID: string;
  applicationName: string;
  refreshIndex: number;
  timeTuple: {
    start: number;
    end: number;
    key: string;
    period: number;
  };
};

type State = {
  isLoading: boolean;
  overview: IMetricsOverview;
};

const symbolFor = Symbol.for;
export const MONITOR_VER_TYPE = symbolFor('monitor.version');
export const MONITOR_INS_TYPE = symbolFor('monitor.instance');
export const MONITOR_VER_VALUE = 'monitor.version.all.instance';

class Metrics extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      overview: {
        requestCount: 0,
        maxInstanceCount: 0,
      },
    };
    this.setOverview = this.setOverview.bind(this);
    this.setIsLoading = this.setIsLoading.bind(this);
  }

  setOverview(overview: IMetricsOverview) {
    this.setState({ overview });
  }

  setIsLoading(loading: boolean) {
    this.setState({ isLoading: loading });
  }

  render() {
    const { isLoading, overview } = this.state;
    const { monitorType, ...restProps } = this.props;

    const metricsEdition = (
      <>
        <Grid.Row gutter={24} className="mb-xl">
          <Grid.Col span={24}>
            <StatCard
              // title=''
              // className='monitor'
              items={[
                {
                  title: intl('saenext.version-detail.basic-monitor.Metrics.NumberOfRequests'),
                  count: [
                    String(overview.requestCount),
                    intl('saenext.version-detail.basic-monitor.Metrics.Times'),
                  ],
                },
                {
                  title: intl('saenext.version-detail.basic-monitor.Metrics.PeakNumberOfInstances'),
                  count: [String(overview.maxInstanceCount), intl('general.unit.count')],
                },
                // {
                //   render: <Description>日同比：8%</Description>
                // },
              ]}
            />
          </Grid.Col>
        </Grid.Row>
        <MetricsBeta
          {...restProps}
          setOverview={this.setOverview}
          setIsLoading={this.setIsLoading}
        />
      </>
    );

    let metricsContent = metricsEdition;

    if (monitorType === MONITOR_VER_TYPE) {
      metricsContent = metricsEdition;
    }

    if (monitorType === MONITOR_INS_TYPE) {
      metricsContent = <MetricsInstance {...restProps} setIsLoading={this.setIsLoading} />;
    }

    return (
      <Loading visible={isLoading} className="full-width mt-l mb-l">
        {metricsContent}
      </Loading>
    );
  }
}

export default Metrics;
