interface IMetricsProps {
  value?: number; 
  timestamp: number;
};

export interface IMetricsRequests extends IMetricsProps {
  Count: number;
};

export interface IMetricsLatency extends IMetricsProps {
  Average: number;
  Maximum: number;
  Minimum: number;
};

export interface IMetricsHTTPStatus extends IMetricsProps {
  Count: number;
};

export interface IMetricsInstances extends IMetricsProps {
  Sum: number;
};

export interface IMetricsCPU extends IMetricsProps {
  Average: number;
  Maximum: number;
  Minimum: number;
};

export interface IMetricsMemory extends IMetricsProps {
  Average: number;
  Maximum: number;
  Minimum: number;
};

export interface IMetricsDisk extends IMetricsProps {
  Average: number;
  Maximum: number;
  Minimum: number;
};


export interface IMetricsNetworkBytes extends IMetricsProps {
  Sum: number;
};

export interface IMetricsLoad extends IMetricsProps {
  Average: number;
  Maximum: number;
  Minimum: number;
};

export interface IMetricsTCP extends IMetricsProps {
  Average: number;
  Maximum: number;
  Minimum: number;
};


export interface IWlineItem {
  name: string;
  visible?: boolean;
  data: [number, number][];
}

export interface IMetricsOverview {
  requestCount: number;
  maxInstanceCount: number;
}

export const FULLDAYMS = 86400000;

export enum MetricAtom {
  Average = 'Average',
  Maximum = 'Maximum',
  Minimum = 'Minimum',
};
