import { intl } from '@ali/cnd';

export const NetworkType = {
  internet: intl('saenext.gateway.components.Configs.PublicNetwork'),
  intranet: intl('saenext.gateway.components.Configs.PrivateNetwork'),
};

export const RouterStatus = {
  Unpublish: 0,
  Publishing: 2,
  Published: 3,
  Editing: 4,
  Offline: 5,
};

export const MatchParam = {
  Path: 'PathPredicates',
  Method: 'MethodPredicates',
  Header: 'HeaderPredicates',
  Query: 'QueryPredicates',
};

export const MatchType = {
  PRE: intl('saenext.gateway.components.Configs.PrefixMatching'),
  EQUAL: intl('saenext.gateway.components.Configs.ExactMatch'),
  ERGULAR: intl('saenext.gateway.components.Configs.RegularMatching'),
};
