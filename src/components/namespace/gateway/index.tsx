import React from 'react';
import services from "~/services";
import {
  Button,
  Select,
  Icon,
  Dialog,
  Message,
  Actions,
  LinkButton,
  Balloon,
  ToolTipCondition,
  CndTable,
} from '@ali/cnd';
import _ from 'lodash';
import { lowerFirstData } from '../../../utils/transfer-data';
import ComDialog from '../../shared/ComDialog';
import IngressWarning from './IngressWarning';
import If from '../../shared/If';
import { MatchType, NetworkType } from './Configs';
import { intl } from '@ali/cnd';
import { isForbidden } from '../../../utils/authUtils';
import { UnAuthedLabel } from '../../shared/unauthedLabel';
import TextWithBalloon from '../../shared/TextWithBalloon';
import CachedData from '../../../cache/common';

type Props = {
  regionId: string;
  namespaceId: string;
  handleRedirectToEvent?: Function;
  featureConfig?: object;
  className?: string;
  history?: any;
  location?: any; // ?后参数
  handleCreateRoute: () => void;
  handleEditRoute: (record: Record<string, any>) => void;
  handleRouteDetail: (record: Record<string, any>) => void;
  isNewSae?: boolean;
};

type State = {
  routerList: any[];
  curSlbType: string;
  curProtocalType: string;
  loaded: boolean;
  slbList: any[];
  albList: any[];
  namespaceDetail: any;
  certificationList: any[];
  albCertList: any[];
  routerDetail: any;
  previewDialogVisible: boolean;
  mseGatewayList: any[];
  previewLoading: boolean;
  authedSLB: boolean;
  authedALB: boolean;
  authedMSE: boolean;
  authedApig: boolean;
  apigList: any[];
  refreshIndex: number;
  refreshPolicyIndex: number;
  allData: any[];  // 缓存所有数据
};

const isPre = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';

const transferMatchTypeToMse = {
  Prefix: 'PRE',
  Exact: 'EQUAL',
  Regex: 'ERGULAR',
};
class GatewayRoute extends React.Component<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      routerList: [],
      previewDialogVisible: false,
      routerDetail: { defaultRule: {} },
      curSlbType: '',
      curProtocalType: '',
      certificationList: [],
      albCertList: [],
      slbList: [],
      albList: [],
      loaded: false,
      namespaceDetail: {},
      previewLoading: false,
      mseGatewayList: [],
      authedSLB: true,
      authedALB: true,
      authedMSE: true,
      authedApig: true,
      apigList: [],
      refreshIndex: 0,
      refreshPolicyIndex: 0,
      allData: [],
    };
  }
  componentDidMount() {
    this.queryCertificationList();
    this.queryAlbCert();
    this.queryNamespaceDetail();
  }
  componentDidUpdate(prevProps) {
    const { regionId, namespaceId } = this.props;
    const { regionId: prevRegionId, namespaceId: prevNamespaceId } = prevProps;

    if (regionId !== prevRegionId || namespaceId !== prevNamespaceId) {
      this.queryCertificationList();
      this.queryAlbCert();
      this.queryNamespaceDetail();
      this.setState({ refreshIndex: Date.now() });
    }
  }


  queryNamespaceDetail = async () => {
    const data = await services.getNamespaceResources({
      RegionId: this.props.regionId,
      NamespaceId: this.props.namespaceId,
    });
    this.setState({
      namespaceDetail: data.Data,
    });
  };

  queryRouterDetail = async (ingressId, callback?: () => void) => {
    const res = await services.getIngressInfo({
      IngressId: ingressId,
      RegionId: this.props.regionId,
    });
    const { Data = {} } = res;
    this.setState(
      {
        routerDetail: Data,
        refreshPolicyIndex: Date.now(),
      },
      () => {
        callback && callback();
      },
    );
  };

  queryMseRouterDetail = async (ingressId, callback?: () => void) => {
    const res = await services.describeMSEIngress(
      {
        IngressId: ingressId,
      },
      (error, data) => {
        if (error.code === 'MSEGateway.NotExist') {
          return { mseNotExist: true };
        }
        return {};
      },
    );

    if (res.mseNotExist) {
      Message.error(intl('saenext.components.gateway.TheMseGatewayInstanceHas'));
      return res;
    }
    const { Data = {} } = res;
    this.setState(
      {
        routerDetail: Data,
      },
      () => {
        callback && callback();
      },
    );
  };

  queryApigRouteInfo = async (ingressId) => {
    const res = await services.getHttpApiRoute({
      params: { IngressId: ingressId, NamespaceId: this.props.namespaceId },
      customErrorHandle: (err, data, callback) => { },
    });
    const { Data = {} } = res;
    const { RouteId, Services = [], Domains = [], Predicates, Policies } = Data;
    const PathPredicates = _.get(Predicates, 'PathPredicates', {});
    const MethodPredicates = _.get(Predicates, 'MethodPredicates', []);
    let HeaderPredicates = _.get(Predicates, 'HeaderPredicates', []);
    let QueryPredicates = _.get(Predicates, 'QueryPredicates', []);
    _.forEach(HeaderPredicates, (headerItem) => {
      headerItem.Type = transferMatchTypeToMse[headerItem.Type];
      headerItem.Key = headerItem.Name;
    });
    _.forEach(QueryPredicates, (queryItem) => {
      queryItem.Type = transferMatchTypeToMse[queryItem.Type];
      queryItem.Key = queryItem.Name;
    });
    const Fallback = _.get(Policies, 'Fallback.Enable', false);
    const FallbackServices = _.head(_.get(Policies, 'Fallback.Destinations', [])) || {};
    let _routerDetail = {
      RouteId,
      DomainList: _.map(Domains, (domain) => domain.DomainName),
      RoutePredicates: {
        PathPredicates,
        MethodPredicates,
        HeaderPredicates,
        QueryPredicates,
      },
      RouteServices: Services,
      Fallback,
      FallbackServices,
    };
    this.setState({
      routerDetail: _routerDetail,
      refreshPolicyIndex: Date.now(),
    });
  };
  queryRouterList = async (params?) => {
    const { curSlbType = '', curProtocalType = '' } = this.state;

    const defaultParams = {
      current: 1,
      pageSize: 10,
      ...params
    };

    if(curSlbType === '' && curProtocalType === '') { // 无筛选条件，直接返回后端分页数据
      const res = await services.getListIngresses({
        RegionId: this.props.regionId,
        NamespaceId: this.props.namespaceId,
        CurrentPage: defaultParams.current,
        PageSize: defaultParams.pageSize,
      });
      return {
        data: res.Data.IngressList,
        total: res.Data.TotalSize,
      }
    } else { 
      // 有筛选条件：获取全部数据，前端筛选和分页
      const { allData } = this.state;
      
      // 如果已有缓存数据，直接使用缓存进行筛选和分页
      if ( allData.length > 0) {
        const filteredData = this.filterData(allData, curSlbType, curProtocalType);
        const { current, pageSize } = defaultParams;
        const startIndex = (current - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        return {
          data: filteredData.slice(startIndex, endIndex),
          total: filteredData.length,
        };
      }
 
      const res = await services.getListIngresses({
        RegionId: this.props.regionId,
        NamespaceId: this.props.namespaceId,
      });
      const { Data = {} } = res;
      const { IngressList = [] } = Data;
      IngressList.sort((a, b) => a.domain - b.domain > 0);
   
      this.setState({
        allData: IngressList,
        routerList: IngressList,
      }, () => {
        const hasType = (type) => {
          return _.some(IngressList, (ingress) => ingress.LoadBalanceType === type);
        };
        hasType('clb') && this.querySlbList();
        hasType('alb') && this.queryAlbList();
        hasType('mse') && this.queryMseGateway();
        hasType('apig') && this.queryApigGateway();
      });

      const filteredData = this.filterData(IngressList, curSlbType, curProtocalType);
      const { current, pageSize } = defaultParams;
      const startIndex = (current - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return {
        data: filteredData.slice(startIndex, endIndex),
        total: filteredData.length,
      };
    }
  };

  filterData = (data, curSlbType, curProtocalType) => {
    return data.filter(item => {
      const slbTypeFilter = curSlbType ? item?.SlbType?.toLowerCase() === curSlbType : true;
      const itemProtocalType = item.CertIds ? 'https' : 'http';
      const protocalFilter = curProtocalType ? itemProtocalType === curProtocalType : true;
      return slbTypeFilter && protocalFilter;
    });
  };

  togglePreviewDialogVisible = () => {
    const { previewDialogVisible } = this.state;
    if (previewDialogVisible) {
      this.setState({
        previewDialogVisible: false,
        routerDetail: { defaultRule: {} },
      });
    } else {
      this.setState({
        previewDialogVisible: true,
      });
    }
  };

  handleOpenPreview = async (ingressId, record) => {
    this.togglePreviewDialogVisible();
    this.setState({
      previewLoading: true,
    });
    if (record.LoadBalanceType === 'mse') {
      const { mseNotExist } = (await this.queryMseRouterDetail(ingressId)) || {};
      if (mseNotExist) {
        this.togglePreviewDialogVisible();
      }
    } else if (record.LoadBalanceType === 'apig') {
      await this.queryApigRouteInfo(ingressId);
    } else {
      await this.queryRouterDetail(ingressId);
    }
    this.setState({
      previewLoading: false,
    });
  };

  handleEditRouter = (record) => {
    // const history = this.props.history;
    // const location = this.props.location;
    // const search = location.search;
    // const _search = `${search}&route=${record.Id}-${record.SlbType}-${record.LoadBalanceType}`;
    // history.push(
    //   `/${this.props.regionId}/namespace/${this.props.namespaceId}/route/edit${_search}`,
    // );
    this.props.handleEditRoute(record);
  };

  handleDeleteRouter = (ingressId, record) => {
    Dialog.alert({
      title: intl('saenext.components.gateway.DeleteRoutingRules'),
      content: intl('saenext.components.gateway.AfterYouDeleteARouting'),
      onOk: () => {
        if (record.LoadBalanceType === 'mse') {
          return services
            .deleteMSEIngress({
              IngressId: ingressId,
            })
            .then(() => {
              Message.success(intl('saenext.components.gateway.TheRoutingRuleHasBeen'));
              this.setState({
                allData: [],
                refreshIndex: Date.now(),
              });
            });
        }
        if (record.LoadBalanceType === 'apig') {
          return services
            .deleteHttpApiRoute({
              params: {
                IngressId: ingressId,
                NamespaceId: this.props.namespaceId,
              },
            })
            .then((res) => {
              if (res?.Success) {
                Message.success(intl('saenext.components.gateway.TheRoutingRuleHasBeen'));
              }
              this.setState({
                allData: [],
                refreshIndex: Date.now(),
              });
            });
        }
        return services
          .deleteIngress({
            IngressId: ingressId,
            RegionId: this.props.regionId,
          })
          .then(() => {
            Message.success(intl('saenext.components.gateway.TheRoutingRuleHasBeen'));
            this.setState({
              allData: [],
              refreshIndex: Date.now(),
            });
          });
      },
    });
  };

  handleSlbChange = (val) => {
    this.setState({
      curSlbType: val,
      refreshIndex: Date.now(),
    });
  };

  handleProtocalChange = (val) => {
    this.setState({
      curProtocalType: val,
      refreshIndex: Date.now(),
    });
  };

  handleCreateRouter = () => {
    // const history = this.props.history;
    // const location = this.props.location;
    // const search = location.search;
    // history.push(
    //   `/${this.props.regionId}/namespace/${this.props.namespaceId}/route/create${search}`,
    // );
    this.props.handleCreateRoute();
  };

  queryCertificationList = async () => {
    const res = await services.getServerCertificates({
      params: {
        RegionId: this.props.regionId,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        this.setState({ authedSLB: !forbidden });
        if (!forbidden) cb?.();
      },
    });

    const { ServerCertificates = {} } = res;
    const { ServerCertificate = [] } = ServerCertificates;
    this.setState({
      certificationList: ServerCertificate.map((item) => ({
        ...item,
        label: item.ServerCertificateName,
        value: item.ServerCertificateId,
      })),
    });
  };

  queryAlbCert = async () => {
    const albCertList = [];
    await this.getAlbAllCert(1, 99, albCertList);

    this.setState({
      albCertList,
    });
  };

  getAlbAllCert = async (CurrentPage = 1, ShowSize = 99, res) => {
    const { CertMetaList, TotalCount } = await services.getSSLCertificateList({
      params: {
        ShowSize,
        CurrentPage,
        RegionId: this.props.regionId,
      },
      customErrorHandle: (err, _p, cb) => {
        const forbidden = isForbidden(err.code);
        this.setState({ authedSLB: !forbidden });
        if (!forbidden) cb?.();
      },
    });
    const albCertList = _.map(CertMetaList, (item) => ({
      label: item.CertName,
      value: item.CertIdentifier,
      ...item,
    }));
    res.push(...albCertList);

    if (CurrentPage * ShowSize < TotalCount) {
      await this.getAlbAllCert(CurrentPage + 1, ShowSize, res);
    }
  };

  querySlbList = async () => {
    const { curSlbType } = this.state;
    const res = await services.getLoadBalancers({
      params: {
        AddressType: curSlbType, // : 'intranet',
        PageNumber: 1,
        PageSize: 50,
        RegionId: this.props.regionId,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        this.setState({ authedSLB: !forbidden });
        if (!forbidden) cb?.();
      },
    });
    const { LoadBalancers = {} } = res;
    const { LoadBalancer = [] } = LoadBalancers;
    const loadBalancers = lowerFirstData(LoadBalancer) || {};

    this.setState({
      loaded: true,
      slbList: loadBalancers,
    });
  };

  queryAlbList = async () => {
    const { LoadBalancers } = await services.getAlbLoadBalancers({
      params: {
        NextToken: '',
        MaxResults: 50,
        RegionId: this.props.regionId,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        this.setState({ authedALB: !forbidden });
        if (!forbidden) cb?.();
      },
    });
    const loadBalancers = lowerFirstData(LoadBalancers) || {};

    this.setState({
      loaded: true,
      albList: loadBalancers,
    });
  };

  queryMseGateway = async (Name?) => {
    const { namespaceDetail } = this.state;
    const data = await services.getListGateway({
      params: {
        PageNumber: 1,
        PageSize: 50,
        FilterParams: {
          Vpc: namespaceDetail?.VpcId,
          Name,
        },
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        this.setState({ authedMSE: !forbidden });
        if (!forbidden) cb?.();
      },
    });

    const mseGatewayList = _.get(data, 'Data.Result');
    this.setState({
      loaded: true,
      mseGatewayList,
    });
  };

  queryApigGateway = async () => {
    const { namespaceDetail } = this.state;
    const res = await services.getHttpApiGateways({
      params: {
        pageSize: 200,
        pageNumber: 1,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        this.setState({ authedApig: !forbidden });
        if (!forbidden) cb?.();
      },
    });
    const list = res?.data?.items || [];
    const _list = _.filter(list, (item) => {
      return item?.vpc?.vpcId === namespaceDetail?.VpcId;
    });
    this.setState({
      loaded: true,
      apigList: _list,
    });
  };

  renderAllCerts = (val, record) => {
    const { LoadBalanceType, CertId } = record;

    const certIdArr =
      LoadBalanceType === 'alb' || LoadBalanceType === 'mse' ? _.split(val, ',') : [CertId];
    const certNameArr = _.map(certIdArr, (item) => {
      return this.renderCert(item, record);
    });

    return (
      <ul>
        {certNameArr.map((cert) => (
          <li>
            {typeof cert === 'string' ? (
              <span>{cert}</span>
            ) : (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ color: '#333' }}>{cert.certDomain}</span>
                <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
                <span style={{ color: '#666' }}>{cert.certName}</span>
              </div>
            )}
          </li>
        ))}
      </ul>
    );
  };

  renderCert = (val, record) => {
    if (!val) return '-';

    const { certificationList, albCertList } = this.state;
    if (record.LoadBalanceType === 'alb') {
      const targetCert = albCertList.find((item) => item.value == val);
      if (targetCert) {
        return {
          certDomain: targetCert.CommonName,
          certName: targetCert.CertName,
        };
      }
      return val;
    }
    const targetCert = certificationList.find((item) => item.value === val);
    if (targetCert) {
      return {
        certDomain: targetCert.ServerCertificateName,
        certName: targetCert.AliCloudCertificateName,
      };
    }
    return val;
  };

  renderSlb = (val, record) => {
    const { slbList, albList, mseGatewayList, apigList } = this.state;
    if (record.LoadBalanceType === 'mse') {
      const { MseGatewayId } = record;
      const targetMseGateway = mseGatewayList.find((item) => item.GatewayUniqueId === MseGatewayId);
      if (targetMseGateway) {
        return this.renderSlbMore('mse', targetMseGateway);
      } else {
        return this.renderSlbMore('mse', { GatewayUniqueId: MseGatewayId });
      }
    }
    if (record.LoadBalanceType === 'alb') {
      const targetAlb = albList.find((item) => item.loadBalancerId === val);
      if (targetAlb) {
        return this.renderSlbMore('alb', targetAlb);
      } else {
        return this.renderSlbMore('alb', { loadBalancerId: val });
      }
    }
    if (record.LoadBalanceType === 'apig') {
      const { MseGatewayId } = record;
      const targetApigGateway = apigList.find((item) => item.gatewayId === MseGatewayId);
      if (targetApigGateway) {
        return this.renderSlbMore('apig', targetApigGateway);
      } else {
        return this.renderSlbMore('apig', { gatewayId: MseGatewayId });
      }
    }
    const targetSlb = slbList.find((item) => item.loadBalancerId === val);
    if (targetSlb) {
      return this.renderSlbMore('slb', targetSlb);
    } else {
      return this.renderSlbMore('slb', { loadBalancerId: val });
    }
  };

  renderSlbMore = (type, info) => {
    let name = '';
    let id = '';
    let dns = '';
    let ip = '';
    let url = '';
    switch (type) {
      case 'mse':
        id = info?.GatewayUniqueId;
        name = info?.Name;
        url = `${CachedData.confLink('feature:mse:url')}/#/gateway/basicInfo?Id=${id}&region=${this.props.regionId}`;
        break;
      case 'alb':
        id = info?.loadBalancerId;
        name = info?.loadBalancerName;
        dns = info?.dNSName;
        url = `${CachedData.confLink('feature:slb:url')}/alb/${this.props.regionId}/albs/${id}`;
        break;
      case 'slb':
        id = info?.loadBalancerId;
        name = info?.loadBalancerName;
        ip = info?.address;
        url = `${CachedData.confLink('feature:slb:url')}/slb/${this.props.regionId}/slbs/${id}`;
        break;
      case 'apig':
        id = info?.gatewayId;
        name = info?.name;
        url = `${CachedData.confLink('feature:apigw:url')}/#/${this.props.regionId}/gateway/${id}/detail`;
        break;
      // no default
    }
    const trigger = (
      <span
        style={{
          color: '#0064c8',
          cursor: 'pointer',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
        onClick={() => {
          if (id) {
            window.open(url, '_blank');
          }
        }}
      >
        {id || '-'}
      </span>
    );

    return (
      <Balloon trigger={trigger} closable={false} align="r">
        <div>
          <div>
            <span>{intl('saenext.components.gateway.Name.1')}</span>
            <span>{name || '-'}</span>
          </div>
          <div>
            <span>ID:</span>
            <span>{id}</span>
          </div>
          {dns && (
            <div>
              <span>DNS:</span>
              <span>{dns}</span>
            </div>
          )}
          {ip && (
            <div>
              <span>IP:</span>
              <span>{ip}</span>
            </div>
          )}
        </div>
      </Balloon>
    );
  };

  handleRouterRulePolicy = (policyConfig) => {
    // 每条规则中重写(转发至)与重定向只会存在一种
    let rewriteConfig = {};
    let redirectConfig = {};
    _.forEach(policyConfig, (item) => {
      if (item.ActionType === 'rewrite') {
        rewriteConfig = JSON.parse(item?.ActionConfig);
      }
      if (item.ActionType === 'redirect') {
        redirectConfig = JSON.parse(item?.ActionConfig);
      }
    });
    return {
      rewriteConfig,
      redirectConfig,
    };
  };

  handlePath = (path) => {
    if (path && !path.startsWith) {
      return `/${path}`;
    } else {
      return path;
    }
  };

  render() {
    const { className = '' } = this.props;
    const {
      routerList = [],
      previewDialogVisible,
      previewLoading,
      routerDetail,
      loaded,
      slbList,
      refreshIndex
    } = this.state;

    routerDetail.defaultRule = routerDetail.defaultRule || {};

    const showBackEndProtocol = routerDetail.LoadBalanceType === 'alb' && routerDetail.CertIds;

    const columns = [
      {
        title: intl('saenext.components.gateway.Name'),
        width: 140,
        lock: 'left',
        dataIndex: 'Description',
        cell: (val, index, record) => {
          if (this.props.isNewSae && record?.LoadBalanceType === 'apig') {
            return (
              <LinkButton
                onClick={() => {
                  this.props.handleRouteDetail(record);
                }}
              >
                {val}
              </LinkButton>
            );
          } else {
            return val;
          }
        },
      },
      {
        title: intl('saenext.components.gateway.NetworkType'),
        width: 90,
        dataIndex: 'SlbType',
        cell: val => NetworkType[val?.toLowerCase()] || '-',
      },
      {
        title: intl('saenext.components.gateway.GatewayType'),
        width: 90,
        dataIndex: 'LoadBalanceType',
        cell: val => val?.toUpperCase(),
      },
      {
        title: intl('saenext.components.gateway.GatewayInstance'),
        width: 240,
        dataIndex: 'SlbId',
        cell: (val, index, record) => this.renderSlb(val, record),
      },
      {
        title: intl('saenext.components.gateway.AccessPort'),
        width: 90,
        dataIndex: 'ListenerPort',
        cell: (val, index, record) => val || record.MseGatewayPort,
      },
      {
        title: intl('saenext.components.gateway.ProtocolType'),
        width: 90,
        cell: (val, idx, record) => (
          <span style={{ whiteSpace: 'nowrap' }}>
            {record.MseGatewayProtocol || record.ListenerProtocol}
          </span>
        ),
      },
      {
        title: (
          <UnAuthedLabel
            text={intl('saenext.components.gateway.SslCertificate')}
            authed={this.state.authedSLB}
            authKey="AliyunSLBReadOnlyAccess"
          />
        ),
        width: 200,
        dataIndex: 'CertIds',
        cell: (val, index, record) => this.renderAllCerts(val, record),
      },
      {
        title: intl('saenext.components.gateway.ForwardingPolicy'),
        width: 90,
        dataIndex: 'Id',
        cell: (val, idx, record) => {
          return (
            <LinkButton onClick={() => this.handleOpenPreview(val, record)}>
              {intl('saenext.components.gateway.ViewDetails')}
            </LinkButton>
          );
        },
      },
      {
        title: intl('saenext.components.gateway.Operation'),
        width: 140,
        dataIndex: 'Id',
        lock: 'right',
        cell: (val, idx, record) => {
          return (
            <Actions threshold={6}>
              <ToolTipCondition
                show={!record.SlbType && record.LoadBalanceType === 'alb'}
                tip={
                  !record.SlbType && record.LoadBalanceType === 'alb' ? (
                    intl('saenext.components.gateway.TheAlbInstanceMayBe')
                  ) : (
                    <>
                      {intl('saenext.components.gateway.CloudNativeApiGatewayOnly')}

                      <a
                        href={isPre
                          ? CachedData.confLink('feature:pre-saenext:url')
                          : CachedData.confLink('feature:saenext:url')}
                        target="_blank"
                        className="mr-4 ml-4"
                      >
                        {intl('saenext.components.gateway.SaeNewConsole')}
                      </a>
                      {intl('saenext.components.gateway.Support')}
                    </>
                  )
                }
              >
                <LinkButton
                  disabled={!record.SlbType && record.LoadBalanceType === 'alb'}
                  onClick={() => this.handleEditRouter(record)}
                >
                  {intl('saenext.components.gateway.Edit')}
                </LinkButton>
              </ToolTipCondition>
              <LinkButton onClick={() => this.handleDeleteRouter(val, record)}>
                {intl('saenext.components.gateway.Delete')}
              </LinkButton>
              <ToolTipCondition
                show={record.LoadBalanceType === 'apig'}
                tip={intl('saenext.components.gateway.CurrentlyNotSupported')}
              >
                <LinkButton
                  onClick={() => this.props.handleRedirectToEvent?.(record)}
                  disabled={record.LoadBalanceType === 'apig'}
                >
                  {intl('saenext.components.gateway.Event')}
                </LinkButton>
              </ToolTipCondition>
            </Actions>
          );
        }
      }
    ];
    const ruleColumns = [
      {
        title: intl('saenext.components.gateway.DomainNameAccessPortPath'),
        cell: (val, idx, record = {} as any) => {
          const { rewriteConfig = {} as any } = this.handleRouterRulePolicy(
            record?.RuleActions || [],
          );
          return (
            <a
              target="_blank"
              // @ts-ignore
              href={`${routerDetail.ListenerProtocol}://${record.Domain}:${routerDetail.ListenerPort}${record.Path}`}
            >
              {`${record.Domain}:${routerDetail.ListenerPort}${record.Path}`}
              {!_.isEmpty(rewriteConfig) &&
                `${intl('saenext.components.gateway.rewrite')}${rewriteConfig?.host
                }${this.handlePath(rewriteConfig?.path)}${rewriteConfig?.query ? `?${rewriteConfig?.query}` : ''
                }${intl('saenext.common.parenthesis.right')}`}
            </a>
          );
        },
      },
      {
        title: "",
        cell: (val, idx, record) => {
          const { redirectConfig = {} } = this.handleRouterRulePolicy(
            record?.RuleActions || [],
          );
          if (!_.isEmpty(redirectConfig)) {
            return <>{intl('saenext.components.gateway.Redirect')}</>;
          } else {
            return <>{intl('saenext.components.gateway.Forward')}</>;
          }
        }
      },
      {
        title: intl('saenext.components.gateway.Target'),
        cell: (val, idx, record = {} as any) => {
          const { redirectConfig = {} as any } = this.handleRouterRulePolicy(
            record?.RuleActions || [],
          );
          if (!_.isEmpty(redirectConfig)) {
            const { protocol, host, port, path, query, httpCode } = redirectConfig as any;
            // 重定向
            return (
              <>
                <span>{`${protocol}//${host}${port ? `:${port}` : ''}${this.handlePath(
                  path,
                )}${query ? `?${query}` : ''}`}</span>
                {httpCode && (
                  <span style={{ marginLeft: 8 }}>
                    {intl('saenext.components.gateway.StatusCodeHttpcode', {
                      httpCode: httpCode,
                    })}
                  </span>
                )}
              </>
            );
          } else {
            return (
              <span>
                {showBackEndProtocol ? `${record.BackendProtocol || 'http'}://` : ''}
                {
                  <a
                    href={
                      this.props.isNewSae
                        ? `/${this.props.regionId}/app-list/${record.AppId}/micro-app/base?name=${record.AppName}`
                        : `/#/AppList/AppDetail?appName=${record.AppName}&appId=${record.AppId
                        }&${window.location.hash.split('?')[1]}`
                    }
                    target="_blank"
                  >
                    {record.AppName}
                  </a>
                }
                :{record.ContainerPort}
              </span>
            );
          }
        }
      }
    ];
    const serviceColumns = [
      {
        title: intl('saenext.components.gateway.DomainName'),
        width: 180,
        cell: () => {
          return routerDetail.DomainList.map(domain => (
            <p>
              {domain === '*' ? (
                domain
              ) : (
                <a href={`http://${domain}`} target="_blank">
                  {domain}
                </a>
              )}
            </p>
          ));
        },
      },
      {
        title: intl('saenext.components.gateway.Path'),
        width: 200,
        cell: () => (
          <span>
            {MatchType[routerDetail.RoutePredicates?.PathPredicates?.Type]}
            &nbsp;&nbsp;
            {routerDetail.RoutePredicates?.PathPredicates?.Path}
            &nbsp;&nbsp;
            {routerDetail.RoutePredicates?.PathPredicates?.IgnoreCase
              ? intl('saenext.components.gateway.CaseIgnored')
              : intl('saenext.components.gateway.CaseSensitive')}
          </span>
        ),
      },
      {
        title: intl('saenext.components.gateway.Method'),
        cell: () => <span>{_.join(routerDetail.RoutePredicates?.MethodPredicates, ', ')}</span>,
      },
      {
        title: intl('saenext.components.gateway.RequestHeader'),
        cell: () => (
          <span>
            {_.map(routerDetail.RoutePredicates?.HeaderPredicates, header => (
              <p>
                <span>{header.Key}</span>
                <span>&nbsp;{MatchType[header.Type]}&nbsp;</span>
                <span>{header.Value}</span>
              </p>
            ))}
          </span>
        ),
      },
      {
        title: intl('saenext.components.gateway.RequestParametersQuery'),
        width: 150,
        cell: () => (
          <span>
            {_.map(routerDetail.RoutePredicates?.QueryPredicates, query => (
              <p>
                <span>{query.Key}</span>
                <span>&nbsp;{MatchType[query.Type]}&nbsp;</span>
                <span>{query.Value}</span>
              </p>
            ))}
          </span>
        ),
      },
      {
        title: '',
        width: 50,
        cell: () => <Icon type="arrow-right" size={'small'} />,
      },
      {
        title: intl('saenext.components.gateway.ApplicationServicePort'),
        width: 250,
        cell: (val, idx, record = {} as any) => (
          <span>
            <a
              href={
                this.props.isNewSae
                  ? `/${this.props.regionId}/app-list/${record.AppId}/micro-app/base?name=${record.AppName}`
                  : `#/AppList/AppDetail?appName=${record.AppName}&appId=${record.AppId}&${window.location.hash.split('?')[1]
                  }`
              }
              target="_blank"
            >
              {record.AppName}/{record.ServiceName}:{record.ServicePort}
            </a>
          </span>
        ),
      },
    ];

    return (
      <div className={className} style={{ paddingBottom: 0 }}>
        <Message
          type="warning"
          title={intl('saenext.components.gateway.ImportantNotice')}
          style={{ marginBottom: 8 }}
        >
          {intl.html('saenext.components.gateway.BecauseTheClbSubscriptionInstance.new', {
            href: CachedData.confLink('help:sae:set-routing-rules-for-an-app-alb')
          })}
        </Message>
        <>
          {loaded ? (
            <IngressWarning
              dataSource={routerList}
              slbList={slbList}
              regionId={this.props.regionId}
              namespaceId={this.props.namespaceId}
            />
          ) : null}
        </>
        <CndTable
          fetchData={this.queryRouterList}
          columns={columns}
          pagination={{
            hideOnlyOnePage: false,
          }}
          operation={
            <>
              <Button type="primary" onClick={this.handleCreateRouter}>
                {intl('saenext.components.gateway.CreateAGatewayRoute')}
              </Button>
              <Select
                placeholder={intl('saenext.components.gateway.SelectANetworkType')}
                dataSource={[
                  { label: intl('saenext.components.gateway.AllNetworkTypes'), value: '' },
                  { label: intl('saenext.components.gateway.PublicNetwork'), value: 'internet' },
                  { label: intl('saenext.components.gateway.PrivateNetwork'), value: 'intranet' },
                ]}
                style={{ marginLeft: 8, minWidth: 200 }}
                onChange={this.handleSlbChange}
                hasClear
              />

              <Select
                placeholder={intl('saenext.components.gateway.SelectAProtocolType')}
                style={{ marginLeft: 8, marginRight: 8, minWidth: 200 }}
                dataSource={[
                  { label: intl('saenext.components.gateway.AllProtocolTypes'), value: '' },
                  { label: 'HTTP', value: 'http' },
                  { label: 'HTTPS', value: 'https' },
                ]}
                onChange={this.handleProtocalChange}
                hasClear
              />

              <TextWithBalloon
                text={intl('saenext.components.gateway.Limits')}
                ballonProps={{
                  popupStyle: { minWidth: 640 },
                }}
                tips={
                  <Message
                    type="notice"
                    title={intl('saenext.components.gateway.Limits')}
                  >
                    <div className="text-line">
                      {intl('saenext.components.gateway.BeforeUsingTheSaeNetwork')}
                      <a href={CachedData.confLink('help:sae:limits-of-mse-gateway')} target="_blank">
                        {intl('saenext.components.gateway.MseCloudNativeGatewayUsage')}
                      </a>
                      {intl('sae.common.comma')}
                      <a href={CachedData.confLink('help:sae:limits-of-clb')} target="_blank">
                        {intl('saenext.components.gateway.ClbUsageConstraints')}
                      </a>
                      {intl('sae.common.comma')}
                      <a href={CachedData.confLink('help:sae:limits-of-alb')} target="_blank">
                        {intl('saenext.components.gateway.AlbUsageConstraints')}
                      </a>
                    </div>
                    <div className="text-line">
                      {intl('saenext.components.gateway.SinceAprilAfterConfiguringThe')}
                    </div>
                  </Message>
                }
              />
            </>
          }
          showRefreshButton
          refreshIndex={refreshIndex}
        />
        {/* @ts-ignore */}
        <ComDialog
          visible={previewDialogVisible}
          title={intl('saenext.components.gateway.ViewForwardingPolicies')}
          // @ts-ignore
          style={{ width: '80%' }}
          loading={previewLoading}
          onOk={this.togglePreviewDialogVisible}
          onCancel={this.togglePreviewDialogVisible}
          onClose={this.togglePreviewDialogVisible}
        >
          <If condition={!routerDetail.RouteId}>
            <p style={{ fontWeight: 'bold' }}>
              {intl('saenext.components.gateway.CustomForwardingPolicy')}
            </p>
            <CndTable
              dataSource={routerDetail?.Rules || []}
              columns={ruleColumns}
              pagination={false}
              hasBorder={false}
              refreshIndex={this.state.refreshPolicyIndex}
            />

            <div>
              <p style={{ marginTop: 20, fontWeight: 'bold' }}>
                {intl('saenext.components.gateway.DefaultForwardingPolicy')}
              </p>
              <p style={{ color: '#aaa' }}>
                {intl('saenext.components.gateway.AllRequestsThatDoNot')}
                {showBackEndProtocol
                  ? `${routerDetail.DefaultRule?.BackendProtocol || 'http'}://`
                  : ''}
                <a
                  href={
                    this.props.isNewSae
                      ? `/${this.props.regionId}/app-list/${routerDetail.DefaultRule?.AppId}/micro-app/base?name=${routerDetail.DefaultRule?.AppName}`
                      : `/#/AppList/AppDetail?appName=${routerDetail.DefaultRule?.AppName}&appId=${routerDetail.DefaultRule?.AppId
                      }&${window.location.hash.split('?')[1]}`
                  }
                  target="_blank"
                >
                  {routerDetail.DefaultRule?.AppName}
                </a>
                :{routerDetail.DefaultRule?.ContainerPort}
              </p>
            </div>
          </If>
          <If condition={routerDetail.RouteId}>
            <p style={{ fontWeight: 'bold' }}>
              {intl('saenext.components.gateway.CustomForwardingPolicy')}
            </p>
            <CndTable
              dataSource={routerDetail.RouteServices || []}
              columns={serviceColumns}
              pagination={false}
              hasBorder={false}
              refreshIndex={this.state.refreshPolicyIndex}
            />

            <If condition={routerDetail.Fallback}>
              <p style={{ marginTop: 20, fontWeight: 'bold' }}>
                {intl('saenext.components.gateway.FallbackService')}
              </p>
              <p style={{ color: '#aaa' }}>
                {intl('saenext.components.gateway.WhenNoNodeIsAvailable')}

                <span>
                  <a
                    href={
                      this.props.isNewSae
                        ? `/${this.props.regionId}/app-list/${routerDetail.FallbackServices?.AppId}/micro-app/base?name=${routerDetail.FallbackServices?.AppName}`
                        : `/#/AppList/AppDetail?appName=${routerDetail.FallbackServices?.AppName
                        }&appId=${routerDetail.FallbackServices?.AppId}&${window.location.hash.split('?')[1]
                        }`
                    }
                    target="_blank"
                  >
                    {routerDetail.FallbackServices?.AppName}/
                    {routerDetail.FallbackServices?.ServiceName}:
                    {routerDetail.FallbackServices?.ServicePort}
                  </a>
                </span>
              </p>
            </If>
          </If>
        </ComDialog>
      </div>
    );
  }
}

export default GatewayRoute;
