import React from 'react';
import ComDialog from '../../shared/ComDialog';
import _ from 'lodash';
import services from "~/services";
import { Message, Table, Button, CndTable } from '@ali/cnd';
import { lowerFirstData } from '../../../utils/transfer-data';
import { intl } from '@ali/cnd';
import CachedData from '../../../cache/common';

type Props = {
  dataSource: any[];
  slbList: any[];
  regionId: string;
  namespaceId: string;
};

type State = {
  isIngressWarningDialogVisable: boolean;
  info: any;
  warnings: any[];
};

class IngressWarning extends React.Component<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      isIngressWarningDialogVisable: false,
      info: {},
      warnings: [],
    };
  }

  componentDidMount() {
    this.init();
  }

  async init() {
    const { dataSource } = this.props;
    const { warnings } = this.state;
    for await (const item of dataSource) {
      const res = await services.checkIngressWarn({
        IngressId: item.Id,
        NamespaceId: this.props.namespaceId,
        RegionId: this.props.regionId,
      });
      const { Data = {} } = res;
      this.setState({
        warnings: _.concat(warnings, {
          ingressId: item.Id,
          list: lowerFirstData(Data.Warnings),
        }),
      });
    }
  }

  openIngressWarningDialog = (item) => {
    this.setState({
      info: item,
      isIngressWarningDialogVisable: true,
    });
  };

  closeIngressWarningDialog = () => {
    this.setState({
      isIngressWarningDialogVisable: false,
    });
  };

  disableIngressConfigWarn = () => {
    const { dataSource } = this.props;
    const option = [];
    _.each(dataSource, (item) => {
      const fetchOne = services.disableIngressWarn({
        IngressId: item.Id,
        NamespaceId: this.props.namespaceId,
        RegionId: this.props.regionId,
      });
      option.push(fetchOne);
    });
    Promise.all(option).then(() => {
      this.setState(
        {
          warnings: [],
        },
        () => {
          this.init();
        },
      );
    });
  };

  renderMessage = () => {
    const { warnings } = this.state;
    const { dataSource, slbList } = this.props;
    let option = [];
    _.each(warnings, (item) => {
      option = _.concat(option, item.list);
    });

    const warningContent = (
      <Message type="warning" style={{ position: 'relative' }}>
        <Button
          type="primary"
          style={{ position: 'absolute', top: 8, right: 16 }}
          text
          onClick={this.disableIngressConfigWarn}
        >
          {intl('saenext.components.gateway.IngressWarning.NoMoreReminders')}
        </Button>
        <span>
          {_.map(dataSource, (item) => {
            const targetSlb = _.find(slbList, (obj) => obj.loadBalancerId === item.SlbId);
            const ip = targetSlb ? targetSlb.address : item.SlbId;
            const protocol = item.CertIds ? 'HTTPS' : 'HTTP';
            const slbType =
              item.SlbType === 'Internet'
                ? intl('saenext.components.gateway.IngressWarning.PublicNetwork')
                : intl('saenext.components.gateway.IngressWarning.PrivateNetwork');
            return (
              <div>
                {intl('saenext.components.gateway.IngressWarning.YouHaveUpdatedTheRouting', {
                  protocol: protocol,
                  slbType: slbType,
                  ip: ip,
                })}
                <Button
                  type="primary"
                  text
                  onClick={() => this.openIngressWarningDialog({ ...item, ip })}
                >
                  {intl('saenext.components.gateway.IngressWarning.Details')}
                </Button>
              </div>
            );
          })}
          <div className="text-line">
            {intl('saenext.components.gateway.IngressWarning.PleaseAvoidConfiguringGatewayRouting')}{' '}
            <a href={CachedData.confLink('help:sae:limits-of-clb')} target="_blank">
              {intl(
                'saenext.components.gateway.IngressWarning.GatewayRoutingConfigurationConstraints',
              )}
            </a>
          </div>
        </span>
      </Message>
    );

    return option.length > 0 ? warningContent : null;
  };

  render() {
    const { isIngressWarningDialogVisable, info, warnings } = this.state;
    const tableData = _.find(warnings, (item) => item.ingressId === info.id) || {};

    return (
      <div>
        {this.renderMessage()}

        {/* @ts-ignore */}
        <ComDialog
          loading={false}
          title={intl(
            'saenext.components.gateway.IngressWarning.NotificationThatGatewayRoutingConfiguration',
          )}
          // @ts-ignore
          style={{ width: '80%' }}
          visible={isIngressWarningDialogVisable}
          onCancel={this.closeIngressWarningDialog}
          onClose={this.closeIngressWarningDialog}
          cancelButtonText={intl('saenext.components.gateway.IngressWarning.Close')}
          shouldUpdatePosition
        >
          <div>
            <div>
              <Message type="notice">
                <div className="text-line">
                  {intl('saenext.components.gateway.IngressWarning.BecauseYouModifiedTheInstance', {
                    infoIp: info.ip,
                  })}
                  <span style={{ color: '#d93026' }}>
                    {_.join(
                      _.map(tableData.list, item => item.item),
                      ', ',
                    )}
                  </span>
                  <span>
                    {intl(
                      'saenext.components.gateway.IngressWarning.ConflictsWithTheLatestGateway',
                    )}
                  </span>
                </div>
                <div className="text-line">
                  {intl(
                    'saenext.components.gateway.IngressWarning.ThereforeTheModifiedSlbInformation',
                  )}

                  <a href={CachedData.confLink('help:sae:limits-of-clb')} target="_blank">
                    {intl(
                      'saenext.components.gateway.IngressWarning.GatewayRoutingConfigurationConstraints',
                    )}
                  </a>
                </div>
              </Message>
              <CndTable
                dataSource={tableData?.list||[]}
                hasBorder={false}
                columns={[
                  {
                    title: intl('saenext.components.gateway.IngressWarning.ConfigurationItems'),
                    dataIndex: 'item',
                  },
                  {
                    title: intl(
                      'saenext.components.gateway.IngressWarning.LatestConfigurationValuesSavedBy',
                    ),
                    dataIndex: 'actual',
                  },
                  {
                    title: intl(
                      'saenext.components.gateway.IngressWarning.ModifiedValueOfSlbProduct',
                    ),
                    dataIndex: 'expected',
                  },
                  {
                    title: intl('saenext.components.gateway.IngressWarning.Result'),
                    dataIndex: 'message',
                  },
                ]}
                style={{ marginTop: 20 }}
              />
            </div>
          </div>
        </ComDialog>
      </div>
    );
  }
}

export default IngressWarning;
