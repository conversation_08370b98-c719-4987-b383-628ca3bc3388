import React from 'react';
import { intl } from '@ali/cnd';
import { Actions } from '@ali/cnd';
const { LinkButton } = Actions;

type Props = {
  record: NamespaceItem;
  regionId: string;
  handleEdit: (n: NamespaceItem) => void;
  handleDelete: (n: NamespaceItem) => void;
  handleDetail: (n: NamespaceItem) => void;
};

export type NamespaceItem = {
  NamespaceId: string;
  NamespaceName: string;
  NamespaceShortId: string;
  NamespaceDescription: string;
  VpcId: string;
};

const Events = (props: Props) => {
  const {
    record,
    regionId,
    handleEdit = () => {},
    handleDelete = () => {},
    // handleDetail = () => {},
  } = props;
  return (
    // @ts-ignore
    <Actions>
      {/* <LinkButton
        onClick={() => {
          handleDetail(record);
        }}
      >
        {intl('saenext.components.namespace.Events.Details')}
      </LinkButton> */}
      {record.NamespaceId ? (
        <>
          <LinkButton
            onClick={() => {
              handleEdit(record);
            }}
            disabled={record.NamespaceId === regionId}
          >
            {intl('saenext.components.namespace.Events.Edit')}
          </LinkButton>
          <LinkButton
            onClick={() => {
              handleDelete(record);
            }}
            disabled={record.NamespaceId === regionId}
          >
            {intl('saenext.components.namespace.Events.Delete')}
          </LinkButton>
        </>
      ) : null}
    </Actions>
  );
};

export default Events;
