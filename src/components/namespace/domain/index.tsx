import { intl } from '@ali/cnd';
import React from 'react';
import { Message } from '@ali/cnd';
import CustomDomain from '~/components/app-detail/web-app/basic-info/CustomDomain';

const DomianManage = (props) => {
  const {
    // regionId,
    namespaceId,
  } = props;

  return (
    <>
      <Message
        className="mb-s"
        type="notice"
        title={intl('saenext.namespace.domain.InWhatScenariosDoI')}
      >
        <div className="text-line">
          {intl('saenext.namespace.domain.ForExampleYouHaveCreated')}
        </div>
        <div className="text-line color-orange">
          {intl('saenext.namespace.domain.LimitsDomainNameManagementIs')}
        </div>
      </Message>
      <CustomDomain namespaceID={namespaceId} />
    </>
  );
};

export default DomianManage;
