import React from 'react';
import { intl } from '@ali/cnd';
import services from "~/services";
import { SlidePanel, Form, Field, Message, Input, Select } from '@ali/cnd';
import { NamespaceItem } from './Events';
import _ from 'lodash';
import TextRefreshButton from '../shared/TextRefreshButton';
import ExternalLink from '../shared/ExternalLink';
import CachedData from '../../cache/common';

const FormItem = Form.Item;

type Props = {
  type: string;
  regionId: string;
  dialogVisible: boolean;
  initData: NamespaceItem;
  hideDialog: () => void;
  changeRefreshIndex: () => void;
};

type State = {
  isLoading: boolean;
  vpcLoading: boolean;
  initData: NamespaceItem;
  vpcList: { label: string; value: string; vpcName: string; vpcId: string }[];
};

class FromSlidePanel extends React.Component<Props, State> {
  // eslint-disable-next-line no-useless-constructor
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      initData: this.props.initData,
      vpcList: [],
      vpcLoading: false,
    };
  }

  field = new Field(this);

  componentDidMount(): void {
    this.getVpcList();
  }

  getVpcList = async () => {
    this.setState({
      vpcLoading: true,
    });
    const { regionId } = this.props;
    const res = await services.getBelongVpcs({
      Force: true,
      RegionId: regionId,
    });
    const { Data = {} } = res;
    const _vpcList = _.map(Data.Vpcs, ({ VpcName, VpcId }) => ({
      label: VpcName || VpcId,
      value: VpcId,
      vpcName: VpcName,
      vpcId: VpcId,
    }));

    this.setState({
      vpcLoading: false,
      vpcList: _vpcList,
    });
  };

  // onSearchVpc = (searchKey: string = '') => {
  //   const { vpcList } = this.state;
  //   const vpcsFilter = [];

  //   vpcList.forEach((item) => {
  //     if (item?.vpcName?.includes(searchKey) || item?.value?.includes(searchKey)) {
  //       vpcsFilter.push(item);
  //     }
  //   });

  //   this.setState({
  //     vpcsFilter,
  //   });
  // }

  componentWillReceiveProps(nextProps: Readonly<Props>, nextContext: any): void {
    if (nextProps.initData !== this.props.initData) {
      this.setState({
        initData: nextProps.initData,
      });
      const { initData } = nextProps;
      const { setValues } = this.field;
      setValues({
        namespaceName: initData.NamespaceName,
        namespaceId: (initData.NamespaceId && initData.NamespaceId.split(':')[1]) || '',
        description: initData.NamespaceDescription || '',
        vpcId: initData.VpcId || '',
      });
    }
  }

  handleValidate() {
    const { validate } = this.field;
    let isValid = false;
    validate((valid, form) => {
      if (!valid) {
        isValid = true;
      }
    });

    return isValid;
  }

  handleSubmit = async () => {
    if (!this.handleValidate()) {
      return false;
    }
    this.setState({
      isLoading: true,
    });
    const { getValues } = this.field;
    const { type, regionId, changeRefreshIndex, hideDialog } = this.props;
    const values = getValues();
    // @ts-ignore
    const { description = '', namespaceId = '', namespaceName ='', vpcId = '' } = values;
    const params = {
      NamespaceId: `${regionId}:${namespaceId}`,
      NamespaceName: namespaceName as string,
      NamespaceDescription: description as string,
      VpcId: vpcId as string,
    };
    let message = '';
    let _namespaceId = '';
    if (type === 'create') {
      const res = await services.createNamespaceV2({
        params,
        customErrorHandle: (error, data, callback) => {
          this.setState({
            isLoading: false,
          });
          callback && callback();
        },
      });
      message = intl('saenext.components.namespace.FromSlidePanel.NamespaceCreated');
      const { Data = {} } = res;
      _namespaceId = Data?.NamespaceId;
    } else {
      const res = await services.updateNamespace(params);
      message = intl('saenext.components.namespace.FromSlidePanel.TheNamespaceHasBeenEdited');
      const { Data = {} } = res;
      _namespaceId = Data?.NamespaceId;
    }
    this.setState({
      isLoading: false,
    });
    if (_namespaceId) {
      Message.success(message);
      hideDialog();
      changeRefreshIndex();
    }
  };

  // filterVpcList = (value) => {
  //   console.log(value)
  //   const { vpcList } = this.state;
  //   const _vpcList = _.filter(vpcList, (item) => item.vpcName.indexOf(value) > -1 || item.value.indexOf(value) > -1);
  //   this.setState({
  //     vpcList: _vpcList,
  //   });
  // }

  render() {
    const formItemLayout = {
      labelCol: { fixedSpan: 10 },
      wrapperCol: { span: 14 },
    };
    const { isLoading, initData, vpcList, vpcLoading } = this.state;
    const { type = 'create', regionId, dialogVisible, hideDialog } = this.props;
    const { init } = this.field;
    return (
      <SlidePanel
        title={
          type === 'create'
            ? intl('saenext.components.namespace.FromSlidePanel.CreateANamespace')
            : intl('saenext.components.namespace.FromSlidePanel.EditNamespace')
        }
        width="50vw"
        isShowing={dialogVisible}
        isProcessing={isLoading}
        onCancel={hideDialog}
        onClose={hideDialog}
        onOk={this.handleSubmit}
      >
        <Form {...formItemLayout} field={this.field} className="namespace-form" labelAlign="top">
          <FormItem label={intl('saenext.components.namespace.FromSlidePanel.Region')}>
            <p>{regionId}</p>
          </FormItem>
          <FormItem
            label={intl('saenext.components.namespace.FromSlidePanel.NamespaceName')}
            required
          >
            <Input
              {...init('namespaceName', {
                initValue: initData.NamespaceName || '',
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.namespace.FromSlidePanel.TheNamespaceNameCannotBe',
                    ),
                  },
                ],
              })}
              htmlType="text"
              maxLength={63}
              name="namespaceName"
              placeholder={intl(
                'saenext.components.namespace.FromSlidePanel.EnterANamespaceForThe',
              )}
            />
          </FormItem>
          <FormItem
            label={intl('saenext.components.namespace.FromSlidePanel.NamespaceId')}
            required
          >
            <Input
              {...init('namespaceId', {
                initValue: (initData.NamespaceId && initData.NamespaceId.split(':')[1]) || '',
                rules: [
                  {
                    required: true,
                    pattern: type === 'create' ? /^[0-9a-z]*$/ : /^[0-9a-zA-Z]*$/,
                    message: intl(
                      'saenext.components.namespace.FromSlidePanel.NamespaceIdAllowsYouTo',
                    ),
                  },
                ],
              })}
              addonTextBefore={regionId}
              htmlType="text"
              name="namespaceId"
              maxLength={32 - regionId.length - 1}
              disabled={type != 'create'}
              placeholder={intl(
                'saenext.components.namespace.FromSlidePanel.EnglishLettersOrNumbersAre',
              )}
            />
          </FormItem>
          <FormItem label={intl('saenext.components.namespace.FromSlidePanel.Description')}>
            <Input.TextArea
              placeholder={intl('saenext.components.namespace.FromSlidePanel.EnterADescription')}
              name="description"
              {...init('description', {
                initValue: initData.NamespaceDescription || '',
              })}
              showLimitHint
              maxLength={64}
            />
          </FormItem>

          {type === 'create' ? (
            <FormItem label={intl('saenext.components.namespace.FromSlidePanel.Vpc')} required>
              <Select
                name="vpcId"
                // @ts-ignore
                // popupAutoFocus
                dataSource={vpcList}
                itemRender={({ vpcName, vpcId }) => (
                  <>
                    <span className="mr-l">{vpcName || vpcId}</span>
                    <span className="text-description">{vpcId}</span>
                  </>
                )}
                showSearch
                style={{ width: '100%' }}
                state={vpcLoading ? 'loading' : null}
                placeholder={intl(
                  'saenext.components.namespace.FromSlidePanel.ADeploymentEnvironmentCanAssociate',
                )}
                {...init('vpcId', {
                  initValue: initData.VpcId || '',
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.namespace.FromSlidePanel.SelectAnAssociatedVpc',
                      ),
                    },
                  ],
                })}
              />

              <div className="flex">
                <TextRefreshButton onClick={() => this.getVpcList()} />
                <ExternalLink
                  className="ml-l"
                  label={intl('saenext.components.namespace.FromSlidePanel.CreateAVpc')}
                  // @ts-ignore
                  url={`${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/vpcs`}
                />
              </div>
            </FormItem>
          ) : null}
        </Form>
      </SlidePanel>
    );
  }
}

export default FromSlidePanel;
