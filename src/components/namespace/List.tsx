import React, { useState } from 'react';
import { intl } from '@ali/cnd';
import CndTable from '@ali/cnd-table';
import { Button, Dialog, Message } from '@ali/cnd';
import Events, { NamespaceItem } from './Events';
import Create from './Create';
import { get } from 'lodash';
import services from "~/services";
import CachedData from '../../cache/common';

type Props = {
  regionId: string;
  handleDetail: (record: NamespaceItem) => void;
};

const NamespaceList = (props: Props) => {
  const { regionId, handleDetail } = props;
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogType, setDialogType] = useState('create');
  const [initData, setInitData] = useState({});
  const [showListWarn, setShowListWarn] = useState(false);

  const handleCreate = () => {
    setDialogType('create');
    setInitData({});
    setDialogVisible(true);
  };

  const handleEdit = (record) => {
    setDialogType('update');
    setInitData(record);
    setDialogVisible(true);
  };

  const fetchData = async (params) => {
    const _params = {
      CurrentPage: params.current,
      PageSize: params.pageSize,
      // BelongRegion: regionId,
      // RegionId: regionId,
    };
    const res = await services.getNamespaceListV2({
      params: _params,
      customErrorHandle: (error, data) => {
        if (error.code === 'AuthenticationFailed') {
          setShowListWarn(true);
          return {};
        }
        return error;
      },
    });
    return {
      data: get(res, 'Data.Namespaces'),
      total: get(res, 'Data.TotalSize'),
    };
  };

  const handleDelete = (record) => {
    Dialog.alert({
      title: intl('saenext.components.namespace.DeleteANamespace'),
      content: (
        <p>
          {intl('saenext.components.namespace.AreYouSureYouWant')}
          <span style={{ color: '#F90' }}>{record?.NamespaceName}</span> ?
        </p>
      ),

      onOk: () => {
        return services
          .deleteNamespace({
            RegionId: regionId,
            NamespaceId: record?.NamespaceId,
          })
          .then((res) => {
            const { Code, Message: message } = res || {};
            if (Code === 200 && message === 'success') {
              Message.success(intl('saenext.components.namespace.DeletedSuccessfully'));
              changeRefreshIndex();
            } else {
              Message.error(intl('saenext.components.namespace.FailedToDelete'));
              throw new Error(intl('saenext.components.namespace.FailedToDelete'));
            }
          });
      },
    });
  };

  const hideDialog = () => setDialogVisible(false);

  const changeRefreshIndex = () => {
    setRefreshIndex((refreshIndex) => refreshIndex + 1);
  };

  const columns = [
    {
      key: 'NamespaceName',
      title: intl('saenext.components.namespace.NamespaceNameId'),
      dataIndex: 'NamespaceName',
      width: '45%',
      cell: (value, index, record) => {
        return (
          <div>
            <div className="blue-pointer" onClick={() => handleDetail && handleDetail(record)}>
              {value
                ? value === 'Default'
                  ? intl('saenext.components.namespace.Default')
                  : value
                : ''}
            </div>
            <div>{record.NamespaceId ? record.NamespaceId : record.RegionId}</div>
          </div>
        );
      },
    },
    {
      key: 'NamespaceDescription',
      title: intl('saenext.components.namespace.Description'),
      dataIndex: 'NamespaceDescription',
      width: '45%',
      cell: (value, index, record) => {
        return (
          <div>
            {value
              ? value === 'Default Namespace'
                ? intl('saenext.components.namespace.DefaultNamespace')
                : value
              : '--'}
          </div>
        );
      },
    },
    {
      key: 'operations',
      title: intl('saenext.components.namespace.Operation'),
      width: 120,
      cell: (value, index, record) => {
        return (
          <Events
            key={index}
            regionId={regionId}
            record={record}
            handleEdit={handleEdit}
            handleDelete={handleDelete}
            handleDetail={() => handleDetail && handleDetail(record)}
          />
        );
      },
    },
  ];

  return (
    <>
      {showListWarn || isInDebt ? (
        <Message
          type="error"
          className="mb-l"
          title={intl('saenext.components.namespace.AccountExceptionReminder')}
        >
          {intl('saenext.components.namespace.ThisMayBeBecauseYou')}
        </Message>
      ) : null}

      <Message type="notice" className="mb-l">
        {intl('saenext.components.namespace.List.IfYouDeployAnApplication', {
          regionId: regionId,
        })}
      </Message>

      <CndTable
        fetchData={fetchData}
        // @ts-ignore
        columns={columns}
        refreshIndex={refreshIndex}
        showRefreshButton
        // @ts-ignore
        pagination={{ recordCurrent: true }}
        operation={
          <Button type="primary" onClick={handleCreate} disabled={showListWarn || isInDebt}>
            {intl('saenext.components.namespace.CreateANamespace')}
          </Button>
        }
      />

      <Create
        regionId={regionId}
        dialogVisible={dialogVisible}
        type={dialogType}
        initData={initData as NamespaceItem}
        hideDialog={hideDialog}
        changeRefreshIndex={changeRefreshIndex}
      />
    </>
  );
};

export default NamespaceList;
