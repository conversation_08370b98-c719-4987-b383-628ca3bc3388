import React from 'react';
import { DateTime, intl, parseExpression } from '@ali/cnd';
import { isEmpty } from 'lodash';
import services from '~/services';

const CronTimesPreview = (props) => {
  const { triggerTimes } = props;

  if (isEmpty(triggerTimes)) return null;

  return (
    <>
      <p className="mb-s text-bold">
        {intl('saenext.crontab.components.CronTimesPreview.LastFiveExecutionTimes')}
      </p>
      {triggerTimes.map((item, index) => {
        return (
          <div key={index}>
            <DateTime
              value={item}
              format={{
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                weekday: 'long',
                hour: '2-digit',
                minute: '2-digit',
              }}
            />
          </div>
        );
      })}
    </>
  );
};

export default CronTimesPreview;

export const getTriggerTimes = async (value, regionId) => {
  const res = await services.ListTriggerTimes({
    params: {
      RegionId: regionId,
      Type: 'cron',
      Expression: `0 ${value}`,
      EndTime: '2999-12-12 12:00',
      MaxResults: 5,
      TimeZone: 'UTC8',
    },
  });

  if (!res) return;
  const TriggerTime = res.TriggerTimes?.TriggerTime;
  return TriggerTime;
};

export const validateCron = async (value, regionId) => {
  const ErrorRes = {
    error: intl('saenext.crontab.components.CronTimesPreview.TheCronExpressionFormatIs'),
    validate: false,
    triggerTimes: [],
  };

  if (!value) return ErrorRes;
  const newVal = value.trim().split(/\s+/);
  if (newVal.length !== 5) return ErrorRes;
  try {
    parseExpression(value);
    const triggerTimes = await getTriggerTimes(value, regionId);
    if (isEmpty(triggerTimes)) return ErrorRes;
    return {
      error: '',
      validate: true,
      triggerTimes,
    };
  } catch (error) {
    return ErrorRes;
  }
};
