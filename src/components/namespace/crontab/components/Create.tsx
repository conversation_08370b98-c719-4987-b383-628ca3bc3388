import React, { FC, useEffect, useState } from 'react';
import {
  DatePicker,
  Field,
  Form,
  Input,
  Loading,
  Message,
  Radio,
  Select,
  SlidePanel,
  intl,
} from '@ali/cnd';
import _ from 'lodash';
import {
  EActionType,
  ESelectedApp,
  ETriggerType,
  EType,
  TType,
  SLIDE_TITLE,
  ALL_APP,
} from './constant';
import moment from 'moment';
import CndCron from '@ali/cnd-cron';
export * from './constant';
import * as services from '~/services';
import CheckWrapper, { validator } from './CheckWrapper';
import CronTimesPreview, { validateCron } from './CronTimesPreview';
import CachedData from '~/cache/common';
import { TimeZones } from '~/components/shared/TimezoneSelect/constants';

const FormItem = Form.Item;

type Props = {
  namespaceId: string;
  type?: TType;
  dataSource?: Record<string, any>;
  callback: () => void;
};

const Create: FC<Props> = (props) => {
  const { dataSource = {}, namespaceId, type = EType.create, callback = _.noop, children } = props;
  const [isShowing, setIsShowing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [cronLoading, setCronLoading] = useState(false);
  const [triggerTimes, setTriggerTimes] = useState([]);
  const field = Field.useField();
  const { init, getValue, validate, resetToDefault, getError } = field;

  useEffect(() => {
    if (!isShowing) {
      resetToDefault();
      setTriggerTimes([]);
    } else {
      validate('cronExpression');
    }
  }, [isShowing]);

  const handleSubmit = async () => {
    if (type === EType.readonly) {
      return setIsShowing(false);
    }
    const doSubmit = async (params) => {
      const response =
        type === EType.create
          ? await services.CreateTimerRule({ params })
          : await services.UpdateTimerRule({ params });
      if (!_.isEmpty(response)) {
        Message.success(
          type === EType.create
            ? intl('saenext.crontab.components.Create.TheScheduledStartStopRule')
            : intl('saenext.crontab.components.Create.TheScheduledStartStopRule.1'),
        );

        setIsShowing(false);
        callback();
      }
      setIsProcessing(false);
    };
    validate((error, values) => {
      if (error) return;
      setIsProcessing(true);
      const TriggerType = values['TriggerType'];
      const {
        ActionType,
        atExpression,
        TimeZone,
        cronExpression,
        selectedApp,
        WebHookUrl = '',
        Desc,
      } = values;
      const params = {
        Version: '2.0',
        NamespaceId: namespaceId,
        TriggerType,
        ActionType,
        Expression:
          TriggerType === 'at'
            ? getAtExpression(atExpression, TimeZone)
            : window.btoa(`0 ${cronExpression} *`),
        TimeZone,
        AppIds: getAppIds(selectedApp),
        WebHookUrl,
        Desc,
      };

      if (type === EType.edit) {
        params['RuleId'] = dataSource.RuleId;
      }
      doSubmit(params);
    });
  };

  const getAtExpression = (value, timeZone) => {
    const timeLocalStr = value.format('YYYY-MM-DD HH:mm:ss');
    const utcTime = moment(`${timeLocalStr}${timeZone}`);
    const utcTimeStr = utcTime.utc().format();
    return utcTimeStr;
  };

  const getAppIds = (value) => {
    if (_.get(value, 'isSelectAll') === ESelectedApp.SOME) {
      const data = _.map(value.appIds, (item) => _.last(_.split(item, '&&')));
      return data.join(',');
    }
    return 'ALL';
  };

  const handleCancel = () => {
    resetToDefault();
    setIsShowing(false);
  };

  const formatCron = (value) => {
    if (!value) return '';
    return value.split(' ').slice(1, 6).join(' ');
  };

  const cronValidator = async (rule, value, callback) => {
    if (getValue('TriggerType') === ETriggerType.AT) {
      callback();
      return;
    }
    setCronLoading(true);
    const res = await validateCron(value, namespaceId.split(':')[0]);
    setCronLoading(false);
    if (!res.validate) {
      callback(res.error);
    } else {
      setTriggerTimes(res.triggerTimes);
      callback();
    }
  };

  const cronError = getError('cronExpression');

  return (
    <>
      <span onClick={() => setIsShowing((prev) => !prev)}>{children}</span>
      <SlidePanel
        title={SLIDE_TITLE[type]}
        width={900}
        onCancel={type === EType.readonly ? null : handleCancel}
        onOk={handleSubmit}
        isShowing={isShowing}
        isProcessing={isProcessing}
        okText={
          type === EType.readonly
            ? intl('saenext.crontab.components.Create.Close')
            : intl('button.ok')
        }
        cancelText={intl('button.cancel')}
        processingText={intl('button.processing')}
      >
        <Form field={field} disabled={type === EType.readonly}>
          <FormItem
            label={intl('saenext.crontab.components.Create.RuleName')}
            required
            help={intl('saenext.crontab.components.Create.ItMustStartWithA')}
          >
            <Input
              {...init('Desc', {
                initValue: dataSource.Desc,
                rules: [
                  {
                    required: true,
                    message: intl('saenext.crontab.components.Create.ItMustStartWithA'),

                    pattern: /^[a-z][\u4e00-\u9fa5-a-z0-9]{0,61}[a-z0-9]{1,1}$/,
                  },
                ],
              })}
              placeholder={intl('saenext.crontab.components.Create.EnterARuleName')}
            />
          </FormItem>
          <FormItem label={intl('saenext.crontab.components.Create.TriggerAction')} required>
            <Radio.Group
              {...init('ActionType', {
                initValue: _.get(dataSource, 'ActionType', EActionType.BATCH_START),
              })}
              disabled={type === EType.edit}
            >
              <Radio id={EActionType.BATCH_START} value={EActionType.BATCH_START}>
                {intl('saenext.crontab.components.Create.StartApplicationsInBatches')}
              </Radio>
              <Radio id={EActionType.BATCH_STOP} value={EActionType.BATCH_STOP}>
                {intl('saenext.crontab.components.Create.StopApplicationsInBatches')}
              </Radio>
            </Radio.Group>
          </FormItem>
          <FormItem label={intl('saenext.crontab.components.Create.ExecutionFrequency')} required>
            <Radio.Group
              {...init('TriggerType', {
                initValue: _.get(dataSource, 'TriggerType', ETriggerType.AT),
              })}
              disabled={type === EType.edit}
            >
              <Radio id={ETriggerType.AT} value={ETriggerType.AT}>
                {intl('saenext.crontab.components.Create.ExecuteOnlyOnce')}
              </Radio>
              <Radio id={ETriggerType.CRON} value={ETriggerType.CRON}>
                {intl('saenext.crontab.components.Create.RegularExecution')}
              </Radio>
            </Radio.Group>
          </FormItem>
          <FormItem>
            {getValue('TriggerType') === ETriggerType.AT ? (
              <>
                <DatePicker
                  style={{ width: '300px' }}
                  showTime
                  format="YYYY-MM-DD"
                  {...init('atExpression', {
                    initValue: dataSource.Expression
                      ? moment
                          .utc(dataSource.Expression)
                          .add(dataSource.TimeZone || moment().format('Z'), 'hours')
                      : moment(),
                  })}
                />

                <FormItem label={intl('saenext.crontab.components.Create.TimeZone')}>
                  <Select
                    dataSource={TimeZones}
                    name="TimeZone"
                    defaultValue={dataSource.TimeZone || moment().format('Z')}
                    style={{ width: '300px' }}
                  />
                </FormItem>
              </>
            ) : (
              <>
                <CndCron
                  {...init('cronExpression', {
                    initValue: formatCron(dataSource.Expression),
                    rules: [{ validator: cronValidator }],
                  })}
                  renderTime={false}
                />

                <div className="mt-xs">
                  {cronError ? (
                    <div style={{ color: 'red' }}>{cronError}</div>
                  ) : (
                    <Loading visible={cronLoading} style={{ width: 200 }}>
                      <CronTimesPreview triggerTimes={triggerTimes} />
                    </Loading>
                  )}
                </div>
                <Message type="notice" className="mt-xl">
                  {intl('saenext.crontab.components.Create.TheScheduledExecutionTimeZone')}
                </Message>
              </>
            )}
          </FormItem>
          <FormItem
            label={intl(
              'saenext.crontab.components.Create.NotificationConfigurationBeforeExecution',
            )}
            help={intl('saenext.crontab.components.Create.AfterTheConfigurationIsConfigured')}
          >
            <div>
              <Input
                style={{ width: '300px' }}
                placeholder={intl(
                  'saenext.crontab.components.Create.PleaseEnterDingtalkRobotWebhook',
                )}
                {...init('WebHookUrl', {
                  initValue: dataSource.WebHookUrl,
                })}
                disabled={type === EType.readonly}
              />

              <a
                style={{ paddingLeft: '8px' }}
                target="_blank"
                href={CachedData.confLink('help:sae:contacts')}
              >
                {intl('saenext.crontab.components.Create.HowToObtainTheDingtalk')}
              </a>
            </div>
          </FormItem>
          <FormItem
            label={intl('saenext.crontab.components.Create.ListOfTriggeredApplications')}
            required
          >
            <CheckWrapper
              {...(init('selectedApp', {
                initValue: {
                  appSource: _.get(dataSource, 'AppSources', [])[0],
                  isSelectAll: _.isEqual(dataSource.AppIds, ALL_APP)
                    ? ESelectedApp.ALL
                    : ESelectedApp.SOME,
                  appIds: _.isEqual(dataSource.AppIds, ALL_APP)
                    ? []
                    : _.map(
                        dataSource.AppNames,
                        (name, index) => `${name}&&${dataSource.AppIds[index]}`,
                      ),
                },

                rules: [
                  {
                    validator,
                  },
                ],
              }) as any)}
              type={props.type}
              namespaceId={namespaceId}
              disabled={type === EType.readonly}
              ActionType={getValue('ActionType')}
            />
          </FormItem>
        </Form>
      </SlidePanel>
    </>
  );
};

export default Create;
