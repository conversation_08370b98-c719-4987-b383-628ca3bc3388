import { intl } from '@ali/cnd';
import React, { FC, useState } from 'react';
import { Message, Radio } from '@ali/cnd';
import TableOption from './TableOption';
import _ from 'lodash';
import { TActionType, EActionType, ESelectedApp, TSelectedApp, SAEAPP_TYPE, TType } from './constant';

type Props = {
  type?: TType;
  namespaceId: string;
  ActionType: TActionType;
  value?: {
    isSelectAll?: TSelectedApp;
    appIds?: string[];
    appSource?: 'web' | 'web2' | 'micro_service';
  };
  onChange?: (val: { isSelectAll?: TSelectedApp; appIds?: string[] }) => void;
  disabled?: boolean;
};
const CheckAppWrapper: FC<Props> = (props) => {
  const {
    value = {},
    onChange,
    disabled,
    namespaceId,
    ActionType = EActionType.BATCH_START,
  } = props;
  const { appSource, isSelectAll: isSelectAllProps = ESelectedApp.ALL, appIds } = value;
  const [isSelectAll, setIsSelectAll] = useState<TSelectedApp>(isSelectAllProps);

  const renderApp = () => {
    if (isSelectAll === ESelectedApp.ALL) return null;
    let _appSource = SAEAPP_TYPE.MICRO_SERVICE;
    switch (appSource) {
      // case 'web':
      //   _appSource = SAEAPP_TYPE.WEB_ALWAYS;
      //   break;
      case 'web2':
        _appSource = SAEAPP_TYPE.WEB_REQURST;
        break;
      case 'web':
      case 'micro_service':
        _appSource = SAEAPP_TYPE.MICRO_SERVICE;
        break;
      default:
        break;
    };
    return (
      <TableOption
        type={props.type}
        disabled={disabled}
        value={appIds}
        appSource={_appSource}
        onChange={(val) => {
          value.appIds = val;
          onChange(value);
        }}
        namespaceId={namespaceId}
      />
    );
  };

  return (
    <>
      <Message type="warning">
        {ActionType === EActionType.BATCH_START
          ? intl('saenext.crontab.components.CheckWrapper.AfterYouStartApplicationsIn')
          : intl('saenext.crontab.components.CheckWrapper.AfterABatchOfApplications')}
      </Message>
      <Radio.Group
        value={isSelectAll}
        onChange={(val: TSelectedApp) => {
          setIsSelectAll(val);
          value.isSelectAll = val;
          onChange(value);
        }}
        disabled={disabled}
      >
        <Radio id={ESelectedApp.ALL} value={ESelectedApp.ALL}>
          {intl('saenext.crontab.components.CheckWrapper.StartAndStopAllApplications')}
        </Radio>
        <Radio id={ESelectedApp.SOME} value={ESelectedApp.SOME}>
          {intl('saenext.crontab.components.CheckWrapper.StartAndStopSomeApplications')}
        </Radio>
      </Radio.Group>
      {renderApp()}
    </>
  );
};

export default CheckAppWrapper;

export const validator = (rule, value, callback) => {
  const { isSelectAll, appIds } = value || {};
  if (isSelectAll === ESelectedApp.SOME) {
    return _.isEmpty(appIds)
      ? callback(intl('saenext.crontab.components.CheckWrapper.SelectAtLeastOneApplication'))
      : callback();
  }
  return callback();
};
