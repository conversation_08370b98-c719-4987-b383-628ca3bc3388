import { intl } from '@ali/cnd';
import React, { useState, useContext, useEffect } from 'react';
import CndTable, { ISearch } from '@ali/cnd-table';
import services from '~/services';
import { getParams, setSearchParams } from '~/utils/global';
import _ from 'lodash';
import { Copy, ConsoleContext, Select } from '@ali/cnd';
import { TagTips } from '@ali/xconsole-rc-tags';
import { RESOURCE_TYPE, FILTERS, SAEAPP_TYPE, TType, EType } from './constant';
import SelectedApp from '~/components/app-list/micro-app/components/BatchOperation/SelectedApp';
import useOpenApi from '~/utils/useOpenApi';
import FeatureContext from '~/utils/featureContext';

type Props = {
  type?: TType;
  namespaceId: string;
  value?: string[];
  appSource?: keyof typeof SAEAPP_TYPE;
  onChange?: (val: string[]) => void;
  disabled?: boolean;
};

const TableOption = (props: Props) => {
  const {
    type = EType.create,
    value,
    appSource = SAEAPP_TYPE.MICRO_SERVICE,
    onChange,
    disabled,
    namespaceId: NamespaceId,
  } = props;
  const { region } = useContext(ConsoleContext);
  const _value = type === EType.create ? [] : value;
  const currentRegionId = region.getCurrentRegionId();
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [appTypes, setAppTypes] = useState(appSource);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>(_value);
  const { run } = useOpenApi('serverless', 'ListWebApplicationsWithStatus', {}, { manual: true });

  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  useEffect(() => {
    onChange && onChange(selectedRowKeys);
  }, [JSON.stringify(selectedRowKeys)]);

  const fetchData = async (params) => {
    let appSource = 'micro_service';
    let fn = getMicroApplications;
    switch (appTypes) {
      case SAEAPP_TYPE.WEB_REQURST:
        appSource = '';
        fn = getWebApplications;
        break;
      // case SAEAPP_TYPE.WEB_ALWAYS:
      //   appSource = 'web';
      //   fn = getMicroApplications;
      //   break;
      case SAEAPP_TYPE.MICRO_SERVICE:
        appSource = 'micro_service';
        fn = getMicroApplications;
        break;
      default:
        break;
    }
    return await fn({ ...params, appSource });
  };

  const getWebApplications = async (params) => {
    const getData = async (nextToken = '', data = []) => {
      const result = await run({
        limit: 100,
        nextToken,
        namespaceID: NamespaceId,
      });
      data.push(...result.applications || []);
      if (result.nextToken) {
        await getData(result.nextToken, data);
      }
      return data;
    };

    let data = await getData();
    data = _.map(data, (item) => ({
      ...item.application,
      status: item.status,
    }));
    data = _.orderBy(
      data,
      [
        (item) => {
          const { lastModifiedTime } = item;
          return lastModifiedTime ? new Date(lastModifiedTime).getTime() : 0;
        },
      ],

      ['desc'],
    );

    return {
      data: _.map(data, (item) => ({
        ...item,
        AppName: item.applicationName,
        NamespaceId: item.namespaceID,
        // 当前实例数
        RunningInstances: _.get(item, 'status.instanceCount', 0),
        // 目标实例数 暂时拿最小实例数
        Instances: _.get(item, 'status.scaleConfig.minimumInstanceCount', 0),
        primaryKey: `${item.applicationName}&&${item.applicationId}`,
      })),
      total: data.length,
    };
  };

  const getMicroApplications = async (params) => {
    const filtersMap = _.values(FILTERS);
    setSearchParams(_.pick(params, filtersMap));
    const keys = Object.keys(params);
    const fieldType = _.find(keys, (k) => {
      return _.includes(filtersMap, k);
    });
    const _params = {
      CurrentPage: params.current,
      PageSize: params.pageSize,
      ClusterType: '8',
      EventSubmitDoQuery: 1,
      PhysicalRegionId: currentRegionId,
      NamespaceId,
      FieldType: fieldType,
      FieldValue: _.get(params, fieldType),
    };
    const {
      Data: { Applications, TotalSize },
    } = await services.listMicroApplications({
      params: _params,
      customErrorHandle(err, serviceConfig, callback) {
        if (err) {
          return {
            data: [],
            total: 0,
          };
        }
      },
    });
    // primaryKey 选中应用后，方便展示 应用名称
    return {
      data: _.map(Applications, (item) => ({
        ...item,
        primaryKey: `${item.AppName}&&${item.AppId}`,
      })),
      total: TotalSize,
    };
  };

  const columns = [
    {
      key: 'AppName',
      title: intl('saenext.crontab.components.TableOption.ApplicationName'),
      dataIndex: 'AppName',
      width: 180,
      lock: 'left',
      cell: (value, index, record) => (
        <Copy text={value}>
          {value}
          {record.appDeletingStatus && (
            <span style={{ color: 'red' }}>
              {intl('saenext.crontab.components.TableOption.Deleting')}
            </span>
          )}
        </Copy>
      ),
    },
    {
      key: 'Tags',
      title: intl('saenext.crontab.components.TableOption.Label'),
      dataIndex: 'Tags',
      width: 80,
      cell: (value, index, record) => (
        <TagTips
          regionId={currentRegionId}
          resourceId={record.AppId}
          resourceType={RESOURCE_TYPE}
          dataSource={_.map(value, (item) => ({ tagKey: item.Key, tagValue: item.Value }))}
          hideEdit
        />
      ),
    },
    {
      key: 'NamespaceId',
      title: intl('saenext.crontab.components.TableOption.Namespace'),
      dataIndex: 'NamespaceId',
      width: 180,
      cell: (value = currentRegionId, index, record) => {
        return <Copy text={value}>{value}</Copy>;
      },
    },
    {
      key: 'ScaleRuleType',
      title: intl('saenext.crontab.components.TableOption.EnableStatusOfElasticPolicy'),
      dataIndex: 'ScaleRuleType',
      width: 150,
      cell: (value, index, record) => {
        if (_.isEmpty(record.ScaleRuleType)) {
          return intl('saenext.crontab.components.TableOption.NotSet');
        } else if (!record.ScaleRuleEnabled) {
          return intl('saenext.crontab.components.TableOption.NotEnabled');
        } else if (record.ScaleRuleEnabled && record.ScaleRuleType === 'metric') {
          return (
            <div>
              <span style={{ color: '#07c' }}>
                {intl('saenext.crontab.components.TableOption.Enabled')}
              </span>
              {intl('saenext.crontab.components.TableOption.MetricElasticityNumberOfInstances', {
                recordRunningInstances: record.RunningInstances,
              })}
            </div>
          );
        } else if (record.ScaleRuleEnabled && record.ScaleRuleType === 'timing') {
          return (
            <div>
              <span style={{ color: '#07c' }}>
                {intl('saenext.crontab.components.TableOption.Enabled')}
              </span>
              {intl('saenext.crontab.components.TableOption.TimingElasticity')}
            </div>
          );
        } else if (record.ScaleRuleEnabled && record.ScaleRuleType === 'mix') {
          return (
            <div>
              <span style={{ color: '#07c' }}>
                {intl('saenext.crontab.components.TableOption.Enabled')}
              </span>
              {intl('saenext.crontab.components.TableOption.HybridElasticityNumberOfInstances', {
                recordRunningInstances: record.RunningInstances,
              })}
            </div>
          );
        }
      },
    },
    {
      key: 'ScaleRuleEnabled',
      title: intl('saenext.crontab.components.TableOption.CurrentInstancesDestinationInstances'),
      dataIndex: 'ScaleRuleEnabled',
      width: 190,
      cell: (value, index, record) => {
        if (record.ScaleRuleEnabled && record.ScaleRuleType && record.ScaleRuleType != 'timing') {
          return '--';
        }
        return `${record.RunningInstances}/${record.Instances}`;
      },
    },
  ];

  const search = {
    defaultDataIndex: 'appName',
    defaultSelectedDataIndex: 'appName',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.crontab.components.TableOption.ApplicationName'),
        dataIndex: FILTERS.APP_NAME,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.crontab.components.TableOption.EnterAnApplicationName'),
        },
        defaultValue: getParams(FILTERS.APP_NAME),
      },
    ],
  };

  const operation = (
    <Select
      value={appTypes}
      disabled={disabled}
      dataSource={[
        {
          label: intl('saenext.crontab.components.TableOption.WebApplications'),
          value: SAEAPP_TYPE.WEB_REQURST,
          disabled: type === EType.edit && appSource !== SAEAPP_TYPE.WEB_REQURST,
        },
        {
          label: intl('saenext.crontab.components.TableOption.MicroserviceApplications'),
          value: SAEAPP_TYPE.MICRO_SERVICE,
          disabled: type === EType.edit && appSource !== SAEAPP_TYPE.MICRO_SERVICE,
        },
      ]}
      onChange={(_appTypes: SAEAPP_TYPE) => {
        setSelectedRowKeys([]);
        setAppTypes(_appTypes);
        setRefreshIndex(Date.now());
      }}
    />
  );

  return (
    <div style={{ marginTop: 8 }}>
      <SelectedApp dataSource={selectedRowKeys} />
      <CndTable
        style={{ marginTop: 16, display: 'block' }}
        fetchData={fetchData}
        primaryKey="primaryKey"
        columns={columns as []}
        recordCurrent
        refreshIndex={refreshIndex}
        pagination={{ pageSizeList: [10, 20, 50, 100], hideOnlyOnePage: true }}
        search={search as ISearch}
        operation={EnableWebApplication && operation}
        rowSelection={{
          selectedRowKeys,
          onChange(selected, records) {
            setSelectedRowKeys(selected);
          },
          getProps: () => {
            return { disabled };
          },
        }}
      />
    </div>
  );
};

export default TableOption;
