import { intl } from '@ali/cnd';
import React from 'react';
import { Message, Button } from '@ali/cnd';
import CachedData from '~/cache/common';

const EnableOOS = () => {
  return (
    <>
      <Message type="notice">
        <div className="text-line">
          {intl('saenext.crontab.components.EnableOOS.TheBatchScheduledStartAnd')}
        </div>
        <div className="text-line">
          {intl('saenext.crontab.components.EnableOOS.TheBackendOfThisFunction')}
        </div>
      </Message>
      <div style={{ height: 370, position: 'relative' }}>
        <img
          style={{
            width: '100%',
            height: '100%',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'top',
            backgroundSize: 'cover',
            objectFit: 'cover',
          }}
          src="https://img.alicdn.com/imgextra/i3/O1CN01KugJ3G1h5YSc1CulU_!!6000000004226-1-tps-3840-740.gif"
        />

        <Button
          type="primary"
          style={{ position: 'absolute', top: 32, left: 0, zIndex: 1 }}
          onClick={() => (window.location.href = CachedData.confLink('feature:oos:url'))}
        >
          {intl('saenext.crontab.components.EnableOOS.ActivateOosOMOrchestration')}
        </Button>
      </div>
    </>
  );
};

export default EnableOOS;
