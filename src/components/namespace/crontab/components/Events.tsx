import { intl } from '@ali/cnd';
import React, { FC } from 'react';
import { Actions, Dialog, Message } from '@ali/cnd';
import * as services from '~/services';
import _ from 'lodash';
import Create, { EType } from './Create';

const { LinkButton } = Actions;

type Props = {
  record: Record<string, any>;
  callback: () => void;
};

const Events: FC<Props> = (props) => {
  const { record = {}, callback = _.noop } = props;
  const { Desc, RuleId, NamespaceId } = record;

  const handleStop = () => {
    Dialog.confirm({
      title: intl('saenext.crontab.components.Events.DisableARule'),
      content: <p>{intl.html('saenext.crontab.components.Events.AreYouSureYouWant', { Desc })}</p>,

      messageProps: {
        type: 'warning',
      },
      onOk: async () => {
        const res = await services.DisableTimerRule({ params: { RuleId, NamespaceId } });
        Message.success(intl('saenext.crontab.components.Events.TheRuleHasBeenDisabled'));
        callback();
        return res;
      },
    });
  };
  const handleStart = () => {
    Dialog.confirm({
      title: intl('saenext.crontab.components.Events.EnableRules'),
      content: <p>{intl.html('saenext.crontab.components.Events.AreYouSureYouWant.1', { Desc })}</p>,

      messageProps: {
        type: 'warning',
      },
      onOk: async () => {
        const res = await services.EnableTimerRule({
          params: { RuleId, NamespaceId, Version: '2.0' },
        });
        Message.success(intl('saenext.crontab.components.Events.TheRuleIsEnabled'));
        callback();
        return res;
      },
    });
  };

  const handleDelete = () => {
    Dialog.confirm({
      title: intl('saenext.crontab.components.Events.DeleteStartAndStopRules'),
      content: <p>{intl.html('saenext.crontab.components.Events.AreYouSureYouWant.2', { Desc })}</p>,

      messageProps: {
        type: 'warning',
      },
      onOk: async () => {
        const res = await services.DeleteTimerRule({ params: { RuleId, NamespaceId } });
        Message.success(intl('saenext.crontab.components.Events.TheRuleHasBeenDeleted'));
        callback();
        return res;
      },
    });
  };

  return (
    <Actions>
      {record.Enable ? (
        <LinkButton onClick={handleStop}>
          {intl('saenext.crontab.components.Events.Disable')}
        </LinkButton>
      ) : (
        <LinkButton onClick={handleStart}>
          {intl('saenext.crontab.components.Events.Enable')}
        </LinkButton>
      )}

      <Create type={EType.edit} dataSource={record} namespaceId={NamespaceId} callback={callback}>
        <LinkButton>{intl('saenext.crontab.components.Events.Edit')}</LinkButton>
      </Create>
      <LinkButton onClick={handleDelete}>
        {intl('saenext.crontab.components.Events.Delete')}
      </LinkButton>
    </Actions>
  );
};

export default Events;
