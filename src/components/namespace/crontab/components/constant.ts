import { intl } from '@ali/cnd';
export enum EActionType {
  BATCH_START = 'BatchStartApps',
  BATCH_STOP = 'BatchStopApps',
}

export type TActionType = `${EActionType}`;

export enum ETriggerType {
  AT = 'at',
  CRON = 'cron',
}

export enum EType {
  create = 'create',
  edit = 'edit',
  readonly = 'readonly',
}

export type TType = `${EType}`;

export enum ESelectedApp {
  ALL = 'all',
  SOME = 'some',
}

export type TSelectedApp = `${ESelectedApp}`;

export const SLIDE_TITLE = {
  [EType.create]: intl('saenext.crontab.components.constant.CreateAScheduledStartStop'),
  [EType.edit]: intl('saenext.crontab.components.constant.EditScheduledStartStopRules'),
  [EType.readonly]: intl('saenext.crontab.components.constant.ViewScheduledStartAndStop'),
};

export const ALL_APP = ['ALL'];

export const RESOURCE_TYPE = 'ALIYUN::SAE::APPLICATION';

export const FILTERS = {
  APP_NAME: 'appName',
  APP_ID: 'appIds',
  SLB_IP: 'slbIps',
  INSTANCE_IP: 'instanceIps',
};

export enum SAEAPP_TYPE {
  WEB_REQURST = 'WEB_REQURST',
  WEB_ALWAYS = 'WEB_ALWAYS',
  MICRO_SERVICE = 'MICRO_SERVICE',
}
