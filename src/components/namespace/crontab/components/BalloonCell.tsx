import { intl } from '@ali/cnd';
import React, { FC } from 'react';
import _ from 'lodash';
import { Balloon } from '@ali/cnd';

type Props = {
  record: Record<string, any>;
};

const CellApplist: FC<Props> = (props) => {
  // @ts-ignore
  const { record = {}, appTotal } = props;
  const { AppIds = [], AppNames = [] } = record;

  const ClickTarget = <a>{intl('saenext.crontab.components.BalloonCell.More')}</a>;

  if (_.head(AppIds) === 'ALL' || _.head(AppNames) === 'ALL') {
    return (
      <span>{intl('saenext.crontab.components.BalloonCell.AllApplicationsInTheNamespace')}</span>
    );
  }

  const renderMore = () => {
    if (record.AppNames.length <= 3) return;
    return (
      <Balloon trigger={ClickTarget} triggerType="click">
        {_.map(record.AppNames, (item, idx) => {
          return (
            <span key={idx}>
              {item}
              {idx + 1 < record.AppNames.length ? ', ' : null}
            </span>
          );
        })}
      </Balloon>
    );
  };

  if (record.AppNames && record.AppNames.length > 0) {
    return (
      <>
        {_.map([1, 2, 3], (item, idx) => {
          return (
            <span key={idx}>
              {AppNames[idx]}
              {idx < 2 && AppNames[idx] && idx + 1 < AppNames.length ? ', ' : null}
              {idx + 1 === 3 && AppNames.length > 3 ? '... ' : null}
            </span>
          );
        })}
        {renderMore()}
      </>
    );
  }
};

export default CellApplist;
