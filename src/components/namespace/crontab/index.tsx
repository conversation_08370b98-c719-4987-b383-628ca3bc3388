import { intl } from '@ali/cnd';
import { Button, Copy, Message, StatusIndicator } from '@ali/cnd';
import CndTable from '@ali/cnd-table';
import _ from 'lodash';
import moment from 'moment';
import React, { useState, FC, useEffect } from 'react';
import BalloonCell from './components/BalloonCell';
import EnableOOS from './components/EnableOOS';
import Create, { EType } from './components/Create';
import Events from './components/Events';
import * as services from '~/services';
import { Link } from 'dva/router';
import CachedData from '~/cache/common';

type Props = {
  regionId: string;
  namespaceId: string;
  history: any;
  location: { search }, // ?后参数
};

const Crontab: FC<Props> = (props) => {
  const {
    regionId,
    namespaceId,
    history,
    location: { search }, // ?后参数
  } = props;
  if (_.get(window.ALIYUN_CONSOLE_CONFIG, 'OPEN_STATUS.oos.authentication') !== 'true')
    return <EnableOOS />;
  const [refreshIndex, setRefreshIndex] = useState(0);

  const operation = (
    <Create
      type={EType.create}
      namespaceId={namespaceId}
      callback={() => setRefreshIndex(Date.now())}
    >
      <Button type="primary">{intl('saenext.namespace.crontab.CreateAScheduledStartStop')}</Button>
    </Create>
  );

  const columns = [
    {
      key: 'Desc',
      title: intl('saenext.namespace.crontab.RuleName'),
      dataIndex: 'Desc',
      width: 180,
      lock: 'left',
      cell: (value, index, record) => {
        return (
          <Create
            dataSource={record}
            type={EType.readonly}
            namespaceId={namespaceId}
            callback={() => setRefreshIndex(Date.now())}
          >
            <Link to='' onClick={(e) => { e.preventDefault() }}>
              <Copy text={value}>{value}</Copy>
            </Link>
          </Create>
        );
      },
    },
    {
      key: 'NamespaceId',
      title: intl('saenext.namespace.crontab.Namespace'),
      dataIndex: 'NamespaceId',
      width: 180,
      cell: (value, index, record) => {
        return <Copy text={value}>{value}</Copy>;
      },
    },
    {
      key: 'ActionType',
      title: intl('saenext.namespace.crontab.TriggerAction'),
      dataIndex: 'ActionType',
      width: 120,
      cell: (value, index, record) => {
        return value === 'BatchStartApps'
          ? intl('saenext.namespace.crontab.StartApplicationsInBatches')
          : intl('saenext.namespace.crontab.StopApplicationsInBatches');
      },
    },
    {
      key: 'TriggerType',
      title: intl('saenext.namespace.crontab.ExecutionFrequency'),
      dataIndex: 'TriggerType',
      width: 120,
      cell: (value, index, record) => {
        return value === 'at'
          ? intl('saenext.namespace.crontab.ExecuteOnlyOnce')
          : intl('saenext.namespace.crontab.RegularExecution');
      },
    },
    {
      key: 'Enable',
      title: intl('saenext.namespace.crontab.Status'),
      dataIndex: 'Enable',
      width: 120,
      cell: (value, index, record) => {
        if (value) {
          return (
            <StatusIndicator type="success">
              {intl('saenext.namespace.crontab.Enabled')}
            </StatusIndicator>
          );
        }
        return (
          <StatusIndicator type="disabled">
            {intl('saenext.namespace.crontab.Disabled')}
          </StatusIndicator>
        );
      },
    },
    {
      key: 'AppNames',
      title: intl('saenext.namespace.crontab.ListOfTriggeredApplications'),
      dataIndex: 'AppNames',
      width: 180,
      cell: (value, index, record) => {
        return <BalloonCell record={record} />;
      },
    },
    {
      key: 'LastExecuteTime',
      title: intl('saenext.namespace.crontab.LastTriggerTime'),
      dataIndex: 'LastExecuteTime',
      width: 180,
      cell: (value, index, record) => {
        if (!value) return '--';
        return (
          <>
            {moment(value).format('YYYY-MM-DD HH:mm:ss')}
            <Button
              type="primary"
              text
              onClick={() => {
                const { LastChangeOrderId } = record;
                if (!LastChangeOrderId) return;
                history.push(`/${regionId}/namespace/${namespaceId}/record/${LastChangeOrderId}${search}`)
              }}
            >
              {intl('saenext.namespace.crontab.ChangeOrderDetails')}
            </Button>
          </>
        );
      },
    },
    {
      key: 'operating',
      title: intl('saenext.namespace.crontab.Operation'),
      dataIndex: 'operating',
      width: 180,
      lock: 'right',
      cell: (value, index, record) => (
        <Events record={record} callback={() => setRefreshIndex(Date.now())} />
      ),
    },
  ];

  const fetchData = async (params) => {
    const { Data } = await services.ListTimerRules({
      params: {
        NamespaceId: namespaceId,
      },
    });
    return {
      data: Data,
      total: Data.length,
    };
  };

  return (
    <>
      <Message type="notice" className="mb-s">
        {intl('saenext.namespace.crontab.TheScheduledStartStopFunction')}

        <a href={CachedData.confLink('help:sae:manage-a-scheduled-start-and-stop-rule')} target="_blank">
          {intl('saenext.namespace.crontab.HowToUseScheduledStart')}
        </a>
      </Message>
      <CndTable
        columns={columns}
        fetchData={fetchData}
        refreshIndex={refreshIndex}
        style={{ display: 'block' }}
        showRefreshButton
        pagination={false}
        operation={operation}
      />
    </>
  );
};

export default Crontab;
