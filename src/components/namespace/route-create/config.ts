import { intl } from '@ali/cnd';

export enum Match {
  equal = 'EQUAL',
  pre = 'PRE',
  ergula = 'ERGULAR',
}

export enum ApigMatch {
  equal = 'Exact',
  pre = 'Prefix',
  ergula = 'Regex',
}

export enum Source {
  mse = 'MSE',
  sae = 'SAE',
}

export enum ApigSource {
  sae = 'SAE_NACOS',
  mse = 'MSE_NACOS',
  k8s = 'SAE_K8S_SERVICE',
}
export const SourceCodeMap = {
  MSE: 2, // mse网关类型时使用
  SAE: 0, // mse网关类型时使用
  SAE_NACOS: 0, // apig网关类型使用
  MSE_NACOS: 2, // apig网关类型使用
  SAE_K8S_SERVICE: 9, // apig网关类型使用
};
export const Scene = {
  alone: 'Single',
  multiple: 'Multiple',
  label: 'VersionOriented',
};
export const MatchRules = [
  { label: intl('saenext.components.route-create.MseForwardform.Equal'), value: Match.equal },
  { label: intl('saenext.components.route-create.MseForwardform.PrefixIs'), value: Match.pre },
  {
    label: intl('saenext.components.route-create.MseForwardform.RegularMatching'),
    value: Match.ergula,
  },
];

export const ApigMatchRules = [
  { label: intl('saenext.components.route-create.MseForwardform.Equal'), value: ApigMatch.equal },
  { label: intl('saenext.components.route-create.MseForwardform.PrefixIs'), value: ApigMatch.pre },
  {
    label: intl('saenext.components.route-create.MseForwardform.RegularMatching'),
    value: ApigMatch.ergula,
  },
];

export const Sources = [
  {
    label: intl('saenext.components.route-create.MseForwardform.SaeBuiltInNacos'),
    value: Source.sae,
  },
  { label: 'MSE Nacos', value: Source.mse },
];

export const Methods = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'OPTIONS', value: 'OPTIONS' },
  { label: 'HEAD', value: 'HEAD' },
  { label: 'PATCH', value: 'PATCH' },
  { label: 'TRACE', value: 'TRACE' },
  { label: 'CONNECT', value: 'CONNECT' },
];

export const retryList = [
  {
    label: '5xx',
    value: '5xx',
    tip: intl('saenext.components.route-create.config.TheBackendServiceReturnsAny'),
  },
  {
    label: 'reset',
    value: 'reset',
    tip: intl('saenext.components.route-create.config.ConnectionDisconnectionResetAndRead'),
  },
  {
    label: 'connect-failure',
    value: 'connect-failure',
    tip: intl('saenext.components.route-create.config.TheRequestIsDisconnected'),
  },
  {
    label: 'refused-stream',
    value: 'refused-stream',
    tip: intl('saenext.components.route-create.config.TheBackendServiceResetsThe'),
  },
  {
    label: 'retriable-status-codes',
    value: 'retriable-status-codes',
    tip: intl('saenext.components.route-create.config.TheHttpStatusCodeOf'),
  },
  {
    label: 'cancelled',
    value: 'cancelled',
    tip: intl('saenext.components.route-create.config.TheGrpcStatusCodeIn'),
  },
  {
    label: 'deadline-exceeded',
    value: 'deadline-exceeded',
    tip: intl('saenext.components.route-create.config.TheGrpcStatusCodeIn.1'),
  },
  {
    label: 'internal',
    value: 'internal',
    tip: intl('saenext.components.route-create.config.TheGrpcStatusCodeIn.2'),
  },
  {
    label: 'resource-exhausted',
    value: 'resource-exhausted',
    tip: intl('saenext.components.route-create.config.TheGrpcStatusCodeIn.3'),
  },
  {
    label: 'unavailable',
    value: 'unavailable',
    tip: intl('saenext.components.route-create.config.TheGrpcStatusCodeIn.4'),
  },
];

export const NACOS_DEFAULT_NAMESPACEID = 'public';
