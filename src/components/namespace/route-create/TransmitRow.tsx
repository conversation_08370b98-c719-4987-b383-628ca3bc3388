import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { map, get, isEmpty, head, has, set } from 'lodash';
import { Gateway } from './index';
import { Protocals } from './AlbForwardform';
import {
  Button,
  Icon,
  Grid,
  Input,
  Select,
  NumberPicker,
  Actions,
  Dialog,
  Form,
  Field,
  ToolTipCondition,
} from '@ali/cnd';
import CachedData from '../../../cache/common';

const { LinkButton } = Actions;
const { Row, Col } = Grid;

const fields = {
  domain: 'domain',
  port: 'port',
  path: 'path',
  appId: 'appId',
  cport: 'containerPort',
  protocol: 'backendProtocol',
  forwardType: 'forwardType',
  rewriteConfig: 'rewriteConfig',
  redirectConfig: 'redirectConfig',
};

const statusCodeEnum = ['301', '302', '303', '307', '308'];

const TransmitRow = (props) => {
  const {
    port,
    regionId,
    target,
    protocal,
    value = [],
    applications = [],
    onChange,
    refreshApps,
    baseContainer,
    index: parentIndex,
    loadBalancerVersion = '',
  } = props;

  const rewriteField = Field.useField();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [rewritePolicy, setRewritePolicy] = useState({ index: 0, record: {} });
  const [rewriteConfigValidate, setRewriteConfigValidate] = useState(true);

  useEffect(() => {
    let _value = [];

    if (!isEmpty(value)) {
      _value = map(value, (item) => ({
        ...item,
        port: port,
      }));
    }
    onChange(_value);
  }, [port]);

  useEffect(() => {
    if (target === Gateway.alb && protocal === Protocals.https && loadBalancerVersion === 'Basic') {
      let _newValue = [...value];
      _newValue = map(_newValue, (item) => {
        if (has(item, 'rewriteConfig')) {
          delete item.rewriteConfig;
        }
        return {
          ...item,
          backendProtocol: item?.backendProtocol === 'https' ? 'http' : item?.backendProtocol,
        };
      });
      onChange(_newValue);
    }
  }, [loadBalancerVersion]);

  const handleChange = async (index, key, _value) => {
    // @ts-ignore
    let _newValue = [...value];
    if (key === fields.forwardType) {
      if (_value === 'forwardTo') {
        _newValue[index] = {
          domain: _newValue[index]?.domain || '',
          port: _newValue[index]?.port || '',
          path: _newValue[index]?.path || '',
          forwardType: _value,
          appId: '',
          containerPort: '',
        };
      } else {
        _newValue[index] = {
          domain: _newValue[index]?.domain || '',
          port: _newValue[index]?.port || '',
          path: _newValue[index]?.path || '',
          forwardType: _value,
          redirectConfig: {
            protocol: '${protocol}',
            host: '${host}',
            port: '${port}',
            path: '${path}',
            query: '${query}',
            httpCode: '301',
          },
          isExpand: false,
        };
      }
    } else {
      _newValue[index][key] = _value;
      if (key === fields.domain) {
        _newValue = map(_newValue, (item) => ({
          ...item,
          domain: _value,
        }));
      }
    }
    onChange(_newValue);
  };

  const handleMoved = async (index, direction) => {
    const _value = [...value];
    const _newValue = _value.filter((i, idx) => idx !== index);
    const _item = _value[index];
    _newValue.splice(direction === 'up' ? index - 1 : index + 1, 0, _item);
    onChange(_newValue);
  };

  const handleRewritePolicy = (index, record) => {
    const defaultRewritePolicy = {
      host: '${host}',
      path: '${path}',
      query: '${query}',
    };

    if (!has(record, 'rewriteConfig') || isEmpty(record.rewriteConfig)) {
      Reflect.set(record, 'rewriteConfig', defaultRewritePolicy);
    }
    setRewritePolicy({ index, record });
    setVisible(true);
  };

  const okRewritePolicy = () => {
    const { index, record = {} as any } = rewritePolicy;
    if (!isEmpty(record?.rewriteConfig) && !handleValidateRewrite(record.rewriteConfig)) {
      return;
    }
    handleChange(index, fields.rewriteConfig, record.rewriteConfig);
    setVisible(false);
  };

  const cancelRewritePolicy = () => {
    setVisible(false);
    setRewritePolicy({ index: 0, record: {} });
    setRewriteConfigValidate(true);
    const { index, record = {} as any } = rewritePolicy;
    if (!isEmpty(record?.rewriteConfig) && !handleValidateRewrite(record.rewriteConfig)) {
      Reflect.set(record, 'rewriteConfig', {});
    }
  };

  const handleValidateRewrite = (config) => {
    const { host, path, query } = config;
    let validateFlag = true;
    if (
      (!host && !path && !query) ||
      (host === '${host}' && path === '${path}' && query === '${query}')
    ) {
      validateFlag = false;
    } else {
      validateFlag = true;
    }
    setRewriteConfigValidate(validateFlag);
    return validateFlag;
  };

  const handleRedirectConfig = (index, key, _value) => {
    let curRuleInfo = value[index];
    set(curRuleInfo, key, _value);
    handleChange(index, fields.redirectConfig, curRuleInfo.redirectConfig);
  };

  const handleMenuProps = {
    footer: (
      <div style={{ padding: '0 4px', textAlign: 'left', borderTop: '1px solid #eee' }}>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={() => {
            const uri =
              baseContainer === 'v2'
                ? `${CachedData.confLink('feature:saenext:url')}/${regionId}/create-app/micro`
                : `${CachedData.confLink('feature:sae:url')}/overview#/AppList/CreateApp?regionId=${regionId}`;
            window.open(uri, '_blank');
          }}
        >
          <Icon size="xs" type="external_link" />
          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.TreeTransmitRows.CreateAnApplication')}
          </span>
        </LinkButton>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={async () => {
            setLoading(true);
            refreshApps && (await refreshApps());
            setLoading(false);
          }}
        >
          {loading ? <Icon size="xs" type="loading" /> : <Icon size="xs" type="refresh" />}

          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.TreeTransmitRows.Refresh')}
          </span>
        </LinkButton>
      </div>
    ),
  };

  const hasRewrite = target === Gateway.alb;
  const hasProtocol = target === Gateway.alb && protocal === Protocals.https;

  return (
    <>
      {map(value, (item, index) => (
        <>
          {(index === 0 || item?.forwardType !== value[index - 1]?.forwardType) && (
            <Row style={{ color: '#555', lineHeight: '32px' }}>
              <Col span={4}>
                {index === 0 && intl('saenext.components.route-create.TreeTransmitRows.DomainName')}
              </Col>
              <Col span={3} style={{ paddingLeft: 10 }}>
                {intl('saenext.components.route-create.TreeTransmitRows.AccessPort')}
              </Col>
              <Col span={4} style={{ paddingLeft: 10 }}>
                Path
              </Col>
              <Col span={3}></Col>
              {!(target === Gateway.alb && get(item, fields.forwardType) === 'redirect') && (
                <>
                  {hasProtocol ? (
                    <Col span={3} style={{ paddingLeft: 10 }}>
                      {intl('saenext.components.route-create.TreeTransmitRows.BackendProtocol')}
                    </Col>
                  ) : null}
                  <Col span={4} style={{ paddingLeft: 10 }}>
                    {intl('saenext.components.route-create.TreeTransmitRows.BackendApplications')}
                  </Col>
                  <Col span={3} style={{ paddingLeft: 10 }}>
                    {intl('saenext.components.route-create.TreeTransmitRows.ContainerPort')}
                  </Col>
                  <Col span={hasRewrite ? 4 : 3}></Col>
                </>
              )}
              {target === Gateway.alb && get(item, fields.forwardType) === 'redirect' && (
                <>
                  <Col span={hasProtocol ? 3 : 4} style={{ paddingLeft: 10 }}>
                    {intl('saenext.components.route-create.TransmitRow.Agreement')}
                  </Col>
                  <Col span={hasProtocol ? 4 : 3} style={{ paddingLeft: 10 }}>
                    {intl('saenext.components.route-create.TransmitRow.DomainName')}
                  </Col>
                  <Col span={3} style={{ paddingLeft: 10 }}>
                    {intl('saenext.components.route-create.TransmitRow.AccessPort')}
                  </Col>
                  {item?.isExpand && (
                    <>
                      <Col span={3} style={{ paddingLeft: 10 }}>
                        Path
                      </Col>
                      <Col span={3} style={{ paddingLeft: 10 }}>
                        {intl('saenext.components.route-create.TransmitRow.Query')}
                      </Col>
                      <Col span={3} style={{ paddingLeft: 10 }}>
                        {intl('saenext.components.route-create.TransmitRow.StatusCode')}
                      </Col>
                    </>
                  )}
                  <Col span={3}></Col>
                </>
              )}
            </Row>
          )}
          <Row key={`${parentIndex}-${index}`} style={{ marginBottom: 10 }}>
            {/* 域名 */}
            <Col span={4}>
              {!index ? (
                <Input
                  style={{ width: '100%' }}
                  value={get(item, fields.domain)}
                  onChange={(val) => handleChange(index, fields.domain, val)}
                  placeholder={
                    target === Gateway.alb
                      ? intl(
                          'saenext.components.route-create.TreeTransmitRows.AllDomainNamesTakeEffect',
                        )
                      : intl('saenext.components.route-create.TreeTransmitRows.ForExampleWwwAbcCom')
                  }
                />
              ) : null}
            </Col>
            {/* 访问端口 */}
            <Col span={3} style={{ paddingLeft: 10 }}>
              <Input
                disabled
                style={{ width: '100%' }}
                value={get(item, fields.port, port)}
                onChange={(val) => handleChange(index, fields.port, val)}
                placeholder={intl('saenext.components.route-create.TreeTransmitRows.AccessPort')}
              />
            </Col>
            {/* Path */}
            <Col span={4} style={{ paddingLeft: 10 }}>
              <Input
                style={{ width: '100%' }}
                value={get(item, fields.path, '/')}
                onChange={(val) => handleChange(index, fields.path, val)}
                placeholder={intl('saenext.components.route-create.TreeTransmitRows.AccessPath')}
              />
            </Col>
            <Col span={3} style={{ paddingLeft: 10, lineHeight: '32px' }}>
              <Select
                style={{ width: '100%' }}
                value={get(item, fields.forwardType, 'forwardTo')}
                onChange={(val) => handleChange(index, fields.forwardType, val)}
                disabled={target !== Gateway.alb}
              >
                <Select.Option value="forwardTo">
                  {intl('saenext.components.route-create.TransmitRow.Forward')}
                </Select.Option>
                <Select.Option value="redirect">
                  {intl('saenext.components.route-create.TransmitRow.Redirect')}
                </Select.Option>
              </Select>
            </Col>
            {!(target === Gateway.alb && get(item, fields.forwardType) === 'redirect') && (
              <>
                {hasProtocol ? (
                  <Col span={3} style={{ paddingLeft: 10 }}>
                    <Select
                      style={{ width: '100%' }}
                      value={get(item, fields.protocol, 'http')}
                      onChange={(val) => handleChange(index, fields.protocol, val)}
                      placeholder={intl(
                        'saenext.components.route-create.TreeTransmitRows.BackendProtocol',
                      )}
                    >
                      <Select.Option value="http">HTTP</Select.Option>
                      <Select.Option value="https" disabled={loadBalancerVersion === 'Basic'}>
                        <ToolTipCondition
                          show={loadBalancerVersion === 'Basic'}
                          align="r"
                          tip={intl(
                            'saenext.components.route-create.TransmitRow.BasicAlbInstancesDoNot',
                          )}
                        >
                          HTTPS
                        </ToolTipCondition>
                      </Select.Option>
                      <Select.Option value="grpc">GRPC</Select.Option>
                    </Select>
                  </Col>
                ) : null}

                {/* 后端应用 */}
                <Col span={4} style={{ paddingLeft: 10 }}>
                  <Select
                    style={{ width: '100%' }}
                    value={get(item, fields.appId)}
                    onChange={(val) => handleChange(index, fields.appId, val)}
                    menuProps={handleMenuProps}
                    placeholder={intl(
                      'saenext.components.route-create.TreeTransmitRows.SelectAnApplication',
                    )}
                    showSearch
                  >
                    {map(applications, (item) => (
                      <Select.Option value={item.value}>{item?.label}</Select.Option>
                    ))}
                  </Select>
                </Col>
                {/* 容器端口 */}
                <Col span={3} style={{ paddingLeft: 10 }}>
                  <NumberPicker
                    value={get(item, fields.cport)}
                    onChange={(val) => handleChange(index, fields.cport, val)}
                    min={1}
                    max={65535}
                    placeholder={intl(
                      'saenext.components.route-create.TreeTransmitRows.ContainerPort',
                    )}
                    style={{ width: '100%' }}
                  />
                </Col>
                <Col span={hasRewrite ? 4 : 3} style={{ paddingLeft: 10, lineHeight: '32px' }}>
                  {hasRewrite ? (
                    <ToolTipCondition
                      show={loadBalancerVersion === 'Basic'}
                      align="t"
                      tip={intl(
                        'saenext.components.route-create.TransmitRow.BasicAlbInstancesDoNot.1',
                      )}
                    >
                      <LinkButton
                        style={{ width: 50, marginLeft: 10 }}
                        onClick={() => handleRewritePolicy(index, item)}
                        disabled={loadBalancerVersion === 'Basic'}
                      >
                        {intl('saenext.components.route-create.TreeTransmitRows.RewritePolicy')}
                      </LinkButton>
                    </ToolTipCondition>
                  ) : null}

                  <Button
                    text
                    style={{ width: 20 }}
                    disabled={parseInt(index, 10) === 0}
                    onClick={() => {
                      handleMoved(index, 'up');
                    }}
                  >
                    <div>
                      <Icon
                        size="small"
                        style={{ marginLeft: 5 }}
                        type={baseContainer === 'v2' ? 'arrow__up' : 'long-arrow-up'}
                      />
                    </div>
                  </Button>
                  <Button
                    text
                    style={{ width: 20 }}
                    disabled={parseInt(index, 10) === value.length - 1}
                    onClick={() => {
                      handleMoved(index, 'down');
                    }}
                  >
                    <div>
                      <Icon
                        size="small"
                        style={{ marginLeft: 5 }}
                        type={baseContainer === 'v2' ? 'arrow__down' : 'long-arrow-down'}
                      />
                    </div>
                  </Button>
                  <Button
                    text
                    style={{ width: 20 }}
                    onClick={() => {
                      props.onChange(props.value.filter((i, idx) => idx !== index));
                    }}
                  >
                    <div>
                      <Icon
                        className="rows-delete"
                        size="small"
                        style={{ marginLeft: 5 }}
                        type="delete"
                      />
                    </div>
                  </Button>
                </Col>
              </>
            )}
            {target === Gateway.alb && get(item, fields.forwardType) === 'redirect' && (
              <>
                <Col span={hasProtocol ? 3 : 4} style={{ paddingLeft: 10 }}>
                  <Select
                    style={{ width: '100%' }}
                    value={get(item, 'redirectConfig.protocol')}
                    onChange={(val) => handleRedirectConfig(index, 'redirectConfig.protocol', val)}
                  >
                    <Select.Option value="${protocol}">{'${protocol}'}</Select.Option>
                    {!hasProtocol && <Select.Option value="http">HTTP</Select.Option>}
                    <Select.Option value="https">HTTPS</Select.Option>
                  </Select>
                </Col>
                <Col span={hasProtocol ? 4 : 3} style={{ paddingLeft: 10 }}>
                  <Input
                    style={{ width: '100%' }}
                    value={get(item, 'redirectConfig.host')}
                    onChange={(val) => handleRedirectConfig(index, 'redirectConfig.host', val)}
                  />
                </Col>
                <Col span={3} style={{ paddingLeft: 10 }}>
                  <Input
                    style={{ width: '100%' }}
                    value={get(item, 'redirectConfig.port')}
                    onChange={(val) => handleRedirectConfig(index, 'redirectConfig.port', val)}
                  />
                </Col>
                {item?.isExpand && (
                  <>
                    <Col span={3} style={{ paddingLeft: 10 }}>
                      <Input
                        style={{ width: '100%' }}
                        value={get(item, 'redirectConfig.path')}
                        onChange={(val) => handleRedirectConfig(index, 'redirectConfig.path', val)}
                      />
                    </Col>
                    <Col span={3} style={{ paddingLeft: 10 }}>
                      <Input
                        style={{ width: '100%' }}
                        value={get(item, 'redirectConfig.query')}
                        onChange={(val) => handleRedirectConfig(index, 'redirectConfig.query', val)}
                      />
                    </Col>
                    <Col span={3} style={{ paddingLeft: 10 }}>
                      <Select
                        style={{ width: '100%' }}
                        value={get(item, 'redirectConfig.httpCode')}
                        onChange={(val) =>
                          handleRedirectConfig(index, 'redirectConfig.httpCode', val)
                        }
                        dataSource={statusCodeEnum}
                      />
                    </Col>
                  </>
                )}
                <Col span={3} style={{ paddingLeft: 10, lineHeight: '32px' }}>
                  <LinkButton
                    onClick={() => {
                      handleChange(index, 'isExpand', !item?.isExpand);
                    }}
                  >
                    <div>
                      <Icon
                        size="small"
                        style={{ marginLeft: 5 }}
                        type={item?.isExpand ? 'double_left' : 'double_right'}
                      />

                      <span>
                        {item?.isExpand
                          ? intl('saenext.components.route-create.TransmitRow.PutItAway')
                          : intl('saenext.components.route-create.TransmitRow.Expand')}
                      </span>
                    </div>
                  </LinkButton>
                  <Button
                    text
                    style={{ width: 20 }}
                    disabled={parseInt(index, 10) === 0}
                    onClick={() => {
                      handleMoved(index, 'up');
                    }}
                  >
                    <div>
                      <Icon
                        size="small"
                        style={{ marginLeft: 5 }}
                        type={baseContainer === 'v2' ? 'arrow__up' : 'long-arrow-up'}
                      />
                    </div>
                  </Button>
                  <Button
                    text
                    style={{ width: 20 }}
                    disabled={parseInt(index, 10) === value.length - 1}
                    onClick={() => {
                      handleMoved(index, 'down');
                    }}
                  >
                    <div>
                      <Icon
                        size="small"
                        style={{ marginLeft: 5 }}
                        type={baseContainer === 'v2' ? 'arrow__down' : 'long-arrow-down'}
                      />
                    </div>
                  </Button>
                  <Button
                    text
                    style={{ width: 20 }}
                    onClick={() => {
                      props.onChange(props.value.filter((i, idx) => idx !== index));
                    }}
                  >
                    <div>
                      <Icon
                        className="rows-delete"
                        size="small"
                        style={{ marginLeft: 5 }}
                        type="delete"
                      />
                    </div>
                  </Button>
                </Col>
              </>
            )}
          </Row>
        </>
      ))}

      {value?.length > 0 ? (
        <Row
          key="plus"
          style={{ paddingBottom: 8, marginBottom: 16, borderBottom: '1px solid #c0c6cc' }}
        >
          <Col span={4}></Col>
          <Col span={3} style={{ paddingLeft: 10 }}>
            <LinkButton
              onClick={() => {
                const _value = head(value);
                const domain = get(_value, fields.domain, '');
                const newValue = {
                  domain,
                  port,
                  path: '/',
                  forwardType: 'forwardTo',
                  appId: '',
                  containerPort: '',
                };
                if (hasProtocol) {
                  newValue[fields.protocol] = 'http';
                }
                props.onChange([...value, newValue]);
              }}
            >
              <Icon type="plus" size="xs" />
              <span style={{ marginLeft: 5 }}>
                {intl('saenext.components.route-create.TreeTransmitRows.AddRule')}
              </span>
            </LinkButton>
          </Col>
        </Row>
      ) : null}

      <Dialog
        title={intl('saenext.components.route-create.TreeTransmitRows.RewritePolicy')}
        visible={visible}
        footerAlign="right"
        onOk={okRewritePolicy}
        onCancel={cancelRewritePolicy}
        onClose={cancelRewritePolicy}
        style={{ width: 480 }}
      >
        <Form field={rewriteField} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item label={intl('saenext.components.route-create.TransmitRow.DomainName')}>
            <Input
              value={get(rewritePolicy, 'record.rewriteConfig.host')}
              onChange={(val) => {
                const { record } = rewritePolicy;
                const newRewriteConfig = {
                  // @ts-ignore
                  ...record.rewriteConfig,
                  host: val,
                };
                const newRecord = { ...record, rewriteConfig: newRewriteConfig };
                setRewritePolicy({ ...rewritePolicy, record: newRecord });
                handleValidateRewrite(newRewriteConfig);
              }}
            />
          </Form.Item>
          <Form.Item label={intl('saenext.components.route-create.TransmitRow.Path')}>
            <Input
              value={get(rewritePolicy, 'record.rewriteConfig.path')}
              onChange={(val) => {
                const { record } = rewritePolicy;
                const newRewriteConfig = {
                  // @ts-ignore
                  ...record.rewriteConfig,
                  path: val,
                };
                const newRecord = { ...record, rewriteConfig: newRewriteConfig };
                setRewritePolicy({ ...rewritePolicy, record: newRecord });
                handleValidateRewrite(newRewriteConfig);
              }}
            />
          </Form.Item>
          <Form.Item label={intl('saenext.components.route-create.TransmitRow.Query')}>
            <Input
              value={get(rewritePolicy, 'record.rewriteConfig.query')}
              onChange={(val) => {
                const { record } = rewritePolicy;
                const newRewriteConfig = {
                  // @ts-ignore
                  ...record.rewriteConfig,
                  query: val,
                };
                const newRecord = { ...record, rewriteConfig: newRewriteConfig };
                setRewritePolicy({ ...rewritePolicy, record: newRecord });
                handleValidateRewrite(newRewriteConfig);
              }}
            />
          </Form.Item>
        </Form>
        {!rewriteConfigValidate && (
          <div style={{ color: 'red' }}>
            {intl('saenext.components.route-create.TransmitRow.TheDomainNamePathAnd')}
          </div>
        )}
      </Dialog>
    </>
  );
};

export default TransmitRow;
