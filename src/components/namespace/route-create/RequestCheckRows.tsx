import { intl } from '@ali/cnd';
import React from 'react';
import { Button, LinkButton, Grid, Input, Select, Icon } from '@ali/cnd';
import RegularTest from './RegularTest';
import { get } from 'lodash';
import { MatchRules } from './config';

const { Row, Col } = Grid;
export enum Params {
  header = 'header',
  query = 'query',
}

export default (props) => {
  const { paramType = Params.header, onChange } = props;

  const handleChange = (key, value, index, reg?) => {
    onChange &&
      onChange(
        get(props, 'value', []).map((i, idx) => {
          if (idx === index) {
            if (i.Type === 'ERGULAR' && !reg) {
              return {
                ...i,
                [key]: value,
                RegCheck: reg,
              };
            } else {
              if (i.hasOwnProperty('RegCheck')) {
                delete i.RegCheck;
              }
              return {
                ...i,
                [key]: value,
              };
            }
          }
          return i;
        }),
      );
  };

  return (
    <>
      {get(props, 'value', []).map((item, index) => (
        <Row key={index} style={{ marginTop: 10 }}>
          <Col span={7} style={{ paddingRight: 10 }}>
            <Input
              style={{ width: '100%' }}
              value={item?.Key}
              placeholder={
                paramType === Params.header
                  ? intl('saenext.components.route-create.RequestCheckRows.RequestHeader')
                  : intl('saenext.components.route-create.RequestCheckRows.RequestParameters')
              }
              onChange={(v) => handleChange('Key', v, index)}
              showLimitHint
              maxLength={100}
              onPressEnter={(e) => {
                e.preventDefault();
              }}
            />
          </Col>
          <Col span={6}>
            <Select
              value={item?.Type}
              placeholder={intl(
                'saenext.components.route-create.RequestCheckRows.MatchingCondition',
              )}
              dataSource={MatchRules}
              style={{ width: '100%' }}
              onChange={(v) => handleChange('Type', v, index)}
            />
          </Col>
          <Col span={11} style={{ paddingLeft: 10, display: 'flex' }}>
            <div style={{ width: 'calc(100% - 22px)' }}>
              <RegularTest
                showLimitHint
                maxLength={8000}
                value={item?.Value}
                onChange={(v, reg) => handleChange('Value', v, index, reg)}
                placeholder={intl('saenext.components.route-create.RequestCheckRows.Value')}
                hasPadding={false}
                inputType={item.Type === 'ERGULAR' ? 'reg' : 'normal'}
                style={{ width: '100%' }}
              />
            </div>
            <div style={{ width: 20, paddingTop: 6 }}>
              <Button
                text
                onClick={() => {
                  props.onChange(props.value.filter((i, idx) => idx !== index));
                }}
              >
                <Icon
                  className="rows-delete"
                  size="medium"
                  style={{ marginLeft: 5 }}
                  type="delete"
                />
              </Button>
            </div>
          </Col>
        </Row>
      ))}

      {get(props, 'value', []).length < 10 && (
        <div style={{ marginTop: 10 }}>
          <LinkButton
            onClick={() => {
              props.onChange([...props.value, { Key: '', Value: '', Type: 'PRE' }]);
            }}
          >
            <Icon type="plus" size="xs" />
            <span style={{ marginLeft: 5 }}>
              {intl('saenext.components.route-create.RequestCheckRows.Add')}
            </span>
          </LinkButton>
        </div>
      )}
    </>
  );
};
