import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Select } from '@ali/cnd';
import { SelectProps } from '@alifd/next/types/select';

interface Props extends SelectProps {
  ingressType?: string;
  fromType?: string;
}

export default (props: Props) => {
  const { ...selectProps } = props;
  // @ts-ignore
  const [loading, setLoading] = useState(false);

  const handleItemRender = (item) => {
    let returnContent = <></>;
    if (item?.value === 'Single') {
      returnContent = (
        <div style={{ marginLeft: 10 }}>
          {intl('saenext.components.route-create.SceneSelect.SingleService')}

          {selectProps?.fromType === 'mse' ? (
            intl.html('saenext.components.route-create.mse.SceneSelect.DistributingRequestsToTheOnly')
          ) : (
            <span style={{ color: '#808080' }}>
              {intl('saenext.components.route-create.SceneSelect.DistributingRequestsToTheOnly')}
            </span>
          )}
        </div>
      );
    } else if (item?.value === 'Multiple') {
      returnContent = (
        <div style={{ marginLeft: 10 }}>
          {intl('saenext.components.route-create.SceneSelect.ProportionalMultiService')}

          <span style={{ color: '#808080' }}>
            {intl(
              'saenext.components.route-create.SceneSelect.RequestsAreDistributedProportionallyTo',
            )}
          </span>
        </div>
      );
    } else if (item?.value === 'VersionOriented') {
      returnContent = (
        <div style={{ marginLeft: 10 }}>
          {intl('saenext.components.route-create.SceneSelect.ByLabelLabelRouting')}

          <span style={{ color: '#808080' }}>
            {intl('saenext.components.route-create.SceneSelect.DistributeRequestsToMultipleBack')}
          </span>
        </div>
      );
    } else if (item?.value === 'Mock') {
      returnContent = (
        <div style={{ marginLeft: 10 }}>
          Mock &nbsp;&nbsp;
          <span style={{ color: '#808080' }}>
            {intl('saenext.components.route-create.SceneSelect.UseSpecificContentAsThe')}
          </span>
        </div>
      );
    } else if (item?.value === 'Redirect') {
      returnContent = (
        <div style={{ marginLeft: 10 }}>
          {intl('saenext.components.route-create.SceneSelect.Redirect')}

          <span style={{ color: '#808080' }}>
            {intl('saenext.components.route-create.SceneSelect.RedirectTheRequestToAnother')}
          </span>
        </div>
      );
    }
    return returnContent;
  };

  return (
    <div style={{ width: '100%' }}>
      {/* @ts-ignore */}
      <Select
        dataSource={[
          {
            label: (
              <div style={{ fontWeight: 'bold', color: '#333' }}>
                {intl('saenext.components.route-create.SceneSelect.BasicScenario')}
              </div>
            ),

            children: [
              {
                label: intl('saenext.components.route-create.SceneSelect.SingleService'),
                value: 'Single',
              },
            ],
          },
          {
            label: (
              <div style={{ fontWeight: 'bold', color: '#333' }}>
                {intl('saenext.components.route-create.SceneSelect.GrayscaleScene')}
              </div>
            ),
            children:
              props?.ingressType === 'apig'
                ? [
                    {
                      label: intl(
                        'saenext.components.route-create.SceneSelect.ProportionalMultiService',
                      ),
                      value: 'Multiple',
                    },
                  ]
                : [
                    {
                      label: intl(
                        'saenext.components.route-create.SceneSelect.ProportionalMultiService',
                      ),
                      value: 'Multiple',
                    },
                    {
                      label: intl(
                        'saenext.components.route-create.SceneSelect.ByLabelLabelRouting',
                      ),
                      value: 'VersionOriented',
                    },
                  ],
          },
        ]}
        itemRender={handleItemRender}
        {...selectProps}
        state={loading ? 'loading' : null}
        style={{
          width: '100%',
          ...selectProps?.style,
        }}
      />
    </div>
  );
};
