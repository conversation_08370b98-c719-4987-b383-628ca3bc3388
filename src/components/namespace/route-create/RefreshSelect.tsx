import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Select, Button, Icon, LinkButton, Balloon, Checkbox, Input } from '@ali/cnd';
import ExternalLink from '../../shared/ExternalLink';
import { SelectProps } from '@alifd/next/types/select';
import { get, map, isEmpty } from 'lodash';

interface Props extends SelectProps {
  external?: {
    label: string;
    url: string;
    width?: number;
  };
  autoSearch?: {
    enabled: boolean;
    placeholder: string;
  };
  fetchMethod?: (params?: any) => Promise<any>;
}

export default (props: Props) => {
  const { autoSearch = {}, external, fetchMethod, ...selectProps } = props;
  const _autoSearch = get(autoSearch, 'enabled', false);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [search, setSearch] = useState('');

  const loadDataSource = async () => {
    setLoading(true);
    const _dataSource = await fetchMethod(search);
    setDataSource(_dataSource);
    setLoading(false);
  };

  const handleMenuProps = {
    header: _autoSearch ? (
      <div style={{ display: 'flex', padding: '4px 16px 8px', borderBottom: '1px solid #eee' }}>
        <Input
          hasClear={true}
          value={search}
          // @ts-ignore
          placeholder={autoSearch.placeholder}
          style={{ width: '100%' }}
          onChange={async (val:string) => {
            if (search && isEmpty(val)) {
              setSearch('');
              setLoading(true);
              const _dataSource = await fetchMethod();
              setDataSource(_dataSource);
              setLoading(false);
            }
            setSearch(val);
          }}
          onPressEnter={loadDataSource}
        />

        <Button
          onClick={loadDataSource}
          style={{
            borderLeft: 'none',
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0,
          }}
        >
          <Icon type="search" />
        </Button>
      </div>
    ) : null,
    footer: (
      <div style={{ padding: '0px 16px', borderTop: '1px solid #eee' }}>
        <LinkButton
          type="primary"
          onClick={() => {
            window.open(external.url, '_blank');
          }}
        >
          <Icon size="xs" type="external_link" />
          <span style={{ marginLeft: 2 }}>{external.label}</span>
        </LinkButton>
        <LinkButton style={{ marginLeft: 10 }} onClick={loadDataSource}>
          {loading ? <Icon size="xs" type="loading" /> : <Icon size="xs" type="refresh" />}

          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.RefreshSelect.Refresh')}
          </span>
        </LinkButton>
        <LinkButton
          type="primary"
          onClick={() => {
            setVisible(false);
          }}
          style={{ float: 'right' }}
        >
          {intl('saenext.components.route-create.RefreshSelect.Close')}
        </LinkButton>
      </div>
    ),
  };

  const handleItemRender = (item) => {
    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {/* @ts-ignore */}
        <Checkbox checked={selectProps.value?.indexOf(item.value) > -1} disabled={item.disabled} />
        {item.tip ? (
          <Balloon.Tooltip align="r" trigger={item.label}>
            {item?.tip}
          </Balloon.Tooltip>
        ) : (
          <span>{item.label}</span>
        )}
      </div>
    );
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
      {get(selectProps, 'mode', 'single') === 'single' ? (
        <Select
          dataSource={dataSource}
          className="refresh-select"
          // @ts-ignore
          menuProps={handleMenuProps}
          {...selectProps}
          // @ts-ignore
          state={loading ? 'loading' : null}
          style={{
            flex: 1,
            ...selectProps?.style,
          }}
          visible={visible}
          // @ts-ignore
          popupAutoFocus={_autoSearch}
          onVisibleChange={(val) => {
            setVisible(val);
            // !val && setSearch('');
          }}
        />
      ) : (
        <Select
          dataSource={dataSource}
          mode="multiple"
          className="refresh-select"
          itemRender={handleItemRender}
          // @ts-ignore
          menuProps={handleMenuProps}
          popupClassName="select-popup-checkbox"
          {...selectProps}
          // @ts-ignore
          state={loading ? 'loading' : null}
          style={{
            flex: 1,
            ...selectProps?.style,
          }}
          visible={visible}
          // @ts-ignore
          popupAutoFocus={_autoSearch}
          onVisibleChange={(val) => {
            setVisible(val);
            // !val && setSearch('');
          }}
        />
      )}

      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: external.width || 130,
        }}
      >
        {/* <TextRefreshButton onClick={loadDataSource} /> */}
        <Button
          onClick={loadDataSource}
          style={{
            borderLeft: 'none',
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0,
          }}
        >
          {loading ? <Icon type="loading" /> : <Icon type="refresh" />}
        </Button>
        <ExternalLink url={external.url} label={external.label} />
      </div>
    </div>
  );
};
