import React, { useState } from 'react';
import { Select, Balloon, Checkbox } from '@ali/cnd';
import { SelectProps } from '@alifd/next/types/select';
import { get, map } from 'lodash';


interface Props extends SelectProps {
  hasSelectAll?: string,
}

interface DataSourceItem {
  value: string;
  label: string;
  tip?: string;
}

export default (props: Props) => {
  const { ...selectProps } = props;
  const [visible, setVisible] = useState(false);


  const handleItemRender = (item) => {
    return (
      <div>
        {/* @ts-ignore */}
        <Checkbox checked={selectProps.value?.indexOf(item.value) > -1} />
        {item.label}
      </div>
    );
  };

  return (
    <>
      {/* @ts-ignore */}
      <Select
        mode='multiple'
        itemRender={handleItemRender}
        {...selectProps}
        // @ts-ignore
        state={null}
        popupClassName="select-popup-checkbox"
        style={{
          width: '100%',
          ...selectProps?.style
        }}
        visible={visible}
        onVisibleChange={val => setVisible(val)}
      >
        {
          map(get(selectProps, 'dataSource', []), (item: DataSourceItem) => (
            <Select.Option key={item.value} value={item.value}>
              {
                item.tip ? (
                  <Balloon.Tooltip align="r" trigger={item.label}>
                    {item?.tip}
                  </Balloon.Tooltip>
                ) : (
                  <span>{item.label}</span>
                )
              }
            </Select.Option>
          ))
        }
      </Select>
    </>
  );
};


