import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { head, map, get } from 'lodash';
import { Gateway } from './index';
import { Protocals } from './AlbForwardform';
import { Select, Icon, Grid, Input, NumberPicker, LinkButton, ToolTipCondition } from '@ali/cnd';
import CachedData from '../../../cache/common';

type Props = {
  regionId: string;
  target: string;
  instanceId: string;
  port: number | string;
  baseContainer: string;
  protocal: Protocals;
  onChange: (val: any) => void;
  value: any[];
  loadBalancers: { label: string; value: string; [key: string]: any }[];
  applications: { label: string; value: string; [key: string]: any }[];
  refreshApps: () => Promise<any>;
  loadBalancerVersion?: string;
};
const { Row, Col } = Grid;

const fields = {
  ip: 'ip',
  port: 'port',
  appId: 'defaultAppId',
  cport: 'defaultPort',
  protocol: 'defaultBackendProtocol',
};

export default (props: Props) => {
  const {
    target,
    regionId,
    instanceId,
    port,
    baseContainer = 'v2',
    protocal = Protocals.http,
    value = [],
    applications = [],
    loadBalancers = [],
    onChange,
    refreshApps,
    loadBalancerVersion = '',
  } = props;
  const _value = head(value) || {};
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    onChange([
      {
        ..._value,
        ip: instanceId || '',
        port: port || '',
      },
    ]);
  }, [instanceId, port]);

  const handleChange = async (key, _value) => {
    // @ts-ignore
    const _newValue = head(value) || {};
    _newValue[key] = _value;
    onChange([_newValue]);
  };

  const handleMenuProps = {
    footer: (
      <div style={{ padding: '0 4px', textAlign: 'left', borderTop: '1px solid #eee' }}>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={() => {
            const uri =
              baseContainer === 'v2'
                ? `${CachedData.confLink('feature:saenext:url')}/${regionId}/create-app/micro`
                : `${CachedData.confLink('feature:sae:url')}/overview#/AppList/CreateApp?regionId=${regionId}`;
            window.open(uri, '_blank');
          }}
        >
          <Icon size="xs" type="external_link" />
          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.InitTransmitRows.CreateAnApplication')}
          </span>
        </LinkButton>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={async () => {
            setLoading(true);
            refreshApps && (await refreshApps());
            setLoading(false);
          }}
        >
          {loading ? <Icon size="xs" type="loading" /> : <Icon size="xs" type="refresh" />}

          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.InitTransmitRows.Refresh')}
          </span>
        </LinkButton>
      </div>
    ),
  };

  const hasProtocol = target === Gateway.alb && protocal === Protocals.https;

  return (
    <>
      <span className="mb-s" style={{ display: 'inline-block', fontSize: 12, color: '#888' }}>
        {intl(
          'saenext.components.route-create.InitTransmitRows.RecommendSpecifyTheDefaultForwarding',
        )}
      </span>
      <Row style={{ color: '#555', lineHeight: '32px' }}>
        <Col span={hasProtocol ? 5 : 7}>
          {target === Gateway.clb
            ? intl('saenext.components.route-create.InitTransmitRows.ClbInstance')
            : intl('saenext.components.route-create.InitTransmitRows.AlbInstance')}
        </Col>
        <Col span="4" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.InitTransmitRows.AccessPort')}
        </Col>
        <Col span="2" style={{ paddingLeft: 10 }}></Col>
        {hasProtocol ? (
          <Col span="4" style={{ paddingLeft: 10 }}>
            {intl('saenext.components.route-create.InitTransmitRows.BackendProtocol')}
          </Col>
        ) : null}

        <Col span={hasProtocol ? 5 : 7} style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.InitTransmitRows.BackendApplications')}
        </Col>
        <Col span="4" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.InitTransmitRows.ContainerPort')}
        </Col>
      </Row>
      <Row>
        <Col span={hasProtocol ? 5 : 7}>
          <Select
            disabled
            style={{ width: '100%' }}
            value={get(_value, fields.ip)}
            onChange={(val) => handleChange(fields.ip, val)}
            placeholder={
              target === Gateway.clb
                ? intl('saenext.components.route-create.InitTransmitRows.ClbInstance')
                : intl('saenext.components.route-create.InitTransmitRows.AlbInstance')
            }
          >
            {map(loadBalancers, (item) => (
              <Select.Option value={item.value}>{item?.label}</Select.Option>
            ))}
          </Select>
        </Col>
        <Col span={4} style={{ paddingLeft: 10 }}>
          <Input
            disabled
            style={{ width: '100%' }}
            value={get(_value, fields.port)}
            onChange={(val) => handleChange(fields.port, val)}
            placeholder={intl('saenext.components.route-create.InitTransmitRows.AccessPort')}
          />
        </Col>
        <Col span="2" style={{ paddingLeft: 10, textAlign: 'center', lineHeight: '32px' }}>
          {intl('saenext.components.route-create.InitTransmitRows.Forward')}
        </Col>
        {hasProtocol ? (
          <Col span={4} style={{ paddingLeft: 10 }}>
            <Select
              style={{ width: '100%' }}
              value={get(_value, fields.protocol, 'http')}
              onChange={(val) => handleChange(fields.protocol, val)}
              placeholder={intl('saenext.components.route-create.InitTransmitRows.BackendProtocol')}
            >
              <Select.Option value="http">HTTP</Select.Option>
              <Select.Option value="https" disabled={loadBalancerVersion === 'Basic'}>
                <ToolTipCondition
                  show={loadBalancerVersion === 'Basic'}
                  align="r"
                  tip={intl(
                    'saenext.components.route-create.InitTransmitRows.BasicAlbInstancesDoNot',
                  )}
                >
                  HTTPS
                </ToolTipCondition>
              </Select.Option>
              <Select.Option value="grpc">GRPC</Select.Option>
            </Select>
          </Col>
        ) : null}

        <Col span={hasProtocol ? 5 : 7} style={{ paddingLeft: 10 }}>
          <Select
            style={{ width: '100%' }}
            value={get(_value, fields.appId)}
            onChange={(val) => handleChange(fields.appId, val)}
            menuProps={handleMenuProps}
            placeholder={intl(
              'saenext.components.route-create.InitTransmitRows.SelectABackendApplication',
            )}
            showSearch
          >
            {map(applications, (item) => (
              <Select.Option value={item.value}>{item?.label}</Select.Option>
            ))}
          </Select>
        </Col>
        <Col span={4} style={{ paddingLeft: 10 }}>
          <NumberPicker
            style={{ width: '100%' }}
            value={get(_value, fields.cport)}
            onChange={(val) => handleChange(fields.cport, val)}
            min={1}
            max={65535}
            placeholder={intl('saenext.components.route-create.InitTransmitRows.ContainerPort')}
          />
        </Col>
      </Row>
    </>
  );
};
