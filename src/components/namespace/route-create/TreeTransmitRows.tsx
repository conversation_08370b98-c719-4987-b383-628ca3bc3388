import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { map, get, isEmpty, head, has } from 'lodash';
import { Gateway } from './index';
import { Protocals } from './AlbForwardform';
import { Message, Icon, Actions, Button } from '@ali/cnd';
import TransmitRow from './TransmitRow';

const { LinkButton } = Actions;

type Props = {
  regionId: string;
  target: string;
  port: number | string;
  protocal: Protocals;
  onChange: (val: any) => void;
  value: any[];
  baseContainer: string;
  applications: { label: string; value: string;[key: string]: any }[];
  refreshApps: () => Promise<any>;
  loadBalancerVersion?: string;
};

export default (props: Props) => {
  const {
    target,
    port,
    regionId,
    protocal = Protocals.http,
    value = [],
    baseContainer = 'v2',
    applications = [],
    onChange,
    refreshApps,
    loadBalancerVersion = ''
  } = props;

  // [[{}, {}], [], []]

  useEffect(() => {
    let _value = [[{ domain: '', port: port, path: '/', forwardType:'forwardTo', appId: '', containerPort: '' }]];
    if (!isEmpty(value)) {
      // @ts-ignore
      _value = map(value, (item) =>
        map(item, (subItem) => ({
          ...subItem,
          port: port,
        })),
      );
    }
    onChange(_value);
  }, [port]);

  const handleChange = async (index, _value) => {
    let _newValue = [...value];
    if (isEmpty(_value)) {
      // 规则为空删除该条域名
      _newValue.splice(index, 1);
    } else {
      _newValue[index] = _value;
    }
    onChange(_newValue);
  };

  let message = <></>;
  switch (target) {
    case Gateway.clb:
      message = (
        <>
          <div className="text-line">
            {intl(
              'saenext.components.route-create.TreeTransmitRows.DomainNameSpecificationItConsists',
            )}
          </div>
          <div className="text-line">
            {intl('saenext.components.route-create.TreeTransmitRows.PathSpecificationTheLengthIs')}
          </div>
        </>
      );

      break;
    case Gateway.alb:
      message = (
        <>
          <div className="text-line">
            {intl(
              'saenext.components.route-create.TreeTransmitRows.DomainNameSpecificationTheLength',
            )}
          </div>
          <div className="text-line">
            {intl('saenext.components.route-create.TreeTransmitRows.PathSpecificationTheLengthOf')}
          </div>
        </>
      );

      break;
    default:
      message = <></>;
      break;
  }

  return (
    <>
      <Message type="notice" className="mb-s">
        {message}
      </Message>

      {map(value, (item, index) =>
        item.length > 0 ? (
          <TransmitRow
            key={index}
            index={index}
            value={item}
            regionId={regionId}
            onChange={val => handleChange(index, val)}
            port={port}
            target={target}
            protocal={protocal}
            baseContainer={baseContainer}
            applications={applications}
            refreshApps={refreshApps}
            loadBalancerVersion={loadBalancerVersion}
          />
        ) : null,
      )}

      <div style={{ marginTop: 10 }}>
        <Button
          onClick={() => {
            props.onChange([
              ...value,
              [
                {
                  domain: '',
                  port: port,
                  path: '/',
                  forwardType: 'forwardTo',
                  appId: '',
                  containerPort: '',
                },
              ],
            ]);
          }}
        >
          <Icon type="plus" size="xs" />
          <span style={{ marginLeft: 5 }}>
            {intl('saenext.components.route-create.TreeTransmitRows.AddDomainName')}
          </span>
        </Button>
      </div>
    </>
  );
};


