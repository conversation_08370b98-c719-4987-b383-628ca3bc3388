import React, { useState, useEffect, useRef } from 'react';
import { Input, Dialog, Button, Form, Icon, Message } from '@ali/cnd';
import services from "~/services";
import { intl } from '@ali/cnd';
import If from '../../shared/If';

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

let timer = null;
let resTimer = null;

const RegularTest = (props) => {
  // const intl = useIntl();
  const { inputType = 'normal', hasPadding = false, onChange, value, getRegState } = props;
  const [regValue, setRegValue] = useState('');
  const [regTestValue, setRegTestValue] = useState('');
  const [state, setState] = useState('');
  const [stateCode, setStateCode] = useState('');
  const [visible, setVisible] = useState(false);
  const [testResult, setTestResult] = useState([]);
  const [regState, setRegState] = useState('');
  const [regStateCode, setRegStateCode] = useState('');

  const ref = useRef(null);

  const StateCodeEnum = {
    PASS: intl('widget.reg.pass'),
    CHINESE: intl('widget.reg.chinese'),
    ERROR: intl('widget.reg.error'),
  };

  const RegStateCodeEnum = {
    PASS: intl('widget.reg.pass'),
    EMPTY: intl('widget.reg.empty'),
    CHINESE: intl('widget.reg.chinese'),
    ERROR: intl('widget.reg.error'),
  };

  const onChangeInput = (val) => {
    setRegValue(val);
    onChange(val);
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
      handleCheckReg(val, false);
    }, 500);
  };

  const onChangeRegInput = (val) => {
    setRegValue(val);
    setRegTestValue('');
    setTestResult([]);
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
      handleCheckReg(val, true);
    }, 500);
  };

  const handleCheckReg = async (val, isRegTestInput?) => {
    isRegTestInput ? setRegStateCode('') : setStateCode('');
    isRegTestInput ? setRegState('loading') : setState('loading');
    // @ts-ignore
    const { Data = '' } = await services.getRegExpCheck({
      RegExp: val,
    });
    if (Data === 'PASS') {
      isRegTestInput ? setRegState('success') : setState('success');
    } else {
      isRegTestInput ? setRegState('error') : setState('error');
    }
    isRegTestInput ? setRegStateCode(Data) : setStateCode(Data);
  };

  const onShowRegTest = () => {
    if (value) {
      handleCheckReg(value, true);
      setRegValue(value);
    }
    setVisible(true);
    setRegState(state);
    setRegStateCode(stateCode);
  };

  const onChangeRegTestContent = (val) => {
    setRegTestValue(val);
    resTimer && clearTimeout(resTimer);
    resTimer = setTimeout(() => {
      handleTestReg(val);
    }, 500);
  };

  const handleTestReg = async (testValue) => {
    const params = {
      RegExp: regValue,
      Path: testValue,
    };
    // @ts-ignore
    const { Data = [] } = await services.getRegExpTest({ ...params });
    let res = [];
    if (Data.length) {
      const regTestValueArr = testValue.split('\n');
      res = regTestValueArr.map((item, index) => {
        return { content: item, result: Data[index] };
      });
    }
    setTestResult(res);
  };

  const handleClose = () => {
    setVisible(false);
    setRegTestValue('');
    setTestResult([]);
    if (regStateCode === 'PASS') {
      onChange(regValue, true);
    }
  };

  useEffect(() => {
    if (stateCode) {
      stateCode === 'PASS' ? onChange(regValue, true) : onChange(regValue, false);
      getRegState && getRegState(stateCode);
    }
  }, [stateCode]);

  useEffect(() => {
    if (inputType === 'reg') {
      setState('');
      setStateCode('');
      if (value) {
        handleCheckReg(value);
        setRegValue(value);
      }
    }
  }, [inputType]);

  return (
    <React.Fragment>
      <If condition={inputType === 'reg'}>
        <div
          style={{
            display: 'flex',
            paddingTop: stateCode && stateCode !== 'EMPTY' && hasPadding ? 16 : 0,
          }}
        >
          <Input {...props} state={state} onChange={onChangeInput} style={{ flex: 1 }} />
          <div
            style={{
              color: '#0070cc',
              cursor: 'pointer',
              lineHeight: '32px',
              width: 55,
              // width: aliyun_lang === 'zh' ? 55 : 185,
              marginLeft: 10,
            }}
            onClick={onShowRegTest}
            ref={ref}
          >
            {intl('widget.reg.test')}
          </div>
        </div>
        <If condition={stateCode !== 'PASS'}>
          <span style={{ color: '#d93026', marginTop: 4 }}>{StateCodeEnum[stateCode]}</span>
        </If>
      </If>

      <If condition={inputType === 'normal'}>
        <Input {...props} />
      </If>

      <Dialog
        visible={visible}
        title={intl('widget.reg.test')}
        onClose={handleClose}
        style={{ width: 700 }}
        footer={
          <React.Fragment>
            <Button onClick={handleClose}>{intl('saenext.components.RegTest.Close')}</Button>
          </React.Fragment>
        }
      >
        <Form {...formItemLayout}>
          <Message type="notice" style={{ marginBottom: 8 }}>
            {intl.html('widget.reg.message')}
          </Message>
          <FormItem label={intl('widget.reg.test_string')}>
            <Input
              value={regValue}
              onChange={onChangeRegInput}
              style={{ width: '100%' }}
              // @ts-ignore
              state={regState}
            />

            <If condition={regStateCode !== 'PASS'}>
              <span style={{ color: '#d93026', marginTop: 4 }}>
                {RegStateCodeEnum[regStateCode]}
              </span>
            </If>
          </FormItem>
          <FormItem label={intl('widget.reg.test_content')}>
            <Input.TextArea
              placeholder={intl('widget.reg.test_placeholder')}
              style={{ width: '100%' }}
              onChange={onChangeRegTestContent}
              autoHeight={{ minRows: 8, maxRows: 8 }}
              autoFocus={true}
              value={regTestValue}
            />
          </FormItem>
          <FormItem label={intl('widget.reg.test_result')}>
            <div className="reg-result-box">
              {testResult.map((item) => {
                return (
                  <div>
                    <If condition={item.result}>
                      <Icon type="success" style={{ color: '#1DC11D', marginRight: '10px' }} />
                    </If>
                    <If condition={!item.result}>
                      <Icon type="error" style={{ color: '#FF3333', marginRight: '10px' }} />
                    </If>
                    {item.content}
                  </div>
                );
              })}
            </div>
          </FormItem>
        </Form>
      </Dialog>
    </React.Fragment>
  );
};

export default RegularTest;
