.flow-content {
  margin-bottom: 46px;
  .items-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .justify-center {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .next-form-item-label {
    font-weight: 600;
  }
  .next-form-item-control {
    .next-radio-wrapper {
      min-width: 120px;
    }
    .refresh-select {
      .next-select-inner {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
    .rows-delete::before {
      font-size: 16px !important;
    }
  }
}

.fixed-footer {
  position: fixed;
  width: 100%;
  bottom: 0;
  background-color: #fff;
  z-index: 10;
  padding: 10px 0;
  border-top: 1px solid #eee;
  .next-btn.next-medium {
    border-radius: 0 !important;
  }
}

.select-popup-checkbox {
  .next-menu-icon-selected {
    right: 20px;
  }
  .next-menu-symbol-icon-selected {
    display: none;
  }
  .next-menu-selectable-multiple{
    .next-menu-content{
      .next-menu-item{
        padding-left: 24px !important;
      }
    }
  }
  .next-menu {
    padding-bottom: 0 !important;
    .next-menu-footer {
      position: sticky;
      bottom: 0;
      left: 0;
      background-color: #fff;
    }
  }
}

.m {
  margin: 16px;
  &-xs {
    margin: 4px;
  }
  &-s {
    margin: 8px;
  }
  &-l {
    margin: 16px;
  }
  &-xl {
    margin: 24px;
  }
}

.mt {
  margin-top: 16px;
  &-xs {
    margin-top: 4px;
  }
  &-s {
    margin-top: 8px;
  }
  &-l {
    margin-top: 16px;
  }
  &-xl {
    margin-top: 24px;
  }
}

.mb {
  margin-bottom: 16px;
  &-xs {
    margin-bottom: 4px;
  }
  &-s {
    margin-bottom: 8px;
  }
  &-l {
    margin-bottom: 16px;
  }
  &-xl {
    margin-bottom: 24px;
  }
  &-none {
    margin-bottom: 0!important;
  }
}

.ml {
  margin-left: 16px;
  &-xs {
    margin-left: 4px;
  }
  &-s {
    margin-left: 8px;
  }
  &-l {
    margin-left: 16px;
  }
  &-xl {
    margin-left: 24px;
  }
}

.mr {
  margin-right: 16px;
  &-xs {
    margin-right: 4px;
  }
  &-s {
    margin-right: 8px;
  }
  &-l {
    margin-right: 16px;
  }
  &-xl {
    margin-right: 24px;
  }
  &-none {
    margin-right: 0 !important;
  }
}

.p {
  padding: 16px;
  &-xs {
    padding: 4px;
  }
  &-s {
    padding: 8px;
  }
  &-l {
    padding: 16px;
  }
  &-xl {
    padding: 24px;
  }
}

.pt {
  padding-top: 16px;
  &-xs {
    padding-top: 4px;
  }
  &-s {
    padding-top: 8px;
  }
  &-l {
    padding-top: 16px;
  }
  &-xl {
    padding-top: 24px;
  }
}

.pb {
  padding-bottom: 16px;
  &-xs {
    padding-bottom: 4px;
  }
  &-s {
    padding-bottom: 8px;
  }
  &-l {
    padding-bottom: 16px;
  }
  &-xl {
    padding-bottom: 24px;
  }
}

.pl {
  padding-left: 16px;
  &-xs {
    padding-left: 4px;
  }
  &-s {
    padding-left: 8px;
  }
  &-l {
    padding-left: 16px;
  }
  &-xl {
    padding-left: 24px;
  }
}

.pr {
  padding-right: 16px;
  &-xs {
    padding-right: 4px;
  }
  &-s {
    padding-right: 8px;
  }
  &-l {
    padding-right: 16px;
  }
  &-xl {
    padding-right: 24px;
  }
}

.text-line {
  line-height: 1.5;
}
.color-error {
  color: red !important;
}

.color-success {
  color: green;
}

.color-link {
  color: #0070cc;
}
