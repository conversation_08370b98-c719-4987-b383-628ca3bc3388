import { intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import {
  Field,
  Form,
  Radio,
  NumberPicker,
  LinkButton,
  Icon,
  Balloon,
  Checkbox,
  Input,
  CndTag,
  Switch,
} from '@ali/cnd';
import { Gateway } from './index';
import services from "~/services";
import { lowerFirstData } from '../../../utils/transfer-data';
import {
  filter,
  map,
  get,
  head,
  forEach,
  isEmpty,
  isEqual,
  includes,
  has,
  set,
  uniq,
  upperFirst,
  toUpper,
  last,
} from 'lodash';
import RefreshSelect from './RefreshSelect';
import InitTransmitRows from './InitTransmitRows';
import TransmitCheckRows from './TreeTransmitRows';
import TextWithBalloon from '../../shared/TextWithBalloon';
import { isForbidden } from '../../../utils/authUtils';
import { UnAuthedLabel } from '../../shared/unauthedLabel';
import CachedData from '../../../cache/common';
import { confFeature } from '@alicloud/console-one-conf';

const { Group: CheckboxGroup } = Checkbox;

type Props = {
  mode: boolean;
  regionId: string;
  visible: boolean;
  namespaceId: string;
  networkType: string;
  baseContainer: string;
  namespaceResources: {
    VpcId?: string;
    [key: string]: any;
  };
  // init: <T>(name: string, option?: InitOption, props?: {}) => InitResult<T>;
  [key: string]: any;
};

export enum Protocals {
  http = 'HTTP',
  https = 'HTTPS',
}

const XForwardedSource = [
  {
    label: intl('saenext.components.route-create.AlbForwardform.EnableTheXForwardedFor'),
    value: 'EnableXForwardedFor',
    disabled: true,
  },
  {
    label: intl('saenext.components.route-create.AlbForwardform.ObtainTheServerLoadBalancer'),
    value: 'EnableXForwardedForSlbId',
  },
  {
    label: intl('saenext.components.route-create.AlbForwardform.UseTheXForwardedProto'),
    value: 'EnableXForwardedForProto',
  },
  {
    label: intl('saenext.components.route-create.AlbForwardform.UseTheXForwardedPort'),
    value: 'EnableXForwardedForSlbPort',
  },
  {
    label: intl('saenext.components.route-create.AlbForwardform.UseTheXForwardedClient'),
    value: 'EnableXForwardedForClientSrcPort',
  },
];

const AlbVersionMap = {
  Basic: {
    label: intl('saenext.components.route-create.AlbForwardform.BasicEdition'),
    type: 'info',
  },
  Standard: {
    label: intl('saenext.components.route-create.AlbForwardform.StandardEdition'),
    type: 'safe',
  },
  StandardWithWaf: {
    label: intl('saenext.components.route-create.AlbForwardform.StandardWaf'),
    type: 'warning',
  },
};

const XForwardedArr = [
  'EnableXForwardedFor',
  'EnableXForwardedForSlbId',
  'EnableXForwardedForProto',
  'EnableXForwardedForSlbPort',
  'EnableXForwardedForClientSrcPort',
];

const CreateAgentALBMap = {
  Standard: intl('saenext.components.route-create.AlbForwardform.StandardEdition'),
  StandardWithWaf: intl('saenext.components.route-create.AlbForwardform.WafEnhancedEdition'),
};

export default forwardRef((props: Props, ref) => {
  const {
    regionId,
    namespaceId,
    visible,
    networkType,
    baseContainer,
    namespaceResources = {
      VpcId: '',
    },
    mode: disabled,
    // @ts-ignore
    ...childProps
  } = props;
  if (!visible) return null;

  const { VpcId } = namespaceResources;
  const field = Field.useField();
  const { init } = field;
  const loadBalancerIdMap = useRef(new Map());
  const [loadBalancers, setLoadBalancers] = useState([]);
  const [protocal, setProtocal] = useState(Protocals.http);
  const [certificates, setCertificates] = useState([]);
  const [applications, setApplications] = useState<any>([]);
  const [instanceId, setInstanceId] = useState('');
  const [port, setPort] = useState('');
  const [isExpended, setIsExpended] = useState(false);
  const [securityPolicies, setSecurityPolicies] = useState([]);
  const [authedALB, setAuthedALB] = useState(true);
  const [authedSLB, setAuthedSLB] = useState(true);
  const [loadBalancerVersion, setLoadBalancerVersion] = useState(''); // 基础版-Basic 标准版-Standard WAF 增强版-StandardWithWaf
  const [albTimeoutQuota, setAlbTimeoutQuota] = useState(180);
  const [authedVswitch, setAuthedVswitch] = useState(true);
  const [albZones, setAlbZones] = useState([]);
  const [vSwitchList, setVSwitchList] = useState([]);
  const [vSwitchIdMap, setVSwitchIdMap] = useState({});
  const CorsConfigVisible = get(window, 'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS.alb_cors_enable');
  // const albTimeoutMaxIgnore = confFeature('alb_timeoutMax_ignore');

  useEffect(() => {
    setInstanceId('');
    setLoadBalancerVersion('');
    field.setValue('alb', '');
  }, [networkType]);

  useEffect(() => {
    getLoadBalancers();
    !disabled && getRegionZones();
  }, [networkType, VpcId]);

  useEffect(() => {
    !disabled && VpcId && getVswitchs();
  }, [VpcId, JSON.stringify(albZones)]);

  useEffect(() => {
    getAlbTimeoutQuota();
    getApplications();
    getSecurityPolicies();
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      submit,
      setValues,
    }),
    [vSwitchIdMap, networkType],
  );

  useEffect(() => {
    if (instanceId && !isEmpty(loadBalancerIdMap.current?.get(instanceId))) {
      const _loadBalancerVersion =
        loadBalancerIdMap.current?.get(instanceId)?.loadBalancerEdition || '';
      setLoadBalancerVersion(_loadBalancerVersion);
    }
  }, [instanceId, loadBalancerIdMap.current?.get(instanceId)]);

  const submit = () => {
    const promise = new Promise<{ success: boolean; params: any }>(async (resolve) => {
      field.validate((errors, values) => {
        if (errors) {
          resolve({ success: false, params: null });
          return;
        }
        doSubmit(values, resolve);
      });
    });

    return promise;
  };

  const doSubmit = (values, resolve) => {
    const {
      alb,
      port,
      protocalType,
      ssl = [],
      timeout,
      idleTimeout,
      tls,
      ruleList = [],
      defaultRouter = [],
      enableXForwarded = [],
      vSwitch = [],
      CorsConfigEnable = false,
    } = values;

    const _certIds = protocalType === Protocals.https ? ssl.join(',') : '';
    const _policyId = protocalType === Protocals.https ? tls : '';
    let _rules = [];
    forEach(ruleList, (item) => {
      let _domain = '';
      for (let i = 0; i < item.length; i++) {
        const { domain } = item[i];
        if (domain) {
          _domain = domain;
          break;
        }
      }
      let children = map(item, (rule) => {
        let _rule: any = {
          domain: _domain,
          path: rule.path,
          backendProtocol: get(rule, 'backendProtocol', 'http'),
        };
        let ruleActions = [];
        if (rule?.forwardType === 'forwardTo') {
          _rule = {
            ..._rule,
            appId: rule.appId,
            containerPort: rule.containerPort,
          };
          if (has(rule, 'rewriteConfig') && !isEmpty(rule?.rewriteConfig)) {
            ruleActions.push({
              actionType: 'rewrite',
              actionConfig: JSON.stringify(rule?.rewriteConfig),
            });
          }
        }
        if (rule?.forwardType === 'redirect') {
          _rule = {
            ..._rule,
            containerPort: 8080,
          };
          if (has(rule, 'redirectConfig') && !isEmpty(rule?.redirectConfig)) {
            ruleActions.push({
              actionType: 'redirect',
              actionConfig: JSON.stringify(rule?.redirectConfig),
            });
          }
        }
        if (!isEmpty(ruleActions)) {
          set(_rule, 'ruleActions', ruleActions);
        }
        return _rule;
      });
      // @ts-ignore
      _rules = _rules.concat(children);
    });

    const _headRule = head(defaultRouter);
    const _defaultRule = {
      appId: get(_headRule, 'defaultAppId'),
      containerPort: get(_headRule, 'defaultPort'),
      backendProtocol: get(_headRule, 'defaultBackendProtocol', 'http'),
    };

    let xForwardedParams = {};
    forEach(XForwardedArr, (item) => {
      xForwardedParams[item] = includes(enableXForwarded, item);
    });
    console.log('=====Rules', _rules);
    let _params = {};

    let commonParams = {
      ListenerProtocol: protocalType,
      ListenerPort: port,
      CertIds: _certIds,
      RequestTimeout: timeout,
      IdleTimeout: idleTimeout,
      SecurityPolicyId: _policyId,
      Rules: JSON.stringify(_rules),
      DefaultRule: JSON.stringify(_defaultRule),
      ...xForwardedParams,
    };
    if (CorsConfigVisible) {
      Reflect.set(commonParams, 'CorsConfig', {
        Enable: JSON.stringify(CorsConfigEnable),
      });
    }
    let vSwitchInfo = [];
    if (has(CreateAgentALBMap, alb)) {
      forEach(vSwitch, (item, index) => {
        vSwitchInfo.push({
          VSwitchId: item,
          ZoneId: get(vSwitchIdMap, `${item}.ZoneId`),
        });
      });
      _params = {
        LoadBalanceType: 'alb',
        AddressType: upperFirst(networkType),
        LoadBalancerEdition: alb,
        ZoneMappings: JSON.stringify(vSwitchInfo),
        ...commonParams,
      };
    } else {
      _params = {
        SlbId: alb,
        ...commonParams,
      };
    }
    resolve({ success: true, params: _params });
  };

  const setValues = (ingress) => {
    const _alb = get(ingress, 'SlbId');
    const _port = get(ingress, 'ListenerPort');
    const _protocal = get(ingress, 'ListenerProtocol', Protocals.http);
    _protocal === Protocals.https && getCertificates();
    const _timeout = get(ingress, 'RequestTimeout', 60);
    const _idleTimeout = get(ingress, 'IdleTimeout', 15);
    const _tls = get(ingress, 'SecurityPolicyId');
    const _isExpended = _timeout || !isEmpty(_tls);
    const _rules = get(ingress, 'Rules', []);
    const _defaultRule = get(ingress, 'DefaultRule', {});
    const _ruleList = [];
    const domainKeys = {};
    for (let i = 0; i < _rules.length; i++) {
      const rule = _rules[i];
      const RuleActions = rule?.RuleActions || [];
      let rewriteConfig = {};
      let redirectConfig = {};
      let forwardType = 'forwardTo';
      if (RuleActions.length > 0) {
        forEach(RuleActions, (ruleAction) => {
          if (ruleAction?.ActionType && ruleAction?.ActionConfig) {
            switch (ruleAction?.ActionType) {
              case 'rewrite':
                rewriteConfig = JSON.parse(ruleAction?.ActionConfig);
                forwardType = 'forwardTo';
                break;
              case 'redirect':
                redirectConfig = JSON.parse(ruleAction?.ActionConfig);
                forwardType = 'redirect';
                break;
              // no default
            }
          }
        });
      }
      const {
        Domain: domain,
        Path: path,
        AppId: appId,
        ContainerPort: containerPort,
        BackendProtocol: backendProtocol,
        // RewritePath: rewritePath,
      } = rule;
      if (!domainKeys[domain]) {
        domainKeys[domain] = [];
      }
      domainKeys[domain].push({
        domain,
        path,
        appId,
        containerPort,
        backendProtocol,
        rewriteConfig,
        redirectConfig,
        forwardType,
      });
    }
    for (let key in domainKeys) {
      // @ts-ignore
      _ruleList.push(domainKeys[key]);
    }

    let enableXForwarded = [];
    forEach(XForwardedArr, (item) => {
      if (get(ingress, item) || item === 'EnableXForwardedFor') {
        enableXForwarded.push(item);
      }
    });
    const _CorsConfigEnable = get(ingress, 'CorsConfig.Enable') === 'true' ? true : false;
    field.setValues({
      alb: _alb,
      protocalType: _protocal,
      port: _port,
      ssl: get(ingress, 'CertIds') ? get(ingress, 'CertIds', '').split(',') : [],
      timeout: _timeout,
      idleTimeout: _idleTimeout,
      tls: _tls,
      ruleList: _ruleList,
      defaultRouter: [
        {
          defaultAppId: get(_defaultRule, 'AppId'),
          defaultPort: get(_defaultRule, 'ContainerPort'),
          defaultBackendProtocol: get(_defaultRule, 'BackendProtocol', 'http'),
        },
      ],

      enableXForwarded,
      CorsConfigEnable: _CorsConfigEnable,
    });
    setProtocal(_protocal);
    setPort(_port);
    setInstanceId(_alb);
    setIsExpended(_isExpended);
  };

  // alb
  const getLoadBalancers = () => {
    return services
      .getAlbLoadBalancers({
        params: {
          NextToken: '',
          MaxResults: 50,
          AddressType: networkType,
          RegionId: regionId,
        },
        customErrorHandle: (error, _p, cb) => {
          const forbidden = isForbidden(error.code);
          setAuthedALB(!forbidden);
          if (!forbidden) cb?.();
        },
      })
      .then((res) => {
        const { LoadBalancers = [] } = res || {};
        const loadBalancers = lowerFirstData(LoadBalancers) || {};
        const validBalancers = filter(loadBalancers, {
          loadBalancerStatus: 'Active',
          vpcId: VpcId,
        });
        const otherBalancers = [
          {
            label: intl(
              'saenext.components.route-create.AlbForwardform.CreateAStandardAlbInstance',
            ),
            value: 'Standard',
          },
          {
            label: intl('saenext.components.route-create.AlbForwardform.CreateAWafEnhancedAlb'),
            value: 'StandardWithWaf',
          },
        ];

        const _loadBalancers = map(validBalancers, (balancer) => ({
          label: `${balancer.loadBalancerName}(${balancer.loadBalancerId})`,
          value: balancer.loadBalancerId,
          ...balancer,
        }));
        loadBalancers.forEach((item) => {
          loadBalancerIdMap.current.set(item.loadBalancerId, item);
        });
        // @ts-ignore
        if (disabled) {
          setLoadBalancers(_loadBalancers);
        } else {
          // 仅新建时支持ALB代购
          setLoadBalancers([...otherBalancers, ..._loadBalancers]);
        }
        return _loadBalancers;
      })
      .catch(() => []);
  };

  const getCertificates = async (search?: string) => {
    // if (val === Protocals.http) {
    //   return Promise.resolve([]);
    // }
    const _certificates = [];
    await loopCertificates(1, 99, _certificates, search);
    // @ts-ignore
    setCertificates(_certificates);
    return _certificates;
  };

  const loopCertificates = async (currentPage = 1, pageSize = 99, certList, search?: string) => {
    const res = await services.getSSLCertificateList({
      params: {
        ShowSize: pageSize,
        CurrentPage: currentPage,
        RegionId: regionId,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        setAuthedSLB(!forbidden);
        if (!forbidden) cb?.();
      },
    });
    const { CertMetaList, TotalCount } = res || {};
    let certMetaList = CertMetaList;
    if (!isEmpty(search)) {
      certMetaList = CertMetaList.filter((item) => {
        return item.CommonName.includes(search) || item.CertName.includes(search);
      });
    }
    const _certList = map(certMetaList, (item) => ({
      label: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: '#333' }}>{item.CommonName}</span>
          <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
          <span style={{ color: '#666' }}>{item.CertName}</span>
        </div>
      ),

      value: item.CertIdentifier,
      ...item,
    }));
    certList.push(..._certList);

    if (currentPage * pageSize < TotalCount) {
      await loopCertificates(currentPage + 1, pageSize, certList, search);
    }
  };

  const getAlbTimeoutQuota = async () => {
    const res = await services.GetProductQuota({
      params: {
        ProductCode: 'alb',
        QuotaActionCode: 'q_i9iiro', // 配额ID,创建监听时连接请求最大超时时间
      },
    });
    const quota = res?.Quota?.TotalQuota;
    if (!quota) return;
    setAlbTimeoutQuota(quota);
  };

  const getApplications = () => {
    return services
      .getApplicationList({
        RegionId: regionId,
        NamespaceId: namespaceId,
      })
      .then((res) => {
        const { Data = {} } = res || {};
        const { Applications = [] } = Data;
        const _applications = map(Applications, (item) => ({
          ...item,
          label: item?.AppName,
          value: item?.AppId,
        }));
        setApplications(_applications);
        return _applications;
      })
      .catch(() => []);
  };

  const getSecurityPolicies = async () => {
    let _securityPolicies = [];
    const systemRes = await services.getSystemSecurityPolicies({
      RegionId: regionId,
    });
    let systemPolicies = get(systemRes, 'SecurityPolicies', []);
    const customRes = await services.getCustomSecurityPolicies({
      params: {
        MaxResults: 20,
        RegionId: regionId,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        setAuthedALB(!forbidden);
        if (!forbidden) cb?.();
      },
    });
    let customPolicies = get(customRes, 'SecurityPolicies', []);

    _securityPolicies = [...systemPolicies, ...customPolicies];
    _securityPolicies = map(_securityPolicies, (policy) => {
      const { CreateTime, ResourceGroupId, Ciphers = [] } = policy;
      const cipherCount = Ciphers.length;
      const finalCipher = Ciphers[cipherCount - 1];
      const isCustom = !isEmpty(CreateTime) && !isEmpty(ResourceGroupId);
      return {
        ...policy,
        label: (
          <>
            <span style={{ marginRight: 8 }}>
              {policy.SecurityPolicyName || '-'} | {policy.SecurityPolicyId}{' '}
              {isCustom ? intl('saenext.components.route-create.AlbForwardform.Custom') : ''}
            </span>
            <>
              {cipherCount > 0 ? (
                <Balloon
                  cache={true}
                  align="r"
                  trigger={<Icon size="xs" type="info_fill" style={{ color: '#888' }} />}
                  closable={false}
                >
                  <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <span>
                      {intl('saenext.components.route-create.AlbForwardform.TlsProtocol')}
                    </span>
                    <span>
                      {intl(
                        'saenext.components.route-create.AlbForwardform.SupportedEncryptionSuitesCiphercount',
                        { cipherCount: cipherCount },
                      )}
                    </span>
                    <>
                      {map(Ciphers, (cipher) => (
                        <span>{`${cipher}${isEqual(cipher, finalCipher) ? '' : ','}`}</span>
                      ))}
                    </>
                  </div>
                </Balloon>
              ) : null}
            </>
          </>
        ),

        value: policy.SecurityPolicyId,
      };
    });
    setSecurityPolicies(_securityPolicies);
  };

  const getRegionZones = async () => {
    const res = await services.QueryAlbZones({
      params: {
        RegionId: regionId,
      },
    });
    const _zones = map(res?.Zones || [], (item) => item.ZoneId);
    setAlbZones(_zones);
  };

  const getVswitchs = async () => {
    const res = await services.DescribeVSwitches({
      params: {
        RegionId: regionId,
        VpcId: VpcId,
        PageNumber: 1,
        PageSize: 50,
      },
      customErrorHandle: (error, _p, cb) => {
        if (isForbidden(error.code)) {
          setAuthedVswitch(false);
        } else {
          setAuthedVswitch(true);
          cb?.();
        }
      },
    });
    const {
      VSwitches: { VSwitch = [] },
    } = res;
    let _vSwitchIdMap = {};
    const _vSwitchs = VSwitch.map((item) => {
      set(_vSwitchIdMap, `${item.VSwitchId}`, item);
      const zoneName = toUpper(last(item.ZoneId as string));
      return {
        label: (
          <div>
            <span className="mr-l">{item.VSwitchName}</span>
            <span className="text-description mr-s">{item.VSwitchId}</span>
            <span className="text-description">
              {intl('saenext.NamespaceField.micro-app.Namespace.ZoneZonename', {
                zoneName: zoneName,
              })}
            </span>
          </div>
        ),

        value: item.VSwitchId,
        disabled: !includes(albZones, item.ZoneId),
        tip: includes(albZones, item.ZoneId)
          ? ''
          : intl('saenext.components.route-create.AlbForwardform.AlbDoesNotSupportThis'),
        ...item,
      };
    });
    setVSwitchIdMap(_vSwitchIdMap);
    setVSwitchList(_vSwitchs);
  };

  const transmitValidator = (rule, val, callback) => {
    if (!port) callback();
    if (val?.length > 0) {
      let isHasEmpty = false;
      let redirectValidate = false;
      for (let i = 0; i < val.length; i++) {
        const value = val[i];
        if (value.length > 0) {
          isHasEmpty = value.some((v) => {
            if (v?.forwardType === 'redirect') {
              return !v.port || !v.path;
            } else {
              return !v.port || !v.appId || !v.path || !v.containerPort;
            }
          });
          redirectValidate = value.some((v) => {
            return (
              v?.forwardType === 'redirect' &&
              has(v, 'redirectConfig') &&
              !isEmpty(v?.redirectConfig) &&
              ((!v?.redirectConfig?.protocol &&
                !v?.redirectConfig?.host &&
                !v?.redirectConfig?.port &&
                !v?.redirectConfig?.path &&
                !v?.redirectConfig?.query) ||
                (v?.redirectConfig?.protocol === '${protocol}' &&
                  v?.redirectConfig?.host === '${host}' &&
                  v?.redirectConfig?.port === '${port}' &&
                  v?.redirectConfig?.path === '${path}' &&
                  v?.redirectConfig?.query === '${query}'))
            );
          });
        }
      }
      if (isHasEmpty) {
        callback(intl('saenext.components.route-create.AlbForwardform.TheForwardingPolicyDataIs'));
      } else if (redirectValidate) {
        callback(intl('saenext.components.route-create.AlbForwardform.TheProtocolDomainNamePort'));
      } else {
        callback();
      }
    } else {
      callback();
    }
  };

  const initTransmitValidator = (rule, val, callback) => {
    if (!port) callback();
    if (val?.length > 0) {
      const isHasEmpty = val.some((v) => {
        return !v.ip || !v.port || !v.defaultAppId || !v.defaultPort;
      });
      if (isHasEmpty) {
        callback(intl('saenext.components.route-create.AlbForwardform.TheForwardingPolicyDataIs'));
      } else {
        callback();
      }
    } else {
      callback();
    }
  };

  return (
    <Form field={field}>
      <Form.Item
        required
        label={
          <UnAuthedLabel
            text={intl('saenext.components.route-create.AlbForwardform.AlbInstance')}
            authed={authedALB}
            authKey="AliyunALBReadOnlyAccess"
          />
        }
        help={
          <div>
            {!has(CreateAgentALBMap, instanceId) && (
              <>
                <span>{intl('saenext.components.route-create.AlbForwardform.IfThereIsNoAlb')}</span>
                <LinkButton
                  className="ml-xs mr-xs"
                  onClick={() =>
                    window.open(
                      `${CachedData.confLink('feature:slb:url')}/alb/alb/${regionId}/albs`,
                      '_blank',
                    )
                  }
                >
                  <span>{intl('saenext.components.route-create.AlbForwardform.AlbConsole')}</span>
                  <Icon size="xs" type="external_link" />
                </LinkButton>
                <span>{intl('saenext.components.route-create.AlbForwardform.Create')}</span>
              </>
            )}
            {has(CreateAgentALBMap, instanceId) && (
              <>
                <span>
                  {intl('saenext.components.route-create.AlbForwardform.AfterYouSelectCreateThe', {
                    CreateAgentALBMapInstanceId: CreateAgentALBMap[instanceId],
                  })}
                </span>
                <a href={CachedData.confLink('help:slb:alb-billing-overview')} target="_blank">
                  {intl('saenext.components.route-create.AlbForwardform.AlbBillingInstructions')}
                </a>
              </>
            )}
          </div>
        }
      >
        <RefreshSelect
          {...init('alb', {
            rules: [
              {
                required: true,
                message: intl('saenext.components.route-create.AlbForwardform.SelectAnAlbInstance'),
              },
            ],

            props: {
              onChange: (val: string) => setInstanceId(val),
            },
          })}
          disabled={disabled}
          dataSource={loadBalancers}
          placeholder={intl('saenext.components.route-create.AlbForwardform.SelectAnAlbInstance')}
          fetchMethod={getLoadBalancers}
          external={{
            width: 150,
            label: intl('saenext.components.route-create.AlbForwardform.CreateAnAlbInstance'),
            url: `${CachedData.confLink('feature:slb:url')}/alb/alb/${regionId}/albs`,
          }}
          // @ts-ignore
          itemRender={(item: any) => {
            return (
              <div>
                <span>{item.label}</span>
                {item.loadBalancerEdition && !isEmpty(AlbVersionMap[item.loadBalancerEdition]) && (
                  <CndTag
                    tagText={AlbVersionMap[item.loadBalancerEdition]?.label}
                    tagType={AlbVersionMap[item.loadBalancerEdition]?.type}
                    style={{ marginLeft: 4 }}
                  />
                )}
              </div>
            );
          }}
        />
      </Form.Item>
      {has(CreateAgentALBMap, instanceId) && (
        <Form.Item label={intl('saenext.components.route-create.AlbForwardform.Vpc')} required>
          <Input value={VpcId} disabled />
        </Form.Item>
      )}
      {has(CreateAgentALBMap, instanceId) && (
        <Form.Item
          label={
            <UnAuthedLabel
              text={intl('saenext.components.route-create.AlbForwardform.VirtualSwitchVswitch')}
              authed={authedVswitch}
              authKey="AliyunVPCReadOnlyAccess"
            />
          }
          required
          requiredMessage={intl(
            'saenext.components.route-create.AlbForwardform.SelectAVirtualSwitch',
          )}
          validator={(rule, value: string[], callback) => {
            if (value.length < 2) {
              callback(intl('saenext.components.route-create.AlbForwardform.SelectAtLeastOrMore'));
            } else {
              const _vSwitchIdZone = (value || []).map((item) => {
                return get(vSwitchIdMap, `${item}.ZoneId`);
              });
              if (uniq(_vSwitchIdZone).length < (value || []).length) {
                callback(intl('saenext.components.route-create.AlbForwardform.OnlyOneSwitchCanBe'));
              } else {
                callback();
              }
            }
          }}
          help={
            field.getError('vSwitch') ||
            intl('saenext.components.route-create.AlbForwardform.ToEnsureHighAvailabilityOf')
          }
        >
          <RefreshSelect
            mode="multiple"
            name="vSwitch"
            dataSource={vSwitchList}
            placeholder={intl(
              'saenext.components.route-create.AlbForwardform.SelectAVirtualSwitch',
            )}
            fetchMethod={getVswitchs}
            external={{
              width: 150,
              label: intl('saenext.components.route-create.AlbForwardform.CreateVswitch'),
              url: `${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/switches`,
            }}
          />
        </Form.Item>
      )}
      <div className="justify-center" style={{ alignItems: 'baseline' }}>
        <Form.Item
          required
          label={intl('saenext.components.route-create.AlbForwardform.FrontendProtocolType')}
        >
          <Radio.Group
            {...init('protocalType', {
              initValue: Protocals.http,
              rules: [
                {
                  required: true,
                  message: intl(
                    'saenext.components.route-create.AlbForwardform.SelectAFrontendProtocolType',
                  ),
                },
              ],

              props: {
                onChange: (val: Protocals) => {
                  setProtocal(val);
                  val === Protocals.https && getCertificates();
                },
              },
            })}
          >
            <Radio id="http" style={{ minWidth: 80 }} value={Protocals.http}>
              HTTP
            </Radio>
            <Radio id="https" style={{ minWidth: 80 }} value={Protocals.https}>
              HTTPS
            </Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          required
          label={intl('saenext.components.route-create.AlbForwardform.AccessPort')}
          help={intl('saenext.components.route-create.AlbForwardform.EnterAValidPort')}
          style={{ marginLeft: 80 }}
        >
          <NumberPicker
            {...init('port', {
              rules: [
                {
                  required: true,
                  message: intl('saenext.components.route-create.AlbForwardform.EnterAnAccessPort'),
                },
              ],

              props: {
                onChange: (val: string) => setPort(val),
              },
            })}
            min={1}
            max={65535}
            style={{ width: 180 }}
          />
        </Form.Item>
      </div>
      <>
        {protocal === Protocals.https ? (
          <Form.Item
            required
            label={intl('saenext.components.route-create.AlbForwardform.SslCertificate')}
          >
            <RefreshSelect
              {...init('ssl', {
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.route-create.AlbForwardform.SelectAnSslCertificate',
                    ),
                  },
                ],
              })}
              mode="multiple"
              dataSource={certificates}
              placeholder={intl(
                'saenext.components.route-create.AlbForwardform.SelectAnSslCertificate',
              )}
              fetchMethod={getCertificates}
              autoSearch={{
                enabled: true,
                placeholder: intl(
                  'saenext.components.route-create.AlbForwardform.YouCanSearchBySsl',
                ),
              }}
              external={{
                width: 150,
                label: intl(
                  'saenext.components.route-create.AlbForwardform.CreateAnSslCertificate',
                ),
                url: `${CachedData.confLink('feature:yundunnext:url')}/?&p=cas#/certExtend/buy`,
              }}
            />
          </Form.Item>
        ) : null}
      </>

      <Form.Item>
        <LinkButton onClick={() => setIsExpended(!isExpended)}>
          <span className="mr-s">
            {intl('saenext.components.route-create.AlbForwardform.AdvancedConfiguration')}
          </span>
          {isExpended ? <Icon size="xs" type="arrow-up" /> : <Icon size="xs" type="arrow-down" />}
        </LinkButton>
      </Form.Item>

      <Form.Item
        label={
          <TextWithBalloon
            align="r"
            text={intl('saenext.components.route-create.AlbForwardform.ConnectionRequestTimeout')}
            tips={intl('saenext.components.route-create.AlbForwardform.WhenTheBackendServerDoes')}
          />
        }
        hidden={!isExpended}
        help={intl(
          'saenext.components.route-create.AlbForwardform.TheInputRangeIsAlbtimeoutquota',
          { albTimeoutQuota: albTimeoutQuota },
        )}
      >
        <NumberPicker
          {...init('timeout', {
            initValue: 60,
          })}
          min={1}
          max={albTimeoutQuota}
          innerAfter={intl('saenext.components.route-create.AlbForwardform.Seconds')}
          style={{ width: 432 }}
        />
      </Form.Item>

      <Form.Item
        label={
          <TextWithBalloon
            align="r"
            text={intl('saenext.components.route-create.AlbForwardform.ConnectionIdleTimeout')}
            tips={intl('saenext.components.route-create.AlbForwardform.WhenTheBackendIdleDoes')}
          />
        }
        hidden={!isExpended}
        help={intl('saenext.components.route-create.AlbForwardform.TheIdleInputRangeIsSeconds')}
      >
        <NumberPicker
          {...init('idleTimeout', {
            initValue: 15,
          })}
          min={1}
          max={60}
          innerAfter={intl('saenext.components.route-create.AlbForwardform.Seconds')}
          style={{ width: 432 }}
        />
      </Form.Item>

      <>
        {protocal === Protocals.https ? (
          <Form.Item
            label={
              <div>
                <TextWithBalloon
                  align="r"
                  text={intl('saenext.components.route-create.AlbForwardform.TlsSecurityPolicy')}
                  tips={intl(
                    'saenext.components.route-create.AlbForwardform.TheTlsSecurityPolicyConsists',
                  )}
                />

                <UnAuthedLabel text="" authed={authedALB} authKey="AliyunALBReadOnlyAccess" />
              </div>
            }
            hidden={!isExpended}
          >
            <RefreshSelect
              {...init('tls')}
              dataSource={securityPolicies}
              placeholder={intl(
                'saenext.components.route-create.AlbForwardform.SelectATlsSecurityPolicy',
              )}
              fetchMethod={getSecurityPolicies}
              external={{
                width: 170,
                label: intl(
                  'saenext.components.route-create.AlbForwardform.CreateATlsSecurityPolicy',
                ),
                url: `${CachedData.confLink('feature:slb:url')}/alb/alb/cn-shanghai/tls`,
              }}
            />
          </Form.Item>
        ) : null}
      </>
      {CorsConfigVisible && (
        <Form.Item
          label={intl('saenext.components.route-create.AlbForwardform.EnableCrossDomain')}
          hidden={!isExpended}
          labelCol={{ span: 3 }}
        >
          <Switch name="CorsConfigEnable" />
        </Form.Item>
      )}
      <Form.Item
        label={intl('saenext.components.route-create.AlbForwardform.AttachHttpHeaderFields')}
        hidden={!isExpended}
      >
        <CheckboxGroup
          name="enableXForwarded"
          //@ts-ignore
          value={get(field.getValues(), 'enableXForwarded', ['EnableXForwardedFor'])}
          itemDirection="ver"
          dataSource={XForwardedSource}
          onChange={(keys) => {
            field.setValue('enableXForwarded', keys);
          }}
        />
      </Form.Item>
      <Form.Item
        label={intl('saenext.components.route-create.AlbForwardform.CustomForwardingPolicy')}
      >
        {/* @ts-ignore */}
        <TransmitCheckRows
          {...init('ruleList', {
            initValue: [],
            rules: [
              {
                validator: transmitValidator,
              },
            ],
          })}
          target={Gateway.alb}
          port={port}
          regionId={regionId}
          baseContainer={baseContainer}
          protocal={protocal}
          applications={applications}
          refreshApps={getApplications}
          loadBalancerVersion={loadBalancerVersion}
        />
      </Form.Item>
      <Form.Item
        required
        label={intl('saenext.components.route-create.AlbForwardform.DefaultForwardingPolicy')}
      >
        {/* @ts-ignore */}
        <InitTransmitRows
          {...init('defaultRouter', {
            initValue: [],
            rules: [
              {
                validator: initTransmitValidator,
              },
            ],
          })}
          target={Gateway.alb}
          instanceId={instanceId}
          port={port}
          protocal={protocal}
          loadBalancers={loadBalancers}
          applications={applications}
          refreshApps={getApplications}
          loadBalancerVersion={loadBalancerVersion}
        />
      </Form.Item>
    </Form>
  );
});
