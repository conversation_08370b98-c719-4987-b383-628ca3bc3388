import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { Form, Select, Icon, LinkButton, Grid, Switch, Input, Balloon } from '@ali/cnd';
import { head, get, map, forEach, isEmpty, keys } from 'lodash';
import services from "~/services";
import { Scene, NACOS_DEFAULT_NAMESPACEID, SourceCodeMap } from './config';
import CachedData from '../../../cache/common';

const { Row, Col } = Grid;

const fields = {
  appId: 'AppId',
  serviceId: 'ServiceId',
  protocol: 'ServiceProtocol',
  port: 'ServicePort',
  tag: 'ServiceTag',
};

export default (props) => {
  const {
    regionId,
    baseContainer,
    renderSource,
    useSence,
    onChange,
    value = [],
    applications,
    refreshApps,
    fallback: _fallback = false,
    updateFallback,
    nacosInstanceId,
    nacosNamespaceId,
  } = props;

  const _value = head(value) || {};
  const [loading, setLoading] = useState(false);
  const appletRef = useRef({});
  const [fallback, setFallback] = useState(_fallback);

  useEffect(() => {
    forEach(applications, (app) => {
      appletRef.current[app.value] = app;
    });
  }, [applications]);

  useEffect(() => {
    setFallback(_fallback);
  }, [_fallback]);

  const refreshServices = (appId, callback) => {
    const _registryType = SourceCodeMap[renderSource]
    return services
      .getServices({
        params: {
          AppId: appId,
          ServiceType: _registryType === 9 ? 'k8sService' : 'springCloud',
          RegistryType: _registryType,
          NacosInstanceId: nacosInstanceId,
          NacosNamespaceId: nacosNamespaceId === NACOS_DEFAULT_NAMESPACEID ? '' : nacosNamespaceId,
          PageNumber: 1,
          PageSize: 99,
        },
      })
      .then((res) => {
        const { Data = [] } = res || {};
        const _services = map(Data, (item) => ({
          ...item,
          label: item.ServiceName,
          value: item.ServiceName,
        }));
        callback && callback(_services);
        return _services;
      })
      .catch(() => {
        callback && callback([]);
        return [];
      });
  };

  const refreshServicePorts = (appId, service, callback) => {
    const _registryType = SourceCodeMap[renderSource];
    return services
      .getServicePorts({
        params: {
          AppId: appId,
          ServiceType: _registryType === 9 ? 'k8sService' : 'springCloud',
          RegistryType: _registryType,
          NacosInstanceId: nacosInstanceId,
          NacosNamespaceId: nacosNamespaceId === NACOS_DEFAULT_NAMESPACEID ? '' : nacosNamespaceId,
          ServiceName: get(service, 'ServiceName', ''),
          ServiceGroup: get(service, 'ServiceGroup', ''),
          ServiceVersion: get(service, 'ServiceVersion', ''),
        },
      })
      .then((res) => {
        const { Data: _service } = res || {};
        const port = get(_service, 'ServicePorts', []);
        const _ports = map(port, (item) => ({
          label: item,
          value: Number(item),
        }));
        callback &&
          callback(
            {
              ..._service,
              ServiceGroup: get(_service, 'ServiceGroup', []) || get(service, 'ServiceGroup', ''),
            },
            _ports,
          );
        return _ports;
      })
      .catch(() => {
        callback && callback({}, []);
        return [];
      });
  };

  const handleChange = async (key, _value, item?) => {
    // @ts-ignore
    const _newValue = head(value) || {};
    _newValue[key] = _value;

    switch (key) {
      case fields.appId:
        // 清空后面的值
        // @ts-ignore
        _newValue.services = [];
        // @ts-ignore
        _newValue.ports = [];
        // @ts-ignore
        _newValue.tags = [];
        _newValue[fields.serviceId] = '';
        _newValue[fields.protocol] = '';
        _newValue[fields.port] = '';
        _newValue[fields.tag] = '';

        // @ts-ignore
        _newValue.sloading = true;
        refreshServices(_value, (services) => {
          onChange([
            {
              // @ts-ignore
              ..._newValue,
              services,
              sloading: false,
            },
          ]);
        });
        break;
      case fields.serviceId:
        // 清空后面的值
        // @ts-ignore
        _newValue.ports = [];
        // @ts-ignore
        _newValue.tags = [];
        _newValue[fields.protocol] = '';
        _newValue[fields.port] = '';
        _newValue[fields.tag] = '';

        // @ts-ignore
        _newValue.ploading = true;
        refreshServicePorts(
          // @ts-ignore
          _newValue.AppId,
          item,
          (service, ports) => {
            const tags = get(service, 'ServiceTags', {});
            let _tags = [
              {
                label: intl('saenext.components.route-create.InitFallbackRows.Unlimited'),
                value: 'mse-gw-version-unrestricted',
              },
            ];

            if (!isEmpty(tags)) {
              forEach(keys(tags), (key) => {
                _tags.unshift({ label: tags[key], value: tags[key] });
              });
            }
            onChange([
              {
                // @ts-ignore
                ..._newValue,
                ports: ports,
                tags: _tags,
                ploading: false,
                ServiceGroup: get(service, 'ServiceGroup', ''),
                ServiceProtocol: get(service, 'ServiceProtocol', ''),
              },
            ]);
          },
        );
        break;
    }
    onChange([_newValue]);
  };

  const handleMenuProps = {
    footer: (
      <div style={{ padding: '0 4px', textAlign: 'left', borderTop: '1px solid #eee' }}>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={() => {
            const uri =
              baseContainer === 'v2'
                ? `${CachedData.confLink('feature:saenext:url')}/${regionId}/create-app/micro`
                : `${CachedData.confLink('feature:sae:url')}/overview#/AppList/CreateApp?regionId=${regionId}`;
            window.open(uri, '_blank');
          }}
        >
          <Icon size="xs" type="external_link" />
          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.InitFallbackRows.CreateAnApplication')}
          </span>
        </LinkButton>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={async () => {
            setLoading(true);
            refreshApps && (await refreshApps());
            setLoading(false);
          }}
        >
          {loading ? <Icon size="xs" type="loading" /> : <Icon size="xs" type="refresh" />}

          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.InitFallbackRows.Refresh')}
          </span>
        </LinkButton>
      </div>
    ),
  };

  const isLabelRoute = useSence === Scene.label;

  return (
    <>
      <span className="mb-s" style={{ display: 'inline-block', fontSize: 12, color: '#888' }}>
        {intl('saenext.components.route-create.InitFallbackRows.WhenTheBackendServicePointed')}
        <span style={{ fontWeight: 500 }}>
          {intl('saenext.components.route-create.InitFallbackRows.NoteThatOnlyFallbackBetween')}
        </span>
        {intl("saenext.common.full-stop")}
      </span>
      <Form.Item>
        <Switch
          checked={fallback}
          onChange={(val) => {
            setFallback(val);
            updateFallback(val);
            // 关闭状态 清除之前的配置
            if (!val) {
              onChange([
                { AppId: '', ServiceName: '', ServiceProtocol: '', ServicePort: '', Version: '' },
              ]);
            }
          }}
        />

        <span style={{ display: 'inline-block', fontSize: 12, color: '#888', marginLeft: 16 }}>
          {intl('saenext.components.route-create.InitFallbackRows.SetFallbackService')}
        </span>
      </Form.Item>
      {fallback ? (
        <>
          <Row style={{ color: '#555', lineHeight: '32px' }}>
            <Col span={isLabelRoute ? 6 : 7}>
              {intl('saenext.components.route-create.InitFallbackRows.ApplicationName')}
            </Col>
            <Col span={isLabelRoute ? 6 : 7} style={{ paddingLeft: 10 }}>
              {intl('saenext.components.route-create.InitFallbackRows.ServiceName')}
            </Col>
            <Col span={isLabelRoute ? 4 : 5} style={{ paddingLeft: 10 }}>
              {intl('saenext.components.route-create.InitFallbackRows.ServiceAgreement')}
            </Col>
            <Col span={isLabelRoute ? 4 : 5} style={{ paddingLeft: 10 }}>
              {intl('saenext.components.route-create.InitFallbackRows.ServicePort')}
            </Col>
            {isLabelRoute ? (
              <Col span="4" style={{ paddingLeft: 10 }}>
                {intl('saenext.components.route-create.InitFallbackRows.ServiceVersion')}
              </Col>
            ) : null}
          </Row>
          <Row>
            <Col span={isLabelRoute ? 6 : 7}>
              <Select
                style={{ width: '100%' }}
                value={get(_value, fields.appId)}
                onChange={(val) => handleChange(fields.appId, val)}
                menuProps={handleMenuProps}
                placeholder={intl(
                  'saenext.components.route-create.InitFallbackRows.SelectAnApplication',
                )}
                showSearch
              >
                {map(applications, (item) => (
                  <Select.Option value={item.value}>{item?.label}</Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={isLabelRoute ? 6 : 7} style={{ paddingLeft: 10 }}>
              <Select
                style={{ width: '100%' }}
                value={get(_value, fields.serviceId)}
                // @ts-ignore
                state={get(_value, 'sloading', false) ? 'loading' : null}
                onChange={(val, __, item) => handleChange(fields.serviceId, val, item)}
                placeholder={intl(
                  'saenext.components.route-create.InitFallbackRows.SelectAService',
                )}
                showSearch
                dataSource={get(_value, 'services', [])}
              />
            </Col>
            <Col span={isLabelRoute ? 4 : 5} style={{ paddingLeft: 10 }}>
              <Input
                readOnly
                style={{ width: '100%' }}
                value={get(_value, fields.protocol)}
                onChange={(val) => handleChange(fields.protocol, val)}
                placeholder={intl('saenext.components.route-create.InitFallbackRows.AutomaticRead')}
              />
            </Col>
            <Col span={isLabelRoute ? 4 : 5} style={{ paddingLeft: 10 }}>
              <Select
                placeholder={intl('saenext.components.route-create.InitFallbackRows.ServicePort')}
                style={{ width: '100%' }}
                value={get(_value, fields.port)}
                // @ts-ignore
                state={get(_value, 'ploading', false) ? 'loading' : null}
                onChange={(val) => handleChange(fields.port, val)}
                popupClassName="router-select-popup"
              >
                {map(get(_value, 'ports', []), (item) => (
                  // @ts-ignore
                  <Select.Option value={item.value}>
                    {/* @ts-ignore */}
                    {!item?.value && (
                      // @ts-ignore
                      <Balloon.Tooltip
                        align="r"
                        trigger={
                          <div>
                            {/* @ts-ignore */}
                            {item?.label} <Icon type="help" className="help-icon" />
                          </div>
                        }
                      >
                        {intl(
                          'saenext.components.route-create.InitFallbackRows.DynamicPortsAreApplicableTo',
                        )}
                      </Balloon.Tooltip>
                    )}

                    {/* @ts-ignore */}
                    {item?.value && item.label}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            {isLabelRoute ? (
              <Col span={4} style={{ paddingLeft: 10 }}>
                <Select
                  placeholder={intl(
                    'saenext.components.route-create.InitFallbackRows.ServiceVersion',
                  )}
                  style={{ width: '100%' }}
                  value={get(_value, fields.tag)}
                  // @ts-ignore
                  state={get(_value, 'vloading', false) ? 'loading' : null}
                  onChange={(val) => handleChange(fields.tag, val)}
                >
                  {map(
                    get(_value, 'tags', [
                      {
                        label: intl('saenext.components.route-create.InitFallbackRows.Unlimited'),
                        value: 'mse-gw-version-unrestricted',
                      },
                    ]),
                    (item) => (
                      // @ts-ignore
                      <Select.Option value={item.value}>{item?.label}</Select.Option>
                    ),
                  )}
                </Select>
              </Col>
            ) : null}
          </Row>
        </>
      ) : null}
    </>
  );
};
