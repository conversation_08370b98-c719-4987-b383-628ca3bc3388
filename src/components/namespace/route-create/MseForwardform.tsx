import { intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import {
  Button,
  Field,
  Grid,
  Form,
  Radio,
  Select,
  Icon,
  LinkButton,
  Checkbox,
  NumberPicker,
  CndTag
} from '@ali/cnd';
import RefreshSelect from './RefreshSelect';
import StaticCheckSelect from './StaticCheckSelect';
import SceneSelect from './SceneSelect';
import services from "~/services";
import { filter, map, get, head, forEach, includes } from 'lodash';
import { Network } from './index';
import RegularTest from './RegularTest';
import RequestCheckRows, { Params } from './RequestCheckRows';
import ServiceCheckRows from './ServiceCheckRows';
import InitFallbackRows from './InitFallbackRows';
import { isForbidden } from '../../../utils/authUtils';
import { UnAuthedLabel } from '../../shared/unauthedLabel';
import {
  Match,
  Source,
  Scene,
  MatchRules,
  Methods,
  NACOS_DEFAULT_NAMESPACEID,
  SourceCodeMap,
} from './config';
import CachedData from '../../../cache/common';

type Props = {
  mode: boolean;
  regionId: string;
  visible: boolean;
  baseContainer: string;
  namespaceId: string;
  networkType: string;
  namespaceResources: {
    VpcId?: string;
    [key: string]: any;
  };
  // init: <T>(name: string, option?: InitOption, props?: {}) => InitResult<T>;
  [key: string]: any;
};

type Gateway = {
  label?: string;
  value?: string;
  GatewayUniqueId?: string;
};

const { Row, Col } = Grid;

export default forwardRef((props: Props, ref) => {
  const {
    regionId,
    namespaceId,
    visible,
    baseContainer,
    networkType,
    // init,
    namespaceResources = {
      VpcId: '',
    },
    mode: disabled,
    setDisableSubmit,
    setSubmitTip,
    // @ts-ignore
    ...childProps
  } = props;
  if (!visible) return null;
  const { VpcId } = namespaceResources;
  const field = Field.useField();
  const { init } = field;
  const [gateway, setGateway] = useState({});
  const [gateways, setGateways] = useState([]);
  const [domains, setDomains] = useState([]);
  const [applications, setApplications] = useState<any>([]);
  const [match, setMatch] = useState(Match.pre);
  // @ts-ignore
  const [pathRegState, setPathRegState] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [source, setSource] = useState(Source.sae);
  const [scene, setScene] = useState(Scene.alone);
  // mse naocs 实例
  const [clusters, setClusters] = useState([]);
  const clustersRef = useRef(new Map());
  // mse naocs 命名空间
  const [namespaces, setNamespaces] = useState([]);
  const [nacosInstanceId, setNacosInstanceId] = useState('');
  const [nacosNamespaceId, setNacosNamespaceId] = useState('');
  const [isExpended, setIsExpended] = useState(false);
  const [fallback, setFallback] = useState(false);
  const fallbackRef = useRef(false);
  // @ts-ignore
  const [intranetDomain, setIntranetDomain] = useState('');
  const domainsRef = useRef({});
  const [authedMSE, setAuthedMSE] = useState(true);

  useEffect(() => {
    getGateways();
  }, [VpcId, networkType]);

  useEffect(() => {
    getApplications(source);
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      submit,
      setValues,
    }),
    [],
  );

  const submit = () => {
    const promise = new Promise<{ success: boolean; params: any }>(async (resolve) => {
      field.validate((errors, values) => {
        if (errors) {
          resolve({ success: false, params: null });
          return;
        }
        if (values) {
          const services = get(values, 'Services', []);
          const val = Array.isArray(services) ? services : [];
          if (val?.length > 0) {
            let percent = 0;
            let hasPercent = false;
            const scene = get(values, 'DestinationType', Scene.alone);
            const isHasEmpty = val.some((v) => {
              percent += get(v, 'Percent', 0);
              hasPercent = Reflect.has(v, 'Percent');

              switch (scene) {
                case Scene.alone:
                  // @ts-ignore
                  return !v.AppId || !v.ServiceId || !v.ServiceProtocol || !v.ServicePort;
                case Scene.multiple:
                  // @ts-ignore
                  return !v.AppId || !v.ServiceId || !v.ServiceProtocol || !v.ServicePort;
                case Scene.label:
                  // @ts-ignore
                  return (
                    !v.AppId ||
                    !v.ServiceId ||
                    !v.ServiceProtocol ||
                    !v.ServicePort ||
                    !v.ServiceTag
                  );
              }
            });

            if (isHasEmpty) {
              field.setError(
                'Services',
                intl('saenext.components.route-create.MseForwardform.TheBackendServiceDataIs'),
              );
              resolve({ success: false, params: null });
              return;
            }

            if (hasPercent && percent !== 100) {
              field.setError(
                'Services',
                intl('saenext.components.route-create.MseForwardform.TheSumOfServiceWeights'),
              );
              resolve({ success: false, params: null });
              return;
            }
          } else {
            field.setError(
              'Services',
              intl('saenext.components.route-create.MseForwardform.ConfigureBackendServices'),
            );
            resolve({ success: false, params: null });
            return;
          }
        }
        doSubmit(values, resolve);
      });
    });

    return promise;
  };

  const doSubmit = (values, resolve) => {
    const {
      GatewayUniqueId,
      DomainIdList,
      IgnoreCase,
      MethodPredicates: _methodPredicates,
      HeaderPredicates: _headerPredicates,
      QueryPredicates: _queryPredicates,
      DestinationType: scene,
      Services: services = [],
      FallbackServices: fallbackServices = [],
    } = values;
    const _domainList = map(DomainIdList, (id) => domainsRef.current[id]);
    const _ignoreCase = Array.isArray(IgnoreCase) ? !!IgnoreCase.length : IgnoreCase;
    const _pathPredicates = {
      Path: get(values, 'Path', ''),
      Type: get(values, 'Type', Match.pre),
      IgnoreCase: !_ignoreCase,
    };
    const _predicates = {
      PathPredicates: _pathPredicates,
      MethodPredicates: _methodPredicates,
      HeaderPredicates: _headerPredicates,
      QueryPredicateList: _queryPredicates,
    };
    const _source = get(values, 'SourceType', Source.sae);
    const _services = dispatchServices(scene, services);
    const _policies = {
      // Retry: {}, 重试配置后期加入
      Timeout: { UnitNum: get(values, 'Timeout', 60), TimeUnit: 's', Status: 'on' },
    };
    const _fallbackServices = dispatchServices(scene, fallbackServices);
    const _fallback = fallbackRef.current;
    const _params = {
      GatewayUniqueId,
      DomainIdList,
      DomainList: _domainList,
      Predicates: _predicates,
      SourceType: _source,
      DestinationType: scene,
      Services: _services,
      Policies: _policies,
      Fallback: _fallback,
      FallbackServices: _fallback ? head(_fallbackServices) : {},
    };
    // source === Source.mse 增加 NacosInstanceId
    if (_source === Source.mse) {
      const _instanceId = get(values, 'NacosInstanceId');
      Reflect.set(_params, 'NacosInstanceId', _instanceId);
      const _cluster = clustersRef.current.get(_instanceId);
      const _intranetDomain = get(_cluster, 'IntranetDomain');
      Reflect.set(_params, 'NacosAddress', _intranetDomain);
      const _nacosNamespaceId =
        get(values, 'NacosNamespaceId') === NACOS_DEFAULT_NAMESPACEID
          ? ''
          : get(values, 'NacosNamespaceId');
      Reflect.set(_params, 'NacosNamespaceId', _nacosNamespaceId);
    }
    resolve({ success: true, params: _params });
  };

  const dispatchServices = (scene, services) => {
    let _services = [];
    switch (scene) {
      case Scene.alone:
        const service = head(services);
        // @ts-ignore
        _services = [
          {
            AppId: get(service, 'AppId'),
            ServiceName: get(service, 'ServiceId'),
            ServiceProtocol: get(service, 'ServiceProtocol'),
            ServicePort: get(service, 'ServicePort'),
            ServiceGroup: get(service, 'ServiceGroup'),
            Percent: 100,
          },
        ];

        break;
      case Scene.multiple:
        _services = services.map((service) => ({
          AppId: get(service, 'AppId'),
          ServiceName: get(service, 'ServiceId'),
          ServiceProtocol: get(service, 'ServiceProtocol'),
          ServicePort: get(service, 'ServicePort'),
          ServiceGroup: get(service, 'ServiceGroup'),
          Percent: get(service, 'Percent', 100),
        }));
        break;
      case Scene.label:
        _services = services.map((service) => ({
          AppId: get(service, 'AppId'),
          ServiceName: get(service, 'ServiceId'),
          ServiceProtocol: get(service, 'ServiceProtocol'),
          ServicePort: get(service, 'ServicePort'),
          ServiceGroup: get(service, 'ServiceGroup'),
          ServiceTag: get(service, 'ServiceTag'),
          Percent: get(service, 'Percent'),
        }));
        break;
    }
    return _services;
  };

  const setValues = (ingress) => {
    const _gatewayId = get(ingress, 'GatewayUniqueId');
    // 为了再刷新域名的时候拿到_gatewayId
    const _gateway = {
      label: _gatewayId,
      value: _gatewayId,
      GatewayUniqueId: _gatewayId,
    };
    // @ts-ignore
    setGateway(_gateway);
    // 请求域名
    getDomains(_gatewayId);
    const predicates = get(ingress, 'RoutePredicates', {});
    const _pathPredicates = get(predicates, 'PathPredicates', {});
    const _methodPredicates = get(predicates, 'MethodPredicates', []);
    const _headerPredicates = get(predicates, 'HeaderPredicates', []);
    const _queryPredicates = get(predicates, 'QueryPredicates', []);
    const _isCollapsed =
      _methodPredicates.length > 0 || _headerPredicates.length > 0 || _queryPredicates.length > 0;

    setIsCollapsed(_isCollapsed);
    const _source = get(ingress, 'SourceType', Source.sae);
    setSource(_source);
    getNacosClusters(_source);
    const _instanceId = get(ingress, 'NacosInstanceId');
    setNacosInstanceId(_instanceId);
    getNacosNamespaces(_source, _instanceId);
    const _nacosNamespaceId = get(ingress, 'NacosNamespaceId') || NACOS_DEFAULT_NAMESPACEID;
    setNacosNamespaceId(_nacosNamespaceId);

    // 如果是 mse 再次请求获取新的app
    getApplications(_source, _instanceId, _nacosNamespaceId);

    const _scene = get(ingress, 'DestinationType', Scene.alone);
    setScene(_scene);

    const services = get(ingress, 'RouteServices', []);
    const _services = map(services, (service) => ({
      AppId: get(service, 'AppId'),
      ServiceId: get(service, 'ServiceName'),
      ServiceProtocol: get(service, 'ServiceProtocol', '-'),
      ServicePort: get(service, 'ServicePort'),
      ServiceGroup: get(service, 'ServiceGroup'),
      ServiceTag: get(service, 'ServiceTag'),
      Percent: get(service, 'Percent', 100),
    }));

    const _policies = get(ingress, 'Policies', {});
    const _timeout = get(_policies, 'Timeout.UnitNum', 60);

    const _fallback = get(ingress, 'Fallback', false);
    setFallback(_fallback);
    fallbackRef.current = _fallback;
    setIsExpended(_fallback);
    const fallbackServices = [get(ingress, 'FallbackServices', {})];
    const _fallbackServices = map(fallbackServices, (service) => ({
      AppId: get(service, 'AppId'),
      ServiceId: get(service, 'ServiceName'),
      ServiceProtocol: get(service, 'ServiceProtocol', '-'),
      ServicePort: get(service, 'ServicePort'),
      ServiceGroup: get(service, 'ServiceGroup'),
      ServiceTag: get(service, 'ServiceTag'),
      Percent: get(service, 'Percent', 100),
    }));

    field.setValues({
      GatewayUniqueId: _gatewayId,
      DomainIdList: get(ingress, 'DomainIdList', []),
      Path: get(_pathPredicates, 'Path'),
      Type: get(_pathPredicates, 'Type'),
      MethodPredicates: _methodPredicates,
      HeaderPredicates: _headerPredicates,
      QueryPredicates: _queryPredicates,
      SourceType: _source,
      NacosInstanceId: _instanceId,
      NacosNamespaceId: _nacosNamespaceId,
      DestinationType: get(ingress, 'DestinationType', Scene.alone),
      Services: _services,
      Timeout: _timeout,
      FallbackServices: _fallbackServices,
    });
  };

  const getGateways = () => {
    if (!VpcId) return Promise.resolve([]);
    return services
      .getListGateway({
        params: {
          PageNumber: 1,
          PageSize: 100,
          FilterParams: {
            Vpc: VpcId,
          },
        },
        customErrorHandle: (error, _p, cb) => {
          const forbidden = isForbidden(error.code);
          setAuthedMSE(!forbidden);
          if (!forbidden) cb?.();
        },
      })
      .then((res) => {
        const { Data = {} } = res || {};
        const { Result = [] } = Data;
        const _gateways = filter(Result, {
          Status: 2,
        })
          .map(gateway => ({
            ...gateway,
            // label: gateway.Name,
            label: (
              <>
                <span>{gateway.Name}</span>
                <CndTag tagText={gateway.GatewayVersion} tagType="safe" style={{ marginLeft: 4 }} />
              </>
            ),
            value: gateway.GatewayUniqueId,
          }))
          .filter(gateway => {
            if (gateway?.MseVersion !== 'mse_premium') {
              if (networkType === Network.public) {
                return gateway?.InternetSlb?.length > 0;
              }
              if (networkType === Network.private) {
                return gateway?.Slb?.length > 0;
              }
            } else {
              // mse专业版逻辑处理
              const accessNetWork = map(get(gateway, 'GatewayEntry', []), 'NetType') || [];
              return (
                (networkType === Network.public && includes(accessNetWork, 'PUB_NET')) ||
                (networkType === Network.private && includes(accessNetWork, 'PRIVATE_NET'))
              );
            }

            return true;
          });
        // @ts-ignore
        setGateways(_gateways);
        return _gateways;
      })
      .catch(() => []);
  };

  const getDomains = (gatewayId) => {
    return services
      .getDomainList({
        params: {
          PageNumber: 1,
          PageSize: 100,
          GatewayUniqueId: gatewayId,
          FilterParams: {
            GatewayUniqueId: gatewayId,
          },
        },
        customErrorHandle: (error, _p, cb) => {
          if (error.code === 'NotFound') {
            setDisableSubmit(true);
            setSubmitTip(intl('saenext.components.route-create.MseForwardform.TheMseGatewayMayBe'));
          } else {
            cb?.();
          }
        },
      })
      .then((res) => {
        const { Data = [] } = res || {};
        const _domains = map(Data, (domain) => ({
          ...domain,
          value: domain.Id,
          label:
            domain.Name === '*'
              ? intl(
                  'saenext.components.route-create.MseForwardform.RecommendedForTestScenariosOnly',
                )
              : domain.Name,
        }));
        forEach(_domains, (domain) => {
          domainsRef.current[domain.Id] = domain.Name;
        });
        // @ts-ignore
        setDomains(_domains);
        return _domains;
      })
      .catch(() => []);
  };

  // MSE Nacos 实例
  const getNacosClusters = async (_source) => {
    if (_source === Source.sae) return;
    const res = await services.listMseNacosInstances({
      params: {
        PageNum: 1,
        PageSize: 99,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        setAuthedMSE(!forbidden);
        if (!forbidden) cb?.();
      },
    });
    const { Data = [] } = res || {};
    // 过滤当天命名空间 绑定的同一个 Vpc 下面的资源
    const data = filter(Data, (cluster) => {
      const { VpcId: clusterVpcId } = cluster;
      return clusterVpcId === VpcId || !VpcId;
    });
    const _clusters = map(data, (item) => ({
      ...item,
      label: item?.ClusterAliasName,
      value: item?.InstanceId,
    }));
    // @ts-ignore
    setClusters(_clusters);
    forEach(_clusters, (cluster) => {
      const { InstanceId } = cluster;
      clustersRef.current.set(InstanceId, cluster);
    });
  };

  // MSE Nacos 命名空间
  const getNacosNamespaces = async (_source, instanceId) => {
    if (!instanceId) return;
    if (_source === Source.sae) return;
    const res = await services.listMseNacosNamespaces({
      PageNum: 1,
      PageSize: 99,
      InstanceId: instanceId,
    });
    const { Data = [] } = res || {};
    const _namespaces = map(Data, (item) => ({
      ...item,
      label: item?.NamespaceShowName,
      value: item?.Namespace || NACOS_DEFAULT_NAMESPACEID,
    }));
    // @ts-ignore
    setNamespaces(_namespaces);
  };

  const getApplications = (_source?, nacosInstance?, nacosNamespace?) => {
    // RegistryType 0 sae nacos 应用
    // RegistryType 2 mse nacos 应用
    const _registryType = SourceCodeMap[_source];
    if (_source === Source.mse && (!nacosInstance || !nacosNamespace)) {
      return Promise.resolve([]);
    }
    return services
      .getApplications({
        params: {
          NamespaceId: namespaceId,
          RegistryType: _registryType,
          NacosInstanceId: nacosInstance,
          NacosNamespaceId: nacosNamespace === NACOS_DEFAULT_NAMESPACEID ? '' : nacosNamespace,
        },
      })
      .then((res) => {
        const { Data: applications = [] } = res || {};
        const _applications = map(applications, (item) => ({
          ...item,
          label: item?.AppName,
          value: item?.AppId,
        }));
        setApplications(_applications);
        return _applications;
      })
      .catch(() => []);
  };

  const rowsValidator = (rule, val, callback) => {
    if (val?.length > 0) {
      const isHasEmpty = val.some((v) => {
        return !v.Key || !v.Type || !v.Value;
      });
      const isHasRegFail = val.some((v) => {
        return v.Type === 'ERGULAR' && v.hasOwnProperty('RegCheck') && !v.RegCheck;
      });
      if (isHasEmpty) {
        callback(intl('saenext.components.route-create.MseForwardform.TheRuleConditionDataIs'));
      } else if (isHasRegFail) {
        callback(true);
      } else {
        callback();
      }
    } else {
      callback();
    }
  };

  return (
    <Form field={field}>
      <Form.Item
        required
        label={
          <UnAuthedLabel
            text={intl('saenext.components.route-create.MseForwardform.GatewayInstance')}
            authed={authedMSE}
            authKey="AliyunMSEReadOnlyAccess"
          />
        }
      >
        <RefreshSelect
          {...init('GatewayUniqueId', {
            rules: [
              {
                required: true,
                message: intl(
                  'saenext.components.route-create.MseForwardform.SelectAGatewayInstance',
                ),
              },
            ],

            props: {
              onChange: (gatewayId, __, _gateway) => {
                setGateway(_gateway);
                getDomains(gatewayId);
              },
            },
          })}
          disabled={disabled}
          dataSource={gateways}
          placeholder={intl(
            'saenext.components.route-create.MseForwardform.SelectAGatewayInstance',
          )}
          fetchMethod={getGateways}
          external={{
            width: 196,
            label: intl('saenext.components.route-create.MseForwardform.CreateAnMseCloudNative'),
            url: `${CachedData.confLink('feature:mse:url')}/#/microgw?region=${regionId}`,
          }}
        />
      </Form.Item>
      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.DomainName')}
        help={intl('saenext.components.route-create.MseForwardform.SelectTheDomainNameThat')}
      >
        <RefreshSelect
          {...init('DomainIdList', {
            rules: [
              {
                required: true,
                message: intl('saenext.components.route-create.MseForwardform.SelectADomainName'),
              },
            ],
          })}
          mode="multiple"
          hasSelectAll={intl('saenext.components.route-create.MseForwardform.SelectAll')}
          placeholder={intl('saenext.components.route-create.MseForwardform.SelectADomainName')}
          dataSource={domains}
          fetchMethod={() => getDomains(get(gateway, 'GatewayUniqueId'))}
          external={{
            width: 196,
            label: intl('saenext.components.route-create.MseForwardform.CreateADomainName'),
            url: `${CachedData.confLink('feature:mse:url')}/#/gateway/domain?Name=${get(
              gateway,
              'Name',
            )}&Id=${get(gateway, 'GatewayUniqueId')}`,
          }}
        />
      </Form.Item>

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.Path')}
        help={intl('saenext.components.route-create.MseForwardform.EnterTheRequestPathAnd')}
      >
        <Row style={{ height: 32 }}>
          <Col span={6}>
            <Form.Item>
              <Select
                {...init('Type', {
                  initValue: Match.pre,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.route-create.MseForwardform.SelectAPathMatchingCondition',
                      ),
                    },
                  ],

                  props: {
                    onChange: (val: Match) => setMatch(val),
                  },
                })}
                style={{ width: '100%' }}
                dataSource={MatchRules}
              />
            </Form.Item>
          </Col>
          <Col span={18} style={{ paddingLeft: 10 }}>
            <Form.Item required style={{ display: 'inline-block', width: 'calc(100% - 100px)' }}>
              <RegularTest
                {...init('Path', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.route-create.MseForwardform.EnterAPathMatchingValue',
                      ),
                    },
                  ],
                })}
                trim
                showLimitHint
                maxLength={8192}
                placeholder={intl(
                  'saenext.components.route-create.MseForwardform.EnterAMatchingPathSuch',
                )}
                inputType={match === Match.ergula ? 'reg' : 'normal'}
                getRegState={(val) => setPathRegState(val)}
              />
            </Form.Item>
            <Form.Item style={{ display: 'inline-block', verticalAlign: 'top', marginRight: -8 }}>
              {/* @ts-ignore */}
              <Checkbox.Group
                {...init('IgnoreCase', {
                  initValue: true,
                })}
                className="ml-l"
              >
                {/* @ts-ignore */}
                <Checkbox id="ignoreCase" value={true}>
                  {intl('saenext.components.route-create.MseForwardform.CaseSensitive')}
                </Checkbox>
              </Checkbox.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form.Item>
      <Form.Item>
        <LinkButton onClick={() => setIsCollapsed(!isCollapsed)}>
          <span className="mr-s">
            {intl('saenext.components.route-create.MseForwardform.MoreMatchingRules')}
          </span>
          {isCollapsed ? <Icon size="xs" type="arrow-up" /> : <Icon size="xs" type="arrow-down" />}
        </LinkButton>
      </Form.Item>

      <Form.Item
        label={intl('saenext.components.route-create.MseForwardform.Method')}
        hidden={!isCollapsed}
        help={intl('saenext.components.route-create.MseForwardform.SelectTheRequestMethodThat')}
      >
        <StaticCheckSelect
          {...init('MethodPredicates')}
          hasSelectAll={intl('saenext.components.route-create.MseForwardform.SelectAll')}
          dataSource={Methods}
          placeholder={intl('saenext.components.route-create.MseForwardform.SelectARequestMethod')}
        />
      </Form.Item>

      <Form.Item
        label={intl('saenext.components.route-create.MseForwardform.RequestHeader')}
        hidden={!isCollapsed}
      >
        <span style={{ display: 'inline-block', fontSize: 12, color: '#888' }}>
          {intl('saenext.components.route-create.MseForwardform.EnterTheRequestHeadersAnd')}
        </span>
        <RequestCheckRows
          {...init('HeaderPredicates', {
            initValue: [],
            rules: [
              {
                validator: rowsValidator,
              },
            ],
          })}
          paramType={Params.header}
        />
      </Form.Item>
      <Form.Item
        label={intl('saenext.components.route-create.MseForwardform.RequestParametersQuery')}
        hidden={!isCollapsed}
      >
        <span style={{ display: 'inline-block', fontSize: 12, color: '#888' }}>
          {intl('saenext.components.route-create.MseForwardform.EnterTheRequestParametersAnd')}
        </span>
        <RequestCheckRows
          {...init('QueryPredicates', {
            initValue: [],
            rules: [
              {
                validator: rowsValidator,
              },
            ],
          })}
          paramType={Params.query}
        />
      </Form.Item>

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.ServiceSource')}
      >
        <Radio.Group
          {...init('SourceType', {
            initValue: Source.sae,
            rules: [
              {
                required: true,
                message: intl(
                  'saenext.components.route-create.MseForwardform.SelectAServiceSource',
                ),
              },
            ],

            props: {
              onChange: (val: Source) => {
                setSource(val);
                getNacosClusters(val);
                getApplications(val);
              },
            },
          })}
        >
          <Radio id="sae" value={Source.sae}>
            {intl('saenext.components.route-create.MseForwardform.SaeBuiltInNacos')}
          </Radio>
          <Radio id="mse" value={Source.mse}>
            MSE Nacos
          </Radio>
        </Radio.Group>
      </Form.Item>

      {source === Source.mse ? (
        <div className="justify-center">
          <Form.Item
            required
            label={
              <UnAuthedLabel
                text={intl('saenext.components.route-create.MseForwardform.MseNacosInstances')}
                authed={authedMSE}
                authKey="AliyunMSEReadOnlyAccess"
              />
            }
            style={{ flex: 1, paddingRight: 16 }}
          >
            <Select
              {...init('NacosInstanceId', {
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.route-create.MseForwardform.SelectMseNacosInstance',
                    ),
                  },
                ],

                props: {
                  onChange: (val: string, __, item: any) => {
                    setIntranetDomain(item.IntranetDomain);
                    setNacosInstanceId(val);
                    getNacosNamespaces(source, val);
                    getApplications(source, val, nacosNamespaceId);
                  },
                },
              })}
              style={{ width: '100%' }}
              dataSource={clusters}
              placeholder={intl(
                'saenext.components.route-create.MseForwardform.SelectMseNacosInstance',
              )}
            />
          </Form.Item>
          <Form.Item
            required
            label={intl('saenext.components.route-create.MseForwardform.MseNacosNamespace')}
            style={{ flex: 1, paddingLeft: 16 }}
          >
            <Select
              {...init('NacosNamespaceId', {
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.route-create.MseForwardform.SelectMseNacosNamespace',
                    ),
                  },
                ],

                props: {
                  onChange: (val: string) => {
                    setNacosNamespaceId(val);
                    getApplications(source, nacosInstanceId, val);
                  },
                },
              })}
              style={{ width: '100%' }}
              dataSource={namespaces}
              placeholder={intl('saenext.components.route-create.MseForwardform.MseNacosNamespace')}
            />
          </Form.Item>
        </div>
      ) : null}

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.UsageScenarios')}
        help={
          <div className="justify-center">
            <span>
              {intl('saenext.components.route-create.MseForwardform.SelectAScenarioForThis')}
            </span>
            <Button
              text
              type="primary"
              style={{ fontWeight: 'normal' }}
              onClick={() =>
                window.open(CachedData.confLink('help:mse:overview-of-routing-methods'), '_blank')
              }
            >
              <span>{intl('saenext.components.route-create.MseForwardform.LearnMore')}</span>
              <Icon type="external_link" />
            </Button>
          </div>
        }
      >
        <SceneSelect
          {...init('DestinationType', {
            initValue: Scene.alone,
            rules: [
              {
                required: true,
                message: intl('saenext.components.route-create.MseForwardform.SelectAScenario'),
              },
            ],

            props: {
              onChange: (val: string) => {
                setScene(val);
                field.setValues({ Services: [] });
              },
              fromType:'mse',
            },
          })}
        />
      </Form.Item>

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.BackendServices')}
      >
        <ServiceCheckRows
          {...init('Services', {
            initValue: [],
          })}
          useSence={scene}
          regionId={regionId}
          renderSource={source}
          baseContainer={baseContainer}
          namespaceId={namespaceId}
          applications={applications}
          // validator={serviceRowsValidator}
          refreshApps={() => getApplications(source, nacosInstanceId, nacosNamespaceId)}
          nacosInstanceId={nacosInstanceId}
          nacosNamespaceId={nacosNamespaceId}
        />
      </Form.Item>

      <Form.Item>
        <LinkButton onClick={() => setIsExpended(!isExpended)}>
          <span className="mr-s">
            {intl('saenext.components.route-create.MseForwardform.AdvancedConfiguration')}
          </span>
          {isExpended ? <Icon size="xs" type="arrow-up" /> : <Icon size="xs" type="arrow-down" />}
        </LinkButton>
      </Form.Item>

      <Form.Item label="Fallback" hidden={!isExpended}>
        <InitFallbackRows
          {...init('FallbackServices', {
            initValue: [],
          })}
          useSence={scene}
          regionId={regionId}
          baseContainer={baseContainer}
          renderSource={source}
          fallback={fallback}
          updateFallback={(val) => {
            setFallback(val);
            fallbackRef.current = val;
          }}
          applications={applications}
          refreshApps={() => getApplications(source, nacosInstanceId, nacosNamespaceId)}
          nacosInstanceId={nacosInstanceId}
          nacosNamespaceId={nacosNamespaceId}
        />
      </Form.Item>

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.TimeoutPeriodSeconds')}
        help={intl('saenext.components.route-create.MseForwardform.EnterATimeoutPeriodIf')}
        hidden={!isExpended}
      >
        <NumberPicker
          {...init('Timeout', {
            initValue: 60,
          })}
          min={0}
          max={3600}
          style={{
            width: scene === Scene.alone ? '29%' : scene === Scene.multiple ? '25%' : '33%',
          }}
        />
      </Form.Item>
    </Form>
  );
});
