import { intl } from '@ali/cnd';
import React from 'react';
import { Source, ApigSource, Scene, NACOS_DEFAULT_NAMESPACEID, SourceCodeMap } from './config';
import OneRouteRows from './OneRouteRows';
import RatioRouteRows from './RatioRouteRows';
import LabelRouteRows from './LabelRouteRows';
import services from "~/services";
import { map, get } from 'lodash';
import { Message, Button, Icon } from '@ali/cnd';
import CachedData from '../../../cache/common';

type Props = {
  regionId: string;
  namespaceId: string;
  useSence: string;
  baseContainer: string;
  renderSource: Source | ApigSource;
  applications: { label: string; value: string; [key: string]: any }[];
  refreshApps: () => Promise<any[]>;
  nacosInstanceId: string;
  nacosNamespaceId: string;
  validator?: (val) => void;
};

export default (props: Props) => {
  const {
    renderSource,
    useSence,
    namespaceId,
    applications,
    refreshApps,
    nacosInstanceId,
    nacosNamespaceId,
    ...otherProps
  } = props;

  const getServices = (appId, callback) => {
    const _registryType = SourceCodeMap[renderSource] as 0 | 2 | 9;
    return services
      .getServices({
        params: {
          AppId: appId,
          ServiceType: _registryType === 9 ? 'k8sService' : 'springCloud',
          RegistryType: _registryType,
          NacosInstanceId: nacosInstanceId,
          NacosNamespaceId: nacosNamespaceId === NACOS_DEFAULT_NAMESPACEID ? '' : nacosNamespaceId,
          PageNumber: 1,
          PageSize: 99,
        },
      })
      .then((res) => {
        const { Data = [] } = res || {};
        const _services = map(Data, (item) => ({
          ...item,
          label: item.ServiceName,
          value: item.ServiceName,
        }));
        callback && callback(_services);
        return _services;
      })
      .catch(() => {
        callback && callback([]);
        return [];
      });
  };

  const getServicePorts = (appId, service, callback) => {
    const _registryType = SourceCodeMap[renderSource] as 0 | 2 | 9;
    return services
      .getServicePorts({
        params: {
          AppId: appId,
          ServiceType: _registryType === 9 ? 'k8sService' : 'springCloud',
          RegistryType: _registryType,
          NacosInstanceId: nacosInstanceId,
          NacosNamespaceId: nacosNamespaceId === NACOS_DEFAULT_NAMESPACEID ? '' : nacosNamespaceId,
          ServiceName: get(service, 'ServiceName', ''),
          ServiceGroup: get(service, 'ServiceGroup', ''),
          ServiceVersion: get(service, 'ServiceVersion', ''),
        },
      })
      .then((res) => {
        const { Data: _service } = res || {};
        const port = get(_service, 'ServicePorts', []);
        const _ports = map(port, (item) => ({
          label: item,
          value: Number(item),
        }));
        callback &&
          callback(
            {
              ..._service,
              ServiceGroup: get(_service, 'ServiceGroup', []) || get(service, 'ServiceGroup', ''),
            },
            _ports,
          );
        return _ports;
      })
      .catch(() => {
        callback && callback({}, []);
        return [];
      });
  };

  let tip = <></>;
  let message = <></>;
  let checkRows = <></>;
  switch (useSence) {
    case Scene.alone:
      tip = (
        <span>
          {intl(
            'saenext.components.route-create.ServiceCheckRows.SelectAnAssociatedApplicationService',
          )}
        </span>
      );

      checkRows = (
        <OneRouteRows
          {...otherProps}
          applications={applications}
          refreshApps={refreshApps}
          refreshServices={getServices}
          refreshServicePorts={getServicePorts}
        />
      );

      break;
    case Scene.multiple:
      tip = (
        <span>
          {intl(
            'saenext.components.route-create.ServiceCheckRows.SelectTheAssociatedApplicationService',
          )}
        </span>
      );

      message = (
        <>
          <span>
            {intl(
              'saenext.components.route-create.ServiceCheckRows.ProportionalRoutingIsOnlyValid',
            )}
          </span>
          <Button
            text
            type="primary"
            style={{ fontWeight: 'normal' }}
            onClick={() =>
              window.open(
                CachedData.confLink('help:mse:implement-an-end-to-end-canary-release-by-using-mse'),
                '_blank',
              )
            }
          >
            <span>{intl('saenext.components.route-create.ServiceCheckRows.LearnMore')}</span>
            <Icon type="external_link" />
          </Button>
        </>
      );

      checkRows = (
        <RatioRouteRows
          {...otherProps}
          applications={applications}
          refreshApps={refreshApps}
          refreshServices={getServices}
          refreshServicePorts={getServicePorts}
        />
      );

      break;
    case Scene.label:
      tip = (
        <span>
          {intl(
            'saenext.components.route-create.ServiceCheckRows.SelectTheAssociatedBackendService',
          )}
        </span>
      );

      message = (
        <>
          <span>
            {intl('saenext.components.route-create.ServiceCheckRows.LabelBasedRoutingIsOnly')}
          </span>
          <Button
            text
            type="primary"
            style={{ fontWeight: 'normal' }}
            onClick={() =>
              window.open(
                CachedData.confLink('help:sae:full-link-gray-scale-based-on-mse'),
                '_blank',
              )
            }
          >
            <span>{intl('saenext.components.route-create.ServiceCheckRows.LearnMore')}</span>
            <Icon type="external_link" />
          </Button>
        </>
      );

      checkRows = (
        <LabelRouteRows
          {...otherProps}
          applications={applications}
          refreshApps={refreshApps}
          refreshServices={getServices}
          refreshServicePorts={getServicePorts}
        />
      );

      break;
    default:
      checkRows = <></>;
      break;
  }

  return (
    <>
      <span className="mb-s" style={{ display: 'inline-block', fontSize: 12, color: '#888' }}>
        {tip}
      </span>
      {useSence !== Scene.alone ? (
        <Message type="notice" className="mb-s">
          {message}
        </Message>
      ) : null}

      {checkRows}
    </>
  );
};
