import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import {
  Button,
  Loading,
  Grid,
  Field,
  Form,
  Input,
  Radio,
  Icon,
  Message,
  ToolTipCondition,
} from '@ali/cnd';
import './index.less';
import { get, noop, isEmpty } from 'lodash';
import services from "~/services";
import MseForwardform from './MseForwardform';
import ClbForwardform from './ClbForwardform';
import AlbForwardform from './AlbForwardform';
import ApigwForwardform from './ApigwForwardform';
import ConflictDetection from './ConflictDetection';
import CachedData from '../../../cache/common';

type Props = {
  regionId: string;
  namespaceId: string;
  loadBalance?: string;
  networkType?: string;
  ingressId?: number;
  history?: any;
  location?: any;
  baseContainer?: string;
  okCallback?: any;
  cancelCallback?: any;
  isNewSae?: boolean;
};

const { Row, Col } = Grid;
export const Network = {
  public: 'internet',
  private: 'intranet',
};
export const Gateway = {
  apig: 'apig',
  mse: 'mse',
  clb: 'clb',
  alb: 'alb',
};
const GatewayChain = {
  [Gateway.mse]: {
    docs: intl('saenext.components.route-create.MseCloudNativeGatewayUsage'),
    href: CachedData.confLink('help:sae:limits-of-mse-gateway'),
  },
  [Gateway.clb]: {
    docs: intl('saenext.components.route-create.ClbUsageConstraints'),
    href: CachedData.confLink('help:sae:limits-of-clb'),
  },
  [Gateway.alb]: {
    docs: intl('saenext.components.route-create.AlbUsageConstraints'),
    href: CachedData.confLink('help:sae:limits-of-alb'),
  },
  [Gateway.apig]: {
    docs: intl('saenext.components.route-create.CloudNativeApiGatewayUsage'),
    href: CachedData.confLink('help:sae:set-up-apigw-for-app'),
  },
};

const RouteCreator = (props: Props) => {
  const {
    regionId,
    namespaceId,
    loadBalance,
    ingressId,
    baseContainer = 'v2',
    okCallback = noop,
    cancelCallback = noop,
    isNewSae = false,
  } = props;
  const mseRef = useRef(null);
  const clbRef = useRef(null);
  const albRef = useRef(null);
  const apigRef = useRef(null);
  const field = Field.useField();
  const { init, validate } = field;
  const apigOpenRegions = get(window, 'ALIYUN_CONSOLE_GLOBAL.apigOpenRegions', []);
  const isShowApig = apigOpenRegions.includes(regionId);
  // 前置条件
  const [namespaceResources, setNamespaceResources] = useState({});
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [submitTip, setSubmitTip] = useState('');

  const [loading, setLoading] = useState(false);
  const [networkType, setNetworkType] = useState(Network.public);
  const [ingressType, setIngressType] = useState(Gateway.alb);
  const [isProcessing, setIsProcessing] = useState(false);
  const [routeId, setRouteId] = useState('');
  const [checkVisible, setCheckVisible] = useState(false);
  const [checkResult, setCheckResult] = useState<any>({});
  const [httpApiRouteInfo, setHttpApiRouteInfo] = useState<any>({});
  const isEdit = !!ingressId;

  const clbHideRegion = get(window, 'ALIYUN_CONSOLE_GLOBAL.clbHideRegions', []);
  const clb_gray = get(window, 'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS.clb-gray');
  const clbHide = clbHideRegion.includes(regionId);

  useEffect(() => {
    getNamespaceResources();

    if (!isEdit) return;
    initIngressResources();
  }, []);

  useEffect(() => {
    if (field.getValues().name) {
      field.validate('name');
    }
  }, [ingressType]);

  const getNamespaceResources = async () => {
    const res = await services.getNamespaceResources({
      RegionId: regionId,
      NamespaceId: namespaceId,
    });
    const { Data: _namespaceResources = {} } = res || {};
    setNamespaceResources(_namespaceResources);
  };

  const initIngressResources = async () => {
    setLoading(true);
    const ingress = await getIngress();
    const _name = get(ingress, 'Description') || get(ingress, 'Name');
    field.setValues({
      name: _name,
      slbType: props.networkType,
      ingressType: props.loadBalance,
    });
    // @ts-ignore
    props.networkType && setNetworkType(props.networkType);
    // @ts-ignore
    setIngressType(loadBalance);
    switch (loadBalance) {
      case Gateway.mse:
        // @ts-ignore
        mseRef.current.setValues(ingress);
        break;
      case Gateway.clb:
        // @ts-ignore
        clbRef.current.setValues(ingress);
        break;
      case Gateway.alb:
        // @ts-ignore
        albRef.current.setValues(ingress);
        break;
      case Gateway.apig:
        // @ts-ignore
        apigRef.current.setValues(ingress);
        if (!props?.networkType) {
          field.setValues({
            slbType: get(ingress, 'AddressType'),
          });
          setNetworkType(get(ingress, 'AddressType'));
        }
        break;
    }
    setLoading(false);
  };

  const getIngress = async () => {
    let ingress = {};
    switch (loadBalance) {
      case Gateway.clb:
      case Gateway.alb:
        ingress = await getSlbIngress();
        break;
      case Gateway.mse:
        ingress = await getMseIngress();
        break;
      case Gateway.apig:
        ingress = await getHttpApiRoute(ingressId);
        break;
      default:
        return null;
    }
    return ingress;
  };

  const getSlbIngress = async () => {
    const res = await services.getIngressInfo({
      RegionId: regionId,
      IngressId: ingressId,
    });
    const { Data: _ingress = {} } = res || {};
    return _ingress;
  };

  const getMseIngress = async () => {
    const res = await services.describeMSEIngress(
      {
        RegionId: regionId,
        IngressId: ingressId,
      },
      (error, data, cb) => {
        // 对于没有查到 MSE 网关的吞掉报错弹窗。
        if (
          error.code === 'InvalidParameter.WithMessage' &&
          error.message.indexOf('mse route not exist') > -1
        ) {
          setDisableSubmit(true);
          setSubmitTip(intl('saenext.components.route-create.TheMseGatewayMayBe'));
        } else {
          cb?.();
        }
      },
    );
    const { Data: _ingress = {} } = res || {};
    setRouteId(get(_ingress, 'RouteId', ''));
    return _ingress;
  };

  const handleSubmit = async () => {
    validate(async (error, values) => {
      if (error) return;
      let commonParams = {};
      let res = { success: false, params: null };
      switch (ingressType) {
        case Gateway.mse:
          // @ts-ignore
          res = await mseRef.current.submit();
          commonParams = {
            NamespaceId: namespaceId,
            Name: get(values, 'name'),
            AddressType: get(values, 'slbType', Network.public),
          };
          break;
        case Gateway.clb:
          // @ts-ignore
          res = await clbRef.current.submit();
          commonParams = {
            NamespaceId: namespaceId,
            Description: get(values, 'name'),
            LoadBalanceType: get(values, 'ingressType', Gateway.clb),
          };
          break;
        case Gateway.alb:
          // @ts-ignore
          res = await albRef.current.submit();
          commonParams = {
            NamespaceId: namespaceId,
            Description: get(values, 'name'),
            LoadBalanceType: get(values, 'ingressType', Gateway.alb),
          };
          break;
        case Gateway.apig:
          res = await apigRef.current.submit();
          commonParams = {
            Name: get(values, 'name'),
            AddressType: get(values, 'slbType', Network.public),
          };
          break;
      }
      if (!res.success) return;
      setIsProcessing(true);
      const params = {
        ...commonParams,
        // @ts-ignore
        ...res.params,
      };
      isEdit ? doEditSubmit(params) : doCreateSubmit(params);
    });
  };

  const doCreateSubmit = async (params) => {
    let res = {};
    switch (ingressType) {
      case Gateway.clb:
      case Gateway.alb:
        res = await services.createIngressGateway({
          ...params,
          RegionId: regionId,
        });
        break;
      case Gateway.mse:
        res = await services.createMSEIngress({
          ...params,
        });
        break;
      case Gateway.apig:
        // 创建
        const createRes = await services.createHttpApiRoute({
          params: { NamespaceId: namespaceId },
          content: {
            ...params,
            NamespaceId: namespaceId,
          },
        });
        const _ingressId = get(createRes, 'Data.IngressId', '');
        if (_ingressId) {
          const httpApiRouteRes = await getHttpApiRoute(_ingressId);
          if (httpApiRouteRes?.RouteId && httpApiRouteRes?.HttpApiId) {
            // 冲突检测
            await handleDetectHttiApiConflicts(
              httpApiRouteRes?.HttpApiId,
              httpApiRouteRes?.RouteId,
              httpApiRouteRes?.IngressId,
            );
          }
        } else {
          setIsProcessing(false);
        }
        break;
      default:
        return null;
    }
    if (ingressType !== Gateway.apig) {
      setIsProcessing(false);
      if (!res) return;
      const ingressId = get(res, 'Data.IngressId', '');
      ingressId && Message.success(intl('saenext.components.route-create.TheRoutingRuleHasBeen'));
      ingressId && okCallback();
    }
  };

  const doEditSubmit = async (params) => {
    let res = {};
    switch (ingressType) {
      case Gateway.clb:
      case Gateway.alb:
        const { Rules = '' } = params;
        delete params.Rules;
        res = await services.updateIngressGateway({
          params: {
            ...params,
            IngressId: ingressId,
            RegionId: regionId,
          },
          content: {
            Rules: Rules,
          },
        });
        break;
      case Gateway.mse:
        res = await services.updateMSEIngress({
          ...params,
          IngressId: ingressId,
          RouteId: routeId,
        });
        break;
      case Gateway.apig:
        const updateRes = await services.updateHttpApiRoute({
          params: { NamespaceId: namespaceId, IngressId: ingressId },
          content: {
            ...params,
            IngressId: ingressId,
            NamespaceId: namespaceId,
          },
        });
        if (updateRes?.Data?.IngressId) {
          // 冲突检测
          await handleDetectHttiApiConflicts();
        } else {
          setIsProcessing(false);
        }
        break;
      default:
        return null;
    }
    if (ingressType !== Gateway.apig) {
      setIsProcessing(false);
      if (!res) return;
      const _ingressId = get(res, 'Data.IngressId', '');
      _ingressId &&
        Message.success(intl('saenext.components.route-create.TheRoutingRuleHasBeen.1'));
      _ingressId && okCallback();
    }
  };
  const getHttpApiRoute = async (_ingressId) => {
    const res = await services.getHttpApiRoute({
      params: { IngressId: _ingressId, NamespaceId: namespaceId },
      customErrorHandle: (err, data, callback) => {
        setIsProcessing(false);
      },
    });
    const { Data = {} } = res;
    setHttpApiRouteInfo(Data);
    return Data;
  };

  // 路由冲突检测
  const handleDetectHttiApiConflicts = async (httpApiId?, routeId?, ingressId?) => {
    setCheckResult({});
    const res = await services.detectHttpApiConflicts({
      params: {
        httpApiId: isEdit ? httpApiRouteInfo.HttpApiId : httpApiId,
      },
      content: {
        routeId: isEdit ? httpApiRouteInfo.RouteId : routeId,
      },
    });
    const { items = [], gatewayId = '' } = res?.data;
    if (!isEmpty(items)) {
      setCheckResult({ items, gatewayId });
      setCheckVisible(true);
    } else {
      handlePublish(ingressId);
    }
  };

  // 发布api路由
  const handlePublish = async (id?) => {
    const _ingressId = isEdit ? ingressId : id || httpApiRouteInfo?.IngressId;
    const res = await services.deployHttpApiRoute({
      params: {
        NamespaceId: namespaceId,
        IngressId: _ingressId,
      },
    });
    if (res?.Success) {
      setIsProcessing(false);
      okCallback();
    } else {
      setIsProcessing(false);
    }
  };

  return (
    <Loading visible={loading} style={{ width: '100%', height: '100%' }}>
      <Row className="flow-content">
        <Col span={16}>
          <Form field={field}>
            <Form.Item required label={intl('saenext.components.route-create.RouteName')}>
              <Input
                {...init('name', {
                  rules: [
                    {
                      required: true,
                      message: intl('saenext.components.route-create.EnterARouteName'),
                    },
                    {
                      message:
                        ingressType === 'alb' || ingressType === 'clb'
                          ? intl('saenext.components.route-create.ItCanContainUppercaseAnd')
                          : intl('saenext.components.route-create.ItCanContainLowercaseLetters'),
                      pattern:
                        ingressType === 'alb' || ingressType === 'clb'
                          ? /^[a-zA-Z0-9]([-a-zA-Z0-9]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([-a-zA-Z0-9]*[a-zA-Z0-9])?)*$/
                          : /^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$/,
                    },
                  ],
                })}
                showLimitHint
                minLength={1}
                maxLength={63}
                placeholder={intl('saenext.components.route-create.EnterARouteName')}
              />
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.components.route-create.NetworkType')}
              help={intl('saenext.components.route-create.TheTrafficForwardedThroughThe')}
            >
              <Radio.Group
                {...init('slbType', {
                  initValue: Network.public,
                  rules: [
                    {
                      required: true,
                      message: intl('saenext.components.route-create.SelectANetworkType'),
                    },
                  ],

                  props: {
                    onChange: (val: string) => setNetworkType(val),
                  },
                })}
                disabled={isEdit}
              >
                <Radio id="internet" value={Network.public}>
                  {intl('saenext.components.route-create.PublicNetwork')}
                </Radio>
                <Radio id="intranet" value={Network.private}>
                  {intl('saenext.components.route-create.PrivateNetwork')}
                </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              required
              label={intl('saenext.components.route-create.GatewayType')}
              help={
                <div className="justify-center">
                  <span>{intl('saenext.components.route-create.SelectAGatewayTypeBefore')}</span>
                  {ingressType !== 'apig' && (
                    <Button
                      text
                      type="primary"
                      style={{ fontWeight: 'normal' }}
                      onClick={() => window.open(GatewayChain[ingressType].href, '_blank')}
                    >
                      <span>{GatewayChain[ingressType].docs}</span>
                      <Icon type="external_link" />
                    </Button>
                  )}
                </div>
              }
            >
              <Radio.Group
                {...init('ingressType', {
                  initValue: Gateway.alb,
                  rules: [
                    {
                      required: true,
                      message: intl('saenext.components.route-create.SelectAGatewayType'),
                    },
                  ],

                  props: {
                    onChange: (val: string) => setIngressType(val),
                  },
                })}
                disabled={isEdit}
              >
                <Radio id="alb" value={Gateway.alb}>
                  {intl('saenext.components.route-create.AlbAppliedLoadBalancing')}
                </Radio>
                {isShowApig && (
                  <Radio id="apig" value={Gateway.apig}>
                    {intl('saenext.components.route-create.CloudNativeApiGateway')}
                  </Radio>
                )}
                <Radio id="mse" value={Gateway.mse}>
                  {intl('saenext.components.route-create.MseCloudNativeGateway')}
                </Radio>
                <ToolTipCondition
                  show={!clb_gray}
                  tip={intl('saenext.components.route-create.SaeClosedTheClbRoute')}
                >
                  <Radio
                    id="clb"
                    value={Gateway.clb}
                    disabled={!clb_gray}
                    className={clbHide ? 'none' : ''}
                  >
                    {intl('saenext.components.route-create.ClbTraditionalLoadBalancing')}
                  </Radio>
                </ToolTipCondition>
              </Radio.Group>
            </Form.Item>

            <ApigwForwardform
              ref={apigRef}
              mode={isEdit}
              regionId={regionId}
              namespaceId={namespaceId}
              networkType={networkType}
              baseContainer={baseContainer}
              visible={ingressType === Gateway.apig}
              namespaceResources={namespaceResources}
              setDisableSubmit={setDisableSubmit}
              setSubmitTip={setSubmitTip}
            />

            <MseForwardform
              ref={mseRef}
              mode={isEdit}
              regionId={regionId}
              namespaceId={namespaceId}
              networkType={networkType}
              baseContainer={baseContainer}
              visible={ingressType === Gateway.mse}
              namespaceResources={namespaceResources}
              setDisableSubmit={setDisableSubmit}
              setSubmitTip={setSubmitTip}
            />

            <ClbForwardform
              ref={clbRef}
              mode={isEdit}
              regionId={regionId}
              namespaceId={namespaceId}
              networkType={networkType}
              baseContainer={baseContainer}
              setDisableSubmit={setDisableSubmit}
              setSubmitTip={setSubmitTip}
              visible={ingressType === Gateway.clb}
              namespaceResources={namespaceResources}
            />

            <AlbForwardform
              ref={albRef}
              mode={isEdit}
              regionId={regionId}
              namespaceId={namespaceId}
              networkType={networkType}
              baseContainer={baseContainer}
              visible={ingressType === Gateway.alb}
              namespaceResources={namespaceResources}
            />
          </Form>
        </Col>
      </Row>

      <div className="fixed-footer">
        <ToolTipCondition show={disableSubmit} tip={submitTip} align="t">
          <Button
            type="primary"
            disabled={disableSubmit}
            loading={isProcessing}
            onClick={handleSubmit}
          >
            {intl('saenext.components.route-create.Save')}
          </Button>
        </ToolTipCondition>
        <Button
          className="ml-l"
          onClick={() => {
            cancelCallback && cancelCallback();
          }}
        >
          {intl('saenext.components.route-create.Cancel')}
        </Button>
      </div>
      {checkVisible && (
        <ConflictDetection
          type="HttpApi"
          checkResult={checkResult}
          setCheckVisible={() => {
            setCheckVisible(false);
            setIsProcessing(false);
          }}
          handleSubmit={() => {
            handlePublish();
          }}
          requestParams={{
            httpApiId: httpApiRouteInfo?.HttpApiId,
            httpApiName: httpApiRouteInfo?.HttpApiName,
            routeId: httpApiRouteInfo?.RouteId,
            gatewayRouteName: httpApiRouteInfo?.Name,
            gatewayId: httpApiRouteInfo?.GatewayId,
          }}
          regionId={regionId}
        />
      )}
    </Loading>
  );
};

export default RouteCreator;
