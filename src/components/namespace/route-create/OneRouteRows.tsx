import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { Input, Select, Icon, LinkButton, Grid } from '@ali/cnd';
import { head, get, map, forEach } from 'lodash';
import CachedData from '../../../cache/common';

const { Row, Col } = Grid;

const fields = {
  appId: 'AppId',
  serviceId: 'ServiceId',
  protocol: 'ServiceProtocol',
  port: 'ServicePort',
};

export default (props) => {
  const {
    regionId,
    baseContainer,
    onChange,
    value = [],
    applications,
    refreshApps,
    refreshServices,
    refreshServicePorts,
    validator,
  } = props;

  const _value = head(value) || {};
  const [loading, setLoading] = useState(false);
  const appletRef = useRef({});

  useEffect(() => {
    forEach(applications, (app) => {
      appletRef.current[app.value] = app;
    });
  }, [applications]);

  const handleChange = async (key, _value, item?) => {
    // @ts-ignore
    const _newValue = head(value) || {};
    _newValue[key] = _value;

    switch (key) {
      case fields.appId:
        // 清空后面的值
        // @ts-ignore
        _newValue.services = [];
        _newValue[fields.serviceId] = '';
        _newValue[fields.protocol] = '';
        // @ts-ignore
        _newValue.ports = [];
        _newValue[fields.port] = '';

        // @ts-ignore
        _newValue.sloading = true;
        refreshServices(_value, (services) => {
          onChange([
            {
              // @ts-ignore
              ..._newValue,
              services: services,
              sloading: false,
            },
          ]);
        });
        break;
      case fields.serviceId:
        // 清空后面的值
        _newValue[fields.protocol] = '';
        // @ts-ignore
        _newValue.ports = [];
        _newValue[fields.port] = '';

        // @ts-ignore
        _newValue.ploading = true;
        // 实际为获取服务详情
        refreshServicePorts(
          // @ts-ignore
          _newValue.AppId,
          item,
          (service, ports) => {
            onChange([
              {
                // @ts-ignore
                ..._newValue,
                ports: ports,
                ploading: false,
                ServiceGroup: get(service, 'ServiceGroup', ''),
                ServiceProtocol: get(service, 'ServiceProtocol', ''),
              },
            ]);
          },
        );
        break;
    }
    onChange([_newValue]);
    validator && validator([_newValue]);
  };

  const handleMenuProps = {
    footer: (
      <div style={{ padding: '0 4px', textAlign: 'left', borderTop: '1px solid #eee' }}>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={() => {
            const uri =
              baseContainer === 'v2'
                ? `${CachedData.confLink('feature:saenext:url')}/${regionId}/create-app/micro`
                : `${CachedData.confLink('feature:sae:url')}/overview#/AppList/CreateApp?regionId=${regionId}`;
            window.open(uri, '_blank');
          }}
        >
          <Icon size="xs" type="external_link" />
          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.OneRouteRows.CreateAnApplication')}
          </span>
        </LinkButton>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={async () => {
            setLoading(true);
            refreshApps && (await refreshApps());
            setLoading(false);
          }}
        >
          {loading ? <Icon size="xs" type="loading" /> : <Icon size="xs" type="refresh" />}

          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.OneRouteRows.Refresh')}
          </span>
        </LinkButton>
      </div>
    ),
  };

  return (
    <>
      <Row style={{ color: '#555', lineHeight: '32px' }}>
        <Col span="7">{intl('saenext.components.route-create.OneRouteRows.ApplicationName')}</Col>
        <Col span="7" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.OneRouteRows.ServiceName')}
        </Col>
        <Col span="5" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.OneRouteRows.ServiceAgreement')}
        </Col>
        <Col span="5" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.OneRouteRows.ServicePort')}
        </Col>
      </Row>
      <Row>
        <Col span={7}>
          <Select
            style={{ width: '100%' }}
            value={get(_value, fields.appId)}
            onChange={(val) => handleChange(fields.appId, val)}
            menuProps={handleMenuProps}
            placeholder={intl('saenext.components.route-create.OneRouteRows.SelectAnApplication')}
            showSearch
          >
            {map(applications, (item) => (
              <Select.Option value={item.value}>{item?.label}</Select.Option>
            ))}
          </Select>
        </Col>
        <Col span={7} style={{ paddingLeft: 10 }}>
          <Select
            style={{ width: '100%' }}
            value={get(_value, fields.serviceId)}
            // @ts-ignore
            state={get(_value, 'sloading', false) ? 'loading' : null}
            onChange={(val, __, item) => handleChange(fields.serviceId, val, item)}
            placeholder={intl('saenext.components.route-create.OneRouteRows.SelectAService')}
            showSearch
            dataSource={get(_value, 'services', [])}
          />
        </Col>
        <Col span={5} style={{ paddingLeft: 10 }}>
          <Input
            readOnly
            style={{ width: '100%' }}
            value={get(_value, fields.protocol)}
            onChange={(val) => handleChange(fields.protocol, val)}
            placeholder={intl('saenext.components.route-create.OneRouteRows.AutomaticRead')}
          />
        </Col>
        <Col span={5} style={{ paddingLeft: 10 }}>
          <Select
            placeholder={intl('saenext.components.route-create.OneRouteRows.ServicePort')}
            style={{ width: '100%' }}
            value={get(_value, fields.port)}
            // @ts-ignore
            state={get(_value, 'ploading', false) ? 'loading' : null}
            onChange={(val) => handleChange(fields.port, val)}
          >
            {map(get(_value, 'ports', []), (item) => (
              // @ts-ignore
              // <Select.Option value={item.value}>
              //   {!item?.value && (
              //     <Balloon.Tooltip
              //       align="r"
              //       trigger={
              //         <div>
              //           {item?.label} <Icon type="help" className="help-icon" />
              //         </div>
              //       }
              //     >
              //       动态端口适用于服务端口会动态变化的场景，但不支持有多端口的服务，请勿在多端口场景使用。
              //     </Balloon.Tooltip>
              //   )}
              //   {/* @ts-ignore */}
              //   {item?.value && item.label}
              // </Select.Option>
              <Select.Option value={item.value}>{item?.value && item.label}</Select.Option>
            ))}
          </Select>
        </Col>
      </Row>
    </>
  );
};
