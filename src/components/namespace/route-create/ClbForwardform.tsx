import { intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import RefreshSelect from './RefreshSelect';
import { Truncate, Field, Form, Radio, NumberPicker, Description } from '@ali/cnd';
import { get, forEach, map, head, isEmpty } from 'lodash';
import { Network, Gateway } from './index';
import services from "~/services";
import { lowerFirstData } from '../../../utils/transfer-data';
import moment from 'moment';
import InitTransmitRows from './InitTransmitRows';
import TransmitCheckRows from './TreeTransmitRows';
import { isForbidden } from '../../../utils/authUtils';
import { UnAuthedLabel } from '../../shared/unauthedLabel';
import CachedData from '../../../cache/common';

type Props = {
  mode: boolean; // true: 编辑  false: 新增
  regionId: string;
  visible: boolean;
  namespaceId: string;
  networkType: string;
  baseContainer: string;
  namespaceResources: {
    VpcId?: string;
    [key: string]: any;
  };
  setDisableSubmit: (val: boolean) => void;
  // init: <T>(name: string, option?: InitOption, props?: {}) => InitResult<T>;
  [key: string]: any;
};

export enum Protocals {
  http = 'HTTP',
  https = 'HTTPS',
}

const TAGS = {
  'acs:ros:stackId': 'acs:ros:stackId',
  'kubernetes.cluster.id': 'kubernetes.cluster.id',
  'kubernetes.cluster.name': 'kubernetes.cluster.name',
  'kubernetes.do.not.delete': 'kubernetes.do.not.delete',
  'edas.serverless.do.not.delete': 'edas.serverless.do.not.delete',
};

export default forwardRef((props: Props, ref) => {
  const {
    regionId,
    namespaceId,
    visible,
    networkType,
    baseContainer,
    namespaceResources = {
      VpcId: '',
    },
    mode: disabled,
    setDisableSubmit,
    setSubmitTip,
    // @ts-ignore
    ...childProps
  } = props;
  if (!visible) return null;

  const { VpcId } = namespaceResources;
  const field = Field.useField();
  const { init } = field;
  const [loadBalancers, setLoadBalancers] = useState([]);
  const [slbExist, setSlbExist] = useState(true);
  const [protocal, setProtocal] = useState(Protocals.http);
  const [certificates, setCertificates] = useState([]);
  const [applications, setApplications] = useState<any>([]);
  const [instanceId, setInstanceId] = useState('');
  const [port, setPort] = useState('');
  const [authedSLB, setAuthedSLB] = useState(true);
  const [isOnePage, setIsOnePage] = useState(true);

  useEffect(() => {
    getLoadBalancers();
  }, [networkType]);

  useEffect(() => {
    if (!slbExist) {
      setDisableSubmit(true);
      setSubmitTip(intl('saenext.components.route-create.ClbForwardform.TheClbInstanceMayBe'));
    }
  }, [slbExist]);

  const getSlbExist = async (slbId) => {
    await services.getLoadBalanceInfo({
      params: {
        RegionId: regionId,
        LoadBalancerId: slbId,
      },
      customErrorHandle: (error, _b, cb) => {
        if (error.code === 'InvalidLoadBalancerId.NotFound') {
          setSlbExist(false);
        }
      },
    });
  };

  useEffect(() => {
    getApplications();
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      submit,
      setValues,
    }),
    [],
  );

  const submit = () => {
    const promise = new Promise<{ success: boolean; params: any }>(async (resolve) => {
      field.validate((errors, values) => {
        if (errors) {
          resolve({ success: false, params: null });
          return;
        }
        doSubmit(values, resolve);
      });
    });

    return promise;
  };

  const doSubmit = (values, resolve) => {
    const { slb, port, protocalType, ssl, ruleList = [], defaultRouter = [] } = values;

    const _certId = protocalType === Protocals.https ? ssl : '';

    let _rules = [];
    forEach(ruleList, (item) => {
      let _domain = '';
      for (let i = 0; i < item.length; i++) {
        const { domain } = item[i];
        if (domain) {
          _domain = domain;
          break;
        }
      }
      let children = map(item, (rule) => ({
        domain: _domain,
        path: rule.path,
        appId: rule.appId,
        containerPort: rule.containerPort,
        backendProtocol: get(rule, 'backendProtocol', 'http'),
      }));
      // @ts-ignore
      _rules = _rules.concat(children);
    });

    const _headRule = head(defaultRouter);
    const _defaultRule = {
      appId: get(_headRule, 'defaultAppId'),
      containerPort: get(_headRule, 'defaultPort'),
      backendProtocol: get(_headRule, 'defaultBackendProtocol', 'http'),
    };

    const _params = {
      SlbId: slb,
      ListenerProtocol: protocalType,
      ListenerPort: port,
      CertId: _certId,
      Rules: JSON.stringify(_rules),
      DefaultRule: JSON.stringify(_defaultRule),
    };
    resolve({ success: true, params: _params });
  };

  const setValues = async (ingress) => {
    const _slb = get(ingress, 'SlbId');
    const _port = get(ingress, 'ListenerPort');
    const _protocal = get(ingress, 'ListenerProtocol', Protocals.http);
    // 如果为 https
    _protocal === Protocals.https && getCertificates();
    const _rules = get(ingress, 'Rules', []);
    const _defaultRule = get(ingress, 'DefaultRule', {});
    const _ruleList = [];
    const domainKeys = {};
    for (let i = 0; i < _rules.length; i++) {
      const rule = _rules[i];
      const {
        Domain: domain,
        Path: path,
        AppId: appId,
        ContainerPort: containerPort,
        BackendProtocol: backendProtocol,
      } = rule;
      if (!domainKeys[domain]) {
        domainKeys[domain] = [];
      }
      domainKeys[domain].push({ domain, path, appId, containerPort, backendProtocol });
    }
    for (let key in domainKeys) {
      // @ts-ignore
      _ruleList.push(domainKeys[key]);
    }

    field.setValues({
      slb: _slb,
      protocalType: get(ingress, 'ListenerProtocol', Protocals.http),
      port: _port,
      ssl: get(ingress, 'CertId', ''),
      ruleList: _ruleList,
      defaultRouter: [
        {
          defaultAppId: get(_defaultRule, 'AppId'),
          defaultPort: get(_defaultRule, 'ContainerPort'),
          defaultBackendProtocol: get(_defaultRule, 'BackendProtocol', 'http'),
        },
      ],
    });
    setProtocal(_protocal);
    setPort(_port);
    setInstanceId(_slb);
    // 在编辑状态下调接口查看是否存在该clb
    if (disabled) {
      await getSlbExist(_slb);
    }
  };

  // clb
  const getLoadBalancers = (search?: string) => {
    const params = {
      PageNumber: 1,
      PageSize: 50,
      AddressType: networkType,
      RegionId: regionId,
    };
    let authedSLB = true;
    if (VpcId && networkType === Network.private) {
      Reflect.set(params, 'VpcId', VpcId);
    }
    if (!isEmpty(search)) {
      Reflect.set(params, 'LoadBalancerName', search);
    }
    return services
      .getLoadBalancers({
        params,
        customErrorHandle: (error, _p, cb) => {
          const forbidden = isForbidden(error.code);
          authedSLB = !forbidden;
          setAuthedSLB(authedSLB);
          if (!forbidden) cb?.();
        },
      })
      .then((res) => {
        const { LoadBalancers = {}, TotalCount = 0 } = res || {};
        const { LoadBalancer = [] } = LoadBalancers;
        if (TotalCount > 50) setIsOnePage(false);
        return LoadBalancer;
      })
      .then(async (loadBalancers) => {
        const res = await filterBalancers(loadBalancers, authedSLB);
        const _loadBalancers = map(res, (balancer) => {
          return {
            // @ts-ignore
            label: `${balancer.LoadBalancerName}(${balancer.Address})`,
            // @ts-ignore
            value: balancer.LoadBalancerId,
            // @ts-ignore
            ...balancer,
          };
        });
        // @ts-ignore
        setLoadBalancers(_loadBalancers);
        return _loadBalancers;
      })
      .catch(() => []);
  };

  const filterBalancers = async (loadBalancers, authedSLB) => {
    const validBalancers = [];
    for await (const balancer of loadBalancers) {
      let valid = true;
      const Tag = get(balancer, 'Tags.Tag', []);
      forEach(Tag, (tag) => {
        const { TagKey } = tag;
        if (TAGS[TagKey]) {
          valid = false;
        }
      });
      if (valid) {
        const res = await services.getLoadBalanceInfo({
          params: {
            RegionId: regionId,
            LoadBalancerId: balancer.LoadBalancerId,
          },
        });
        const { LoadBalancerSpec } = res;
        if (LoadBalancerSpec) {
          // @ts-ignore
          validBalancers.push(balancer);
        }
      }
    }

    return validBalancers;
  };

  const getCertificates = (search?: string) => {
    // if (val === Protocals.http) {
    //   return Promise.resolve([]);
    // }
    return services
      .getServerCertificates({
        params: {
          RegionId: regionId,
        },
        customErrorHandle: (error, _p, cb) => {
          const forbidden = isForbidden(error.code);
          setAuthedSLB(!forbidden);
          if (!forbidden) cb?.();
        },
      })
      .then((res) => {
        const { ServerCertificates = {} } = res;
        const { ServerCertificate = [] } = ServerCertificates;
        let serverCertificate = lowerFirstData(ServerCertificate);
        if (!isEmpty(search)) {
          serverCertificate = serverCertificate.filter((item) => {
            return (
              item.serverCertificateName.includes(search) ||
              item.aliCloudCertificateName.includes(search)
            );
          });
        }
        const _certificates = serverCertificate.map((item) => ({
          label: (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ color: '#333' }}>{item.serverCertificateName}</span>
              <span style={{ marginLeft: 4, marginRight: 4 }}>/</span>
              <span style={{ color: '#666' }}>{item.aliCloudCertificateName}</span>
            </div>
          ),

          value: item.serverCertificateId,
          ...item,
        }));
        setCertificates(_certificates);
        return _certificates;
      })
      .catch(() => []);
  };

  const getApplications = () => {
    return services
      .getApplicationList({
        RegionId: regionId,
        NamespaceId: namespaceId,
      })
      .then((res) => {
        const { Data = {} } = res || {};
        const { Applications = [] } = Data;
        const _applications = map(Applications, (item) => ({
          ...item,
          label: item?.AppName,
          value: item?.AppId,
        }));
        setApplications(_applications);
        return _applications;
      })
      .catch(() => []);
  };

  // @ts-ignore
  const handleItemRender = (dataSource) => {
    const items = [
      {
        dataIndex: 'serverCertificateName',
        label: intl('saenext.components.route-create.ClbForwardform.CertificateName'),
        span: 24,
      },
      {
        dataIndex: 'serverCertificateId',
        label: intl('saenext.components.route-create.ClbForwardform.CertificateId'),
        span: 24,
        render: (val) => (
          <Truncate tooltipMaxWidth={400} type="width" threshold={160}>
            {val}
          </Truncate>
        ),
      },
      {
        dataIndex: 'commonName',
        label: intl('saenext.components.route-create.ClbForwardform.CertificateDomainName'),
        span: 24,
      },
      {
        dataIndex: 'expireTime',
        label: intl('saenext.components.route-create.ClbForwardform.ExpirationTime'),
        span: 24,
        render: (val) => moment(val).format('YYYY-MM-DD HH:mm'),
      },
    ];

    return (
      <Description
        // @ts-ignore
        items={items}
        dataSource={dataSource}
      />
    );
  };

  const transmitValidator = (rule, val, callback) => {
    if (!port) callback();
    if (val?.length > 0) {
      let isHasEmpty = false;
      for (let i = 0; i < val.length; i++) {
        const value = val[i];
        if (value.length > 0) {
          isHasEmpty = value.some((v) => {
            return !v.domain || !v.port || !v.appId || !v.path || !v.containerPort;
          });
        }
      }
      if (isHasEmpty) {
        callback(intl('saenext.components.route-create.ClbForwardform.TheForwardingPolicyDataIs'));
      } else {
        callback();
      }
    } else {
      callback();
    }
  };

  const initTransmitValidator = (rule, val, callback) => {
    if (!port) callback();
    if (val?.length > 0) {
      const isHasEmpty = val.some((v) => {
        return !v.ip || !v.port || !v.defaultAppId || !v.defaultPort;
      });
      if (isHasEmpty) {
        callback(intl('saenext.components.route-create.ClbForwardform.TheForwardingPolicyDataIs'));
      } else {
        callback();
      }
    } else {
      callback();
    }
  };

  return (
    <Form field={field}>
      <Form.Item
        required
        label={
          <UnAuthedLabel
            text={intl('saenext.components.route-create.ClbForwardform.ClbOriginalSlbInstance')}
            authed={authedSLB}
            authKey="AliyunSLBReadOnlyAccess"
          />
        }
      >
        <RefreshSelect
          {...init('slb', {
            rules: [
              {
                required: true,
                message: intl('saenext.components.route-create.ClbForwardform.SelectAClbInstance'),
              },
            ],

            props: {
              onChange: (val) => {
                setInstanceId(String(val));
              },
            },
          })}
          autoSearch={{
            enabled: true,
            placeholder: intl('saenext.components.route-create.ClbForwardform.YouCanSearchByClb'),
          }}
          disabled={disabled}
          dataSource={loadBalancers}
          placeholder={intl('saenext.components.route-create.ClbForwardform.SelectAClbInstance.1')}
          fetchMethod={getLoadBalancers}
          external={{
            width: 150,
            label: intl('saenext.components.route-create.ClbForwardform.CreateAClbInstance'),
            url: `${CachedData.confLink('feature:slbnew.url')}/slb/${regionId}/slbs`,
          }}
        />
      </Form.Item>
      <div className="justify-center" style={{ alignItems: 'baseline' }}>
        <Form.Item
          required
          label={intl('saenext.components.route-create.ClbForwardform.FrontendProtocolType')}
        >
          <Radio.Group
            {...init('protocalType', {
              initValue: Protocals.http,
              rules: [
                {
                  required: true,
                  message: intl(
                    'saenext.components.route-create.ClbForwardform.SelectAFrontendProtocolType',
                  ),
                },
              ],

              props: {
                onChange: (val: Protocals) => {
                  setProtocal(val);
                  val === Protocals.https && getCertificates();
                },
              },
            })}
          >
            <Radio id="http" style={{ minWidth: 80 }} value={Protocals.http}>
              HTTP
            </Radio>
            <Radio id="https" style={{ minWidth: 80 }} value={Protocals.https}>
              HTTPS
            </Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          required
          label={intl('saenext.components.route-create.ClbForwardform.AccessPort')}
          help={intl('saenext.components.route-create.ClbForwardform.EnterAValidPort')}
          style={{ marginLeft: 80 }}
        >
          <NumberPicker
            {...init('port', {
              rules: [
                {
                  required: true,
                  message: intl('saenext.components.route-create.ClbForwardform.EnterAnAccessPort'),
                },
              ],

              props: {
                onChange: (val) => setPort(String(val)),
              },
            })}
            min={1}
            max={65535}
            style={{ width: 180 }}
          />
        </Form.Item>
      </div>
      <>
        {protocal === Protocals.https ? (
          <Form.Item
            required
            label={
              <UnAuthedLabel
                text={intl('saenext.components.route-create.ClbForwardform.SslCertificate')}
                authed={authedSLB}
                authKey="AliyunSLBReadOnlyAccess"
              />
            }
          >
            <RefreshSelect
              {...init('ssl', {
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.route-create.ClbForwardform.SelectAnSslCertificate',
                    ),
                  },
                ],
              })}
              dataSource={certificates}
              placeholder={intl(
                'saenext.components.route-create.ClbForwardform.SelectAnSslCertificate',
              )}
              fetchMethod={getCertificates}
              autoSearch={{
                enabled: true,
                placeholder: intl(
                  'saenext.components.route-create.ClbForwardform.YouCanSearchBySsl',
                ),
              }}
              external={{
                width: 150,
                label: intl(
                  'saenext.components.route-create.ClbForwardform.CreateAnSslCertificate',
                ),
                url: `${CachedData.confLink('feature:slbnew.url')}/slb/${regionId}/certs`,
              }}
            />
          </Form.Item>
        ) : null}
      </>

      <Form.Item
        required={false}
        label={intl('saenext.components.route-create.ClbForwardform.CustomForwardingPolicy')}
      >
        {/* @ts-ignore */}
        <TransmitCheckRows
          {...init('ruleList', {
            initValue: [],
            rules: [
              {
                validator: transmitValidator,
              },
            ],
          })}
          target={Gateway.clb}
          port={port}
          regionId={regionId}
          baseContainer={baseContainer}
          protocal={protocal}
          applications={applications}
          refreshApps={getApplications}
        />
      </Form.Item>
      <Form.Item
        required
        label={intl('saenext.components.route-create.ClbForwardform.DefaultForwardingPolicy')}
      >
        {/* @ts-ignore */}
        <InitTransmitRows
          {...init('defaultRouter', {
            initValue: [],
            rules: [
              {
                validator: initTransmitValidator,
              },
            ],
          })}
          target={Gateway.clb}
          instanceId={instanceId}
          port={port}
          regionId={regionId}
          baseContainer={baseContainer}
          protocal={protocal}
          loadBalancers={loadBalancers}
          applications={applications}
          refreshApps={getApplications}
        />
      </Form.Item>
    </Form>
  );
});
