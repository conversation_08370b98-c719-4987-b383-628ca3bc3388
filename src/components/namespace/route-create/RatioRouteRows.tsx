import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { Input, Select, Icon, LinkButton, Grid, Balloon, Button, NumberPicker } from '@ali/cnd';
import { get, map, isEmpty, forEach } from 'lodash';
import CachedData from '../../../cache/common';

const { Row, Col } = Grid;

const fields = {
  appId: 'AppId',
  serviceId: 'ServiceId',
  protocol: 'ServiceProtocol',
  port: 'ServicePort',
  percent: 'Percent',
};

export default (props) => {
  const {
    regionId,
    baseContainer,
    onChange,
    value = [],
    applications,
    refreshApps,
    refreshServices,
    refreshServicePorts,
    validator,
  } = props;

  const [loading, setLoading] = useState(false);
  const appletRef = useRef({});

  useEffect(() => {
    forEach(applications, (app) => {
      appletRef.current[app.value] = app;
    });
  }, [applications]);

  useEffect(() => {
    if (!isEmpty(value)) return;
    onChange([{ AppId: '', ServiceId: '', ServiceProtocol: '', ServicePort: '', Percent: '' }]);
  }, []);

  const handleChange = async (index, key, _value, item?) => {
    // @ts-ignore
    const _newValue = [...value];
    _newValue[index][key] = _value;
    switch (key) {
      case fields.appId:
        // 清空后面的值
        _newValue[index].services = [];
        _newValue[index][fields.serviceId] = '';
        _newValue[index][fields.protocol] = '';
        _newValue[index].ports = [];
        _newValue[index][fields.port] = '';
        _newValue[index][fields.percent] = '';

        _newValue[index].sloading = true;
        refreshServices(_value, (services) => {
          _newValue[index].services = services;
          _newValue[index].sloading = false;
          onChange(_newValue);
        });
        break;
      case fields.serviceId:
        // 清空后面的值
        _newValue[index][fields.protocol] = '';
        _newValue[index].ports = [];
        _newValue[index][fields.port] = '';
        _newValue[index][fields.percent] = '';

        _newValue[index].ploading = true;
        refreshServicePorts(
          // @ts-ignore
          value[index].AppId,
          item,
          (service, ports) => {
            _newValue[index].ports = ports;
            (_newValue[index].ServiceGroup = get(service, 'ServiceGroup', '')),
              (_newValue[index].ServiceProtocol = get(service, 'ServiceProtocol', ''));
            _newValue[index].ploading = false;
            onChange(_newValue);
          },
        );
        break;
    }
    onChange(_newValue);
    validator && validator(_newValue);
  };

  const handleMenuProps = {
    footer: (
      <div style={{ padding: '0 4px', textAlign: 'left', borderTop: '1px solid #eee' }}>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={() => {
            const uri =
              baseContainer === 'v2'
                ? `${CachedData.confLink('feature:saenext:url')}/${regionId}/create-app/micro`
                : `${CachedData.confLink('feature:sae:url')}/overview#/AppList/CreateApp?regionId=${regionId}`;
            window.open(uri, '_blank');
          }}
        >
          <Icon size="xs" type="external_link" />
          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.RatioRouteRows.CreateAnApplication')}
          </span>
        </LinkButton>
        <LinkButton
          style={{ marginLeft: 10 }}
          onClick={async () => {
            setLoading(true);
            refreshApps && (await refreshApps());
            setLoading(false);
          }}
        >
          {loading ? <Icon size="xs" type="loading" /> : <Icon size="xs" type="refresh" />}

          <span style={{ marginLeft: 2 }}>
            {intl('saenext.components.route-create.RatioRouteRows.Refresh')}
          </span>
        </LinkButton>
      </div>
    ),
  };

  return (
    <>
      <Row style={{ color: '#555', lineHeight: '32px' }}>
        <Col span="6">{intl('saenext.components.route-create.RatioRouteRows.ApplicationName')}</Col>
        <Col span="6" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.RatioRouteRows.ServiceName')}
        </Col>
        <Col span="4" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.RatioRouteRows.ServiceAgreement')}
        </Col>
        <Col span="4" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.RatioRouteRows.ServicePort')}
        </Col>
        <Col span="4" style={{ paddingLeft: 10 }}>
          {intl('saenext.components.route-create.RatioRouteRows.ServiceWeight')}
        </Col>
      </Row>
      {map(value, (item, index) => (
        <Row key={index} style={{ marginBottom: 10 }}>
          <Col span={6}>
            <Select
              style={{ width: '100%' }}
              value={get(item, fields.appId)}
              onChange={(val) => handleChange(index, fields.appId, val)}
              menuProps={handleMenuProps}
              placeholder={intl(
                'saenext.components.route-create.RatioRouteRows.SelectAnApplication',
              )}
              showSearch
            >
              {map(applications, (item) => (
                <Select.Option value={item.value}>{item?.label}</Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={6} style={{ paddingLeft: 10 }}>
            <Select
              style={{ width: '100%' }}
              value={get(item, fields.serviceId)}
              // @ts-ignore
              state={get(item, 'sloading', false) ? 'loading' : null}
              onChange={(val, __, item) => handleChange(index, fields.serviceId, val, item)}
              placeholder={intl('saenext.components.route-create.RatioRouteRows.SelectAService')}
              showSearch
              dataSource={get(item, 'services', [])}
            />
          </Col>
          <Col span={4} style={{ paddingLeft: 10 }}>
            <Input
              readOnly
              style={{ width: '100%' }}
              value={get(item, fields.protocol)}
              onChange={(val) => handleChange(index, fields.protocol, val)}
              placeholder={intl('saenext.components.route-create.RatioRouteRows.AutomaticRead')}
            />
          </Col>
          <Col span={4} style={{ paddingLeft: 10 }}>
            <Select
              placeholder={intl('saenext.components.route-create.RatioRouteRows.ServicePort')}
              style={{ width: '100%' }}
              value={get(item, fields.port)}
              // @ts-ignore
              state={get(item, 'ploading', false) ? 'loading' : null}
              onChange={(val) => handleChange(index, fields.port, val)}
            >
              {map(get(item, 'ports', []), (item) => (
                <Select.Option value={item.value}>
                  {!item?.value && (
                    <Balloon.Tooltip
                      align="r"
                      trigger={
                        <div>
                          {item?.label} <Icon type="help" className="help-icon" />
                        </div>
                      }
                    >
                      {intl(
                        'saenext.components.route-create.RatioRouteRows.DynamicPortsAreApplicableTo',
                      )}
                    </Balloon.Tooltip>
                  )}

                  {item?.value && item.label}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={4} style={{ paddingLeft: 10 }}>
            <NumberPicker
              min={0}
              max={100}
              innerAfter="%"
              value={get(item, fields.percent)}
              style={{ width: 'calc(100% - 20px)' }}
              onChange={(val) => handleChange(index, fields.percent, val)}
              placeholder={intl('saenext.components.route-create.RatioRouteRows.ServiceWeight')}
            />

            <Button
              text
              style={{ width: 20 }}
              disabled={value?.length > 1 ? false : true}
              onClick={() => {
                props.onChange(props.value.filter((i, idx) => idx !== index));
              }}
            >
              <Icon className="rows-delete" size="medium" style={{ marginLeft: 5 }} type="delete" />
            </Button>
          </Col>
        </Row>
      ))}

      {value?.length < 10 && (
        <div style={{ marginTop: 10 }}>
          <LinkButton
            onClick={() => {
              props.onChange([...value, {}]);
            }}
          >
            <Icon type="plus" size="xs" />
            <span style={{ marginLeft: 5 }}>
              {intl('saenext.components.route-create.RatioRouteRows.Add')}
            </span>
          </LinkButton>
        </div>
      )}
    </>
  );
};
