import { intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import {
  Button,
  Field,
  Grid,
  Form,
  Radio,
  Select,
  Icon,
  LinkButton,
  Checkbox,
  NumberPicker,
} from '@ali/cnd';
import RefreshSelect from './RefreshSelect';
import StaticCheckSelect from './StaticCheckSelect';
import SceneSelect from './SceneSelect';
import services from "~/services";
import { filter, map, get, head, forEach, toLower, includes } from 'lodash';
import RegularTest from './RegularTest';
import RequestCheckRows, { Params } from './RequestCheckRows';
import ServiceCheckRows from './ServiceCheckRows';
import InitFallbackRows from './InitFallbackRows';
import { isForbidden } from '../../../utils/authUtils';
import { UnAuthedLabel } from '../../shared/unauthedLabel';
import SelectCheckbox from '../../shared/SelectCheckbox';
import ExternalLink from '../../shared/ExternalLink';
import TagsGroup from '../../shared/TagsGroup';
import {
  Match,
  ApigSource,
  SourceCodeMap,
  Scene,
  ApigMatch,
  ApigMatchRules,
  Methods,
  NACOS_DEFAULT_NAMESPACEID,
  retryList,
} from './config';
import CachedData from '../../../cache/common';

type Props = {
  mode: boolean;
  regionId: string;
  visible: boolean;
  baseContainer: string;
  namespaceId: string;
  networkType: string;
  namespaceResources: {
    VpcId?: string;
    [key: string]: any;
  };
  // init: <T>(name: string, option?: InitOption, props?: {}) => InitResult<T>;
  [key: string]: any;
};

const { Row, Col } = Grid;

const matchRetryTip = (v) => {
  const curRetryItem = retryList.filter((item) => {
    return item.value === v;
  });
  return curRetryItem[0]?.tip;
};

const transferMatchTypeToApi = {
  PRE: ApigMatch.pre,
  EQUAL: ApigMatch.equal,
  ERGULAR: ApigMatch.ergula,
};
const transferMatchTypeToMse = {
  Prefix: Match.pre,
  Exact: Match.equal,
  Regex: Match.ergula,
};

export default forwardRef((props: Props, ref) => {
  const {
    regionId,
    namespaceId,
    visible,
    baseContainer,
    networkType,
    // init,
    namespaceResources = {
      VpcId: '',
    },
    mode: disabled,
    setDisableSubmit,
    setSubmitTip,
    // @ts-ignore
    ...childProps
  } = props;
  if (!visible) return null;
  const { VpcId } = namespaceResources;
  const field = Field.useField();
  const { init } = field;
  const [gateways, setGateways] = useState([]);
  const [domains, setDomains] = useState([]);
  const [applications, setApplications] = useState<any>([]);
  const [match, setMatch] = useState(ApigMatch.pre);
  // @ts-ignore
  const [pathRegState, setPathRegState] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [source, setSource] = useState(ApigSource.mse);
  const [scene, setScene] = useState(Scene.alone);
  // mse naocs 实例
  const [clusters, setClusters] = useState([]);
  const clustersRef = useRef(new Map());
  // mse naocs 命名空间
  const [namespaces, setNamespaces] = useState([]);
  const [nacosInstanceId, setNacosInstanceId] = useState('');
  const [nacosNamespaceId, setNacosNamespaceId] = useState('');
  const [isExpended, setIsExpended] = useState(false);
  const [fallback, setFallback] = useState(false);
  const fallbackRef = useRef(false);
  // @ts-ignore
  const [intranetDomain, setIntranetDomain] = useState('');
  const [authedApig, setAuthedApig] = useState(true);
  const [authedMSE, setAuthedMSE] = useState(true);
  const [retryEnable, setRetryEnable] = useState(true);
  const [timeoutEnable, setTimeoutEnable] = useState(true);
  const domainsRef = useRef({});
  const applicationsRef = useRef({});
  const [listDomainsAuth,setListDomainsAuth] = useState(true);

  useEffect(() => {
    getGateways();
  }, [VpcId, networkType]);

  useEffect(() => {
    getApplications(source);
    // 请求域名
    getDomains();
    if (!disabled) {
      getNacosClusters(source);
    }
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      submit,
      setValues,
    }),
    [],
  );

  const setValues = (ingress) => {
    const _gatewayId = get(ingress, 'GatewayId');
    const predicates = get(ingress, 'Predicates', {});
    const _pathPredicates = get(predicates, 'PathPredicates', {});
    const _methodPredicates = get(predicates, 'MethodPredicates', []);
    let _headerPredicates = get(predicates, 'HeaderPredicates', []);
    let _queryPredicates = get(predicates, 'QueryPredicates', []);
    // apig与mse共用组件, 二者路由条件的对应字段有区别
    forEach(_headerPredicates, (headerItem) => {
      headerItem.Type = transferMatchTypeToMse[headerItem.Type];
      headerItem.Key = headerItem.Name;
    });
    forEach(_queryPredicates, (queryItem) => {
      queryItem.Type = transferMatchTypeToMse[queryItem.Type];
      queryItem.Key = queryItem.Name;
    });
    const _isCollapsed =
      _methodPredicates.length > 0 || _headerPredicates.length > 0 || _queryPredicates.length > 0;

    setIsCollapsed(_isCollapsed);
    const _source = get(ingress, 'SourceType', ApigSource.mse);
    setSource(_source);
    getNacosClusters(_source);
    const _instanceId = get(ingress, 'NacosInstanceId');
    setNacosInstanceId(_instanceId);
    getNacosNamespaces(_source, _instanceId);
    const _nacosNamespaceId = get(ingress, 'NacosNamespaceId') || NACOS_DEFAULT_NAMESPACEID;
    setNacosNamespaceId(_nacosNamespaceId);

    getApplications(_source, _instanceId, _nacosNamespaceId);

    const _scene = get(ingress, 'DestinationType', Scene.alone);
    setScene(_scene);

    const services = get(ingress, 'Services', []);
    const _services = map(services, (service) => ({
      AppId: get(service, 'AppId'),
      ServiceId: get(service, 'ServiceName'),
      ServiceProtocol: get(service, 'ServiceProtocol', '-'),
      ServicePort: get(service, 'ServicePort'),
      ServiceGroup: get(service, 'ServiceGroup'),
      ServiceTag: get(service, 'ServiceTag'),
      Percent: get(service, 'ServiceWeight', 100),
    }));

    const _policies = get(ingress, 'Policies', {});
    const _timeout = get(_policies, 'Timeout.UnitNum', 60);
    const _fallback = get(_policies, 'Fallback.Enable', false);
    setFallback(_fallback);
    fallbackRef.current = _fallback;
    setIsExpended(_fallback);
    const fallbackServices = get(_policies, 'Fallback.Destinations', []);
    const _fallbackServices = map(fallbackServices, (service) => ({
      AppId: get(service, 'AppId'),
      ServiceId: get(service, 'ServiceName'),
      ServiceProtocol: get(service, 'ServiceProtocol', '-'),
      ServicePort: get(service, 'ServicePort'),
      ServiceGroup: get(service, 'ServiceGroup'),
      ServiceTag: get(service, 'ServiceTag'),
      Percent: get(service, 'ServiceWeight', 100),
    }));
    const _retryAttempts = get(_policies, 'Retry.Attempts');
    const _retryOn = get(_policies, 'Retry.RetryOn') || [];
    const _retryEnable = get(_policies, 'Retry.Enable', true);
    const _httpCodes = get(_policies, 'Retry.HttpCodes') || [];
    const _timeoutEnable = get(_policies, 'Timeout.Enable', true);
    setRetryEnable(_retryEnable);
    setTimeoutEnable(_timeoutEnable);
    field.setValues({
      gatewayId: _gatewayId,
      DomainIdList: map(get(ingress, 'Domains', []), (item) => item.DomainId),
      Path: get(_pathPredicates, 'Path'),
      Type: get(_pathPredicates, 'Type'),
      MethodPredicates: _methodPredicates,
      HeaderPredicates: _headerPredicates,
      QueryPredicates: _queryPredicates,
      SourceType: _source,
      NacosInstanceId: _instanceId,
      NacosNamespaceId: _nacosNamespaceId,
      DestinationType: get(ingress, 'DestinationType', Scene.alone),
      Services: _services,
      Timeout: _timeout,
      FallbackServices: _fallbackServices,
      RetryAttempts: _retryAttempts,
      RetryOn: _retryOn,
      HttpCodes: _httpCodes,
    });
  };

  const getGateways = () => {
    if (!VpcId) return Promise.resolve([]);
    return services
      .getHttpApiGateways({
        params: {
          pageSize: 200,
          pageNumber: 1,
        },
        customErrorHandle: (error, _p, cb) => {
          const forbidden = isForbidden(error.code);
          setAuthedApig(!forbidden);
          if (!forbidden) cb?.();
        },
      })
      .then((res) => {
        const list = res?.data?.items || [];
        const _gateways = filter(list, (item) => {
          if (item?.vpc?.vpcId === VpcId) {
            const lbAddressTypes = map(item?.loadBalancers, (lb) => toLower(lb.addressType));
            return lbAddressTypes.includes(toLower(networkType));
          }
        }).map((gateway) => {
          return {
            ...gateway,
            label: gateway.name,
            value: gateway.gatewayId,
          };
        });
        setGateways(_gateways);
        return _gateways;
      })
      .catch(() => []);
  };

  const getDomains = () => {
    return services
      .getHttpApiDomains({
        params: {
          pageNumber: 1,
          pageSize: 200
        },
        customErrorHandle: (error, _p, cb) => {
          const forbidden = isForbidden(error.code);
          setListDomainsAuth(!forbidden);
          if (!forbidden) cb?.();
        },
      })
      .then((res) => {
        const list = res?.data?.items || [];
        const _domains = map(list, (domain) => {
          return {
            ...domain,
            value: domain.domainId,
            label:
              domain.name === '*'
                ? intl(
                    'saenext.components.route-create.MseForwardform.RecommendedForTestScenariosOnly',
                  )
                : domain.name,
          };
        });
        forEach(_domains, (domain) => {
          domainsRef.current[domain.domainId] = domain.name;
        });
        setDomains(_domains);
        return _domains;
      })
      .catch(() => []);
  };

  // MSE Nacos 实例
  const getNacosClusters = async (_source) => {
    if (_source !== ApigSource.mse) return;
    const res = await services.listMseNacosInstances({
      params: {
        PageNum: 1,
        PageSize: 99,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        setAuthedMSE(!forbidden);
        if (!forbidden) cb?.();
      },
    });
    const { Data = [] } = res || {};
    // 过滤当天命名空间 绑定的同一个 Vpc 下面的资源
    const data = filter(Data, (cluster) => {
      const { VpcId: clusterVpcId } = cluster;
      return clusterVpcId === VpcId || !VpcId;
    });
    const _clusters = map(data, (item) => ({
      ...item,
      label: item?.ClusterAliasName,
      value: item?.InstanceId,
    }));
    // @ts-ignore
    setClusters(_clusters);
    forEach(_clusters, (cluster) => {
      const { InstanceId } = cluster;
      clustersRef.current.set(InstanceId, cluster);
    });
  };

  // MSE Nacos 命名空间
  const getNacosNamespaces = async (_source, instanceId) => {
    if (!instanceId) return;
    if (_source !== ApigSource.mse) return;
    const res = await services.listMseNacosNamespaces({
      PageNum: 1,
      PageSize: 99,
      InstanceId: instanceId,
    });
    const { Data = [] } = res || {};
    const _namespaces = map(Data, (item) => ({
      ...item,
      label: item?.NamespaceShowName,
      value: item?.Namespace || NACOS_DEFAULT_NAMESPACEID,
    }));
    // @ts-ignore
    setNamespaces(_namespaces);
  };

  const getApplications = (_source?, nacosInstance?, nacosNamespace?) => {
    // RegistryType 0 sae nacos 应用
    // RegistryType 2 mse nacos 应用
    // RegistryType 9 k8s service 应用
    const _registryType = SourceCodeMap[_source];
    if (_source === ApigSource.mse && (!nacosInstance || !nacosNamespace)) {
      return Promise.resolve([]);
    }
    return services
      .getApplications({
        params: {
          NamespaceId: namespaceId,
          RegistryType: _registryType,
          NacosInstanceId: nacosInstance,
          NacosNamespaceId: nacosNamespace === NACOS_DEFAULT_NAMESPACEID ? '' : nacosNamespace,
        },
      })
      .then((res) => {
        const { Data: applications = [] } = res || {};
        const _applications = map(applications, (item) => ({
          ...item,
          label: item?.AppName,
          value: item?.AppId,
        }));
        forEach(_applications, (application) => {
          applicationsRef.current[application.AppId] = application.AppName;
        });
        setApplications(_applications);
        return _applications;
      })
      .catch(() => []);
  };

  const rowsValidator = (rule, val, callback) => {
    if (val?.length > 0) {
      const isHasEmpty = val.some((v) => {
        return !v.Key || !v.Type || !v.Value;
      });
      const isHasRegFail = val.some((v) => {
        return v.Type === 'ERGULAR' && v.hasOwnProperty('RegCheck') && !v.RegCheck;
      });
      if (isHasEmpty) {
        callback(intl('saenext.components.route-create.MseForwardform.TheRuleConditionDataIs'));
      } else if (isHasRegFail) {
        callback(true);
      } else {
        callback();
      }
    } else {
      callback();
    }
  };

  const submit = () => {
    const promise = new Promise<{ success: boolean; params: any }>(async (resolve) => {
      field.validate((errors, values) => {
        if (errors) {
          resolve({ success: false, params: null });
          return;
        }
        if (values) {
          const services = get(values, 'Services', []);
          const val = Array.isArray(services) ? services : [];
          if (val?.length > 0) {
            let percent = 0;
            let hasPercent = false;
            const scene = get(values, 'DestinationType', Scene.alone);
            const isHasEmpty = val.some((v) => {
              percent += get(v, 'Percent', 0);
              hasPercent = Reflect.has(v, 'Percent');

              switch (scene) {
                case Scene.alone:
                  // @ts-ignore
                  return !v.AppId || !v.ServiceId || !v.ServiceProtocol || !v.ServicePort;
                case Scene.multiple:
                  // @ts-ignore
                  return !v.AppId || !v.ServiceId || !v.ServiceProtocol || !v.ServicePort;
              }
            });

            if (isHasEmpty) {
              field.setError(
                'Services',
                intl('saenext.components.route-create.MseForwardform.TheBackendServiceDataIs'),
              );
              resolve({ success: false, params: null });
              return;
            }

            if (hasPercent && percent !== 100) {
              field.setError(
                'Services',
                intl('saenext.components.route-create.MseForwardform.TheSumOfServiceWeights'),
              );
              resolve({ success: false, params: null });
              return;
            }
          } else {
            field.setError(
              'Services',
              intl('saenext.components.route-create.MseForwardform.ConfigureBackendServices'),
            );
            resolve({ success: false, params: null });
            return;
          }
        }
        doSubmit(values, resolve);
      });
    });

    return promise;
  };

  const doSubmit = (values, resolve) => {
    const {
      gatewayId,
      DomainIdList,
      IgnoreCase,
      MethodPredicates: _methodPredicates,
      HeaderPredicates,
      QueryPredicates,
      DestinationType: scene,
      Services: services = [],
      FallbackServices: fallbackServices = [],
    } = values;
    const _domainList = map(DomainIdList, (id) => {
      return {
        DomainId: id,
        DomainName: domainsRef.current[id],
      };
    });
    const _ignoreCase = Array.isArray(IgnoreCase) ? !!IgnoreCase.length : IgnoreCase;
    const _pathPredicates = {
      Path: get(values, 'Path', ''),
      Type: get(values, 'Type', ApigMatch.pre),
      IgnoreCase: !_ignoreCase,
    };
    // apig与mse共用组件, 需要转换成 api 的对应字段
    let _headerPredicates = [];
    let _queryPredicates = [];
    forEach(HeaderPredicates, (headerItem) => {
      _headerPredicates.push({
        Name: headerItem.Key,
        Value: headerItem.Value,
        Type: transferMatchTypeToApi[headerItem.Type],
      });
    });
    forEach(QueryPredicates, (queryItem) => {
      _queryPredicates.push({
        Name: queryItem.Key,
        Value: queryItem.Value,
        Type: transferMatchTypeToApi[queryItem.Type],
      });
    });
    const _predicates = {
      PathPredicates: _pathPredicates,
      MethodPredicates: _methodPredicates,
      HeaderPredicates: _headerPredicates,
      QueryPredicates: _queryPredicates,
    };
    const _source = get(values, 'SourceType', ApigSource.mse);
    const _services = dispatchServices(scene, services);
    const _fallbackServices = dispatchServices(scene, fallbackServices);
    const _fallback = fallbackRef.current;
    const _policies = {
      Timeout: {
        UnitNum: get(values, 'Timeout', 60),
        TimeUnit: 's',
        Enable: disabled ? timeoutEnable : true,
      },
      Retry: {
        Attempts: get(values, 'RetryAttempts', 2),
        RetryOn: get(values, 'RetryOn', []),
        HttpCodes: get(values, 'HttpCodes', []),
        Enable: disabled ? retryEnable : true,
      },
      Fallback: {
        Destinations: _fallback ? _fallbackServices : [],
        Enable: _fallback,
      },
    };
    // 编辑时 超时&重试 不可编辑
    if (disabled) {
      delete _policies.Timeout;
      delete _policies.Retry;
    }

    const _params = {
      GatewayId: gatewayId,
      Domains: _domainList,
      Predicates: _predicates,
      SourceType: _source,
      DestinationType: scene,
      Services: _services,
      Policies: _policies,
      HttpApiType: 'Http',
    };
    // source === ApigSource.mse 增加 NacosInstanceId
    if (_source === ApigSource.mse) {
      const _instanceId = get(values, 'NacosInstanceId');
      Reflect.set(_params, 'NacosInstanceId', _instanceId);
      const _cluster = clustersRef.current.get(_instanceId);
      const _intranetDomain = get(_cluster, 'IntranetDomain');
      Reflect.set(_params, 'NacosAddress', _intranetDomain);
      const _nacosNamespaceId =
        get(values, 'NacosNamespaceId') === NACOS_DEFAULT_NAMESPACEID
          ? ''
          : get(values, 'NacosNamespaceId');
      Reflect.set(_params, 'NacosNamespaceId', _nacosNamespaceId);
    }
    resolve({ success: true, params: _params });
  };

  const dispatchServices = (scene, services) => {
    let _services = [];
    switch (scene) {
      case Scene.alone:
        const service = head(services);
        const appId = get(service, 'AppId');
        // @ts-ignore
        _services = [
          {
            AppId: appId,
            AppName: appId && applicationsRef.current[appId],
            ServiceName: get(service, 'ServiceId'),
            ServiceProtocol: get(service, 'ServiceProtocol'),
            ServicePort: get(service, 'ServicePort'),
            ServiceGroup: get(service, 'ServiceGroup'),
            ServiceWeight: 100,
          },
        ];

        break;
      case Scene.multiple:
        _services = services.map((service) => ({
          AppId: get(service, 'AppId'),
          AppName: get(service, 'AppId') && applicationsRef.current[get(service, 'AppId')],
          ServiceName: get(service, 'ServiceId'),
          ServiceProtocol: get(service, 'ServiceProtocol'),
          ServicePort: get(service, 'ServicePort'),
          ServiceGroup: get(service, 'ServiceGroup'),
          ServiceWeight: get(service, 'Percent', 100),
        }));
        break;
    }
    return _services;
  };

  return (
    <Form field={field}>
      <Form.Item
        required
        label={
          <UnAuthedLabel
            text={intl('saenext.components.route-create.MseForwardform.GatewayInstance')}
            authed={authedApig}
            authKey="AliyunAPIGReadOnlyAccess"
          />
        }
      >
        <RefreshSelect
          {...init('gatewayId', {
            rules: [
              {
                required: true,
                message: intl(
                  'saenext.components.route-create.MseForwardform.SelectAGatewayInstance',
                ),
              },
            ],
          })}
          disabled={disabled}
          dataSource={gateways}
          placeholder={intl(
            'saenext.components.route-create.MseForwardform.SelectAGatewayInstance',
          )}
          fetchMethod={getGateways}
          external={{
            width: 196,
            label: intl('saenext.components.route-create.ApigwForwardform.CreateACloudNativeApi'),
            url: `${CachedData.confLink('feature:apigw:url')}/#/${regionId}/gateway`,
          }}
        />
      </Form.Item>
      <Form.Item
        required
        label={
          <UnAuthedLabel
            text={intl('saenext.components.route-create.MseForwardform.DomainName')}
            authed={listDomainsAuth}
            authKey="AliyunAPIGReadOnlyAccess"
          />
        }
        help={intl('saenext.components.route-create.MseForwardform.SelectTheDomainNameThat')}
      >
        <RefreshSelect
          {...init('DomainIdList', {
            rules: [
              {
                required: true,
                message: intl('saenext.components.route-create.MseForwardform.SelectADomainName'),
              },
            ],
          })}
          mode="multiple"
          hasSelectAll={intl('saenext.components.route-create.MseForwardform.SelectAll')}
          placeholder={intl('saenext.components.route-create.MseForwardform.SelectADomainName')}
          dataSource={domains}
          fetchMethod={() => getDomains()}
          external={{
            width: 196,
            label: intl('saenext.components.route-create.MseForwardform.CreateADomainName'),
            url: `${CachedData.confLink('feature:apigw:url')}/#/${regionId}/domain-certificate`,
          }}
        />
      </Form.Item>

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.Path')}
        help={intl('saenext.components.route-create.MseForwardform.EnterTheRequestPathAnd')}
      >
        <Row style={{ height: 32 }}>
          <Col span={6}>
            <Form.Item>
              <Select
                {...init('Type', {
                  initValue: ApigMatch.pre,
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.route-create.MseForwardform.SelectAPathMatchingCondition',
                      ),
                    },
                  ],

                  props: {
                    onChange: (val: ApigMatch) => setMatch(val),
                  },
                })}
                style={{ width: '100%' }}
                dataSource={ApigMatchRules}
              />
            </Form.Item>
          </Col>
          <Col span={18} style={{ paddingLeft: 10 }}>
            <Form.Item required style={{ display: 'inline-block', width: 'calc(100% - 100px)' }}>
              <RegularTest
                {...init('Path', {
                  rules: [
                    {
                      required: true,
                      message: intl(
                        'saenext.components.route-create.MseForwardform.EnterAPathMatchingValue',
                      ),
                    },
                  ],
                })}
                trim
                showLimitHint
                maxLength={8192}
                placeholder={intl(
                  'saenext.components.route-create.MseForwardform.EnterAMatchingPathSuch',
                )}
                inputType={match === ApigMatch.ergula ? 'reg' : 'normal'}
                getRegState={(val) => setPathRegState(val)}
              />
            </Form.Item>
            <Form.Item style={{ display: 'inline-block', verticalAlign: 'top', marginRight: -8 }}>
              {/* @ts-ignore */}
              <Checkbox.Group
                {...init('IgnoreCase', {
                  initValue: true,
                })}
                className="ml-l"
              >
                {/* @ts-ignore */}
                <Checkbox id="ignoreCase" value={true}>
                  {intl('saenext.components.route-create.MseForwardform.CaseSensitive')}
                </Checkbox>
              </Checkbox.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form.Item>
      <Form.Item>
        <LinkButton onClick={() => setIsCollapsed(!isCollapsed)}>
          <span className="mr-s">
            {intl('saenext.components.route-create.MseForwardform.MoreMatchingRules')}
          </span>
          {isCollapsed ? <Icon size="xs" type="arrow-up" /> : <Icon size="xs" type="arrow-down" />}
        </LinkButton>
      </Form.Item>

      <Form.Item
        label={intl('saenext.components.route-create.MseForwardform.Method')}
        hidden={!isCollapsed}
        help={intl('saenext.components.route-create.MseForwardform.SelectTheRequestMethodThat')}
      >
        <StaticCheckSelect
          {...init('MethodPredicates')}
          hasSelectAll={intl('saenext.components.route-create.MseForwardform.SelectAll')}
          dataSource={Methods}
          placeholder={intl('saenext.components.route-create.MseForwardform.SelectARequestMethod')}
        />
      </Form.Item>

      <Form.Item
        label={intl('saenext.components.route-create.MseForwardform.RequestHeader')}
        hidden={!isCollapsed}
      >
        <span style={{ display: 'inline-block', fontSize: 12, color: '#888' }}>
          {intl('saenext.components.route-create.MseForwardform.EnterTheRequestHeadersAnd')}
        </span>
        <RequestCheckRows
          {...init('HeaderPredicates', {
            initValue: [],
            rules: [
              {
                validator: rowsValidator,
              },
            ],
          })}
          paramType={Params.header}
        />
      </Form.Item>
      <Form.Item
        label={intl('saenext.components.route-create.MseForwardform.RequestParametersQuery')}
        hidden={!isCollapsed}
      >
        <span style={{ display: 'inline-block', fontSize: 12, color: '#888' }}>
          {intl('saenext.components.route-create.MseForwardform.EnterTheRequestParametersAnd')}
        </span>
        <RequestCheckRows
          {...init('QueryPredicates', {
            initValue: [],
            rules: [
              {
                validator: rowsValidator,
              },
            ],
          })}
          paramType={Params.query}
        />
      </Form.Item>

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.ServiceSource')}
      >
        <Radio.Group
          {...init('SourceType', {
            initValue: ApigSource.mse,
            rules: [
              {
                required: true,
                message: intl(
                  'saenext.components.route-create.MseForwardform.SelectAServiceSource',
                ),
              },
            ],

            props: {
              onChange: (val: ApigSource) => {
                setSource(val);
                getNacosClusters(val);
                getApplications(val);
              },
            },
          })}
        >
          <Radio id="mse" value={ApigSource.mse}>
            MSE Nacos
          </Radio>
          <Radio id="k8s" value={ApigSource.k8s}>
            K8S Service
          </Radio>
        </Radio.Group>
      </Form.Item>
      {source === ApigSource.mse ? (
        <div className="justify-center">
          <Form.Item
            required
            label={
              <UnAuthedLabel
                text={intl('saenext.components.route-create.MseForwardform.MseNacosInstances')}
                authed={authedMSE}
                authKey="AliyunMSEReadOnlyAccess"
              />
            }
            style={{ flex: 1, paddingRight: 16 }}
          >
            <Select
              {...init('NacosInstanceId', {
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.route-create.MseForwardform.SelectMseNacosInstance',
                    ),
                  },
                ],

                props: {
                  onChange: (val, __, item: any) => {
                    setIntranetDomain(item.IntranetDomain);
                    setNacosInstanceId(String(val));
                    getNacosNamespaces(source, val);
                    getApplications(source, val, nacosNamespaceId);
                  },
                },
              })}
              style={{ width: '100%' }}
              dataSource={clusters}
              placeholder={intl(
                'saenext.components.route-create.MseForwardform.SelectMseNacosInstance',
              )}
            />
          </Form.Item>
          <Form.Item
            required
            label={intl('saenext.components.route-create.MseForwardform.MseNacosNamespace')}
            style={{ flex: 1, paddingLeft: 16 }}
          >
            <Select
              {...init('NacosNamespaceId', {
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.route-create.MseForwardform.SelectMseNacosNamespace',
                    ),
                  },
                ],

                props: {
                  onChange: (val) => {
                    setNacosNamespaceId(String(val));
                    getApplications(source, nacosInstanceId, val);
                  },
                },
              })}
              style={{ width: '100%' }}
              dataSource={namespaces}
              placeholder={intl('saenext.components.route-create.MseForwardform.MseNacosNamespace')}
            />
          </Form.Item>
        </div>
      ) : null}

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.UsageScenarios')}
        help={
          <div className="justify-center">
            <span>
              {intl('saenext.components.route-create.MseForwardform.SelectAScenarioForThis')}
            </span>
            <Button
              text
              type="primary"
              style={{ fontWeight: 'normal' }}
              onClick={() =>
                window.open(CachedData.confLink('help:mse:overview-of-routing-methods'), '_blank')
              }
            >
              <span>{intl('saenext.components.route-create.MseForwardform.LearnMore')}</span>
              <Icon type="external_link" />
            </Button>
          </div>
        }
      >
        <SceneSelect
          {...init('DestinationType', {
            initValue: Scene.alone,
            rules: [
              {
                required: true,
                message: intl('saenext.components.route-create.MseForwardform.SelectAScenario'),
              },
            ],

            props: {
              onChange: (val) => {
                setScene(String(val));
                field.setValues({ Services: [] });
              },
              ingressType: 'apig',
            },
          })}
        />
      </Form.Item>

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.BackendServices')}
      >
        <ServiceCheckRows
          {...init('Services', {
            initValue: [],
          })}
          useSence={scene}
          regionId={regionId}
          renderSource={source}
          baseContainer={baseContainer}
          namespaceId={namespaceId}
          applications={applications}
          refreshApps={() => getApplications(source, nacosInstanceId, nacosNamespaceId)}
          nacosInstanceId={nacosInstanceId}
          nacosNamespaceId={nacosNamespaceId}
        />
      </Form.Item>

      <Form.Item>
        <LinkButton onClick={() => setIsExpended(!isExpended)}>
          <span className="mr-s">
            {intl('saenext.components.route-create.MseForwardform.AdvancedConfiguration')}
          </span>
          {isExpended ? <Icon size="xs" type="arrow-up" /> : <Icon size="xs" type="arrow-down" />}
        </LinkButton>
      </Form.Item>
      <Form.Item label="Fallback" hidden={!isExpended}>
        <InitFallbackRows
          {...init('FallbackServices', {
            initValue: [],
          })}
          useSence={scene}
          regionId={regionId}
          baseContainer={baseContainer}
          renderSource={source}
          fallback={fallback}
          updateFallback={(val) => {
            setFallback(val);
            fallbackRef.current = val;
          }}
          applications={applications}
          refreshApps={() => getApplications(source, nacosInstanceId, nacosNamespaceId)}
          nacosInstanceId={nacosInstanceId}
          nacosNamespaceId={nacosNamespaceId}
        />
      </Form.Item>

      <Form.Item
        required
        label={intl('saenext.components.route-create.MseForwardform.TimeoutPeriodSeconds')}
        help={intl('saenext.components.route-create.MseForwardform.EnterATimeoutPeriodIf')}
        hidden={!isExpended || disabled}
      >
        <NumberPicker
          {...init('Timeout', {
            initValue: 60,
          })}
          min={0}
          max={3600}
          style={{
            width: '30%',
          }}
        />
      </Form.Item>
      <Form.Item
        required
        label={intl('saenext.components.route-create.ApigwForwardform.NumberOfRetries')}
        help={intl('saenext.components.route-create.ApigwForwardform.EnterTheNumberOfRetries')}
        hidden={!isExpended || disabled}
      >
        <NumberPicker
          {...init('RetryAttempts', {
            initValue: 2,
          })}
          min={0}
          max={1000}
          style={{
            width: '30%',
          }}
        />
      </Form.Item>
      <Form.Item
        label={intl('saenext.components.route-create.ApigwForwardform.RetryCondition')}
        help={
          <>
            {intl('saenext.components.route-create.ApigwForwardform.SelectARetryConditionMultiple')}

            <ExternalLink
              label={intl('saenext.components.route-create.ApigwForwardform.LearnMore')}
              url={CachedData.confLink('help:apigw:configure-retry-policy')}
            />
          </>
        }
        hidden={!isExpended || disabled}
      >
        <SelectCheckbox
          {...init('RetryOn', {
            initValue: [
              'connect-failure',
              'refused-stream',
              'unavailable',
              'cancelled',
              'retriable-status-codes',
            ],
          })}
          mode="multiple"
          dataSource={retryList}
          itemRender={(item) => {
            return (
              <div>
                <Checkbox checked={includes(field.getValue('RetryOn') || [], item.value)} />
                <span>{item.label}</span>
                <span style={{ color: '#808080', marginLeft: 8 }}>{matchRetryTip(item.value)}</span>
              </div>
            );
          }}
        />
      </Form.Item>
      {/* @ts-ignore */}
      {includes(field.getValue('RetryOn') || [], 'retriable-status-codes') && !disabled && (
        <Form.Item
          label={intl('saenext.components.route-create.ApigwForwardform.RetryStatusCode')}
          help={intl('saenext.components.route-create.ApigwForwardform.AddARetryStatusCode')}
          hidden={!isExpended}
        >
          {/* @ts-ignore */}
          <TagsGroup name="HttpCodes" maxLimitHint={10} onlyAllowMod="number" />
        </Form.Item>
      )}
    </Form>
  );
});
