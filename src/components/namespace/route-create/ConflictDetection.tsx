import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { LinkButton, Truncate, Icon, Balloon, Dialog, CndTable } from '@ali/cnd';
import services from "~/services";
import { isEmpty, get, forEach, join, includes } from 'lodash';
import CachedData from '../../../cache/common';

const ConflictDetection = (props) => {
  const { setCheckVisible, handleSubmit, requestParams, checkResult = {}, regionId } = props;
  const [visibleDialog, setVisibleDialog] = useState(false);
  const [dataSource, setDataSource] = useState(checkResult?.items || []);
  const [resourceGatewayId, setResourceGatewayId] = useState('');
  const [disabled, setDisabled] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isEmpty(checkResult)) {
      CheckConflict();
    } else {
      formatData(checkResult);
      setVisibleDialog(true);
    }
  }, []);

  const resourceTypes = {
    HttpApiRoute: {
      resourceTitle: 'HTTP API:',
      resourceText: intl('saenext.components.route-create.ConflictDetection.Route'),
      link: ({ resourceName, resourceId, routeInfo, linkType }) => {
        if (linkType === 'list') {
          return `${CachedData.confLink('feature:apigw:url')}/#/${regionId}/api-manage/api-http/${resourceId}/router?region=${regionId}`;
        } else {
          return `${CachedData.confLink('feature:apigw:url')}/#/${regionId}/api-manage/api-http/${resourceId}/router/detail?region=${regionId}&gatewayId=${resourceGatewayId}&routerId=${routeInfo.routeId}&apiName=${resourceName}`;
        }
      },
    },
  };

  const matchRule = {
    Exact: intl('saenext.components.route-create.ConflictDetection.Precision'),
    Prefix: intl('saenext.components.route-create.ConflictDetection.Prefix'),
    Regex: intl('saenext.components.route-create.ConflictDetection.Regular'),
  };

  const levelInfo = {
    Critical: {
      name: intl('saenext.components.route-create.ConflictDetection.Serious'),
      iconColor: 'color-error',
    },
    Warning: {
      name: intl('saenext.components.route-create.ConflictDetection.Warning'),
      iconColor: 'color-warning',
    },
    Informational: {
      name: intl('saenext.components.route-create.ConflictDetection.Information'),
      iconColor: 'color-333',
    },
  };

  const CheckConflict = async () => {
    let result: any = {};
    try {
      result = await services.detectHttpApiConflicts({
        params: { httpApiId: requestParams.httpApiId },
        content: {
          routeId: requestParams.routeId,
        },
      });
      if (!result) {
        setCheckVisible(false);
        return;
      }
      const { items = [], gatewayId: _gatewayId } = result;
      if (!isEmpty(items)) {
        formatData(result);
        setVisibleDialog(true);
      } else {
        handleSubmit && handleSubmit();
      }
    } catch (error) {}
  };

  const formatData = ({ items: data = [], gatewayId }) => {
    setResourceGatewayId(gatewayId);
    let level = [];
    forEach(data, (item) => {
      let details = [];
      forEach(item.conflicts, (j) => {
        forEach(j.details, (k) => {
          let _k = { ...k, ...j };
          level.push(k.level);
          details.push(_k);
        });
      });
      item.details = details;
    });
    if (includes(level, 'Critical')) {
      setDisabled(true);
    }
    setDataSource(data);
  };

  const columns: any = [
    {
      key: 'detectedMatch',
      title: intl('saenext.components.route-create.ConflictDetection.CurrentResource'),
      dataIndex: 'detectedMatch',
      width: 320,
      cell: (value, index, record) => {
        let { detectedMatch = {} } = record;
        let match = get(detectedMatch, 'match', {});
        let path = get(match, 'path', {});
        let headers = get(match, 'headers', []);
        let methods = get(match, 'methods', []);
        let queryParams = get(match, 'queryParams', []);
        return (
          <div>
            <div className="align-center">
              <span className="mr-4">{'HTTP API:'}</span>
              <LinkButton
                style={{ textAlign: 'left', flex: 1 }}
                onClick={() => {
                  let url = `${CachedData.confLink('feature:apigw:url')}/#/${regionId}/api-manage/api-http/${requestParams?.httpApiId}/router?region=${regionId}`;
                  window.open(url, '_blank');
                }}
              >
                <Truncate
                  type="width"
                  style={{ width: 210 }}
                  popupStyle={{ wordBreak: 'break-all' }}
                  threshold={'auto'}
                >
                  {`${requestParams.httpApiName}`}
                </Truncate>
              </LinkButton>
            </div>
            <div className="align-center">
              <span className="mr-4">
                {intl('saenext.components.route-create.ConflictDetection.Route')}
              </span>
              <LinkButton
                style={{ textAlign: 'left', flex: 1 }}
                onClick={() => {
                  let url = `${CachedData.confLink('feature:apigw:url')}/#/${regionId}/api-manage/api-http/${requestParams?.httpApiId}/router/detail?region=${regionId}&gatewayId=${requestParams.gatewayId}&routerId=${requestParams.routeId}&apiName=${requestParams?.httpApiName}`;
                  window.open(url, '_blank');
                }}
              >
                <Truncate
                  type="width"
                  style={{ width: 210 }}
                  popupStyle={{ wordBreak: 'break-all' }}
                  threshold={'auto'}
                >
                  {requestParams.gatewayRouteName}
                </Truncate>
              </LinkButton>
            </div>
            <Truncate
              type="width"
              style={{ width: '95%', color: '#888888' }}
              popupStyle={{ wordBreak: 'break-all' }}
              threshold={'auto'}
            >
              {`${matchRule[path.type] ? matchRule[path.type] + ' ' : ''} ${join(methods, '/')} ${
                path.value
              }`}
            </Truncate>
            {headers.map((item, index) => {
              return (
                <Truncate
                  key={index}
                  type="width"
                  style={{ width: '95%', color: '#888888' }}
                  popupStyle={{ wordBreak: 'break-all' }}
                  threshold={'auto'}
                >
                  {`Header: ${item?.name} == ${item?.value}`}
                </Truncate>
              );
            })}
            {queryParams.map((item, index) => {
              return (
                <Truncate
                  key={index}
                  type="width"
                  style={{ width: '95%', color: '#888888' }}
                  popupStyle={{ wordBreak: 'break-all' }}
                  threshold={'auto'}
                >
                  {`Query: ${item?.name} == ${item?.value} `}
                </Truncate>
              );
            })}
          </div>
        );
      },
    },
    {
      key: 'conflictingMatch',
      title: intl('saenext.components.route-create.ConflictDetection.ConflictResources'),
      width: 320,
      dataIndex: 'conflictingMatch',
      cell: (value, index, record) => {
        let {
          resourceType = '',
          resourceId = '',
          resourceName = '',
          routeInfo = {},
          environmentInfo = {},
          conflictingMatch = {},
        } = record;
        let match = get(conflictingMatch, 'match', {});
        let path = get(match, 'path', {});
        let headers = get(match, 'headers', []);
        let methods = get(match, 'methods', []);
        let queryParams = get(match, 'queryParams', []);

        return (
          <div>
            <div className="align-center">
              <span className="mr-4">{resourceTypes[resourceType]?.resourceTitle}</span>
              <LinkButton
                style={{ textAlign: 'left', flex: 1 }}
                onClick={() => {
                  let url = resourceTypes[resourceType]?.link({
                    resourceName,
                    resourceId,
                    linkType: 'list',
                  });
                  window.open(url, '_blank');
                }}
              >
                <Truncate
                  type="width"
                  style={{ width: 210 }}
                  popupStyle={{ wordBreak: 'break-all' }}
                  threshold={'auto'}
                >
                  {resourceName}
                </Truncate>
              </LinkButton>
            </div>
            <div className="align-center">
              <span className="mr-4">{resourceTypes[resourceType]?.resourceText}</span>
              <LinkButton
                style={{ textAlign: 'left', flex: 1 }}
                onClick={() => {
                  let url = resourceTypes[resourceType]?.link({
                    resourceName,
                    resourceId,
                    routeInfo,
                    linkType: 'details',
                  });
                  window.open(url, '_blank');
                }}
              >
                <Truncate
                  type="width"
                  style={{ width: 240 }}
                  popupStyle={{ wordBreak: 'break-all' }}
                  threshold={'auto'}
                >
                  {routeInfo.name}
                </Truncate>
              </LinkButton>
            </div>
            <Truncate
              type="width"
              style={{ width: '95%', color: '#888888' }}
              popupStyle={{ wordBreak: 'break-all' }}
              threshold={'auto'}
            >
              {`${matchRule[path.type] ? matchRule[path.type] + ' ' : ''} ${join(methods, '/')} ${
                path.value
              }`}
            </Truncate>

            {headers.map((item, index) => {
              return (
                <Truncate
                  key={index}
                  type="width"
                  style={{ width: '95%', color: '#888888' }}
                  popupStyle={{ wordBreak: 'break-all' }}
                  threshold={'auto'}
                >
                  {`Header: ${item?.name} == ${item?.value}`}
                </Truncate>
              );
            })}
            {queryParams.map((item, index) => {
              return (
                <Truncate
                  key={index}
                  type="width"
                  style={{ width: '95%', color: '#888888' }}
                  popupStyle={{ wordBreak: 'break-all' }}
                  threshold={'auto'}
                >
                  {`Query: ${item?.name} == ${item?.value} `}
                </Truncate>
              );
            })}
            {!isEmpty(environmentInfo) && (
              <Truncate
                type="width"
                style={{ width: '95%', color: '#888888' }}
                popupStyle={{ wordBreak: 'break-all' }}
                threshold={'auto'}
              >
                {intl('saenext.components.route-create.ConflictDetection.Environment')}

                {`${environmentInfo?.name} (${environmentInfo?.environmentId}})`}
              </Truncate>
            )}

            {resourceType === 'HttpApiRoute' && (
              <Truncate
                type="width"
                style={{ width: '95%', color: '#888888' }}
                popupStyle={{ wordBreak: 'break-all' }}
                threshold={'auto'}
              >
                {intl('saenext.components.route-create.ConflictDetection.GatewayInstance')}

                {`${resourceGatewayId}`}
              </Truncate>
            )}
          </div>
        );
      },
    },
    {
      key: 'level',
      title: intl('saenext.components.route-create.ConflictDetection.ConflictLevel'),
      width: 120,
      dataIndex: 'level',
      cell: (value, index, record) => {
        let { level = '' } = record;
        return (
          <span>
            <Icon type="info" className={`${levelInfo[level]?.iconColor} mr-4`} size="small" />
            {levelInfo[level]?.name}
          </span>
        );
      },
    },
    {
      key: 'reason',
      title: intl('saenext.components.route-create.ConflictDetection.Cause'),
      dataIndex: 'reason',
      cell: (value, index, record) => {
        let { conflictingMatch = {} } = record;
        let match = get(conflictingMatch, 'match', {});
        let headers = get(match, 'headers', []);
        let queryParams = get(match, 'queryParams', []);
        return (
          <div>
            {!isEmpty(headers) || !isEmpty(queryParams)
              ? intl(
                  'saenext.components.route-create.ConflictDetection.PathAndParameterConflictPlease',
                )
              : intl(
                  'saenext.components.route-create.ConflictDetection.PathConflictPleaseAdjustThe',
                )}
          </div>
        );
      },
    },
  ];

  const handleOk = () => {
    handleSubmit && handleSubmit();
    setLoading(true);
  };
  const onCancel = () => {
    setVisibleDialog(false);
    setCheckVisible(false);
  };

  return (
    <div>
      <Dialog
        title={
          <span className="align-center">
            <Icon className="mr-4" style={{ color: '#f76b09' }} type="warning" />
            {intl('saenext.components.route-create.ConflictDetection.ExceptionPrompt')}
          </span>
        }
        style={{ width: '960px' }}
        visible={visibleDialog}
        onOk={handleOk}
        onCancel={onCancel}
        onClose={onCancel}
        shouldUpdatePosition
        okProps={{
          children: disabled ? (
            <Balloon
              trigger={
                <span>
                  {intl('saenext.components.route-create.ConflictDetection.ContinuePublishing')}
                </span>
              }
              align="r"
            >
              <span>
                {intl(
                  'saenext.components.route-create.ConflictDetection.TheConflictLevelIsSerious',
                )}
              </span>
            </Balloon>
          ) : (
            <span>
              {intl('saenext.components.route-create.ConflictDetection.ContinuePublishing')}
            </span>
          ),

          disabled: disabled,
          loading: loading,
        }}
      >
        <div>
          <div className="mb-8">
            {intl(
              'saenext.components.route-create.ConflictDetection.ResourceConflictsPleaseAdjustThe',
            )}
          </div>
          {dataSource.map((item, index) => {
            return (
              <div
                key={index}
                style={{ marginBottom: '16px', padding: 8, border: '2px dashed #ccc' }}
              >
                <h3>
                  {intl('saenext.components.route-create.ConflictDetection.DomainName')}
                  {item?.domainInfo?.name}
                </h3>
                <CndTable
                  dataSource={item.details}
                  columns={columns}
                  pagination={false}
                />
              </div>
            );
          })}
        </div>
      </Dialog>
    </div>
  );
};

export default ConflictDetection;
