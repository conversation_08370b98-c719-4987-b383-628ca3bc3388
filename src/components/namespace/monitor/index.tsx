import React from 'react';
import { createAlfaApp } from '@alicloud/alfa-react';


const CloudMonitor = createAlfaApp({
  name: '@ali/alfa-cloud-cms-widget-home',
});

const NsNonitor = (props) => {
  const { regionId, namespaceId } = props;
  
  return (
    <CloudMonitor
      category="serverless"
      // dimensions={[{ appId: `${appId}` }]}
      dimensions={[]}
      namespaceId={namespaceId}
      regionId={regionId}
      supportFilteringApplications
      hideGroupTab
      className="alfa-cloud-cms-widget-home"
    />
  )
}

export default NsNonitor