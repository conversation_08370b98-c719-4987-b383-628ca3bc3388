import React, { useState, useEffect, useRef } from 'react';
import { Loading } from '@ali/cnd';
import CachedData from '../../../cache/common';

const ConsoleNavHeight = 50;
const BreadcrumbHeight = 52;
const AppPageNavHeight = 52;
const ReservedSpaceHeight = 16;
const ApigwDetail = props => {
  const { regionId,routeInfo } = props;
  const iframeRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const iframeHeight =
    document.documentElement.clientHeight -
    ConsoleNavHeight -
    BreadcrumbHeight -
    AppPageNavHeight -
    ReservedSpaceHeight;

  useEffect(() => {
    setLoading(true);
  }, []);

  const iframeLoaded = () => {
    setLoading(false);
  };

  return (
    <Loading visible={loading} style={{ width: '100%', position: 'relative' }}>
      <iframe
        width="100%"
        height={iframeHeight}
        scrolling="no"
        frameBorder={0}
        onLoad={iframeLoaded}
        src={`${CachedData.confLink('feature:apigw:url')}/#${regionId}/api-manage/api-http/${routeInfo?.HttpApiId}/router/detail?region=${regionId}&gatewayId=${routeInfo?.GatewayId}&routerId=${routeInfo?.RouteId}&apiName=${routeInfo?.HttpApiName}&isHideNav=true&isHideTopBar=true`}
        ref={iframeRef}
      />
    </Loading>
  );
};

export default ApigwDetail;
