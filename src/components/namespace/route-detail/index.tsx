import React, { useState, useEffect, useRef } from 'react';
import { intl } from '@ali/cnd';
import { Page, Loading, Button, Dialog, Message } from '@ali/cnd';
import ApigwDetail from './ApigwDetail';
import services from "~/services";
import { getParams } from '../../../utils/global';
import { includes, isEmpty } from 'lodash';
import ConflictDetection from '../route-create/ConflictDetection';

enum HttpApiRouteStatus {
  NotDeployed = 'NotDeployed', //未发布
  Deploying = 'Deploying', // 发布中
  Deployed = 'Deployed', //已发布
  DeployedWithChanges = 'DeployedWithChanges', //已发布但修改
  Undeploying = 'Undeploying', //下线中
  DeployFailed = 'DeployFailed', //发布失败
  UndeployFailed = 'UndeployFailed',
} //下线失败
const RouteDetail = (props) => {
  const { regionId, namespaceId, namespaceName, handleEdit, routeListUrl, breadcrumbs=[] } = props;
  const routeName = getParams('routeName');
  const route = getParams('route');
  const [ingressId, networkType, loadBalance] = route.split('-');
  const [checkVisible, setCheckVisible] = useState(false);
  const [checkResult, setCheckResult] = useState<any>({});
  const [httpApiRouteInfo, setHttpApiRouteInfo] = useState<any>({});
  const canPublishStatus = [
    HttpApiRouteStatus.NotDeployed,
    HttpApiRouteStatus.DeployedWithChanges,
    HttpApiRouteStatus.DeployFailed,
  ];

  const offLineStatus = [
    HttpApiRouteStatus.Deployed,
    HttpApiRouteStatus.DeployedWithChanges,
    HttpApiRouteStatus.UndeployFailed,
  ];

  useEffect(() => {
    getHttpApiRoute();
  }, []);

  const getHttpApiRoute = async () => {
    const res = await services.getHttpApiRoute({
      params: { IngressId: ingressId, NamespaceId: namespaceId },
      customErrorHandle: (err, data, callback) => {},
    });
    const { Data = {} } = res;
    setHttpApiRouteInfo(Data);
    if (
      includes([HttpApiRouteStatus.Deploying, HttpApiRouteStatus.Undeploying], Data.DeployStatus)
    ) {
      setTimeout(() => {
        getHttpApiRoute();
      }, 3000);
    }
  };

  const handleDelete = () => {
    Dialog.alert({
      title: intl('saenext.components.gateway.DeleteRoutingRules'),
      content: intl('saenext.components.gateway.AfterYouDeleteARouting'),
      onOk: () => {
        services
          .deleteHttpApiRoute({
            params: {
              IngressId: ingressId,
              NamespaceId: namespaceId,
            },
          })
          .then(() => {
            Message.success(intl('saenext.components.gateway.TheRoutingRuleHasBeen'));
            props.history.push(routeListUrl);
          });
      },
    });
  };

  // 路由冲突检测
  const handleDetectHttiApiConflicts = async () => {
    setCheckResult({});
    const res = await services.detectHttpApiConflicts({
      params: {
        httpApiId: httpApiRouteInfo?.HttpApiId,
      },
      content: {
        routeId: httpApiRouteInfo?.RouteId,
      },
    });
    const { items = [], gatewayId = '' } = res?.data;
    if (!isEmpty(items)) {
      setCheckResult({ items, gatewayId });
      setCheckVisible(true);
    } else {
      handlePublish();
    }
  };

  const handlePublish = async () => {
    const res = await services.deployHttpApiRoute({
      params: {
        IngressId: ingressId,
        NamespaceId: namespaceId,
      },
    });
    if (res?.Success) {
      Message.success(intl('saenext.components.route-detail.RoutePublishedSuccessfully'));
      getHttpApiRoute();
    }
  };

  const handleOffline = () => {
    Dialog.alert({
      title: intl('saenext.components.route-detail.RouteOffline'),
      content: intl('saenext.components.route-detail.ConfirmOfflineHttpapirouteinfoname', {
        httpApiRouteInfoName: httpApiRouteInfo.Name,
      }),
      onOk: async () => {
        const res = await services.offlineRouter({
          params: { httpApiId: httpApiRouteInfo?.HttpApiId },
          content: { routeId: httpApiRouteInfo?.RouteId },
        });
        Message.success(intl('saenext.components.route-detail.OfflineSuccessfully'));
        getHttpApiRoute();
      },
    });
  };

  return (
    <Page
      title={routeName || intl('saenext.components.route-detail.RouteDetails')}
      historyBack={routeListUrl}
      breadcrumbs={breadcrumbs}
      titleExtra={
        <>
          <Button
            type="primary"
            onClick={() => {
              handleEdit(getParams('route'));
            }}
            style={{ marginRight: 8 }}
          >
            {intl('saenext.components.route-detail.Edit')}
          </Button>
          {includes(canPublishStatus, httpApiRouteInfo.DeployStatus) && (
            <Button
              style={{ marginRight: 8 }}
              onClick={() => {
                handleDetectHttiApiConflicts();
              }}
            >
              {intl('saenext.components.route-detail.Publish')}
            </Button>
          )}
          {includes(offLineStatus, httpApiRouteInfo.DeployStatus) && (
            <Button
              style={{ marginRight: 8 }}
              type="primary"
              warning
              onClick={() => handleOffline()}
            >
              {intl('saenext.components.route-detail.Offline')}
            </Button>
          )}
          {httpApiRouteInfo.DeployStatus === HttpApiRouteStatus.NotDeployed && (
            <Button type="primary" warning onClick={() => handleDelete()}>
              {intl('saenext.components.route-detail.Delete')}
            </Button>
          )}
        </>
      }
      // @ts-ignore
      className={breadcrumbs.length > 0 ? "custom-page-layout" : "route-detail-page"}
    >
      {loadBalance === 'apig' && <ApigwDetail routeInfo={httpApiRouteInfo} regionId={regionId} />}
      {checkVisible && (
        <ConflictDetection
          type="HttpApi"
          checkResult={checkResult}
          setCheckVisible={() => {
            setCheckVisible(false);
          }}
          handleSubmit={() => {
            handlePublish();
          }}
          requestParams={{
            httpApiId: httpApiRouteInfo?.HttpApiId,
            httpApiName: httpApiRouteInfo?.HttpApiName,
            routeId: httpApiRouteInfo?.RouteId,
            gatewayRouteName: httpApiRouteInfo?.Name,
            gatewayId: httpApiRouteInfo?.GatewayId,
          }}
          regionId={regionId}
        />
      )}
    </Page>
  );
};

export default RouteDetail;
