import { intl } from '@ali/cnd';
import React, { useEffect, useMemo, useState } from 'react';
import { Checkbox, DateTime, Select } from '@ali/cnd';
import { forEach, get, isObject, keys, union } from 'lodash';
import CodeDiff from 'react-code-diff-lite';
import { OutputFormatType } from 'diff2html/lib/types';
import If from '~/components/shared/If';
import './index.less';

const ConfigDiff = (props) => {
  const { values } = props;

  const [data, setData] = useState(values);
  const [values1, values2] = data;

  const [showDiffOnly, setShowDiffOnly] = useState(false);
  const [selectedKey, setSelectedKey] = useState('');
  const [diffType, setDiffType] = useState<OutputFormatType>('side-by-side');

  useEffect(() => {
    setDiffData();
  }, [values, selectedKey]);

  const { allKeys, diffKeys } = useMemo(() => {
    const [values1, values2] = values;
    const keys1 = keys(values1.Data);
    const keys2 = keys(values2.Data);

    const allKeys = union(keys1, keys2);

    const diffKeys = [];

    forEach(allKeys, (key) => {
      const value1 = values1.Data[key];
      const value2 = values2.Data[key];
      if (value1 !== value2) {
        diffKeys.push(key);
      }
    });

    return {
      allKeys,
      diffKeys,
    };
  }, [values]);

  const onCheckChange = (checked) => {
    setSelectedKey('');
    setShowDiffOnly(checked);
  };

  const setDiffData = () => {
    if (!selectedKey) {
      setData(values);
      return;
    }
    const [values1, values2] = values;
    const keyVal1 = get(values1, ['Data', selectedKey], '');
    const keyVal2 = get(values2, ['Data', selectedKey], '');
    const data = [
      {
        ...values1,
        Data: keyVal1,
      },
      {
        ...values2,
        Data: keyVal2,
      },
    ];

    setData(data);
  };

  const formatValues = (data) => {
    if (!isObject(data)) {
      return data;
    }
    // 为避免 key 顺序影响 diff，先排序
    const keys = Object.keys(data);
    keys.sort();
    const sortedObj = {};
    forEach(keys, (key) => {
      sortedObj[key] = data[key];
    });

    return JSON.stringify(sortedObj, null, 4);
  };

  return (
    <>
      <div className="flex justify-between mb">
        <div>
          <Select
            style={{ width: 400 }}
            placeholder={intl('saenext.config-map.components.ConfigDiff.SelectAVariableName')}
            dataSource={showDiffOnly ? diffKeys : allKeys}
            value={selectedKey}
            onChange={(v: string) => setSelectedKey(v)}
            showSearch
            hasClear
          />
        </div>
        <div>
          <span className="mr">
            {intl('saenext.config-map.components.ConfigDiff.TotalDifferenceDiffkeyslengthItems', {
              diffKeysLength: diffKeys.length,
            })}
          </span>
          <Checkbox checked={showDiffOnly} onChange={onCheckChange}>
            {intl('saenext.config-map.components.ConfigDiff.OnlyDifferencesAreDisplayed')}
          </Checkbox>
          <Select
            className="ml"
            dataSource={[
              {
                label: 'INLINE',
                value: 'line-by-line',
              },
              {
                label: 'SIDE-BY-SIDE',
                value: 'side-by-side',
              },
            ]}
            value={diffType}
            onChange={(v: OutputFormatType) => setDiffType(v)}
          />
        </div>
      </div>
      <div className="config-diff">
        <If condition={diffType === 'side-by-side'}>
          <div className="diff-header flex justify-between pt-s pb-s">
            <div className="w-50 pl-s">
              <DateTime value={values1.UpdateTime} />
            </div>
            <div className="w-50">
              <DateTime value={values2.UpdateTime} />
            </div>
          </div>
        </If>
        <CodeDiff
          oldStr={formatValues(values1.Data)}
          newStr={formatValues(values2.Data)}
          context={showDiffOnly ? 0 : 1000}
          diffStyle="char"
          outputFormat={diffType}
        />
      </div>
    </>
  );
};

export default ConfigDiff;
