import React, { createRef, useState } from 'react';
import { Dialog, SlidePanel, intl } from '@ali/cnd';
import { isEmpty } from 'lodash';
import ConfigMapSlideContent from '~/components/shared/ConfigMapSlide/ConfigMapSlideContent';

const ConfigMapEditButton = (props) => {
  const {
    children,
    record,
    onRefresh,
    namespaceId
  } = props;

  const [active, setActive] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const slideRef = (createRef() as any);

  const onOpen = () => {
    if (!isEmpty(record.RelateApps)) {
      onWarningEdit();
      return;
    }
    setActive(true);
  };

  const onWarningEdit = () => {
    Dialog.alert({
      // @ts-ignore
      size: 'medium',
      title: intl("saenext.configMap.components.ConfigMapEditButton.EditConfigurationItems"),
      content:
      <p>{intl("saenext.configMap.components.ConfigMapEditButton.ThisConfigurationItemAlreadyHas")}
        <br /><br />{intl("saenext.configMap.components.ConfigMapEditButton.InOrderToEnsureThe")}
        <br /><br />{intl("saenext.configMap.components.ConfigMapEditButton.AreYouSureYouWant")}

      </p>,

      onOk: () => setActive(true)
    });
  };

  const onOk = async () => {
    try {
      setIsProcessing(true);
      await slideRef?.current?.handleConfirm();
      onClose();
    } catch (error) {
    }
    setIsProcessing(false);
  };

  const onClose = () => {
    setActive(false);
  };

  return (
    <>
      <span onClick={onOpen}>
        {children}
      </span>
      <SlidePanel
        isProcessing={isProcessing}
        title={intl("saenext.configMap.components.ConfigMapEditButton.EditConfigurationItems")}
        isShowing={active}
        width={900}
        onMaskClick={onClose}
        onClose={onClose}
        onOk={onOk}
        onCancel={onClose}
        okText={intl('button.ok')}
        cancelText={intl('button.cancel')}
        processingText={intl('button.processing')}>

        <ConfigMapSlideContent
          ref={slideRef}
          type="edit"
          reload={onRefresh}
          namespaceId={namespaceId}
          ConfigMapId={record.ConfigMapId}
        />
      </SlidePanel>
    </>);

};

export default ConfigMapEditButton;