import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Button, CndTable, DateTime, LinkButton } from '@ali/cnd';
import services from '~/services';
import { get, map, noop, sortBy } from 'lodash';
import SlideWrap from '~/components/shared/SlideWrap';
import ConfigMapSlideContent from '~/components/shared/ConfigMapSlide/ConfigMapSlideContent';
import ConfigDiff from './ConfigDiff';
import { jsonParse } from '~/utils/transfer-data';

const VersionList = (props) => {
  const { record } = props;
  const { NamespaceId, Name } = record;

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);

  const fetchData = async () => {
    const res = await services.ListResourceRevisions({
      NamespaceId,
      Name,
      Resource: 'configmap',
    });
    const configMaps = get(res, 'Data.Revisions', []);
    const data = map(configMaps, (item) => ({
      ...item,
      Name,
      Data: jsonParse(item.Data)?.data,
    }));
    return {
      data,
      total: configMaps.length,
    };
  };

  const onChange = (selectedRowKeys, selectedRows) => {
    if (selectedRowKeys.length > 2) {
      return;
    }
    setSelectedRowKeys(selectedRowKeys);
    const sortedRow = sortBy(selectedRows, 'UpdateTime');
    setSelectedRows(sortedRow);
  };

  const primaryOperation = (
    <SlideWrap
      title={intl('saenext.config-map.components.VersionList.ContrastDifference')}
      width={1200}
      onMaskClick={noop}
      onClose={noop}
      content={<ConfigDiff values={selectedRows} />}
    >
      <Button disabled={selectedRowKeys.length !== 2}>
        {intl('saenext.config-map.components.VersionList.ContrastDifference')}
      </Button>
    </SlideWrap>
  );

  const columns = [
    {
      title: intl('saenext.config-map.components.VersionList.UpdateTime'),
      dataIndex: 'UpdateTime',
      width: 40,
      cell: (value, index, record) => <DateTime value={value} />,
    },
    {
      title: intl('saenext.config-map.components.VersionList.Operation'),
      dataIndex: 'UpdateTime',
      width: 20,
      cell: (value, index, record) => (
        <SlideWrap
          title={
            <>
              {intl('saenext.config-map.components.VersionList.Details')}
              {record.Name} - <DateTime value={value} />
              {intl('saenext.common.parenthesis.right')}
            </>
          }
          width={800}
          onMaskClick={noop}
          onClose={noop}
          onCancel={noop}
          content={<ConfigMapSlideContent type="preview" namespaceId={NamespaceId} data={record} />}
        >
          <LinkButton>{intl('saenext.config-map.components.VersionList.Details.1')}</LinkButton>
        </SlideWrap>
      ),
    },
  ];

  return (
    <CndTable
      fetchData={fetchData}
      columns={columns}
      pagination={false}
      primaryKey="UpdateTime"
      operation={primaryOperation}
      showRefreshButton
      rowSelection={{
        selectedRowKeys,
        onChange,
        mode: 'multiple',
        titleProps: () => ({
          style: { display: 'none' },
        }),
      }}
    />
  );
};

export default VersionList;
