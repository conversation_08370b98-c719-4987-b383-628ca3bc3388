import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Actions, Button, Copy, Dialog, Link, LinkButton, Message, Truncate } from '@ali/cnd';
import CndTable from '@ali/cnd-table';
import services from '~/services';
import { get, isEmpty, map, noop } from 'lodash';
import moment from 'moment';
import ConfigMapSlideButton from '~/components/shared/ConfigMapSlide';
import ConfigMapEditButton from './components/ConfigMapEditButton';
import CachedData from '~/cache/common';
import SlideWrap from '~/components/shared/SlideWrap';
import VersionList from './components/VersionList';

const ConfigMap = (props) => {
  const { regionId, namespaceId } = props;

  const [configMapNames, setConfigMapNames] = useState([]);
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    setRefreshIndex(refreshIndex + 1);
  }, [regionId,namespaceId]);

  const onRefresh = () => {
    setRefreshIndex(refreshIndex + 1);
  };

  const handleDelete = ({ record }) => {
    if (get(record, 'RelateApps.length') === 0 || !record.RelateApps) {
      const onOk = async () => {
        const { Data } = await services.DeleteConfigMap({
          params: {
            ConfigMapId: record.ConfigMapId,
          },
        });
        Message.success(intl('saenext.namespace.configMap.TheConfigurationItemHasBeen'));
        onRefresh();
        return Data;
      };
      Dialog.alert({
        title: intl('saenext.namespace.configMap.DeleteAConfigurationItem'),
        content: (
          <>
            {intl('saenext.namespace.configMap.ConfirmToDeleteAConfiguration')}
            <span className="color-orange">{record.Name}</span>
            {intl('saenext.namespace.configMap.IsIt')}
          </>
        ),

        onOk,
      });
    } else {
      Dialog.alert({
        title: intl('saenext.namespace.configMap.DeleteAConfigurationItem'),
        content: <p>{intl('saenext.namespace.configMap.ExistingApplicationsAreUsingThis')}</p>,
        footer: false,
      });
    }
  };

  const fetchData = async () => {
    const { Data } = await services.ListNamespacedConfigMaps({
      NamespaceId: namespaceId,
    });
    const configMaps = get(Data, 'ConfigMaps');
    const nameList = map(configMaps, 'Name');
    setConfigMapNames(nameList);
    return {
      data: configMaps,
    };
  };

  const columns = [
    {
      key: 'Name',
      title: intl('saenext.namespace.configMap.ConfigurationItemNameId'),
      dataIndex: 'Name',
      width: 160,
      cell: (value, index, record) => (
        <div>
          <Copy text={value}>{value}</Copy>
          <div> {record.ConfigMapId} </div>
        </div>
      ),
    },
    {
      key: 'NamespaceId',
      title: intl('saenext.namespace.configMap.Namespace'),
      dataIndex: 'NamespaceId',
      width: 160,
      cell: (value, index, record) => <Copy text={value}>{value}</Copy>,
    },
    {
      key: 'Description',
      title: intl('saenext.namespace.configMap.ConfigurationItemDescription'),
      dataIndex: 'Description',
      width: 200,
    },
    {
      key: 'RelateApps',
      title: intl('saenext.namespace.configMap.AssociatedApplications'),
      dataIndex: 'RelateApps',
      width: 200,
      cell: (value, index, record) => {
        if (isEmpty(value)) return '--';

        const isPre = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';
        const origin = isPre
        ? CachedData.confLink('feature:pre-saenext:url')
        : CachedData.confLink('feature:saenext:url');
        return (
          <Truncate type="width" threshold={200}>
            {value.map((item, index) => {
              return (
                <>
                  <a
                    href={`${origin}/${regionId}/app-list/${item.AppId}/micro-app/base?name=${item.AppName}`}
                    target="_blank"
                    className="mr-s"
                  >
                    {item.AppName}
                  </a>
                </>
              );
            })}
          </Truncate>
        );
      },
    },
    {
      key: 'CreateTime',
      title: intl('saenext.namespace.configMap.CreationTime'),
      dataIndex: 'CreateTime',
      width: 200,
      cell: (value, index, record) => moment(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      key: 'UpdateTime',
      title: intl('saenext.namespace.common.LatestUpdateTime'),
      dataIndex: 'UpdateTime',
      width: 200,
      cell: (value, index, record) => moment(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      key: 'ConfigMapId',
      title: intl('saenext.namespace.configMap.Operation'),
      dataIndex: 'ConfigMapId',
      width: 200,
      cell: (value, index, record) => {
        return (
          <Actions>
            <ConfigMapEditButton
              record={record}
              onRefresh={onRefresh}
              namespaceId={namespaceId}
              nameList={configMapNames}
            >
              <LinkButton>{intl('saenext.namespace.configMap.Edit')}</LinkButton>
            </ConfigMapEditButton>
            <ConfigMapSlideButton
              linkButton
              buttonText={intl('saenext.namespace.configMap.Copy')}
              slideTitle={intl('saenext.namespace.configMap.CopyConfigurationItems')}
              type="copy"
              reload={onRefresh}
              namespaceId={namespaceId}
              ConfigMapId={record.ConfigMapId}
              nameList={configMapNames}
            />

            <SlideWrap
              title={intl('saenext.namespace.config-map.ViewHistoryRecordname', {
                recordName: record.Name,
              })}
              // width={1200}
              onMaskClick={noop}
              onClose={noop}
              onCancel={noop}
              content={<VersionList record={record} />}
            >
              <LinkButton>{intl('saenext.namespace.config-map.ViewHistory')}</LinkButton>
            </SlideWrap>

            <LinkButton onClick={() => handleDelete({ record })}>
              {intl('saenext.namespace.configMap.Delete')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];

  return (
    <>
      <Message type="notice" className="mb-s">
        <div className="text-line">
          <span>{intl('saenext.namespace.configMap.ConfigurationItemKSConfigmap')}</span>
          <a href={CachedData.confLink('help:sae:manage-a-kubernetes-configmap')} target="_blank">
            {intl('saenext.namespace.configMap.HowToUseConfigurationItems')}
          </a>
        </div>
        <div className="text-line color-orange">
          {intl('saenext.namespace.configMap.ForTheTimeBeingThe')}
        </div>
      </Message>
      <CndTable
        fetchData={fetchData}
        columns={columns}
        refreshIndex={refreshIndex}
        showRefreshButton
        pagination={false}
        operation={
          <ConfigMapSlideButton
            buttonText={intl('saenext.namespace.configMap.Create')}
            slideTitle={intl('saenext.namespace.configMap.CreateAConfigurationItem')}
            type="create"
            reload={onRefresh}
            namespaceId={namespaceId}
            nameList={configMapNames}
          />
        }
      />
    </>
  );
};

export default ConfigMap;
