import React, { useState, useEffect } from 'react';
import services from '~/services';
import { get, map, head } from 'lodash';
import { Loading, Tab } from '@ali/cnd';
import RecordDesc from './RecordDesc';
import PipelinePanel from './PipelinePanel';


/**
 * 
 * 流程  
 * 1、命名空间变更单 changeOrderId
 * DescribeChangeOrder  --->  流水线条数 pipelineId --->  DescribePipeline --->  流水线的阶段 stageId --->  DescribeNestedStage
 * ---> 阶段详情 应用的changeOrderId
 * 
 * 2、应用变更单 changeOrderId
 * DescribeChangeOrder  --->  应用的流水线条数 pipelineId --->  DescribePipeline --->  应用的流水线的阶段及详情
 */


const ChangeRecord = (props) => {
  const { changeOrderId } = props;
  const [loading, setLoading] = useState(false);
  const [changeOrder, setChangeOrder] = useState({});
  const [pipelines, setPipelines] = useState([]);
  const [multiPipeline, setMultiPipeline] = useState(false);

  useEffect(() => {
    getChangeOrder(changeOrderId);
  }, []);

  // 获取变更单包含的流水线 （命名空间）
  const getChangeOrder = async (changeOrderId) => {
    setLoading(true);
    const res = await services.getChangeOrder({
      params: {
        ChangeOrderId: changeOrderId,
      },
      customErrorHandle(err, data, callback) {
        setLoading(false);
        callback && callback();
      },
    });
    const { Data: _changeOrder = {} } = res || {};
    const _batchCount = get(_changeOrder, 'BatchCount', 1);
    const _pipelines = get(_changeOrder, 'Pipelines', []);
    const _multiPipeline = _batchCount > 1 && _pipelines.length > 1;
    setChangeOrder(_changeOrder);
    setPipelines(_pipelines);
    setMultiPipeline(_multiPipeline);
    setLoading(false);
  };

  return (
    <Loading
      visible={loading}
      className="full-width full-height"
    >
      <RecordDesc 
        changeOrder={changeOrder} 
        changeOrderId={changeOrderId}
      />
      {
        multiPipeline ? (
          <Tab 
            shape="wrapped" 
            className='mt-l'
          >
            {
              map(pipelines, pipeline => {
                return (
                  <Tab.Item 
                    key={pipeline.PipelineId}
                    title={pipeline.PipelineName} 
                  >
                    <PipelinePanel pipeline={pipeline} />
                  </Tab.Item>
                )
              })
            }
          </Tab>
        ) : (
          <PipelinePanel pipeline={head(pipelines)} />
        )
      }
    </Loading>
  )
}

export default ChangeRecord;