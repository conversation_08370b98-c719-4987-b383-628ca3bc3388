import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import services from '~/services';
import { get, map, head } from 'lodash';
import { Loading, Tab, Icon } from '@ali/cnd';
import AppPipelinePanel from './AppPipelinePanel';

const AppRecordDesc = (props) => {
  const { changeOrderId } = props;

  if (!changeOrderId)
    return (
      <div className="flex pt-xl pb-xl" style={{ justifyContent: 'center' }}>
        <Icon type="document" size="small" style={{ marginLeft: -48 }} />

        <span className="ml-s">
          {intl('saenext.namespace.record.AppRecordDesc.NoApplicationChangeRecords')}
        </span>
      </div>
    );

  const [loading, setLoading] = useState(false);
  const [pipelines, setPipelines] = useState([]);
  const [multiPipeline, setMultiPipeline] = useState(false);

  useEffect(() => {
    getChangeOrder(changeOrderId);
  }, [changeOrderId]);

  // 获取变更单包含的流水线 （命名空间）
  const getChangeOrder = async (changeOrderId) => {
    if (!changeOrderId) return;
    setLoading(true);
    const res = await services.getChangeOrder({
      params: {
        ChangeOrderId: changeOrderId,
      },
      customErrorHandle(err, data, callback) {
        setLoading(false);
        callback && callback();
      },
    });
    const { Data: _changeOrder = {} } = res || {};
    const _batchCount = get(_changeOrder, 'BatchCount', 1);
    const _pipelines = get(_changeOrder, 'Pipelines', []);
    const _multiPipeline = _batchCount > 1 && _pipelines.length > 1;
    setPipelines(_pipelines);
    setMultiPipeline(_multiPipeline);
    setLoading(false);
  };

  return (
    <Loading visible={loading} className="full-width full-height">
      {multiPipeline ? (
        <Tab shape="wrapped" className="mt-l">
          {map(pipelines, (pipeline) => {
            return (
              <Tab.Item key={pipeline.PipelineId} title={pipeline.PipelineName}>
                <AppPipelinePanel pipeline={pipeline} changeOrderId={changeOrderId} />
              </Tab.Item>
            );
          })}
        </Tab>
      ) : (
        <AppPipelinePanel pipeline={head(pipelines)} changeOrderId={changeOrderId} />
      )}
    </Loading>
  );
};

export default AppRecordDesc;
