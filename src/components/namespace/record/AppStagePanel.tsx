import React from 'react';
import { map } from 'lodash';
import { Collapse } from '@ali/cnd';
import RecordStatus, { StatusMap } from '../revision/RecordStatus';

const Panel = Collapse.Panel;

const AppStagePanel = (props) => {
  const { stage } = props;
  const { TaskList = [] } = stage;

  return (
    <div className='nstage-panel'>
      {
        map(TaskList, task => {
          const { TaskId, TaskName, Status, Message } = task;
          const { color } = StatusMap[Status];
          return (
            <Collapse key={TaskId}>
              <Panel 
                title={
                  <div className='flex'>
                    <span className='mr-xl'>{TaskName}</span>
                    <RecordStatus value={Status} />
                  </div>
                }
              >
                <div
                  style={{ color }}
                  className='nstage-panel-body'
                >
                  {Message}
                </div>
              </Panel>
            </Collapse>
          );
        })
      }
    </div>
  )
}

export default AppStagePanel;