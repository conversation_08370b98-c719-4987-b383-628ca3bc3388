import React, { useEffect, useState } from 'react';
import { get, map, head } from 'lodash';
import services from '~/services';
import { intl, Loading, Timeline, StatusIndicator } from '@ali/cnd';
import { StatusMap } from '../revision/RecordStatus';
import StagePanel from './StagePanel';

const PipelinePanel = (props) => {
  const { pipeline = {} } = props;
  const { PipelineId } = pipeline;
  const [loading, setLoading] = useState(false);
  const [stageList, setStageList] = useState([]);

  useEffect(() => {
    getPipeline();
  }, [JSON.stringify(pipeline)]);

  // 获取流水线包含的stageList
  const getPipeline = async () => {
    if (!PipelineId) return;
    setLoading(true);
    const res = await services.getPipeline({
      params: {
        PipelineId: PipelineId,
      },
      customErrorHandle(err, data, callback) {
        setLoading(false);
        callback && callback();
      },
    });
    const { Data: _pipeline = {} } = res || {};
    const _stageList = get(_pipeline, 'StageList', []);
    setStageList(_stageList);
    // setStageList([
    //   {
    //     "Status":2,
    //     "StageName":"批量停止应用",
    //     "StageId":"7e7efedb-74a5-46b0-8cdc-b5a9d0509bf6@co_virtual_stage",
    //     "ExecutorType":1,
    //     "TaskList":[]
    //   },
    //   {
    //     "Status":2,
    //     "StageName":"批量停止应用",
    //     "StageId":"7e7efedb-74a5-46b0-8cdc-b5a9d0509bf6@co_virtual_stage",
    //     "ExecutorType":1,
    //     "TaskList":[]
    //   }
    // ]);
    setLoading(false);
  };

  return (
    <Loading 
      visible={loading} 
      style={{ minHeight: 100 }} 
      className="full-width full-height"
    >
      <div className="mt-l">
        {stageList.length > 0 ? (
          <StagePanelTemplate stageList={stageList} pipelineId={PipelineId} />
        ) : (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              padding: '60px 0',
            }}
          >
            <svg
              // @ts-ignore
              t="1713236071282"
              class="icon"
              viewBox="0 0 2070 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="1776"
              width="256"
              height="256"
            >
              <path
                d="M1863.365688 418.407252H647.077827c-10.74646 0-19.466948 9.887624-19.466948 22.087499 0 12.188864 8.720488 22.076488 19.466948 22.076488h34.463544v528.327242c0 12.199875 8.709477 22.087499 19.466948 22.087499s19.466948-9.887624 19.466948-22.087499l0.29729-528.327242h976.188162l90.012613 528.327242c0 12.199875 8.709477 22.087499 19.466948 22.087499s19.477959-9.887624 19.477959-22.087499l5.659509-528.327242h31.78794c10.74646 0 19.466948-9.887624 19.466948-22.076488 0-12.199875-8.720488-22.087499-19.466948-22.087499z"
                fill="#CCCCCC"
                p-id="1777"
              ></path>
              <path
                d="M869.846656 330.343537v88.041694H659.971376c-14.974575 0.550536-29.068293-7.344148-36.996009-20.689138a45.980755 45.980755 0 0 1-1.464426-44.009836c7.024838-13.895525 20.57903-22.792185 35.542595-23.34272a39.858796 39.858796 0 0 1 2.91784 0H869.846656z"
                fill="#F2F2F2"
                p-id="1778"
              ></path>
              <path
                d="M869.846656 414.795737c0 1.981929-1.398361 3.611515-3.116033 3.611515H659.10153C635.61567 418.385231 616.600162 396.209647 616.600162 368.859025s19.015509-49.526206 42.501368-49.548227h207.629093c1.112082 0 2.14709 0.682664 2.697626 1.805758 0.550536 1.123093 0.550536 2.499433 0 3.622525a3.049969 3.049969 0 0 1-2.697626 1.805758H659.10153c-20.039505 0.033032-36.258292 18.971466-36.258292 42.314186s16.229797 42.281154 36.258292 42.314186h207.640104c1.717672 0 3.105022 1.618575 3.105022 3.622526z"
                fill="#6C63FF"
                p-id="1779"
              ></path>
              <path
                d="M858.219339 353.069657H639.249207c-0.341332 0-0.627611-0.330322-0.627611-0.715697 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981143c0.330322 0 0.6166 0.330322 0.6166 0.726707 0 0.396386-0.275268 0.715697-0.6166 0.726708zM858.219339 364.080374H639.249207c-0.341332 0-0.627611-0.330322-0.627611-0.715697 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981143c0.330322 0 0.6166 0.330322 0.6166 0.726708 0 0.396386-0.275268 0.715697-0.6166 0.726707zM858.219339 375.091091H639.249207c-0.341332 0-0.627611-0.330322-0.627611-0.715697 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981143c0.330322 0 0.6166 0.330322 0.6166 0.726708 0 0.396386-0.275268 0.715697-0.6166 0.726707zM858.219339 397.112525H639.249207c-0.341332 0-0.627611-0.330322-0.627611-0.715696 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981143c0.330322 0 0.6166 0.330322 0.6166 0.726707 0 0.396386-0.275268 0.715697-0.6166 0.726707z"
                fill="#CCCCCC"
                p-id="1780"
              ></path>
              <path
                d="M825.803788 231.247082v88.041694H615.928508c-23.133517 0.836815-42.545411-18.178694-43.349194-42.490357-0.803782-24.311664 17.297837-44.703512 40.431354-45.551337a39.858796 39.858796 0 0 1 2.91784 0H825.803788z"
                fill="#F2F2F2"
                p-id="1781"
              ></path>
              <path
                d="M825.803788 315.699283c0 1.981929-1.38735 3.611515-3.105023 3.611515H615.080683C591.594823 319.310798 572.557293 297.124203 572.557293 269.762571s19.03753-49.548227 42.52339-49.548228h207.618082c1.101072 0 2.136079 0.693675 2.686615 1.805758 0.550536 1.123093 0.550536 2.499433 0 3.622526a3.049969 3.049969 0 0 1-2.686615 1.805758H615.080683c-20.061527 0-36.313345 18.938434-36.313346 42.314186 0 23.364742 16.251819 42.314186 36.313346 42.314186h207.618082c1.717672 0 3.105022 1.618575 3.105023 3.622526z"
                fill="#3F3D56"
                p-id="1782"
              ></path>
              <path
                d="M825.187188 242.962485H606.217055c-0.341332 0-0.627611-0.330322-0.627611-0.715697 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981144c0.330322 0 0.6166 0.330322 0.6166 0.726708 0 0.396386-0.275268 0.715697-0.6166 0.726707zM825.187188 264.983919H606.217055c-0.341332 0-0.627611-0.330322-0.627611-0.715696 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981144c0.330322 0 0.6166 0.330322 0.6166 0.726707 0 0.396386-0.275268 0.715697-0.6166 0.726707zM825.187188 287.005354H606.217055c-0.341332 0-0.627611-0.330322-0.627611-0.715697 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981144c0.330322 0 0.6166 0.330322 0.6166 0.726707 0 0.396386-0.275268 0.715697-0.6166 0.726708zM825.187188 298.016071H606.217055c-0.341332 0-0.627611-0.330322-0.627611-0.715697 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981144c0.330322 0 0.6166 0.330322 0.6166 0.726708 0 0.396386-0.275268 0.715697-0.6166 0.726707zM1068.039565 748.728768v-75.500488c0-6.947763-5.37323-12.58525-12.001681-12.58525H782.751884c-6.628452 0-12.001682 5.637487-12.001682 12.58525V748.728768h297.289363zM770.750202 781.760919h297.289363v110.107172H770.750202zM770.750202 924.900242v75.500488c0 6.947763 5.37323 12.58525 12.001682 12.58525h273.286c6.628452 0 12.001682-5.637487 12.001681-12.58525V924.900242H770.750202z"
                fill="#CCCCCC"
                p-id="1783"
              ></path>
              <path
                d="M908.384167 715.696616a11.010717 11.010717 0 1 0 22.021434 0 11.010717 11.010717 0 1 0-22.021434 0Z"
                fill="#FFFFFF"
                p-id="1784"
              ></path>
              <path
                d="M908.384167 847.825222a11.010717 11.010717 0 1 0 22.021434 0 11.010717 11.010717 0 1 0-22.021434 0Z"
                fill="#FFFFFF"
                p-id="1785"
              ></path>
              <path
                d="M908.384167 979.953828a11.010717 11.010717 0 1 0 22.021434 0 11.010717 11.010717 0 1 0-22.021434 0Z"
                fill="#FFFFFF"
                p-id="1786"
              ></path>
              <path
                d="M1023.996697 572.579314v88.041695H814.099395c-23.122506 0.825804-42.52339-18.189705-43.327172-42.490358-0.803782-24.300653 17.286826-44.68149 40.409332-45.551337 0.968943-0.033032 1.937886-0.033032 2.91784 0H1023.996697z"
                fill="#F2F2F2"
                p-id="1787"
              ></path>
              <path
                d="M1035.007414 668.042232c0 1.981929-1.398361 3.611515-3.116033 3.611515H824.284309c-23.474849 0-42.501368-22.164574-42.52339-49.515195-0.011011-27.361632 19.004498-49.548227 42.479347-49.581259h207.662126c1.101072 0 2.136079 0.682664 2.686615 1.805757 0.550536 1.123093 0.550536 2.499433 0 3.622526a3.049969 3.049969 0 0 1-2.697626 1.805758H824.284309c-20.039505 0.033032-36.258292 18.960455-36.258292 42.314186 0 23.34272 16.229797 42.281154 36.258292 42.314186h207.607072c1.717672 0 3.105022 1.618575 3.105022 3.622526z"
                fill="#3F3D56"
                p-id="1788"
              ></path>
              <path
                d="M1034.390814 584.294717H815.420681c-0.341332 0-0.627611-0.330322-0.62761-0.715696 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981143c0.330322 0 0.6166 0.330322 0.6166 0.726707 0 0.396386-0.275268 0.715697-0.6166 0.726707zM1034.390814 606.316152H815.420681c-0.341332 0-0.627611-0.330322-0.62761-0.715697 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981143c0.330322 0 0.6166 0.330322 0.6166 0.726707 0 0.396386-0.275268 0.715697-0.6166 0.726708zM1034.390814 617.326869H815.420681c-0.341332 0-0.627611-0.330322-0.62761-0.715697 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981143c0.330322 0 0.6166 0.330322 0.6166 0.726708 0 0.396386-0.275268 0.715697-0.6166 0.726707zM1034.390814 639.348303H815.420681c-0.341332 0-0.627611-0.330322-0.62761-0.715696 0-0.407397 0.275268-0.726707 0.6166-0.737718h218.981143c0.330322 0 0.6166 0.330322 0.6166 0.726707 0 0.396386-0.275268 0.715697-0.6166 0.726707z"
                fill="#CCCCCC"
                p-id="1789"
              ></path>
              <path
                d="M787.398406 198.192909H690.482074a6.121959 6.121959 0 0 0-2.642572 0.605589l-24.465814 11.913596A4.338223 4.338223 0 0 0 660.64303 214.708985c0 1.739693 1.06804 3.303215 2.719647 3.99689l24.454803 11.913596a6.121959 6.121959 0 0 0 2.653583 0.594579h96.927343a5.042908 5.042908 0 0 0 5.37323-4.591469v-23.827192a5.042908 5.042908 0 0 0-5.37323-4.591469z"
                fill="#3F3D56"
                p-id="1790"
              ></path>
              <path
                d="M792.771636 202.795389v23.827192c-0.176171 2.708636-2.312251 4.745619-4.789662 4.591469H770.750202v-33.01013h17.231772c2.477411-0.15415 4.624501 1.882833 4.789662 4.591469z"
                fill="#6C63FF"
                p-id="1791"
              ></path>
              <path
                d="M227.723653 732.862324c23.452828 9.942678 49.493174 4.404287 68.706875-14.611222 24.069428-24.388739 31.611769-64.555835 37.755749-101.739026L352.342949 506.49299l-38.020006 31.600758c-27.339611 22.71511-55.295822 46.167937-74.223244 79.089982-18.938434 32.911034-27.196471 77.84577-11.990671 113.454429"
                fill="#E6E6E6"
                p-id="1792"
              ></path>
              <path
                d="M226.76572 856.435603c-4.206094-33.527634-8.533306-67.484686-5.582433-101.375673 2.620551-30.09229 11.010717-59.490905 28.088339-83.593365a131.963445 131.963445 0 0 1 32.591723-32.327465c3.259172-2.246186 6.265098 3.413322 3.016936 5.648497-21.625049 14.963565-38.350328 37.051063-47.775501 63.09141-10.394117 28.958186-12.067746 60.525912-10.273 91.278845 1.07905 18.608112 3.38029 37.084095 5.692541 55.549068 0.374364 1.728683-0.528514 3.479387-2.092036 4.018912-1.585543 0.46245-3.215129-0.550536-3.666569-2.290229z"
                fill="#F2F2F2"
                p-id="1793"
              ></path>
              <path
                d="M275.267929 796.691452c10.603321 18.861359 28.848079 29.883086 48.116834 29.068293 24.366717-1.354318 44.68149-21.250684 62.970292-40.145075L440.428687 729.73528l-35.784831-2.00395c-25.743057-1.431393-52.146757-2.785711-76.667624 6.496323-24.509856 9.304056-47.125869 31.655812-51.607231 61.351716"
                fill="#E6E6E6"
                p-id="1794"
              ></path>
              <path
                d="M220.599718 876.265905c19.687162-40.882793 42.512379-86.324023 83.318097-100.85817a79.948817 79.948817 0 0 1 35.047113-4.239126c3.721622 0.374364 2.785711 7.112923-0.9249 6.738559-19.786259-1.937886-39.539485 4.195083-55.934443 17.363901-15.767347 12.59626-28.044297 30.114311-38.427403 48.689391-6.375205 11.374071-12.067746 23.232613-17.771298 35.080145-1.827779 3.787687-7.156966 1.057029-5.307166-2.7747z"
                fill="#F2F2F2"
                p-id="1795"
              ></path>
              <path
                d="M184.044137 920.804255c1.343307-17.50704 11.230932-32.723851 25.621939-39.46241V858.835939h33.494602v23.221603c13.686321 7.05787 22.935324 21.856274 24.223578 38.757724L275.267929 1023.996697h-99.096454l7.872662-103.192442z"
                fill="#F2F2F2"
                p-id="1796"
              ></path>
              <path
                d="M1319.931742 413.782751l-10.394117-4.75663-7.134945-56.683172h-94.989457l-7.729523 56.451947-9.304056 5.06493a2.477411 2.477411 0 0 0-1.167136 2.697626 2.257197 2.257197 0 0 0 2.1581 1.8498h127.702298a2.268208 2.268208 0 0 0 2.169111-1.926875 2.45539 2.45539 0 0 0-1.310275-2.686615z"
                fill="#E6E6E6"
                p-id="1797"
              ></path>
              <path
                d="M1477.649255 363.353667H1032.805271c-4.866737-0.011011-8.797563-4.261148-8.797563-9.491239V286.278646h462.450121v67.583782c0 5.230091-3.930826 9.469217-8.797563 9.491239z"
                fill="#CCCCCC"
                p-id="1798"
              ></path>
              <path
                d="M1486.446818 319.310798H1023.996697V12.155832c0-6.716537 4.745619-12.144821 10.60332-12.155832h441.24348c5.857702 0 10.59231 5.450305 10.603321 12.155832V319.310798z"
                fill="#3F3D56"
                p-id="1799"
              ></path>
              <path
                d="M1467.145031 297.289364H1054.309201c-4.580458 0-8.29107-4.206094-8.29107-9.392142V31.413576c0-5.175037 3.710612-9.381131 8.29107-9.392142h412.83583c4.580458 0 8.29107 4.206094 8.29107 9.392142v256.483646c0 5.175037-3.710612 9.381131-8.29107 9.392142z"
                fill="#FFFFFF"
                p-id="1800"
              ></path>
              <path
                d="M1362.047735 392.631164c5.119983-5.494348 12.067746-8.059845 18.982477-7.035849 6.91473 1.035007 13.069721 5.571423 16.835386 12.387057l52.983571-10.933642L1464.425384 422.800529l-75.016017 14.225846c-10.504224 6.716537-23.684053 3.237151-30.664847-8.092877-6.980795-11.319017-5.571423-26.921203 3.303215-36.302334z"
                fill="#FFB6B6"
                p-id="1801"
              ></path>
              <path
                d="M1398.361081 429.41797l57.751211-0.825804 7.311116-0.110107 117.484353-1.695651s31.909058-34.84892 59.689097-70.732847l-3.897794-32.25039c-3.798697-31.644801-17.50704-60.636019-38.581553-81.567393-39.792732 30.444633-62.871195 90.695277-62.871195 90.695277l-72.835894 19.95142-7.289094 1.992939-45.265059 12.387057L1398.361081 429.41797z"
                fill="#6C63FF"
                p-id="1802"
              ></path>
              <path
                d="M1697.555298 627.610879h-108.565671c0 5.06493-118.695531 9.171927-118.695531 9.171927a45.176973 45.176973 0 0 0-7.101913 7.344148 48.06178 48.06178 0 0 0-9.777517 29.365583v7.333138c0 25.33566 18.872369 45.870648 42.138015 45.881658h202.002617c23.265645-0.022021 42.115993-20.545998 42.138015-45.881658v-7.333138c-0.033032-25.324649-18.88338-45.848626-42.138015-45.881658zM1574.532555 726.707333h44.042869v286.278647h-44.042869z"
                fill="#E4E4E4"
                p-id="1803"
              ></path>
              <path
                d="M1453.414666 1020.682471c0 5.791637 66.548775 2.235176 148.644682 2.235176s148.644682 3.556462 148.644682-2.235176c0-5.802648-66.548775-51.73936-148.644682-51.73936S1453.414666 1014.879823 1453.414666 1020.682471z"
                fill="#E4E4E4"
                p-id="1804"
              ></path>
              <path
                d="M1431.393232 990.964545h-29.839043L1387.350363 847.825222h44.042869z"
                fill="#FFB6B6"
                p-id="1805"
              ></path>
              <path
                d="M1431.393232 1012.98598h-99.096454v-1.387351c0-23.562935 17.264805-42.655518 38.570542-42.655518H1431.393232v44.042869z"
                fill="#2F2E41"
                p-id="1806"
              ></path>
              <path
                d="M1332.296778 990.964545h-29.839044L1288.253909 847.825222h44.042869z"
                fill="#FFB6B6"
                p-id="1807"
              ></path>
              <path
                d="M1332.296778 1012.98598h-99.096455v-1.387351c0-23.562935 17.264805-42.655518 38.570542-42.655518H1332.296778v44.042869z"
                fill="#2F2E41"
                p-id="1808"
              ></path>
              <path
                d="M1563.521838 99.096455a66.064303 55.053586 90 1 0 110.107172 0 66.064303 55.053586 90 1 0-110.107172 0Z"
                fill="#FFB6B6"
                p-id="1809"
              ></path>
              <path
                d="M1704.414975 537.730394s18.960455 113.102087-53.181764 127.504105c-72.15323 14.413029-176.303603 24.531878-176.303603 24.531878L1428.167092 935.910959 1365.328929 929.579797s8.357134-296.298399 28.903133-319.486969C1414.789071 586.915268 1544.67149 517.503707 1544.67149 517.503707l159.743485 20.226687z"
                fill="#2F2E41"
                p-id="1810"
              ></path>
              <path
                d="M1574.961973 495.482273l-31.622779 35.487541s-228.285199 41.14705-237.093773 100.142473C1292.944474 720.188989 1277.243192 913.889525 1277.243192 913.889525l68.640811-5.758605 26.910192-220.181311 109.820893-27.956211L1607.564707 583.799235 1574.961973 495.482273zM1668.707219 67.25346c-5.582434-11.693382-12.684346-35.00307-23.970331-39.319271a82.172982 82.172982 0 0 0-19.312798-4.558436l-19.180669 8.169952 10.43816-8.995756a254.292513 254.292513 0 0 0-6.088927-0.330322l-12.937593 5.51637 6.617441-5.703552c-12.067746-0.143139-24.509856 1.255222-33.857955 9.392142-7.971759 6.936752-15.866443 33.230344-17.143687 44.37319a93.877375 93.877375 0 0 0 1.409372 31.975123l3.270183 3.457365c1.156125-2.906829 2.014961-5.945787 2.576508-9.072831 2.378315-11.781467 9.105863-21.900316 18.553058-27.901157l0.176172-0.110108c5.505359-3.424333 12.166842-3.270183 18.464973-3.016936l29.93814 1.189157c7.201009 0.286279 14.952554 0.792772 20.568019 5.802648 3.567472 3.699601 6.375205 8.202984 8.225006 13.212861 2.785711 6.243077 8.247027 29.78399 8.247027 29.78399s3.083001-4.44833 4.558437-1.134104c2.367304-8.632402 3.809708-17.540072 4.29418-26.546839a56.848333 56.848333 0 0 0-4.844716-26.183486z"
                fill="#2F2E41"
                p-id="1811"
              ></path>
              <path
                d="M1876.897859 132.205681c-7.652448 0.550536-14.688297 4.734608-19.224712 11.451146a30.048247 30.048247 0 0 0-4.239126 23.353731L1805.757616 205.151682 1820.071548 242.235778l66.62585-54.899436c12.58525-4.349233 20.182645-18.211726 17.683212-32.283423-2.499433-14.071697-14.313932-23.893256-27.482751-22.847238z"
                fill="#FFB6B6"
                p-id="1812"
              ></path>
              <path
                d="M1860.811202 205.085618L1748.788165 352.342949s-53.567139-20.303762-94.989457-42.303175l-18.608112-67.660857a157.717513 157.717513 0 0 1-5.450305-48.689391c57.431901-18.090608 124.233922 49.217906 124.233922 49.217905L1837.820824 165.160758 1860.811202 205.085618z"
                fill="#6C63FF"
                p-id="1813"
              ></path>
              <path
                d="M1594.120621 555.182381c-19.896366-8.059845-40.475396-16.405969-62.673002-19.522001l-0.957932-0.14314 0.264257-1.07905c22.979367-94.58206 17.242783-155.096962 5.978819-245.979422-4.91078-40.266193 13.356-79.519399 44.890694-96.475903l0.132129-0.066065 63.399709-4.734608h0.14314l46.101872 15.899475c13.642279 4.734608 22.902292 19.555034 22.638035 36.22526-0.495482 29.883086 0.561547 63.25657 1.68464 98.589962 2.521454 79.255142 5.119983 161.218921-9.645389 214.400684l-0.077075 0.275268-0.198193 0.187182c-13.576214 12.948603-30.576762 19.95142-48.072791 19.79727-20.700148 0-41.818704-8.555327-63.608913-17.374912z"
                fill="#6C63FF"
                p-id="1814"
              ></path>
              <path
                d="M2067.019913 1023.996697H2.994915C1.343307 1023.996697 0 1021.530296 0 1018.491338s1.343307-5.505359 2.994915-5.505358h2064.024998c1.651608 0 2.994915 2.466401 2.994915 5.505358s-1.343307 5.505359-2.994915 5.505359z"
                fill="#CCCCCC"
                p-id="1815"
              ></path>
            </svg>
            <span style={{ color: '#e0e0e0', fontSize: 14, marginTop: -46 }}>
              {intl('saenext.namespace.record.PipelinePanel.TheCurrentPipelineOfThe')}
            </span>
          </div>
        )}
      </div>
    </Loading>
  );
};

const StagePanelTemplate = (props) => {
  const { stageList = [], pipelineId } = props;
  const [onlyStage, setOnlyStage] = useState(true);
  const [stage, setStage] = useState({});

  useEffect(() => {
    checkOnlyStage();
  }, [stageList]);

  const checkOnlyStage = () => {
    if (!stageList.length) return;
    let _stage = head(stageList);
    // @ts-ignore
    const { Status } = _stage;
    const { color, label, icon } = StatusMap[Status];
    // @ts-ignore
    _stage = { color, label, icon, ..._stage };
    let _onlyStage = !(stageList.length > 1);
    setStage(_stage);
    setOnlyStage(_onlyStage);
  };

  return (
    <>
    {
      onlyStage ? (
        <div className='nstage-root'>
          <div className='nstage-status'>
            <StatusIndicator type={get(stage, 'icon')} />
            <div 
              className='title' 
              style={{ color: get(stage, 'color') }}
            >
              <span>{get(stage, 'StageName')}</span>
                <span style={{ fontWeight: 'normal' }}>
                    {intl("saenext.common.parenthesis.left")}
                    {get(stage, 'label')}
                    {intl("saenext.common.parenthesis.right")}
                </span>
            </div>
          </div>
          <div className='nstage-table'>
            <StagePanel stage={stage} pipelineId={pipelineId} />
          </div>
        </div>
      ) : (
        <Timeline className="nrecord-timeline">
          {
            map(stageList, (stage) => {
              const { Status } = stage;
              const { color, label, icon } = StatusMap[Status];
              return (
                <Timeline.Item
                  key={stage.StageId}
                  title={
                    <div style={{ color }}>
                      <span>{stage.StageName}</span>
                      <span style={{ fontWeight: 'normal' }}>
                          {intl("saenext.common.parenthesis.left")}
                          {label}
                          {intl("saenext.common.parenthesis.right")}
                      </span>
                    </div>
                  }
                  dot={<StatusIndicator type={icon} />}
                  content={<StagePanel stage={stage} pipelineId={pipelineId} />}
                />
              );
            })
          }
        </Timeline>
      )
    }
    </>
  );
}

export default PipelinePanel;
