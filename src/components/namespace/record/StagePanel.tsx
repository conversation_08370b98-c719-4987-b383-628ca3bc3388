import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import services from '~/services';
import { get, map, head } from 'lodash';
import { Copy } from '@ali/cnd';
import RecordStatus from '../revision/RecordStatus';
import CndTable, { ISearch } from '@ali/cnd-table';
import AppRecordDesc from './AppRecordDesc';

const StagePanel = (props) => {
  const { pipelineId, stage } = props;
  const { StageId } = stage;
  const [totalSize, setTotalSize] = useState(0);

  // 获取stage详情
  const getNestedStage = async (params) => {
    const res = await services.getNestedStage({
      params: {
        PipelineId: pipelineId,
        StageId: StageId,
        PageSize: params.pageSize,
        CurrentPage: params.current,
        QueryStatus: params.QueryStatus,
      },
      customErrorHandle(err, data, callback) {
        callback && callback();
        return {
          data: [],
          total: 0,
        };
      },
    });
    const { Data = {} } = res || {};
    const { InstanceList = [], TotalSize: _totalSize } = Data;
    setTotalSize(_totalSize);
    return {
      data: map(InstanceList, (item) => ({ ...item, primaryKey: item.AppId })),
      total: _totalSize,
    };
  };

  const columns = [
    {
      key: 'AppName',
      title: intl('saenext.namespace.record.StagePanel.ApplicationName'),
      width: '30%',
      dataIndex: 'AppName',
    },
    {
      key: 'AppId',
      title: intl('saenext.namespace.record.StagePanel.ApplicationId'),
      width: '35%',
      dataIndex: 'AppId',
      cell: (value) => <Copy text={value}>{value}</Copy>,
    },
    {
      key: 'Status',
      title: intl('saenext.namespace.record.StagePanel.ExecutionStatus'),
      width: '35%',
      dataIndex: 'Status',
      cell: (value) => <RecordStatus value={value} />,
    },
  ];

  const search = {
    defaultDataIndex: 'QueryStatus',
    defaultSelectedDataIndex: 'QueryStatus',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.namespace.record.StagePanel.ExecutionStatus'),
        dataIndex: 'QueryStatus',
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.namespace.record.StagePanel.SelectExecutionStatus'),
          dataSource: [
            { label: intl('saenext.namespace.record.StagePanel.NotExecuted'), value: 0 },
            { label: intl('saenext.namespace.record.StagePanel.Running'), value: 1 },
            { label: intl('saenext.namespace.record.StagePanel.SuccessfulExecution'), value: 2 },
            { label: intl('saenext.namespace.record.StagePanel.ExecutionFailed'), value: 3 },
            { label: intl('saenext.namespace.record.StagePanel.WaitForRetry'), value: 5 },
            { label: intl('saenext.namespace.record.StagePanel.ExecutionTermination'), value: 6 },
            {
              label: intl('saenext.namespace.record.StagePanel.WaitForConfirmationManually'),
              value: 8,
            },
            {
              label: intl('saenext.namespace.record.StagePanel.AutomaticallyWaitForExecution'),
              value: 9,
            },
            {
              label: intl(
                'saenext.namespace.record.StagePanel.SystemExceptionCausesExecutionFailure',
              ),
              value: 10,
            },
            { label: intl('saenext.namespace.record.StagePanel.PendingApproval'), value: 11 },
            { label: intl('saenext.namespace.record.StagePanel.Approved'), value: 12 },
          ],
        },
      },
    ],
  };

  return (
    <div className="nstage-panel full-width" style={{ paddingBottom: totalSize > 10 ? '16px' : 0 }}>
      <CndTable
        primaryKey="primaryKey"
        columns={columns as []}
        isZebra={false}
        // hasBorder={true}
        fetchData={getNestedStage as any}
        recordCurrent
        showRefreshButton
        pagination={{
          hideOnlyOnePage: true,
          pageSizeList: [10, 20, 50, 100],
        }}
        search={search as ISearch}
        expandedRowRender={(record) => {
          // 获取应用的 changeOrderId
          const taskList = get(record, 'TaskList', []);
          // 这里默认应用只有一个变更单
          const task = head(taskList) || { ChangeOrderId: '' };
          const changeOrderId = get(task, 'ChangeOrderId', '');
          return <AppRecordDesc changeOrderId={changeOrderId} />;
        }}
      />
    </div>
  );
};

export default StagePanel;
