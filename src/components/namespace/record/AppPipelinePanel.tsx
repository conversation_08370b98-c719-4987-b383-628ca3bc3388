import React, { useEffect, useState } from 'react';
import { get, map, head } from 'lodash';
import services from '~/services';
import { Loading, Timeline, intl } from '@ali/cnd';
import { StatusIndicator } from '@ali/cnd';
import { StatusMap } from '../revision/RecordStatus';
import AppStagePanel from './AppStagePanel';


const PipelinePanel = (props) => {
  const { pipeline = {}, changeOrderId } = props;
  const { PipelineId } = pipeline;
  const [loading, setLoading] = useState(false);
  const [stageList, setStageList] = useState([]);
  const [stage, setStage] = useState({});
  const [onlyStage, setOnlyStage] = useState(false);

  useEffect(() => {
    getPipeline();
  }, [JSON.stringify(pipeline)]);

  // 获取流水线包含的stageList
  const getPipeline = async () => {
    if (!PipelineId) return;
    setLoading(true);
    const res = await services.getPipeline({
      params: {
        PipelineId: PipelineId,
        ChangeOrderId: changeOrderId,
      },
      customErrorHandle(err, data, callback) {
        setLoading(false);
        callback && callback();
      },
    });
    const { Data: _pipeline = {} } = res || {};
    const _stageList = get(_pipeline, 'StageList', []);
    let _stage = head(_stageList);
    // @ts-ignore
    const { Status } = _stage;
    const { color, label, icon } = StatusMap[Status];
    // @ts-ignore
    _stage = { color, label, icon, ..._stage };
    let _onlyStage = !(stageList.length > 1);
    setStage(_stage);
    setOnlyStage(_onlyStage);
    setStageList(_stageList);
    // setStageList([
    //   {
    //     "Status":2,
    //     "StageName":"执行应用停止",
    //     "StageId":"4e771239-e986-47c2-aa11-5c58c2bfcdd0",
    //     "ExecutorType":0,
    //     "TaskList":[
    //       {
    //         "Status":2,
    //         "ErrorIgnore":0,
    //         "TaskId":"3285549b-4b74-4aea-b943-f238f20cb371",
    //         "Message":"Disable app scaling rules success.",
    //         "TaskName":"禁用弹性伸缩",
    //         "ShowManualIgnore":false,
    //         "StageId":"4e771239-e986-47c2-aa11-5c58c2bfcdd0",
    //         "CreateTime":1711369311167,
    //         "StartTime":1711369311538,
    //         "UpdateTime":1711369311669
    //       },
    //       {
    //         "Status":2,
    //         "ErrorIgnore":0,
    //         "TaskId":"fadcd158-c59b-4c4f-9dbf-3a0fc3ecd9be",
    //         "Message":"scale serverless app success.\n",
    //         "TaskName":"停止部署",
    //         "ShowManualIgnore":false,
    //         "StageId":"4e771239-e986-47c2-aa11-5c58c2bfcdd0",
    //         "CreateTime":1711369311168,
    //         "StartTime":1711369311702,
    //         "UpdateTime":1711369312288
    //       },
    //       {
    //         "Status":2,
    //         "ErrorIgnore":0,
    //         "TaskId":"1237e2b3-ee97-46a2-a990-9e0962926284",
    //         "Message":"Application deploy success",
    //         "TaskName":"执行应用部署",
    //         "ShowManualIgnore":false,
    //         "StageId":"4e771239-e986-47c2-aa11-5c58c2bfcdd0",
    //         "CreateTime":1711369311172,
    //         "StartTime":1711369312323,
    //         "UpdateTime":1711369354123
    //       }
    //     ]
    //   },
    //   {
    //     "Status":2,
    //     "StageName":"执行应用停止",
    //     "StageId":"4e771239-e986-47c2-aa11-5c58c2bfcdd0",
    //     "ExecutorType":0,
    //     "TaskList":[
    //       {
    //         "Status":2,
    //         "ErrorIgnore":0,
    //         "TaskId":"3285549b-4b74-4aea-b943-f238f20cb371",
    //         "Message":"Disable app scaling rules success.",
    //         "TaskName":"禁用弹性伸缩",
    //         "ShowManualIgnore":false,
    //         "StageId":"4e771239-e986-47c2-aa11-5c58c2bfcdd0",
    //         "CreateTime":1711369311167,
    //         "StartTime":1711369311538,
    //         "UpdateTime":1711369311669
    //       },
    //       {
    //         "Status":2,
    //         "ErrorIgnore":0,
    //         "TaskId":"fadcd158-c59b-4c4f-9dbf-3a0fc3ecd9be",
    //         "Message":"scale serverless app success.\n",
    //         "TaskName":"停止部署",
    //         "ShowManualIgnore":false,
    //         "StageId":"4e771239-e986-47c2-aa11-5c58c2bfcdd0",
    //         "CreateTime":1711369311168,
    //         "StartTime":1711369311702,
    //         "UpdateTime":1711369312288
    //       },
    //       {
    //         "Status":2,
    //         "ErrorIgnore":0,
    //         "TaskId":"1237e2b3-ee97-46a2-a990-9e0962926284",
    //         "Message":"Application deploy success",
    //         "TaskName":"执行应用部署",
    //         "ShowManualIgnore":false,
    //         "StageId":"4e771239-e986-47c2-aa11-5c58c2bfcdd0",
    //         "CreateTime":1711369311172,
    //         "StartTime":1711369312323,
    //         "UpdateTime":1711369354123
    //       }
    //     ]
    //   }
    // ]);
    setLoading(false);
  };

  return (
    <Loading
      visible={loading}
      style={{ minHeight: 100 }}
      className="full-width full-height"
    >
      <div className='mt-l'>
        {
          onlyStage ? (
            <div className='npipe-root'>
              <div className='npipe-status'>
                <StatusIndicator type={get(stage, 'icon')} />
                <div 
                  className='title' 
                  style={{ color: get(stage, 'color') }}
                >
                  <span>{get(stage, 'StageName')}</span>
                  <span style={{ fontWeight: 'normal' }}>
                    {intl("saenext.common.parenthesis.left")}
                    {get(stage, 'label')}
                    {intl("saenext.common.parenthesis.right")}
                  </span>
                </div>
              </div>
              <div className='npipe-panel'>
                <AppStagePanel stage={stage} />
              </div>
            </div>
          ) : (
            <Timeline className="npipe-timeline">
              {
                map(stageList, stage => {
                  const { Status } = stage;
                  const { color, label, icon } = StatusMap[Status];
                  return (
                    <Timeline.Item
                      key={stage.StageId}
                      title={
                        <div className='npipe-timeline-item'>
                          <div style={{ color }}>
                            <span>{stage.StageName}</span>
                            <span style={{ fontWeight: 'normal' }}>
                              {intl("saenext.common.parenthesis.left")}
                              {label}
                              {intl("saenext.common.parenthesis.right")}
                            </span>
                          </div>
                          <AppStagePanel stage={stage} />
                        </div>
                      }
                      dot={
                        <StatusIndicator type={icon} />
                      }
                    />
                  )
                })
              }
            </Timeline>
          )
        }

      </div>
    </Loading>
  )
}

export default PipelinePanel;