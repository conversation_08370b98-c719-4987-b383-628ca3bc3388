import { intl } from '@ali/cnd';
import React from 'react';
import { get } from 'lodash';
import RecordStatus from '../revision/RecordStatus';
import RecordTruncate from '../revision/RecordTruncate';

const CoTypeCode = {
  CoBatchStartApplication: intl('saenext.namespace.record.RecordDesc.StartApplicationsInBatches'),
  CoBatchStopApplication: intl('saenext.namespace.record.RecordDesc.StopApplicationsInBatches'),
};

const RecordDesc = (props) => {
  const { changeOrder, changeOrderId } = props;

  return (
    <table
      // @ts-ignore
      border="1"
      width="100%"
      style={{ backgroundColor: '#fff', borderCollapse: 'collapse' }}
    >
      <tbody>
        <tr style={{ border: '1px solid #ddd' }}>
          <td style={{ width: '10%' }} className="pl-l pr-l pt-s pb-s">
            <div className="title-text" style={{ minWidth: 90 }}>
              {intl('saenext.namespace.record.RecordDesc.ChangeProcessId')}
            </div>
          </td>
          <td style={{ width: '24%' }} className="pl-l pr-l pt-s pb-s">
            <div className="content-text">{get(changeOrder, 'ChangeOrderId', changeOrderId)}</div>
          </td>
          <td style={{ width: '10%' }} className="pl-l pr-l pt-s pb-s">
            <div className="title-text" style={{ minWidth: 90 }}>
              {intl('saenext.namespace.record.RecordDesc.NumberOfPublishedBatches')}
            </div>
          </td>
          <td style={{ width: '23%' }} className="pl-l pr-l pt-s pb-s">
            <div className="content-text">{get(changeOrder, 'BatchCount', '--')}</div>
          </td>

          <td style={{ width: '10%' }} className="pl-l pr-l pt-s pb-s">
            <div className="title-text" style={{ minWidth: 90 }}>
              {intl('saenext.namespace.record.RecordDesc.InterbatchProcessing')}
            </div>
          </td>
          <td style={{ width: '23%' }} className="pl-l pr-l pt-s pb-s">
            <div className="content-text">N/A</div>
          </td>
        </tr>
        <tr style={{ border: '1px solid #ddd' }}>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="title-text" style={{ minWidth: 90 }}>
              {intl('saenext.namespace.record.RecordDesc.ExecutionStatus')}
            </div>
          </td>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="content-text">
              {get(changeOrder, 'Status') ? (
                <RecordStatus value={get(changeOrder, 'Status')} />
              ) : (
                '--'
              )}
            </div>
          </td>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="title-text" style={{ minWidth: 90 }}>
              {intl('saenext.namespace.record.RecordDesc.ChangeType')}
            </div>
          </td>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="content-text">
              {get(changeOrder, 'CoTypeCode') ? (
                <span>{get(CoTypeCode, get(changeOrder, 'CoTypeCode'))}</span>
              ) : (
                '--'
              )}
            </div>
          </td>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="title-text" style={{ minWidth: 90 }}>
              {intl('saenext.namespace.record.RecordDesc.ReleaseTime')}
            </div>
          </td>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="content-text">{get(changeOrder, 'CreateTime', '--')}</div>
          </td>
        </tr>
        <tr style={{ border: '1px solid #ddd' }}>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="title-text" style={{ minWidth: 90 }}>
              {intl('saenext.namespace.record.RecordDesc.Description')}
            </div>
          </td>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="content-text">
              {get(changeOrder, 'Description') ? (
                <RecordTruncate value={get(changeOrder, 'Description')} />
              ) : (
                '--'
              )}
            </div>
          </td>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="title-text" style={{ minWidth: 90 }}>
              {intl('saenext.namespace.record.RecordDesc.ChangeObject')}
            </div>
          </td>
          <td className="pl-l pr-l pt-s pb-s">
            <div className="content-text">
              {get(changeOrder, 'CoTargets', []).length ? (
                <RecordTruncate value={get(changeOrder, 'CoTargets').join(',')} />
              ) : (
                '--'
              )}
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  );
};

export default RecordDesc;
