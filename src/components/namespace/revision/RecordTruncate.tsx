import React from 'react';
import { isEmpty } from 'lodash';
import { Balloon } from '@ali/cnd';

type Props = {
  value: string;
  limit?: number;
  link?: string;
};

class RecordTruncate extends React.PureComponent<Props> {
  render() {
    const { value, limit = 25, link = '' } = this.props;
    if (isEmpty(value)) return '--';
    const text = value.length > limit ? `${value.slice(0, limit)}...` : value;
    if (value.length <= limit) {
      if(link){
        return <a href={link} target="_blank">{value}</a>
      }
      return value;
    }
    return (
      <Balloon align="t" trigger={<span>{text}</span>} closable={false}>
        {link ? <a href={link} target="_blank">{value}</a> :value}
      </Balloon>
    );
  }
}

export default RecordTruncate;