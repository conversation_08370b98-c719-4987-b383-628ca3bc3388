import { intl } from '@ali/cnd';
import React from 'react';
import CndTable, { ISearch } from '@ali/cnd-table';
import services from '~/services';
import { get, map } from 'lodash';
import { Copy } from '@ali/cnd';
import { Link } from 'dva/router';
import RecordStatus from './RecordStatus';
import RecordTruncate from './RecordTruncate';

const CoTypeCode = {
  CoBatchStartApplication: intl('saenext.namespace.revision.StartApplicationsInBatches'),
  CoBatchStopApplication: intl('saenext.namespace.revision.StopApplicationsInBatches'),
};

const ChangeRevision = (props) => {
  const {
    regionId,
    namespaceId,
    // @ts-ignore
    location: { search: urlSearch },
  } = props;
  // const [refreshIndex, setRefreshIndex] = useState(0);

  const fetchData = async (params) => {
    const res = await services.getChangeRecord({
      params: {
        ...params,
        BelongRegion: regionId,
        NamespaceId: namespaceId,
        PageSize: params.pageSize,
        CurrentPage: params.current,
      },
      customErrorHandle(err, data, callback) {
        callback && callback();
        return { data: [], total: 0 };
      },
    });
    const { Data = {} } = res || {};
    const { ChangeOrderList = [], TotalSize } = Data;
    return {
      data: map(ChangeOrderList, (item) => ({ ...item, primaryKey: item.ChangeOrderId })),
      total: TotalSize,
    };
  };

  const columns = [
    {
      key: 'ChangeOrderId',
      title: intl('saenext.namespace.revision.ChangeId'),
      dataIndex: 'ChangeOrderId',
      width: 300,
      lock: 'left',
      cell: (value) => (
        <Link
          to={`/${regionId}/namespace/${namespaceId}/record/${value}${urlSearch}`}
        >
          <Copy text={value}>{value}</Copy>
        </Link>
      ),
    },
    {
      key: 'CreateTime',
      title: intl('saenext.namespace.revision.CreationTime'),
      dataIndex: 'CreateTime',
      width: 170,
    },
    {
      key: 'FinishTime',
      title: intl('saenext.namespace.revision.EndTime'),
      dataIndex: 'FinishTime',
      width: 170,
    },
    {
      key: 'CoTypeCode',
      title: intl('saenext.namespace.revision.ChangeType'),
      dataIndex: 'CoTypeCode',
      width: 140,
      cell: (value) => get(CoTypeCode, value),
    },
    {
      key: 'Status',
      title: intl('saenext.namespace.revision.ChangeStatus'),
      dataIndex: 'Status',
      width: 140,
      cell: (value) => <RecordStatus value={value} />,
    },
    {
      key: 'Description',
      title: intl('saenext.namespace.revision.Description'),
      dataIndex: 'Description',
      width: 140,
      cell: (value) => <RecordTruncate value={value} />,
    },
    {
      key: 'Source',
      title: intl('saenext.namespace.revision.Source'),
      dataIndex: 'Source',
      width: 100,
    },
    {
      key: 'CreateUserId',
      title: intl('saenext.namespace.revision.Operator'),
      dataIndex: 'CreateUserId',
      width: 160,
      lock: 'right',
      cell: (val, idx, record) => {
        const { CreateUserId, UserId } = record;
        const isPrimaryCount = CreateUserId === UserId;
        return (
          <span>
            {isPrimaryCount ? intl('saenext.namespace.revision.PrimaryAccount') : CreateUserId}
          </span>
        );
      },
    },
  ];

  const search = {
    defaultDataIndex: 'Key',
    defaultSelectedDataIndex: 'Key',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.namespace.revision.ChangeType'),
        dataIndex: 'CoType',
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.namespace.revision.SelectAChangeType'),
          dataSource: [
            {
              label: intl('saenext.namespace.revision.StartApplicationsInBatches'),
              value: 'CoBatchStartApplication',
            },
            {
              label: intl('saenext.namespace.revision.StopApplicationsInBatches'),
              value: 'CoBatchStopApplication',
            },
          ],
        },
        defaultValue: '',
      },
      {
        label: intl('saenext.namespace.revision.ChangeStatus'),
        dataIndex: 'CoStatus',
        template: 'select',
        templateProps: {
          placeholder: intl('saenext.namespace.revision.SelectChangeStatus'),
          dataSource: [
            { label: intl('saenext.namespace.revision.RecordStatus.NotExecuted'), value: '0' },
            { label: intl('saenext.namespace.revision.Running'), value: '1' },
            { label: intl('saenext.namespace.revision.SuccessfulExecution'), value: '2' },
            { label: intl('saenext.namespace.revision.ExecutionFailed'), value: '3' },
            { label: intl('saenext.namespace.revision.ExecutionTermination'), value: '6' },
          ],
        },
        defaultValue: '',
      },
      {
        label: intl('saenext.namespace.revision.ChangeDescription'),
        dataIndex: 'Key',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.namespace.revision.EnterAChangeDescription'),
        },
      },
    ],
  };

  return (
    <>
      <CndTable
        primaryKey="primaryKey"
        columns={columns as []}
        fetchData={fetchData as any}
        recordCurrent
        showRefreshButton
        // refreshIndex={refreshIndex}
        pagination={{
          hideOnlyOnePage: true,
          pageSizeList: [10, 20, 50, 100],
        }}
        search={search as ISearch}
      />
    </>
  );
};

export default ChangeRevision;
