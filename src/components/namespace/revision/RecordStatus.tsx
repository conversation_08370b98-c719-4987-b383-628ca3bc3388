import { intl } from '@ali/cnd';
import React, { memo } from 'react';
import { StatusIndicator } from '@ali/cnd';

export const StatusMap = {
  0: {
    icon: 'disabled',
    color: '#d1d5d9',
    label: intl('saenext.namespace.revision.RecordStatus.NotExecuted'),
  },
  1: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.namespace.revision.RecordStatus.Running'),
  },
  2: {
    icon: 'success',
    color: '#06B624',
    label: intl('saenext.namespace.revision.RecordStatus.SuccessfulExecution'),
  },
  3: {
    icon: 'error',
    color: '#F54745',
    label: intl('saenext.namespace.revision.RecordStatus.ExecutionFailed'),
  },
  5: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.namespace.revision.RecordStatus.WaitForRetry'),
  },
  6: {
    icon: 'error',
    color: '#F54745',
    label: intl('saenext.namespace.revision.RecordStatus.ExecutionTermination'),
  },
  8: {
    icon: 'warning',
    color: '#FFA003',
    label: intl('saenext.namespace.revision.RecordStatus.WaitForConfirmationManually'),
  },
  9: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.namespace.revision.RecordStatus.AutomaticallyWaitForExecution'),
  },
  10: {
    icon: 'error',
    color: '#F54745',
    label: intl('saenext.namespace.revision.RecordStatus.SystemExceptionCausesExecutionFailure'),
  },
  11: {
    icon: 'loading',
    color: '#333333',
    label: intl('saenext.namespace.revision.RecordStatus.PendingApproval'),
  },
  12: {
    icon: 'success',
    color: '#06B624',
    label: intl('saenext.namespace.revision.RecordStatus.Approved'),
  },
};

const Status = (props) => {
  const { value = 0 } = props;
  return <StatusIndicator type={StatusMap[value].icon}>{StatusMap[value].label}</StatusIndicator>;
};

export default memo(Status);
