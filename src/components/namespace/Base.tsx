import React from 'react';
import { intl } from '@ali/cnd';
import services from "~/services";
import { Icon, Card, Description, Copy, LinkButton, Truncate, Button } from '@ali/cnd';
import BindVpc from './BindVpc';
import CachedData from '../../cache/common';

type Props = {
  regionId: string;
  namespaceId: string;
  EnableWebApplication?: boolean;
};

type State = {
  basicInfo: any;
  resourceInfo: any;
  status: 0 | 1 | 2 | 3 | 4;
  activeTab: 0 | 1;
  vpcDialogVisible: boolean;
  jumpSeverSlidePanelVisible: boolean;
  deleteVisible: boolean;
  jumpServerConfig: any;
};

const CardProps = {
  showTitleBullet: false,
  showHeadDivider: false,
  contentHeight: 'auto',
};

class NamespaceDetail extends React.Component<Props, State> {

  constructor(props) {
    super(props);
    this.state = {
      basicInfo: {},
      resourceInfo: {},
      vpcDialogVisible: false,
      activeTab: 0,
      jumpSeverSlidePanelVisible: false,
      jumpServerConfig: {},
      deleteVisible: false,
      status: 0, //0: 无, 1:创建中, 2: 发布单创建成功, 3:发布单创建失败, 4: 发布单删除成功
    };
  }

  componentDidMount() {
    this.fetchDescribeNamespaceResources();
  }

  fetchDescribeNamespaceResources = async () => {
    const baseInfo = await services.getNamespaceDescribeV2({
      NamespaceId: this.props.namespaceId,
    });
    const { Data: _baseInfo } = baseInfo;

    this.setState({
      basicInfo: _baseInfo,
    });

    const resourceInfo = await services.getNamespaceResourcesV2({
      NamespaceId: this.props.namespaceId,
    });
    const { Data: _resourceInfo } = resourceInfo;

    this.setState({
      resourceInfo: _resourceInfo,
    });
  };

  handleToggleVpcDialog = () => {
    const { vpcDialogVisible } = this.state;

    this.setState(
      {
        vpcDialogVisible: !vpcDialogVisible,
      },
      () => {
        if (vpcDialogVisible) {
          this.fetchDescribeNamespaceResources();
        }
      },
    );
  };

  handleToggleJumpServerSlide = () => {
    const { jumpSeverSlidePanelVisible } = this.state;

    this.setState(
      {
        jumpSeverSlidePanelVisible: !jumpSeverSlidePanelVisible,
      },
      () => {
        if (jumpSeverSlidePanelVisible) {
          this.fetchDescribeNamespaceResources();
        }
      },
    );
  };

  render() {
    const { regionId, namespaceId, EnableWebApplication } = this.props;
    const { vpcDialogVisible, basicInfo, resourceInfo } = this.state;

    return (
      <>
        <Card
          title={intl('saenext.components.nspace-detail.BasicInformation')}
          {...CardProps}
          className="next-extra-card namespace-card"
        >
          <Description
            dataSource={basicInfo}
            items={[
              {
                dataIndex: 'NamespaceName',
                label: intl('saenext.components.nspace-detail.Namespace'),
                render: (value) => (
                  <Copy text={value}>
                    <Truncate threshold="auto" type="width">
                      {value}
                    </Truncate>
                  </Copy>
                ),
              },
              {
                dataIndex: 'VpcName',
                label: intl('saenext.components.namespace.Base.Vpc'),
                render: (value) => (
                  <div>
                    <span>
                      {value ? (
                        <a
                          href={`${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/vpcs/${basicInfo.VpcId}`}
                          target="_blank"
                        >
                          <Copy text={value}>{value}</Copy>
                        </a>
                      ) : (
                        '-'
                      )}
                    </span>
                    <LinkButton onClick={this.handleToggleVpcDialog}>
                      <Icon type={'edit'} size="small" className="ml-l" />
                    </LinkButton>
                  </div>
                ),
              },
            ]}
          />
        </Card>

        <Card
          title={intl('saenext.components.nspace-detail.ResourceInformation')}
          {...CardProps}
          className="next-extra-card namespace-card mt"
        >
          <Description
            dataSource={resourceInfo}
            items={[
              {
                dataIndex: 'WebAppCount',
                label: intl('saenext.components.namespace.Base.WebApplications'),
                visible: EnableWebApplication,
                render: (value) => (
                  <Button
                    text
                    type="primary"
                    onClick={() => {
                      window.open(`/${regionId}/app-list/web?namespaceId=${namespaceId}`);
                    }}
                  >
                    {value}
                  </Button>
                ),
              },
              {
                dataIndex: 'MicroAppCount',
                label: intl('saenext.components.nspace-detail.MicroserviceApplications'),
                render: (value) => (
                  <Button
                    text
                    type="primary"
                    onClick={() => {
                      window.open(`/${regionId}/app-list/micro?namespaceId=${namespaceId}`);
                    }}
                  >
                    {value}
                  </Button>
                ),
              },
            ].filter((item) => item.visible !== false)}
          />
        </Card>

        <BindVpc
          regionId={this.props.regionId}
          vpcId={basicInfo.VpcId}
          namespaceId={this.props.namespaceId}
          namespaceName={basicInfo?.NamespaceName}
          isShowing={vpcDialogVisible}
          isExistApp={resourceInfo.WebAppCount > 0}
          onToggleVpcDialog={this.handleToggleVpcDialog}
        />
      </>
    );
  }
}

export default NamespaceDetail;
