import { Button, CustomEditor, Form, Input, intl, Select, Upload } from '@ali/cnd';
import { debounce, filter, last, map, toUpper } from 'lodash';
import React, { useEffect, useState } from 'react';
import ExternalLink from '~/components/shared/ExternalLink';
import TextRefreshButton from '~/components/shared/TextRefreshButton';
import C from '~/constants/common';
import services from '~/services';
import CachedData from '~/cache/common';

const JumpForm = (props) => {
  const { field, namespaceId, vpcId, vpcName } = props;

  const regionId = namespaceId.split(':')[0];

  const vpcList = [
    {
      label: vpcName,
      value: vpcId,
    },
  ];

  const [vswitchList, setVswitchList] = useState([]);
  const [vswitchLoading, setVswitchLoading] = useState(false);
  const [securityGroupsList, setSecurityGroupsList] = useState([]);
  const [securityGroupsLoading, setSecurityGroupsLoading] = useState(false);

  const { VSwitchId, SecurityGroupId, sshKey } = field.getValues();

  useEffect(() => {
    field.setValue('VpcId', vpcId);
    getVswitches();
    getSecurityGrouponList();
  }, []);

  useEffect(() => {
    if (vswitchList.length && !VSwitchId) {
      field.setValue('VSwitchId', [vswitchList[0].value]);
    }
  }, [vswitchList]);

  useEffect(() => {
    if (securityGroupsList.length && !SecurityGroupId) {
      field.setValue('SecurityGroupId', securityGroupsList[0].value);
    }
  }, [securityGroupsList]);

  const getVswitches = async (vSwitchId?) => {
    setVswitchLoading(true);

    const res = await services.DescribeVSwitches({
      params: {
        RegionId: regionId,
        VSwitchId: vSwitchId || undefined,
        VpcId: vpcId,
        PageNumber: 1,
        PageSize: 50,
      },
      customErrorHandle: (error, _p, cb) => {
        cb();
        return {};
      },
    });

    const { VSwitches: { VSwitch = [] } = {} } = res;
    if (!VSwitch.length) {
      const filterList = vswitchList.filter((item) => {
        if (item.searchKey.includes(vSwitchId)) {
          return item;
        }
      });
      setVswitchList(filterList);
      setVswitchLoading(false);
      return;
    }

    const data = VSwitch.map((item) => {
      return {
        ...item,
        value: item.VSwitchId,
        label: item.VSwitchName,
        searchKey: item.VSwitchName + ' ' + item.VSwitchId,
      };
    });

    setVswitchList(data);
    setVswitchLoading(false);
  };

  const getSecurityGrouponList = async (searchVal?) => {
    setSecurityGroupsLoading(true);
    const {
      SecurityGroups: { SecurityGroup },
    } = await services.DescribeSecurityGroups({
      RegionId: regionId,
      VpcId: vpcId,
      PageSize: 99,
      PageNumber: 1,
      SecurityGroupId: searchVal,
    });

    setSecurityGroupsLoading(false);

    const securityGroupValid = filter(SecurityGroup, (v) => !v.ServiceManaged);

    if (!securityGroupValid.length && searchVal && securityGroupsList) {
      // 搜索场景，且未搜索到
      const filterList = securityGroupsList.filter((item) => {
        if (item.searchKey.includes(searchVal)) {
          return item;
        }
      });
      setSecurityGroupsList(filterList);
      return;
    }

    const data = map(securityGroupValid, (v) => {
      return {
        value: v.SecurityGroupId,
        label: v.SecurityGroupName,
        searchKey: v.SecurityGroupName + ' ' + v.SecurityGroupId,
      };
    });
    setSecurityGroupsList(data);
  };

  const beforeUpload = async (file) => {
    const sshText = await file.text();
    if (!sshText.includes('ssh-rsa')) {
      field.setError(
        'sshKey',
        intl('saenext.namespace.jump-server.JumpForm.PleaseUploadTheSshPublic'),
      );
      return;
    }
    field.setValue('sshKey', sshText);
    field.setError('sshKey', '');
  };

  const vpcRender = (value) => {
    return (
      <>
        <span className="mr-l">{vpcName || vpcId}</span>
        <span className="text-description">{vpcId}</span>
      </>
    );
  };

  const vswitchRender = (item) => {
    const { value, label, ZoneId, AvailableIpAddressCount } = item;
    const zoneName = toUpper(last(ZoneId as string));

    return (
      <div>
        <span className="mr-l">{label}</span>
        <span className="text-description">{value}</span>
        <span className="text-description ml-s">
          {intl('saenext.NamespaceField.micro-app.Namespace.ZoneZonename', {
            zoneName: zoneName,
          })}
        </span>
        <span className="text-description ml-s">
          {intl(
            'saenext.NamespaceField.micro-app.Namespace.AvailableIpAddressesAvailableipaddresscount',
            { AvailableIpAddressCount: AvailableIpAddressCount },
          )}
        </span>
        <ExternalLink
          className="text-description ml"
          url={`${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/switches/${value}`}
        />
      </div>
    );
  };

  const securityGroupRender = (item) => {
    const { value, label } = item;
    return (
      <div>
        <span className="mr-l">{label}</span>
        <span className="text-description">{value}</span>
        <ExternalLink
          className="text-description ml"
          url={`${CachedData.confLink('feature:ecs:url')}/securityGroupDetail/region/${regionId}/groupId/${value}/detail`}
        />
      </div>
    );
  };

  return (
    <Form field={field} labelAlign="left" {...C.FORM_CUSTOM_MIDDLE_LABEL_LEFT}>
      <Form.Item required label={intl('saenext.NamespaceField.micro-app.Namespace.Vpc')}>
        <Select
          name="VpcId"
          disabled
          dataSource={vpcList}
          valueRender={vpcRender}
          style={{ width: '100%' }}
        />
      </Form.Item>
      <Form.Item
        required
        requiredMessage={intl('saenext.NamespaceField.micro-app.Namespace.SelectSwitchVswitch')}
        label={intl('saenext.NamespaceField.micro-app.Namespace.VSwitch')}
      >
        <Select
          mode="multiple"
          dataSource={vswitchList}
          itemRender={vswitchRender}
          valueRender={vswitchRender}
          filterLocal={false}
          showSearch
          onSearch={debounce(getVswitches, 500)}
          onBlur={() => getVswitches()}
          state={vswitchLoading ? 'loading' : null}
          name="VSwitchId"
          style={{ width: '100%' }}
          placeholder={intl('saenext.NamespaceField.micro-app.Namespace.WeRecommendThatYouSelect')}
        />

        <div className="flex">
          <TextRefreshButton onClick={getVswitches} />
          <ExternalLink
            className="ml-l"
            label={intl('saenext.NamespaceField.micro-app.Namespace.CreateASwitch')}
            url={`${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/switches`}
          />
        </div>
      </Form.Item>
      <Form.Item
        required
        requiredMessage={intl('saenext.NamespaceField.micro-app.Namespace.SelectASecurityGroup')}
        label={intl('saenext.NamespaceField.micro-app.Namespace.SecurityGroup')}
      >
        <Select
          dataSource={securityGroupsList}
          itemRender={securityGroupRender}
          valueRender={securityGroupRender}
          showSearch
          filterLocal={false}
          onSearch={debounce(getSecurityGrouponList, 300)}
          onBlur={() => getSecurityGrouponList()}
          state={securityGroupsLoading ? 'loading' : null}
          name="SecurityGroupId"
          style={{ width: '100%' }}
        />

        <div className="flex">
          <TextRefreshButton onClick={getSecurityGrouponList} />
          <ExternalLink
            className="ml-l"
            label={intl('saenext.NamespaceField.micro-app.Namespace.CreateASecurityGroup')}
            url={`${CachedData.confLink('feature:ecs:url')}/#/securityGroup/region/${regionId}/create`}
          />
        </div>
      </Form.Item>
      <Form.Item
        label={intl('saenext.namespace.jump-server.JumpForm.SshCertificate')}
        required
        requiredMessage={intl('saenext.namespace.jump-server.JumpForm.PleaseFillInSshKey')}
      >
        <CustomEditor
          value={sshKey}
          onChange={(value) => {
            field.setValue('sshKey', value);
          }}
          style={{ width: 500 }}
        />

        <Input name="sshKey" style={{ display: 'none' }} />

        <Upload beforeUpload={beforeUpload}>
          <Button type="primary" className="mt">
            {intl('saenext.namespace.jump-server.JumpForm.SelectFile')}
          </Button>
        </Upload>
      </Form.Item>
    </Form>
  );
};

export default JumpForm;
