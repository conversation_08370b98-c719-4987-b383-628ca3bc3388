import React, { useEffect, useRef, useState } from 'react';
import If from '~/components/shared/If';
import JumpServerConfig from './JumpConfig';
import { Copy } from '@ali/cnd';
import services from '~/services';
import { EJumpType } from './constant';

const JumpServer = (props) => {
  const {
    namespaceId,
    vpcId,
    vpcName,
  } = props;

  const [namespaceResources, setNamespaceResources] = useState<any>({});
  const [pollType, setPollType] = useState<EJumpType | ''>('');
  const timerRef = useRef<any>();

  const {
    JumpServerAppId: appId,
    JumpServerIp: ip,
  } = namespaceResources;

  useEffect(() => {
    fetchDescribeNamespaceResources();
    return () => {
      clearInterval(timerRef.current);
    };
  }, []);

  useEffect(() => {
    if (!pollType) return;
    if (pollType === EJumpType.CREATE && appId && ip) {
      stopPolling();
    } else if (pollType === EJumpType.DELETE && !appId && !ip) {
      stopPolling();
    }
  }, [pollType, appId, ip]);

  const startPolling = (type) => {
    setPollType(type);
    timerRef.current = setInterval(fetchDescribeNamespaceResources, 5000);
  }

  const stopPolling = () => {
    setPollType('');
    clearInterval(timerRef.current);
  }

  const fetchDescribeNamespaceResources = async () => {
    const res = await services.getNamespaceResources({
      NamespaceId: namespaceId,
    });
    if (!res?.Success) return;
    const { Data: resourceInfo } = res;
    setNamespaceResources(resourceInfo);
  }

  return (
    <>
      <If condition={ip}>
        <Copy text={ip} style={{ marginRight: 8 }} >
          <span>{ip}</span>
        </Copy>
      </If>
      <JumpServerConfig
        appId={appId}
        startPolling={startPolling}
        namespaceId={namespaceId}
        vpcId={vpcId}
        vpcName={vpcName}
      />
    </>
  )
};

export default JumpServer;