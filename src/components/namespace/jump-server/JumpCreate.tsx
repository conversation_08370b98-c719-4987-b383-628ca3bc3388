import { intl } from '@ali/cnd';
import { Button, Field, Message, Step, Tab } from '@ali/cnd';
import { noop } from 'lodash';
import React, { useState } from 'react';
import SlideWrap from '~/components/shared/SlideWrap';
import JumpForm from './JumpForm';
import If from '~/components/shared/If';
import JumpPreCheck from './JumpPreCheck';
import services from '~/services';
import CachedData from '~/cache/common';

const JumpCreate = (props) => {
  const { namespaceId, vpcId, vpcName, onSuccess } = props;

  const regionId = namespaceId.split(':')[0];

  const [currentStep, setCurrentStep] = useState(0);
  const [preStatus, setPreStatus] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);

  const field = Field.useField();

  const createJumpServer = async (cb) => {
    setCreateLoading(true);
    const values = field.getValues();

    const { VSwitchId, SecurityGroupId, sshKey } = values as any;

    const envs = [
      {
        name: 'SSH_KEY',
        value: sshKey.trim(),
      },
    ];

    const Envs = JSON.stringify(envs);

    const params = {
      AppName: 'sae-jump-server',
      NamespaceId: namespaceId,
      VpcId: vpcId,
      VSwitchId: VSwitchId.join(','),
      SecurityGroupId,
      Replicas: 1,
      Cpu: 500,
      Memory: 1024,
      PackageType: 'Image',
      ImageUrl: `registry.${regionId}.aliyuncs.com/edas-serverless-base/sae-jump-server:1.0`,
      Deploy: true,
      Jdk: 'Open JDK 8',
      ProgrammingLanguage: 'java',
      Envs,
      PurchaseEip: true,
      AssociateEip: true,
    };

    const res = await services.createMicroApplication({
      params,
      content: {
        ...params,
      },
    });

    setCreateLoading(false);

    if (!res?.Success) {
      return;
    }

    const {
      Data: { ChangeOrderId },
    } = res;
    onSuccess(ChangeOrderId);
    cb?.();
  };

  const onClose = (cb?) => {
    setCurrentStep(0);
    setPreStatus(false);
    setCreateLoading(false);
    field.resetToDefault();
    cb?.();
  };

  const onBack = () => {
    setCurrentStep(0);
  };

  const onForward = async () => {
    const { errors } = await field.validatePromise();
    if (errors) {
      return;
    }
    setCurrentStep(1);
  };

  const customFooter = (closeSlide) => {
    return (
      <>
        <If condition={currentStep === 0}>
          <Button onClick={() => onClose(closeSlide)}>
            {intl('saenext.namespace.jump-server.JumpCreate.Cancel')}
          </Button>
          <Button
            // loading={createLoading}
            type="primary"
            onClick={onForward}
          >
            {intl('saenext.namespace.jump-server.JumpCreate.NextStep')}
          </Button>
        </If>
        <If condition={currentStep === 1}>
          <Button onClick={onBack}>
            {intl('saenext.namespace.jump-server.JumpCreate.PreviousStep')}
          </Button>
          <Button
            disabled={!preStatus}
            loading={createLoading}
            type="primary"
            onClick={() => createJumpServer(closeSlide)}
          >
            {intl('saenext.namespace.jump-server.JumpCreate.Ok')}
          </Button>
        </If>
      </>
    );
  };

  return (
    <SlideWrap
      title={intl('saenext.namespace.jump-server.JumpCreate.AddSpringboardMachine')}
      width={1000}
      onMaskClick={onClose}
      content={
        <>
          <p>
            {intl('saenext.namespace.jump-server.JumpCreate.SpringboardMachineJumpServerThe')}
            <a href={CachedData.confLink('help:sae:remote-debugging')} target="_blank">
              {intl('saenext.namespace.jump-server.JumpCreate.HowToAddASpringboard')}
            </a>
          </p>
          <Message type="warning" className="mt mb">
            {intl('saenext.namespace.jump-server.JumpCreate.RemoteDebuggingIsOnlyUsed')}
          </Message>
          <Step current={currentStep} stretch shape="arrow">
            <Step.Item
              title={intl(
                'saenext.namespace.jump-server.JumpCreate.SetSpringboardCreationInformation',
              )}
              onClick={onBack}
            />
            <Step.Item
              title={intl('saenext.namespace.jump-server.JumpCreate.CreateSpringboardResources')}
              onClick={onForward}
            />
          </Step>
          <Tab activeKey={`${currentStep}`} navClassName="none" contentClassName="pd-card">
            <Tab.Item key="0">
              <JumpForm field={field} namespaceId={namespaceId} vpcId={vpcId} vpcName={vpcName} />
            </Tab.Item>
            <Tab.Item key="1">
              <JumpPreCheck regionId={regionId} checkStatus={setPreStatus} />
            </Tab.Item>
          </Tab>
        </>
      }
      customFooter={customFooter}
    >
      <Button text type="primary">
        {intl('saenext.namespace.jump-server.JumpCreate.BindingSpringboardMachine')}
      </Button>
    </SlideWrap>
  );
};

export default JumpCreate;
