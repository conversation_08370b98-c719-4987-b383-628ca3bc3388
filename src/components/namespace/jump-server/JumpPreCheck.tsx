import { intl } from '@ali/cnd';
import { Icon, Table } from '@ali/cnd';
import { find, get, isNaN, isNil } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import CachedData from '~/cache/common';
import RefreshButton from '~/components/shared/RefreshButton';
import services from '~/services';

const STATUS_MAP = {
  loading: {
    text: intl('saenext.namespace.jump-server.JumpPreCheck.Checking'),
    status: 'loading',
    icon: 'loading',
    color: '#999',
  },
  success: {
    text: intl('saenext.namespace.jump-server.JumpPreCheck.Pass'),
    status: 'success',
    icon: 'success-filling',
    color: 'green',
  },
  error: {
    text: intl('saenext.namespace.jump-server.JumpPreCheck.Failed'),
    status: 'error',
    icon: 'delete-filling',
    color: 'red',
  },
};

const JumpPreCheck = (props) => {
  const { regionId, checkStatus } = props;

  const regionName = CachedData.getRegionName(regionId);

  const [eipQuantity, setEipQuantity] = useState({
    InstanceQuantity: null,
    QuotaQuantity: null,
  });

  const eipData = useMemo(() => {
    const { InstanceQuantity, QuotaQuantity } = eipQuantity;

    const status = InstanceQuantity < QuotaQuantity ? true : false;

    checkStatus(status);

    return [
      {
        type: 'EIP',
        project: intl('saenext.namespace.jump-server.JumpPreCheck.EipQuotaCheck'),
        intro: !status
          ? intl('saenext.namespace.jump-server.JumpPreCheck.InsufficientEipQuota')
          : '',
      },
    ];
  }, [eipQuantity]);

  useEffect(() => {
    eipQuotaPreCheck();
  }, []);

  const eipQuotaPreCheck = async () => {
    const data = await services.DescribeNetworkQuotas({
      RegionId: regionId,
      Product: 'vpc',
      ResourceType: 'eip',
    });
    const NetworkQuotaInfo = get(
      data,
      'NetworkQuotas.NetworkQuota[0].NetworkQuotaInfos.NetworkQuotaInfo',
    );

    const eipQuota = find(NetworkQuotaInfo, { QuotaName: 'eip_quota_instances_num' });
    if (!eipQuota) return;
    const { InstanceQuantity, QuotaQuantity } = eipQuota;
    setEipQuantity({
      InstanceQuantity: Number(InstanceQuantity),
      QuotaQuantity: Number(QuotaQuantity),
    });
  };

  const renderPreCheckState = () => {
    const { InstanceQuantity, QuotaQuantity } = eipQuantity;

    if (isNaN(InstanceQuantity) || isNaN(QuotaQuantity)) {
      return intl('saenext.namespace.jump-server.JumpPreCheck.CheckFailed');
    }

    const loading = isNil(InstanceQuantity) || isNil(QuotaQuantity);

    const state = InstanceQuantity < QuotaQuantity ? true : false;

    const status = loading ? 'loading' : state ? 'success' : 'error';

    const { text, icon, color } = STATUS_MAP[status];

    return (
      <span style={{ color }}>
        {InstanceQuantity}/{QuotaQuantity}
        <span className="ml-s">{text}</span>
        <Icon type={icon} size="xs" className="ml" />
      </span>
    );
  };

  const productList = [
    {
      productType: intl('saenext.namespace.jump-server.JumpPreCheck.ServerlessApplicationEngine'),
      config: (
        <>
          <p>{intl('saenext.namespace.jump-server.JumpPreCheck.ApplicationNameSaeJumpServer')}</p>
          <p>{intl('saenext.namespace.jump-server.JumpPreCheck.NumberOfApplicationInstances')}</p>
          <p>{intl('saenext.namespace.jump-server.JumpPreCheck.ApplicationInstanceTypeCoreGib')}</p>
        </>
      ),

      count: 1,
      priceType: intl('saenext.namespace.jump-server.JumpPreCheck.PayAsYouGo'),
      time: intl('saenext.namespace.jump-server.JumpPreCheck.None'),
      priceLink: (
        <a href={CachedData.confLink('help:sae:pay-as-you-go-1')} target="_blank">
          {intl('saenext.namespace.jump-server.JumpPreCheck.ViewPrices')}
        </a>
      ),
    },
    {
      productType: intl('saenext.namespace.jump-server.JumpPreCheck.Eip'),
      config: (
        <>
          <p>
            {intl('saenext.namespace.jump-server.JumpPreCheck.Region')}
            {regionName}{' '}
          </p>
          <p>{intl('saenext.namespace.jump-server.JumpPreCheck.PeakBandwidthMbps')}</p>
          <p>{intl('saenext.namespace.jump-server.JumpPreCheck.TrafficPayByTraffic')}</p>
        </>
      ),

      count: 1,
      priceType: intl('saenext.namespace.jump-server.JumpPreCheck.PayAsYouGo'),
      time: intl('saenext.namespace.jump-server.JumpPreCheck.None'),
      priceLink: (
        <a href={CachedData.confLink('help:eip:pay-as-you-go')} target="_blank">
          {intl('saenext.namespace.jump-server.JumpPreCheck.ViewPrices')}
        </a>
      ),
    },
  ];

  return (
    <>
      <p className="mb">
        {intl('saenext.namespace.jump-server.JumpPreCheck.TheSystemAutomaticallyHelpsUsers')}
      </p>
      <Table dataSource={eipData} hasBorder={false} className="mb-xl">
        <Table.Column
          title={intl('saenext.namespace.jump-server.JumpPreCheck.CheckItems')}
          width={100}
          dataIndex="project"
        />

        <Table.Column
          title={
            <>
              <span className="mr-s">
                {intl('saenext.namespace.jump-server.JumpPreCheck.CheckStatus')}
              </span>
              <RefreshButton handler={eipQuotaPreCheck} />
            </>
          }
          width={100}
          dataIndex="type"
          cell={renderPreCheckState}
        />

        <Table.Column
          title={intl('saenext.namespace.jump-server.JumpPreCheck.Description')}
          width={200}
          dataIndex="intro"
        />
      </Table>
      <Table dataSource={productList} hasBorder={false}>
        <Table.Column
          title={intl('saenext.namespace.jump-server.JumpPreCheck.ProductCategory')}
          dataIndex="productType"
        />

        <Table.Column
          title={intl('saenext.namespace.jump-server.JumpPreCheck.ProductConfiguration')}
          dataIndex="config"
        />

        <Table.Column
          title={intl('saenext.namespace.jump-server.JumpPreCheck.Quantity')}
          dataIndex="count"
        />

        <Table.Column
          title={intl('saenext.namespace.jump-server.JumpPreCheck.PaymentMethod')}
          dataIndex="priceType"
        />

        <Table.Column
          title={intl('saenext.namespace.jump-server.JumpPreCheck.Tariff')}
          dataIndex="priceLink"
        />
      </Table>
    </>
  );
};

export default JumpPreCheck;
