import { <PERSON>oon, Button, DataFields, Dialog, Icon, intl, Loading, Message, ErrorPrompt2 as errorPrompt } from '@ali/cnd';
import React, { useEffect, useRef, useState } from 'react';
import services from '~/services';
import If from '~/components/shared/If';
import { ACTION_TYPE, EJumpType } from './constant';
import JumpCreate from './JumpCreate';
import useRegion from '~/hooks/useRegion';

const JumpConfig = (props) => {
  const { appId, startPolling, namespaceId, vpcId, vpcName } = props;

  const [appConfig, setAppConfig] = useState<any>({});
  const [pollType, setPollType] = useState<EJumpType | ''>('');
  const [changeOrderId, setChangeOrderId] = useState<string>('');
  const [changeStatus, setChangeStatus] = useState<number>(0); // 1: 运行中 2: 成功 3: 失败

  const regionId = useRegion();

  const action = ACTION_TYPE[pollType];

  const timerRef = useRef<any>();

  useEffect(() => {
    return () => {
      clearInterval(timerRef.current);
    };
  }, []);

  useEffect(() => {
    if (!appId) return;
    getAppConfig();
  }, [appId]);

  useEffect(() => {
    if (!changeOrderId) return;
    startPollingChangeOrder();
  }, [changeOrderId]);

  const getAppConfig = async () => {
    const res = await services.describeMicroApplicationConfig({
      AppId: appId,
    });
    if (!res?.Success) return;
    setAppConfig(res.Data);
  };

  const deleteJumpServerDialog = () => {
    Dialog.confirm({
      title: intl('saenext.namespace.jump-server.JumpConfig.DeleteSpringboardMachine'),
      content: intl('saenext.namespace.jump-server.JumpConfig.AfterDeletionTheRemoteSae'),
      okProps: { children: intl('saenext.common.dialog.ok') },
      cancelProps: { children: intl('saenext.common.dialog.cancel') },
      onOk: async () => {
        try {
          const res = await services.deleteMicroApplication({
            params: {
              AppId: appId,
            },
            options: {
              region: regionId,
            }
          });
          if (res?.Success) {
            setPollType(EJumpType.DELETE);
            startPolling(EJumpType.DELETE);
            setChangeOrderId(res?.Data?.ChangeOrderId);
            setChangeStatus(1);
            return true;
          } else {
            Message.error(
              intl('saenext.namespace.jump-server.JumpConfig.FailedToDeleteSpringboardMachine'),
            );
            return false;
          }
        } catch (error) {
          if (error.code === 'FetcherErrorRiskCancelled') {
            // 风控弹窗点取消
            return false;
          } else {
            // 通用控制台弹窗
            errorPrompt(error);
            return false;
          }
        }
      },
    });
  };

  const onCreateSuccess = (ChangeOrderId) => {
    setPollType(EJumpType.CREATE);
    startPolling(EJumpType.CREATE);
    setChangeOrderId(ChangeOrderId);
    setChangeStatus(1);
  };

  const checkChangeOrder = async () => {
    const res = await services.getChangeOrder({
      params: {
        ChangeOrderId: changeOrderId,
      },
    });
    if (!res?.Success) return;
    const {
      Data: { Status },
    } = res;
    if (Status !== 1) {
      clearInterval(timerRef.current);
    }
    setChangeStatus(Status);
    if (Status === 2) {
      setChangeOrderId('');
    }
  };

  const startPollingChangeOrder = () => {
    checkChangeOrder();
    timerRef.current = setInterval(checkChangeOrder, 5000);
  };

  return (
    <>
      {appId ? (
        <>
          <Balloon.Tooltip
            align="t"
            trigger={
              <Button type="primary" text>
                {intl('saenext.namespace.jump-server.JumpConfig.ViewTheNetworkConfigurationOf')}
              </Button>
            }
          >
            <DataFields
              dataSource={appConfig}
              items={[
                {
                  dataIndex: 'VpcId',
                  label: intl('saenext.app-list.micro-app.Vpc'),
                  span: 24,
                },
                {
                  dataIndex: 'VSwitchId',
                  label: intl('saenext.micro-app.basic-info.AppBaseInfo.Switch'),
                  span: 24,
                  render: (value) => value?.split(',')?.map((item) => <div>{item}</div>),
                },
                {
                  dataIndex: 'SecurityGroupId',
                  label: intl('saenext.micro-app.basic-info.AppBaseInfo.SecurityGroup'),
                  span: 24,
                },
              ]}
            />
          </Balloon.Tooltip>
          {!changeOrderId ? (
            <Button type="primary" text className="ml" onClick={deleteJumpServerDialog}>
              {intl('saenext.namespace.jump-server.JumpConfig.DeleteSpringboardMachine')}
            </Button>
          ) : (
            <></>
          )}
        </>
      ) : (
        <JumpCreate
          namespaceId={namespaceId}
          vpcId={vpcId}
          vpcName={vpcName}
          onSuccess={onCreateSuccess}
        />
      )}
      <If condition={!!changeOrderId}>
        <If condition={changeStatus === 1}>
          <Icon type="loading" size="small" className="ml-s mr-s" />
          <span>
            {intl('saenext.namespace.jump-server.JumpConfig.ActionPleaseWait', { action: action })}
          </span>
        </If>
        <If condition={changeStatus === 3}>
          <span className="color-red">
            {intl('saenext.namespace.jump-server.JumpConfig.ActionFailed', { action: action })}
          </span>
        </If>
      </If>
    </>
  );
};

export default JumpConfig;
