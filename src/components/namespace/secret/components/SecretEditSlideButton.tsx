import React, { useState } from 'react';
import SlideButton from '~/components/shared/SlideButton';
import SecretSetting from './SecretSetting';

const SecretEditSlideButton = (props) => {
  const {
    buttonText,
    linkButton = false,
    disabled = false,
    buttonType = 'primary',
    ...rest
  } = props;

  const ref = React.createRef() as any;

  return (
    <SlideButton
      buttonText={buttonText}
      buttonType={buttonType}
      slideTitle={buttonText}
      linkButton={linkButton}
      disabled={disabled}
      slideSize={900}
      slideContent={
        <SecretSetting
          ref={ref}
          {...rest}
        />
      }
      submit={() => {
        return ref.current?.submit();
      }}
    />
  );
};

export default SecretEditSlideButton;
