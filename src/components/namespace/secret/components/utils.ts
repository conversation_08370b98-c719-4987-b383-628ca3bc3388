import { forEach, replace } from "lodash";
import { ESecretType, TSecretType } from "../constants";

const formatSecretData = (data: any, type: TSecretType, encodeOpaque?: boolean) => {
  const {
    opaqueKeyValue = [],
    domain = '',
    username = '',
    password = '',
    cert = '',
    key = '',
  } = data;

  switch (type) {
    case ESecretType.Opaque:
      return arrKVToObj(opaqueKeyValue, encodeOpaque);
    case ESecretType.DockerConfigJson:
      const domainArr = [];
      const registryDomainReg = /^(\S)*registry(-vpc)?\.(\S)+\.aliyuncs.com(\S)*$/; // sae-auto-test-registry-vpc.cn-beijing.cr.aliyuncs.com/ns1/repo1:nginx_latest
      if (registryDomainReg.test(domain)) {
        const registryVpcReg = /registry(-vpc)?.*?(?=(\S)+.aliyuncs.com)/
        const domainFull = replace(domain, registryVpcReg, 'registry-vpc');
        const domainShort = replace(domain, registryVpcReg, 'registry');
        domainArr.push(domainFull, domainShort);
      } else {
        domainArr.push(domain);
      }
      const value = {
        auths: {}
      }
      const domainObj = {
        username,
        password,
        auth: utf8Tob64(`${username}:${password}`)
      }

      forEach(domainArr, (item) => {
        value['auths'][item] = domainObj;
      })

      return {
        '.dockerconfigjson': utf8Tob64(JSON.stringify(value)),
      }
    case ESecretType.TLS:
      return {
        'tls.crt': cert,
        'tls.key': key,
      }

    default:
      break;
  }
}

const parseSecretData = (data, type) => {
  switch (type) {
    case ESecretType.Opaque:
      return {
        opaqueKeyValue: objToArrKV(data),
      }
    case ESecretType.DockerConfigJson:
      // 数据示例:
      // {
      //   ".dockerconfigjson": { //bsae64
      //     "auths": {
      //       "registry-vpc.cn-beijing.aliyuncs.com/sae-dev-test/harbor-db": {
      //         "username": "zhangsan",
      //         "password": "qaz123",
      //         "auth": "emhhbmdzYW46cWF6MTIz" // base64 username:password
      //       },
      //       "registry.cn-beijing.aliyuncs.com/sae-dev-test/harbor-db": {
      //         "username": "zhangsan",
      //         "password": "qaz123",
      //         "auth": "emhhbmdzYW46cWF6MTIz" // base64 username:password
      //       }
      //     }
      //   }
      // }
      const dockerConfig = data['.dockerconfigjson'];
      const dockerObjStr = b64Toutf8(dockerConfig);
      const dockerObj = JSON.parse(dockerObjStr)
      const { auths } = dockerObj;
      const [domain] = Object.keys(auths);
      const userPass = b64Toutf8(auths[domain]['auth']);
      const [username, password] = userPass.split(':');
      return {
        domain,
        username,
        password,
      }
    case ESecretType.TLS:
      const cert = data['tls.crt'];
      const key = data['tls.key'];
      return {
        cert: cert,
        key: key,
      }
    default:
      break;
  }
}

const arrKVToObj = (arr, encode) => {
  const obj = {};
  forEach(arr, (item) => {
    const { key = '', value = '' } = item;
    if (key && value) {
      obj[key.trim()] = encode ? utf8Tob64(value.trim()) : value.trim();
    }
  })
  return obj;
};

const objToArrKV = (obj) => {
  const arr = [];
  for (const key in obj) {
    const value = obj[key];
    arr.push({
      key,
      value: b64Toutf8(value),
    })
  }
  return arr;
}

const utf8Tob64 = (str) => {
  return window.btoa(unescape(encodeURIComponent(str)));
}

const b64Toutf8 = (str) => {
  try {
    const res = decodeURIComponent(escape(window.atob(str)));
    return res;
  } catch (e) {
    return str;
  }
}

export {
  formatSecretData,
  parseSecretData,
}

