import { intl } from '@ali/cnd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Checkbox, Field, Form, Input, KeyValue, Loading, Message, Radio } from '@ali/cnd';
import * as services from '~/services';
import { forEach, isEmpty, replace } from 'lodash';
import { formatSecretData, parseSecretData } from './utils';
import { ActionType, ESecretType, SECRET_TYPE_DATA } from '../constants';

const SecretSetting = (props, ref) => {
  const { type, initData = {}, namespaceId, onSuccess } = props;
  const isEdit = type === 'edit';

  const [dataLoading, setDataLoading] = useState(false);
  const [encodeChecked, setEncodeChecked] = useState(true);

  const field = Field.useField();
  const { init, getValue, setValues, validate } = field;

  const secretType = getValue('secretType') || ESecretType.Opaque;

  useEffect(() => {
    getInitData();
  }, [initData]);

  useImperativeHandle(
    ref,
    () => ({
      submit,
    }),
    [encodeChecked],
  );

  const getInitData = async () => {
    if (!initData.SecretId) return;
    setDataLoading(true);
    const { Data = {} } = await services.DescribeSecret({
      NamespaceId: namespaceId,
      SecretId: initData.SecretId,
    });

    const { SecretName = '', SecretType = ESecretType.Opaque, SecretData = {} } = Data;

    if (isEmpty(SecretData)) {
      setDataLoading(false);
      return;
    }

    const formatValues = parseSecretData(SecretData, SecretType);

    setValues(formatValues);

    setDataLoading(false);
  };

  const handleEncodeOpaque = (checked) => {
    setEncodeChecked(checked);
  };

  const submit = async () => {
    const promise = new Promise<void>(async (resolve, reject) => {
      validate((errors, values) => {
        if (errors) {
          reject(errors);
          return;
        }

        const params = formatParams(values, reject);
        doSubmit(params, resolve, reject);
      });
    });

    return promise;
  };

  const formatParams = (values, reject) => {
    const { secretName = '', secretType = '' } = values as any;

    const SecretData = formatSecretData(values, secretType, encodeChecked);

    const SecretDataStr = JSON.stringify(SecretData);
    if (SecretDataStr.length > 51200) {
      Message.error(intl('saenext.secret.components.SecretSetting.TheDataIsTooLong'));
      reject(false);
      return;
    }

    const params = {
      NamespaceId: namespaceId,
      SecretData: SecretDataStr,
    } as any;

    switch (type) {
      case 'create':
      case 'copy':
        params.SecretName = secretName;
        params.SecretType = secretType;
        break;
      case 'edit':
        params.SecretId = initData.SecretId;
        break;
      default:
        break;
    }

    return params;
  };

  const doSubmit = async (params, resolve, reject) => {
    let service;

    switch (type) {
      case 'create':
      case 'copy':
        service = services.CreateSecret;
        break;
      case 'edit':
        service = services.UpdateSecret;
        break;
    }

    try {
      const result = await service({
        params,
      });

      if (result) {
        const actionType = ActionType[type];
        Message.success(
          intl('saenext.secret.components.SecretSetting.ActiontypeDictionarySucceeded', {
            actionType: actionType,
          }),
        );
        onSuccess();
        resolve(result);
      } else {
        reject();
      }
    } catch (err) {
      reject(err);
    }
  };

  const validateOpaque = (rule, value, callback) => {
    if (isEmpty(value)) {
      callback(intl('saenext.secret.components.SecretSetting.EnterCompleteData'));
      return;
    }
    let valid = true;
    forEach(value, (item) => {
      const { key = '', value = '' } = item;
      if (!key.trim() || !value.trim()) {
        valid = false;
      }
    });
    if (!valid) {
      callback(intl('saenext.secret.components.SecretSetting.EnterCompleteData'));
      return;
    }
    callback();
  };

  const formItemLayout = {
    labelCol: {
      fixedSpan: 6,
    },
    wrapperCol: {
      span: 18,
    },
  };

  return (
    <>
      <Loading style={{ width: '100%' }} visible={dataLoading}>
        {secretType === ESecretType.TLS && (
          <Message type="warning" className="mb-l">
            {intl('saenext.secret.components.SecretSetting.ThePublicKeyCertAnd')}
          </Message>
        )}
        <Form field={field} {...formItemLayout}>
          <Form.Item label={intl('saenext.secret.components.SecretSetting.Name')} required>
            <Input
              trim
              {...init('secretName', {
                initValue: (initData.SecretName || '') + (type === 'copy' ? '-copy' : ''),
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.secret.components.SecretSetting.EnterASecretDictionaryName',
                    ),
                  },
                ],
              })}
              maxLength={64}
              placeholder={intl(
                'saenext.secret.components.SecretSetting.EnterASecretDictionaryName',
              )}
              style={{ width: '80%', marginRight: 5 }}
              disabled={isEdit}
            />
          </Form.Item>
          <Form.Item label={intl('saenext.secret.components.SecretSetting.Type')} required>
            <Radio.Group
              {...init('secretType', { initValue: initData.SecretType || ESecretType.Opaque })}
              dataSource={SECRET_TYPE_DATA}
              disabled={isEdit}
            />
          </Form.Item>

          {secretType === ESecretType.Opaque && (
            <Form.Item
              label={intl('saenext.secret.components.SecretSetting.KeyValuePair')}
              required
              key={`${dataLoading} + ${encodeChecked}`}
            >
              <Checkbox checked={encodeChecked} onChange={handleEncodeOpaque}>
                {intl('saenext.secret.components.SecretSetting.BaseEncodingDataValues')}
              </Checkbox>
              <KeyValue
                {...init('opaqueKeyValue', {
                  // initValue: [{key: '', value: ''}],
                  rules: [
                    {
                      validator: validateOpaque,
                    },
                  ],
                })}
                prefixKeyText={intl('saenext.secret.components.SecretSetting.Name.1')}
                prefixValueText={intl('saenext.secret.components.SecretSetting.Value')}
              />
            </Form.Item>
          )}

          {secretType === ESecretType.DockerConfigJson && (
            <>
              <Form.Item
                label={intl('saenext.secret.components.SecretSetting.ImageRepositoryAddress')}
                required
              >
                <Input
                  trim
                  {...init('domain', {
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.secret.components.SecretSetting.EnterTheAddressOfThe',
                        ),
                      },
                    ],
                  })}
                  placeholder={intl('saenext.secret.components.SecretSetting.EnterTheDomainNameOf')}
                  style={{ width: '80%', marginRight: 5 }}
                />
              </Form.Item>
              <Form.Item label={intl('saenext.secret.components.SecretSetting.Username')} required>
                <Input
                  trim
                  {...init('username', {
                    rules: [
                      {
                        required: true,
                        message: intl('saenext.secret.components.SecretSetting.EnterAUsername'),
                      },
                    ],
                  })}
                  placeholder={intl('saenext.secret.components.SecretSetting.EnterAUsername')}
                  style={{ width: '80%', marginRight: 5 }}
                />
              </Form.Item>
              <Form.Item label={intl('saenext.secret.components.SecretSetting.Password')} required>
                <Input
                  trim
                  htmlType="password"
                  {...init('password', {
                    rules: [
                      {
                        required: true,
                        message: intl('saenext.secret.components.SecretSetting.EnterAPassword'),
                      },
                    ],
                  })}
                  placeholder={intl('saenext.secret.components.SecretSetting.EnterAPassword')}
                  style={{ width: '80%', marginRight: 5 }}
                />
              </Form.Item>
            </>
          )}

          {secretType === ESecretType.TLS && (
            <>
              <Form.Item label="Cert:" required>
                <Input.TextArea
                  {...init('cert', {
                    rules: [
                      {
                        required: true,
                        message: intl('saenext.secret.components.SecretSetting.EnterCert'),
                      },
                    ],
                  })}
                  placeholder={intl('saenext.secret.components.SecretSetting.EnterCert')}
                  style={{ width: '80%', marginRight: 5 }}
                />
              </Form.Item>
              <Form.Item label="Key:" required>
                <Input.TextArea
                  {...init('key', {
                    rules: [
                      {
                        required: true,
                        message: intl('saenext.secret.components.SecretSetting.EnterAKey'),
                      },
                    ],
                  })}
                  placeholder={intl('saenext.secret.components.SecretSetting.EnterAKey')}
                  style={{ width: '80%', marginRight: 5 }}
                />
              </Form.Item>
            </>
          )}
        </Form>
      </Loading>
    </>
  );
};

export default forwardRef(SecretSetting);
