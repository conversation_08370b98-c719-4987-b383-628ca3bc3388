import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { <PERSON>s, Button, Copy, Dialog, Link, LinkButton, Message, Truncate } from '@ali/cnd';
import CndTable from '@ali/cnd-table';
import * as services from '~/services';
import { get, isEmpty, map } from 'lodash';
import { ActionType, SECRET_TYPE_MAP } from './constants';
import SecretEditSlideButton from './components/SecretEditSlideButton';
import CachedData from '~/cache/common';

const SecretList = (props) => {
  const { regionId, namespaceId } = props;

  const [secretCount, setSecretCount] = useState(0);
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    setRefreshIndex(refreshIndex + 1);
  }, [regionId, namespaceId]);

  const onRefresh = () => {
    setRefreshIndex(refreshIndex + 1);
  };

  const handleDelete = (record) => {
    if (get(record, 'RelateApps.length') === 0 || !record.RelateApps) {
      const onOk = async () => {
        const { Data } = await services.DeleteSecret({
          params: {
            NamespaceId: namespaceId,
            SecretId: record.SecretId,
          },
        });
        Message.success(intl('saenext.namespace.secret.TheSecretDictionaryHasBeen'));
        onRefresh();
        return Data;
      };
      Dialog.alert({
        title: intl('saenext.namespace.secret.DeleteAConfidentialDictionary'),
        content: (
          <>
            {intl('saenext.namespace.secret.ConfirmToDeleteTheSecret')}
            <span className="color-orange">{record.SecretName}</span>?
          </>
        ),

        onOk,
      });
    } else {
      Dialog.alert({
        title: intl('saenext.namespace.secret.DeleteAConfidentialDictionary'),
        content: intl('saenext.namespace.secret.ExistingApplicationsAreUsingThe'),

        footer: false,
      });
    }
  };

  const fetchData = async () => {
    const { Data } =
      (await services.ListSecrets({
        NamespaceId: namespaceId,
      })) || {};
    const { Secrets = [] } = Data;
    setSecretCount(Secrets.length);
    return {
      data: Secrets,
    };
  };

  const columns = [
    {
      key: 'SecretName',
      title: intl('saenext.namespace.secret.SecretDictionaryNameId'),
      dataIndex: 'SecretName',
      width: 200,
      cell: (value, index, record) => (
        <>
          <Copy text={value}>{value}</Copy>
          <br />
          <Copy text={String(record.SecretId)}> {record.SecretId} </Copy>
        </>
      ),
    },
    {
      key: 'SecretType',
      title: intl('saenext.namespace.secret.Type'),
      dataIndex: 'SecretType',
      width: 160,
      cell: (value, index, record) => SECRET_TYPE_MAP[value],
    },
    {
      key: 'RelateApps',
      title: intl('saenext.namespace.secret.AssociatedApplications'),
      dataIndex: 'RelateApps',
      width: 240,
      cell: (value, index, record) => {
        const appNameList = map(value, 'AppName');
        const appNameStr = appNameList.length > 0 ? appNameList.join(', ') : '--';
        return appNameStr;
      },
    },
    {
      key: 'CreateTime',
      title: intl('saenext.namespace.secret.CreationTime'),
      dataIndex: 'CreateTime',
      width: 200,
      cell: (value, index, record) => {
        return intl.date(value);
      },
    },
    {
      key: 'UpdateTime',
      title: intl('saenext.namespace.common.LatestUpdateTime'),
      dataIndex: 'UpdateTime',
      width: 200,
      cell: (value, index, record) => {
        return intl.date(value);
      },
    },
    {
      key: 'SecretId',
      title: intl('saenext.namespace.secret.Operation'),
      dataIndex: 'SecretId',
      width: 200,
      cell: (value, index, record) => {
        return (
          <Actions>
            <SecretEditSlideButton
              buttonText={ActionType['edit']}
              linkButton
              type="edit"
              initData={record}
              onSuccess={onRefresh}
              namespaceId={namespaceId}
            />

            <SecretEditSlideButton
              buttonText={ActionType['copy']}
              linkButton
              disabled={secretCount >= 20}
              type="copy"
              initData={record}
              onSuccess={onRefresh}
              namespaceId={namespaceId}
            />

            <LinkButton onClick={() => handleDelete(record)}>
              {intl('saenext.namespace.secret.Delete')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];

  return (
    <>
      <Message type="notice" className="mb-s">
        <div className="text-line">
          <span>{intl('saenext.namespace.secret.TheKSSecretSolves')}</span>
          <a href={CachedData.confLink('help:sae:manage-a-kubernetes-secret')} target="_blank">
            {intl('saenext.namespace.secret.HowToSetUpAnd')}
          </a>
        </div>
        <div className="text-line color-orange">
          {intl('saenext.namespace.secret.ForTheTimeBeingThe')}
        </div>
      </Message>
      <CndTable
        fetchData={fetchData}
        columns={columns}
        refreshIndex={refreshIndex}
        showRefreshButton
        pagination={false}
        operation={
          <SecretEditSlideButton
            buttonText={ActionType['create']}
            disabled={secretCount >= 20}
            type="create"
            onSuccess={onRefresh}
            namespaceId={namespaceId}
          />
        }
      />
    </>
  );
};

export default SecretList;
