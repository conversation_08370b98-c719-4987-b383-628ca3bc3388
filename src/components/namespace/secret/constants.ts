import { intl } from '@ali/cnd';
import { objToDataSource } from '~/utils/transfer-data';

export enum ESecretType {
  Opaque = 'Opaque',
  DockerConfigJson = 'kubernetes.io/dockerconfigjson',
  TLS = 'kubernetes.io/tls',
}

export type TSecretType = `${ESecretType}`;

export const SECRET_TYPE_MAP = {
  [ESecretType.Opaque]: 'Opaque',
  [ESecretType.DockerConfigJson]: intl(
    'saenext.namespace.secret.constants.PrivateImageRepositoryLogonKey',
  ),
  [ESecretType.TLS]: intl('saenext.namespace.secret.constants.TlsCertificate'),
};

export const SECRET_TYPE_DATA = objToDataSource(SECRET_TYPE_MAP);

export const ActionType = {
  create: intl('saenext.namespace.secret.constants.Create'),
  copy: intl('saenext.namespace.secret.constants.Copy'),
  edit: intl('saenext.namespace.secret.constants.Edit'),
};
