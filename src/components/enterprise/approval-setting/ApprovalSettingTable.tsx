import { intl, ToolTipCondition } from '@ali/cnd';
import React, { useState } from 'react';
import CndTable from '@ali/cnd-table';
import { Button } from '@ali/cnd';
import { fetchData, columns } from './ApprovalSettingTableProps';
import CachedData from '~/cache/common';

export default ({ handleEdit, handleDelete, handleCreate, refreshIndex }) => {
  const [notAuth, setNotAuth] = useState(false);

  return (
    <CndTable
      fetchData={
        (async (value) => {
          const res = await fetchData({ ...value });
          if (res.error === 'AuthenticationFailed') {
            setNotAuth(true);
            return { data: [], total: 0 };
          } else {
            setNotAuth(false);
          }
          return res;
        }) as any
      }
      primaryKey="primaryKey"
      columns={columns({ handleEdit, handleDelete })}
      refreshIndex={refreshIndex}
      showRefreshButton
      operation={
        <ToolTipCondition
          popupStyle={{ maxWidth: 500 }}
          show={notAuth}
          tip={intl.html(
            'saenext.enterprise.approval-setting.ApprovalSettingTable.NoApprovalPermission.new',{
              href:CachedData.confLink('help:sae:approval-configuration'),
            }
          )}
        >
          <Button type="primary" disabled={notAuth} onClick={handleCreate}>
            {intl(
              'saenext.enterprise.approval-setting.ApprovalSettingTable.CreateApprovalSettings',
            )}
          </Button>
        </ToolTipCondition>
      }
      pagination={{
        pageSizeList: [10, 20, 50, 100],
        hideOnlyOnePage: true,
      }}
    />
  );
};
