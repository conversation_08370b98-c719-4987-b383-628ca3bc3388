import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import CachedData from '~/cache/common';
import { Dialog, Message } from '@ali/cnd';
import services from '~/services';
import If from '~/components/shared/If';
import ApprovalSettingTable from './ApprovalSettingTable';
import SettingSlidePanel from './setting-slide-panel';

const ApprovalSetting = props => {
  const isMainAccount = CachedData.isMainAccount();
  const [slideActive, setSlideActive] = useState(false);
  const [curData, setCurData] = useState('');
  const [operationTypes, setOperationTypes] = useState('');
  const [refreshIndex, setRefreshIndex] = useState(0);

  const getListApprovedOperationTypes = async () => {
    const { Data } = await services.ListApprovedOperationTypes() || {};

    setOperationTypes(Data);
  };

  useEffect(() => {
    getListApprovedOperationTypes();
  }, []);

  const handleCreate = () => {
    setCurData('');
    setSlideActive(true);
  };
  const handleEdit = record => {
    setCurData(record);
    setSlideActive(true);
  };
  const handleDelete = record => {
    const { RuleId } = record;

    Dialog.confirm({
      title: intl('saenext.enterprise.approval-setting.Prompt'),
      content: <p>{intl('saenext.enterprise.approval-setting.AreYouSureYouWant')}</p>,
      onOk: () => {
        return new Promise<void>(async (resolve, reject) => {
          await services.DeleteOperationApprovalRule({
            OperationApprovalRuleId: RuleId,
            customErrorHandle(err, serviceConfig, callback) {
              if (err) {
                reject();
              }
              callback();
            },
          });
          resolve();
          Message.success(intl('saenext.enterprise.approval-setting.TheApprovalItemHasBeen'));
          setRefreshIndex(+new Date());
        });
      },
    });
  };

  return (
    <>
      <Message type="notice" className='mb-s'>
        {intl('saenext.enterprise.approval-setting.ApplicationScenariosForApprovalSettings')}
      </Message>
      <ApprovalSettingTable
        {...{
          handleCreate,
          handleEdit,
          handleDelete,
          refreshIndex,
        }}
      />

      <SettingSlidePanel
        active={slideActive}
        setActive={setSlideActive}
        curData={curData}
        operationTypes={operationTypes}
        setRefreshIndex={setRefreshIndex}
      />
    </>
  );
};
export default ApprovalSetting;
