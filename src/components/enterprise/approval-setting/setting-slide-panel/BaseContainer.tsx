import React, { forwardRef } from 'react';

type options = {
  title: any;
  style?: object;
};

const BaseContainer = (WrapComponent, options: options) => {
  const { title } = options;
  return forwardRef((props: any, ref) => {
    const { style: styles = {} } = props;
    return (
      <div style={styles}>
        <p style={{ paddingBottom: 16 }}>{title}</p>
        <WrapComponent {...props} ref={ref} />
      </div>
    );
  });
};

export default BaseContainer;
