import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Select, Form, Field } from '@ali/cnd';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import services from '~/services';
import { filter, map } from 'lodash';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    fixedSpan: 8,
  },
  wrapperCol: {
    span: 16,
  },
};

const WhiteList = ({ field }) => {
  const [ramUsers, setRamUsers] = useState([]);
  const [selectRamUsers, setSelectRamUsers] = useState([]);
  const [notAuth, setNotAuth] = useState(false);
  
  const getListRamUsers = async () => {
    const User = await getRamUserRequest();
    let list = map(User, v => {
      const { UserName, UserId } = v;
      return {
        value: `${UserName}/${UserId}`,
        label: `${UserName}/${UserId}`,
      };
    });
    setRamUsers(list);
    setSelectRamUsers(list);
  };

  const getRamUserRequest = async (params = {}) => {
    const res = await services.ListRamUsers({
      params: {
        MaxItems: 100,
        ...params,
      },
      customErrorHandle: (err, serviceConfig, callback) => {
        if (err.code === 'NoPermission') {
          return {
            error: 'NoPermission',
          };
        } else {
          callback();
        }
      },
    });
    if (res.error === 'NoPermission') {
      setNotAuth(true);
      return [];
    } else {
      setNotAuth(false);
    }
    const { IsTruncated, Marker, Users = {} } = res || {};
    const { User = [] } = Users || {};
    if (IsTruncated && Marker) {
      const moreUser = await getRamUserRequest({ Marker });
      return [...User, ...moreUser];
    } else {
      return User;
    }
  };

  const { init } = field;

  useEffect(() => {
    getListRamUsers();
  }, []);

  const handleRamUserSearch = value => {
    setSelectRamUsers(filter(ramUsers, item => item.value.indexOf(value) > -1));
  };

  return (
    <Form field={field} {...formItemLayout}>
      <FormItem
        label={
          <TextWithBalloon
            text={intl(
              'saenext.approval-setting.setting-slide-panel.WhiteList.ApprovalFreeSubAccountWhitelist',
            )}
            tips={intl(
              'saenext.approval-setting.setting-slide-panel.WhiteList.SubAccountsInTheWhitelist',
            )}
          />
        }
      >
        <Select
          {...init('excludeAccounts')}
          mode="multiple"
          showSearch
          placeholder=""
          filterLocal={false}
          dataSource={selectRamUsers}
          onSearch={handleRamUserSearch}
          style={{ width: 400 }}
        />
        <UnAuthedLabel
          text=''
          authKey="AliyunRAMReadOnlyAccess"
          authed={!notAuth}
        />
      </FormItem>
    </Form>
  );
};
export default WhiteList;
