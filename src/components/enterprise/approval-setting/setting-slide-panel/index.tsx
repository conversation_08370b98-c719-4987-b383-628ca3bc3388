import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { Field, SlidePanel, Message } from '@ali/cnd';
import services from '~/services';
import { jsonStringify } from '~/utils/transfer-data';
import ApprovalScopeForm from './ApprovalScope';
import WhiteListForm from './WhiteList';
import ApprovingOfficersForm from './ApprovingOfficers';
import BaseContainer from './BaseContainer';
import { filter, find, get, includes, isEmpty, lowerCase, map, split } from 'lodash';

const ApprovalScope = BaseContainer(ApprovalScopeForm, {
  title: intl('saenext.approval-setting.setting-slide-panel.ApprovalScopeSettings'),
});

const WhiteList = BaseContainer(WhiteListForm, {
  title: intl('saenext.approval-setting.setting-slide-panel.WhitelistConfiguration'),
});

const ApprovingOfficers = BaseContainer(ApprovingOfficersForm, {
  title: intl('saenext.approval-setting.setting-slide-panel.ApproverConfiguration'),
});

const SettingSlidePanel = props => {
  const { active, setActive, operationTypes, setRefreshIndex, curData } = props;
  const [isProcessing, setIsProcessing] = useState(false);
  const approvalScopeRef = useRef(null);
  const field = Field.useField();
  const { setValue, getValue } = field;

  const initialExcludeAccount = () => {
    let excludeDefault = [''];
    excludeDefault = map(curData.ExcludeAccounts, v => {
      const { uid, name } = v;
      return `${name}/${uid}`;
    });
    setValue('excludeAccounts', excludeDefault);
  };

  useEffect(() => {
    initialExcludeAccount();
  }, [active]);

  const handleCreateApproval = async (params, reject) => {
    const res = await services.CreateOperationApprovalRule({
      OperationApprovalRule: jsonStringify(params),
      customErrorHandle(err, serviceConfig, callback) {
        if (err) {
          setIsProcessing(false);
          reject();
        }
        callback();
      },
    });
    return res;
  };

  const handleUpdateApproval = async (params, reject) => {
    const res = await services.UpdateOperationApprovalRule({
      OperationApprovalRule: jsonStringify(params),
      customErrorHandle(err, serviceConfig, callback) {
        if (err) {
          setIsProcessing(false);
          reject();
        }
        callback();
      },
    });
    return res;
  };

  const handelOnOk = async () => {
    const { errors } = await approvalScopeRef.current.validatePromise();
    if (errors) {
      return;
    }
    const { region, namespaceId, appjob } = getValue('approvalScopeInfo') as any;
    const approvingOfficers = getValue('approvingOfficers') as any;
    const excludeAccounts = getValue('excludeAccounts') as any;
    const isAll = !isEmpty(find(appjob, i => i.value === 'all'));
    const isAllApps = !isEmpty(find(appjob, i => i.value === 'allApps'));

    const resourceData =
      isAll || isAllApps
        ? {
            isAllApps: true,
            resources: [],
          }
        : {
            isAllApps: false,
            resources: map(
              filter(appjob, i => !includes(['allApps', 'allJobs', 'all'], i.value)),
              v => {
                return {
                  type: lowerCase(v.type),
                  resourceId: v.value,
                  resourceName: v.label,
                };
              },
            ),
          };
    const params = {
      ruleId: get(curData, 'RuleId', ''),
      operations: [
        {
          desc: get(operationTypes, '[0].Desc'),
          type: get(operationTypes, '[0].Type'),
        },
      ],

      region,
      namespaceId: namespaceId,
      resourceDesc: resourceData,
      // approvingOfficers: [{ name: 'edas_test1', uid: '75' }],
      approvingOfficers: map(approvingOfficers, v => {
        const [name, uid] = split(v, '/');
        return {
          name,
          uid,
        };
      }),
      excludeAccounts:
        excludeAccounts[0] !== ''
          ? map(excludeAccounts, v => {
              const [name, uid] = split(v, '/');
              return {
                name,
                uid,
              };
            })
          : [],
    };

    setIsProcessing(true);

    const tip = !curData
      ? intl('saenext.approval-setting.setting-slide-panel.Create')
      : intl('saenext.approval-setting.setting-slide-panel.Edit');

    return new Promise<void>(async (resolve, reject) => {
      let res;
      if (curData) {
        res = await handleUpdateApproval(params, reject);
      } else {
        res = await handleCreateApproval(params, reject);
      }
      if (res?.Code !== 200) {
        setIsProcessing(false);
        return;
      }
      Message.success(
        intl('saenext.approval-setting.setting-slide-panel.TipApprovalSettingsSuccessful', {
          tip: tip,
        }),
      );
      setIsProcessing(false);
      setActive(false);
      setRefreshIndex(new Date());

      resolve();
    });
  };

  return (
    <SlidePanel
      title={
        curData
          ? intl('saenext.approval-setting.setting-slide-panel.ChangeApprovalSettings')
          : intl('saenext.approval-setting.setting-slide-panel.CreateApprovalSettings')
      }
      isShowing={active}
      width="large"
      onMaskClick={() => {
        setActive(false);
        setValue('excludeAccounts', []);
      }}
      onClose={() => {
        setActive(false);
        setValue('excludeAccounts', []);
      }}
      onCancel={() => {
        setActive(false);
        setValue('excludeAccounts', []);
      }}
      onOk={handelOnOk}
      isProcessing={isProcessing}
    >
      {/* 审批范围设置 */}
      <ApprovalScope field={field} curData={curData} ref={approvalScopeRef} />
      {/* 白名单配置 */}
      <WhiteList
        field={field}
        style={{
          borderTop: '1px dashed #ccc',
          paddingTop: 16,
          height: 200,
        }}
      />

      {/* 审批人设置 */}
      <ApprovingOfficers
        field={field}
        curData={curData}
        style={{
          borderTop: '1px dashed #ccc',
          paddingTop: 16,
        }}
      />
    </SlidePanel>
  );
};

export default SettingSlidePanel;
