import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Select, Form, Tag, Icon } from '@ali/cnd';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import services from '~/services';
import { filter, map, find, get } from 'lodash';
import ExternalLink from '~/components/shared/ExternalLink';
import RefreshButton from '~/components/shared/RefreshButton';
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    fixedSpan: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const ApprovingOfficers = ({ field, curData }) => {
  const { init, setValue } = field;
  const [contactList, setContactList] = useState([]);
  const [contactLoading, setContactLoading] = useState(false);
  const [selectContactList, setSelectContactList] = useState([]);

  const approvingValueRender = item => {
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          width: 'max-content',
          height: '100%',
          marginRight: 12,
        }}
      >
        <span> {item.label}</span>
        {item.title ? (
          <Tag color="yellow" style={{ marginLeft: 10 }}>
            {intl('saenext.approval-setting.setting-slide-panel.ApprovingOfficers.PrimaryAccount')}
          </Tag>
        ) : (
          ''
        )}
      </div>
    );
  };

  const getListContacts = async () => {
    setContactLoading(true);
    const {
      Data: { ContactsInfo = [] },
    } = await services.ListContactsWithoutDetailInfo();
    const list = map(ContactsInfo, v => {
      const { Uid, Name, IsMainAccount } = v;
      return {
        value: `${Name}/${Uid}`,
        label: Name,
        title: IsMainAccount
          ? intl('saenext.approval-setting.setting-slide-panel.ApprovingOfficers.PrimaryAccount')
          : '',
      };
    });
    setContactLoading(false);
    setContactList(list);
    setSelectContactList(list);
  };

  useEffect(() => {
    getListContacts();
  }, []);

  const handleApprovingOfficersSearch = value => {
    setSelectContactList(filter(contactList, item => item.label.indexOf(value) > -1));
  };

  useEffect(() => {
    let approvalDefault = map(curData.ApprovingOfficers, v => {
      const { uid, name } = v;
      return `${name}/${uid}`;
    });
    if (!curData) {
      const mainAccount = find(contactList, v => {
        const { title } = v;
        return (
          title ===
          intl('saenext.approval-setting.setting-slide-panel.ApprovingOfficers.PrimaryAccount')
        );
      });
      if (mainAccount) {
        approvalDefault = [get(mainAccount, 'value')];
      }
    }
    setValue('approvingOfficers', approvalDefault);
  }, [JSON.stringify(curData)]);

  return (
    <Form field={field} {...formItemLayout}>
      <FormItem
        label={
          <TextWithBalloon
            text={intl('saenext.approval-setting.setting-slide-panel.ApprovingOfficers.Approver')}
            tips={
              <>
                {intl(
                  'saenext.approval-setting.setting-slide-panel.ApprovingOfficers.TheApproverMustSelectFrom',
                )}

                <br />
                {intl(
                  'saenext.approval-setting.setting-slide-panel.ApprovingOfficers.ByDefaultThePrimaryAccount',
                )}

                <br />
                {intl(
                  'saenext.approval-setting.setting-slide-panel.ApprovingOfficers.IfThePrimaryAccountIs',
                )}
              </>
            }
          />
        }
        help={
          <>
            {intl(
              'saenext.approval-setting.setting-slide-panel.ApprovingOfficers.IfYouFindThatThe',
            )}

            <ExternalLink
              label={intl(
                'saenext.approval-setting.setting-slide-panel.ApprovingOfficers.ContactManagement',
              )}
              url="/operations-management/approval?type=concats"
            />
            {intl(
              'saenext.approval-setting.setting-slide-panel.ApprovingOfficers.AddSettingsToTheMenu',
            )}
          </>
        }
      >
        <Select
          {...init('approvingOfficers')}
          mode="multiple"
          showSearch
          placeholder=""
          filterLocal={false}
          dataSource={selectContactList}
          onSearch={handleApprovingOfficersSearch}
          style={{ width: 400 }}
          valueRender={approvingValueRender}
          itemRender={approvingValueRender}
          state={contactLoading ? 'loading' : undefined}
          disabled={contactLoading}
        />

        <RefreshButton
          type="normal"
          className="ml-s"
          iconSize="small"
          handler={() => getListContacts()}
        />
      </FormItem>
    </Form>
  );
};
export default ApprovingOfficers;
