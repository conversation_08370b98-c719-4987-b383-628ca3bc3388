import { intl } from '@ali/cnd';
import React, { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { Form } from '@ali/cnd';
import { get, map } from 'lodash';
import RegionNamespaceAppIdForm from '~/components/shared/RegionNamespaceAppIdForm';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    fixedSpan: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const ApprovalScope = ({ field, curData }, ref) => {
  const { init, setValue } = field;
  const approvalScopeInfo = useRef(null);

  useEffect(() => {
    const appsDefault = () => {
      if (curData.ResourceDesc) {
        if (curData.ResourceDesc.isAllApps) {
          return [{ value: 'allApps' }];
        } else {
          return map(curData.ResourceDesc.resources, v => ({ value: v.resourceId }));
        }
      } else {
        return [];
      }
    };
    setValue('approvalScopeInfo', {
      appjob: appsDefault(),
      region: get(curData, 'Region', ''),
      namespaceId: get(curData, 'NamespaceId', ''),
    });
  }, [curData]);

  useImperativeHandle(ref, () => approvalScopeInfo?.current || {});

  return (
    <Form field={field} {...formItemLayout}>
      <FormItem
        label={intl('saenext.approval-setting.setting-slide-panel.ApprovalScope.Resources')}
        required
      >
        <RegionNamespaceAppIdForm
          {...init('approvalScopeInfo')}
          appUseDetailValue
          appTypes={['allApps']}
          allowAllRegions
          allowAllNamespaces
          ref={approvalScopeInfo}
        />
      </FormItem>
      <FormItem
        label={intl('saenext.approval-setting.setting-slide-panel.ApprovalScope.OperationType')}
        required
      >
        <span style={{ lineHeight: '32px' }}>
          {intl('saenext.approval-setting.setting-slide-panel.ApprovalScope.PublishChanges')}
        </span>
      </FormItem>
    </Form>
  );
};

export default forwardRef(ApprovalScope);
