import { intl } from '@ali/cnd';
import React from 'react';
import { Truncate, Actions, LinkButton } from '@ali/cnd';
import _ from 'lodash';
import services from '~/services';

export const columns = ({ handleEdit, handleDelete }) => {
  return [
    {
      key: 'Operations',
      title: intl(
        'saenext.enterprise.approval-setting.ApprovalSettingTableProps.ApprovalOperation',
      ),

      dataIndex: 'Operations',
      width: 300,
      cell: (value) => {
        return (
          <Truncate type="width" threshold={300}>
            {_.map(value, (item) => {
              return <div> {item.Desc}</div>;
            })}
          </Truncate>
        );
      },
    },

    {
      key: 'Region',
      title: 'region',
      dataIndex: 'Region',
      width: 300,
      cell: (value) => {
        return value === '*'
          ? intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.AllRegions')
          : value;
      },
    },

    {
      key: 'NamespaceId',
      title: intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.Namespace'),
      width: 300,
      dataIndex: 'NamespaceId',
      cell: (value) => {
        return value?.endsWith('*')
          ? intl('saenext.components.shared.NamespaceTag.AllNamespaces')
          : value;
      },
    },

    {
      key: 'ResourceDesc',
      title: intl(
        'saenext.enterprise.approval-setting.ApprovalSettingTableProps.AuthorizeApplications',
      ),

      dataIndex: 'ResourceDesc',
      width: 300,
      cell: (value) => {
        return value.isAllApps ? (
          <span>
            {intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.AllApplications')}
          </span>
        ) : (
          <Truncate type="width" threshold={300}>
            {_.map(value.resources, (item) => {
              return <div> {item.resourceName}</div>;
            })}
          </Truncate>
        );
      },
    },

    {
      key: 'ExcludeAccounts',
      title: intl(
        'saenext.enterprise.approval-setting.ApprovalSettingTableProps.ApprovalFreeSubAccountWhitelist',
      ),

      dataIndex: 'ExcludeAccounts',
      width: 300,
      cell: (value) => {
        return (
          <Truncate type="width" threshold={300}>
            <div>
              {value.length === 0 &&
                intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.None')}
              {_.map(value, (item) => {
                return <div> {item.name} </div>;
              })}
            </div>
          </Truncate>
        );
      },
    },

    {
      key: 'ApprovingOfficers',
      title: intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.Approver'),
      dataIndex: 'ApprovingOfficers',
      width: 300,
      cell: (value) => {
        return (
          <Truncate type="width" threshold={300}>
            <div>
              {value.length === 0 &&
                intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.None')}
              {_.map(value, (item) => {
                return <div> {item.name} </div>;
              })}
            </div>
          </Truncate>
        );
      },
    },

    {
      key: 'operations',
      title: intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.Operation'),
      width: 300,
      cell: (value, index, record) => {
        return (
          <Actions>
            <LinkButton
              onClick={() => {
                handleEdit(record);
              }}
            >
              {intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.Edit')}
            </LinkButton>
            <LinkButton
              onClick={() => {
                handleDelete(record);
              }}
            >
              {intl('saenext.enterprise.approval-setting.ApprovalSettingTableProps.Delete')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];
};

export const fetchData = async (params) => {
  const { current, pageSize } = params;
  const data = await services.ListOperationApprovalRules({
    params: {
      CurrentPage: current,
      PageSize: pageSize,
    },

    customErrorHandle: (err, serviceConfig, callback) => {
      if (err.code === 'AuthenticationFailed') {
        return {
          error: 'AuthenticationFailed',
        };
      } else {
        callback();
      }
    },
  });

  return {
    data: data.Data?.OperationApprovalRules,
    total: data.TotalSize,
    error: data.error,
  };
};
