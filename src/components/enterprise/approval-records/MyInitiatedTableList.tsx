import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import CndTable, { ISearch } from '@ali/cnd-table';
import { search, fetchData, myInitiatedTableColumns } from './TableProps';
import { Dialog, Message } from '@ali/cnd';
import services from '~/services';

const MyInitiatedTableList = props => {
  const { ramUsers } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [OrderBy, setOrderBy] = useState('state');
  const [Reverse, setReverse] = useState<any>(true);

  const handelCancelApproval = ({ RecordId }) => {
    Dialog.confirm({
      title: intl('saenext.enterprise.approval-records.MyInitiatedTableList.Prompt'),
      content: (
        <p>{intl('saenext.enterprise.approval-records.MyInitiatedTableList.AreYouSureYouWant')}</p>
      ),
      onOk: async () => {
        const { Data } = await services.CancelOperationApproval({
          OperationApprovalRecordId: RecordId,
        });
        Message.success(
          intl(
            'saenext.enterprise.approval-records.MyInitiatedTableList.ApprovalWithdrawnSuccessfully',
          ),
        );
        setRefreshIndex(+new Date());
      },
    });
  };
  const handelResendApproval = ({ RecordId }) => {
    const dialog = Dialog.confirm({
      title: intl('saenext.enterprise.approval-records.MyInitiatedTableList.Prompt'),
      content: (
        <p>
          {intl('saenext.enterprise.approval-records.MyInitiatedTableList.AreYouSureYouWant.1')}
        </p>
      ),
      onOk: () => {
        return new Promise<void>(async (resolve, reject) => {
          await services.ResendApprovalNotification({
            RecordId,
            customErrorHandle(err, serviceConfig, callback) {
              if (err) {
                if (err.code == 'sae.errorcode.notification.too.frequent.message') {
                  Message.error(
                    intl(
                      'saenext.enterprise.approval-records.MyInitiatedTableList.SubmittedForReviewDoNot',
                    ),
                  );
                }
              }
              dialog.hide();
              reject();
              callback();
            },
          });
          Message.success(
            intl('saenext.enterprise.approval-records.MyInitiatedTableList.SubmittedForReview'),
          );
          resolve();
          setRefreshIndex(+new Date());
        });
      },
    });
  };
  const tableSortChange = (key, type) => {
    const _reverse = type === '' ? '' : type === 'asc';
    const _key = type === '' ? '' : key;
    setOrderBy(_key);
    setReverse(_reverse);
    setRefreshIndex(+new Date());
  };

  return (
    <CndTable
      fetchData={
        (async value => {
          return await fetchData({ ...value, OrderBy, Reverse, Condition: 2 });
        }) as any
      }
      columns={myInitiatedTableColumns({
        OrderBy,
        Reverse,
        tableSortChange,
        handelResendApproval,
        handelCancelApproval,
      })}
      refreshIndex={refreshIndex}
      search={search(ramUsers) as ISearch}
      showRefreshButton
      pagination={{
        pageSizeList: [10, 20, 50, 100],
        hideOnlyOnePage: true,
      }}
    />
  );
};

export default MyInitiatedTableList;
