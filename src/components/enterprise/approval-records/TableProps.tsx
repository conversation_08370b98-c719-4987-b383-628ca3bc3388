import { intl, Link } from '@ali/cnd';
import React from 'react';
import { Truncate, Actions, LinkButton } from '@ali/cnd';
import SortLabel from '~/components/app-list/micro-app/components/SortLabel';
import moment from 'moment';
import { getParams, microAppLink } from '~/utils/global';
import CachedData from '~/cache/common';
import { forEach, get } from 'lodash';
import services from '~/services';
import If from '~/components/shared/If';
import TextWithBalloon from '~/components/shared/TextWithBalloon';

const BaseColumns = () => {
  return [
    {
      key: 'Operation',
      title: intl('saenext.enterprise.approval-records.TableProps.ApprovalOperation'),
      dataIndex: 'Operation',
      width: 300,
      cell: value => {
        return (
          <Truncate type="width" threshold={300}>
            <div> {value.Desc}</div>
          </Truncate>
        );
      },
    },
    {
      key: 'NamespaceApp',
      title: intl('saenext.enterprise.approval-records.TableProps.NamespaceApplication'),
      dataIndex: 'NamespaceApp',
      width: 300,
      cell: (value, index, record) => {
        const { ResourceInfo, NamespaceId = '' } = record;
        const regionId = NamespaceId.split(':')[0];

        const {
          ResourceId : appId = ''
        } = ResourceInfo || {};

        const { v1Link, v2Link, enable } = microAppLink(appId, regionId);
        return (
          <Link
            href={!enable && v1Link}
            to={enable && v2Link}
            // @ts-ignore
            target={!enable && "_blank"}
          >
            {record.NamespaceId} : {record.ResourceInfo.ResourceName}
          </Link>
        );
      },
    },
  ];
};

const UidColumns = ramUsers => {
  const renderUser = userId => {
    let str = '';
    forEach(ramUsers, v => {
      if (v.value === userId) {
        const { value, label } = v;
        str = `${label}/${value}`;
      }
    });
    return str;
  };

  return [
    {
      key: 'Operator',
      title: intl('saenext.enterprise.approval-records.TableProps.InitiatorUsernameId'),
      width: 300,
      dataIndex: 'Operator',
      cell: value => renderUser(value.Uid),
    },
  ];
};

export const search = ramUsers => {
  return {
    defaultDataIndex: 'appName',
    defaultSelectedDataIndex: 'appName',
    options: [
      {
        label: intl('saenext.enterprise.approval-records.TableProps.Initiator'),
        dataIndex: 'operator',
        template: 'select',
        templateProps: {
          dataSource: ramUsers,
        },
      },
      {
        label: intl('saenext.enterprise.approval-records.TableProps.ApplicationName'),
        dataIndex: 'appName',
        template: 'input',
        templateProps: {
          placeholder: intl(
            'saenext.enterprise.approval-records.TableProps.SearchByApplicationName',
          ),
        },
      },
    ],
  };
};

export const pendingTableColumns = ({
  OrderBy,
  Reverse,
  tableSortChange,
  ramUsers,
  handleApprovalDialog,
  handleRefusedDialog,
  handleHandoverDialog,
}) => {
  const handleNavToOrderDetail = ({ ResourceInfo, NamespaceId = '', ChangeOrderId, RecordId }) => {
    const {
      ResourceId : appId = ''
    } = ResourceInfo || {};

    const regionId = NamespaceId.split(':')[0];

    window.xconsoleHistory.push(`/${regionId}/app-list/${appId}/micro-app/record/${ChangeOrderId}?approvalRecordId=${RecordId}`)
  };

  return [
    ...BaseColumns(),
    ...UidColumns(ramUsers),
    {
      key: 'CreateTime',
      title: (
        <SortLabel
          dataSource={[
            {
              key: 'createTime',
              name: intl('saenext.enterprise.approval-records.TableProps.CreationTime'),
            },
          ]}
          OrderBy={OrderBy}
          Reverse={Reverse}
          handler={tableSortChange}
        />
      ),

      dataIndex: 'CreateTime',
      width: 300,
      cell: value => <span>{moment(value).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      key: 'operations',
      title: intl('saenext.enterprise.approval-records.TableProps.Operation'),
      width: 300,
      cell: (value, index, record) => {
        return (
          <Actions threshold={4}>
            <LinkButton onClick={() => handleNavToOrderDetail(record)}>
              {intl('saenext.enterprise.approval-records.TableProps.ViewDetails')}
            </LinkButton>
            <LinkButton
              onClick={() => {
                handleApprovalDialog(record, 'approved', 'single');
              }}
            >
              {intl('saenext.enterprise.approval-records.TableProps.Agree')}
            </LinkButton>
            <LinkButton
              onClick={() => {
                handleRefusedDialog(record, 'refused', 'single');
              }}
            >
              {intl('saenext.enterprise.approval-records.TableProps.Reject')}
            </LinkButton>
            <LinkButton
              onClick={() => {
                handleHandoverDialog(record, 'handover', 'single');
              }}
            >
              {intl('saenext.enterprise.approval-records.TableProps.Transfer')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];
};

export const doneTableColumns = ({ ramUsers, OrderBy, Reverse, tableSortChange }) => {
  return [
    ...BaseColumns(),
    ...UidColumns(ramUsers),
    {
      key: 'CreateTime',
      title: intl('saenext.enterprise.approval-records.TableProps.CreationTime'),
      dataIndex: 'CreateTime',
      width: 300,
      cell: value => <span>{moment(value).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      key: 'UpdateTime',
      title: (
        <SortLabel
          dataSource={[
            {
              key: 'updateTime',
              name: intl('saenext.enterprise.approval-records.TableProps.ApprovalCompletionTime'),
            },
          ]}
          OrderBy={OrderBy}
          Reverse={Reverse}
          handler={tableSortChange}
        />
      ),

      dataIndex: 'UpdateTime',
      width: 300,
      cell: value => <span>{moment(value).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      key: 'State',
      title: intl('saenext.enterprise.approval-records.TableProps.ApprovalResult'),
      dataIndex: 'State',
      width: 300,
      cell: (value, index, record) => {
        return (
          <>
            <span>{value.Desc}</span>
            <If
              condition={
                (value.Desc === intl('saenext.enterprise.approval-records.TableProps.Rejected') ||
                  value.Desc ===
                    intl('saenext.enterprise.approval-records.TableProps.OthersHaveRefused')) &&
                !(record.Comment === '')
              }
            >
              <TextWithBalloon
                text={` `}
                tips={intl(
                  'saenext.enterprise.approval-records.TableProps.RejectionRecordcomment',
                  { recordComment: record.Comment },
                )}
              />
            </If>
          </>
        );
      },
    },
  ];
};

export const myInitiatedTableColumns = ({
  OrderBy,
  Reverse,
  tableSortChange,
  handelResendApproval,
  handelCancelApproval,
}) => {
  const renderUser = value => {
    let str = '';
    if (value.hasOwnProperty('Name')) {
      str = `${value.Name}/${value.Uid}`;
    } else {
      str = intl('saenext.enterprise.approval-records.TableProps.PrimaryAccount');
    }
    return str;
  };

  return [
    ...BaseColumns(),
    {
      key: 'CreateTime',
      title: intl('saenext.enterprise.approval-records.TableProps.CreationTime'),
      dataIndex: 'CreateTime',
      width: 300,
      cell: value => <span>{moment(value).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      key: 'State',
      title: (
        <SortLabel
          dataSource={[
            {
              key: 'state',
              name: intl('saenext.enterprise.approval-records.TableProps.ApprovalStatus'),
            },
          ]}
          OrderBy={OrderBy}
          Reverse={Reverse}
          handler={tableSortChange}
        />
      ),

      dataIndex: 'State',
      width: 300,
      cell: (value, index, record) => {
        return (
          <>
            <span>{value.Desc}</span>
            <If
              condition={
                value.Desc === intl('saenext.enterprise.approval-records.TableProps.Rejected') &&
                !(record.Comment === '')
              }
            >
              <TextWithBalloon
                text={` `}
                tips={intl(
                  'saenext.enterprise.approval-records.TableProps.RejectionRecordcomment',
                  { recordComment: record.Comment },
                )}
              />
            </If>
          </>
        );
      },
    },
    {
      key: 'ApprovingOfficer',
      title: intl('saenext.enterprise.approval-records.TableProps.Approver'),
      dataIndex: 'ApprovingOfficer',
      width: 300,
      cell: value => renderUser(value),
    },
    {
      key: 'operations',
      title: intl('saenext.enterprise.approval-records.TableProps.Operation'),
      width: 300,
      cell: (value, index, record) => {
        return (
          <Actions>
            <LinkButton
              disabled={
                record.State.Desc !== intl('saenext.enterprise.approval-records.TableProps.Pending')
              }
              onClick={() => {
                handelResendApproval(record);
              }}
            >
              {intl('saenext.enterprise.approval-records.TableProps.Reminder')}
            </LinkButton>
            <LinkButton
              disabled={
                record.State.Desc !== intl('saenext.enterprise.approval-records.TableProps.Pending')
              }
              onClick={() => {
                handelCancelApproval(record);
              }}
            >
              {intl('saenext.enterprise.approval-records.TableProps.Withdraw')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];
};

export const fetchData = async params => {
  const { current, pageSize, appName, operator, OrderBy, Reverse, Condition } = params;
  const data = await services.ListOperationApprovalRecords({
    CurrentPage: current,
    PageSize: pageSize,
    Condition,
    OrderBy,
    Reverse,
    ResourceName: appName,
    Operator: operator,
  });
  return {
    data: data.Data.OperationApprovalRecords,
    total: data.TotalSize,
  };
};
