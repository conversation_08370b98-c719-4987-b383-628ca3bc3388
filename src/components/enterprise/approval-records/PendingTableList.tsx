import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Button, Dialog, Form, Input, Message, Select, Tag } from '@ali/cnd';
import CndTable, { ISearch } from '@ali/cnd-table';
import services from '~/services';
import { pendingTableColumns, fetchData, search } from './TableProps';
import { forEach, map, size } from 'lodash';
import ExternalLink from '~/components/shared/ExternalLink';

const PendingTableList = props => {
  const { ramUsers, contactList } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [OrderBy, setOrderBy] = useState('createTime');
  const [Reverse, setReverse] = useState<any>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [contactData, setContactData] = useState([]);

  const initialContactData = () => {
    const list = map(contactList, v => {
      const { Uid, Name, IsMainAccount } = v;
      return {
        value: Uid,
        label: Name,
        title: IsMainAccount
          ? intl('saenext.enterprise.approval-records.PendingTableList.PrimaryAccount')
          : '',
      };
    });
    setContactData(list);
  };

  useEffect(() => {
    initialContactData();
  }, [contactList]);

  const tableSortChange = (key, type) => {
    const _reverse = type === '' ? '' : type === 'asc';
    const _key = type === '' ? '' : key;
    setOrderBy(_key);
    setReverse(_reverse);
    changeRefreshIndex();
  };
  const changeRefreshIndex = () => {
    setRefreshIndex(refreshIndex => refreshIndex + 1);
  };
  const sendApprovalRequest = async ({
    recordId,
    result,
    comment = '',
    otherApprovingOfficer = '',
  }) => {
    const data = await services.ApprovalOperationRequest({
      OperationApprovalRecordId: recordId,
      OperationApprovalResult: result,
      OperationApprovalComment: comment,
      OtherApprovingOfficer: otherApprovingOfficer,
    });
    return data;
  };

  const handleApprovalDialog = (record, result, type) => {
    let recordIdList = [];
    if (type === 'single') {
      const { RecordId } = record;
      recordIdList = [RecordId];
    } else {
      recordIdList = selectedRowKeys;
    }
    const dialog = Dialog.confirm({
      title: intl('saenext.enterprise.approval-records.PendingTableList.Prompt'),
      content: (
        <p>{intl('saenext.enterprise.approval-records.PendingTableList.AreYouSureYouWant')}</p>
      ),
      onOk: async () => {
        await Promise.all(
          forEach(recordIdList, async v => {
            const params = {
              recordId: v,
              result,
            };
            // console.log(params);
            await sendApprovalRequest(params);
          }),
        );
        Message.success(
          intl('saenext.enterprise.approval-records.PendingTableList.AgreeToApproval'),
        );
        dialog.hide();
        setTimeout(() => {
          setRefreshIndex(+new Date());
        }, 1000);
      },
    });
  };

  const handleRefusedDialog = (record, result, type) => {
    let recordIdList = [];
    if (type === 'single') {
      const { RecordId } = record;
      recordIdList = [RecordId];
    } else {
      recordIdList = selectedRowKeys;
    }
    let comment = '';
    const dialog = Dialog.show({
      title: intl('saenext.enterprise.approval-records.PendingTableList.ConfirmApprovalRejection'),
      content: (
        <div style={{ width: 400 }}>
          <Message type="warning">
            {intl(
              'saenext.enterprise.approval-records.PendingTableList.AfterTheApprovalIsRejected',
            )}
          </Message>
          <div>
            <Input.TextArea
              size="large"
              placeholder={intl(
                'saenext.enterprise.approval-records.PendingTableList.ReasonForRejection',
              )}
              maxLength={200}
              hasLimitHint
              onChange={value => (comment = value)}
              style={{ width: '100%', marginTop: '20px' }}
            />
          </div>
        </div>
      ),

      onOk: async () => {
        // console.log(comment);
        await Promise.all(
          forEach(recordIdList, async v => {
            const params = {
              recordId: v,
              result,
              comment,
            };
            // console.log(params);
            await sendApprovalRequest(params);
          }),
        );
        Message.success(intl('saenext.enterprise.approval-records.PendingTableList.Rejected'));
        comment = '';
        dialog.hide();
        setTimeout(() => {
          setRefreshIndex(+new Date());
        }, 1000);
      },
    });
  };

  const handleHandoverDialog = (record, result, type) => {
    let recordIdList = [];
    if (type === 'single') {
      const { RecordId } = record;
      recordIdList = [RecordId];
    } else {
      recordIdList = selectedRowKeys;
    }
    let otherApprovingOfficer = '';
    const dialog = Dialog.show({
      title: intl('saenext.enterprise.approval-records.PendingTableList.ApprovalTransfer'),
      content: (
        <div style={{ width: 500 }}>
          <Message type="notice">
            {intl('saenext.enterprise.approval-records.PendingTableList.AfterTheApprovalTaskIs')}
          </Message>
          <Form inline style={{ marginTop: 20 }}>
            <Form.Item
              label={intl('saenext.enterprise.approval-records.PendingTableList.Approver')}
              required
              requiredMessage={intl(
                'saenext.enterprise.approval-records.PendingTableList.PleaseSelectTransferApprover',
              )}
            >
              <Select
                showSearch
                placeholder=""
                filterLocal={false}
                dataSource={contactData}
                style={{ width: 400 }}
                onChange={(value: string) => (otherApprovingOfficer = value)}
                valueRender={approvingValueRender}
                itemRender={approvingValueRender}
              />
            </Form.Item>
          </Form>
          <div style={{ margin: 20, paddingLeft: 40 }}>
            <p>
              {intl('saenext.enterprise.approval-records.PendingTableList.IfYouFindThatThe')}

              <ExternalLink
                label={intl(
                  'saenext.enterprise.approval-records.PendingTableList.ContactManagement',
                )}
                url="/operations-management/approval?type=concats"
              />
              {intl('saenext.enterprise.approval-records.PendingTableList.AddSettingsToTheMenu')}
            </p>
          </div>
        </div>
      ),

      onOk: async () => {
        if (!otherApprovingOfficer) {
          Message.error(
            intl('saenext.enterprise.approval-records.PendingTableList.SelectAnApprover'),
          );
          return;
        }
        const options = [];
        forEach(recordIdList, async v => {
          const params = {
            recordId: v,
            result,
            otherApprovingOfficer,
          };
          options.push(sendApprovalRequest(params));
        });
        const res = await Promise.all(options);
        Message.success(intl('saenext.enterprise.approval-records.PendingTableList.Transferred'));
        otherApprovingOfficer = '';
        recordIdList = [];
        dialog.hide();
        setTimeout(() => {
          setRefreshIndex(+new Date());
        }, 1000);
      },
    });
  };

  const approvingValueRender = item => {
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          width: 'max-content',
          height: '100%',
          marginRight: 12,
        }}
      >
        <span> {item.label}</span>
        {item.title ? (
          <Tag color="yellow" style={{ marginLeft: 10 }}>
            {intl('saenext.enterprise.approval-records.PendingTableList.PrimaryAccount')}
          </Tag>
        ) : (
          ''
        )}
      </div>
    );
  };

  return (
    <CndTable
      fetchData={
        (async value => {
          return await fetchData({ ...value, OrderBy, Reverse, Condition: 0 });
        }) as any
      }
      primaryKey="RecordId"
      columns={pendingTableColumns({
        OrderBy,
        Reverse,
        ramUsers,
        tableSortChange,
        handleHandoverDialog,
        handleRefusedDialog,
        handleApprovalDialog,
      })}
      refreshIndex={refreshIndex}
      search={search(ramUsers) as ISearch}
      showRefreshButton
      rowSelection={{
        onChange: value => {
          setSelectedRowKeys(value);
        },
        selectedRowKeys,
      }}
      selection={value => (
        <>
          <Button
            type="primary"
            disabled={size(selectedRowKeys) < 1}
            onClick={() => handleApprovalDialog('', 'approved', 'multiple')}
          >
            {intl('saenext.enterprise.approval-records.PendingTableList.BatchPass')}
          </Button>
          <Button
            type="normal"
            disabled={size(selectedRowKeys) < 1}
            onClick={() => handleRefusedDialog('', 'refused', 'multiple')}
          >
            {intl('saenext.enterprise.approval-records.PendingTableList.BatchRejection')}
          </Button>
          <Button
            type="normal"
            disabled={size(selectedRowKeys) < 1}
            onClick={() => {
              handleHandoverDialog('', 'handover', 'multiple');
            }}
          >
            {intl('saenext.enterprise.approval-records.PendingTableList.BatchTransfer')}
          </Button>
        </>
      )}
      pagination={{
        pageSizeList: [10, 20, 50, 100],
        hideOnlyOnePage: true,
      }}
    />
  );
};

export default PendingTableList;
