import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Tab } from '@ali/cnd';
import CachedData from '~/cache/common';
import { getParams } from '~/utils/global';
import services from '~/services';
import { map } from 'lodash';
import PendingTableList from './PendingTableList';
import DoneTableList from './DoneTableList';
import MyInitiatedTableList from './MyInitiatedTableList';
import useRegion from '~/hooks/useRegion';

const ApprovalRecords = () => {
  const [activeTab, setActiveTab] = useState('pending');
  const [ramUsers, setRamUsers] = useState([]);
  const [contactList, setContactList] = useState([]);
  const isMainAccount = CachedData.isMainAccount();

  const regionId = useRegion();

  useEffect(() => {
    const tabParams = getParams('activeTab');
    if (tabParams) {
      setActiveTab(tabParams);
    }
  }, [isMainAccount]);

  const getListRamUsers = async () => {
    const User = await getRamUserRequest();
    const list = map(User, v => {
      const { UserName, UserId } = v;
      return {
        value: UserId,
        label: UserName,
      };
    });
    setRamUsers(list);
  };

  const getRamUserRequest = async (params = {}) => {
    const res = await services.ListRamUsers({
      params: {
        MaxItems: 100,
        ...params,
      },
      customErrorHandle: (err, serviceConfig, callback) => {
        if (err.code === 'NoPermission') {
          return {
            error: 'NoPermission',
          };
        } else {
          callback();
        }
      },
    });
    if (res.error === 'NoPermission') {
      return [];
    }
    const { IsTruncated, Marker, Users = {} } = res || {};
    const { User = [] } = Users || {};
    if (IsTruncated && Marker) {
      const moreUser = await getRamUserRequest({ Marker });
      return [...User, ...moreUser];
    } else {
      return User;
    }
  };

  const getListContacts = async () => {
    const {
      Data: { ContactsInfo = [] },
    } = await services.ListContactsWithoutDetailInfo();
    setContactList(ContactsInfo);
  };

  useEffect(() => {
    getListRamUsers();
    getListContacts();
  }, []);
  return (
    <Tab key={regionId} activeKey={activeTab}>
      <Tab.Item
        title={intl('saenext.enterprise.approval-records.PendingApproval')}
        key={'pending'}
        onClick={() => setActiveTab('pending')}
      >
        <div style={{ paddingTop: 16 }}>
          <PendingTableList ramUsers={ramUsers} contactList={contactList} />
        </div>
      </Tab.Item>
      <Tab.Item
        title={intl('saenext.enterprise.approval-records.Processed')}
        key={'done'}
        onClick={() => setActiveTab('done')}
      >
        <div style={{ paddingTop: 16 }}>
          <DoneTableList ramUsers={ramUsers} />
        </div>
      </Tab.Item>
      <Tab.Item
        title={intl('saenext.enterprise.approval-records.IInitiated')}
        key={'mine'}
        onClick={() => setActiveTab('mine')}
      >
        <div style={{ paddingTop: 16 }}>
          <MyInitiatedTableList ramUsers={ramUsers} />
        </div>
      </Tab.Item>
    </Tab>
  );
};
export default ApprovalRecords;
