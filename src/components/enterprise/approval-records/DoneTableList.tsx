import React, { useState } from 'react';
import CndTable, { ISearch } from '@ali/cnd-table';
import { doneTableColumns, search, fetchData } from './TableProps';

const DoneTableList = props => {
  const { ramUsers } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [OrderBy, setOrderBy] = useState('updateTime');
  const [Reverse, setReverse] = useState<any>(false);

  const tableSortChange = (key, type) => {
    const _reverse = type === '' ? '' : type === 'asc';
    const _key = type === '' ? '' : key;
    setOrderBy(_key);
    setReverse(_reverse);
    setRefreshIndex(+new Date());
  };

  return (
    <CndTable
      fetchData={
        (async value => {
          return await fetchData({ ...value, OrderBy, Reverse, Condition: 1 });
        }) as any
      }
      columns={doneTableColumns({
        OrderBy,
        Reverse,
        ramUsers,
        tableSortChange,
      })}
      refreshIndex={refreshIndex}
      search={search(ramUsers) as ISearch}
      showRefreshButton
      pagination={{
        pageSizeList: [10, 20, 50, 100],
        hideOnlyOnePage: true,
      }}
    />
  );
};

export default DoneTableList;
