import { intl } from '@ali/cnd';
import React from 'react';
import { get, find, toString } from 'lodash';
import moment from 'moment';
import { Tag, Balloon, CndTable, Actions, LinkButton } from '@ali/cnd';
import { jsonParse } from '~/utils/transfer-data';
import { eventSource } from './EventFilterForm';
import services from '~/services';

const levelStyles = {
  WARNING: { background: '#F7EDED', color: '#996262' },
  INFO: { background: '#EDF7ED', color: '#629962' },
  CRITICAL: { background: '#e92f2f', color: '#fff' },
};

const informWay = [
  {
    value: 'dingTalk',
    label: intl('saenext.event-center.event-search.EventTableProps.Dingtalk'),
  },
  {
    value: 'wechat',
    label: intl('saenext.event-center.subscribe-rule.NoticeRuleStep.EnterpriseWechat'),
  },
  {
    value: 'feishu',
    label: intl('saenext.event-center.subscribe-rule.NoticeRuleStep.FlyingBook'),
  },
  {
    value: 'email',
    label: intl('saenext.event-center.event-search.EventTableProps.EmailAddress'),
  },
  {
    value: 'sms',
    label: intl('saenext.event-center.event-search.EventTableProps.Sms'),
  },
];

/**
 * 是否通知组件
 * @param {*} value
 * @param {*} record
 *
 */
const IsNotified = ({ value, record }) => {
  const dataSource = [];
  const notifiedColumns = [
    {
      title: intl('saenext.event-center.event-search.EventTableProps.NotificationMethod'),
      dataIndex: 'channel',
      width: 200,
      cell: value => {
        const val = value.map(v => {
          const label = informWay.find(i => i.value === v).label;
          return label;
        });
        return val.join(',');
      },
    },
    {
      title: intl('saenext.event-center.event-search.EventTableProps.ContactUid'),
      dataIndex: 'account',
      width: 200,
      cell: (value, index, record) => {
        return value.map(v => {
          return (
            <div style={{ display: 'flex' }}>
              <span style={{ marginRight: 6 }}>{v.name}</span>/
              <span
                style={{
                  display: 'inline-block',
                  transform: 'scale(0.9)',
                  fontStyle: 'oblique',
                }}
              >
                {v.uid}
              </span>
            </div>
          );
        });
      },
    },
  ];

  // const NotifyInfo =
  //   '{"successNotifyInfo":[{"notifiedAccounts":"[{\\"name\\":\\"yichuan1\\",\\"uid\\":\\"237656433958037887\\"},{\\"name\\":\\"yichuan2\\",\\"uid\\":\\"237219933958037954\\"}]","notifyChannels":"[\\"dingTalk\\"]"}]}';
  const successNotifyInfo = get(jsonParse(record.NotifyInfo), 'successNotifyInfo', []);
  // const successNotifyInfo = _.get(jsonParse(NotifyInfo), 'successNotifyInfo', []);
  successNotifyInfo.forEach(i => {
    if (!i) return;
    const accounts = jsonParse(i.notifiedAccounts);
    const channels = jsonParse(i.notifyChannels);
    if (accounts && channels) {
      dataSource.push({ channel: channels, account: accounts });
    }
  });

  return value ? (
    <Balloon
      style={{ minWidth: 400 }}
      trigger={
        <span style={{ color: '#0070cc', cursor: 'help' }}>
          {intl('saenext.event-center.event-search.EventTableProps.Yes')}
        </span>
      }
      closable={false}
    >
      <CndTable columns={notifiedColumns} dataSource={dataSource} />
    </Balloon>
  ) : (
    <span>{intl('saenext.event-center.event-search.EventTableProps.No')}</span>
  );
};

/**
 * 上下文列
 * @param {*} value
 * @param {*} record
 */
const ContextColumn = ({ record, onClickText }) => {
  return (
    <div style={{ height: 90, overflowY: 'scroll', wordBreak: 'break-word' }}>
      <div onClick={() => onClickText(record.NamespaceId, 'namespaceId')}>
        {record.NamespaceId !== 'null' && (
          <>
            NamespaceId :
            <span
              style={{
                cursor: 'pointer',
                color: '#0070cc',
                textDecorationLine: 'underline',
                marginLeft: 4,
              }}
            >
              {record.NamespaceId}
            </span>
          </>
        )}
      </div>
      <div onClick={() => onClickText(record.ResourceId, 'appId')}>
        {record.ResourceId !== 'null' && (
          <>
            ResourceId :
            <span
              style={{
                cursor: 'pointer',
                color: '#0070cc',
                textDecorationLine: 'underline',
                marginLeft: 4,
              }}
            >
              {record.ResourceId}
            </span>
          </>
        )}
      </div>
      <div onClick={() => onClickText(record.InstanceId, 'instanceId')}>
        {record.InstanceId !== 'null' && (
          <>
            InstanceId :
            <span
              style={{
                cursor: 'pointer',
                color: '#0070cc',
                textDecorationLine: 'underline',
                marginLeft: 4,
              }}
            >
              {record.InstanceId}
            </span>
          </>
        )}
      </div>
      {record.Source !== 'null' && <div>Source : {record.Source}</div>}
      {record.Type !== 'null' && <div>Type : {record.Type}</div>}
      {record.Level !== 'null' && <div>Level : {record.Level}</div>}
      <div>Detail : {record.Detail}</div>
      {record.RegionId !== 'null' && <div>RegionId : {record.RegionId}</div>}
      <div>IsNotified : {JSON.stringify(record.IsNotified)}</div>
      {record.NotifyInfo !== 'null' && <div>NotifyInfo : {record.NotifyInfo}</div>}
    </div>
  );
};

export const columns = params => {
  const { typeList, onClickLevel, onClickText, handleShow, handleSubscribe } = params;
  return [
    {
      title: intl('saenext.event-center.event-search.EventTableProps.Time'),
      dataIndex: 'Time',
      width: 200,
      cell: value => moment.unix(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: intl('saenext.event-center.event-search.EventTableProps.Source'),
      dataIndex: 'Source',
      width: 150,
      cell: value => {
        return find(eventSource, event => event.value === value)?.label || value;
      },
    },
    {
      title: intl('saenext.event-center.event-search.EventTableProps.Type'),
      dataIndex: 'Type',
      width: 200,
      cell: value => {
        const typeRecord = typeList.find(i => i.value === value);
        const typeLabel = get(typeRecord, 'label', value);
        return <span>{typeLabel}</span>;
      },
    },
    {
      title: intl('saenext.event-center.event-search.EventTableProps.Grade'),
      dataIndex: 'Level',
      width: 100,
      cell: value => {
        return (
          <Tag style={levelStyles[value]} onClick={() => onClickLevel(value)}>
            {value}
          </Tag>
        );
      },
    },
    {
      title: intl('saenext.event-center.event-search.EventTableProps.Summary'),
      dataIndex: 'Message',
      width: 400,
      cell: value => <span style={{ wordBreak: 'break-all' }}>{value}</span>,
    },
    {
      title: intl('saenext.event-center.event-search.EventTableProps.WhetherToNotify'),
      dataIndex: 'IsNotified',
      width: 100,
      cell: (value, index, record) => <IsNotified value={value} record={record} />,
    },
    {
      title: intl('saenext.event-center.event-search.EventTableProps.Context'),
      dataIndex: 'text',
      width: 400,
      cell: (value, index, record) => <ContextColumn onClickText={onClickText} record={record} />,
    },
    {
      title: intl('saenext.event-center.event-search.EventTableProps.Operation'),
      dataIndex: 'action',
      width: 100,
      cell: (value, index, record) => (
        <Actions threshold={5}>
          <LinkButton onClick={() => handleShow(record)}>
            {intl('saenext.event-center.event-search.EventTableProps.View')}
          </LinkButton>
          <LinkButton
            disabled={typeList.find(i => i.value === record.Type)?.visible !== 0}
            onClick={() => handleSubscribe(record)}
          >
            {intl('saenext.event-center.event-search.EventTableProps.Subscription')}
          </LinkButton>
        </Actions>
      ),
    },
  ];
};

export const fetchData = async params => {
  const {
    current,
    pageSize,
    Source,
    region,
    namespaceId,
    appId,
    Type,
    Level,
    instanceId,
    timeRange = [] as any,
    timeKey,
    option,
  } = params;
  localStorage.setItem('eventSearchParams', JSON.stringify(params));
  const startTime = get(timeRange, '[0]') / 1000;
  const endTime = get(timeRange, '[1]') / 1000;
  if (timeRange.length) {
    const commonParams = {
      CurrentPage: current,
      PageSize: pageSize,
      Source,
      Type,
      Level,
      FromTime: parseInt(toString(startTime)),
      ToTime:
        timeKey === 'custom'
          ? parseInt(toString(endTime))
          : moment()
              .unix()
              .valueOf(),
      Region: region,
      RegionId: region,
    };
    let apiParams = {};
    if (!option) {
      apiParams = {
        ...commonParams,
      };
    } else {
      apiParams = {
        ...commonParams,
        NamespaceId: namespaceId,
        ResourceId: appId,
        InstanceId: instanceId,
      };
    }
    const { Data } = await services.ListEventRecord({
      ...apiParams,
    });
    return {
      data: Data.EventRecords,
      total: Data.TotalSize,
    };
  }
  return {
    data: [],
    total: 0,
  };
};
