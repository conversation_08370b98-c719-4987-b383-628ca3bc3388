import { intl , Icon } from '@ali/cnd';
import React from 'react';
import CndTable from '@ali/cnd-table';
import { fetchData, columns } from './EventTableProps';
import { Button } from '@ali/cnd';

const EventTable = ({
  refreshIndex,
  setRefreshIndex,
  typeList,
  onClickText,
  onClickLevel,
  handleShow,
  handleSubscribe,
  field,
  handleRefreshTable
}) => {
  const { getValues } = field;
  return (
    <CndTable
      fetchData={
        (async value => {
          return await fetchData({ ...value, ...getValues() });
        }) as any
      }
      primaryKey="primaryKey"
      columns={columns({ onClickText, onClickLevel, typeList, handleShow, handleSubscribe })}
      refreshIndex={refreshIndex}
      // showRefreshButton
      operation={
        <Button type="primary" onClick={() => setRefreshIndex(+new Date())}>
          {intl('saenext.event-center.event-search.EventTable.Query')}
        </Button>
      }
      secondaryOperation={
        <Button type="normal" style={{ padding: '0 8px' }} onClick={handleRefreshTable}>
          <Icon type="refresh" style={{ color: '#666' }} />
        </Button>
      }
      pagination={{
        pageSizeList: [10, 20, 50, 100],
        hideOnlyOnePage: true,
      }}
    />
  );
};
export default EventTable;
