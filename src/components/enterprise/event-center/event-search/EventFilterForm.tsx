import { intl } from '@ali/cnd';
import React, { useEffect, useCallback, useRef, useContext, useImperativeHandle, forwardRef } from 'react';
import { Select, Form } from '@ali/cnd';
import { filter, find, get } from 'lodash';
import TimeRangeSelector from '~/components/shared/TimeRangeSelector';
import { getParams } from '~/utils/global';
import FeatureContext from '~/utils/featureContext';
import moment from 'moment';
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { fixedSpan: 4 },
};

export const eventSource = [
  {
    value: 'RUNTIME',
    label: intl('saenext.event-center.event-search.EventFilterForm.RunningCurrentEvents'),
  },
  {
    value: 'CHANGE_ORDER',
    label: intl('saenext.event-center.event-search.EventFilterForm.ChangeCurrentEvents'),
  },
  {
    value: 'SYSTEM',
    label: intl('saenext.event-center.event-search.EventFilterForm.SystemEvents'),
  },
];

export const appTypeSource = [
  {
    value: 'allApps,allJobs',
    label: intl('saenext.event-center.event-search.EventFilterForm.MicroserviceApplications'),
  },
  {
    value: 'allWebV2Apps',
    label: intl('saenext.event-center.event-search.EventFilterForm.WebApplications'),
  },
  // {
  //   value: 'allJobs',
  //   label: '任务',
  // },
];

const levelEnum = [
  {
    label: 'INFO',
    value: 'INFO',
  },
  {
    label: 'WARNING',
    value: 'WARNING',
  },
  {
    label: 'CRITICAL',
    value: 'CRITICAL',
  },
];

export const filterTypeByAppType = (eventType, appType) => {
  if (eventType?.startsWith('SAE2_')) {
    return appType === 'allWebV2Apps';
    // } else if (eventType?.startsWith('SAE_JOB')) {
    //   return appType === 'allJobs';
  } else {
    return appType === 'allApps,allJobs';
  }
};

const EventFilterForm = ({ field, typeList }, ref ) => {
  const { init, getValue, setValue } = field;
  const child = useRef(null);

  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  useEffect(() => {
    const scalingFailed = getParams('scalingFailed');
    if (scalingFailed) {
      field.setValues({
        appType: 'allWebV2Apps',
        Type: 'SAE2_SCALE_UP_FAIL',
      });
    }
  }, [])

  useEffect(() => {
    if(localStorage.getItem('eventSearchParams')){
      const search = JSON.parse(localStorage.getItem('eventSearchParams'));
      field.setValues({
        Source: search?.Source,
        appType:search?.appType,
        Type:search?.Type,
        Level: search?.Level,
      })
    }
  }, []);

  useImperativeHandle(ref, () => ({
    refreshTime: (time) => {
      if(time && Array.isArray(time)){
        child.current?.field?.setValue('time',[moment(time[0]),moment(time[1])]);
        setValue('timeRange', time);
      }
    },
  }));

  const onMonitorTimeChange = async (value) => {
    const { start: newStart, end: newEnd, key } = value;
    setValue('timeRange', [newStart, newEnd]);
    setValue('timeKey', key);
  };

  const typeDataSource = useCallback(() => {
    const sourceValue = getValue('Source');
    const appTypeValue = getValue('appType');
    // 若sourceValue或appTypeValue有值，则过滤，否则不过滤
    const filterArr = filter(
      typeList,
      (v) =>
        (!sourceValue || v.source === sourceValue) &&
        (!appTypeValue || filterTypeByAppType(v.value, appTypeValue)),
    );
    return filterArr;
  }, [getValue('Source'), getValue('appType'), JSON.stringify(typeList)]);

  const levelDataSource = useCallback(() => {
    const typeValue = getValue('Type');
    if (typeValue) {
      const level = get(
        find(typeList, (i) => i.value === typeValue),
        'level',
      );
      const newLevelDataSource = filter(levelEnum, (l) => l.value === level);
      return newLevelDataSource;
    } else return levelEnum;
  }, [getValue('Type'),JSON.stringify(typeList)]);

  return (
    <div className="flex space-between item-start">
      <Form field={field} {...formItemLayout} inline>
        <FormItem label={intl('saenext.event-center.event-search.EventFilterForm.EventSource')}>
          <Select {...init('Source')} style={{ width: 220 }} dataSource={eventSource} hasClear />
        </FormItem>
        <FormItem
          label={intl('saenext.event-center.event-search.EventFilterForm.ResourceType')}
          className={EnableWebApplication ? '' : 'none'}
        >
          <Select
            {...init('appType', { initValue: 'allApps,allJobs' })}
            style={{ width: 220 }}
            dataSource={appTypeSource}
            hasClear
          />
        </FormItem>
        <FormItem label={intl('saenext.event-center.event-search.EventFilterForm.EventType')}>
          <Select {...init('Type')} style={{ width: 220 }} dataSource={typeDataSource()} hasClear />
        </FormItem>
        <FormItem label={intl('saenext.event-center.event-search.EventFilterForm.EventLevel')}>
          <Select
            {...init('Level')}
            style={{ width: 220 }}
            dataSource={levelDataSource()}
            hasClear
          />
        </FormItem>
        <FormItem>
          <TimeRangeSelector
            onRef={child}
            defaultTime={false}
            timeInitValue={'half_7_day'}
            onTimeChanged={onMonitorTimeChange}
            width={430}
          />
        </FormItem>
      </Form>
    </div>
  );
};

export default forwardRef(EventFilterForm);;
