import { intl } from '@ali/cnd';
import React, { useState, useRef, useEffect } from 'react';
import EventFilterForm from './EventFilterForm';
import ExtendFilterForm from './ExtendFilterForm';
import EventTable from './EventTable';
import { Dialog, Field, Button, Copy } from '@ali/cnd';
import { CustomEditor } from '@ali/cnd';
import RuleSlidePanel from '../subscribe-rule/RuleSlidePanel';
import moment from 'moment';

const EventSearch = ({ typeList }) => {
  const [jsonData, setJsonData] = useState('');
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [panelVisible, setPanelVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [curData, setCurData] = useState({});
  const filterRef = useRef(null);

  const field = Field.useField({
    onChange: name => {
      if (name === 'Source') {
        reset(['Type']);
      }
      if (name === 'appType') {
        reset(['Type', 'appId', 'region', 'namespaceId']);
      }
      if (name === 'Type') {
        reset(['Level']);
      }
      if (name === 'region') {
        reset(['namespaceId']);
      }
    },
  });
  const { setValue, getValue, reset } = field;

  useEffect(() => {
    const timeRange = (getValue('timeRange') || []) as [];
    if (timeRange.length) {
      setRefreshIndex(+new Date());
    }
  }, [JSON.stringify(getValue('timeRange'))]);

  const handleShow = record => {
    const json = JSON.stringify(record, null, 2);
    setVisible(true);
    setJsonData(json);
  };
  const handleSubscribe = record => {
    setPanelVisible(true);
    const res = {
      ...record,
      Source: record.Source,
      Type: record.Type,
    };
    setCurData(res);
  };

  const onClickLevel = value => {
    setValue('Level', value);
    setRefreshIndex(+new Date());
  };

  const onClickText = (value, name) => {
    setValue('option', true);
    setValue(name, value);
    setRefreshIndex(+new Date());
  };

  const handleRefreshTable = () => {
    if (field.getValues().timeKey !== 'custom' ) {
      if(field.getValues().timeRange){
      // @ts-ignore
        const [startTime, endTime] = field.getValues().timeRange;
        const diffrent = endTime - startTime;
        filterRef?.current?.refreshTime([Date.now() - diffrent,Date.now()]);
      }else{
        filterRef?.current?.refreshTime([
          moment()
            .subtract(7, 'days')
            .valueOf(),
          moment().valueOf(),
        ]);
      }
    } else {
      setRefreshIndex(Date.now())
    }
  };

  return (
    <div style={{ paddingTop: 10 }}>
      <EventFilterForm {...{ typeList, field }} ref={filterRef} />
      <ExtendFilterForm {...{ field }} />
      <EventTable
        {...{
          field,
          onClickLevel,
          onClickText,
          setRefreshIndex,
          refreshIndex,
          handleShow,
          handleSubscribe,
          typeList,
          handleRefreshTable,
        }}
      />

      <Dialog
        title={intl('saenext.event-center.event-search.EventDetails')}
        visible={visible}
        onClose={() => setVisible(false)}
        onCancel={() => setVisible(false)}
        style={{ width: 600 }}
        footer={
          <>
            <Copy
              text={jsonData}
              showIcon
              icon={
                <Button type="primary">{intl('saenext.event-center.event-search.Copy')}</Button>
              }
            ></Copy>
            <Button onClick={() => setVisible(false)}>
              {intl('saenext.event-center.event-search.Cancel')}
            </Button>
          </>
        }
      >
        <CustomEditor value={jsonData} />
      </Dialog>
      <RuleSlidePanel
        title={intl('saenext.event-center.event-search.CreateASubscriptionRule')}
        ruleType="create"
        visible={panelVisible}
        setRefreshIndex={setRefreshIndex}
        onClose={() => setPanelVisible(false)}
        curData={curData}
        typeList={typeList}
      />
    </div>
  );
};

export default EventSearch;
