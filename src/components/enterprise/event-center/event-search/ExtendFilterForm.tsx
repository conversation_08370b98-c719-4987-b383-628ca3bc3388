import { intl } from '@ali/cnd';
import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { Select, Switch, Form } from '@ali/cnd';
import { flatten, get, isEmpty, map } from 'lodash';
import { getParams } from '~/utils/global';
import services from '~/services';
import { translateSelectDataSource } from '~/utils/transfer-data';
import { useRegionNamespace } from '../custom-hooks';
import AppIdsSelect from '../AppIdsSelect';
import useRegion from '~/hooks/useRegion';
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { fixedSpan: 4 }
};

const Regions = get(window, 'ALIYUN_CONSOLE_CONFIG.REGIONS', []);
const WebRegions = get(window, 'ALIYUN_CONSOLE_GLOBAL.regions', []);

const ExtendFilterForm = ({ field }) => {
  const { init, getValue, reset, setValues } = field;
  const { nameSpacesDataSource } = useRegionNamespace({ getValue, reset });
  // const [appDataSource, setAppDataSource] = useState([]);
  const [instanceDataSource, setInstanceDataSource] = useState([]);

  const regionId = useRegion();

  // 表单appId值对应的appName、appType, 用来查询应用实例
  const appSelectedRef = useRef<any>({});

  const appType = getValue('appType')
  const regionData = useMemo(() => {
    const regionData = appType === 'allWebV2Apps' ? WebRegions : Regions;
    const regionList = map(regionData, (v) => {
      return {
        value: v.regionId || v.id,
        label: v.name
      };
    });
    return regionList;
  }, [appType])
  
  const regionInitValue = regionId || regionData[0]?.value;

  useEffect(() => {
     if(localStorage.getItem('eventSearchParams')){
       const search = JSON.parse(localStorage.getItem('eventSearchParams'));
       setValues({
        region: search?.region || regionInitValue,
        namespaceId: search?.namespaceId,
        appId: search?.appId,
        instanceId: search?.instanceId,
        option: search?.option,
       });
     }
   }, []);
  const appDisplayRender = (item) => {
    const {
      value: appId,
      label: appName,
      appType
    } = item || {};
    appSelectedRef.current = {
      appId,
      appName,
      appType
    };
    return item?.label || item.value;
  };

  const getAppList = async (namespaceId) => {
    const region = getValue('region');
    const params = {
      CurrentPage: 1,
      PageSize: 999,
      ClusterType: '8',
      EventSubmitDoQuery: 1,
      useThisRegionId: region,
      NamespaceId: namespaceId,
      FieldType: 'appName',
      FieldValue: ''
    };
    const {
      Data: { Applications }
    } = await services.ListApplicationsForPermissionAssistant({
      ...params,
      customErrorHandle(err, serviceConfig, callback) {
        if (err) {

          // setAppDataSource([]);
        }}
    });
    const dataSource = [...translateSelectDataSource(Applications, ['AppId'], 'AppName')];
    // setAppDataSource(dataSource);
  };

  const getInstanceList = ({ appId, appName, appType }) => {
    if (appType === 'SAE1') {
      getInstanceListV1(appId);
    } else if (appType === 'SAE2') {
      getInstanceListV2(appId, appName);
    }
  };

  const getInstanceListV1 = async (appId) => {
    const { Data: appDeploy } = await services.DescribeApplicationGroups({
      AppId: appId
    });
    if (!isEmpty(appDeploy)) {
      appDeploy.sort((a, b) => b.GroupType - a.GroupType);
      const instances = await Promise.all(
        map(appDeploy, (deploy) => getInstancesData(appId, deploy.GroupId))
      );
      const newInstanceData = map(flatten(instances), (v) => ({
        value: v.InstanceId,
        label: v.InstanceId
      }));
      setInstanceDataSource(newInstanceData);
    }
  };

  const getInstanceListV2 = async (appId, applicationName) => {
    const [startTime, endTime] = getValue('timeRange') || [];
    const { instances } = await services.getAppVersionInstances({
      startTime,
      endTime,
      applicationName,
      applicationID: appId
    });

    if (!isEmpty(instances)) {
      const newInstanceData = map(instances, 'instanceId');
      setInstanceDataSource(newInstanceData);
    }
  };

  const getInstancesData = async (appId, groupId) => {
    const res = await services.DescribeApplicationInstances({
      AppId: appId,
      GroupId: groupId
    });
    const instances = get(res, 'Data.Instances', []);
    return instances;
  };

  useEffect(() => {
    const {
      appId,
      appName,
      appType
    } = appSelectedRef.current || {};

    appId && getInstanceList(appSelectedRef.current);
    setInstanceDataSource([]);
    reset(['instanceId']);
  }, [getValue('appId')]);

  return (
    <Form field={field} {...formItemLayout} inline>
      <FormItem
        label={intl('saenext.event-center.event-search.ExtendFilterForm.RegionInformation')}>

        <Select
          {...init('region', { initValue: regionInitValue })}
          style={{ width: 220 }}
          placeholder={intl('saenext.event-center.event-search.ExtendFilterForm.SelectARegion')}
          dataSource={regionData}
          // hasClear
        />
      </FormItem>
      {getValue('option') &&
      <Fragment>
          <FormItem label={intl('saenext.event-center.event-search.ExtendFilterForm.Namespace')}>
            <Select
            {...init('namespaceId')}
            showSearch
            style={{ width: 220 }}
            placeholder={intl(
              'saenext.event-center.event-search.ExtendFilterForm.SelectANamespaceOrFill'
            )}
            dataSource={nameSpacesDataSource}
            hasClear
            hasArrow={false}
            onChange={(v)=>{
              field.setValue('namespaceId',v);
              reset(['appId']);
            }}
             />

          </FormItem>
          <FormItem label={intl('saenext.event-center.event-search.ExtendFilterForm.Application')}>
            <AppIdsSelect
            key={`appidselect-${getValue('appType')}`}
            {...init('appId', {
              rules: [
              {
                required: true,
                message: intl("saenext.event-center.event-search.ExtendFilterForm.SelectAnApplication")
              }]

            })}
            region={getValue('region')}
            namespaceId={getValue('namespaceId')}
            valueRender={appDisplayRender}
            appTypes={getValue('appType')?.split(',')}
            hasClear
            resultAutoWidth={false} />

          </FormItem>
          <FormItem label={intl('saenext.event-center.event-search.ExtendFilterForm.Instance')}>
            <Select
            {...init('instanceId')}
            showSearch
            style={{ width: 220 }}
            placeholder={intl(
              'saenext.event-center.event-search.ExtendFilterForm.SelectAnInstanceOrEnter'
            )}
            dataSource={instanceDataSource}
            hasClear
            hasArrow={false} />

          </FormItem>
        </Fragment>}


      <FormItem
        label={intl('saenext.event-center.event-search.ExtendFilterForm.ShowAdvancedOptions')}
        labelCol={{ fixedSpan: 5 }}>

        <Switch {...init('option', { valueName: 'checked' })} />
      </FormItem>
    </Form>);

};

export default ExtendFilterForm;