import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Form, Checkbox, Switch, Select, Balloon, Icon, Button } from '@ali/cnd';
import services from '~/services';
import Create, { EType } from '../../concats/components/Create';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import CachedData from '~/cache/common';

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    fixedSpan: 8,
  },
  wrapperCol: {
    span: 18,
  },
};

const NoticeRuleStep = ({ field }) => {
  const [contactsDatasoucre, setContactsDataSource] = useState([]);
  const { init } = field;

  const InformWay = [
    {
      value: 'dingTalk',
      label: (
        <TextWithBalloon
          text={
            <>
              {intl('saenext.event-center.subscribe-rule.NoticeRuleStep.Dingtalk')}
              <span className="text-description ml-s">
                {intl('saenext.event-center.subscribe-rule.NoticeRuleStep.Recommend')}
              </span>
            </>
          }
          tips={
            <a href={CachedData.confLink('help:sae:contacts')} target="_blank">
              {intl('saenext.event-center.subscribe-rule.NoticeRuleStep.ViewHowToObtainThe')}
            </a>
          }
        />
      ),
    },
    {
      value: 'wechat',
      label: (
        <TextWithBalloon
          text={intl('saenext.event-center.subscribe-rule.NoticeRuleStep.EnterpriseWechat')}
          tips={
            <a href={CachedData.confLink('help:sae:contacts')} target="_blank">
              {intl('saenext.event-center.subscribe-rule.NoticeRuleStep.ViewHowToObtainThe.1')}
            </a>
          }
        />
      ),
    },
    {
      value: 'feishu',
      label: (
        <TextWithBalloon
          text={intl('saenext.event-center.subscribe-rule.NoticeRuleStep.FlyingBook')}
          tips={
            <a
              href={CachedData.confLink('help:sae:lark-chatbots')}
              target="_blank"
            >
              {intl('saenext.event-center.subscribe-rule.NoticeRuleStep.ViewHowToObtainThe.2')}
            </a>
          }
        />
      ),
    },
    {
      value: 'email',
      label: intl('saenext.event-center.subscribe-rule.NoticeRuleStep.EmailAddress'),
    },
    {
      value: 'sms',
      label: (
        <span>
          {intl('saenext.event-center.subscribe-rule.NoticeRuleStep.Sms')}

          <Balloon
            trigger={
              <span
                style={{
                  cursor: 'help',
                  marginLeft: 8,
                  color: 'green',
                }}
              >
                <Icon
                  type="help"
                  size="xs"
                  onClick={() => window.open(CachedData.confLink('help:arms:billing-description'))}
                />
              </span>
            }
            closable={false}
          >
            {intl(
              'saenext.event-center.subscribe-rule.NoticeRuleStep.ThereAreFreeSmsNotifications',
            )}
          </Balloon>
        </span>
      ),
    },
  ];

  const getContactsList = async () => {
    const {
      Data: { ContactsInfo = [] },
    } = await services.ListContacts({
      CurrentPage: 1,
      PageSize: 100,
    });
    const res = ContactsInfo.map((i) => {
      return {
        label: i.Name,
        value: i.Uid,
      };
    });
    setContactsDataSource(res);
  };

  useEffect(() => {
    getContactsList();
  }, []);

  return (
    <Form
      field={field}
      style={{
        width: '100%',
        paddingTop: 12,
      }}
      {...formItemLayout}
      labelTextAlign="right"
    >
      <FormItem
        label={intl('saenext.event-center.subscribe-rule.NoticeRuleStep.NotificationMethod')}
        required
      >
        <Checkbox.Group
          {...init('way', {
            initValue: ['dingTalk'],
            rules: [
              {
                required: true,
                message: intl(
                  'saenext.event-center.subscribe-rule.NoticeRuleStep.SelectANotificationMethod',
                ),
              },
            ],
          })}
          dataSource={InformWay}
        />
      </FormItem>
      <FormItem label={intl('saenext.event-center.subscribe-rule.NoticeRuleStep.Contact')} required>
        <Select
          {...init('contact', {
            rules: [
              {
                required: true,
                message: intl(
                  'saenext.event-center.subscribe-rule.NoticeRuleStep.PleaseSelectAContact',
                ),
              },
            ],
          })}
          style={{
            minWidth: 300,
          }}
          dataSource={contactsDatasoucre}
          mode="multiple"
          useDetailValue
        />

        <Icon
          type="refresh"
          size="xs"
          style={{
            margin: '0 8px',
            cursor: 'pointer',
          }}
          onClick={() => {
            getContactsList();
          }}
        />

        <Create type={EType.create} width="medium" callback={getContactsList}>
          <Button type="primary" text>
            {intl('saenext.event-center.subscribe-rule.NoticeRuleStep.CreateAContact')}
          </Button>
        </Create>
      </FormItem>
      <FormItem
        label={intl(
          'saenext.event-center.subscribe-rule.NoticeRuleStep.WhetherToEnableSubscriptionRules',
        )}
      >
        <Switch
          {...init('switchState', {
            valueName: 'checked',
          })}
        />
      </FormItem>
    </Form>
  );
};

export default NoticeRuleStep;
