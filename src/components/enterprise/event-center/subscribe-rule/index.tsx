import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Message } from '@ali/cnd';
import RuleSlidePanel from './RuleSlidePanel';
import services from '~/services';
import { jsonStringify } from '~/utils/transfer-data';
import { get } from 'lodash';
import SubscribeTable from './SubscribeTable';

const ruleTypeTitles = {
  create: intl('saenext.event-center.subscribe-rule.CreateASubscriptionRule'),
  edit: intl('saenext.event-center.subscribe-rule.EditSubscriptionRules'),
  clone: intl('saenext.event-center.subscribe-rule.CloneSubscriptionRules'),
};

const SubscribeRule = props => {
  const { typeList } = props;
  const [visible, setVisible] = useState(false);
  const [ruleType, setRuleType] = useState('create');
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [curData, setCurData] = useState({});

  const cloneRule = async record => {
    setRuleType('clone');
    const obj = {
      ...record,
      Name: `${record.Name}-copy`,
      Region: get(record.NamespaceId.split(':'), '[0]', record.NamespaceId),
    };
    setCurData(obj);
    setVisible(true);
  };
  const editRule = async record => {
    setRuleType('edit');
    const obj = {
      ...record,
      Region: get(record.NamespaceId.split(':'), '[0]', record.NamespaceId),
    };
    setCurData(obj);
    setVisible(true);
  };
  const createRule = () => {
    setVisible(true);
    setCurData({});
    setRuleType('create');
  };

  const deleteRule = async record => {
    const { Success } = await services.DeleteEventSubscribeRule({
      RuleId: record.RuleId,
    });
    if (Success) {
      Message.success(intl('saenext.event-center.subscribe-rule.DeletedSuccessfully'));
      setRefreshIndex(+new Date());
    } else {
      Message.success(intl('saenext.event-center.subscribe-rule.FailedToDelete'));
    }
  };
  const updateRule = async (record, action) => {
    let tip = '';
    const {
      Name,
      NamespaceId,
      NotifyInfo: { NotifiedAccounts, NotifyChannels },
      ResourceDesc: { IsAllApps, IsAllJobs, Resources },
      RuleId,
      SilentPeriod,
      SilentThreshold,
      Source,
      State,
      Type,
    } = record;
    const params = {
      ruleId: action !== 'clone' ? RuleId : null,
      name: Name,
      type: Type,
      source: Source,
      state: State,
      silentPeriod: SilentPeriod,
      silentThreshold: SilentThreshold,
      resourceDesc: {
        isAllApps: IsAllApps,
        isAllJobs: IsAllJobs,
        resources: Resources,
      },
      namespaceId: NamespaceId,
      notifyInfo: {
        notifyChannels: NotifyChannels,
        notifiedAccounts: NotifiedAccounts,
      },
    };
    switch (action) {
      case 'stop':
        params.state = -1;
        tip = intl('saenext.event-center.subscribe-rule.Disable');
        break;
      case 'start':
        params.state = 0;
        tip = intl('saenext.event-center.subscribe-rule.Enable');
        break;
      case 'clone':
        params.name = `${params.name}-copy`;
        tip = intl('saenext.event-center.subscribe-rule.Clone');
        break;
      default:
        break;
    }
    let success;
    if (action !== 'clone') {
      const { Success } = await services.UpdateEventSubscribeRule({
        Subscribe: jsonStringify(params),
      });
      success = Success;
    } else {
      const { Success } = await services.CreateEventSubscribeRule({
        Subscribe: jsonStringify(params),
      });
      success = Success;
    }
    if (success) {
      Message.success(intl('saenext.event-center.subscribe-rule.TipSucceeded', { tip: tip }));
      setRefreshIndex(+new Date());
    }
  };

  return (
    <div style={{ paddingTop: 12 }}>
      <SubscribeTable
        {...{
          cloneRule,
          editRule,
          createRule,
          updateRule,
          deleteRule,
          typeList,
          refreshIndex,
          setRefreshIndex,
        }}
      />

      <RuleSlidePanel
        title={ruleTypeTitles[ruleType]}
        ruleType={ruleType}
        visible={visible}
        onClose={() => setVisible(false)}
        setRefreshIndex={setRefreshIndex}
        typeList={typeList}
        curData={curData}
      />
    </div>
  );
};
export default SubscribeRule;
