import { intl } from '@ali/cnd';
import React, { useEffect, useCallback, useMemo, useContext } from 'react';
import { Form, Grid, Select, Input, Balloon, Icon } from '@ali/cnd';
import { filter, find, get, map } from 'lodash';
import { appTypeSource, eventSource, filterTypeByAppType } from '../event-search/EventFilterForm';
import { useRegionNamespace } from '../custom-hooks';
import AppIdsSelect from '../AppIdsSelect';
import FeatureContext from '~/utils/featureContext';

const FormItem = Form.Item;
const Row = Grid.Row;
const Col = Grid.Col;

const formItemLayout = {
  labelCol: {
    fixedSpan: 8
  },
  wrapperCol: {
    span: 18
  }
};

const levelStyles = {
  WARNING: { color: '#ed5e5e' },
  INFO: { color: '#629962' },
  CRITICAL: { color: 'red' }
};


const Regions = get(window, 'ALIYUN_CONSOLE_CONFIG.REGIONS', []);
const WebRegions = get(window, 'ALIYUN_CONSOLE_GLOBAL.regions', []);

const EventRuleStep = ({ typeList, field }) => {
  const { init, setValue, getValue } = field;
  const appIdsSelectRef = React.useRef(null);
  
  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  const appType = getValue('appType')
  const regionData = useMemo(() => {
    const regionData = appType === 'allWebV2Apps' ? WebRegions : Regions;
    const regionList = map(regionData, (v) => {
      return {
        value: v.regionId || v.id,
        label: v.name
      };
    });
    return regionList;
  }, [appType])

  const { nameSpacesDataSource } = useRegionNamespace({
    getValue
  });

  const filterEvent = filter(eventSource, (event) =>
  find(typeList, (type) => type.source === event.value && type.visible === 0)
  );

  const filterTypeList = useCallback(() => {
    const source = getValue('source');
    const appType = getValue('appType');
    const filterArr = filter(typeList, (v) =>
    v.visible === 0 &&
    v.source === source &&
    filterTypeByAppType(v.value, appType)
    );
    return filterArr;
  }, [getValue('source'), getValue('appType')]);

  const typeItemRender = (item) => {
    if (!item.label) {
      return item.value;
    }
    return (
      <div>
        {item.label}
        <Balloon
          trigger={<Icon type="help" size="xs" style={{ color: 'green', marginLeft: 8 }} />}
          closable={false}>

          {item.description}
        </Balloon>
        <span
          style={{
            marginLeft: 12,
            fontStyle: 'oblique',
            display: 'inline-block',
            transform: 'scale(0.85)',
            color: '#333'
          }}>

          {intl('saenext.event-center.subscribe-rule.EventRuleStep.Grade')}

          <span style={levelStyles[item.level]}>{item.level}</span>
        </span>
      </div>);

  };

  useEffect(() => {
    const type = getValue('type');
    if (type === 'SAE_WAIT_BATCH_CONFIRM_EVENT') {
      setValue('silentPeriod', 30);
      setValue('timeType', 'm');
    }
  }, [getValue('type')]);

  return (
    <Form field={field} style={{ width: '100%', paddingTop: 12 }} {...formItemLayout}>
      <FormItem label={intl('saenext.event-center.subscribe-rule.EventRuleStep.RuleName')} required>
        <Input
          {...init('name', {
            rules: [
            {
              required: true,
              message: intl('saenext.event-center.subscribe-rule.EventRuleStep.EnterARuleName')
            }]

          })}
          maxLength={64} />

      </FormItem>
      <FormItem
        label={intl('saenext.event-center.subscribe-rule.EventRuleStep.EventSource')}
        required>

        <Select
          {...init('source', {
            rules: [
            {
              required: true,
              message: intl(
                'saenext.event-center.subscribe-rule.EventRuleStep.SelectAnEventSource'
              )
            }]

          })}
          style={{ width: '100%' }}
          dataSource={filterEvent} />

      </FormItem>
      <FormItem
        label={intl("saenext.event-center.subscribe-rule.EventRuleStep.ResourceType")}
        required
        className={ EnableWebApplication ? '' : 'none' }
      >
        <Select
          {...init('appType', {
            initValue: 'allApps,allJobs',
            rules: [
            {
              required: true,
              message: intl("saenext.event-center.subscribe-rule.EventRuleStep.SelectAResourceType")
            }]

          })}
          style={{ width: '100%' }}
          dataSource={appTypeSource} />

      </FormItem>
      
      <FormItem
        label={intl('saenext.event-center.subscribe-rule.EventRuleStep.EventType')}
        required>

        <Select
          {...init('type', {
            rules: [
            {
              required: true,
              message: intl(
                'saenext.event-center.subscribe-rule.EventRuleStep.SelectAnEventType'
              )
            }]

          })}
          style={{ width: '100%' }}
          dataSource={filterTypeList()}
          itemRender={typeItemRender}
          valueRender={typeItemRender} />

      </FormItem>
      <FormItem
        label={intl(
          'saenext.event-center.subscribe-rule.EventRuleStep.EventNotificationEffectiveResource'
        )}
        required>

        <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between' }}>
          <FormItem required>
            <Select
              {...init('region', {
                rules: [
                {
                  required: true,
                  message: intl(
                    'saenext.event-center.subscribe-rule.EventRuleStep.SelectARegion'
                  )
                }]

              })}
              placeholder={intl('saenext.event-center.subscribe-rule.EventRuleStep.SelectARegion')}
              style={{ width: 300, marginRight: 8 }}
              dataSource={regionData} />

          </FormItem>
          <FormItem required>
            <Select
              {...init('namespaceId', {
                rules: [
                {
                  required: true,
                  message: intl(
                    'saenext.event-center.subscribe-rule.EventRuleStep.SelectANamespace'
                  )
                }]

              })}
              placeholder={intl(
                'saenext.event-center.subscribe-rule.EventRuleStep.SelectANamespace'
              )}
              style={{ width: 300 }}
              dataSource={nameSpacesDataSource} />

          </FormItem>
          <FormItem required>
            <AppIdsSelect
              key={`appidselect-${getValue('appType')}`}
              {...init('appjob', {
                rules: [
                {
                  required: true,
                  message: intl(
                    'saenext.event-center.subscribe-rule.EventRuleStep.SelectAnApplicationOrTask'
                  )
                }]

              })}
              region={getValue('region')}
              namespaceId={getValue('namespaceId')}
              ref={appIdsSelectRef}
              appTypes={getValue('appType')?.split(',')}
              appUseDetailValue
              multiple
              hasClear
              resultAutoWidth={false}
              style={{ width: 620 }} />

          </FormItem>
        </div>
      </FormItem>
      <FormItem
        label={
        <div>
            <span>{intl('saenext.event-center.subscribe-rule.EventRuleStep.CheckCycle')}</span>
            <Balloon
            trigger={
            <span style={{ cursor: 'help', marginLeft: 8, color: 'green' }}>
                  <Icon type="help" size="xs" />
                </span>}

            closable={false}>

              {intl('saenext.event-center.subscribe-rule.EventRuleStep.ThatIsIfTheEvent')}
            </Balloon>
          </div>}>


        <Row gutter="2">
          <Col span="8">
            <Input
              disabled
              value={intl('saenext.event-center.subscribe-rule.EventRuleStep.FixedInterval')} />

          </Col>
          <Col span="8">
            <FormItem>
              <Input
                {...init('silentPeriod', {
                  initValue: 30
                })}
                disabled={getValue('type') === 'SAE_WAIT_BATCH_CONFIRM_EVENT'}
                style={{ width: 200 }} />

            </FormItem>
          </Col>
          <Col span="8">
            <FormItem>
              <Select
                {...init('timeType', {
                  initValue: 's'
                })}
                disabled={getValue('type') === 'SAE_WAIT_BATCH_CONFIRM_EVENT'}
                style={{ width: 200 }}
                dataSource={[
                {
                  label: intl('saenext.event-center.subscribe-rule.EventRuleStep.Seconds'),
                  value: 's'
                },
                {
                  label: intl('saenext.event-center.subscribe-rule.EventRuleStep.Minutes'),
                  value: 'm'
                }]} />


            </FormItem>
          </Col>
        </Row>
      </FormItem>
      <FormItem
        label={intl(
          'saenext.event-center.subscribe-rule.EventRuleStep.TriggerNotificationThreshold'
        )}>

        <Input {...init('silentThreshold', { initValue: 1 })} />
      </FormItem>
    </Form>);

};
export default EventRuleStep;