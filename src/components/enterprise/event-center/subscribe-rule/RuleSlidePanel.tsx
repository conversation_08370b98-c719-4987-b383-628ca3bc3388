import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Step, Field, Button, SlidePanel, Message } from '@ali/cnd';
import ComShow from '~/components/shared/ComShow';
import services from '~/services';
import EventRuleStep from './EventRuleStep';
import NoticeRuleStep from './NoticeRuleStep';
import { get, map, find, filter, isEmpty, includes } from 'lodash';
import { jsonStringify } from '~/utils/transfer-data';

const RuleSlidePanel = ({
  visible,
  onClose,
  setRefreshIndex,
  typeList,
  curData,
  title,
  ruleType = 'create',
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);

  const field = Field.useField({
    onChange: name => {
      if (name === 'source') {
        reset(['type']);
      }
      if (name === 'appType') {
        reset(['type', 'appjob', 'region', 'namespaceId']);
      }
      if (name === 'region') {
        reset(['namespaceId', 'appjob']);
      }
      if (name === 'namespaceId') {
        reset(['appjob']);
      }
    },
  });
  const { setValues, reset, validate } = field;

  const renderFooter = () => {
    if (currentStep === 0) {
      return (
        <div>
          <Button type="primary" style={{ marginRight: 8 }} onClick={goNext}>
            {intl('saenext.event-center.subscribe-rule.RuleSlidePanel.NextStep')}
          </Button>
          <Button onClick={onClose}>
            {intl('saenext.event-center.subscribe-rule.RuleSlidePanel.Cancel')}
          </Button>
        </div>
      );
    } else {
      return (
        <div>
          <Button type="primary" style={{ marginRight: 8 }} onClick={goPrev}>
            {intl('saenext.event-center.subscribe-rule.RuleSlidePanel.PreviousStep')}
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={loading}>
            {intl('saenext.event-center.subscribe-rule.RuleSlidePanel.Submit')}
          </Button>
          <Button onClick={onClose} style={{ marginLeft: 8 }}>
            {intl('saenext.event-center.subscribe-rule.RuleSlidePanel.Cancel')}
          </Button>
        </div>
      );
    }
  };

  const goNext = () => {
    validate(['name', 'source', 'type', 'region', 'namespaceId', 'appjob'], errors => {
      if (!errors) {
        setCurrentStep(1);
      }
    });
  };

  const goPrev = () => {
    setCurrentStep(0);
  };

  const handleSubmit = async () => {
    validate((errors, values) => {
      if (!errors) {
        setLoading(true);
        const {
          name,
          type,
          source,
          namespaceId,
          silentPeriod,
          silentThreshold,
          appjob,
          contact,
          way,
          switchState,
          timeType,
        } = values as any;
        const contactArr = contact.map(i => {
          return { name: i.label, uid: i.value };
        });
        const threshold = timeType === 's' ? silentPeriod : silentPeriod * 60;
        const isAll = !isEmpty(find(appjob, i => i.value === 'all'));
        const isAllApps = !isEmpty(find(appjob, i => i.value === 'allApps'));
        const isAllJobs = !isEmpty(find(appjob, i => i.value === 'allJobs'));
        const resourceData = {
          isAllApps: isAll || isAllApps,
          isAllJobs: isAll || isAllJobs,
          resources: map(
            filter(appjob, i => !includes(['allApps', 'allJobs', 'all'], i.value)),
            v => {
              return {
                type: v.type,
                resourceId: v.value,
                resourceName: v.label,
              };
            },
          ),
        };
        const params = {
          name,
          type,
          source,
          state: switchState ? 0 : -1,
          silentPeriod: threshold,
          silentThreshold,
          resourceDesc: resourceData,
          namespaceId,
          notifyInfo: {
            notifyChannels: way,
            notifiedAccounts: contactArr,
          },
          ruleId: get(curData, 'RuleId'),
        };
        if (ruleType === 'edit') {
          editRule(params);
        } else {
          createRule(params);
        }
      }
    });
  };

  const createRule = async params => {
    const { Success } = await services.CreateEventSubscribeRule({
      Subscribe: jsonStringify(params),
    }) || {};
    setLoading(false);
    if (Success) {
      Message.success(intl('saenext.event-center.subscribe-rule.RuleSlidePanel.New'));
      onClose();
      setRefreshIndex(+new Date());
    }
  };
  const editRule = async params => {
    const { Success } = await services.UpdateEventSubscribeRule({
      Subscribe: jsonStringify(params),
    });
    if (Success) {
      Message.success(
        intl('saenext.event-center.subscribe-rule.RuleSlidePanel.EditedSuccessfully'),
      );
      setLoading(false);
      onClose();
      setRefreshIndex(+new Date());
    }
  };

  const getAppTypeByEventType = (type) => {
    if (!type) return;
    if (type.startsWith('SAE2_')) {
      return 'allWebV2Apps';
    // } else if (type.startsWith('SAE_JOB')) {
    //   return 'allJobs';
    } else {
      return 'allApps,allJobs';
    }
  }

  useEffect(() => {
    if (!visible) {
      setCurrentStep(0);
    }
  }, [visible]);

  useEffect(() => {
    if (!isEmpty(curData)) {
      const {
        Name = '',
        Source = '',
        Type = '',
        NamespaceId = '',
        State = 0,
        SilentPeriod = 30,
        SilentThreshold = 1,
        NotifyInfo = {},
        ResourceDesc = {},
        ResourceId = '',
      } = curData;
      let period = SilentPeriod;
      const { NotifyChannels, NotifiedAccounts = [] } = NotifyInfo;
      const { IsAllApps, IsAllJobs, Resources = [] } = ResourceDesc;
      const contacts = NotifiedAccounts.map(i => {
        return { value: i.Uid, label: i.Name };
      });
      const regionSource = NamespaceId?.split(':');
      const appsAndJobsIdsDefault = () => {
        if (ResourceId) {
          return [{ value: ResourceId}];
        } else {
          const selected = [];
          if (IsAllApps) {
            selected.push({ value: 'allApps' });
          }
          if (IsAllJobs) {
            selected.push({ value: 'allJobs' });
          }
          const resources = map(Resources, item => ({ value: item.ResourceId }));
          selected.push(...resources);
          return selected;
        }
      };
      let timeType = 's';
      if (SilentPeriod % 60 === 0) {
        timeType = 'm';
        period = SilentPeriod / 60;
      }
      setValues({
        name: Name,
        source: Source,
        type: Type,
        appType: getAppTypeByEventType(Type),
        region: get(regionSource, '[0]'),
        namespaceId: NamespaceId,
        appjob: appsAndJobsIdsDefault(),
        silentPeriod: period,
        timeType,
        silentThreshold: SilentThreshold,
        way: NotifyChannels || ['dingTalk'],
        contact: contacts,
        switchState: State === 0,
      });
    }
  }, [curData]);

  return (
    <SlidePanel
      title={title}
      width="large"
      isShowing={visible}
      onClose={onClose}
      customFooter={renderFooter()}
      onMaskClick={onClose}
    >
      <Step current={currentStep}>
        <Step.Item
          key={0}
          title={intl('saenext.event-center.subscribe-rule.RuleSlidePanel.SelectEventMode')}
        />
        <Step.Item
          key={1}
          title={intl(
            'saenext.event-center.subscribe-rule.RuleSlidePanel.ConfigureNotificationObjects',
          )}
        />
      </Step>
      <ComShow if={currentStep === 0}>
        <EventRuleStep typeList={typeList} field={field} />
      </ComShow>
      <ComShow if={currentStep === 1}>
        <NoticeRuleStep field={field} />
      </ComShow>
    </SlidePanel>
  );
};

export default RuleSlidePanel;
