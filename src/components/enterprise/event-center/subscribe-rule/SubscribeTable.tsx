import { intl } from '@ali/cnd';
import React, { useCallback } from 'react';
import { columns, fetchData } from './SubscribeTableProps';
import CndTable from '@ali/cnd-table';
import { Button, Form, Field, Select, Dialog } from '@ali/cnd';
import { eventSource } from '../event-search/EventFilterForm';
import { filter } from 'lodash';
const FormItem = Form.Item;

const SubscribeTable = ({
  typeList,
  refreshIndex,
  setRefreshIndex,
  updateRule,
  cloneRule,
  editRule,
  deleteRule,
  createRule,
}) => {
  const field = Field.useField({
    onChange() {
      setRefreshIndex(+new Date());
    },
  });
  const { init, getValue, getValues } = field;

  const typeDataSource = useCallback(() => {
    const sourceValue = getValue('Source');
    if (sourceValue) {
      const filterArr = filter(typeList, v => v.source === sourceValue);
      return filterArr;
    } else return typeList;
  }, [getValue('Source'), JSON.stringify(typeList)]);

  const handleStop = record => {
    Dialog.confirm({
      title: intl('saenext.event-center.subscribe-rule.SubscribeTable.DisableSubscriptionRules'),
      content: intl(
        'saenext.event-center.subscribe-rule.SubscribeTable.AfterYouDisableASubscription',
      ),
      onOk: () => updateRule(record, 'stop'),
    });
  };
  const handleStart = record => {
    Dialog.confirm({
      title: intl('saenext.event-center.subscribe-rule.SubscribeTable.EnableSubscriptionRules'),
      content: intl(
        'saenext.event-center.subscribe-rule.SubscribeTable.AfterYouEnableSubscriptionRules',
      ),
      onOk: () => updateRule(record, 'start'),
    });
  };
  const handleDelete = record => {
    Dialog.confirm({
      title: intl('saenext.event-center.subscribe-rule.SubscribeTable.Prompt'),
      content: intl('saenext.event-center.subscribe-rule.SubscribeTable.AreYouSureYouWant'),
      onOk: () => deleteRule(record),
    });
  };

  return (
    <CndTable
      fetchData={
        (async value => {
          return await fetchData({ ...value, ...getValues() });
        }) as any
      }
      primaryKey="primaryKey"
      columns={columns({ typeList, cloneRule, editRule, handleStop, handleStart, handleDelete })}
      refreshIndex={refreshIndex}
      showRefreshButton
      operation={
        <div style={{ display: 'flex' }}>
          <Button type="primary" className="mr-8" onClick={createRule}>
            {intl('saenext.event-center.subscribe-rule.SubscribeTable.CreateASubscriptionRule')}
          </Button>
          <Form field={field} inline>
            <FormItem label="" style={{ marginBottom: 0, marginRight: 8 }}>
              <Select
                {...init('Source')}
                style={{ width: 220 }}
                placeholder={intl(
                  'saenext.event-center.subscribe-rule.SubscribeTable.SelectAnEventSource',
                )}
                dataSource={eventSource}
                hasClear
              />
            </FormItem>
            <FormItem label="" style={{ marginBottom: 0 }}>
              <Select
                {...init('Type')}
                placeholder={intl(
                  'saenext.event-center.subscribe-rule.SubscribeTable.SelectAnEventType',
                )}
                style={{ width: 220 }}
                dataSource={typeDataSource()}
                hasClear
              />
            </FormItem>
          </Form>
        </div>
      }
      pagination={{
        pageSizeList: [10, 20, 50, 100],
        hideOnlyOnePage: true,
      }}
    />
  );
};

export default SubscribeTable;
