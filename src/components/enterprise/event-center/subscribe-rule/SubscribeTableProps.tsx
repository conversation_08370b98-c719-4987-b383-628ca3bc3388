import { intl } from '@ali/cnd';
import React from 'react';
import { Balloon, Actions, LinkButton, StatusIndicator, Truncate, Tag } from '@ali/cnd';
import { get } from 'lodash';
import CachedData from '~/cache/common';
import { eventSource } from '../event-search/EventFilterForm';
import moment from 'moment';
import services from '~/services';

const AccountUid = get(window, 'ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK');

export const columns = ({
  typeList,
  cloneRule,
  editRule,
  handleStop,
  handleStart,
  handleDelete,
}) => {
  const isMainAccount = CachedData.isMainAccount();

  return [
    {
      title: intl('saenext.event-center.subscribe-rule.SubscribeTableProps.RuleName'),
      dataIndex: 'Name',
      cell: (value, index, record) => {
        if (isMainAccount && record.Uid !== AccountUid) {
          return (
            <div>
              <span>{value}</span>
              <Balloon
                trigger={
                  <Tag color="green" style={{ marginLeft: 20 }}>
                    {intl('saenext.event-center.subscribe-rule.SubscribeTableProps.RamUser')}
                  </Tag>
                }
                closable={false}
              >
                <div>Uid : {record.Uid}</div>
              </Balloon>
            </div>
          );
        } else {
          return <span>{value}</span>;
        }
      },
    },
    {
      title: intl('saenext.event-center.subscribe-rule.SubscribeTableProps.RuleStatus'),
      dataIndex: 'State',
      cell: value => {
        return value === 0 ? (
          <StatusIndicator type="success" shape="dot">
            {intl('saenext.event-center.subscribe-rule.SubscribeTableProps.Enabled')}
          </StatusIndicator>
        ) : (
          <StatusIndicator type="disabled" shape="dot">
            {intl('saenext.event-center.subscribe-rule.SubscribeTableProps.Stopped')}
          </StatusIndicator>
        );
      },
    },
    {
      title: intl('saenext.event-center.subscribe-rule.SubscribeTableProps.EventSource'),
      dataIndex: 'Source',
      cell: value => {
        const sourceLabel = eventSource.find(i => i.value === value).label || value;
        return <span>{sourceLabel}</span>;
      },
    },
    {
      title: intl('saenext.event-center.subscribe-rule.SubscribeTableProps.EventType'),
      dataIndex: 'Type',
      cell: value => {
        const typeRecord = typeList.find(i => i.value === value);
        const typeLabel = get(typeRecord, 'label', '');
        return <span>{typeLabel}</span>;
      },
    },
    {
      title: intl(
        'saenext.event-center.subscribe-rule.SubscribeTableProps.EventNotificationEffectiveResource',
      ),
      dataIndex: 'podName',
      width: 150,
      cell: (value, index, record) => {
        const {
          NamespaceId,
          ResourceDesc: { IsAllApps, IsAllJobs, Resources = [] },
        } = record;

        // if (IsAllApps) {
        //   return (
        //     <Truncate value={150} type="width">
        //       {NamespaceId}:全部应用
        //     </Truncate>
        //   );
        // } else {
        const appNameList = Resources.map(i => {
          return i.ResourceName;
        });
        if (IsAllApps) {
          appNameList.unshift(
            intl('saenext.event-center.subscribe-rule.SubscribeTableProps.AllApplications'),
          );
        }
        if (IsAllJobs) {
          appNameList.unshift(
            intl('saenext.event-center.subscribe-rule.SubscribeTableProps.AllTasks'),
          );
        }
        const appShow = appNameList.join(',');
        return (
          <Truncate value={150} type="width">
            {NamespaceId}:{appShow}
          </Truncate>
        );

        // }
      },
    },
    {
      title: intl('saenext.event-center.subscribe-rule.SubscribeTableProps.CreationTime'),
      dataIndex: 'CreateTime',
      cell: value => moment(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: intl('saenext.event-center.subscribe-rule.SubscribeTableProps.TheLastTimeTheRule'),
      dataIndex: 'StopTime',
      cell: value => {
        return value === 0 ? '--' : moment(value).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: intl('saenext.event-center.subscribe-rule.SubscribeTableProps.Operation'),
      dataIndex: 'podName',
      cell: (value, index, record) => (
        <Actions threshold={5}>
          <LinkButton onClick={() => cloneRule(record)}>
            {intl('saenext.event-center.subscribe-rule.SubscribeTableProps.Clone')}
          </LinkButton>
          <LinkButton onClick={() => editRule(record)}>
            {intl('saenext.event-center.subscribe-rule.SubscribeTableProps.Edit')}
          </LinkButton>
          {record.State === 0 ? (
            <LinkButton
              onClick={() => {
                handleStop(record);
              }}
            >
              {intl('saenext.event-center.subscribe-rule.SubscribeTableProps.Disable')}
            </LinkButton>
          ) : (
            <LinkButton
              onClick={() => {
                handleStart(record);
              }}
            >
              {intl('saenext.event-center.subscribe-rule.SubscribeTableProps.Enable')}
            </LinkButton>
          )}

          <LinkButton
            onClick={() => {
              handleDelete(record);
            }}
          >
            {intl('saenext.event-center.subscribe-rule.SubscribeTableProps.Delete')}
          </LinkButton>
        </Actions>
      ),
    },
  ];
};

export const fetchData = async params => {
  const { current, pageSize, Type, Source } = params;
  const { Data } = await services.ListEventSubscribeRule({
    CurrentPage: current,
    PageSize: pageSize,
    Type,
    Source,
  });
  return {
    data: Data.EventSubscribeRules,
    total: Data.TotalSize,
  };
};
