import { intl, Tab } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import EventSearch from './event-search';
import SubscribeRule from './subscribe-rule';
import services from '~/services';
import ConcatsTable from '~/components/enterprise/concats';
const TabItem = Tab.Item;

const EventCenter = () => {
  const [typeList, setTypeList] = useState([]);

  const getEventTypeList = async () => {
    let Data = [];
    const data = await services.ListEventType({
      customErrorHandle(err, serviceConfig, callback) {
        if (err && err.data?.length > 0) {
          Data = err.data;
        }
      },
    });
    if (data) {
      Data = data.Data;
    }
    const res = Data.map(i => {
      return {
        value: i.Type,
        label: i.Title,
        level: i.Level,
        description: i.Description,
        source: i.Source,
        visible: i.Visible,
      };
    });
    setTypeList(res);
  };
  useEffect(() => {
    getEventTypeList();
  }, []);

  return (
    <Tab defaultActiveKey="search" shape="wrapped" unmountInactiveTabs>
      <TabItem title={intl('saenext.enterprise.event-center.EventQuery')} key="search">
        <EventSearch typeList={typeList} />
      </TabItem>
      <TabItem title={intl('saenext.enterprise.event-center.SubscriptionRules')} key="rule">
        <SubscribeRule typeList={typeList} />
      </TabItem>
      <TabItem title={intl('saenext.enterprise.concats.ContactManagement')} key="concats">
        <div style={{ paddingTop: 12 }}>
          <ConcatsTable />
        </div>
      </TabItem>
    </Tab>
  );
};

export default EventCenter;
