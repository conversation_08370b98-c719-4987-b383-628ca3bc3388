import { intl } from '@ali/cnd';
import { useEffect, useState } from 'react';
import { filter, find, get, includes, map, noop } from 'lodash';
import services from '~/services';
import { translateSelectDataSource } from '~/utils/transfer-data';

export const useRegionNamespace = options => {
  const { getValue, allowAllNamespaces } = options;
  const [nameSpacesDataSource, setNameSpacesDataSource] = useState([]);

  const getNamespacesList = async regionId => {
    const { Data } = await services.ListNamespacesForPermissionAssistant({
      useThisRegionId: regionId,
      RegionId: regionId,
      customErrorHandle: (err, serviceConfig, callback) => {
        if (err) {
          setNameSpacesDataSource([]);
        }
      },
    });
    const _dataSource = translateSelectDataSource(
      Data,
      ['NamespaceId', 'RegionId'],
      'NamespaceName',
    );
    const _dataDefault = map(_dataSource, v => {
      if (v.NamespaceId.indexOf(':') === -1) {
        v.label = intl('saenext.enterprise.event-center.custom-hooks.Default');
      }
      return v;
    });
    if (allowAllNamespaces) {
      _dataDefault.unshift({
        label: intl('saenext.components.shared.NamespaceTag.AllNamespaces'),
        value: `${regionId}:*`,
      });
    }
    setNameSpacesDataSource(_dataDefault);
  };

  useEffect(() => {
    const region = getValue('region');
    if (region === '*') {
      const data = [{
        label: intl('saenext.components.shared.NamespaceTag.AllNamespaces'),
        value: '*',
      }];
      setNameSpacesDataSource(data);
      return;
    }
    region && getNamespacesList(region);
    setNameSpacesDataSource([]);
  }, [getValue('region'), allowAllNamespaces]);

  return { nameSpacesDataSource };
};

export const useAppIds = options => {
  const {
    region,
    namespaceId,
    appTypes = ['allMicroApps', 'allWebV2Apps', 'allJobs'],
    multiple,
  } = options;
  const [cascadeDataSource, setCascadeDataSource] = useState([]);
  const [appDataSource, setAppDataSource] = useState([]);
  const [microAppDataSource, setMicroAppDataSource] = useState([]);
  const [webV2AppDataSource, setWebV2AppDataSource] = useState([]);
  const [jobsDataSource, setJobsDataSource] = useState([]);

  // 获取全部应用（旧版接口）
  const getAppList = async namespaceId => {
    const params = {
      CurrentPage: 1,
      PageSize: 999,
      NamespaceId: namespaceId,
    };
    const {
      Data: { Applications },
    } = await services.ListApplicationsForPermissionAssistant({
      ...params,
    }, {
      region,
    });
    const data = Applications.map(i => {
      return { value: i.AppId, label: i.AppName, type: 'App' };
    });
    setAppDataSource(data);
  };

  // 获取微应用 列表
  const getMicroAppList = async namespaceId => {
    const params = {
      CurrentPage: 1,
      PageSize: 999,
      NamespaceId: namespaceId,
    };
    const {
      Data: { Applications },
    } = await services.ListApplicationsForPermissionAssistant({
      ...params,
    }, {
      region,
    });

    const data = Applications.map(i => {
      return { value: i.AppId, label: i.AppName, type: 'App', appType: 'SAE1' };
    });
    setMicroAppDataSource(data);
  };

  // 获取web应用 仅在请求分配CPU 列表
  const getWebV2AppList = async namespaceId => {
    const params = {
      limit: 100,
      namespaceID: namespaceId,
      nextToken: '',
    };
    const { Data } = await services.listWebApplicationsForPermissionAssistant(
      {
        ...params,
      },
      {
        region,
      },
    );

    const data = Data.map(i => {
      return { value: i.AppId, label: i.AppName, type: 'App', appType: 'SAE2' };
    });
    setWebV2AppDataSource(data);
  };

  // 获取全部任务
  const getJobList = async namespaceId => {
    const params = {
      CurrentPage: 1,
      PageSize: 999,
      NamespaceId: namespaceId,
      Workload: 'job',
    };
    const {
      Data: { Applications },
    } = await services.ListApplicationsForPermissionAssistant({
      ...params,
    }, {
      region,
    });
    const data = Applications.map(i => {
      return { value: i.AppId, label: i.AppName, type: 'Job', appType: 'SAE1' };
    });
    setJobsDataSource(data);
  };

  const refreshTypes = {
    allApps: { refresh: getAppList, state: appDataSource },
    allMicroApps: { refresh: getMicroAppList, state: microAppDataSource },
    allWebV2Apps: { refresh: getWebV2AppList, state: webV2AppDataSource },
    allJobs: { refresh: getJobList, state: jobsDataSource },
  };

  useEffect(() => {
    setCascadeDataSource([]);
    if (namespaceId?.endsWith('*')) {
      const data = [
        {
          label: intl('saenext.enterprise.event-center.custom-hooks.allApps'),
          value: 'allApps',
        },
      ];
      setCascadeDataSource(data);
      return;
    }
    if (namespaceId) {
      Promise.all(map(appTypes, type => get(refreshTypes, `${type}.refresh`, noop)(namespaceId)));
    }
  }, [namespaceId]);

  useEffect(
    () => {
      if (!namespaceId) {
        return;
      }
      const states = map(appTypes, type => ({
        label: intl(`saenext.enterprise.event-center.custom-hooks.${type}`),
        value: type,
        children: get(refreshTypes, `${type}.state`, []),
        disabled: get(refreshTypes, `${type}.state`, []).length === 0 && !multiple,
      }));
      const appV2Type = find(appTypes, type =>
        includes(['allMicroApps', 'allWebV2Apps'], type),
      );

      // appV2Type: [全部应用v2 : [全部微服务，全部web], 全部任务],
      // appV1Type: [全部应用v1]
      const appsAndJobs = appV2Type ? [
        {
          label: intl(`saenext.enterprise.event-center.custom-hooks.allApps`),
          value: 'allApps',
          children: filter(states, state => state.value !== 'allJobs'),
        },
        find(states, state => state.value === 'allJobs') && {
          label: intl(`saenext.enterprise.event-center.custom-hooks.allJobs`),
          value: 'allJobs',
          children: find(states, state => state.value === 'allJobs')?.children || [],
        },
      ].filter(Boolean) :
      states;

      if (appsAndJobs.length < 2) {
        setCascadeDataSource(appV2Type ? appsAndJobs : states);
      } else {
        const data = [
          {
            label: intl('saenext.enterprise.event-center.custom-hooks.AllApplicationsAndTasks'),
            value: 'all',
            children: appsAndJobs,
          },
        ];
        setCascadeDataSource(data);
      }
    },
    map(appTypes, type => get(refreshTypes, `${type}.state`)),
  );

  return {
    microAppDataSource,
    webV2AppDataSource,
    appDataSource,
    jobsDataSource,
    cascadeDataSource,
  };
};
