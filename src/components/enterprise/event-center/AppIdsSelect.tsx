import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { intl } from '@ali/cnd';
import { useAppIds } from './custom-hooks';
import CascaderSelectDetail from '~/components/shared/CascaderSelectDetail';
import { concat, filter, find, flatten, forEach, includes, map } from 'lodash';

type AppIdsSelectProps = {
  // field: any;
  value?: any;
  onChange?: (value: any[]) => void;
  appUseDetailValue: boolean;
  region: string;
  namespaceId: string;
  appTypes?: AppTypes[]; // 应用类型 allApps: 全部应用(旧版接口)、 allMicroApps: 全部微应用、 allWebV1Apps: 始终分配CPU、 allWebV2Apps: 仅在请求时分配CPU、 allJobs: 全部任务
  multiple?: boolean; // 是否多选
  [key: string]: any;
};

export type AppTypes = 'allMicroApps' | 'allWebV2Apps' | 'allJobs' | 'allApps';

const AppIdsSelect = (props: AppIdsSelectProps, ref) => {
  const {
    // field,
    value,
    onChange,
    appUseDetailValue,
    region,
    namespaceId,
    appTypes = ['allMicroApps', 'allWebV2Apps', 'allJobs'],
    multiple,
    ...restProps
  } = props;
  // const { init } = field;

  useImperativeHandle(ref, () => ({
    microAppDataSource,
    webV2AppDataSource,
    appDataSource,
    jobsDataSource
  }));

  const {
    cascadeDataSource,
    microAppDataSource,
    webV2AppDataSource,
    appDataSource,
    jobsDataSource
  } = useAppIds({
    region,
    namespaceId,
    appTypes,
    multiple
  });
  
  useEffect(() => {
    if (appUseDetailValue) {
      // 当value内只有 id 时，填充 value 的 label 和 type
      fillValueDetail();
    }
  }, [
    value,
    microAppDataSource,
    webV2AppDataSource,
    appDataSource,
    jobsDataSource,
  ])

  const fillValueDetail = () => {
    if (multiple) {
      findValueDetail(value);
    } else {
      findValueDetail([value]);
    }
  }

  const findValueDetail = (valueArr) => {
    const allData = concat(microAppDataSource, webV2AppDataSource, appDataSource, jobsDataSource);
    forEach(valueArr, (valueItem) => {
      if (valueItem.label && valueItem.type) {
        return;
      }
      const target = find(allData, {
        value: valueItem?.value,
      })
      if (target) {
        valueItem.label = target.label;
        valueItem.type = target.type;
      }
    })
  }

  const getAppTypeData = (type, isDetail) => {
    switch (type) {
      case 'allMicroApps':
        return isDetail ? microAppDataSource : map(microAppDataSource, (item) => item.value);
      case 'allWebV2Apps':
        return isDetail ? webV2AppDataSource : map(webV2AppDataSource, (item) => item.value);
      default:
        return [];
    }
  };

  const onChangeFormatData = (data) => {
    if (!multiple) {
      onChange(data);
      return;
    }
    // 过滤出2.0版本的三种应用全选key, 转成全部的 ids, 并拼接
    const appV2Types = [];
    const ids = filter(data, (item) => {
      if (includes(['allMicroApps', 'allWebV2Apps'], item?.value || item)) {
        appV2Types.push(item?.value || item);
        return false;
      } else {
        return item;
      }
    });
    const appV2Data = flatten(map(appV2Types, (type) => getAppTypeData(type, appUseDetailValue)));
    onChange(concat(ids, appV2Data));
  };

  return (
    <CascaderSelectDetail
      value={value}
      onChange={onChangeFormatData}
      multiple={multiple}
      useDetailValue={appUseDetailValue}
      showSearch
      placeholder={intl(
        'saenext.event-center.subscribe-rule.EventRuleStep.SelectAnApplicationOrTask'
      )}
      style={{ width: 280 }}
      listStyle={{ minWidth: '200px', width: 'max-content' }}
      displayRender={(labels) => labels[labels.length - 1]}
      dataSource={cascadeDataSource}
      tagInline
      maxTagPlaceholder={(selectedValues, total) => {
        const count = selectedValues.length;
        return (
          <span>{intl("saenext.enterprise.event-center.AppIdsSelect.CountItemSelected", { count:count })}

          </span>);

      }}
      {...restProps} />);


};
export default forwardRef(AppIdsSelect);