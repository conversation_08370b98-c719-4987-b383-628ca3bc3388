import { intl } from '@ali/cnd';
import { cloneDeep, filter, find, forEach, get, includes, map, uniq } from "lodash";

const getPermissionJson = (list) => {
  const permissionJson = { Version: '1', Statement: [] };
  forEach(list, (v) => {
    const { Actions = [], Resources = [] } = v;
    permissionJson.Statement.push({
      Effect: 'Allow',
      Action: map(Actions, (item) => {
        return `sae:${item}`;
      }),
      Resource: Resources
    });
  });
  return JSON.stringify(permissionJson, null, 4);
};

const convertToTree = (data = [], parentKey = '') => {
  const result = [];
  for (const item of data) {
    const {
      PrimaryMenuType,
      PrimaryMenuDesc,
      SubmenuType,
      SubmenuDesc,
      ItemType,
      ItemDesc,
      Items = [],
      Submenus = [],
      ...rest
    } = item;

    const obj = {
      key: PrimaryMenuType || SubmenuType || ItemType,
      label: PrimaryMenuDesc || SubmenuDesc || ItemDesc,
      ...rest
    };
    const children = convertToTree([...Items, ...Submenus], obj.key);
    if (children.length) {
      if (parentKey) {
        obj.key = `${parentKey}-${obj.key}`;
      }
      obj.children = children;
    }
    result.push(obj);
  }
  return result;
};

const treeFilterEmptyNode = (tree) => {
  const result = cloneDeep(tree);
  for (let index = 0; index < result.length; index++) {
    const item = result[index];
    if (item?.children?.length === 0) {
      result.splice(index, 1);
      index--;
    } else if (item?.children) {
      item.children = treeFilterEmptyNode(item.children);
    }
  }
  return result;
};

const treeFilterPermissions = (tree, filterConditon) => {
  const result = cloneDeep(tree);

  for (let index = 0; index < result.length; index++) {
    const item = result[index];
    if (!item.children) {
      if (!filterConditon(item)) {
        result.splice(index, 1);
        index--;
      }
    } else {
      item.children = treeFilterPermissions(item.children, filterConditon);
    }
  }

  return treeFilterEmptyNode(result);
};

const getDefaultSelectedTree = (tree) => {
  const { children: treeReadChilren = [] } = find(tree, { key: 'read' }) || {};
  const chilrenTreeDefaultSelected = treeFilterPermissions(treeReadChilren, (item) => {
    return item.DefaultSelected;
  });
  const treeDefaultSelected = {
    key: 'default',
    label: intl("saenext.enterprise.permission-assistant.utils.DefaultReadPermissions"),
    children: chilrenTreeDefaultSelected
  };

  return treeDefaultSelected;
};

const getRelatingItems = (selectedKeys, flattenedTree) => {
  const relatingItems = [];
  const selectedItems = filter(flattenedTree, (v) => {
    return includes(selectedKeys, get(v, 'key'));
  });
  forEach(selectedItems, (v) => {
    relatingItems.push(...get(v, 'RelatingItems', []));
  });
  const result = uniq(relatingItems);
  return result;
};

export {
  getPermissionJson,
  convertToTree,
  treeFilterPermissions,
  getDefaultSelectedTree,
  getRelatingItems,
};