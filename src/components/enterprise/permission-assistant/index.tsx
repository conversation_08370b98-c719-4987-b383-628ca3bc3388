import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { Actions, Button, Dialog, LinkButton, Message } from '@ali/cnd';
import CndTable from '@ali/cnd-table';
import * as services from '~/services';
import RamJsonButton from './components/RamJsonButton';
import PreviewButton from './components/PreviewPermissionButton';
import PermissionConfigButton from './components/PermissionConfigButton';

const PermissionAssistant = (props) => {

  const [refreshIndex, setRefreshIndex] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  const handleDelete = (record) => {
    const { RecordId } = record;
    Dialog.confirm({
      content:
      <p>{intl("saenext.enterprise.permission-assistant.ConfirmThatYouWantTo")}
        <span className='color-orange'>{record.Name}</span>
        </p>,

      onOk: () => {
        return new Promise(async (resolve, reject) => {
          await services.DeletePermissionAssistantRecord(
            {
              RecordId
            },
            {
              customErrorHandle(err, serviceConfig, callback) {
                err ? reject() : resolve(true);
                callback();
              }
            }
          );
          resolve(true);
          Message.success(intl("saenext.enterprise.permission-assistant.DeletedSuccessfully"));
          setRefreshIndex(refreshIndex + 1);
        });
      }
    });
  };

  const fetchData = async (params) => {
    const {
      current,
      pageSize
    } = params;
    const {
      Data: {
        UserRecords = [],
        TotalSize = 0
      }
    } = (await services.GetPermissionAssistantRecords(
      {
        CurrentPage: current,
        PageSize: pageSize
      }
    )) || {};

    setTotalCount(TotalSize);

    return {
      data: UserRecords,
      total: TotalSize
    };
  };

  const columns = [
  {
    key: 'Name',
    title: intl("saenext.enterprise.permission-assistant.PermissionPolicyName"),
    dataIndex: 'Name',
    width: 200,
    cell: (value, index, record) => {
      const { Uid } = record;
      return (
        <span>
            <span style={{ paddingRight: 8 }}>{value}</span>
            {/* <If condition={Uid}>
             <Balloon trigger={Uid===mainAccountUid ? <span>(主账号)</span> : <span>(RAM用户)</span>} closable={false} align="t">
               {Uid}
             </Balloon>
            </If> */}
          </span>);

    }
  },
  {
    key: 'Comment',
    title: intl("saenext.enterprise.permission-assistant.Remarks"),
    dataIndex: 'Comment',
    width: 300,
    cell: (value, index, record) => {
      return value;
    }
  },
  {
    key: 'AuthorizationOperation        ',
    title: intl("saenext.enterprise.permission-assistant.AuthorizationOperation"),
    dataIndex: 'AuthorizationOperation',
    width: 300,
    cell: (value, index, record) => {
      return (
        <PreviewButton
          buttonText={intl("saenext.enterprise.permission-assistant.ViewPermissions")}
          data={record?.SentenceRecords} />);


    }
  },
  {
    key: 'AuthOperation        ',
    title: intl("saenext.enterprise.permission-assistant.Operation"),
    dataIndex: 'Operation',
    width: 400,
    cell: (value, index, record) => {
      return (
        <Actions>
            <RamJsonButton
            buttonText={intl("saenext.enterprise.permission-assistant.CopyRamAuthorizationStatementsWith")}
            data={record?.Result?.RamActionsAndResources} />

            <PermissionConfigButton
            initData={record}
            setRefreshIndex={setRefreshIndex}>

              <LinkButton>{intl("saenext.enterprise.permission-assistant.Edit")}

            </LinkButton>
            </PermissionConfigButton>
            <LinkButton
            key={index}
            onClick={() => handleDelete(record)}>{intl("saenext.enterprise.permission-assistant.Delete")}


          </LinkButton>
          </Actions>);

    }
  }];


  return (
    <>
      <Message type="notice" className='mb-s'>{intl("saenext.enterprise.permission-assistant.ThePermissionConfigurationAssistantCan")}

      </Message>

      <CndTable
        fetchData={fetchData}
        columns={columns}
        refreshIndex={refreshIndex}
        showRefreshButton
        operation={
          <PermissionConfigButton
            setRefreshIndex={setRefreshIndex}
          >
            <Button
              name="create"
              disabled={totalCount >= 20}
              type="primary">{intl("saenext.enterprise.permission-assistant.CreateAPermissionPolicy")}
            </Button>
          </PermissionConfigButton>}
      />



    </>);

};

export default PermissionAssistant;