import { intl } from '@ali/cnd';
import React, { useRef } from 'react';
import SlideButton from '~/components/shared/SlideButton';
import PermissionItems from './PreviewPermissionButton/PermissionItems';
import { Button } from '@ali/cnd';

const PermissionItemsSlideButton = (props) => {
  const {
    buttonText,
    data
  } = props;

  const buttonRef = useRef(null);

  return (
    <SlideButton
      buttonText={buttonText}
      slideTitle={buttonText}
      slideSize={900}
      ref={buttonRef}
      slideContent={
      <PermissionItems
        permissions={data} />}


      linkButton
      customFooter={
      <>
          <Button
          onClick={() => {
            buttonRef.current?.setActive(false);
          }}>{intl("saenext.permission-assistant.components.PermissionItemsSlideButton.Close")}


        </Button>
        </>} />);



};

export default PermissionItemsSlideButton;