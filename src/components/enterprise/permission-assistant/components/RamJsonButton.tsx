import { intl } from '@ali/cnd';
import React, { useMemo, useRef } from 'react';
import { Button, Copy } from '@ali/cnd';
import { CustomEditor } from '@ali/cnd';
import SlideButton from '~/components/shared/SlideButton';
import { getPermissionJson } from '../utils';

const RamJsonButton = (props) => {
  const {
    buttonText,
    data = []
  } = props;

  const buttonRef = useRef(null);

  const jsonData = useMemo(() => {
    return getPermissionJson(data);
  }, [data]);

  return (
    <SlideButton
      buttonText={buttonText}
      slideTitle={buttonText}
      slideSize={900}
      ref={buttonRef}
      slideContent={
      <CustomEditor
        isAutoHeight
        value={jsonData}
        // mode='json'
      />}

      linkButton
      customFooter={
      <>
          <Copy
          text={jsonData}
          showIcon
          icon={
          <Button type="primary">{intl("saenext.permission-assistant.components.RamJsonButton.OneClickCopy")}

          </Button>} />



          <Button
          onClick={() => {
            buttonRef.current?.setActive(false);
          }}>{intl("saenext.permission-assistant.components.RamJsonButton.Close")}


        </Button>
        </>} />);



};

export default RamJsonButton;