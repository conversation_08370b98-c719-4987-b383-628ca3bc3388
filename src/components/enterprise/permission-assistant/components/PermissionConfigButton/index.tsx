import { intl } from '@ali/cnd';
import React, { useEffect, useRef, useState } from 'react';
import { Button, SlidePanel, Step } from '@ali/cnd';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import ComShow from '~/components/shared/ComShow';
import If from '~/components/shared/If';
import RequestWrap from '~/components/shared/RequestWrap';
import { get } from 'lodash';
import PermissionSetting from './PermissionSetting';
import PermissionResult from './PermissionResult';

const PermissionConfigButton = (props) => {
  const { initData, allPermissionAssistants, setRefreshIndex, children } = props;
  const [isShowing, setIsShowing] = useState(false);
  const [current, setCurrent] = useState(0);
  const [editRecordId, setEditRecordId] = useState('');
  const [ramActionsAndResources, setRamActionsAndResources] = useState([]);
  const FirstSettingEl = useRef(null);

  useEffect(() => {
    if (!isShowing) {
      setCurrent(0);
      setEditRecordId('');
      setRamActionsAndResources([]);
    }
  }, [isShowing])

  const hanleStepChange = async (index) => {
    if (index === 1) {
      await getResult();
    }
    setCurrent(index);
  };

  const hanleNextStep = async () => {
    await getResult();
    setCurrent((current) => {
      return current + 1;
    });
  };

  const handleBackstep = () => {
    setCurrent((current) => {
      return current - 1 || 0;
    });
  };

  const handelDone = () => {
    setIsShowing(false);
    setCurrent(0);
    setRefreshIndex(new Date());
  };

  const getResult = async () => {
    const res = (await FirstSettingEl.current?.hanleNextStep()) || {};
    const { RamActionsAndResources, RecordId } = res;
    setEditRecordId(RecordId);
    setRamActionsAndResources(RamActionsAndResources);
  };

  const renderFooter = () => {
    if (current === 0) {
      return (
        <>
          <RequestWrap>
            <Button type="primary" onClick={hanleNextStep}>
              {intl('saenext.components.PermissionConfigButton.NextStep')}
            </Button>
          </RequestWrap>
          <Button
            onClick={() => {
              setIsShowing(false);
              setCurrent(0);
            }}
          >
            {intl('button.cancel')}
          </Button>
        </>
      );
    }
    return (
      <>
        <Button type="secondary" onClick={handleBackstep}>
          {intl('saenext.components.PermissionConfigButton.PreviousStep')}
        </Button>
        <Button type="primary" onClick={handelDone}>
          {intl('saenext.components.PermissionConfigButton.Complete')}
        </Button>
        <Button
          onClick={() => {
            setIsShowing(false);
            setCurrent(0);
          }}
        >
          {intl('button.cancel')}
        </Button>
      </>
    );
  };

  const onClick = () => {
    setIsShowing(true);
  };

  const steps = [intl("saenext.components.PermissionConfigButton.PermissionPolicyConfiguration"), intl("saenext.components.PermissionConfigButton.PermissionPolicyPreview")];

  return (
    <>
      <AddPropsWrap onClick={onClick}>
        {children}
      </AddPropsWrap>
      <SlidePanel
        title={initData ? intl("saenext.components.PermissionConfigButton.EditPermissionPolicy") : intl("saenext.components.PermissionConfigButton.CreateAPermissionPolicy")}
        isShowing={isShowing}
        width="large"
        customFooter={renderFooter()}
        onMaskClick={() => {
          setIsShowing(false);
          setCurrent(0);
        }}>

        <Step current={current}>
          {
          steps.map((item, index) =>
          <Step.Item key={index} title={item} onClick={hanleStepChange} />
          )}

        </Step>
        <ComShow if={current === 0}>
          <PermissionSetting
            ref={FirstSettingEl}
            initData={initData}
            editRecordId={editRecordId}
            allPermissionAssistants={allPermissionAssistants} />

        </ComShow>
        <If condition={current === 1}>
          <PermissionResult
            ramActionsAndResources={ramActionsAndResources} />

        </If>
      </SlidePanel>
    </>);

};

export default PermissionConfigButton;
