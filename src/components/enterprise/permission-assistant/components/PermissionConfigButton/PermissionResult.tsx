import { intl } from '@ali/cnd';
import React from 'react';
import { Copy, Step, CndTable } from '@ali/cnd';
import { getPermissionJson } from '../../utils';
import { CustomEditor } from '@ali/cnd';
import { systemStrategy } from '../../constants';
import CachedData from '~/cache/common';

const PermissionResult = (props) => {
  const {
    ramActionsAndResources = []
  } = props;

  const jsonData = getPermissionJson(ramActionsAndResources);

  return (
    <div className="mt-xl mb-xl">
      <Step
        direction='ver'
        stretch
        animation>

        <Step.Item
          status='process'
          title={
          <>{intl("saenext.components.PermissionConfigButton.PermissionResult.EnableAccessControlRam")}
            <a href={`${CachedData.confLink('feature:ram:url')}/policies`} target="_blank">{intl("saenext.components.PermissionConfigButton.PermissionResult.PermissionPolicyManagement")}</a>{intl("saenext.components.PermissionConfigButton.PermissionResult.AddOrModifyCustomPolicies")}
          </>}

          content={
          <>
              <Copy
              text={jsonData}
              showIcon>{intl("saenext.components.PermissionConfigButton.PermissionResult.OneClickCopy")}


            </Copy>
              <CustomEditor
              value={jsonData} />

            </>} />


        <Step.Item
          status='process'
          title={
          <>{intl("saenext.components.PermissionConfigButton.PermissionResult.EnableAccessControlRam")}

            <a href={`${CachedData.confLink('feature:ram:url')}/users`} target="_blank">{intl("saenext.components.PermissionConfigButton.PermissionResult.UserManagement")}

            </a>{intl("saenext.components.PermissionConfigButton.PermissionResult.AuthorizeTheAuthorizedSubAccounts")}

          </>} />


        <Step.Item
          status='process'
          title={
          <>{intl("saenext.components.PermissionConfigButton.PermissionResult.IfTheOperationInvolvesOther")}

            <a href={CachedData.confLink('help:sae:policies-and-examples')}>{intl("saenext.components.PermissionConfigButton.PermissionResult.SystemPermissionPolicy")}</a>{intl("saenext.components.PermissionConfigButton.PermissionResult.ToEnsureThatRamUsers")}

          </>}

          content={
          <CndTable dataSource={systemStrategy} className='mt'>
              <CndTable.Column title={intl("saenext.components.PermissionConfigButton.PermissionResult.PermissionPolicyName")} dataIndex="key" />
              <CndTable.Column title={intl("saenext.components.PermissionConfigButton.PermissionResult.Description")} dataIndex="label" />
            </CndTable>} />


      </Step>
    </div>);

};

export default PermissionResult;
