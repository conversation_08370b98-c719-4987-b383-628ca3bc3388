import { intl } from '@ali/cnd';
import { Actions, Button, LinkButton, CndTable } from '@ali/cnd';
import React from 'react';
import PermissionResource from '../PreviewPermissionButton/PermissionResource';
import PermissionItemsSlideButton from '../PermissionItemsSlideButton';
import SentenceSlideButton from '../SentenceSlideButton';

const PermissionSentence = (props) => {
  const {
    value = [],
    onChange = () => {}
  } = props;

  const handleAdd = (val) => {
    const newValue = [...value, val];
    onChange(newValue);
  };

  const handleEdit = (val, index) => {
    const newValue = [...value];
    newValue[index] = val;
    onChange(newValue);
  };

  const handleDelete = (index) => {
    const newValue = [...value];
    newValue.splice(index, 1);
    onChange(newValue);
  };

  return (
    <div className='mt'>

      <SentenceSlideButton
        buttonText={intl("saenext.components.PermissionConfigButton.PermissionSentence.AddPermissionStatement")}
        onChange={(val) => handleAdd(val)} />


      <span className='ml'>{intl("saenext.components.PermissionConfigButton.PermissionSentence.ResourcePermissionSettingsForMultiple")}

      </span>

      <CndTable dataSource={value} className='mt-8'>
        <CndTable.Column
          title={intl("saenext.components.PermissionConfigButton.PermissionSentence.AuthorizedResources")}
          dataIndex="Resource"
          cell={(value) =>
          <PermissionResource
            resource={value}
            width={400} />}

          width='400' />


        <CndTable.Column
          title={intl("saenext.components.PermissionConfigButton.PermissionSentence.AuthorizationOperation")}
          dataIndex="PermissionAssistantItems"
          cell={(value) =>
          <PermissionItemsSlideButton
            buttonText={intl("saenext.components.PermissionConfigButton.PermissionSentence.ViewPermissions")}
            data={value} />} />




        <CndTable.Column
          title={intl("saenext.components.PermissionConfigButton.PermissionSentence.Operation")}
          dataIndex="AuthOperation"
          cell={(value, index, record) =>
          <Actions>
              <SentenceSlideButton
              buttonText={intl("saenext.components.PermissionConfigButton.PermissionSentence.Clone")}
              linkButton
              record={record}
              onChange={(val) => handleAdd(val)} />

              <SentenceSlideButton
              buttonText={intl("saenext.components.PermissionConfigButton.PermissionSentence.Edit")}
              linkButton
              record={record}
              onChange={(val) => handleEdit(val, index)} />

              <LinkButton
              onClick={() => handleDelete(index)}>{intl("saenext.components.PermissionConfigButton.PermissionSentence.Delete")}


            </LinkButton>
            </Actions>} />


      </CndTable>
    </div>);

};

export default PermissionSentence;
