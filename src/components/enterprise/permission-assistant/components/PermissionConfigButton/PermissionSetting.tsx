import { intl } from '@ali/cnd';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle } from 'react';
import { Field, Form, Input } from '@ali/cnd';
import { get, isEmpty } from 'lodash';
import { jsonStringify } from '~/utils/transfer-data';
import * as services from '~/services';
import PermissionSentence from './PermissionSentence';

const PermissionSetting = (props, ref) => {
  const { initData, editRecordId } = props;
  const FormItem = Form.Item;
  const field = Field.useField();
  const { init, validate, getValues, setValues, reset, getError } = field;

  useEffect(() => {
    if (initData) {
      const {
        RecordId,
        Name,
        Comment,
        SentenceRecords
      } = initData;
      setValues({
        RecordId,
        Name,
        Comment,
        SentenceRecords
      });
    }
  }, [initData]);

  const recordsValidator = (rule, value = [], callback) => {
    if (value.length === 0) {
      callback(intl("saenext.components.PermissionConfigButton.PermissionSetting.AddAPermissionStatementBefore"));
    } else {
      callback();
    }
  };

  const hanleNextStep = async () => {
    return new Promise(async (resolve, reject) => {
      validate(async (errors, values: any) => {
        if (!isEmpty(errors)) {
          reject(null);
        } else {
          if (editRecordId) {
            values.RecordId = editRecordId;
          }
          const params = {
            Record: jsonStringify({ ...values })
          };
          const { Data } = (await services.GetPermissionAssistantResult(params)) || {};
          if (!Data) {
            reject(null);
            return;
          }
          resolve(Data);
        }
      });
    });
  };

  useImperativeHandle(
    ref,
    () => ({
      hanleNextStep
    }),
    [editRecordId]
  );

  const formItemLayout = {
    labelCol: {
      fixedSpan: 10
    },
    wrapperCol: {
      span: 14
    }
  };

  return (
    <Form field={field} className='mt-xl mb-xl'>
      <FormItem label={intl("saenext.components.PermissionConfigButton.PermissionSetting.PolicyName")} {...formItemLayout} required>
        <Input
          placeholder={intl("saenext.components.PermissionConfigButton.PermissionSetting.EnterAPolicyName")}
          {...init('Name', {
            rules: [
            {
              pattern: /^[a-zA-Z][a-zA-Z0-9_-]{0,36}$/,
              message: intl("saenext.components.PermissionConfigButton.PermissionSetting.ItMustStartWithA")
            },
            {
              required: true,
              message: intl("saenext.components.PermissionConfigButton.PermissionSetting.EnterAPolicyName")
            }]

          })} />

      </FormItem>
      <FormItem label={intl("saenext.components.PermissionConfigButton.PermissionSetting.Remarks")} {...formItemLayout}>
        <Input.TextArea
          {...init('Comment')} />

      </FormItem>
      <FormItem label="">
        <PermissionSentence
          {...init('SentenceRecords', {
            rules: [
            {
              validator: recordsValidator
            }]

          })} />

      </FormItem>
    </Form>);

};

export default forwardRef(PermissionSetting);