import React, { useRef, useState } from 'react';
import SlideButton from '~/components/shared/SlideButton';
import SentenceForm from './SentenceForm';

const SentenceSlideButton = (props) => {
  const {
    buttonText,
    linkButton = false,
    record,
    onChange,
  } = props;

  const ref = useRef(null);

  return (
    <SlideButton
      buttonText={buttonText}
      slideTitle={buttonText}
      slideSize={1100}
      slideContent={
        <SentenceForm
          initValue={record}
          submitChange={onChange}
          ref={ref}
        />
      }
      linkButton={linkButton}
      submit={() => ref.current?.submit()}
    />
  );
}

export default SentenceSlideButton;