import { find } from 'lodash';
import React, { useContext, useMemo } from 'react';
import RegionNamespaceAppIdForm from '~/components/shared/RegionNamespaceAppIdForm';
import FeatureContext from '~/utils/featureContext';

const SentenceResource = (props) => {
  const {
    value = {},
    onChange = () => {},
  } = props;

  const { webFeature } = useContext(FeatureContext);
  const { EnableWebApplication } = webFeature;

  const parseValue = useMemo(() => {
    const {
      Region,
      Namespace,
      Apps = [],
      Jobs = [],
    } = value;

    const namespaceId = Namespace === '_default' ? Region : Namespace;

    const allApps = find(Apps, {
      AppId: '*',
    })

    const allJobs = find(Jobs, {
      JobId: '*',
    })

    const apps = allApps ?
      [
        {
          value: 'allApps'
        }
      ] :
      Apps.map(item => {
        return {
          value: item.AppId,
          label: item.AppName,
          type: 'App',
        }
      })

    const jobs = allJobs ?
      [
        {
          value: 'allJobs'
        }
      ] :
      Jobs.map(item => {
        return {
          value: item.JobId,
          label: item.JobName,
          type: 'Job',
        }
      })

    const appjob = [
      ...apps,
      ...jobs,
    ]

    return {
      region: Region,
      namespaceId,
      appjob,
    }

  }, [value])

  const onFormatChange = (v) => {
    const {
      region,
      namespaceId,
      appjob = [],
    } = v;

    const Namespace = namespaceId === region ? '_default' : namespaceId;

    const allAppJob = find(appjob, {
      value: 'all',
    })

    const allApps = find(appjob, {
      value: 'allApps',
    })

    const allJobs = find(appjob, {
      value: 'allJobs',
    })

    const apps = allAppJob || allApps ?
      [
        {
          AppId: '*'
        }
      ] :
      appjob.filter(item => {
        return item.type === 'App'
      }).map(item => {
        return {
          AppId: item.value,
          AppName: item.label,
        }
      })
    const jobs = allAppJob || allJobs ?
      [
        {
          JobId: '*'
        }
      ] :
      appjob.filter(item => {
        return item.type === 'Job'
      }).map(item => {
        return {
          JobId: item.value,
          JobName: item.label,
        }
      })

    onChange({
      Region: region,
      Namespace,
      Apps: apps,
      Jobs: jobs,
    })
  }

  return (
    <RegionNamespaceAppIdForm
      value={parseValue}
      onChange={onFormatChange}
      appTypes={EnableWebApplication ? ['allMicroApps', 'allWebV2Apps', 'allJobs'] : ['allApps', 'allJobs']}
      appUseDetailValue
    />
  )
}

export default SentenceResource;