import { Search, intl } from '@ali/cnd';
import React, { useEffect, useMemo, useState } from 'react';
import { Transfer, Tree } from '@ali/cnd';
import * as services from '~/services';
import { convertToTree, getDefaultSelectedTree, getRelatingItems, treeFilterPermissions } from '../../utils';
import { uniq } from 'lodash';

const SentenceTreeTransfer = (props) => {
  const { value: selectedPermissions = [], onChange: selectedOnChange = () => {} } = props;
  const [treeData, setTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState(['write', 'read']);
  const [matchedKeys, setMatchedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  useEffect(() => {
    getPermissionAssistants();
  }, []);

  const getPermissionAssistants = async () => {
    const {
      Data: { PrimaryMenus = [] },
    } = (await services.DescribePermissionAssistant()) || {};
    const tree = convertToTree(PrimaryMenus);
    setTreeData(tree);
  };

  const [transferDataSource, parentNodeKey, defaultSelectedKey] = useMemo(() => {
    const data = [];
    const parentNodeKey = [];
    const defaultSelectedKey = [];
    const flatten = (list = []) => {
      list.forEach((item) => {
        if (!item.children) {
          item.value = item.key;
          data.push(item);
          if (item.DefaultSelected) {
            defaultSelectedKey.push(item.key);
          }
        } else {
          parentNodeKey.push(item.key);
          flatten(item.children);
        }
      });
    };
    flatten(treeData);
    return [data, parentNodeKey, defaultSelectedKey];
  }, [treeData]);

  const allWriteReadTree = useMemo(
    () => treeFilterPermissions(treeData, (item) => !item.DefaultSelected),
    [treeData],
  );

  const defaultSelectedTree = useMemo(() => getDefaultSelectedTree(treeData), [treeData]);

  const selectedWriteReadTree = useMemo(
    () => treeFilterPermissions(allWriteReadTree, (item) => selectedPermissions.includes(item.key)),
    [allWriteReadTree, selectedPermissions],
  );

  const allSelectedTree = useMemo(() => selectedWriteReadTree.concat(defaultSelectedTree), [
    treeData,
    selectedPermissions,
  ]);

  const onTransferChange = (value, extra) => {
    const valueLeafFilter = value.filter((item) => !parentNodeKey.includes(item));

    let relatedItems = [];
    if (extra?.checked) {
      relatedItems = getRelatingItems(valueLeafFilter, transferDataSource);
    }

    const result = valueLeafFilter.concat(relatedItems).concat(defaultSelectedKey);
    selectedOnChange(uniq(result));
  };

  const handleSearch = (value) => {
    value = value.trim();
    if (!value) {
      setMatchedKeys(null);
      return;
    }

    const matchedKeys = [];
    const loop = data =>
      data.forEach(item => {
        if (item.label.indexOf(value) > -1) {
          matchedKeys.push(item.key);
        }
        if (item.children && item.children.length) {
          loop(item.children);
        }
      });
    loop(allWriteReadTree);

    setExpandedKeys([...matchedKeys]);
    setAutoExpandParent(true);
    setMatchedKeys(matchedKeys);
  }

  const handleExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  const filterTreeNode = node => {
    return (
      matchedKeys?.includes(node.props.eventKey)
    );
  };

  return (
    <Transfer
      mode="simple"
      listStyle={{ width: 300, height: 450 }}
      titles={[
        intl('saenext.components.SentenceSlideButton.SentenceTreeTransfer.OptionalPermissions'),
        intl('saenext.components.SentenceSlideButton.SentenceTreeTransfer.SelectedPermissions'),
      ]}
      dataSource={transferDataSource}
      value={selectedPermissions}
      onChange={onTransferChange}
    >
      {({ position, onChange, value }) => {
        if (position === 'left') {
          return (
            <>
              <Search
                shape="simple"
                size="medium"
                className='full-width mb'
                onChange={handleSearch}
                placeholder={intl('saenext.components.SentenceSlideButton.SentenceTreeTransfer.Search')}
              />
              <Tree
                key={JSON.stringify(treeData)}
                checkable
                // defaultExpandedKeys={['write', 'read']}
                autoExpandParent={autoExpandParent}
                expandedKeys={expandedKeys}
                dataSource={allWriteReadTree}
                onExpand={handleExpand}
                checkedKeys={selectedPermissions}
                onCheck={onTransferChange}
                filterTreeNode={filterTreeNode}
              />
            </>
          );
        } else {
          return (
            <Tree
              key={JSON.stringify(allSelectedTree)}
              defaultExpandAll
              dataSource={allSelectedTree}
            />
          );
        }
      }}
    </Transfer>
  );
};

export default SentenceTreeTransfer;
