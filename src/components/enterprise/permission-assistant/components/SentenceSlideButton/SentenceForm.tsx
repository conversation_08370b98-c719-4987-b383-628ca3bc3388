import { intl } from '@ali/cnd';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Field, Form } from '@ali/cnd';
import SentenceTreeTransfer from './SentenceTreeTransfer';
import SentenceResource from './SentenceResource';
import { isEmpty } from 'lodash';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    fixedSpan: 6
  },
  wrapperCol: {
    span: 24
  }
};

const SentenceForm = (props, ref) => {
  const {
    initValue,
    submitChange
  } = props;

  const field = Field.useField();

  const {
    init,
    getValues
  } = (field as any);

  useEffect(() => {
    if (initValue) {
      field.setValues(initValue);
    }
  }, [initValue]);

  useImperativeHandle(
    ref,
    () => ({
      submit
    }),
    [getValues()]
  );

  const submit = async () => {
    return new Promise((resolve, reject) => {
      field.validate((errors, values) => {
        if (!isEmpty(errors)) {
          reject(false);
        } else {
          submitChange(values);
          resolve(true);
        }
      });
    });
  };

  const validateResource = (rule, value, callback) => {
    const {
      Region,
      Namespace,
      Apps = [],
      Jobs = []
    } = value || {};

    if (!Region || !Namespace || !Apps.length && !Jobs.length) {
      callback(intl("saenext.components.SentenceSlideButton.SentenceForm.SelectAnAuthorizedResource"));
    } else {
      callback();
    }
  };

  const validatePermission = (rule, value, callback) => {
    if (!value || !value.length) {
      callback(intl("saenext.components.SentenceSlideButton.SentenceForm.SelectAnAuthorizationAction"));
    } else {
      callback();
    }
  };

  return (
    <Form field={field} {...formItemLayout}>
      <FormItem
        label={intl("saenext.components.SentenceSlideButton.SentenceForm.AuthorizedResources")}
        required
        style={{
          marginBottom: '0px'
        }}>

        <SentenceResource
          {...init('Resource', {
            rules: [
            {
              validator: validateResource,
              trigger: ['onBlur']
            }]

          })} />

      </FormItem>
      <FormItem
        label={intl("saenext.components.SentenceSlideButton.SentenceForm.AuthorizationOperation")}
        required>

        <SentenceTreeTransfer
          {...init('PermissionAssistantItems', {
            rules: [
            {
              validator: validatePermission,
              trigger: ['onBlur']
            }]

          })} />

      </FormItem>
    </Form>);

};

export default forwardRef(SentenceForm);
