import React, { useEffect, useState } from 'react';
import * as services from '~/services';
import { convertToTree, getDefaultSelectedTree, treeFilterPermissions } from '../../utils';
import { Tree } from '@ali/cnd';

const PermissionItems = (props) => {
  const {
    permissions = [],
  } = props;

  const [allPermissionTree, setAllPermissionTree] = useState([]);

  useEffect(() => {
    getPermissionAssistants();
  }, []);

  const getPermissionAssistants = async () => {
    const {
      Data: {
        PrimaryMenus = [],
      },
    } = await services.DescribePermissionAssistant() || {};
    const tree = convertToTree(PrimaryMenus);
    
    const treeFiltered = treeFilterPermissions(tree, (item) => {
      return permissions.includes(item.key) && !item.DefaultSelected;
    });
    const treeDefaultSelected = getDefaultSelectedTree(tree);

    const treeCombined = treeFiltered.concat(treeDefaultSelected);
    setAllPermissionTree(treeCombined);
  };

  return (
    <Tree
      key={JSON.stringify(allPermissionTree)}
      defaultExpandAll
      dataSource={allPermissionTree}
    />
  )
}

export default PermissionItems;