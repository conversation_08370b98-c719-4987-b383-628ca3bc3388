import { intl } from '@ali/cnd';
import React from 'react';
import { Form, Input } from '@ali/cnd';
import _ from 'lodash';
import { Truncate } from '@ali/xconsole';

const PermissionResource = (props) => {
  const {
    resource = {},
    width
  } = props;

  const {
    Apps = [],
    Jobs = [],
    Namespace,
    Region
  } = resource;

  return (
    <Truncate type="width" threshold={width} align="b">
      <Form
        isPreview
        inline
        style={{ height: 20 }}
        className='preview-permission-button-title--form'
      >

        <Form.Item label={intl("saenext.components.PreviewPermissionButton.PermissionResource.Region")}>
          <Input value={Region} />
        </Form.Item>
        <Form.Item label={intl("saenext.components.PreviewPermissionButton.PermissionResource.Namespace")}>
          <Input value={Namespace === '_default' ? intl("saenext.components.PreviewPermissionButton.PermissionResource.Default") : Namespace} />
        </Form.Item>
        <Form.Item label={intl("saenext.components.PreviewPermissionButton.PermissionResource.Application")}>
          <Input value={_.map(Apps, (v) => {return v.AppName;}).join(', ')} />
        </Form.Item>
        <Form.Item label={intl("saenext.components.PreviewPermissionButton.PermissionResource.Task")}>
          <Input value={_.map(Jobs, (v) => {return v.JobName;}).join(', ')} />
        </Form.Item>
      </Form>
    </Truncate>);

};

export default PermissionResource;
