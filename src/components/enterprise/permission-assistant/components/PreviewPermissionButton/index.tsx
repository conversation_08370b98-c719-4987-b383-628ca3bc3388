import { intl } from '@ali/cnd';
import { Button } from '@ali/cnd';
import React, { useRef } from 'react';
import SlideButton from '~/components/shared/SlideButton';
import PreviewContent from './PermissionContent';
import './index.less';

const PreviewPermissionButton = (props) => {
  const {
    buttonText,
    data
  } = props;

  const buttonRef = useRef(null);

  return (
    <SlideButton
      buttonText={buttonText}
      slideTitle={buttonText}
      slideSize={900}
      ref={buttonRef}
      slideContent={
      <PreviewContent
        data={data} />}


      linkButton
      customFooter={
      <>
          <Button
          onClick={() => {
            buttonRef.current?.setActive(false);
          }}>{intl("saenext.components.PreviewPermissionButton.Close")}


        </Button>
        </>} />);



};

export default PreviewPermissionButton;