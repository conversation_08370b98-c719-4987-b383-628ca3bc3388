import React from 'react';
import { Collapse, Tree } from '@ali/cnd';
import PermissionResource from './PermissionResource';
import PermissionItems from './PermissionItems';

const { Panel } = Collapse;

const PermissionContent = (props) => {
  const {
    data = [],
  } = props;

  return (
    <Collapse>
      {
        data.map((item) => {
          return (
            <Panel
              title={
                <PermissionResource
                  resource={item.Resource}
                  width={750}
                />
              }
            >
              <PermissionItems
                permissions={item.PermissionAssistantItems}
              />
            </Panel>
          )
        })
      }
    </Collapse>
  )
}

export default PermissionContent;