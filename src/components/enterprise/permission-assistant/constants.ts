import { intl } from '@ali/cnd';
const systemStrategy = [
{ key: 'AliyunSLBReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyAccessToSlb") },
{ key: 'AliyunACMFullAccess', label: intl("saenext.enterprise.permission-assistant.constants.ManageAcmPermissionsYouCan") },
{ key: 'AliyunECSReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyEcsPermissionsYou") },
{ key: 'AliyunOOSReadOnlyAccess	', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyOosPermissionYou") },
{ key: 'AliyunBSSReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyBssPermissionYou") },
{ key: 'AliyunARMSReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyArmsPermissionYou") },
{ key: '<PERSON>yun<PERSON>MReadOnlyAccess	', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyRamPermissionsAfter") },
{ key: 'AliyunCloudMonitorReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyCloudMonitoringPermission") },
{ key: 'AliyunContainerRegistryReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ThePermissionsOfTheRead") },
{ key: 'AliyunALBReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyAccessToApplied") },
{ key: 'AliyunYundunCertReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyAccessToAlibaba") },
{ key: 'AliyunEventBridgeReadOnlyAccess', label: intl("saenext.enterprise.permission-assistant.constants.ReadOnlyEbPermissionYou") }];


export {
  systemStrategy };