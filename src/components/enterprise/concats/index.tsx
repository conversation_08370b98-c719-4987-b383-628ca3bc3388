import React, { useState } from 'react';
import { intl, ToolTipCondition, Truncate } from '@ali/cnd';
import CndTable, { ISearch } from '@ali/cnd-table';
import { Button, Copy, Tag, Dialog, Message } from '@ali/cnd';
import * as services from '~/services';
import Events from './components/Events';
import { map, get } from 'lodash';
import Create, { EType } from './components/Create';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';

type Props = {};

export default (props: Props) => {
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [notAuth, setNotAuth] = useState(false);
  const [currentRamExist, setCurrentRamExist] = useState(false);

  const currentUserId = CachedData.getCurrentUserId();

  const fetchData = async (params) => {
    const res = await services.getContacts({
      params: {
        ...params,
        CurrentPage: params.current,
        PageSize: params.pageSize,
      },
      customErrorHandle(err, data, callback) {
        callback && callback();
        return {
          data: [],
          total: 0,
        };
      },
    });
    const { Data = {} } = res || {};
    const { ContactsInfo = [], TotalSize } = Data || {};
    const currentRamExist = ContactsInfo.some((item) => item.Uid === currentUserId);
    setCurrentRamExist(currentRamExist);
    return {
      data: ContactsInfo,
      total: TotalSize,
    };
  };

  const getRamUsers = async () => {
    const User = await getRamUserRequest();
    return map(User, (item) => ({
      UserId: item.UserId,
      UserName: item.UserName,
    }));
  };

  const getRamUserRequest = async (params = {}) => {
    const res = await services.getRamUsers({
      params: {
        MaxItems: 100,
        ...params,
      },
      customErrorHandle: (err, serviceConfig, callback) => {
        if (err.code === 'NoPermission') {
          return {
            error: 'NoPermission',
          };
        } else {
          callback();
        }
      },
    });
    if (res.error === 'NoPermission') {
      setNotAuth(true);
      return [];
    } else {
      setNotAuth(false);
    }
    const { IsTruncated, Marker, Users = {} } = res || {};
    const { User = [] } = Users || {};
    if (IsTruncated && Marker) {
      const moreUser = await getRamUserRequest({ Marker });
      return [...User, ...moreUser];
    } else {
      return User;
    }
  };

  const getRamUserPhone = async (userName) => {
    const res = await services.getRamUser({
      params: { UserName: userName },
    });
    const { User = {} } = res || {};
    return {
      email: get(User, 'Email', ''),
      phone: get(User, 'MobilePhone', ''),
    };
  };

  const operation = (
    <>
      <Create type={EType.create} callback={() => setRefreshIndex(Date.now())}>
        <ToolTipCondition
          show={notAuth && currentRamExist}
          tip={intl.html('saenext.enterprise.concats.YouDoNotHaveThe.new',{
            href:CachedData.confLink('help:sae:contact-management')
          })}
        >
          <Button disabled={notAuth && currentRamExist} type="primary">
            {intl('saenext.enterprise.concats.CreateAContact')}
          </Button>
        </ToolTipCondition>
      </Create>
      <If condition={!notAuth}>
        <Button
          type="primary"
          className="ml-s"
          onClick={() => {
            Dialog.confirm({
              title: intl('saenext.enterprise.concats.MergeContacts'),
              content: (
                <div style={{ width: 460, lineHeight: 1.5 }}>
                  {intl('saenext.enterprise.concats.MergingContactsAssociatesARam')}
                </div>
              ),

              onOk: async () => {
                const users = await getRamUsers();
                const ramList = await Promise.all(
                  map(users, async (v) => {
                    const { phone, email } = await getRamUserPhone(v.UserName);
                    const _phone = phone && phone.indexOf('86-') === 0 ? phone.substring(3) : phone;
                    return { uid: v.UserId, name: v.UserName, email, phone: _phone };
                  }),
                );
                const res = await services.combineContacts({
                  params: { RamInfos: JSON.stringify(ramList) },
                  customErrorHandle(err, data, callback) {
                    Message.success(intl('saenext.enterprise.concats.FailedToMergeContactsPlease'));
                    callback && callback();
                  },
                });
                Message.success(intl('saenext.enterprise.concats.MergedContacts'));
                setRefreshIndex(Date.now());
                return res;
              },
              okProps: { children: intl('saenext.common.dialog.ok') },
              cancelProps: { children: intl('saenext.common.dialog.cancel') },
            });
          }}
        >
          {intl('saenext.enterprise.concats.MergeContacts')}
        </Button>
      </If>
    </>
  );

  const columns = [
    {
      key: 'Name',
      title: intl('saenext.enterprise.concats.UsernameUid'),
      dataIndex: 'Name',
      width: 240,
      lock: 'left',
      cell: (value, index, record) => {
        return (
          <>
            <div className="flex mb-xs">
              <span>{'**' + value.substr(2)}</span>
              {record.IsMainAccount ? (
                <Tag color="yellow" size="small" style={{ marginLeft: 20 }}>
                  {intl('saenext.enterprise.concats.PrimaryAccount')}
                </Tag>
              ) : null}
            </div>
            <Copy text={record.Uid}>
              <span> {record.Uid} </span>
            </Copy>
          </>
        );
      },
    },
    {
      key: 'Phone',
      title: intl('saenext.enterprise.concats.MobilePhoneNumber'),
      dataIndex: 'Phone',
      width: 180,
      cell: (value) => value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
    },
    {
      key: 'Email',
      title: intl('saenext.enterprise.concats.EmailAddress'),
      dataIndex: 'Email',
      width: 300,
      cell: (value) => (value ? '***' + value.substr(3) : '-'),
    },
    {
      key: 'IsApprovingOfficer',
      title: intl('saenext.enterprise.concats.ApproverOrNot'),
      dataIndex: 'IsApprovingOfficer',
      width: 160,
      cell: (value) =>
        value ? intl('saenext.enterprise.concats.Yes') : intl('saenext.enterprise.concats.No'),
    },
    {
      key: 'DingRobot',
      title: intl('saenext.enterprise.concats.DingtalkRobot'),
      dataIndex: 'DingRobot',
      width: 320,
      cell: (value) =>
        value ? (
          <div className="flex">
            <Truncate threshold={250} type="width" position="middle">
              {value}
            </Truncate>
            <Copy text={value} />
          </div>
        ) : (
          <span>-</span>
        ),
    },
    {
      key: 'WechatRobot',
      title: intl('saenext.enterprise.concats.EnterpriseWechatRobot'),
      dataIndex: 'WechatRobot',
      width: 320,
      cell: (value) =>
        value ? (
          <div className="flex">
            <Truncate threshold={250} type="width" position="middle">
              {value}
            </Truncate>
            <Copy text={value} />
          </div>
        ) : (
          <span>-</span>
        ),
    },
    {
      key: 'LarkRobot',
      title: intl('saenext.enterprise.concats.FlyingBookRobot'),
      dataIndex: 'LarkRobot',
      width: 320,
      cell: (value) =>
        value ? (
          <div className="flex">
            <Truncate threshold={250} type="width" position="middle">
              {value}
            </Truncate>
            <Copy text={value} />
          </div>
        ) : (
          <span>-</span>
        ),
    },
    {
      key: 'operating',
      title: intl('saenext.enterprise.concats.Operation'),
      dataIndex: 'operating',
      width: 130,
      lock: 'right',
      cell: (value, index, record) => (
        <Events record={record} notAuth={notAuth} callback={() => setRefreshIndex(Date.now())} />
      ),
    },
  ];

  const search = {
    defaultDataIndex: 'Name',
    defaultSelectedDataIndex: 'Name',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.enterprise.concats.Username'),
        dataIndex: 'Name',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.enterprise.concats.EnterAUsername'),
        },
      },
      {
        label: intl('saenext.enterprise.concats.MobilePhoneNumber.1'),
        dataIndex: 'Phone',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.enterprise.concats.PleaseEnterYourMobilePhone'),
        },
      },
      {
        label: intl('saenext.enterprise.concats.EmailAddress'),
        dataIndex: 'Email',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.enterprise.concats.EnterEmailAddress'),
        },
      },
    ],
  };

  return (
    <>
      <CndTable
        columns={columns}
        fetchData={fetchData}
        style={{ display: 'block' }}
        showRefreshButton
        refreshIndex={refreshIndex}
        operation={operation}
        search={search as ISearch}
        pagination={{ pageSizeList: [10, 20, 50, 100], hideOnlyOnePage: true }}
      />
    </>
  );
};
