import { intl } from '@ali/cnd';
import React, { FC } from 'react';
import { Actions, Dialog, Message } from '@ali/cnd';
import * as services from '~/services';
import { noop } from 'lodash';
import Create, { EType } from './Create';
import CachedData from '~/cache/common';

const { LinkButton } = Actions;

type Props = {
  record: Record<string, any>;
  notAuth: boolean;
  callback: () => void;
};

const Events: FC<Props> = (props) => {
  const { record = {}, notAuth, callback = noop } = props;
  const { Name, Uid } = record;

  const currentUserId = CachedData.getCurrentUserId();

  const disabled = notAuth && currentUserId !== Uid;

  const handleDelete = () => {
    Dialog.confirm({
      title: intl("saenext.concats.components.Events.DeleteAContact"),
      content:
      <p>{intl("saenext.concats.components.Events.AreYouSureYouWant")}
        <span style={{ color: '#F90' }}>{Name}</span> ?
        </p>,

      messageProps: {
        type: 'warning'
      },
      onOk: async () => {
        const res = await services.deleteContact({ params: { Uid } });
        Message.success(intl("saenext.concats.components.Events.TheContactWasDeleted"));
        callback && callback();
        return res;
      }
    });
  };

  return (
    <Actions>
      <Create
        type={EType.edit}
        dataSource={record}
        callback={callback}
      >
        <LinkButton disabled={disabled}>{intl("saenext.concats.components.Events.Edit")}</LinkButton>
      </Create>
      <LinkButton disabled={disabled} onClick={handleDelete}>
        {intl("saenext.concats.components.Events.Delete")}
      </LinkButton>
    </Actions>);

};

export default Events;