import React, { FC, useState, useEffect } from 'react';
import { Field, Form, Input, Message, Select, SlidePanel, intl } from '@ali/cnd';
import { noop, get, map, some, isEmpty } from 'lodash';
import CachedData from '~/cache/common';
import * as services from '~/services';

const FormItem = Form.Item;

type Props = {
  width?: string;
  type: EType;
  dataSource?: Record<string, any>;
  callback: () => void;
};

export enum EType {
  create = 'create',
  edit = 'edit',
}

const TITLE = {
  [EType.edit]: intl('saenext.concats.components.Create.EditContact'),
  [EType.create]: intl('saenext.concats.components.Create.CreateAContact'),
};

const Create: FC<Props> = (props) => {
  const { type, dataSource = {}, callback = noop, children, width } = props;
  const userName = get(window, 'ALIYUN_CONSOLE_CONFIG.ACCOUNT_NAME');
  const mainAccount = get(window, 'ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK');
  const currentId = get(window, 'ALIYUN_CONSOLE_CONFIG.CURRENT_PK');
  const [isShowing, setIsShowing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [ramUsers, setRamUsers] = useState([]);
  const [nameExtra, setNameExtra] = useState('');
  const [userId, setUserId] = useState('');
  const [notAuth, setNotAuth] = useState(false);
  const field = Field.useField();
  const { init, validate, setValues, resetToDefault } = field;

  const getRamUsers = async () => {
    const User = await getRamUserRequest();
    
    const _ramUsers = map(User, (item) => ({
      label: item.UserName,
      value: item.UserName,
    }));
    setRamUsers(_ramUsers);
  };

  const getRamUserRequest = async (params = {}) => {
    const res = await services.getRamUsers({
      params: {
        MaxItems: 100,
        ...params,
      },
      customErrorHandle: (err, serviceConfig, callback) => {
        if (err.code === 'NoPermission') {
          return {
            error: 'NoPermission',
          };
        } else {
          callback();
        }
      },
    });
    if (res.error === 'NoPermission') {
      setNotAuth(true);
      return [];
    } else {
      setNotAuth(false);
    }
    const { IsTruncated, Marker, Users = {} } = res || {};
    const { User = [] } = Users || {};
    if (IsTruncated && Marker) {
      const moreUser = await getRamUserRequest({ Marker });
      return [...User, ...moreUser];
    } else {
      return User;
    }
  };

  const getRamUserPhone = async (userName) => {
    const res = await services.getRamUser({
      params: { UserName: userName },
    });
    const { User = {} } = res || {};
    return User;
  };

  const handleNameChange = async (value) => {
    let _nameExtra = '';
    let _userId = mainAccount;
    let _values = { phone: '', email: '' };
    if (!isEmpty(value)) {
      _nameExtra = intl('saenext.concats.components.Create.PrimaryAccount');
    }
    if (some(ramUsers, (item) => item.value === value)) {
      _nameExtra = intl('saenext.concats.components.Create.RamUser');
      const user = await getRamUserPhone(value);
      const { UserId: __userId, Email, MobilePhone } = user || {};
      _userId = __userId;
      _values = {
        email: Email,
        phone: MobilePhone
          ? MobilePhone.indexOf('86-') === 0
            ? MobilePhone.substring(3)
            : MobilePhone
          : '',
      };
    }
    setUserId(_userId);
    setValues(_values);
    setNameExtra(_nameExtra);
  };

  const handleSubmit = async () => {
    const doSubmit = async (account) => {
      const response =
        type === EType.create
          ? await services.createContact({
              params: {
                RamInfo: JSON.stringify(account),
              },
            })
          : await services.updateContact({
              params: {
                RamInfo: JSON.stringify(account),
              },
            });
      if (!isEmpty(response)) {
        Message.success(
          type === EType.create
            ? intl('saenext.concats.components.Create.ContactCreated')
            : intl('saenext.concats.components.Create.TheContactHasBeenEdited'),
        );
        setIsShowing(false);
        callback();
      }
      setIsProcessing(false);
    };
    validate((error, values) => {
      if (error) return;
      setIsProcessing(true);
      const params = {
        uid: userId || currentId,
        name: get(values, 'name'),
        phone: get(values, 'phone'),
        email: get(values, 'email'),
        dingRobot: get(values, 'ding'),
        wechatRobot: get(values, 'wechatRobot'),
        larkRobot: get(values, 'larkRobot'),
      };
      doSubmit(params);
    });
  };

  const handleCancel = () => {
    resetToDefault();
    setIsShowing(false);
  };

  const handleCompleted = () => {
    if (!isShowing) return;
    getRamUsers();
    if (type === EType.edit) {
      const { Uid: _userId } = dataSource;
      setUserId(_userId);
    }
  };

  return (
    <>
      <span onClick={() => setIsShowing((prev) => !prev)}>{children}</span>
      <SlidePanel
        title={TITLE[type]}
        width={width ? width : '50vw'}
        onCancel={handleCancel}
        onOk={handleSubmit}
        isShowing={isShowing}
        isProcessing={isProcessing}
        okText={intl('button.ok')}
        cancelText={intl('button.cancel')}
        onSlideCompleted={handleCompleted}
        processingText={intl('button.processing')}
      >
        <Form field={field}>
          <FormItem
            required
            label={intl('saenext.concats.components.Create.UsernameAlibabaCloudLogonAccount')}
            help={
              type === EType.edit
                ? get(dataSource, 'IsMainAccount')
                  ? intl('saenext.concats.components.Create.PrimaryAccount')
                  : intl('saenext.concats.components.Create.RamUser')
                : nameExtra
            }
          >
            <Select.AutoComplete
              {...init('name', {
                initValue: dataSource.Name || (notAuth ? userName : ''),
                rules: [
                  {
                    required: true,
                    message: intl('saenext.concats.components.Create.SelectOrEnterAUsername'),
                  },
                ],

                props: {
                  onChange: handleNameChange,
                },
              })}
              style={{ width: '100%' }}
              disabled={notAuth}
              dataSource={ramUsers}
              placeholder={intl('saenext.concats.components.Create.ManuallyEnterAContactWith')}
            />
          </FormItem>
          <FormItem required label={intl('saenext.concats.components.Create.MobilePhoneNumber')}>
            <Input
              {...init('phone', {
                initValue: dataSource.Phone,
                rules: [
                  {
                    required: true,
                    message: intl('saenext.concats.components.Create.PleaseEnterTheCorrectMobile'),
                    pattern: /^1[3456789]\d{9}$/,
                  },
                ],
              })}
              placeholder={intl('saenext.concats.components.Create.PleaseEnterYourMobilePhone')}
            />
          </FormItem>
          <FormItem label={intl('saenext.concats.components.Create.EmailAddress')}>
            <Input
              {...init('email', {
                initValue: dataSource.Email,
                rules: [
                  {
                    message: intl('saenext.concats.components.Create.EnterTheCorrectEmailAddress'),
                    pattern: /^[A-Za-z0-9]+([_\.][A-Za-z0-9]+)*@([A-Za-z0-9\-]+\.)+[A-Za-z]{2,6}/,
                  },
                ],
              })}
              placeholder={intl('saenext.concats.components.Create.EnterEmailAddress')}
            />
          </FormItem>
          <FormItem label={intl('saenext.concats.components.Create.DingtalkRobot')}>
            <Input
              {...init('ding', {
                initValue: dataSource.DingRobot,
              })}
            />
          </FormItem>
          <FormItem label={intl('saenext.concats.components.Create.EnterpriseWechatRobot')}>
            <Input
              {...init('wechatRobot', {
                initValue: dataSource.WechatRobot,
              })}
            />
          </FormItem>
          <FormItem label={intl('saenext.concats.components.Create.FlyingBookRobot')}>
            <Input
              {...init('larkRobot', {
                initValue: dataSource.LarkRobot,
              })}
            />
          </FormItem>
        </Form>
      </SlidePanel>
    </>
  );
};

export default Create;
