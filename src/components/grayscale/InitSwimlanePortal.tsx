import { intl } from '@ali/cnd';
import React from 'react';
import { <PERSON><PERSON>, Dialog, Message, Timeline } from '@ali/cnd';
import CreateSwimlaneGroup from './CreateSwimlaneGroup';
import flowImg from './img/flowImg';

const InitControlPortal = (props) => {
  const { regionId, namespace, refreshSwimlaneGroups } = props;

  return (
    <div className="flow-portal">
      <div className="mb-l">
        <div className="flex justify-between mb-s">
          <div className="flex">
            {/* @ts-ignore */}
            {/* <svg t="1714381615743" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12770" width="16" height="16"><path d="M518.666039 537.33898l-30.157804-30.117647 83.405804-83.44596 30.157804 30.177882-83.405804 83.385725zM0 353.882353l419.137255 250.980392L662.588235 1024 1003.921569 20.078431 0 353.882353zM652.54902 923.607843L449.254902 574.745098 100.392157 366.431373 893.490196 100.392157 655.058824 338.823529l30.117647 30.117647L923.607843 130.509804 652.54902 923.607843z" fill="#0064c8" p-id="12771"></path></svg> */}
            <div style={{ fontSize: 14, fontWeight: 500, lineHeight:'22px' }}>
              {intl(
                'saenext.components.grayscale.InitSwimlanePortal.CustomerECommerceArchitectureCustomer',
              )}
            </div>
          </div>
          <Button
            text
            type="primary"
            style={{ fontWeight: 'normal' }}
            onClick={() => {
              Dialog.show({
                title: intl(
                  'saenext.components.grayscale.InitSwimlanePortal.FullLinkGrayscaleUsagePath',
                ),
                content: (
                  <div>
                    <Message type="notice">
                      {intl(
                        'saenext.components.grayscale.InitSwimlanePortal.PremiseTheFullLinkGray',
                      )}
                    </Message>
                    <Timeline className="mt-l step-timeline">
                      <Timeline.Item
                        dot={<div className="timeline-dot">1</div>}
                        title={intl(
                          'saenext.components.grayscale.InitSwimlanePortal.CreateAGrayscaleApplicationAnd',
                        )}
                        content={intl(
                          'saenext.components.grayscale.InitSwimlanePortal.CreateAGrayscaleApplicationBased',
                        )}
                      />

                      <Timeline.Item
                        dot={<div className="timeline-dot">2</div>}
                        title={intl(
                          'saenext.components.grayscale.InitSwimlanePortal.CreateALaneGroupAnd',
                        )}
                        content={intl(
                          'saenext.components.grayscale.InitSwimlanePortal.CreateALaneGroupOn',
                        )}
                      />

                      <Timeline.Item
                        dot={<div className="timeline-dot">3</div>}
                        title={intl(
                          'saenext.components.grayscale.InitSwimlanePortal.CreateALaneAddA',
                        )}
                        content={intl(
                          'saenext.components.grayscale.InitSwimlanePortal.CreateAGrayscaleLaneFilter',
                        )}
                      />
                    </Timeline>
                  </div>
                ),

                footer: <></>,
              });
            }}
          >
            {intl('saenext.components.grayscale.InitSwimlanePortal.FullLinkGrayscaleFunctionUsage')}
          </Button>
        </div>
        <div>
          {intl('saenext.components.grayscale.InitSwimlanePortal.AfterTheCustomerOrdersThe')}
        </div>
        <div>{intl('saenext.components.grayscale.InitSwimlanePortal.TheProductCenterAndThe')}</div>
      </div>
      <CreateSwimlaneGroup
        regionId={regionId}
        namespace={namespace}
        callback={() => refreshSwimlaneGroups(+new Date())}
      >
        <Button type="primary">
          {intl('saenext.components.grayscale.InitSwimlanePortal.CreateALaneGroupAnd.1')}
        </Button>
      </CreateSwimlaneGroup>
      <div className="flow-control mt-l">
        <span className="flow-title">
          {intl('saenext.components.grayscale.InitSwimlanePortal.FlowControlLinkDiagram')}
        </span>
        <div className="flow-chart">
          <img src={flowImg} />
        </div>
        <div className="flow-legend">
          <span className="circular">
            {intl('saenext.components.grayscale.InitSwimlanePortal.BaselineTraffic')}
          </span>
          <span className="circulara">header = A</span>
          <span className="circularb">header = B</span>
        </div>
      </div>
    </div>
  );
};
export default InitControlPortal;
