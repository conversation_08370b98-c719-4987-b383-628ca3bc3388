import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { Loading, DatePicker, Select } from '@ali/cnd';
import * as services from '~/services';
import { get, map, forEach, isEmpty } from 'lodash';
import QpsDataItem from './QpsDataItem';
import QpsChartItem from './QpsChartItem';
// import { DATA } from './contants';
import moment from 'moment';
import { setSearchParams } from '~/utils/global';
import TextWithBalloon from '~/components/shared/TextWithBalloon';

type Props = {
  regionId: string;
  namespaceId: string;
  groupId: number;
};

const TrafficPreview = (props: Props) => {
  const { namespaceId, groupId } = props;
  const swimlaneGroupsRef = useRef(new Map());
  const [loading, setLoading] = useState(false);
  const [swimlaneGroups, setSwimlaneGroups] = useState([]);
  const [currentSwimlaneGroupId, setCurrentSwimlaneGroupId] = useState(groupId);
  const [playbackDate, setPlaybackDate] = useState(new Date());
  const _timestamp = Date.parse(playbackDate.toString());
  const [playbackTimestamp, setPlaybackTimestamp] = useState(_timestamp);
  const [swimlaneGroupApps, setSwimlaneGroupApps] = useState([]);

  useEffect(() => {
    getSwimlaneGroups();
  }, [namespaceId, currentSwimlaneGroupId, playbackTimestamp]);

  const getSwimlaneGroups = async () => {
    if (!namespaceId || !currentSwimlaneGroupId) return;
    setLoading(true);
    if (isEmpty(swimlaneGroups)) {
      const res = await services.getSwimlaneGroups({
        params: {
          NamespaceId: namespaceId,
        },
        customErrorHandle: (err, data, callback) => {
          callback && callback();
          setLoading(false);
        },
      });
      const { Data = [] } = res || {};
      const _swimlaneGroups = [];
      forEach(Data, (group) => {
        const { GroupId, GroupName } = group;
        _swimlaneGroups.push({
          ...group,
          value: GroupId,
          label: GroupName,
        });
        swimlaneGroupsRef.current.set(GroupId, group);
      });
      setSwimlaneGroups(_swimlaneGroups);
    }

    const _swimlaneGroup = swimlaneGroupsRef.current.get(currentSwimlaneGroupId);
    const _grayApps = get(_swimlaneGroup, 'Apps', []);

    const data = await Promise.all(
      map(_grayApps, async (grayApp) => {
        const { AppId, AppName, MseAppId, MseAppName, MseNamespaceId } = grayApp;
        const res = await services.getSwimlaneGroupAppOverview({
          params: {
            Namespace: MseNamespaceId,
            AppId: MseAppId,
            StartTime:
              playbackTimestamp - 5 * 60 * 1000 || new Date().getTime() - 15 * 1000 - 5 * 60 * 1000,
            EndTime: playbackTimestamp - 15 * 1000 || new Date().getTime() - 15 * 1000,
          },
          customErrorHandle: (err, data, callback) => {
            callback && callback();
            setLoading(false);
          },
        });
        const { Data = {} } = res || {};

        return {
          ...Data,
          AppId,
          AppName,
          MseNamespaceId,
          MseAppId,
          MseAppName,
          // CurMetrics: DATA,
        };
      }),
    );
    setSwimlaneGroupApps(data);
    setLoading(false);
  };

  const changeSwimlaneGroup = (value) => {
    setCurrentSwimlaneGroupId(value);
    setSearchParams({ groupId: `${value}` });
  };

  const disabledDate = (current) => {
    const { _d } = current;
    let ret = false;
    let twodays = 2 * 24 * 60 * 60 * 1000;
    const nowDate = Date.parse(new Date().toString());
    const theDate = Date.parse(_d.toString());

    if (!theDate) {
      return ret;
    }
    if (theDate > nowDate || theDate < nowDate - twodays) {
      ret = true;
    }
    return ret;
  };

  const handleDateOk = (current) => {
    const { _d } = current;
    const _timestamp = Date.parse(_d.toString());
    setPlaybackDate(_d);
    setPlaybackTimestamp(_timestamp);
  };

  const handleDateChange = (current) => {
    const { _d } = current;
    setPlaybackDate(_d);
  };

  const handleDateVisible = (visible, type) => {
    if (!visible && type !== 'okBtnClick' && type !== 'docClick') {
      setPlaybackDate(new Date());
    }
  };

  return (
    <>
      <div className="mb-s flex flow-bars" style={{ width: 'calc(50% - 8px)' }}>
        <Select
          placeholder={intl('saenext.components.grayscale.TrafficPreview.SelectALaneGroup')}
          style={{ width: '50%' }}
          dataSource={swimlaneGroups}
          value={currentSwimlaneGroupId}
          label={
            <div className="select-label">
              {intl('saenext.components.grayscale.TrafficPreview.LaneGroup')}
            </div>
          }
          onChange={changeSwimlaneGroup}
        />

        <DatePicker
          hasClear={false}
          placeholder={intl(
            'saenext.components.grayscale.TrafficPreview.SelectTimeForwardPlaybackFor',
          )}
          onChange={handleDateChange}
          onOk={handleDateOk}
          onVisibleChange={handleDateVisible}
          disabledDate={disabledDate}
          style={{ width: '50%', marginLeft: 8 }}
          value={moment(playbackDate, 'YYYY-MM-DD HH:mm:ss', true)}
          label={
            <div className="select-label">
              <TextWithBalloon
                align="t"
                text={intl('saenext.components.grayscale.TrafficPreview.PlaybackTime')}
                tips={intl(
                  'saenext.components.grayscale.TrafficPreview.SelectTimeForwardPlaybackFor',
                )}
              />
            </div>
          }
          showTime={{
            disabledHours: (index) => {
              const date = new Date();
              const hours = date.getHours();
              if (playbackDate.getDate() < date.getDate()) {
                return hours > index;
              } else {
                return hours < index;
              }
            },
          }}
        />
      </div>
      <Loading visible={loading} className="full-width full-height">
        <div className="flow-container">
          {map(swimlaneGroupApps, (grayApp) => {
            const { AppName } = grayApp;
            const qpsData = get(grayApp, 'CurMetricsFm', {});
            const qpsMonitor = get(grayApp, 'CurMetrics', []);
            return (
              <div className="swimlane-panel">
                <QpsDataItem title={AppName} data={qpsData} />
                <QpsChartItem data={qpsMonitor} />
              </div>
            );
          })}
        </div>
      </Loading>
    </>
  );
};
export default TrafficPreview;
