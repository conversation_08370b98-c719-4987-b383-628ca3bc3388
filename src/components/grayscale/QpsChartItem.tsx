import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Button, Icon } from '@ali/cnd';
import { Wline } from '@alife/aisc-widgets';
import { get, noop, isEqual, forEach, keys, isEmpty, head } from 'lodash';

let config = {
  area: true,
  axis: {
    auto: true,
  },
  xAxis: {},
  yAxis: {},
  tooltip: {
    lockable: false,
  },
  legend: {
    visible: true,
    position: 'bottom',
    align: 'center',
    showData: false,
  },
  insight: false,
  zoom: true,
};

const QpsChartItem = (props) => {
  const { data: dataSource = [], callback = noop } = props;
  const [data, setData] = useState([]);

  useEffect(() => {
    if (isEmpty(dataSource)) return;
    const qpsData = [];
    const expQpsData = [];
    let _data = [
      { name: intl('saenext.components.grayscale.QpsChartItem.TotalQps'), data: qpsData },
      { name: intl('saenext.components.grayscale.QpsChartItem.AbnormalQps'), data: expQpsData },
    ];

    const tagValues = get(head(dataSource), 'TagValues', {});
    const tagKeys = keys(tagValues);
    let tagsData = new Map();
    forEach(tagKeys, (tagKey) => {
      const tag =
        tagKey === 'null' ? intl('saenext.components.grayscale.QpsChartItem.Unmarked') : tagKey;
      tagsData.set(tagKey, { name: `${tag} QPS`, data: [] });
    });

    forEach(dataSource, (qps) => {
      const { Qps, ExpQps, TagValues = {}, Timestamp } = qps;
      qpsData.push([Timestamp, Qps]);
      expQpsData.push([Timestamp, ExpQps]);
      if (!isEmpty(TagValues)) {
        const tagKeys = keys(TagValues);
        forEach(tagKeys, (key) => {
          const tagData = tagsData.get(key);
          const value = TagValues[key];
          tagData.data.push([Timestamp, get(value, 'Qps', 0)]);
        });
      }
    });

    forEach(tagKeys, (tagKey) => {
      const data = tagsData.get(tagKey);
      _data.push(data);
    });
    setData(_data);
  }, [JSON.stringify(dataSource)]);

  return (
    <>
      <div className="qps-chart">
        {!isEqual(callback, noop) ? (
          <div className="flex justify-between">
            <span>
              {intl('saenext.components.grayscale.QpsChartItem.ApplicationQpsMonitoring')}
            </span>
            <Button
              text
              type="primary"
              onClick={() => {
                callback && callback();
              }}
              style={{ fontWeight: 'normal' }}
            >
              <span>{intl('saenext.components.grayscale.QpsChartItem.TrafficDetails')}</span>
              <Icon type="forward" />
            </Button>
          </div>
        ) : null}

        <div className="mt-l">
          {/* @ts-ignore */}
          <Wline config={config} data={data} height={isEqual(callback, noop) ? 236 : 200} />
        </div>
      </div>
    </>
  );
};
export default QpsChartItem;
