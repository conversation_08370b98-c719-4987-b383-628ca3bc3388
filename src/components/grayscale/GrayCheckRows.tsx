import { intl } from '@ali/cnd';
import React from 'react';
import { Button, LinkButton, Grid, Input, Select, Icon, Balloon } from '@ali/cnd';
import { get } from 'lodash';
import { Entrys } from './CreateSwimlaneGroup';

const { Row, Col } = Grid;
const JavaParamTypes = [
  { label: 'Cookie', value: 'cookie' },
  { label: 'Header', value: 'header' },
  { label: 'Parameter', value: 'param' },
  { label: 'Body Content', value: 'body' },
];

const JavaConditions = [
  { label: '==', value: '==' },
  { label: '!=', value: '!=' },
  { label: 'in', value: 'list' },
  { label: intl('saenext.components.grayscale.GrayCheckRows.Percentage'), value: '%' },
  { label: intl('saenext.components.grayscale.GrayCheckRows.RegularMatching'), value: 'regexp' },
];

const MseParamTypes = [
  { label: 'Header', value: 'header' },
  { label: 'Parameter', value: 'param' },
];

export const MseConditionLabels = {
  PRE: intl('saenext.components.grayscale.GrayCheckRows.PrefixMatching'),
  EQUAL: '==',
  ERGULAR: intl('saenext.components.grayscale.GrayCheckRows.RegularMatching'),
  '==':'==',
  '!=':'!=',
  'list':'in',
  '%':intl('saenext.components.grayscale.GrayCheckRows.Percentage'),
  'regexp':intl('saenext.components.grayscale.GrayCheckRows.RegularMatching'),
};

export const JavaConditionLabels = {
  '==': '==',
  '!=': '!=',
  list: 'in',
  '%': intl('saenext.components.grayscale.GrayCheckRows.Percentage'),
  regexp: intl('saenext.components.grayscale.GrayCheckRows.RegularMatching'),
};

const TypeData = [
  { value: 'header', label: 'Header' },
  { value: 'cookie', label: 'Cookie'},
  { value: 'param', label: 'Parameter'},
];

export default (props) => {
  const { swimlaneEntry = Entrys.MSE, swimVersion, onChange } = props;
  const MseConditions =
    swimVersion === '2'
      ? [
          { value: '==', label: '==' },
          { value: '!=', label: '!=' },
          { value: 'list', label: 'in' },
          { value: '%', label: intl('saenext.components.grayscale.GrayCheckRows.Percentage') },
          {
            value: 'regexp',
            label: intl('saenext.components.grayscale.GrayCheckRows.RegularMatching'),
          },
          {
            value: 'PRE',
            label: intl('saenext.components.grayscale.GrayCheckRows.PrefixMatching'),
          },
        ]
      : [
          {
            label: intl('saenext.components.grayscale.GrayCheckRows.PrefixMatching'),
            value: 'PRE',
          },
          { label: '==', value: 'EQUAL' },
          {
            label: intl('saenext.components.grayscale.GrayCheckRows.RegularMatching'),
            value: 'ERGULAR',
          },
        ];
  const handleChange = (key, value, index, reg?) => {
    onChange &&
      onChange(
        get(props, 'value', []).map((i, idx) => {
          if (key === 'Condition') {
            if (value !== 'list' && i.Condition === 'list') {
              i.Value = i.Value?.join(',') || '';
            }
            if (value === 'list' && i.Condition !== 'list') {
              i.Value = i.Value?.split(',') || [];
            }
          }
          if (idx === index) {
            if (i.Type === 'ERGULAR' && !reg) {
              return {
                ...i,
                [key]: value,
                RegCheck: reg,
              };
            } else {
              if (i.hasOwnProperty('RegCheck')) {
                delete i.RegCheck;
              }
              return {
                ...i,
                [key]: value,
              };
            }
          }
          return i;
        }),
      );
  };

  return (
    <div style={{ background: '#f6f6f6', padding: 16 }}>
      <Row style={{ color: '#555' }}>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.grayscale.GrayCheckRows.ParameterType')}
        </Col>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.grayscale.GrayCheckRows.Parameter')}
        </Col>
        <Col span="6" style={{ paddingRight: 10 }}>
          {intl('saenext.components.grayscale.GrayCheckRows.Condition')}
        </Col>
        <Col span="6">{intl('saenext.components.grayscale.GrayCheckRows.Value')}</Col>
      </Row>
      {get(props, 'value', []).map((item, index) => (
        <Row key={index} style={{ marginTop: 10 }}>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Select
              value={item?.Type}
              placeholder={intl('saenext.components.grayscale.GrayCheckRows.PleaseSelect')}
              dataSource={
                swimVersion === '2'
                  ? TypeData
                  : swimlaneEntry === Entrys.Java
                  ? JavaParamTypes
                  : MseParamTypes
              }
              style={{ width: '100%' }}
              onChange={v => handleChange('Type', v, index)}
            />
          </Col>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Input
              style={{ width: '100%' }}
              value={item?.Name}
              placeholder={intl('saenext.components.grayscale.GrayCheckRows.PleaseEnter')}
              onChange={v => handleChange('Name', v, index)}
              onPressEnter={e => {
                e.preventDefault();
              }}
            />
          </Col>
          <Col span={6} style={{ paddingRight: 10 }}>
            <Select
              value={item?.Condition}
              placeholder={intl('saenext.components.grayscale.GrayCheckRows.PleaseSelect')}
              dataSource={swimlaneEntry === Entrys.Java ? JavaConditions : MseConditions}
              style={{ width: '100%' }}
              onChange={v => handleChange('Condition', v, index)}
              itemRender={(c)=>{
                if(c.value==='%'){
                  return (
                    <div>
                      {c.label}
                      <Balloon
                        trigger={
                          <Icon
                            type="prompt"
                            size="xs"
                            style={{ color: '#888', cursor: 'pointer', marginLeft: 4 }}
                          />
                        }
                        align="t"
                        closable={false}
                      >
                        {intl.html("saenext.components.grayscale.GrayCheckRows.Condition.Percentage.Tip")}
                      </Balloon>
                    </div>
                  );
                }else{
                  return c.label
                }
              }}
            />
          </Col>
          <Col span={6}>
            {item?.Condition !== 'list' && (
              <Input
                style={{ width: 'calc(100% - 22px)' }}
                value={item?.Value}
                placeholder={intl('saenext.components.grayscale.GrayCheckRows.PleaseEnter')}
                onChange={v => handleChange('Value', v, index)}
                onPressEnter={e => {
                  e.preventDefault();
                }}
              />
            )}
            {item?.Condition === 'list' && (
              <Select
                style={{ width: 'calc(100% - 22px)' }}
                mode="tag"
                value={item?.Value}
                dataSource={item?.Value}
                onChange={v => handleChange('Value', v, index)}
              />
            )}

            <Button
              text
              style={{ width: 20 }}
              disabled={props.value?.length > 1 ? false : true}
              onClick={() => {
                props.onChange(props.value.filter((i, idx) => idx !== index));
              }}
            >
              <div>
                <Icon
                  className="rows-delete"
                  size="small"
                  style={{ marginLeft: 5 }}
                  type="delete"
                />
              </div>
            </Button>
          </Col>
        </Row>
      ))}

      {get(props, 'value', []).length < 10 && (
        <div style={{ marginTop: 10 }}>
          <LinkButton
            onClick={() => {
              props.onChange([...props.value, { Type: '', Name: '', Condition: '', Value: '' }]);
            }}
          >
            <div>
              <Icon size="xs" type="plus" />
            </div>
            <span style={{ marginLeft: 5 }}>
              {intl('saenext.components.grayscale.GrayCheckRows.AddGrayscaleConditions')}
            </span>
          </LinkButton>
        </div>
      )}
    </div>
  );
};
