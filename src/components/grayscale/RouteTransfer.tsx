import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Checkbox, Grid, Input, Icon, Balloon, Button, Loading } from '@ali/cnd';

const { Row, Col } = Grid;

const RouteTransfer = (props) => {
  const { value, dataSource, handleChecked, titles, refreshData, disabled } = props;
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [searchVal, setSearchVal] = useState('');
  const [selectedSearchVal, setSelectedSearchVal] = useState('');
  const [searchList, setSearchList] = useState(dataSource);
  const [filteredValue, setFilteredValue] = useState(value);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (value?.length == 0) {
      setIndeterminate(false);
      setCheckAll(false);
    } else if (value?.length === dataSource?.length) {
      setIndeterminate(false);
      setCheckAll(true);
    } else {
      setIndeterminate(true);
    }
  }, [value]);

  useEffect(() => {
    if (!searchVal) {
      setSearchList(dataSource);
    }
  }, [dataSource]);

  useEffect(() => {
    if (selectedSearchVal) {
      const filtered = value.filter((val) =>
        dataSource.find(
          (item) =>
            item.value === val &&
            item.label.toLowerCase().includes(selectedSearchVal.toLowerCase()),
        ),
      );
      setFilteredValue(filtered);
    } else {
      // 如果搜索框为空，则展示所有选中项
      setFilteredValue(value);
    }
  }, [selectedSearchVal, value, dataSource]);

  return (
    <Row className="transfer-new">
      <Col span={12}>
        <div className="transfer-left-box">
          <div className="transfer-left-title">
            <span>{titles[0]}</span>
            <Button
              type="primary"
              text
              onClick={async () => {
                setLoading(true);
                await refreshData();
                setLoading(false);
              }}
            >
              <div>
                <Icon size="xs" type="refresh" />
              </div>
              <span style={{ fontWeight: 'normal' }}>
                {intl('saenext.components.grayscale.RouteTransfer.Refresh')}
              </span>
            </Button>
          </div>
          <Input
            style={{ width: '100%', margin: '10px 0', padding: '0 10px' }}
            innerAfter={<Icon type="search" size="xs" style={{ margin: 4 }} />}
            value={searchVal}
            onChange={(value: string) => {
              setSearchVal(value);
              if (value) {
                const newList = dataSource.filter((item) => item.label.indexOf(value) > -1);
                setSearchList(newList);
              } else {
                setSearchList(dataSource);
              }
            }}
          />

          <Loading visible={loading} style={{ width: '100%', height: 283 }}>
            <Checkbox.Group
              value={value}
              disabled={disabled}
              onChange={(value) => {
                handleChecked(value);
              }}
              className="transfer-checkbox-box"
            >
              {searchList.map((item) => (
                <Checkbox key={item.value} value={item.value} className="transfer-checkbox-li">
                  <Balloon.Tooltip
                    trigger={<div className="transfer-checkbox-li-text">{item.label}</div>}
                    align="t"
                  >
                    {item.label}
                  </Balloon.Tooltip>
                </Checkbox>
              ))}
            </Checkbox.Group>
          </Loading>
        </div>
        <div className="transfer-footer">
          <Checkbox
            checked={checkAll}
            disabled={disabled}
            indeterminate={indeterminate}
            onChange={(checked) => {
              const allId = [];
              dataSource.forEach((item) => allId.push(item.value));
              if (checked) {
                handleChecked(allId);
                setCheckAll(true);
              } else {
                handleChecked([]);
                setCheckAll(false);
              }
            }}
          >
            {intl('saenext.components.grayscale.RouteTransfer.Selected')}

            {value?.length}
            {intl('saenext.components.grayscale.RouteTransfer.Item')}
          </Checkbox>
        </div>
      </Col>
      <Col span={12}>
        <div className="transfer-right-box">
          <div className="transfer-right-title">{titles[1]}</div>
          <Input
            style={{ width: '100%', margin: '10px 0', padding: '0 10px' }}
            innerAfter={<Icon type="search" size="xs" style={{ margin: 4 }} />}
            value={selectedSearchVal}
            onChange={(value:string) => {
              setSelectedSearchVal(value);
            }}
          />

          {filteredValue?.map((item) => (
            <div key={item} className="transfer-right-tag-li">
              <div className="transfer-right-tag-text">
                {dataSource
                  ?.filter((i) => i.value === item)
                  .map(
                    (i) => (
                      <span key={i.value}>{i.label}</span>
                    ), // 使用经过搜索过滤后的项目来渲染
                  )}
                <Button
                  text
                  type="primary"
                  disabled={disabled}
                  className="transfer-right-tag-close"
                  style={{ marginLeft: '10px' }}
                  onClick={() => {
                    handleChecked(value.filter((i) => i !== item));
                  }}
                >
                  <div>
                    <Icon size="xs" type="close" />
                  </div>
                </Button>
              </div>
            </div>
          ))}
        </div>
        <div className="transfer-footer">
          <Button
            type="primary"
            text
            disabled={disabled}
            className="transfer-footer-delete"
            onClick={() => {
              handleChecked([]);
              setCheckAll(false);
              setSelectedSearchVal('');
            }}
          >
            <div>
              <Icon size="xs" type="close" />
            </div>
            <span style={{ fontWeight: 'normal' }}>
              {intl('saenext.components.grayscale.RouteTransfer.RemoveAll')}
            </span>
          </Button>
        </div>
      </Col>
    </Row>
  );
};

export default RouteTransfer;
