import React, { useState, useEffect } from 'react';
import { Loading } from '@ali/cnd';
import * as services from '~/services';
import InitSwimlanePortal from './InitSwimlanePortal';
import SwimlaneGroupPortal from './SwimlaneGroupPortal';
import { get, map } from 'lodash';

type Props = {
  regionId: string;
  namespace: {
    NamespaceId: string;
    NamespaceName: string;
    VpcId?: string;
    VpcName?: string;
  };
};

const Grayscale = (props: Props) => {
  const { regionId, namespace } = props;
  const namespaceId = get(namespace, 'NamespaceId', '');
  const [loading, setLoading] = useState(false);
  const [swimlaneGroups, setSwimlaneGroups] = useState([]);
  const [hasSwimlaneGroup, setHasSwimlaneGroup] = useState(false);
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    getSwimlaneGroups();
  }, [refreshIndex]);

  useEffect(() => {
    setSwimlaneGroups([]);
    setHasSwimlaneGroup(false);
    getSwimlaneGroups();
  }, [namespaceId]);


  const getSwimlaneGroups = async () => {
    if (!namespaceId) return;
    setLoading(true);
    const res = await services.getSwimlaneGroups({
      params: {
        NamespaceId: namespaceId,
      },
      customErrorHandle: (err, data, callback) => {
        callback && callback();
        setLoading(false);
        setSwimlaneGroups([]);
      }
    });
    const { Data = [] } = res || {};
    const _swimlaneGroups = map(Data, group => {
      const { GroupId, GroupName } = group;
      return {
        ...group,
        value: GroupId,
        label: GroupName,
      };
    });
    setSwimlaneGroups(_swimlaneGroups);
    setLoading(false);
    setHasSwimlaneGroup(!!Data.length);
  };

  return (
    <Loading
      visible={loading}
      className="full-width full-height"
    >
      {
        hasSwimlaneGroup ? (
          <SwimlaneGroupPortal 
            regionId={regionId} 
            // @ts-ignore
            namespace={namespace} 
            swimlaneGroups={swimlaneGroups}
            refreshSwimlaneGroups={setRefreshIndex}
          />
        ) : (
          <InitSwimlanePortal 
            regionId={regionId} 
            // @ts-ignore
            namespace={namespace} 
            refreshSwimlaneGroups={setRefreshIndex}
          />
        )
      }
    </Loading>
  )
}
export default Grayscale;