import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Loading, Actions, Tag, Button, StatusIndicator, Dialog, Message } from '@ali/cnd';
import CreateSwimlane from './CreateSwimlane';
import * as services from '~/services';
import { get, map, isEmpty, head, keys } from 'lodash';
import CndTable from '@ali/cnd-table';
import { Entrys } from './CreateSwimlaneGroup';
import { RouteMode } from './CreateSwimlane';
import Truncate from '@ali/cnd-truncate';
import { MseConditionLabels, JavaConditionLabels } from './GrayCheckRows';

const { LinkButton } = Actions;
const { Group: TagGroup } = Tag;

const SwimlanePanel = (props) => {
  const { regionId, namespace, swimlaneGroup, refreshSwimlaneGroups } = props;
  const namespaceId = get(namespace, 'NamespaceId', '');
  const groupId = get(swimlaneGroup, 'GroupId', '');
  const swimlaneEntry = get(swimlaneGroup, 'EntryAppType', Entrys.MSE);
  const swimlaneRouteMode = get(swimlaneGroup, 'CanaryModel', RouteMode.Content);
  const swimVersion = get(swimlaneGroup, 'SwimVersion','');
  const [loading, setLoading] = useState(false);
  const [hasSwimlane, setHasSwimlane] = useState(false);
  const [headSwimlane, setHeadSwimlane] = useState({});
  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    setRefreshIndex(Date.now());
  }, [namespaceId, groupId]);

  const getSwimlanes = async () => {
    if (!namespaceId || !groupId) return;
    !hasSwimlane && setLoading(true);
    const res = await services.getSwimlanes({
      params: {
        NamespaceId: namespaceId,
        GroupId: groupId,
      },
      customErrorHandle: (err, data, callback) => {
        // callback && callback();
        setLoading(false);
        setHasSwimlane(false);
        return {
          data: [],
          total: 0,
        };
      },
    });
    const { Data: _swimlanes = [] } = res || {};
    const _headSwimlane = head(_swimlanes);
    setHasSwimlane(!!_swimlanes.length);
    setLoading(false);
    setHeadSwimlane(_headSwimlane);
    return {
      data: _swimlanes,
      total: _swimlanes.length,
    };
  };

  const handleCloseSwinlane = (record) => {
    Dialog.confirm({
      title: intl('saenext.components.grayscale.SwimlanePanel.CloseTheLane'),
      content: (
        <div>
          {intl('saenext.components.grayscale.SwimlanePanel.AreYouSureYouWant')}
          <span style={{ color: '#F90' }}>{record.LaneName}</span> ?
        </div>
      ),

      messageProps: {
        type: 'warning',
      },
      onOk: async () => {
        const res = await services.updateSwimlaneEnable({
          params: {
            GroupId: groupId,
            LaneId: record.LaneId,
            Namespace: swimlaneGroup.MseNamespaceId,
            Enable: false,
          },
        });
        Message.success(intl('saenext.components.grayscale.SwimlanePanel.ClosedLaneSuccessfully'));
        setRefreshIndex(Date.now());
        return res;
      },
    });
  };

  const handleStartSwinlane = (record) => {
    Dialog.confirm({
      title: intl('saenext.components.grayscale.SwimlanePanel.OpenLane'),
      content: (
        <div>
          {intl('saenext.components.grayscale.SwimlanePanel.AreYouSureYouWant.1')}
          <span style={{ color: '#F90' }}>{record.LaneName}</span> ?
        </div>
      ),

      messageProps: {
        type: 'warning',
      },
      onOk: async () => {
        const res = await services.updateSwimlaneEnable({
          params: {
            GroupId: groupId,
            LaneId: record.LaneId,
            Namespace: swimlaneGroup.MseNamespaceId,
            Enable: true,
          },
        });
        Message.success(intl('saenext.components.grayscale.SwimlanePanel.TheLaneHasBeenOpened'));
        setRefreshIndex(Date.now());
        return res;
      },
    });
  };

  const handleDeleteSwinlane = (record) => {
    Dialog.confirm({
      title: intl('saenext.components.grayscale.SwimlanePanel.DeleteALane'),
      content: (
        <div>
          {intl('saenext.components.grayscale.SwimlanePanel.AreYouSureYouWant.2')}
          <span style={{ color: '#F90' }}>{record.LaneName}</span> ?
        </div>
      ),

      messageProps: {
        type: 'warning',
      },
      onOk: async () => {
        const res = await services.deleteSwimlane({
          params: {
            GroupId: groupId,
            LaneId: record.LaneId,
            Namespace: swimlaneGroup.MseNamespaceId,
          },
        });
        Message.success(intl('saenext.components.grayscale.SwimlanePanel.TheLaneHasBeenDeleted'));
        setRefreshIndex(Date.now());
        return res;
      },
    });
  };

  const operation = (
    <CreateSwimlane
      regionId={regionId}
      namespace={namespace}
      hasSwimlane={hasSwimlane}
      headSwimlane={headSwimlane}
      swimlaneGroup={swimlaneGroup}
      callback={() => {
        setHasSwimlane(true);
        setRefreshIndex(Date.now());
      }}
    >
      <Button type="primary">
        {intl('saenext.components.grayscale.SwimlanePanel.CreateALane')}
      </Button>
    </CreateSwimlane>
  );

  const mseExtraColumns = [
    {
      key: 'MseGatewayEntryRule',
      title: intl('saenext.components.grayscale.SwimlanePanel.BaselineRouting'),
      dataIndex: 'MseGatewayEntryRule',
      width: 160,
      cell: (value) => {
        const { Routes = [], IndependentPercentageEnable = false, PercentageByRoute = {} } = value;
        if (IndependentPercentageEnable) {
          return (
            <TagGroup>
              {map(Routes, (route) => {
                const { RouteName, RouteId } = route;
                return (
                  <Tag type="normal" size="small">
                    {`${RouteName} (${PercentageByRoute[RouteId]}%)`}
                  </Tag>
                );
              })}
            </TagGroup>
          );
        }
        return (
          <TagGroup>
            {map(Routes, (route) => {
              const { RouteName } = route;
              return (
                <Tag type="normal" size="small">
                  {RouteName || '-'}
                </Tag>
              );
            })}
          </TagGroup>
        );
      },
    },
    swimlaneRouteMode === RouteMode.Content
      ? {
          key: 'MseGatewayEntryRule',
          title: intl('saenext.components.grayscale.SwimlanePanel.ContentGray'),
          dataIndex: 'MseGatewayEntryRule',
          width: 200,
          cell: (value) => {
            if (isEmpty(value)) return '-';
            const { Conditions = [] } = value;
            return (
              <>
                <div>{intl('saenext.components.grayscale.SwimlanePanel.ConditionAnd')}</div>
                {map(Conditions, (condition) => {
                  return (
                    <div>
                      {`${condition.Type}: [${condition.Name}] ${
                        MseConditionLabels[condition.Condition]
                      } ${condition.Value}`}
                    </div>
                  );
                })}
              </>
            );
          },
        }
      : {
          key: 'MseGatewayEntryRule',
          title: intl('saenext.components.grayscale.SwimlanePanel.ProportionalGrayScale'),
          dataIndex: 'MseGatewayEntryRule',
          width: 160,
          cell: (value) => {
            const { Percentage = 0, IndependentPercentageEnable = false } = value;
            if (IndependentPercentageEnable) {
              return <span>-</span>;
            }
            return <span>{`${Percentage}%`}</span>;
          },
        },
  ];

  const apigExtraColumns=[
    swimlaneRouteMode === RouteMode.Content
    ? {
        key: 'MseGatewayEntryRule',
        title: intl('saenext.components.grayscale.SwimlanePanel.ContentGray'),
        dataIndex: 'MseGatewayEntryRule',
        width: 200,
        cell: (value,index,record) => {
          if (isEmpty(value)) return '-';
          const { Conditions = [], ConditionJoiner = 'AND' } = value;
          return (
            <>
              <div>{ConditionJoiner}</div>
              {map(Conditions, (condition) => {
                return (
                  <div>
                    {`${condition.Type}: [${condition.Name}] ${
                      MseConditionLabels[condition.Condition]
                    } ${condition.Condition === 'list'?`[${condition.Value}]`:condition.Value}`}
                  </div>
                );
              })}
            </>
          );
        },
      }
    : {
        key: 'MseGatewayEntryRule',
        title: intl('saenext.components.grayscale.SwimlanePanel.ProportionalGrayScale'),
        dataIndex: 'MseGatewayEntryRule',
        width: 160,
        cell: (value) => {
          const { Percentage = 0, IndependentPercentageEnable = false } = value;
          if (IndependentPercentageEnable) {
            return <span>-</span>;
          }
          return <span>{`${Percentage}%`}</span>;
        },
      },
  ]

  const javaExtraColumns = [
    {
      key: 'AppEntryRule',
      title: intl('saenext.components.grayscale.SwimlanePanel.RequestPath'),
      dataIndex: 'AppEntryRule',
      width:160,
      cell: (value) => {
        if (isEmpty(value)) return '-';
        const { Paths = [], IndependentPercentageEnable = false, PercentageByPath = {} } = value;
        if (IndependentPercentageEnable) {
          const paths = keys(PercentageByPath);
          return (
            <TagGroup>
              {map(paths, (path) => {
                return (
                  <Tag type="normal" size="small">
                    {`${path} (${PercentageByPath[path]}%)`}
                  </Tag>
                );
              })}
            </TagGroup>
          );
        }
        return (
          <TagGroup>
            {map(Paths, (path) => {
              return (
                <Tag type="normal" size="small">
                  {path || '-'}
                </Tag>
              );
            })}
          </TagGroup>
        );
      },
    },
    swimlaneRouteMode === RouteMode.Content
      ? {
          key: 'AppEntryRule',
          title: intl('saenext.components.grayscale.SwimlanePanel.ContentGray'),
          dataIndex: 'AppEntryRule',
          width:200,
          cell: (value) => {
            if (isEmpty(value)) return '-';
            const { ConditionJoiner, Conditions = [] } = value;
            return (
              <>
                <div>
                  {intl('saenext.components.grayscale.SwimlanePanel.ConditionConditionjoiner', {
                    ConditionJoiner: ConditionJoiner,
                  })}
                </div>
                {map(Conditions, (condition) => {
                  return (
                    <div>
                      {`${condition.Type}: [${condition.Name}] ${
                        JavaConditionLabels[condition.Condition]
                      } ${condition.Condition === 'list'?`[${condition.Value}]`:condition.Value}`}
                    </div>
                  );
                })}
              </>
            );
          },
        }
      : {
          key: 'AppEntryRule',
          title: intl('saenext.components.grayscale.SwimlanePanel.ProportionalGrayScale'),
          dataIndex: 'AppEntryRule',
          width:160,
          cell: (value) => {
            const { Percentage = 0, IndependentPercentageEnable = false } = value;
            if (IndependentPercentageEnable) {
              return <span>-</span>;
            }
            return <span>{`${Percentage}%`}</span>;
          },
        },
  ];

  let columns = [
    {
      key: 'LaneName',
      title: intl('saenext.components.grayscale.SwimlanePanel.LaneName'),
      width: 140,
      dataIndex: 'LaneName',
      cell: (value) => {
        return (
          <div className="flex">
            <Truncate threshold={100} type="width" position="end">
              {value}
            </Truncate>
          </div>
        );
      },
    },
    {
      key: 'LaneTag',
      title: intl('saenext.components.grayscale.SwimlanePanel.LaneLabel'),
      width: 140,
      dataIndex: 'LaneTag',
      cell: (value) => {
        return (
          <TagGroup>
            <Tag type="normal" size="small">
              {value}
            </Tag>
          </TagGroup>
        );
      },
    },
    {
      key: 'Apps',
      title: intl('saenext.components.grayscale.SwimlanePanel.ListOfLaneApplications'),
      dataIndex: 'Apps',
      width: 200,
      cell: (value) => {
        return (
          <TagGroup>
            {map(value, (app) => {
              const { AppName } = app;
              return (
                <Tag type="normal" size="small">
                  {AppName}
                </Tag>
              );
            })}
          </TagGroup>
        );
      },
    },
    {
      key: 'Enable',
      title: intl('saenext.components.grayscale.SwimlanePanel.Status'),
      width: 100,
      dataIndex: 'Enable',
      cell: (value) => {
        if (value) {
          return (
            <StatusIndicator type="success">
              {intl('saenext.components.grayscale.SwimlanePanel.Enabled')}
            </StatusIndicator>
          );
        }
        return (
          <StatusIndicator type="disabled">
            {intl('saenext.components.grayscale.SwimlanePanel.Closed')}
          </StatusIndicator>
        );
      },
    },
    {
      key: 'operating',
      title: intl('saenext.components.grayscale.SwimlanePanel.Operation'),
      dataIndex: 'operating',
      width: 140,
      lock: 'right',
      cell: (value, index, record) => (
        <Actions>
          {record.Enable ? (
            <LinkButton onClick={() => handleCloseSwinlane(record)}>
              {intl('saenext.components.grayscale.SwimlanePanel.Close')}
            </LinkButton>
          ) : (
            <LinkButton onClick={() => handleStartSwinlane(record)}>
              {intl('saenext.components.grayscale.SwimlanePanel.Enable')}
            </LinkButton>
          )}

          <CreateSwimlane
            type="edit"
            regionId={regionId}
            namespace={namespace}
            hasSwimlane={true}
            headSwimlane={headSwimlane}
            swimlaneGroup={swimlaneGroup}
            dataSource={record}
            callback={() => {
              setHasSwimlane(true);
              setRefreshIndex(Date.now());
            }}
          >
            <LinkButton type="primary">
              {intl('saenext.components.grayscale.SwimlanePanel.Edit')}
            </LinkButton>
          </CreateSwimlane>
          <LinkButton onClick={() => handleDeleteSwinlane(record)}>
            {intl('saenext.components.grayscale.SwimlanePanel.Delete')}
          </LinkButton>
        </Actions>
      ),
    },
  ];


  if (swimlaneEntry === Entrys.MSE) {
    if (swimVersion === '2') {
      // swimVersion为2没有基线路由
      // @ts-ignore
      columns.splice(3, 0, ...apigExtraColumns);
    } else {
      // @ts-ignore
      columns.splice(3, 0, ...mseExtraColumns);
    }
  } else if (swimlaneEntry === Entrys.APIG) {
    // @ts-ignore
    columns.splice(3, 0, ...apigExtraColumns);
  } else {
    // @ts-ignore
    columns.splice(3, 0, ...javaExtraColumns);
  }

  return (
    <Loading visible={loading} className="full-width full-height swimlane mb-l">
      <CndTable
        fetchData={getSwimlanes}
        operation={operation}
        primaryKey="primaryKey"
        columns={columns as []}
        recordCurrent
        showRefreshButton
        refreshIndex={refreshIndex}
        pagination={false}
        style={{ display: hasSwimlane ? 'block' : 'none' }}
      />

      <CreateSwimlane
        regionId={regionId}
        namespace={namespace}
        hasSwimlane={hasSwimlane}
        swimlaneGroup={swimlaneGroup}
        callback={() => {
          setHasSwimlane((preHasSwimlane) => {
            if (!preHasSwimlane) {
              // 如果当前泳道数为0 则需要刷新泳道组的信息
              refreshSwimlaneGroups && refreshSwimlaneGroups();
            }
            return true;
          });
          setRefreshIndex(Date.now());
        }}
      >
        <div
          style={{
            padding: 24,
            textAlign: 'center',
            minHeight: 100,
            display: hasSwimlane ? 'none' : 'block',
          }}
        >
          <LinkButton className="mb-l" style={{ fontSize: 14 }}>
            {intl('saenext.components.grayscale.SwimlanePanel.ClickCreateFirstDiversionLane')}
          </LinkButton>
          <div>{intl('saenext.components.grayscale.SwimlanePanel.ThereIsNoFlowControl')}</div>
        </div>
      </CreateSwimlane>
    </Loading>
  );
};
export default SwimlanePanel;
