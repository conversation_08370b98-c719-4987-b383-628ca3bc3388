import React, { FC, useState, useEffect } from 'react';
import {
  Field,
  Form,
  Input,
  Select,
  Radio,
  SlidePanel,
  intl,
  NumberPicker,
  Timeline,
  CndTable,
  Switch,
  Message,
  Icon,
} from '@ali/cnd';
import { get, noop, map, isEmpty, keys } from 'lodash';
import * as services from '~/services';
import GrayCheckRows from './GrayCheckRows';
import { Entrys } from './CreateSwimlaneGroup';
import RouteTransfer from './RouteTransfer';
import IndividualPercentage from './IndividualPercentage';
import CachedData from '~/cache/common';

const FormItem = Form.Item;
const { Item: TimelineItem } = Timeline;

type Props = {
  type?: 'create' | 'edit';
  regionId: string;
  hasSwimlane: boolean;
  namespace: {
    NamespaceId: string;
    NamespaceName: string;
    VpcId?: string;
    VpcName?: string;
  };
  callback?: () => void;
  dataSource?: Record<string, any>;
  headSwimlane?: Record<string, any>;
  swimlaneGroup: Record<string, any>;
};

export enum RouteMode {
  Ratio = 1,
  Content = 0,
}
const JoinerMode = {
  And: 'AND',
  Or: 'OR',
};
const SlideTitle = {
  create: intl('saenext.components.grayscale.CreateSwimlane.CreateALane'),
  edit: intl('saenext.components.grayscale.CreateSwimlane.EditALane'),
};

const CreateSwimlane: FC<Props> = (props) => {
  const {
    type = 'create',
    dataSource = {},
    namespace,
    hasSwimlane,
    headSwimlane = {},
    swimlaneGroup,
    callback = noop,
    children,
  } = props;
  const namespaceId = get(namespace, 'NamespaceId', '');
  const groupId = get(swimlaneGroup, 'GroupId', '');
  const swimlaneEntry = get(swimlaneGroup, 'EntryAppType', Entrys.APIG);
  const swimlaneRouteMode = get(swimlaneGroup, 'CanaryModel', RouteMode.Content);
  const swimVersion = get(swimlaneGroup, 'SwimVersion', '');
  const [isShowing, setIsShowing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [swimlaneGroupTags, setSwimlaneGroupTags] = useState([]);
  const [swimlaneTagApps, setSwimlaneTagApps] = useState([]);
  const [routeMode, setRouteMode] = useState(RouteMode.Content);
  const [hasTag, setHasTag] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [swimlaneEnable, setSwimlaneEnable] = useState(true);
  const [gatewayRoutes, setGatewayRoutes] = useState([]);
  const [disabledPercent, setDisabledPercent] = useState(false);

  const groupName = get(swimlaneGroup, 'GroupName', '');

  // path routeIds、灰度路由模式都是泳道组保持一致

  const field = Field.useField();
  const { init, validate, getValue, resetToDefault, setValues } = field;

  useEffect(() => {
    if (!isShowing) return;
    let _routeIds = [];
    let _paths = [];
    let _routeMode = RouteMode.Content;
    if (hasSwimlane && !isEmpty(headSwimlane)) {
      _routeMode = swimlaneRouteMode;
    }
    setRouteMode(_routeMode);
    setValues({ RouteIds: _routeIds, Paths: _paths, CanaryModel: _routeMode });

    getSwimlaneGroupTags();
    getSwimlaneGatewayRoutes();
  }, [isShowing, namespaceId]);

  useEffect(() => {
    if (!isShowing) return;
    if (type === 'create') return;
    if (isEmpty(dataSource)) return;
    const { LaneName, LaneTag, Enable, CanaryModel } = dataSource;
    // 根据灰度标签 请求灰度应用
    getSwimlaneTagApps(LaneTag);
    setHasTag(true);
    setSwimlaneEnable(Enable);
    let values = {};
    let _disabledPercent = false;
    if (swimlaneEntry === Entrys.MSE || swimlaneEntry === Entrys.APIG) {
      const { MseGatewayEntryRule = {} } = dataSource;
      const {
        RouteIds = [],
        Conditions = [],
        Percentage,
        IndependentPercentageEnable,
        PercentageByRoute,
        ConditionJoiner = 'AND',
      } = MseGatewayEntryRule;
      let routeIds = RouteIds;
      let percentageByRoute = {};
      if (IndependentPercentageEnable) {
        _disabledPercent = true;
        percentageByRoute = {
          ...PercentageByRoute,
          independentPercentageEnable: true,
        };
      }
      values = { RouteIds: routeIds, PercentageByPath: percentageByRoute, ConditionJoiner };
      let key = CanaryModel === RouteMode.Content ? 'Conditions' : 'Percentage';
      let value = CanaryModel === RouteMode.Content ? map(Conditions,item=>{
        return {
          ...item,
          Value:item?.Condition==='list'?item.Value?.split(','):item.Value
        }
      }) : Percentage;
      Reflect.set(values, key, value);
    } else {
      const { AppEntryRule = {} } = dataSource;
      const {
        Paths = [],
        ConditionJoiner,
        Conditions = [],
        Percentage,
        IndependentPercentageEnable,
        PercentageByPath,
      } = AppEntryRule;
      let paths = Paths;
      let percentageByPath = {};
      if (IndependentPercentageEnable) {
        _disabledPercent = true;
        paths = keys(PercentageByPath);
        percentageByPath = {
          ...PercentageByPath,
          independentPercentageEnable: true,
        };
      }
      values = { Paths: paths, ConditionJoiner, PercentageByPath: percentageByPath };
      let key = CanaryModel === RouteMode.Content ? 'Conditions' : 'Percentage';
      let value = CanaryModel === RouteMode.Content ? map(Conditions,item=>{
        return {
          ...item,
          Value:item?.Condition==='list'?item.Value?.split(','):item.Value
        }
      }) : Percentage;
      Reflect.set(values, key, value);
    }
    setRouteMode(CanaryModel);
    setDisabledPercent(_disabledPercent);
    setValues({ LaneName, LaneTag, CanaryModel, ...values });
  }, [isShowing, dataSource]);

  const getSwimlaneGroupTags = async () => {
    if (!groupId) return;
    if (!namespaceId) return;
    const res = await services.getSwimlaneGroupTags({
      params: {
        GroupId: groupId,
        NamespaceId: namespaceId,
      },
    });
    const { Data = [] } = res;
    const _swimlaneGroupTags = map(Data, (item) => ({
      ...item,
      label: item.Tag,
      value: item.Tag,
    }));
    setSwimlaneGroupTags(_swimlaneGroupTags);
  };

  const getSwimlaneTagApps = async (tag) => {
    setIsLoading(true);
    const res = await services.getSwimlaneGroupApps({
      params: {
        Tag: tag,
        NamespaceId: namespaceId,
        GroupId: groupId,
      },
      customErrorHandle: (err, data, callback) => {
        callback && callback();
        setIsLoading(false);
        setSwimlaneTagApps([]);
      },
    });
    const { Data = [] } = res;
    setSwimlaneTagApps(Data);
    setIsLoading(false);
  };

  const getSwimlaneGatewayRoutes = async () => {
    // java 应用不使用基线路由
    if (swimlaneEntry === Entrys.Java || swimlaneEntry === Entrys.APIG || (swimlaneEntry === Entrys.MSE && swimVersion === '2')) return;
    const { EntryAppId } = swimlaneGroup;
    const res = await services.getSwimlaneGatewayRoute({
      params: {
        NamespaceId: namespaceId,
        GatewayUniqueId: EntryAppId,
      },
    });
    const { Data = [] } = res;
    const _gatewayRoutes = map(Data, (item) => ({
      ...item,
      label: item.RouteName,
      value: item.RouteId,
    }));
    setGatewayRoutes(_gatewayRoutes);
  };

  const handleSubmit = async () => {
    const doSubmit = async (params) => {
      const customErrorHandle = (error, data, callback) => {
        if (error.message === 'You must specify the parameter MseGatewayRoutes.') {
          Message.error(
            intl('saenext.components.grayscale.CreateSwimlane.TheGatewayRequiresAtLeast'),
          );
          return;
        }
        callback();
      };
      const response = await services.buildSwimlane({ params, customErrorHandle });
      if (!isEmpty(response) && get(response, 'Data.LaneId', false)) {
        Message.success(
          type === 'create'
            ? intl('saenext.components.grayscale.CreateSwimlane.TheLaneWasCreated')
            : intl('saenext.components.grayscale.CreateSwimlane.TheLaneHasBeenUpdated'),
        );
        setIsShowing(false);
        callback && callback();
      }
      setIsProcessing(false);
    };

    validate((error, values) => {
      if (error) return;
      setIsProcessing(true);
      // @ts-ignore
      const { CanaryModel: routeMode } = values;
      const params = {
        LaneId: type === 'create' ? -1 : dataSource.LaneId,
        GroupId: groupId,
        NamespaceId: namespaceId,
        LaneName: get(values, 'LaneName', ''),
        LaneTag: get(values, 'LaneTag', ''),
        CanaryModel: routeMode,
        Enable: swimlaneEnable,
      };

      const entryRule = {};
      if (routeMode === RouteMode.Content) {
        const _conditions = map(get(values, 'Conditions', []) as any[],item=>{
          return {
            ...item,
            Value:item?.Condition === 'list'?item.Value?.join(','):item.Value
          }
        })
        Reflect.set(entryRule, 'Conditions', _conditions);
        Reflect.set(entryRule, 'ConditionJoiner', get(values, 'ConditionJoiner', JoinerMode.And));
      } else {
        Reflect.set(entryRule, 'Percentage', get(values, 'Percentage', 1));
        const independentPercentageEnable = get(
          values,
          'PercentageByPath.independentPercentageEnable',
          false,
        );
        if (independentPercentageEnable) {
          const percentageByPath: {} = get(values, 'PercentageByPath', {});
          Reflect.deleteProperty(percentageByPath, 'independentPercentageEnable');
          const key = swimlaneEntry === Entrys.MSE ? 'PercentageByRoute' : 'PercentageByPath';
          Reflect.set(entryRule, key, percentageByPath);
          Reflect.set(entryRule, 'IndependentPercentageEnable', true);
        }
      }
      if (swimlaneEntry === Entrys.MSE || swimlaneEntry === Entrys.APIG) {
        swimlaneEntry === Entrys.MSE && Reflect.set(entryRule, 'RouteIds', get(values, 'RouteIds', []));
        Reflect.set(params, 'MseGatewayEntryRule', JSON.stringify(entryRule));
      } else {
        Reflect.set(entryRule, 'Paths', get(values, 'Paths', []));
        Reflect.set(entryRule, 'ConditionJoiner', get(values, 'ConditionJoiner', JoinerMode.And));
        Reflect.set(params, 'AppEntryRule', JSON.stringify(entryRule));
      }
      doSubmit(params);
    });
  };

  const handleCancel = () => {
    resetToDefault();
    setHasTag(false);
    setSwimlaneEnable(true);
    setRouteMode(RouteMode.Content);
    setIsShowing(false);
  };

  return (
    <>
      <span onClick={() => setIsShowing(prev => !prev)}>{children}</span>
      <SlidePanel
        title={SlideTitle[type]}
        width={700}
        onCancel={handleCancel}
        onOk={handleSubmit}
        isShowing={isShowing}
        isProcessing={isProcessing}
        cancelText={intl('button.cancel')}
        processingText={intl('button.processing')}
      >
        <Form field={field}>
          <Timeline className="swimlane-timeline">
            <TimelineItem
              key="gray"
              title={intl(
                'saenext.components.grayscale.CreateSwimlane.CreateAGrayscaleApplication',
              )}
              state="process"
              content={
                <>
                  <span className="text-description">
                    {intl(
                      'saenext.components.grayscale.CreateSwimlane.CreateAGrayScaleApplication',
                      { groupName: groupName },
                    )}
                  </span>
                  <FormItem label={intl('saenext.components.grayscale.CreateSwimlane.Procedure')}>
                    <div className="text-line">
                      {intl(
                        'saenext.components.grayscale.CreateSwimlane.OnTheApplicationManagementMicroservice',
                      )}
                      <Icon type="more" size="xs" className="ml-xs mr-xs" />
                      {intl('saenext.components.grayscale.CreateSwimlane.IconToCreateAGrayscale')}
                    </div>
                    <div className="text-line">
                      {intl('saenext.components.grayscale.CreateSwimlane.AddAlicloudServiceTagTag')}
                    </div>
                  </FormItem>
                </>
              }
            />

            <TimelineItem
              key="swimlane"
              title={intl('saenext.components.grayscale.CreateSwimlane.FillInTheLaneInformation')}
              state="process"
              content={
                <>
                  <FormItem
                    required
                    label={intl('saenext.components.grayscale.CreateSwimlane.LaneName')}
                  >
                    <Input
                      {...init('LaneName', {
                        initValue: '',
                        rules: [
                          {
                            required: true,
                            message: intl(
                              'saenext.components.grayscale.CreateSwimlane.EnterALaneName',
                            ),
                          },
                          {
                            message: intl(
                              'saenext.components.grayscale.CreateSwimlane.SupportsUppercaseAndLowercaseLetters',
                            ),
                            pattern: /^[0-9a-zA-Z\-_]{1,64}$/,
                          },
                        ],
                      })}
                      minLength={1}
                      maxLength={64}
                      showLimitHint
                      placeholder={intl(
                        'saenext.components.grayscale.CreateSwimlane.EnterALaneNameThat',
                      )}
                    />
                  </FormItem>
                  <FormItem
                    required
                    label={intl('saenext.components.grayscale.CreateSwimlane.LaneLabel')}
                    help={intl('saenext.components.grayscale.CreateSwimlane.LaneTagsAreUsedTo')}
                  >
                    <Select
                      {...init('LaneTag', {
                        rules: [
                          {
                            required: true,
                            message: intl(
                              'saenext.components.grayscale.CreateSwimlane.PleaseSelectALaneLabel',
                            ),
                          },
                        ],

                        props: {
                          onChange: val => {
                            setHasTag(!!val);
                            getSwimlaneTagApps(val);
                          },
                        },
                      })}
                      dataSource={swimlaneGroupTags}
                      style={{ width: '100%' }}
                      disabled={type === 'edit'}
                      placeholder={intl(
                        'saenext.components.grayscale.CreateSwimlane.SelectTheAlicloudServiceTag',
                      )}
                    />
                  </FormItem>
                  {hasTag ? (
                    <FormItem
                      label={intl(
                        'saenext.components.grayscale.CreateSwimlane.ConfirmTheMatchingRelationship',
                      )}
                    >
                      <span className="text-description">
                        {intl(
                          'saenext.components.grayscale.CreateSwimlane.PleaseConfirmTheMatchingRelationship',
                        )}
                      </span>
                      <CndTable
                        className="mt-s"
                        loading={isLoading}
                        dataSource={swimlaneTagApps}
                        columns={[
                          {
                            dataIndex: 'AppName',
                            title: intl(
                              'saenext.components.grayscale.CreateSwimlane.GrayscaleApplicationName',
                            ),
                          },
                          {
                            dataIndex: 'BaseAppName',
                            title: intl(
                              'saenext.components.grayscale.CreateSwimlane.CorrespondingBaselineApplication',
                            ),
                          },
                        ]}
                      />
                    </FormItem>
                  ) : null}

                  <FormItem label={intl('saenext.components.grayscale.CreateSwimlane.LaneStatus')}>
                    <Switch
                      checked={swimlaneEnable}
                      onChange={enable => setSwimlaneEnable(enable)}
                    />
                  </FormItem>
                </>
              }
            />

            <TimelineItem
              key="route"
              title={intl('saenext.components.grayscale.CreateSwimlane.ConfigureRoutingRules')}
              state="process"
              content={
                <>
                  {swimlaneEntry === Entrys.MSE || swimlaneEntry === Entrys.APIG ? (
                    <FormItem
                      label={intl('saenext.components.grayscale.CreateSwimlane.BaselineRouting')}
                      className={swimVersion === '2' ? 'none' : ''}
                    >
                      <span className="text-description">
                        {intl(
                          'saenext.components.grayscale.CreateSwimlane.BaselineRoutesAreLabelBased',
                        )}
                      </span>
                      <Message
                        type="notice"
                        className="mt-s mb-s"
                        title={
                          <div>
                            {intl('saenext.components.grayscale.CreateSwimlane.IfTheRouteToBe')}

                            <a
                              target="_blank"
                              style={{ marginLeft: 2, marginRight: 2 }}
                              href={`${CachedData.confLink('feature:saenext:url')}/${
                                props.regionId
                              }/traffic-management/gateway-route/list?namespaceId=${namespaceId}`}
                            >
                              {intl('saenext.components.grayscale.CreateSwimlane.GatewayRouting')}
                            </a>
                            {intl(
                              'saenext.components.grayscale.CreateSwimlane.CheckInTheManagementInterface',
                            )}
                          </div>
                        }
                      >
                        <div>
                          {intl(
                            'saenext.components.grayscale.CreateSwimlane.WhetherTheUseScenarioField',
                          )}
                        </div>
                        <div>
                          {intl(
                            'saenext.components.grayscale.CreateSwimlane.WhetherTheBackendServiceField',
                          )}
                        </div>
                      </Message>
                      <RouteTransfer
                        {...init('RouteIds')}
                        handleChecked={val => {
                          field.setValues({
                            RouteIds: val,
                          });
                        }}
                        dataSource={gatewayRoutes}
                        titles={[
                          intl('saenext.components.grayscale.CreateSwimlane.OptionalRoute'),
                          intl('saenext.components.grayscale.CreateSwimlane.SelectedRoute'),
                        ]}
                        refreshData={() => getSwimlaneGatewayRoutes()}
                      />
                    </FormItem>
                  ) : (
                    <FormItem label="Path">
                      <Select
                        {...init('Paths')}
                        maxTagCount={10}
                        mode="tag"
                        dataSource={[]}
                        style={{ width: '100%' }}
                        placeholder={intl(
                          'saenext.components.grayscale.CreateSwimlane.EnterAValueAndPress',
                        )}
                        notFoundContent={intl(
                          'saenext.components.grayscale.CreateSwimlane.NoDataIsAvailableEnter',
                        )}
                      />
                    </FormItem>
                  )}

                  <FormItem
                    label={intl('saenext.components.grayscale.CreateSwimlane.GrayscaleMode')}
                  >
                    <span className="text-description">
                      {intl('saenext.components.grayscale.CreateSwimlane.TheGrayModeMustBe')}
                    </span>
                    <Radio.Group
                      {...init('CanaryModel', {
                        initValue: routeMode,
                        props: {
                          onChange: (val: RouteMode) => setRouteMode(val),
                        },
                      })}
                      direction="ver"
                      disabled={type === 'edit' || hasSwimlane}
                    >
                      <Radio id={`RouteMode.Content`} value={RouteMode.Content}>
                        {intl('saenext.components.grayscale.CreateSwimlane.GrayscaleByContent')}
                      </Radio>
                      <div
                        className="text-description"
                        style={{ lineHeight: 'normal', paddingLeft: 22 }}
                      >
                        {intl(
                          'saenext.components.grayscale.CreateSwimlane.ThisGrayModeIsRecommended',
                        )}
                      </div>
                      <Radio id={`RouteMode.Ratio`} value={RouteMode.Ratio}>
                        {intl('saenext.components.grayscale.CreateSwimlane.ProportionalGrayScale')}
                      </Radio>
                      <div
                        className="text-description"
                        style={{ lineHeight: 'normal', paddingLeft: 22 }}
                      >
                        {intl(
                          'saenext.components.grayscale.CreateSwimlane.WhenTheRequestContentCannot',
                        )}
                      </div>
                    </Radio.Group>
                  </FormItem>
                  {routeMode === RouteMode.Content ? (
                    <>
                      <FormItem
                        label={intl('saenext.components.grayscale.CreateSwimlane.GrayCondition')}
                      >
                        <span className="text-description">
                          {intl(
                            'saenext.components.grayscale.CreateSwimlane.TheGrayScaleConditionSpecifies',
                          )}
                        </span>
                        <Radio.Group
                          {...init('ConditionJoiner', {
                            initValue: JoinerMode.And,
                          })}
                        >
                          <Radio id={`JoinerMode.And`} value={JoinerMode.And}>
                            {intl(
                              'saenext.components.grayscale.CreateSwimlane.TheFollowingConditionsAreMet',
                            )}
                          </Radio>
                          {swimlaneEntry === Entrys.Java ||
                          ((swimlaneEntry === Entrys.MSE || swimlaneEntry === Entrys.APIG) &&
                            swimVersion === '2') ? (
                            <Radio id={`JoinerMode.Or`} value={JoinerMode.Or}>
                              {intl(
                                'saenext.components.grayscale.CreateSwimlane.AnyOfTheFollowingConditions',
                              )}
                            </Radio>
                          ) : null}
                        </Radio.Group>
                      </FormItem>
                      <FormItem label="">
                        <GrayCheckRows
                          {...init('Conditions', {
                            initValue: [{}],
                          })}
                          swimlaneEntry={swimlaneEntry}
                          swimVersion={swimVersion}
                        />
                      </FormItem>
                    </>
                  ) : (
                    <>
                      <FormItem
                        required
                        label={intl('saenext.components.grayscale.CreateSwimlane.TrafficRatio')}
                        help={intl(
                          'saenext.components.grayscale.CreateSwimlane.SetTheTrafficRatioFor',
                        )}
                      >
                        <NumberPicker
                          {...init('Percentage', {
                            initValue: 1,
                            rules: [
                              {
                                required: true,
                                message: intl(
                                  'saenext.components.grayscale.CreateSwimlane.PleaseEnterTheTrafficRatio',
                                ),
                              },
                            ],
                          })}
                          min={1}
                          max={100}
                          disabled={disabledPercent}
                          innerAfter="%"
                          placeholder="1-100"
                        />
                      </FormItem>
                      {swimVersion !== '2' && (
                        <FormItem label="">
                          <IndividualPercentage
                            {...init('PercentageByPath', {
                              initValue: {},
                            })}
                            dataSource={
                              swimlaneEntry === Entrys.MSE
                                ? getValue('RouteIds')
                                : getValue('Paths')
                            }
                            swimlane={dataSource}
                            swimlaneGroup={swimlaneGroup}
                            swimlaneEntry={swimlaneEntry}
                            gatewayRoutes={gatewayRoutes}
                            initPercentage={getValue('Percentage')}
                            setDisabledPercent={disabled => setDisabledPercent(disabled)}
                          />
                        </FormItem>
                      )}
                    </>
                  )}
                </>
              }
            />

            <TimelineItem
              key="done"
              title={intl('saenext.components.grayscale.CreateSwimlane.LaneConfigurationCompleted')}
              state="process"
            />
          </Timeline>
        </Form>
      </SlidePanel>
    </>
  );
};

export default CreateSwimlane;
