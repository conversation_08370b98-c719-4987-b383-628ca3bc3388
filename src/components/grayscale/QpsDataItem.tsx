import { intl } from '@ali/cnd';
import React from 'react';
import { get, isEmpty, keys, map } from 'lodash';

const QpsDataItem = (props) => {
  const { title = intl('saenext.components.grayscale.QpsDataItem.ApplyQpsData'), data = {} } =
    props;

  return (
    <>
      <div className="qps-data">
        <span>{title}</span>
        <div className="flex mt-l">
          <div className="flex-column">
            <span className="mb-s" style={{ whiteSpace: 'nowrap' }}>
              {intl('saenext.components.grayscale.QpsDataItem.ErrorRequestsTotalRequestsTotal')}
            </span>
            <span>
              <span style={{ color: '#333', fontSize: 13 }}>{get(data, 'ExpQps', 0)}</span>
              <span>/{get(data, 'Qps', 0)}</span>
            </span>
          </div>
          {!isEmpty(data.TagValues) ? (
            <>
              <div className="symbol">=</div>
              <div className="flex" style={{ overflowX: 'auto' }}>
                {map(keys(data.TagValues), (key, index) => {
                  const value = data.TagValues[key];
                  const tag =
                    key === 'null'
                      ? intl('saenext.components.grayscale.QpsDataItem.Unmarked')
                      : key;
                  return (
                    <div key={key} className="flex">
                      <div className="flex-column">
                        <span className="mb-s" style={{ whiteSpace: 'nowrap' }}>
                          {intl(
                            'saenext.components.grayscale.QpsDataItem.ErrorRequestsTotalRequestsTag',
                            { tag: tag },
                          )}
                        </span>
                        <span>
                          <span style={{ color: '#c80000', fontSize: 13 }}>
                            {get(value, 'ExpQps', 0)}
                          </span>
                          <span>/{get(value, 'Qps', 0)}</span>
                        </span>
                      </div>
                      {index < keys(data.TagValues).length - 1 ? (
                        <div className="symbol">+</div>
                      ) : null}
                    </div>
                  );
                })}
              </div>
            </>
          ) : null}
        </div>
      </div>
    </>
  );
};
export default QpsDataItem;
