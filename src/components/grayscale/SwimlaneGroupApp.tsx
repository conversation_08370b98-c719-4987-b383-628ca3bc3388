import React, { useState, useEffect } from 'react';
import { Loading } from '@ali/cnd';
import QpsDataItem from './QpsDataItem';
import QpsChartItem from './QpsChartItem';
import * as services from '~/services';
import { isEmpty, get } from 'lodash';
// import { DATA } from './contants';

const SwimlaneAppPanel = (props) => {
  const { currentGrayApp, playbackTimestamp, callback } = props;
  const [loading, setLoading] = useState(false);
  const [qpsData, setQpsData] = useState({});
  const [qpsMonitor, setQpsMonitor] = useState([]);

  useEffect(() => {
    getSwimlaneGroupAppOverview();
  }, [playbackTimestamp, JSON.stringify(currentGrayApp)]);

  const getSwimlaneGroupAppOverview = async () => {
    if (isEmpty(currentGrayApp)) return;
    setLoading(true);
    const { MseAppId, MseNamespaceId } = currentGrayApp;
    const res = await services.getSwimlaneGroupAppOverview({
      params: {
        Namespace: MseNamespaceId,
        AppId: MseAppId,
        StartTime: (playbackTimestamp - (5 * 60 * 1000)) || (new Date().getTime() - (15 * 1000) - (5 * 60 * 1000)),
        EndTime: playbackTimestamp - (15 * 1000) || (new Date().getTime() - (15 * 1000)),
      },
    });
    const { Data = {} } = res || {};
    const _qpsData = get(Data, 'CurMetricsFm', {});
    const _qpsMonitor = get(Data, 'CurMetrics', []);
    setQpsData(_qpsData);
    setQpsMonitor(_qpsMonitor);
    // setQpsMonitor(DATA);
    setLoading(false);
  };

  return (
    <div className='swimlane-panel'>
      <Loading
        visible={loading}
        className="full-width full-height"
      >
        {/* 应用QPS数据 */}
        <QpsDataItem data={qpsData} />

        {/* 应用QPS监控 */}
        <QpsChartItem data={qpsMonitor} callback={callback} />
      </Loading>
    </div>
  )
}
export default SwimlaneAppPanel;