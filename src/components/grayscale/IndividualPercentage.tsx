import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { get, isEmpty, forEach, map, noop, filter, find } from 'lodash';
import * as services from '~/services';
import { Checkbox, Loading, Collapse, NumberPicker, CndTable } from '@ali/cnd';
import { Entrys } from './CreateSwimlaneGroup';

const Panel = Collapse.Panel;

const IndividualPercentage = (props) => {
  const {
    value,
    onChange,
    dataSource = [],
    swimlane,
    swimlaneGroup,
    swimlaneEntry,
    gatewayRoutes,
    initPercentage,
    setDisabledPercent = noop,
  } = props;
  const groupId = get(swimlaneGroup, 'GroupId', '');
  const key = swimlaneEntry === Entrys.Java ? 'AppEntryRule' : 'MseGatewayEntryRule';
  const independentPercentageEnable = get(swimlane, `${key}.IndependentPercentageEnable`, false);
  const [paths, setPaths] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showIndividual, setShowIndividual] = useState(independentPercentageEnable);
  const [disabled, setDisabled] = useState(isEmpty(dataSource));

  useEffect(() => {
    if (isEmpty(dataSource)) {
      setShowIndividual(false);
      setDisabledPercent(false);
      setDisabled(true);
      onChange && onChange({ independentPercentageEnable: false });
      return;
    }
    setDisabled(isEmpty(dataSource));
    getSwimlanePathPercent();
  }, [dataSource, showIndividual, gatewayRoutes]);

  const getSwimlanePathPercent = async () => {
    if (!showIndividual) return;
    setLoading(true);
    const getPathPercent = async (pathOrRouteId) => {
      const res = await services.getSwimlanePathPercent({
        params: {
          LaneId: -1,
          GroupId: groupId,
          Path: swimlaneEntry === Entrys.Java ? pathOrRouteId : '',
          RouteId: swimlaneEntry === Entrys.MSE ? pathOrRouteId : 1,
          Source: 'edasmsc',
          Namespace: '',
        },
        customErrorHandle: (err, data, callback) => {
          callback && callback();
          return { pathOrRoute: pathOrRouteId, percentage: initPercentage, otherPercents: [] };
        },
      });
      let _otherPercents = get(res, 'Data', []);
      _otherPercents = filter(
        _otherPercents,
        (path) => path.SwimmingLaneId !== get(swimlane, 'LaneId'),
      );
      let percents = 0;
      forEach(_otherPercents, (path) => {
        percents += path.Percent;
      });
      const maxPercentage = 100 - percents;
      // 初始化的百分比要看是否分别设置了路径的百分比
      const _initPercentage = independentPercentageEnable
        ? get(value, pathOrRouteId, initPercentage)
        : initPercentage;
      let _pathOrRoute = pathOrRouteId;
      if (swimlaneEntry === Entrys.MSE) {
        const gatewayRoute = find(gatewayRoutes, { RouteId: pathOrRouteId });
        _pathOrRoute = get(gatewayRoute, 'RouteName', '');
      }
      return {
        pathOrRoute: _pathOrRoute,
        pathOrRouteId,
        percentage: _initPercentage,
        maxPercentage,
        otherPercents: _otherPercents,
      };
    };
    const options = [];
    forEach(dataSource, (pathOrRouteId) => {
      options.push(getPathPercent(pathOrRouteId));
    });
    let res = await Promise.all(options);
    setPaths(res);
    setLoading(false);
    const _value = {
      independentPercentageEnable: true,
    };
    forEach(res, (item) => {
      Reflect.set(_value, item.pathOrRouteId, item.percentage);
    });
    onChange && onChange(_value);
  };

  const handlePercentageChange = (percentage, pathOrRouteId) => {
    const _value = {
      independentPercentageEnable: true,
    };
    const _paths = map(paths, (item) => {
      if (item.pathOrRouteId === pathOrRouteId) {
        item.percentage = percentage;
      }
      Reflect.set(_value, item.pathOrRouteId, item.percentage);
      return item;
    });
    setPaths(_paths);
    onChange && onChange(_value);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      {/* @ts-ignore */}
      <Checkbox.Group
        value={showIndividual ? ['individual'] : []}
        onChange={(val) => {
          const isIndividual = !isEmpty(val);
          setShowIndividual(isIndividual);
          setDisabledPercent(isIndividual);
          if (!isIndividual) {
            onChange && onChange({ independentPercentageEnable: false });
          }
        }}
      >
        <Checkbox id="individual" value="individual" disabled={disabled}>
          <span className="text-bold" style={{ color: '#333' }}>
            {intl('saenext.components.grayscale.IndividualPercentage.SetTheTrafficRatioFor')}
          </span>
        </Checkbox>
      </Checkbox.Group>
      <Loading visible={loading} className="swimlane-collapse mt-s">
        {showIndividual ? (
          <>
            {map(paths, (item) => {
              const {
                pathOrRoute,
                pathOrRouteId,
                percentage,
                maxPercentage,
                otherPercents = [],
              } = item;
              return (
                <Collapse key={pathOrRouteId} className="mb-xs">
                  <Panel
                    title={
                      <div className="flex justify-between" style={{ alignItems: 'baseline' }}>
                        <span>{pathOrRoute}</span>
                        <div
                          style={{ display: 'flex', flexDirection: 'column', alignItems: 'end' }}
                        >
                          <div className="flex" style={{ width: 'fit-content' }}>
                            <span>
                              {intl(
                                'saenext.components.grayscale.IndividualPercentage.TrafficRatio',
                              )}
                            </span>
                            <NumberPicker
                              min={1}
                              max={maxPercentage || 100}
                              value={percentage}
                              className="ml-s"
                              innerAfter="%"
                              placeholder={`1-${maxPercentage || 100}`}
                              onClick={(e) => {
                                e.stopPropagation();
                              }}
                              onChange={(val) => handlePercentageChange(val, pathOrRouteId)}
                            />
                          </div>
                          <>
                            {percentage === maxPercentage ? (
                              <span className="mt-xs" style={{ color: '#ff0000' }}>
                                {intl(
                                  'saenext.components.grayscale.IndividualPercentage.TheTrafficRatioOfThis',
                                )}
                              </span>
                            ) : null}

                            {percentage > maxPercentage ? (
                              <span className="mt-xs" style={{ color: '#ff0000' }}>
                                {intl(
                                  'saenext.components.grayscale.IndividualPercentage.TheSumOfTheTraffic',
                                )}
                              </span>
                            ) : null}

                            {!percentage ? (
                              <span className="mt-xs" style={{ color: '#ff0000' }}>
                                {intl(
                                  'saenext.components.grayscale.IndividualPercentage.TheTrafficRatioCannotBe',
                                )}
                              </span>
                            ) : null}
                          </>
                        </div>
                      </div>
                    }
                  >
                    {intl('saenext.components.grayscale.IndividualPercentage.InAllLanesTheSum')}

                    {isEmpty(otherPercents) ? null : (
                      <CndTable className="mt-s" dataSource={otherPercents}>
                        <CndTable.Column
                          title={intl(
                            'saenext.components.grayscale.IndividualPercentage.LaneGroup',
                          )}
                          width="40%"
                          dataIndex="SwimmingLaneGroupName"
                        />
                        <CndTable.Column
                          title={intl('saenext.components.grayscale.IndividualPercentage.Lane')}
                          width="40%"
                          dataIndex="SwimmingLaneName"
                        />
                        <CndTable.Column
                          title={intl(
                            'saenext.components.grayscale.IndividualPercentage.TrafficRatio',
                          )}
                          width="20%"
                          dataIndex="Percent"
                        />
                      </CndTable>
                    )}
                  </Panel>
                </Collapse>
              );
            })}
          </>
        ) : null}
      </Loading>
    </div>
  );
};
export default IndividualPercentage;
