import { intl } from '@ali/cnd';
import React, { useState, useEffect, useRef } from 'react';
import { Button, Select, DatePicker, Loading, useHistory } from '@ali/cnd';
import SwimlaneGroupCard from './SwimlaneGroupCard';
import SwimlaneGroupApp from './SwimlaneGroupApp';
import CreateSwimlaneGroup from './CreateSwimlaneGroup';
import SwimlanePanel from './SwimlanePanel';
import { isEmpty, forEach, head, get } from 'lodash';
import moment from 'moment';
import TextWithBalloon from '~/components/shared/TextWithBalloon';

const SwimlaneGroupPortal = (props) => {
  const { regionId, namespace, swimlaneGroups, refreshSwimlaneGroups } = props;
  const history = useHistory();
  const namespaceId = get(namespace, 'NamespaceId', '');
  const swimlaneGroupRef = useRef(new Map());
  const [loading, setLoading] = useState(false);
  const [currentSwimlaneGroup, setCurrentSwimlaneGroup] = useState();
  const [currentSwimlaneGroupId, setCurrentSwimlaneGroupId] = useState(0);
  const [currentGrayApp, setCurrentGrayApp] = useState();
  const [playbackDate, setPlaybackDate] = useState(new Date());
  const _timestamp = Date.parse(playbackDate.toString());
  const [playbackTimestamp, setPlaybackTimestamp] = useState(_timestamp);

  useEffect(() => {
    // 加载泳道组
    if (isEmpty(swimlaneGroups)) return;
    forEach(swimlaneGroups, (swimlaneGroup) => {
      const { GroupId } = swimlaneGroup;
      swimlaneGroupRef.current.set(GroupId, swimlaneGroup);
    });

    // 泳道组发生改变时 刷新当前泳道组的信息
    let _currentSwimlaneGroup;
    let _currentSwimlaneGroupId = currentSwimlaneGroupId;
    if (!currentSwimlaneGroupId) {
      // 默认取第一个泳道组
      _currentSwimlaneGroup = head(swimlaneGroups);
      _currentSwimlaneGroupId = get(_currentSwimlaneGroup, 'GroupId', '');
    }
    _currentSwimlaneGroup = swimlaneGroupRef.current.get(_currentSwimlaneGroupId);
    setCurrentSwimlaneGroup(_currentSwimlaneGroup);
    setCurrentSwimlaneGroupId(_currentSwimlaneGroupId);
  }, [namespaceId, JSON.stringify(swimlaneGroups)]);

  const changeSwimlaneGroup = (value) => {
    setLoading(true);
    const swimlaneGroup = swimlaneGroupRef.current.get(value);
    setCurrentSwimlaneGroup(swimlaneGroup);
    setCurrentSwimlaneGroupId(value);
    setLoading(false);
  };

  const disabledDate = (current) => {
    const { _d } = current;
    let ret = false;
    let twodays = 2 * 24 * 60 * 60 * 1000;
    const nowDate = Date.parse(new Date().toString());
    const theDate = Date.parse(_d.toString());

    if (!theDate) {
      return ret;
    }
    if (theDate > nowDate || theDate < nowDate - twodays) {
      ret = true;
    }
    return ret;
  };

  const handleDateOk = (current) => {
    const { _d } = current;
    const _timestamp = Date.parse(_d.toString());
    setPlaybackDate(_d);
    setPlaybackTimestamp(_timestamp);
  };

  const handleDateChange = (current) => {
    const { _d } = current;
    setPlaybackDate(_d);
  };

  const handleDateVisible = (visible, type) => {
    if (!visible && type !== 'okBtnClick' && type !== 'docClick') {
      setPlaybackDate(new Date());
    }
  };

  return (
    <>
      <div className="swimlane-portal">
        <div className="mb-s">
          <CreateSwimlaneGroup
            regionId={regionId}
            namespace={namespace}
            callback={(groupId) => {
              setCurrentSwimlaneGroupId(groupId);
              refreshSwimlaneGroups(+new Date());
            }}
          >
            <Button type="primary" className="mr-s">
              {intl('saenext.components.grayscale.SwimlaneGroupPortal.CreateALaneGroup')}
            </Button>
          </CreateSwimlaneGroup>
          <Select
            placeholder={intl('saenext.components.grayscale.SwimlaneGroupPortal.SelectALaneGroup')}
            style={{ width: 278 }}
            dataSource={swimlaneGroups}
            value={currentSwimlaneGroupId}
            label={
              <div className="select-label">
                {intl('saenext.components.grayscale.SwimlaneGroupPortal.SelectALaneGroup.1')}
              </div>
            }
            onChange={changeSwimlaneGroup}
          />

          <DatePicker
            hasClear={false}
            placeholder={intl(
              'saenext.components.grayscale.SwimlaneGroupPortal.SelectTimeForwardPlaybackFor',
            )}
            onChange={handleDateChange}
            onOk={handleDateOk}
            onVisibleChange={handleDateVisible}
            disabledDate={disabledDate}
            style={{ width: 300, float: 'right' }}
            value={moment(playbackDate, 'YYYY-MM-DD HH:mm:ss', true)}
            label={
              <div className="select-label">
                <TextWithBalloon
                  align="t"
                  text={intl('saenext.components.grayscale.SwimlaneGroupPortal.PlaybackTime')}
                  tips={intl(
                    'saenext.components.grayscale.SwimlaneGroupPortal.SelectTimeForwardPlaybackFor',
                  )}
                />
              </div>
            }
            showTime={{
              disabledHours: (index) => {
                const date = new Date();
                const hours = date.getHours();
                if (playbackDate.getDate() < date.getDate()) {
                  return hours > index;
                } else {
                  return hours < index;
                }
              },
            }}
          />
        </div>
        <Loading visible={loading} className="full-width full-height">
          <div className="flex mb-l">
            <SwimlaneGroupCard
              regionId={regionId}
              namespace={namespace}
              swimlaneGroup={currentSwimlaneGroup}
              callback={(groupId) => {
                setCurrentSwimlaneGroupId(groupId);
                refreshSwimlaneGroups(+new Date());
              }}
              setCurrentGrayApp={setCurrentGrayApp}
            />

            <SwimlaneGroupApp
              currentGrayApp={currentGrayApp}
              playbackTimestamp={playbackTimestamp}
              callback={() => {
                history.push(
                  `/${regionId}/traffic-management/msc/grayscale-overview?namespaceId=${namespaceId}&groupId=${currentSwimlaneGroupId}`,
                );
              }}
            />
          </div>
          <SwimlanePanel
            regionId={regionId}
            namespace={namespace}
            swimlaneGroup={currentSwimlaneGroup}
            refreshSwimlaneGroups={() => {
              refreshSwimlaneGroups(+new Date());
            }}
          />
        </Loading>
      </div>
    </>
  );
};
export default SwimlaneGroupPortal;
