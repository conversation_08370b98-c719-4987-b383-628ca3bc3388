import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { DataFields, Actions, Dialog, Message, Icon, Truncate } from '@ali/cnd';
import CndTable, { ISearch } from '@ali/cnd-table';
import CreateSwimlaneGroup from './CreateSwimlaneGroup';
import { map, filter, get, head } from 'lodash';
import * as services from '~/services';

const { LinkButton } = Actions;
const EntryLabels = {
  mse: intl('saenext.components.grayscale.SwimlaneGroupCard.JavaServiceGateway'),
  'mse-gw': intl('saenext.components.grayscale.SwimlaneGroupCard.MseCloudNativeGateway'),
  apig: intl('saenext.components.grayscale.SwimlaneGroupCard.CloudNativeApiGateway'),
};

const SwimlaneGroupCard = (props) => {
  const { regionId, namespace, swimlaneGroup = {}, callback, setCurrentGrayApp } = props;
  const namespaceId = get(namespace, 'NamespaceId', '');
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [currentAppId, setCurrentAppId] = useState('');
  const [hasFilterParams, setHasFilterParams] = useState(false);

  useEffect(() => {
    setRefreshIndex(Date.now());
  }, [JSON.stringify(swimlaneGroup)]);

  const fetchData = async (params: ISearch) => {
    // @ts-ignore
    const { AppName = '' } = params;
    const { Apps = [] } = swimlaneGroup;
    const _grayApp = head(Apps);
    const _currentAppId = get(_grayApp, 'AppId', '');
    setCurrentAppId(_currentAppId);
    setCurrentGrayApp && setCurrentGrayApp(_grayApp);
    setHasFilterParams(AppName ? true : false);
    return {
      data: map(
        filter(Apps, (item) => item.AppName?.includes(AppName)),
        (item) => ({
          ...item,
          primaryKey: `${item.AppName}#${item.AppId}`,
        }),
      ),
      total: Apps.length,
    };
  };

  const handleRowClick = (record) => {
    const { AppId } = record;
    setCurrentAppId(AppId);
    setCurrentGrayApp && setCurrentGrayApp(record);
  };

  const handleRowProps = (record) => {
    if (record.AppId === currentAppId) {
      return {
        style: { background: 'rgba(138, 190, 255, 0.3)', color: '#0077cc' },
      };
    }
  };

  return (
    <>
      <div className="swimlane-card">
        <DataFields
          dataSource={swimlaneGroup}
          items={[
            {
              dataIndex: 'GroupName',
              label: intl('saenext.components.grayscale.SwimlaneGroupCard.LaneGroupName'),
              span: 24,
              render: (value) => (
                <div className="flex justify-between">
                  <div className="flex">
                    <Truncate
                      type="width"
                      align="r"
                      threshold={150}
                      showTooltip={true}
                      tooltipMaxWidth={2000}
                    >
                      {value}
                    </Truncate>
                  </div>
                  <Actions wrap={true} threshold={2} style={{ marginRight: -16 }}>
                    <LinkButton>
                      <CreateSwimlaneGroup
                        type="edit"
                        regionId={regionId}
                        namespace={namespace}
                        dataSource={swimlaneGroup}
                        callback={callback}
                      >
                        <span>{intl('saenext.components.grayscale.SwimlaneGroupCard.Edit')}</span>
                      </CreateSwimlaneGroup>
                    </LinkButton>
                    <LinkButton
                      key="delete"
                      onClick={() => {
                        Dialog.confirm({
                          title: intl(
                            'saenext.components.grayscale.SwimlaneGroupCard.DeleteALaneGroup',
                          ),
                          content: (
                            <div>
                              {intl(
                                'saenext.components.grayscale.SwimlaneGroupCard.AreYouSureYouWant',
                              )}
                              <span style={{ color: '#F90' }}>{swimlaneGroup.GroupName}</span> ?
                            </div>
                          ),

                          messageProps: {
                            type: 'warning',
                          },
                          onOk: async () => {
                            const res = await services.deleteSwimlaneGroup({
                              params: {
                                NamespaceId: namespaceId,
                                GroupId: swimlaneGroup.GroupId,
                              },
                            });
                            if (!res) return;
                            Message.success(
                              intl(
                                'saenext.components.grayscale.SwimlaneGroupCard.TheLaneGroupHasBeen',
                              ),
                            );
                            callback && callback(0);
                            return res;
                          },
                        });
                      }}
                    >
                      {intl('saenext.components.grayscale.SwimlaneGroupCard.Delete')}
                    </LinkButton>
                  </Actions>
                </div>
              ),
            },
            {
              dataIndex: 'EntryAppType',
              label: intl('saenext.components.grayscale.SwimlaneGroupCard.EntryType'),
              span: 24,
              render: (value) => <span>{value ? EntryLabels[value] : '-'}</span>,
            },
            {
              dataIndex: 'EntryApp',
              label: intl('saenext.components.grayscale.SwimlaneGroupCard.LaneGroupTrafficInlet'),
              span: 24,
              render: (value) => {
                if (!value) return '-';
                const { AppName } = value;
                return <span>{AppName}</span>;
              },
            },
          ]}
        />

        <CndTable
          fetchData={fetchData}
          primaryKey="primaryKey"
          columns={[
            {
              title: intl(
                'saenext.components.grayscale.SwimlaneGroupCard.LaneGroupAndRelatedApplications',
              ),
              dataIndex: 'AppName',
              width: '100%',
              cell: (value, index, record) => {
                return (
                  <div className="flex justify-between">
                    <span>{value}</span>
                    {currentAppId === record.AppId ? (
                      <Icon type="checkmark" style={{ color: '#009431' }} size="small" />
                    ) : null}
                  </div>
                );
              },
            },
          ]}
          recordCurrent
          refreshIndex={refreshIndex}
          pagination={false}
          search={{
            defaultDataIndex: 'AppName',
            defaultSelectedDataIndex: 'AppName',
            onlySupportOne: true,
            options: [
              {
                label: intl('saenext.components.grayscale.SwimlaneGroupCard.ApplicationName'),
                dataIndex: 'AppName',
                template: 'input',
                templateProps: {
                  placeholder: intl(
                    'saenext.components.grayscale.SwimlaneGroupCard.EnterAnApplicationName',
                  ),
                },
              },
            ],
          }}
          rowProps={handleRowProps}
          onRowClick={handleRowClick}
          useVirtual
          maxBodyHeight={hasFilterParams ? 170 : 200}
        />
      </div>
    </>
  );
};
export default SwimlaneGroupCard;
