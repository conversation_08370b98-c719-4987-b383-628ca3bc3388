import React, { FC, useState, useEffect, useContext } from 'react';
import {
  Field,
  Form,
  Input,
  Select,
  Radio,
  SlidePanel,
  intl,
  Message,
  DataFields,
  Button,
  CndTag,
  CndRcSelect,
  Icon,
} from '@ali/cnd';
import { get, noop, map, filter, isEmpty, intersection, forEach, set, find } from 'lodash';
import * as services from '~/services';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import TextRefreshButton from '~/components/shared/TextRefreshButton';
import ExternalLink from '~/components/shared/ExternalLink';
import { isForbidden } from '~/utils/authUtils';
import { UnAuthedLabel } from '~/components/shared/unauthedLabel';
import { compareVersions } from '~/utils/global';
import FeatureContext from '~/utils/featureContext';
import If from '../shared/If';
import CachedData from '~/cache/common';
import moment from 'moment';

const FormItem = Form.Item;

type Props = {
  type?: 'create' | 'edit';
  regionId: string;
  namespace: {
    NamespaceId: string;
    NamespaceName: string;
    VpcId?: string;
    VpcName?: string;
  };
  dataSource?: Record<string, any>;
  callback?: (groupId?: number) => void;
};

export enum Entrys {
  APIG = 'apig',
  Java = 'mse',
  MSE = 'mse-gw',
}
const SlideTitle = {
  create: intl('saenext.components.grayscale.CreateSwimlaneGroup.CreateALaneGroup'),
  edit: intl('saenext.components.grayscale.CreateSwimlaneGroup.EditALaneGroup'),
};

const CreateSwimlaneGroup: FC<Props> = (props) => {
  const {
    type = 'create',
    dataSource = {},
    regionId,
    namespace,
    callback = noop,
    children,
  } = props;
  const namespaceId = get(namespace, 'NamespaceId', '');
  const apigOpenRegions = get(window, 'ALIYUN_CONSOLE_GLOBAL.apigOpenRegions', []);
  const isShowApig = apigOpenRegions.includes(regionId);
  const [isShowing, setIsShowing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [entryType, setEntryType] = useState(isShowApig?Entrys.APIG:Entrys.MSE);
  const [mseGateways, setMseGateways] = useState([]);
  const [swimlaneApps, setSwimlaneApps] = useState([]);
  const [javaGatewayId, setJavaGatewayId] = useState('');
  const [loading, setLoading] = useState(false);
  const [authedMSE, setAuthedMSE] = useState(true);
  const [mseGatewayFeature, setMseGatewayFeature] = useState<{ [key: string]: any }>({});
  const [isSameVpc, setIsSameVpc] = useState(true);
  const [authedAPIG, setAuthedAPIG] = useState(true);
  const [apigIsOpen, setApigIsOpen] = useState(true);
  const [apigVersionValidate, setApigVersionValidate] = useState(true);
  const [apigRefreshIndex, setApigRefreshIndex] = useState(0);
  const { feature, webFeature } = useContext(FeatureContext);
  const AccountOpenTime = webFeature?.AccountOpenTime;
  const multipleVersionsTime = get(window, 'ALIYUN_CONSOLE_GLOBAL.multipleVersionsTime');
  // 是否多版本新用户 --- 轻量版+专业版
  const isMultipleNewAccount =
    AccountOpenTime && multipleVersionsTime
      ? AccountOpenTime > moment(multipleVersionsTime).valueOf()
      : false;
  // 判断老用户是否加白 --- 加白：轻量版+标准版+专业版  未加白：不区分版本
  const enableNewSaeVersion = feature?.enableNewSaeVersion;

  const { serverlessGateway, grayRelease, dynamicRoute } = mseGatewayFeature;

  const field = Field.useField();
  const { init, validate, resetToDefault, setValues, getValues } = field;

  const { EntryAppId, EntryAppType } = getValues() as any;

  useEffect(() => {
    if (!isShowing) return;
    if (type === 'create' && isShowApig) {
      handleApigIsOpen();
    }
    getMseGateway();
    getSwimlaneGroupApps();
  }, [isShowing, namespaceId]);

  useEffect(() => {
    if (!isShowing) return;
    if (type === 'create') return;
    if (isEmpty(dataSource)) return;
    const { GroupName, EntryAppType, EntryAppId, AppIds, SwimVersion } = dataSource;
    let _javaGatewayId = '';
    if (EntryAppType === Entrys.Java) {
      _javaGatewayId = dataSource.EntryAppId;
    }
    setEntryType(EntryAppType);
    setJavaGatewayId(_javaGatewayId);
    setValues({
      GroupName,
      EntryAppType,
      EntryAppId,
      AppIds,
      SwimVersion,
    });
  }, [isShowing, dataSource]);

  useEffect(() => {
    onMseGatewayChange();
  }, [EntryAppType, EntryAppId]);

  const handleApigIsOpen = async () => {
    const res = await services.CheckCommodityStatus({
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        setAuthedAPIG(!forbidden);
        if (!forbidden) cb?.();
      },
    });
    const isOpen = res?.data.commodities[0].enable;
    setApigIsOpen(isOpen);
  };

  const getApigGateway = async (search, pageNumber = 1) => {
    const res = await services.getApigListGateway({
      params: {
        pageSize: 100,
        pageNumber: pageNumber,
      },
      customErrorHandle: (error, _p, cb) => {
        const forbidden = isForbidden(error.code);
        setAuthedAPIG(!forbidden);
        if (!forbidden) cb?.();
      },
    });
    const items = res?.data?.items || [];
    const _apigGateways = map(items, (item) => {
      return {
        label: `${item?.name} / ${item?.gatewayId} / ${item?.version}`,
        value: item?.gatewayId,
        ...item,
      };
    });
    const _EntryAppId = field.getValue('EntryAppId') || '';
    if (_EntryAppId) {
      const currentGatewayInfo = find(_apigGateways, (item) => item.value === _EntryAppId);
      setApigVersionValidate(compareVersions(currentGatewayInfo?.version, '2.1.1') >= 0);
      setIsSameVpc(currentGatewayInfo?.vpc?.vpcId === get(namespace, 'VpcId', ''));
    }

    return {
      data: _apigGateways,
      total: res?.data?.totalSize || 0,
    };
  };

  const getMseGateway = async () => {
    setLoading(true);
    const allRes = await services.getListGateway({
      params: {
        PageNumber: 1,
        PageSize: 100,
        FilterParams: {},
      },
      customErrorHandle: (err, data, callback) => {
        callback && callback();
        setLoading(false);
      },
    });
    const allGateways = get(allRes, 'Data.Result', []);
    const allGatewaysRef = new Map();
    forEach(allGateways, (gateway) => {
      const { Name, GatewayUniqueId, GatewayVersion } = gateway;
      allGatewaysRef.set(GatewayUniqueId, {
        ...gateway,
        label: `${Name} / ${GatewayUniqueId} / v${GatewayVersion}`,
        value: GatewayUniqueId,
      });
    });
    const otherRes = await services.getSwimlaneGateway({
      params: {
        PageSize: 100,
        PageNumber: 1,
      },
      customErrorHandle: (err, data, callback) => {
        if (isForbidden(err.code)) {
          setAuthedMSE(false);
        } else {
          callback && callback();
          setAuthedMSE(true);
        }
        setLoading(false);
      },
    });
    const otherGateways = get(otherRes, 'Data.Result', []);
    const res = intersection(
      map(allGateways, (gateway) => gateway.GatewayUniqueId),
      map(otherGateways, (gateway) => gateway.GatewayUniqueId),
    );
    const _mseGateways = [];
    const vpcId = get(namespace, 'VpcId', '');
    forEach(res, (gatewayId) => {
      const gateway = allGatewaysRef.get(gatewayId);
      if (gateway) {
        // const { VpcId } = gateway;
        // if (vpcId === VpcId) {
        //   _mseGateways.push(gateway);
        // }
        _mseGateways.push(gateway);
      }
    });
    setLoading(false);
    setMseGateways(_mseGateways);
  };

  const getSwimlaneGroupApps = async () => {
    if (!namespaceId) return;
    const res = await services.getSwimlaneGroupApps({
      params: {
        NamespaceId: namespaceId,
      },
    });
    const { Data = [] } = res || {};
    const _swimlaneApps = map(Data, (app) => {
      const { AppId, AppName } = app;
      return {
        ...app,
        value: AppId,
        label: AppName,
      };
    });

    setSwimlaneApps(_swimlaneApps);
  };

  const onMseGatewayChange = () => {
    if (EntryAppType === Entrys.APIG || EntryAppType === Entrys.Java || !EntryAppId) {
      setMseGatewayFeature({});
      if (EntryAppType === Entrys.APIG) {
        setValues({
          SwimVersion: '2',
        });
      }
      return;
    }

    const mseGatewayItem = find(mseGateways, { GatewayUniqueId: EntryAppId });
    if (!mseGatewayItem) return;

    const { MseVersion, GatewayVersion = '' } = mseGatewayItem || {};

    const serverlessGateway = MseVersion === 'mse_serverless';
    const grayRelease = compareVersions(GatewayVersion, '1.2.13') >= 0;
    const dynamicRoute = compareVersions(GatewayVersion, '2.0.6') >= 0;
    setValues({
      SwimVersion: dynamicRoute ? '2' : '',
    });

    setMseGatewayFeature({
      serverlessGateway,
      grayRelease,
      dynamicRoute,
    });
  };

  const handleSubmit = async () => {
    const doSubmit = async (params) => {
      const response = await services.buildSwimlaneGroup({ params });
      if (!isEmpty(response) && get(response, 'Data.GroupId', false)) {
        Message.success(
          type === 'create'
            ? intl('saenext.components.grayscale.CreateSwimlaneGroup.TheLaneGroupIsCreated')
            : intl('saenext.components.grayscale.CreateSwimlaneGroup.TheLaneGroupHasBeen'),
        );
        setIsShowing(false);
        callback(get(response, 'Data.GroupId'));
      }
      setIsProcessing(false);
    };
    validate((error, values) => {
      if (error) return;
      setIsProcessing(true);
      const params = {
        ...values,
        GroupId: type === 'create' ? -1 : dataSource.GroupId,
        // @ts-ignore
        AppIds: JSON.stringify(values.AppIds),
        NamespaceId: namespaceId,
      };
      doSubmit(params);
    });
  };

  const handleCancel = () => {
    resetToDefault();
    setEntryType(Entrys.APIG);
    setIsShowing(false);
    setIsSameVpc(true);
  };

  const selectAppPlaceholder = () => {
    if (enableNewSaeVersion) {
      return intl('saenext.components.grayscale.CreateSwimlaneGroup.SelectALaneGroupThat.1');
    }
    if (isMultipleNewAccount) {
      return intl('saenext.components.grayscale.CreateSwimlaneGroup.SelectALaneGroupThat.2');
    }
    return intl('saenext.components.grayscale.CreateSwimlaneGroup.SelectALaneGroupFor');
  };

  return (
    <>
      <span onClick={() => setIsShowing((prev) => !prev)}>{children}</span>
      <SlidePanel
        title={SlideTitle[type]}
        width={700}
        onCancel={handleCancel}
        onOk={handleSubmit}
        okProps={{
          disabled:
            (!isEmpty(mseGatewayFeature) && (serverlessGateway || !grayRelease)) ||
            (entryType === Entrys.APIG && (!apigVersionValidate || !apigIsOpen)),
        }}
        isShowing={isShowing}
        isProcessing={isProcessing}
        cancelText={intl('button.cancel')}
        processingText={intl('button.processing')}
      >
        <DataFields
          dataSource={{}}
          style={{ marginBottom: 8 }}
          items={[
            {
              dataIndex: 'NamespaceName',
              label: (
                <>
                  <span style={{ color: '#c80000', marginRight: 4 }}>*</span>
                  <span>{intl('saenext.components.grayscale.CreateSwimlaneGroup.Namespace')}</span>
                </>
              ),

              span: 24,
              render: () => null,
            },
            {
              dataIndex: 'NamespaceName',
              label: <span>{get(namespace, 'NamespaceName', '')}</span>,
              span: 24,
              render: () => null,
            },
          ]}
        />

        <Form field={field}>
          <FormItem
            required
            label={intl('saenext.components.grayscale.CreateSwimlaneGroup.LaneGroupName')}
          >
            <Input
              {...init('GroupName', {
                initValue: dataSource.GroupName,
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.components.grayscale.CreateSwimlaneGroup.EnterALaneGroupName',
                    ),
                  },
                  {
                    message: intl(
                      'saenext.components.grayscale.CreateSwimlaneGroup.SupportsUppercaseAndLowercaseLetters',
                    ),
                    pattern: /^[0-9a-zA-Z\-_]{1,64}$/,
                  },
                ],
              })}
              minLength={1}
              maxLength={64}
              showLimitHint
              placeholder={intl(
                'saenext.components.grayscale.CreateSwimlaneGroup.SupportsUppercaseAndLowercaseLetters',
              )}
            />
          </FormItem>
          <FormItem
            required
            label={intl('saenext.components.grayscale.CreateSwimlaneGroup.EntryType')}
            help={
              entryType === Entrys.MSE
                ? intl('saenext.components.grayscale.CreateSwimlaneGroup.MseCloudNativeGatewaysOf')
                : null
            }
          >
            <Radio.Group
              {...init('EntryAppType', {
                initValue: dataSource.EntryAppType || (isShowApig ? Entrys.APIG : Entrys.MSE),
                props: {
                  onChange: (val: Entrys) => {
                    setEntryType(val);
                    resetToDefault(['EntryAppId', 'AppIds']);
                    setIsSameVpc(true);
                  },
                },
              })}
              disabled={type === 'edit'}
            >
              {isShowApig && (
                <Radio id={Entrys.APIG} value={Entrys.APIG}>
                  {intl('saenext.components.grayscale.CreateSwimlaneGroup.CloudNativeApiGateway')}
                </Radio>
              )}
              <Radio id={Entrys.MSE} value={Entrys.MSE}>
                <TextWithBalloon
                  align="tr"
                  text={intl(
                    'saenext.components.grayscale.CreateSwimlaneGroup.MseCloudNativeGateway',
                  )}
                  tips={
                    <>
                      <span>
                        {intl(
                          'saenext.components.grayscale.CreateSwimlaneGroup.TheGatewayBaselineRouteMust',
                        )}
                      </span>
                      <a href={CachedData.confLink('help:sae:full-link-gray-scale-based-on-mse')} target="_blank">
                        {intl('saenext.components.grayscale.CreateSwimlaneGroup.AccessDocuments')}
                      </a>
                    </>
                  }
                />
              </Radio>
              <Radio id={Entrys.Java} value={Entrys.Java}>
                <TextWithBalloon
                  align="tr"
                  text={intl('saenext.components.grayscale.CreateSwimlaneGroup.JavaServiceGateway')}
                  tips={
                    <>
                      <span>
                        {intl(
                          'saenext.components.grayscale.CreateSwimlaneGroup.TheJavaServiceGatewayThat',
                        )}
                      </span>
                      <a href={CachedData.confLink('help:sae:full-link-gray-scale-based-on-java')} target="_blank">
                        {intl('saenext.components.grayscale.CreateSwimlaneGroup.AccessDocuments')}
                      </a>
                    </>
                  }
                />
              </Radio>
            </Radio.Group>
          </FormItem>
          {entryType === Entrys.APIG && apigIsOpen && (
            <>
              <FormItem
                required
                requiredMessage={intl(
                  'saenext.components.grayscale.CreateSwimlaneGroup.SelectAnIngressGateway',
                )}
                label={
                  <UnAuthedLabel
                    text={intl(
                      'saenext.components.grayscale.CreateSwimlaneGroup.LaneGroupTrafficInlet',
                    )}
                    authed={authedAPIG}
                    authKey="AliyunAPIGReadOnlyAccess"
                  />
                }
              >
                <CndRcSelect
                  name="EntryAppId"
                  fetchData={getApigGateway}
                  style={{ width: '100%' }}
                  placeholder={intl(
                    'saenext.components.grayscale.CreateSwimlaneGroup.SelectAnIngressGatewayOnly',
                  )}
                  onChange={(val, actionType, item: any) => {
                    item?.version &&
                      setApigVersionValidate(compareVersions(item?.version, '2.1.1') >= 0);
                    setIsSameVpc(item?.vpc?.vpcId === get(namespace, 'VpcId', ''));
                  }}
                  refreshIndex={apigRefreshIndex}
                  itemRender={(item) => {
                    const itemVpcId = get(item, 'vpc.vpcId', '');
                    const vpcId = get(namespace, 'VpcId', '');
                    return (
                      <div>
                        <span>{item.label}</span>
                        {itemVpcId && (
                          <CndTag
                            tagText={
                              itemVpcId === vpcId
                                ? intl('saenext.components.grayscale.CreateSwimlaneGroup.SameVpc')
                                : intl(
                                    'saenext.components.grayscale.CreateSwimlaneGroup.DifferentVpcs',
                                  )
                            }
                            tagType={itemVpcId === vpcId ? 'info' : 'warning'}
                            style={{ marginLeft: 4 }}
                          />
                        )}
                      </div>
                    );
                  }}
                />

                <div className="flex">
                  <TextRefreshButton onClick={() => setApigRefreshIndex(Date.now())} />
                  <ExternalLink
                    className="ml-l"
                    label={intl(
                      'saenext.components.grayscale.CreateSwimlaneGroup.CreateACloudNativeApi',
                    )}
                    // @ts-ignore
                    url={`${CachedData.confLink('feature:apigw:url')}/#/${regionId}/gateway`}
                  />
                </div>
                {!apigVersionValidate && (
                  <Message type="warning" className="mt-s">
                    {intl.html('saenext.components.grayscale.CreateSwimlaneGroup.Apig.GatewayVersion.validate.new',{
                      href:`${CachedData.confLink('feature:apigw:url')}/#/${regionId}/gateway`
                    })}
                  </Message>
                )}
                {!isSameVpc && (
                  <Message type={'warning'} className="mt-s">
                    {intl(
                      'saenext.components.grayscale.CreateSwimlaneGroup.TheVpcWhereTheSelected',
                    )}
                  </Message>
                )}
              </FormItem>
              <FormItem className="none">
                <Input {...init('SwimVersion')} />
              </FormItem>
              <FormItem
                required
                label={intl(
                  'saenext.components.grayscale.CreateSwimlaneGroup.LaneGroupInvolvesApplications',
                )}
              >
                <Select
                  {...init('AppIds', {
                    initValue: [],
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.components.grayscale.CreateSwimlaneGroup.SelectALaneGroupFor',
                        ),
                      },
                    ],
                  })}
                  showSearch
                  hasClear
                  mode="multiple"
                  dataSource={swimlaneApps}
                  style={{ width: '100%' }}
                  placeholder={selectAppPlaceholder()}
                />
              </FormItem>
            </>
          )}
          {entryType === Entrys.MSE && (
            <>
              <FormItem
                required
                label={
                  <UnAuthedLabel
                    text={intl(
                      'saenext.components.grayscale.CreateSwimlaneGroup.LaneGroupTrafficInlet',
                    )}
                    authed={authedMSE}
                    authKey="AliyunMSEReadOnlyAccess"
                  />
                }
              >
                <Select
                  {...init('EntryAppId', {
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.components.grayscale.CreateSwimlaneGroup.SelectAnIngressGateway',
                        ),
                      },
                    ],
                  })}
                  showSearch
                  hasClear
                  state={loading ? 'loading' : null}
                  dataSource={mseGateways}
                  style={{ width: '100%' }}
                  placeholder={intl(
                    'saenext.components.grayscale.CreateSwimlaneGroup.SelectAnIngressGatewayOnly',
                  )}
                  itemRender={(item) => {
                    const { Name, GatewayUniqueId, GatewayVersion, VpcId } = item;
                    const vpcId = get(namespace, 'VpcId', '');
                    return (
                      <div>
                        <span>{`${Name} / ${GatewayUniqueId} / v${GatewayVersion}`}</span>
                        {VpcId && (
                          <CndTag
                            tagText={
                              VpcId === vpcId
                                ? intl('saenext.components.grayscale.CreateSwimlaneGroup.SameVpc')
                                : intl(
                                    'saenext.components.grayscale.CreateSwimlaneGroup.DifferentVpcs',
                                  )
                            }
                            tagType={VpcId === vpcId ? 'info' : 'warning'}
                            style={{ marginLeft: 4 }}
                          />
                        )}
                      </div>
                    );
                  }}
                  onChange={(val, actionType, item: any) => {
                    field.setValue('EntryAppId', val);
                    setIsSameVpc(item?.VpcId === get(namespace, 'VpcId', ''));
                  }}
                />

                <div className="flex">
                  <TextRefreshButton onClick={() => getMseGateway()} />
                  <ExternalLink
                    className="ml-l"
                    label={intl(
                      'saenext.components.grayscale.CreateSwimlaneGroup.CreateAnMseCloudNative',
                    )}
                    // @ts-ignore
                    url={`${CachedData.confLink('feature:mse:url')}/#/microgw?region=${regionId}`}
                  />
                </div>

                {!isSameVpc && (
                  <Message type={'warning'} className="mt-s">
                    {intl(
                      'saenext.components.grayscale.CreateSwimlaneGroup.TheVpcWhereTheSelected',
                    )}
                  </Message>
                )}

                <If condition={!isEmpty(mseGatewayFeature)}>
                  <div className="mt-s">
                    {serverlessGateway ? (
                      <Message type={'warning'}>
                        {intl(
                          'saenext.components.grayscale.CreateSwimlaneGroup.GatewayVersionServerlessDoesNot',
                        )}
                      </Message>
                    ) : (
                      <>
                        <If condition={!grayRelease}>
                          <Message type={'warning'}>
                            {intl(
                              'saenext.components.grayscale.CreateSwimlaneGroup.TheGatewayVersionYouSelected',
                            )}
                          </Message>
                        </If>
                        <If condition={grayRelease && !dynamicRoute}>
                          <Message type={'warning'}>
                            {intl(
                              'saenext.components.grayscale.CreateSwimlaneGroup.TheGatewayVersionYouSelected.1',
                            )}
                            <br />
                            {intl(
                              'saenext.components.grayscale.CreateSwimlaneGroup.NoLongerRelyOnLabel',
                            )}
                            <br />
                            {intl(
                              'saenext.components.grayscale.CreateSwimlaneGroup.NoLongerRelyOnStatic',
                            )}
                            <br />
                            {intl.html('saenext.components.grayscale.CreateSwimlaneGroup.UpgradeTheGatewayVersion.Tip',{
                              href:`${CachedData.confLink('feature:mse:url')}/#/microgw?region=${regionId}`
                            })}
                          </Message>
                        </If>
                        <If condition={dynamicRoute}>
                          <Message type={'notice'}>
                            {intl(
                              'saenext.components.grayscale.CreateSwimlaneGroup.TheRouteUnderTheGateway',
                            )}
                            <a
                              href={CachedData.confLink('help:mse:implement-an-end-to-end-canary-release-by-using-mse')}
                              target="_blank"
                            >
                              {intl('saenext.components.grayscale.CreateSwimlaneGroup.LearnMore')}
                            </a>
                          </Message>
                        </If>
                      </>
                    )}
                  </div>
                </If>
              </FormItem>

              <FormItem className="none">
                <Input {...init('SwimVersion')} />
              </FormItem>

              <FormItem
                required
                label={intl(
                  'saenext.components.grayscale.CreateSwimlaneGroup.LaneGroupInvolvesApplications',
                )}
              >
                <Select
                  {...init('AppIds', {
                    initValue: [],
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.components.grayscale.CreateSwimlaneGroup.SelectALaneGroupFor',
                        ),
                      },
                    ],
                  })}
                  showSearch
                  hasClear
                  mode="multiple"
                  dataSource={swimlaneApps}
                  style={{ width: '100%' }}
                  placeholder={selectAppPlaceholder()}
                />
              </FormItem>
            </>
          )}
          {entryType === Entrys.Java && (
            <>
              <FormItem
                required
                label={intl(
                  'saenext.components.grayscale.CreateSwimlaneGroup.LaneGroupTrafficInlet',
                )}
              >
                <Select
                  {...init('EntryAppId', {
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.components.grayscale.CreateSwimlaneGroup.SelectAnEntryApplication',
                        ),
                      },
                    ],

                    props: {
                      onChange: (val: string) => {
                        setJavaGatewayId(val);
                        resetToDefault(['AppIds']);
                      },
                    },
                  })}
                  showSearch
                  hasClear
                  dataSource={swimlaneApps}
                  style={{ width: '100%' }}
                  placeholder={intl(
                    'saenext.components.grayscale.CreateSwimlaneGroup.SelectAnEntryApplication',
                  )}
                />
              </FormItem>
              <FormItem
                required
                label={intl(
                  'saenext.components.grayscale.CreateSwimlaneGroup.LaneGroupInvolvesApplications',
                )}
              >
                <Select
                  {...init('AppIds', {
                    initValue: [],
                    rules: [
                      {
                        required: true,
                        message: intl(
                          'saenext.components.grayscale.CreateSwimlaneGroup.SelectALaneGroupFor',
                        ),
                      },
                    ],
                  })}
                  showSearch
                  hasClear
                  mode="multiple"
                  dataSource={filter(swimlaneApps, (app) => app.value !== javaGatewayId)}
                  style={{ width: '100%' }}
                  placeholder={selectAppPlaceholder()}
                />
              </FormItem>
            </>
          )}
          {entryType === Entrys.APIG && !apigIsOpen && (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                background: '#f6f6f6',
                width: '100%',
                height: 100,
                borderRadius: 2,
              }}
            >
              <div style={{ marginBottom: 8 }}>
                <Icon type="warning" size="xs" style={{ color: '#0070cc', marginRight: '10px' }} />
                <span>
                  {intl('saenext.components.grayscale.CreateSwimlaneGroup.TheProductHasNotBeen')}
                </span>
              </div>
              <div>
                <Button
                  type="primary"
                  size="small"
                  style={{ marginRight: 8 }}
                  onClick={() => {
                    window.open(
                      `${CachedData.confLink('feature:common-buy')}/?commodityCode=apigateway_apim_public_cn`,
                      '_blank',
                    );
                  }}
                >
                  {intl('saenext.components.grayscale.CreateSwimlaneGroup.GoToActivate')}
                </Button>
                <Button
                  type="normal"
                  size="small"
                  onClick={() => {
                    window.open(
                      CachedData.confLink('help:apigw:what-is-cloud-native-api-gateway'),
                      '_blank',
                    );
                  }}
                >
                  {intl('saenext.components.grayscale.CreateSwimlaneGroup.LearnMore')}
                </Button>
              </div>
            </div>
          )}
        </Form>
      </SlidePanel>
    </>
  );
};

export default CreateSwimlaneGroup;
