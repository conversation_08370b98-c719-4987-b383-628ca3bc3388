import { intl } from '@ali/cnd';
export const FILTERS = {
  APP_NAME: 'appName',
  APP_ID: 'appIds',
};

export const STATES = {
  'template-0': {
    label: intl('saenext.components.job-list.constant.TaskPublishingFailed'),
    state: 'error',
    intro: intl('saenext.components.job-list.constant.PleaseReEditTheTask'),
    link: '#/JobList/JobDetail/ApplyUpdate',
  },
  0: {
    label: intl('saenext.components.job-list.constant.NotExecuted'),
    state: 'disabled',
    intro: intl('saenext.components.job-list.constant.ViewTaskExecutionRecords'),
    link: '#/JobList/JobRecordDetail',
  },
  1: {
    label: intl('saenext.components.job-list.constant.SuccessfulExecution'),
    state: 'success',
  },
  2: {
    label: intl('saenext.components.job-list.constant.ExecutionFailed'),
    state: 'error',
    intro: intl('saenext.components.job-list.constant.ViewTaskDetails'),
    link: '#/JobList/JobRecordDetail',
  },
  3: {
    label: intl('saenext.components.job-list.constant.Executing'),
    state: 'loading',
  },
};

export const orderIndexMap = {
  AppName: 'name',
};

export const orderReverseMap = {
  asc: false,
  desc: true,
  default: '',
};

export const recordStateMap = [
  {
    label: intl('saenext.components.job-list.constant.All'),
    value: '',
  },
  {
    label: intl('saenext.components.job-list.constant.NotExecuted'),
    value: '0',
  },
  {
    label: intl('saenext.components.job-list.constant.SuccessfulExecution'),
    value: '1',
  },
  {
    label: intl('saenext.components.job-list.constant.ExecutionFailed'),
    value: '2',
  },
  {
    label: intl('saenext.components.job-list.constant.Executing'),
    value: '3',
  },
];
