import { intl } from '@ali/cnd';
import React, { useState } from 'react';
import { SlidePanel, Field } from '@ali/cnd';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import { ImageConfigField } from '~/components/shared/Sidecar/ImageConfig';
import { Type } from '~/components/shared/ImageSelectorField/utils';
import { isEmpty } from 'lodash';

const UpdateImageSlide = (props) => {
  const { children, imageInfo, vpcId, namespaceId, setImageInfo } = props;
  const field = Field.useField();
  const [show, setShow] = useState(false);

  const onSubmit = () => {
    !isEmpty(field.getValues()) && setImageInfo({ ...field.getValues() });
    toggleVisible();
  };
  const toggleVisible = () => {
    setShow(!show);
  };

  return (
    <>
      <AddPropsWrap onClick={toggleVisible}>{children}</AddPropsWrap>
      <SlidePanel
        title={intl('saenext.job-list.UpdateImageSlide.ModifyAnImage')}
        isShowing={show}
        width={1000}
        onMaskClick={toggleVisible}
        onClose={toggleVisible}
        onCancel={toggleVisible}
        onOk={onSubmit}
      >
        <ImageConfigField
          field={field}
          appConfig={{ VpcId: vpcId }}
          hideSecret
          supportType={[Type.cr, Type.demo, Type.custom, Type.otherAccount]}
          appType="job"
          namespaceId={namespaceId}
          defaultValue={imageInfo}
        />
      </SlidePanel>
    </>
  );
};
export default UpdateImageSlide;
