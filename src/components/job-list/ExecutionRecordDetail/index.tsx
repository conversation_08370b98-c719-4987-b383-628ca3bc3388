import React, { useEffect, useRef, useState } from 'react';
import { intl, Copy, Balloon, Icon, CndForm, Button, Dialog, Message } from '@ali/cnd';
import services from '~/services';
import moment from 'moment';
import { get, find, isEmpty, isEqual } from 'lodash';
import { recordStateMap } from '../constant';
import InstanceList from '~/components/app-detail/micro-app/basic-info/InstanceGroup/InstanceList';

const ExecutionRecordDetail = (props) => {
  const { history, regionId, appId, jobId } = props;
  const [appConfig, setAppConfig] = useState<any>({});
  const [jobStatus, setJobStatus] = useState({});
  const [vSwitchList, setVSwitchList] = useState([]);
  const [groupInfo, setGroupInfo] = useState({});
  const [lastChangeOrder, setLastChangeOrder] = useState<any>({});

  const refreshRef = useRef(null);

  useEffect(() => {
    refreshRef.current = refreshJob;
  });

  useEffect(() => {
    getJobConfig();
    getJobStatus();
    getJobGroups();
    getJobOrderStatus();
  }, [jobId]);

  useEffect(() => {
    refreshRef.current();

    const interval = setInterval(() => {
      refreshRef.current();
    }, 10000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const refreshJob = () => {
    getJobConfig();
    getJobStatus();
    getJobOrderStatus();
  };

  const getJobConfig = async () => {
    const { Data } = await services.DescribeJob({
      params: {
        AppId: appId,
        JobId: jobId,
      },
    });
    if (isEmpty(Data) || isEqual(appConfig, Data)) return;
    setAppConfig(Data);
    const { VpcId, VSwitchId } = Data;
    getVSwtich(VpcId, VSwitchId);
  };
  const getJobStatus = async () => {
    const data = await services.DescribeJobStatus({
      params: {
        AppId: appId,
        JobId: jobId,
      },
    });
    if (!data) return;
    const { Data } = data;
    if (isEmpty(Data) || isEqual(jobStatus, Data)) return;
    setJobStatus(Data);
  };

  const getJobGroups = async () => {
    const { Data = [] } =
      (await services.DescribeApplicationGroups(
        {
          AppId: appId,
        },
        true,
      )) || {};
    // const GroupId = get(Data, '[0].GroupId');
    setGroupInfo(Data[0]);
  };

  const getVSwtich = async (vpcId, vSwitchId) => {
    if (!vpcId) {
      return;
    }
    const { VSwitches: { VSwitch = [] } = {} } =
      (await services.DescribeVSwitches({
        params: {
          VpcId: vpcId,
          VSwitchId: vSwitchId,
        },
        customErrorHandle: (error, _p, cb) => {
          cb();
          return {};
        },
      })) || {};
    setVSwitchList(VSwitch);
  };

  const getJobOrderStatus = async () => {
    const data = await services.ListChangeOrders({
      params: {
        AppId: appId,
        CurrentPage: 1,
        PageSize: 1,
      },
    });
    if (!data) return;
    const { Data } = data;
    const _lastChangeOrder = get(Data, 'ChangeOrderList.[0]');
    if (isEmpty(_lastChangeOrder) || isEqual(_lastChangeOrder, lastChangeOrder)) return;
    setLastChangeOrder(_lastChangeOrder);
  };

  const handleDeleteJobHistory = async () => {
    Dialog.show({
      title: intl('saenext.job-list.ExecutionRecordDetail.DeleteATaskRecord'),
      content: intl('saenext.job-list.ExecutionRecordDetail.AfterTheTaskIsDeleted'),
      okProps: { children: intl('saenext.common.dialog.ok') },
      cancelProps: { children: intl('saenext.common.dialog.cancel') },
      onOk: async () => {
        const res = await services.DeleteHistoryJob({
          params: {
            AppId: appId,
            JobId: jobId,
          },
        });
        if (res.Success) {
          Message.success(intl('saenext.job-list.ExecutionRecordDetail.DeletedSuccessfully'));
          history.push(`/${regionId}/job-list/${appId}/record`);
        }
      },
    });
  };

  const renderState = (state) => {
    const stateObj = find(recordStateMap, {
      value: String(state),
    });
    return get(stateObj, 'label');
  };

  const renderMessage = (message) => {
    if (message.includes('quota')) {
      return intl('saenext.job-list.ExecutionRecordDetail.YourTaskQuotaHasExceeded');
    }
    if (message.includes('suspend')) {
      return intl('saenext.job-list.ExecutionRecordDetail.YouManuallyStoppedTheTask');
    }
    if (message.includes('forbid')) {
      return intl('saenext.job-list.ExecutionRecordDetail.YourCurrentTaskConcurrencyPolicy');
    }
    return message;
  };

  const renderPolicy = (value) => {
    let hint = '';
    switch (value) {
      case 'Forbid':
        hint = intl('saenext.job-list.ExecutionRecordDetail.ConcurrentRunningIsProhibitedWhen');
        break;
      case 'Allow':
        hint = intl('saenext.job-list.ExecutionRecordDetail.AllowConcurrentRunningOfTasks');
        break;
      case 'Replace':
        hint = intl('saenext.job-list.ExecutionRecordDetail.WhenANewTaskIs');
        break;
      default:
        break;
    }
    return hint;
  };

  const formItems = [
    {
      dataIndex: 'JobId',
      label: intl('saenext.job-list.ExecutionRecordDetail.TaskId'),
      render: (val) => <Copy text={val}>{val}</Copy>,
    },
    {
      dataIndex: 'NamespaceId',
      label: intl('saenext.job-list.ExecutionRecordDetail.Namespace'),
    },
    {
      dataIndex: 'State',
      label: intl('saenext.job-list.ExecutionRecordDetail.Status'),
      render: (val, { Active, Succeeded, Failed, Replicas, Message }) => (
        <div>
          {renderState(val)}
          {Message && (
            <Balloon
              align="t"
              trigger={
                <Icon
                  style={{ position: 'relative', color: '#0070cc', marginLeft: 10 }}
                  type="help"
                  size="xs"
                />
              }
              closable={false}
            >
              {renderMessage(Message)}
            </Balloon>
          )}
        </div>
      ),
    },
    {
      dataIndex: 'ConcurrencyPolicy',
      label: intl('saenext.job-list.ExecutionRecordDetail.ConcurrencyPolicy'),
      render: (val) => (
        <div>
          <span>{val}</span>
          <Balloon
            align="t"
            trigger={
              <Icon
                style={{ position: 'relative', color: '#0070cc', marginLeft: 10 }}
                type="help"
                size="xs"
              />
            }
            closable={false}
          >
            {renderPolicy(val)}
          </Balloon>
        </div>
      ),
    },
    {
      label: intl('saenext.job-list.ExecutionRecordDetail.NumberOfRunningSuccessfulFailed'),
      render: (val, { Active, Succeeded, Failed }) => (
        <div>
          {Active} / {Succeeded} / {Failed}
        </div>
      ),
    },
    {
      dataIndex: 'Replicas',
      label: intl('saenext.job-list.ExecutionRecordDetail.NumberOfConcurrentInstances'),
    },
    {
      dataIndex: 'Timeout',
      label: intl('saenext.job-list.ExecutionRecordDetail.Timeout'),
      render: (val) => (val ? val : intl('saenext.job-list.ExecutionRecordDetail.NotSet')),
    },
    {
      dataIndex: 'BackoffLimit',
      label: intl('saenext.job-list.ExecutionRecordDetail.NumberOfRetries'),
    },
    {
      dataIndex: 'StartTime',
      label: intl('saenext.job-list.ExecutionRecordDetail.CreationTime'),
      render: (val) => (val ? moment(val * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      dataIndex: 'CompletionTime',
      label: intl('saenext.job-list.ExecutionRecordDetail.CompletionTime'),
      render: (val) => (val ? moment(val * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
  ];

  return (
    <div style={{ position: 'relative' }}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row-reverse',
          justifyContent: 'space-between',
          margin: '-50px 0 16px',
          position: 'absolute',
          right: 0,
        }}
      >
        <Button type="primary" onClick={handleDeleteJobHistory}>
          {intl('saenext.job-list.ExecutionRecordDetail.DeleteATaskRecord')}
        </Button>
      </div>
      <div className="mb-l">
        <CndForm
          formType="detail"
          dataSource={{ ...appConfig, ...jobStatus }}
          items={[...formItems] as any}
        />
      </div>
      {!isEmpty(groupInfo) && (
        <InstanceList
          appId={appId}
          jobId={jobId}
          appConfig={appConfig}
          appStatus={lastChangeOrder}
          vSwitchList={vSwitchList}
          groupInfo={groupInfo}
          pageType="job"
        />
      )}
    </div>
  );
};

export default ExecutionRecordDetail;
