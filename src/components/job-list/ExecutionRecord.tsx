import React, { useEffect, useState } from 'react';
import { intl,Message, CndTable, Copy, Actions, LinkButton, Balloon, Icon, Dialog, Select } from '@ali/cnd';
import { getParams } from '~/utils/global';
import services from '~/services';
import { get, find } from 'lodash';
import moment from 'moment';
import { recordStateMap } from './constant';
const ExecutionRecord = (props) => {
  const { history, regionId, appId } = props;
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [totalSize, setTotalSize] = useState(0);
  const [selectedState, setSelectedState] = useState('');

  useEffect(() => {
    setRefreshIndex(+new Date());
  }, [selectedState]);

  const fetchJobHistory = async (params) => {
    const res = await services.DescribeJobHistory({
      params: {
        CurrentPage: params.current,
        PageSize: params.pageSize,
        State: selectedState,
        AppId: appId,
      },
    });
    setTotalSize(get(res, 'Data.TotalSize', 0));
    return {
      data: get(res, 'Data.Jobs', []),
      total: get(res, 'Data.TotalSize', 0),
    };
  };

  const handleDeleteJobHistory = async (record) => {
    Dialog.show({
      title: intl('saenext.components.job-list.ExecutionRecord.DeleteATaskRecord'),
      content: intl('saenext.components.job-list.ExecutionRecord.AfterTheTaskIsDeleted'),
      okProps: { children: intl('saenext.common.dialog.ok') },
      cancelProps: { children: intl('saenext.common.dialog.cancel') },
      onOk: async () => {
        const res = await services.DeleteHistoryJob({
          params: {
            AppId: appId,
            JobId: record.JobId,
          },
        });
        if (res.Success) {
          Message.success(intl('saenext.components.job-list.ExecutionRecord.DeletedSuccessfully'));
          setRefreshIndex(+new Date());
        }
      },
    });
  };

  const renderState = (state) => {
    const stateObj = find(recordStateMap, {
      value: String(state),
    });
    return get(stateObj, 'label');
  };

  const renderMessage = (message) => {
    if (message.includes('quota')) {
      return intl('saenext.components.job-list.ExecutionRecord.YourTaskQuotaHasExceeded');
    }
    if (message.includes('suspend')) {
      return intl('saenext.components.job-list.ExecutionRecord.YouManuallyStoppedTheTask');
    }
    if (message.includes('forbid')) {
      return intl('saenext.components.job-list.ExecutionRecord.YourCurrentTaskConcurrencyPolicy');
    }
    return message;
  };

  const columns = [
    {
      key: 'JobId',
      title: intl('saenext.components.job-list.ExecutionRecord.TaskId'),
      dataIndex: 'JobId',
      width: 300,
      cell: (value, index, record) => {
        return (
          <Copy text={value}>
            <LinkButton
              style={{ textAlign: 'left' }}
              onClick={() => {
                history.push(`/${regionId}/job-list/${appId}/record/${record.JobId}`);
              }}
            >
              {value}
            </LinkButton>
          </Copy>
        );
      },
    },
    {
      key: 'State',
      title: intl('saenext.components.job-list.ExecutionRecord.ExecutionStatus'),
      dataIndex: 'State',
      width: 300,
      cell: (value, index, record) => {
        const { Active, Succeeded, Failed, Message } = record;
        return (
          <div>
            {renderState(value)}
            {Message && (
              <Balloon
                align="t"
                trigger={
                  <Icon
                    style={{ position: 'relative', color: '#0070cc', marginLeft: 10 }}
                    type="help"
                    size="xs"
                  />
                }
                closable={false}
              >
                {renderMessage(Message)}
              </Balloon>
            )}
          </div>
        );
      },
    },
    {
      key: 'Active',
      title: intl('saenext.components.job-list.ExecutionRecord.NumberOfRunningSuccessfulFailed'),
      dataIndex: 'Active',
      width: 250,
      cell: (value, index, record) => {
        const { Active, Succeeded, Failed } = record;
        if (typeof Active !== 'undefined') {
          return (
            <div>
              {Active} / {Succeeded} / {Failed}
            </div>
          );
        } else {
          return '-';
        }
      },
    },
    {
      key: 'StartTime',
      title: intl('saenext.components.job-list.ExecutionRecord.CreationTime'),
      dataIndex: 'StartTime',
      width: 300,
      cell: (value) => {
        return <div>{value ? moment(value * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>;
      },
    },
    {
      key: 'CompletionTime',
      title: intl('saenext.components.job-list.ExecutionRecord.CompletionTime'),
      dataIndex: 'CompletionTime',
      width: 300,
      cell: (value) => {
        return <div>{value ? moment(value * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>;
      },
    },
    {
      key: 'opt',
      title: intl('saenext.components.job-list.ExecutionRecord.Operation'),
      dataIndex: 'opt',
      width: 200,
      cell: (value, index, record) => {
        return (
          <Actions>
            <LinkButton key={index} onClick={() => handleDeleteJobHistory(record)}>
              {intl('saenext.components.job-list.ExecutionRecord.Delete')}
            </LinkButton>
          </Actions>
        );
      },
    },
  ];

  return (
    <div>
      <Message type="notice" className="mb-l">
        {intl('saenext.components.job-list.ExecutionRecord.ATotalOfTotalsizeRecords', {
          totalSize: totalSize,
        })}
      </Message>
      <CndTable
        fetchData={fetchJobHistory}
        columns={columns}
        showRefreshButton
        refreshIndex={refreshIndex}
        // search={{
        //   defaultDataIndex: 'state',
        //   defaultSelectedDataIndex: 'state',
        //   options: [
        //     {
        //       label: '执行状态',
        //       dataIndex: 'state',
        //       template: 'select',
        //       templateProps: {
        //         placeholder: '请选择执行状态',
        //         dataSource: recordStateMap,
        //       },
        //     },
        //   ],
        // }}
        operation={
          <Select
            value={selectedState}
            dataSource={recordStateMap}
            onChange={(val: string) => {
              setSelectedState(val);
            }}
            style={{ width: 200 }}
          />
        }
      />
    </div>
  );
};

export default ExecutionRecord;
