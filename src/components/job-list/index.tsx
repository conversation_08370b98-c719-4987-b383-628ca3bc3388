import React, { useState, useContext, useEffect } from 'react';
import CndTable, { ISearch } from '@ali/cnd-table';
import services from '~/services';
import { getParams, setSearchParams } from '~/utils/global';
import _ from 'lodash';
import {
  Form,
  Field,
  Copy,
  Truncate,
  Actions,
  Button,
  Balloon,
  Icon,
  ConsoleContext,
  Message,
  useHistory,
  DataFields,
  Dialog,
  StatusIndicator,
  intl,
  ErrorPrompt2 as errorPrompt,
  RegionGuidance,
  Link,
  Badge,
  SlidePanel,
  Input,
} from '@ali/cnd';
import '@ali/xconsole-rc-tags/dist/index.css';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import ExternalLink from '~/components/shared/ExternalLink';
import CronLaterBalloon from '~/components/shared/CronLaterBalloon';
import { jsonParse } from '~/utils/transfer-data';
import CachedData from '~/cache/common';
import * as C from './constant';
import SelectedApp from '../app-list/micro-app/components/BatchOperation/SelectedApp';
import UpdateImageSlide from './UpdateImageSlide';

const { LinkButton } = Actions;

type Props = {
  history: any;
  namespaceList?: any[];
  namespaceId: string;
  v1Micro?: boolean;
};

const JobList = (props: Props) => {
  const field = Field.useField();
  const { namespaceList, namespaceId, v1Micro = false } = props;
  const NamespaceId = !namespaceId || namespaceId === 'all' ? '' : namespaceId;
  const { region } = useContext(ConsoleContext);
  const history = useHistory();
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const currentRegionId = region.getCurrentRegionId();
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [auth, setAuth] = useState(true);
  const [isShowing, setIsShowing] = useState(false);
  const [selectedApps, setSelectedApps] = useState<string[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [vpcId, setVpcId] = useState('');
  const [curImageInfo, setCurImageInfo] = useState({
    ImageUrl: '',
  });
  const [updateImageLoading, setUpdateImageLoading] = useState(false);

  const isPre = CachedData.isPre();

  useEffect(() => {
    getVpcInfo();
  }, []);

  useEffect(() => {
    if (isShowing) {
      setSelectedApps(selectedRowKeys);
    }
  }, [selectedRowKeys, isShowing]);

  const getVpcInfo = async () => {
    if (!NamespaceId) return;
    const res = await services.getNamespaceDescribeV2({
      NamespaceId: NamespaceId,
    });
    setVpcId(res?.Data?.VpcId || '');
  };

  const fetchData = async (params) => {
    // 未知命名空间
    if (NamespaceId === 'none') {
      return {
        data: [],
        total: 0,
      };
    }

    const filtersMap = _.values(C.FILTERS);
    setSearchParams(_.pick(params, filtersMap));
    const keys = Object.keys(params);
    const fieldType = _.find(keys, (k) => {
      return _.includes(filtersMap, k);
    });
    const _params = {
      CurrentPage: params.current,
      PageSize: params.pageSize,
      NamespaceId,
      OrderBy: params.order !== 'default' ? C.orderIndexMap[params.orderBy] : '',
      Reverse: C.orderReverseMap[params.order],
      FieldType: fieldType,
      FieldValue: _.get(params, fieldType),
      Workload: 'job',
    };

    const { Data: { Applications = [], TotalSize = 0 } = {} } =
      (await services.ListJobs({
        params: _params,
        customErrorHandle(err, serviceConfig, callback) {
          if (err) {
            setAuth(err.code !== 'AuthenticationFailed');
            return {
              data: [],
              total: 0,
            };
          }
        },
      })) || {};

    return {
      data: Applications.map((item) => {
        return {
          ...item,
          primaryKey: `${item.AppId}&&${item?.ImageUrl}`,
        };
      }),
      total: TotalSize,
    };
  };

  const renderNamespace = (NamespaceId = currentRegionId, index, record) => {
    const namespace = _.find(namespaceList, { NamespaceId });
    const { NamespaceName = '' } = namespace || {};

    if (!namespace) {
      return NamespaceId;
    }

    const trigger = (
      <a
        href={`/${region.getCurrentRegionId()}/namespace/${NamespaceId}/base/?name=${NamespaceName}`}
      >
        {NamespaceName}
      </a>
    );

    return (
      <Balloon.Tooltip trigger={trigger}>
        <DataFields
          dataSource={namespace}
          items={[
            {
              dataIndex: 'NamespaceName',
              label: intl('saenext.app-list.micro-app.NamespaceName'),
              span: 24,
            },
            {
              dataIndex: 'NamespaceId',
              label: intl('saenext.app-list.micro-app.NamespaceId'),
              span: 24,
            },
            {
              dataIndex: 'VpcId',
              label: intl('saenext.components.job-list.Vpc'),
              span: 24,
            },
          ]}
        />
      </Balloon.Tooltip>
    );
  };

  const handleNavToCreateApp = (record?) => {
    const { AppId } = record || {};

    if (AppId) {
      history.push(`/${currentRegionId}/create-job?appId=${AppId}`);
    } else {
      history.push(`/${currentRegionId}/create-job`);
    }
  };

  const suspendJobConfirm = (record, isSuspend) => {
    const style = {
      width: 600,
      fontSize: 12,
      lineHeight: '24px',
      marginTop: 8,
    };

    const content = (
      <div style={style}>
        {isSuspend
          ? intl('saenext.components.job-list.AfterTheTaskIsStarted')
          : intl('saenext.components.job-list.AfterStoppingTheSystemWill')}
      </div>
    );

    Dialog.confirm({
      title: isSuspend
        ? intl('saenext.components.job-list.EnableTasks')
        : intl('saenext.components.job-list.StopATask'),
      content,
      onOk: () => {
        return new Promise((resolve, reject) => {
          handleSuspendJob(record, !isSuspend, resolve, reject);
        });
      },
    });
  };

  const handleSuspendJob = async (record, isSuspend, resolve, reject) => {
    const data = await services.SuspendJob({
      params: {
        AppId: record.AppId,
        Suspend: isSuspend,
      },
    });

    const { Data: { success = false } = {} } = data || {};
    if (!success) {
      Message.error(intl('saenext.components.job-list.OperationFailed'));
      reject();
      return;
    }

    if (isSuspend) {
      Message.success(intl('saenext.components.job-list.Stopped'));
    } else {
      Message.success(intl('saenext.components.job-list.Enabled'));
    }
    resolve();
    setRefreshIndex(+new Date());
  };

  const deleteJobConfirm = (record) => {
    const style = {
      width: 600,
      fontSize: 12,
      lineHeight: '24px',
      marginTop: 8,
    };

    const content = (
      <div style={style}>{intl('saenext.components.job-list.AfterYouDeleteATask')}</div>
    );

    Dialog.alert({
      title: intl('saenext.components.job-list.DeleteATaskTemplate'),
      content,
      onOk: () => {
        return new Promise((resolve, reject) => {
          handleDeleteJob(record, resolve, reject);
        });
      },
    });
  };

  const handleDeleteJob = async (record, resolve, reject) => {
    try {
      const data = await services.DeleteJob({
        params: {
          AppId: record.AppId,
        },
        options: {
          region: currentRegionId,
        },
      });

      const { Data: { success = false } = {} } = data || {};
      if (!success) {
        Message.error(intl('saenext.components.job-list.OperationFailed'));
        reject();
      } else {
        Message.success(intl('saenext.components.job-list.DeletedSuccessfully'));
        resolve();
        setRefreshIndex(+new Date());
      }
    } catch (error) {
      if (error.code === 'FetcherErrorRiskCancelled') {
        // 风控弹窗点取消
        reject();
      } else {
        // 通用控制台弹窗
        errorPrompt(error);
        reject();
      }
    }
  };

  const handleBatchUpdateImage = async () => {
    setUpdateImageLoading(true);
    const {
      type,
      AcrInstanceId = '',
      AcrAssumeRoleArn = '',
      ImagePullSecrets = '',
      EnableImageAccl = false,
      ImageUrl = '',
    } = curImageInfo as any;
    const appIds = selectedRowKeys.map((item) => item.split('&&')[0]);
    await services.BatchUpdateJobs({
      params: {
        AppIds: appIds.join(','),
        AcrInstanceId,
        AcrAssumeRoleArn,
        ImagePullSecrets,
        EnableImageAccl,
        ImageUrl,
      },
    });
    setUpdateImageLoading(false);
    setIsShowing(false);
    setCurImageInfo({
      ImageUrl: '',
    });
    setSelectedRowKeys([]);
    setSelectedApps([]);
    setRefreshIndex(Date.now());
  };

  const columns = [
    {
      key: 'AppName',
      title: intl('saenext.app-list.micro-app.ApplicationName'),
      dataIndex: 'AppName',
      sortable: true,
      sortDirections: ['asc', 'desc', 'default'],
      width: 180,
      cell: (value, index, record) => {
        const { AppId, RegionId, NamespaceId = '', AppName } = record;
        const jobDetailLink = `/${RegionId}/job-list/${AppId}/base`;
        return (
          <Link to={jobDetailLink}>
            <Copy text={value}>
              <span className="text-left break-all">{value}</span>
            </Copy>
          </Link>
        );
      },
      // lock: 'left',  //锁列影响排序，先注释
    },
    {
      key: 'NamespaceId',
      title: intl('saenext.app-list.micro-app.Namespace'),
      dataIndex: 'NamespaceId',
      width: 160,
      cell: renderNamespace,
    },
    {
      key: 'TriggerConfig',
      title: intl('saenext.components.job-list.ExecutionPolicy'),
      dataIndex: 'TriggerConfig',
      width: 160,
      cell: (value, index, record) => {
        const triggerConfig = jsonParse(value) || {};
        return triggerConfig.type === 'time' ? (
          <div>
            {triggerConfig.config}
            <CronLaterBalloon trigger={triggerConfig.config} />
          </div>
        ) : (
          intl('saenext.components.job-list.OneTimeTask')
        );
      },
    },
    {
      key: 'Suspend',
      title: intl('saenext.components.job-list.TaskTemplateStatus'),
      dataIndex: 'Suspend',
      width: 160,
      cell: (value, index, record) => (
        <StatusIndicator type={value ? 'disabled' : 'success'} shape="dot">
          {value
            ? intl('saenext.components.job-list.Stopped')
            : intl('saenext.components.job-list.Started')}
        </StatusIndicator>
      ),
    },
    {
      key: 'LastJobState',
      title: intl('saenext.components.job-list.TaskExecutionStatus'),
      dataIndex: 'LastJobState',
      width: 160,
      cell: (value, index, record) => {
        const { AppId, RegionId, NamespaceId, AppName, Message, LastChangeorderState } = record;
        const stateKey = LastChangeorderState === 0 ? 'template-0' : value;
        const { label, state, intro, link } = C.STATES[stateKey] || {};

        return (
          <>
            {C.STATES[stateKey] ? (
              <>
                <StatusIndicator type={state} shape="dot">
                  {label}
                </StatusIndicator>
                {intro && (
                  <Balloon
                    align="tr"
                    trigger={<Icon size="xs" type="help" className={`pointer`} />}
                    closable={false}
                  >
                    {Message || (
                      <ExternalLink
                        label={intro}
                        // TODO
                        url={`${link}?appId=${AppId}&regionId=${RegionId}&namespaceId=${
                          NamespaceId || getParams('namespaceId')
                        }&appName=${AppName}`}
                      />
                    )}
                  </Balloon>
                )}
              </>
            ) : (
              '--'
            )}
          </>
        );
      },
    },
    {
      key: 'Active',
      title: intl('saenext.components.job-list.NumberOfRunningSuccessfulFailed'),
      dataIndex: 'Active',
      width: 180,
      cell: (value, index, record) => {
        const { Active, Succeeded, Failed } = record;
        if (typeof Active !== 'undefined') {
          return (
            <>
              {Active} / {Succeeded} / {Failed}
            </>
          );
        } else {
          return '-';
        }
      },
    },
    {
      key: 'Spec',
      title: intl('saenext.components.job-list.InstanceType'),
      dataIndex: 'Cpu',
      width: 160,
      cell: (value, index, record) => {
        const { Cpu, Mem } = record;
        const cpuNum = Cpu / 1000;
        const memNum = Mem / 1024;
        return intl('saenext.components.job-list.CpunumCoreMemnumGb', {
          cpuNum: cpuNum,
          memNum: memNum,
        });
      },
    },
    {
      key: 'AppDescription',
      title: intl('saenext.components.job-list.TaskTemplateDescription'),
      dataIndex: 'AppDescription',
      width: 180,
      cell: (value, index, record) => {
        if (!value) {
          return '--';
        }
        return (
          <Truncate
            type="width"
            threshold={120}
            align="t"
            style={{ width: '100%', wordBreak: 'break-all' }}
          >
            <span style={{ width: '100%', wordBreak: 'break-all' }}>{value}</span>
          </Truncate>
        );
      },
    },
    {
      key: 'opt',
      title: intl('saenext.app-list.micro-app.Operation'),
      dataIndex: 'opt',
      width: 160,
      cell: (value, index, record) => {
        const isSuspend = record.Suspend ? true : false;
        return (
          // @ts-ignore
          <Actions>
            <LinkButton
              disabled={isInDebt}
              onClick={() => {
                handleNavToCreateApp(record);
              }}
            >
              {intl('saenext.app-list.micro-app.Copy')}
            </LinkButton>
            <LinkButton
              disabled={isSuspend && isInDebt}
              onClick={() => suspendJobConfirm(record, isSuspend)}
            >
              {isSuspend
                ? intl('saenext.components.job-list.Enable')
                : intl('saenext.components.job-list.Stop')}
            </LinkButton>
            <LinkButton key={index} onClick={() => deleteJobConfirm(record)}>
              {intl('saenext.components.job-list.Delete')}
            </LinkButton>
          </Actions>
        );
      },
      lock: 'right',
    },
  ];

  const search = {
    defaultDataIndex: 'appName',
    defaultSelectedDataIndex: 'appName',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.components.job-list.TaskTemplateName'),
        dataIndex: C.FILTERS.APP_NAME,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.components.job-list.EnterATaskTemplateName'),
        },
        defaultValue: getParams(C.FILTERS.APP_NAME),
      },
      {
        label: intl('saenext.components.job-list.TaskTemplateId'),
        dataIndex: C.FILTERS.APP_ID,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.components.job-list.EnterTheTaskTemplateId'),
        },
        defaultValue: getParams(C.FILTERS.APP_ID),
      },
    ],
  };

  // 无权限或者欠费
  const disabled = !auth || isInDebt;

  const operation = (
    <Button
      name="createservlessapp"
      type="primary"
      disabled={disabled || NamespaceId === 'none'}
      onClick={() => handleNavToCreateApp()}
    >
      {intl('saenext.components.job-list.CreateATaskTemplate')}
    </Button>
  );

  const emptyContent = () => {
    if (auth) {
      return <RegionGuidance currentRegion={currentRegionId} />;
    }
    return (
      <p>
        {intl('saenext.app-list.micro-app.YourAccountDoesNotHave')}

        <a href={CachedData.confLink('help:sae:sae-permission-assistant')} target="_blank">
          {intl('saenext.app-list.micro-app.ApplyForPermission')}
        </a>
        {intl('saenext.app-list.micro-app.ThenViewAndOperate')}
      </p>
    );
  };

  const tipRender = () => {
    if (!disabled) return null;
    return (
      <Message
        type="error"
        className="mt-s"
        title={intl('saenext.app-list.micro-app.AccountExceptionReminder')}
      >
        <div className="text-line mt-s">
          {intl('saenext.app-list.micro-app.ThisMayBeBecauseYou')}
        </div>
      </Message>
    );
  };

  return (
    <>
      {tipRender()}
      <CndTable
        fetchData={fetchData}
        refreshIndex={refreshIndex}
        columns={columns as []}
        showRefreshButton
        recordCurrent
        pagination={{ pageSizeList: [10, 20, 50, 100] }}
        search={search as ISearch}
        operation={operation}
        emptyContent={emptyContent()}
        primaryKey="primaryKey"
        rowSelection={{
          getProps: (record, index) => {
            if (_.isEmpty(NamespaceId) || !_.get(record, 'ImageUrl', '')) {
              return { disabled: true };
            }
          },
          selectedRowKeys,
          onChange(selected, records) {
            setSelectedRowKeys(selected);
          },
        }}
        selection={({ selectedRowKeys }: { selectedRowKeys: any[] }) => {
          return (
            <Badge count={selectedRowKeys.length}>
              <Button
                disabled={_.isEmpty(NamespaceId) || selectedRowKeys.length === 0}
                onClick={() => {
                  const imageUrls = selectedRowKeys.map((item) => item.split('&&')[1]);
                  if (new Set(imageUrls).size !== 1) {
                    return Message.error(
                      intl('saenext.components.job-list.SelectAnApplicationWithThe'),
                    );
                  }
                  setCurImageInfo({
                    ImageUrl: imageUrls[0],
                  });
                  setIsShowing(true);
                }}
              >
                {intl('saenext.components.job-list.BatchUpdateImages')}
              </Button>
            </Badge>
          );
        }}
      />

      <SlidePanel
        title={intl('saenext.components.job-list.BatchUpdateImages')}
        isShowing={isShowing}
        onOk={handleBatchUpdateImage}
        onCancel={() => {
          setCurImageInfo({
            ImageUrl: '',
          });
          setIsShowing(false);
        }}
        okProps={{
          disabled: !curImageInfo?.ImageUrl,
          loading: updateImageLoading,
        }}
      >
        <SelectedApp
          mode="selectable"
          value={selectedApps}
          onChange={setSelectedApps}
          dataSource={selectedRowKeys}
        />

        <Form field={field}>
          <Form.Item label={intl('saenext.components.job-list.ApplicationImage')}>
            <Input
              trim
              readOnly
              value={curImageInfo?.ImageUrl}
              style={{ width: '90%' }}
              innerAfter={
                <UpdateImageSlide
                  vpcId={vpcId}
                  namespaceId={NamespaceId}
                  imageInfo={curImageInfo}
                  setImageInfo={setCurImageInfo}
                >
                  <Button type="primary" text className="mr-xs">
                    {intl('saenext.components.job-list.ModifyAnImage')}
                  </Button>
                </UpdateImageSlide>
              }
            />
          </Form.Item>
        </Form>
      </SlidePanel>
    </>
  );
};

export default JobList;
