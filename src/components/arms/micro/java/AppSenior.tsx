import { intl } from '@ali/cnd';
import React, { useEffect, useState } from 'react';
import { Message, Checkbox, Button, Icon, Dialog } from '@ali/cnd';
import { get, noop } from 'lodash';
import services from "~/services";
import CachedData from '../../../../cache/common';

const { Group: CheckboxGroup } = Checkbox;
type Props = {
  appId: string;
  regionId: string;
  appStatus: {
    [key: string]: any;
  };
  refresh: () => void;
};

const dataSource = [
  intl('saenext.micro.java.AppSenior.IncludingSelfDiscoveryOfApplication'),
  intl('saenext.micro.java.AppSenior.VariousFaultDiagnosisEnhancementsSuch'),
  intl('saenext.micro.java.AppSenior.ViewBasedOnDistributedCall'),
  intl('saenext.micro.java.AppSenior.CallChainAdHocSearch'),
];

const AppSenior = (props: Props) => {
  const { regionId, appId, appStatus, refresh = noop } = props;
  const _armsAdvancedEnabled = get(appStatus, 'ArmsAdvancedEnabled', false);
  const [checkedList, setCheckedList] = useState(dataSource);
  const [armsAdvancedEnabled, setArmsAdvancedEnabled] = useState(false);

  useEffect(() => {
    setArmsAdvancedEnabled(_armsAdvancedEnabled);
  }, [_armsAdvancedEnabled]);

  const handleAdvancedMonitor = async () => {
    const title = armsAdvancedEnabled
      ? intl('saenext.micro.java.AppSenior.DisableArmsAdvancedMonitoring')
      : intl('saenext.micro.java.AppSenior.EnableArmsAdvancedMonitoring');
    const content = armsAdvancedEnabled
      ? intl('saenext.micro.java.AppSenior.AfterArmsAdvancedMonitoringIs')
      : intl('saenext.micro.java.AppSenior.AfterArmsAdvancedMonitoringIs.1');
    const params = { RegionId: regionId, AppId: appId };
    Dialog.alert({
      title: title,
      content: <p style={{ width: 500 }}>{content}</p>,
      okProps: { children: intl('saenext.micro.java.AppSenior.Confirm') },
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          let res = {};
          if (armsAdvancedEnabled) {
            res = await services.closeAdvancedMonitor({
              params,
              customErrorHandle: (error, data, callback) => {
                reject();
                callback && callback();
              },
            });
          } else {
            res = await services.openAdvancedMonitor({
              params,
              customErrorHandle: (error, data, callback) => {
                reject();
                callback && callback();
              },
            });
          }
          const status = get(res, 'Data.Status', false);
          if (status) {
            resolve(true);
            refresh && refresh();
            setArmsAdvancedEnabled(!armsAdvancedEnabled);
            Message.success(intl('saenext.micro.java.AppSenior.TitleSucceeded', { title: title }));
          } else {
            reject();
            Message.error(intl('saenext.micro.java.AppSenior.TitleFailed', { title: title }));
          }
        });
      },
    });
  };

  return (
    <>
      <Message type="notice" style={{ marginBottom: 16 }}>
        {intl('saenext.micro.java.AppSenior.ArmsApplicationMonitoring')}

        <span style={{ color: 'red' }}>2019/3/25</span>
        {intl('saenext.micro.java.AppSenior.StartChargingForAccessTo')}
        <span style={{ color: 'green' }}>{intl('saenext.micro.java.AppSenior.Off')}</span>
        {intl('saenext.micro.java.AppSenior.ForMoreInformationSee')}
        <a href={CachedData.confLink('help:arms:billing-overview')} target="_blank">
          {intl('saenext.micro.java.AppSenior.PreferentialRules')}
        </a>
      </Message>

      <div style={{ fontSize: 12, fontWeight: 'bold', marginBottom: 8 }}>
        {intl('saenext.micro.java.AppSenior.ArmsApplicationMonitoringIsA')}
      </div>
      <CheckboxGroup itemDirection="ver" dataSource={dataSource} value={checkedList} />

      <div style={{ display: 'flex', alignItems: 'center', marginTop: 16 }}>
        <Button type="primary" onClick={handleAdvancedMonitor}>
          {armsAdvancedEnabled
            ? intl('saenext.micro.java.AppSenior.DisableArmsAdvancedMonitoring')
            : intl('saenext.micro.java.AppSenior.EnableArmsAdvancedMonitoring')}
        </Button>
        {armsAdvancedEnabled ? (
          <div style={{ display: 'flex', alignItems: 'center', marginLeft: 16 }}>
            <Icon type="success" size="xs" />
            <span style={{ marginLeft: 4 }}>{intl('saenext.micro.java.AppSenior.Enabled')}</span>
          </div>
        ) : (
          <div style={{ display: 'flex', alignItems: 'center', marginLeft: 16 }}>
            <Icon type="error" size="xs" />
            <span style={{ marginLeft: 4 }}>{intl('saenext.micro.java.AppSenior.Closed')}</span>
          </div>
        )}
        <Button
          text
          type="primary"
          style={{ marginLeft: 8 }}
          onClick={() =>
            window.open(
              `${CachedData.confLink('feature:arms:url')}/#/tracing/list?regionNo=cn-hangzhou`,
              '_blank',
            )
          }
        >
          {intl('saenext.micro.java.AppSenior.JumpToArmsAdvancedMonitoring')}

          <Icon type="external_link" />
        </Button>
      </div>

      <div style={{ display: 'flex', paddingTop: 16, marginTop: 24, borderTop: '1px solid #ccc' }}>
        <div style={{ marginRight: 16 }}>
          <div style={{ marginBottom: 8 }}>
            {intl('saenext.micro.java.AppSenior.ApplyGlobalTopology')}
          </div>
          <img
            style={{ width: 480, height: 240, border: '1px solid #2cdaed' }}
            src="https://img.alicdn.com/tfs/TB1iFoecj39YK4jSZPcXXXrUFXa-800-409.png"
          />
        </div>
        <div style={{ marginLeft: 16 }}>
          <div style={{ marginBottom: 8 }}>
            {intl('saenext.micro.java.AppSenior.AbnormalSqlAnalysis')}
          </div>
          <img
            style={{ width: 480, height: 240, border: '1px solid #2cdaed' }}
            src="https://img.alicdn.com/tfs/TB1Oy_XXmR26e4jSZFEXXbwuXXa-2414-1368.jpg"
          />
        </div>
      </div>
      <div style={{ display: 'flex' }}>
        <div style={{ marginRight: 16 }}>
          <div style={{ marginBottom: 8 }}>
            {intl('saenext.micro.java.AppSenior.LocalCallStack')}
          </div>
          <img
            style={{ width: 480, height: 240, border: '1px solid #2cdaed' }}
            src="https://img.alicdn.com/tfs/TB1SzzOKQL0gK0jSZFtXXXQCXXa-2162-1166.jpg"
          />
        </div>
        <div style={{ marginLeft: 16 }}>
          <div style={{ marginBottom: 8 }}>
            {intl('saenext.micro.java.AppSenior.CallChainQuery')}
          </div>
          <img
            style={{ width: 480, height: 240, border: '1px solid #2cdaed' }}
            src="https://img.alicdn.com/tfs/TB13jzZKUT1gK0jSZFrXXcNCXXa-2808-1340.jpg"
          />
        </div>
      </div>
    </>
  );
};

export default AppSenior;
