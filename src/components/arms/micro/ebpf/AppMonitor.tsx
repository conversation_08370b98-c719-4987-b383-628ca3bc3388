import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import services from "~/services";
import { get } from 'lodash';
import { Loading, Message } from '@ali/cnd';
import ArmsIframe from '../../../shared/ArmsIframe';
import CachedData from '../../../../cache/common';

type Props = {
  appId: string;
  appName: string;
  regionId: string;
};

const imageStyle = {
  width: '100%',
  height: '100%',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'top',
  backgroundSize: 'cover',
  objectFit: 'cover',
};

const AppMonitor = (props: Props) => {
  const { regionId, appId, appName } = props;
  const [loading, setLoading] = useState(false);
  const [enableEbpf, setEnableEbpf] = useState(false);
  const [iframeUrl, setIframeUrl] = useState('');

  useEffect(() => {
    getEbpfStatus();
  }, []);

  const getEbpfStatus = async () => {
    setLoading(true);
    const res = await services.getArmsEnable({
      params: {
        RegionId: regionId,
        AppId: appId,
      },
      customErrorHandle: (error, data, callback) => {
        setLoading(false);
        callback && callback();
      },
    });
    const _enableEbpf = get(res, 'Data.Enable', false);
    setLoading(_enableEbpf);
    setEnableEbpf(_enableEbpf);
    if (_enableEbpf) {
      getIframeUrl();
    }
  };

  const getIframeUrl = async () => {
    const res = await services.getServiceConsole({
      params: {
        RegionId: regionId,
        SourceType: 'sae',
        ProductType: 'ebpf',
        QueryData: JSON.stringify({ appId, appName }),
      },
      customErrorHandle: (error, data, callback) => {
        setLoading(false);
        callback && callback();
      },
    });
    const data = get(res, 'Data', '{ "url": ""}');
    const _iframeUrl = JSON.parse(data).url;
    setIframeUrl(_iframeUrl);
    setLoading(false);
  };

  return (
    <Loading visible={loading} style={{ height: '100%', width: '100%' }}>
      {enableEbpf ? null : (
        <>
          <Message type="notice">
            {intl('saenext.micro.ebpf.AppMonitor.ApplicationMonitoringIsNotEnabled')}
          </Message>
          <div style={{ height: 370, position: 'relative' }}>
            <img
              // @ts-ignore
              style={imageStyle}
              src="https://img.alicdn.com/imgextra/i3/O1CN01JJ97sS1OjQDuV6GRo_!!6000000001741-1-tps-3840-740.gif"
            />
          </div>
        </>
      )}
      {iframeUrl ? (
        <>
          <Message type="notice" style={{ marginBottom: 8 }}>
            {intl('saenext.micro.ebpf.AppMonitor.BasedOnTheIndustryLeading')}
            <a href={CachedData.confLink('help:sae:configure-a-configuration-file-for-a-php-application')} target="_blank">
              {intl('saenext.micro.ebpf.AppMonitor.ViewDetails')}
            </a>
          </Message>
          <ArmsIframe regionId={regionId} applicationId={appId} url={iframeUrl} />
        </>
      ) : null}
    </Loading>
  );
};

export default AppMonitor;
