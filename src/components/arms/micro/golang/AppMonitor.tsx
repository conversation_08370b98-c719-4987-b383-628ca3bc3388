import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Message, Timeline, Icon, LinkButton } from '@ali/cnd';
import services from "~/services";
import { get, includes, noop } from 'lodash';
import { LANGUAGE_NAME, LANGUAGE_TYPE } from '../../../../constants/application';
import CachedData from '../../../../cache/common';

type Props = {
  redirectDeploy: (callback: any) => void;
  lang: string;
  packageType: string;
};
const { Item: TimelineItem } = Timeline;
const documentStyle = {
  width: 520,
  lineHeight: 1.5,
  marginBottom: 12,
};
const imageStyle = {
  width: '100%',
  height: '100%',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'top',
  backgroundSize: 'cover',
  objectFit: 'cover',
};

const LangLink = {
  [LANGUAGE_TYPE.GO]: CachedData.confLink('help:sae:installing-probes-for-golang-applications'),
  [LANGUAGE_TYPE.PYTHON]: CachedData.confLink('help:sae:install-the-arms-agent-for-a-python-application'),
};

const ArmsGoMonitor = (props: Props) => {
  const { redirectDeploy = noop, lang, packageType } = props;
  const [loading, setLoading] = useState(false);
  const [isArmsOpened, setIsArmsOpened] = useState(false);
  const [refreshIndex, setRefreshIndex] = useState(0);

  const isImage = packageType === 'Image' || includes(packageType, 'IMAGE_PHP');

  const langName = LANGUAGE_NAME[lang];

  useEffect(() => {
    getProductOpenStatus();
  }, [refreshIndex]);

  const getProductOpenStatus = async () => {
    setLoading(true);
    const res = await services.DescribeUserBusinessStatus({
      ServiceCode: 'arms',
    });
    const status = get(res, 'Statuses.Status', []);
    const enabled = !!status.find((s) => s.StatusKey === 'enabled' && s.StatusValue !== 'false');
    setIsArmsOpened(enabled);
    setLoading(false);
  };

  return (
    <>
      <Message type="notice">
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.micro.golang.AppMonitor.ServerlessTheApplicationEngineAutomatically')}
        </div>
        <div style={{ lineHeight: 1.5 }}>
          {intl('saenext.micro.golang.AppMonitor.CurrentlyApplicationMonitoringIsNot')}
        </div>
      </Message>
      <div style={{ height: 370, position: 'relative' }}>
        <img
          // @ts-ignore
          style={imageStyle}
          src="https://img.alicdn.com/imgextra/i3/O1CN01JJ97sS1OjQDuV6GRo_!!6000000001741-1-tps-3840-740.gif"
        />

        <div
          style={{ position: 'absolute', top: 32, left: 0, background: 'transparent', zIndex: 1 }}
        >
          <div style={{ marginBottom: 16, fontWeight: 'bold' }}>
            {intl('saenext.micro.golang.AppMonitor.EnableApplicationMonitoring')}
          </div>
          <Timeline className="swimlane-timeline">
            <TimelineItem
              key="open"
              title={intl('saenext.micro.golang.AppMonitor.ActivateArmsProducts')}
              state="process"
              content={
                <div style={documentStyle}>
                  {isArmsOpened ? (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Icon type="success" size="xs" />
                      <span style={{ marginLeft: 8 }}>
                        {intl('saenext.micro.golang.AppMonitor.CurrentlyActivatedPleaseContinueTo')}
                      </span>
                    </div>
                  ) : (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Icon type="warning" size="xs" />
                      <span style={{ marginLeft: 8 }}>
                        {intl('saenext.micro.golang.AppMonitor.CurrentlyNotActivatedPleaseGo')}
                        <a href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=arms`} target="_blank">
                          {intl(
                            'saenext.micro.golang.AppMonitor.ArmsApplicationMonitoringRealTime',
                          )}
                        </a>
                        {intl('saenext.micro.golang.AppMonitor.Activate')}
                      </span>
                      <LinkButton
                        style={{ marginLeft: 8 }}
                        onClick={() => setRefreshIndex(Date.now())}
                      >
                        {loading ? (
                          <Icon type="loading" size="small" />
                        ) : (
                          <Icon type="refresh" size="small" />
                        )}
                      </LinkButton>
                    </div>
                  )}
                </div>
              }
            />

            {isImage ? (
              <TimelineItem
                key="image"
                title={intl('saenext.micro.golang.AppMonitor.CustomImage')}
                state="process"
                content={
                  <>
                    <div style={documentStyle}>
                      {intl('saenext.micro.golang.AppMonitor.ModifyTheCustomImageAnd', {
                        lang: langName,
                      })}
                      <a href={LangLink[lang]} target="_blank">
                        {intl('saenext.micro.golang.AppMonitor.ViewOperationDocuments')}
                      </a>
                    </div>
                  </>
                }
              />
            ) : (
              <TimelineItem
                key="package"
                title={intl('saenext.micro.golang.AppMonitor.ModifyStartupCommands')}
                state="process"
                content={
                  <>
                    <div style={documentStyle}>
                      {intl('saenext.micro.golang.AppMonitor.WeWillInstallTheLangname', {
                        langName: langName,
                      })}
                      <a href={LangLink[lang]} target="_blank">
                        {intl('saenext.micro.golang.AppMonitor.ViewOperationDocuments')}
                      </a>
                    </div>
                  </>
                }
              />
            )}

            <TimelineItem
              key="enable"
              title={intl('saenext.micro.golang.AppMonitor.EnableApplicationMonitoring.1')}
              state="process"
              content={
                <>
                  <div style={documentStyle}>
                    {intl('saenext.micro.golang.AppMonitor.ChangeTheImageAndTurn')}

                    <LinkButton
                      style={{ margin: '0 2px' }}
                      onClick={() => {
                        redirectDeploy && redirectDeploy();
                      }}
                    >
                      {intl('saenext.micro.golang.AppMonitor.DeployApplications')}
                    </LinkButton>
                    {intl('saenext.micro.golang.AppMonitor.ReplaceTheOriginalImageWith')}
                  </div>
                </>
              }
            />

            <TimelineItem
              key="ok"
              title={intl('saenext.micro.golang.AppMonitor.ConfirmDeployment')}
              state="process"
              content={
                <>
                  <div style={documentStyle}>
                    {intl('saenext.micro.golang.AppMonitor.ClickOkToEnableApplication')}
                  </div>
                </>
              }
            />
          </Timeline>
        </div>
      </div>
    </>
  );
};

export default ArmsGoMonitor;
