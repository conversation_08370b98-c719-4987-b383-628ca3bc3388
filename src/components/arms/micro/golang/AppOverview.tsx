import React from 'react';
import ArmsIframe from '../../../shared/ArmsIframe';
import CachedData from '../../../../cache/common';

type Props = {
  appId: string;
  regionId: string;
};


const AppOverview = (props: Props) => {
  const { regionId, appId } = props;

  return (
    <ArmsIframe
      regionId={regionId}
      applicationId={appId}
      url={`${CachedData.confLink('feature:armsnext:url')}/tracing#/tracing/${regionId}?appId=${appId}&tab=appOverview&source=TRACE&from=now-30m&to=now&refresh=off&language=golang`}
    />
  );
};

export default AppOverview;
