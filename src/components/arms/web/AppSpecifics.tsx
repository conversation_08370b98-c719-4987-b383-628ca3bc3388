import React from 'react';
import ArmsIframe from '../../shared/ArmsIframe';

type Props = {
  regionId: string;
  applicationID: string;
};


const AppSpecifics = (props: Props) => {
  const { 
    regionId, 
    applicationID: applicationId
  } = props;

  return (
    <ArmsIframe
      regionId={regionId} 
      applicationId={applicationId}
      pageName="apps"
    />
  );
};

export default AppSpecifics;
