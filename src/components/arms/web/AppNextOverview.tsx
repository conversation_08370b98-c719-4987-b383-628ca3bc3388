import React from 'react';
import { intl } from '@ali/cnd';
import { Button, Dialog, Message } from '@ali/cnd';
import ArmsIframe from '../../shared/ArmsIframe';
import services from "~/services";
import { isEmpty, noop } from 'lodash';
import CachedData from '../../../cache/common';

type Props = {
  regionId: string;
  applicationID: string;
  applicationName: string;
  programmingLanguage: string;
  redirectArms: (callback: any) => void;
};

const AppNextOverview = (props: Props) => {
  const {
    regionId,
    applicationID: applicationId,
    applicationName,
    programmingLanguage = 'java',
    redirectArms = noop,
  } = props;

  const disabledArms = () => {
    Dialog.alert({
      title: intl('saenext.components.arms.Stop'),
      content: <p style={{ width: 500 }}>{intl('saenext.components.arms.StopConfirm')}</p>,

      okProps: { children: intl('saenext.arms.web.AppNextOverview.Confirm') },
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res = await services.updateWebApplication({
            applicationName,
            applicationID: applicationId,
            content: {
              applicationID: applicationId,
              applicationName,
              enableAppMetric: false,
              effectiveImmediately: true,
            },
            customErrorHandle: (error, data, callback) => {
              reject();
              callback && callback();
            },
          });
          const { version: latestVersion } = res;
          if (!isEmpty(latestVersion)) {
            redirectArms && redirectArms();
            resolve(true);
            Message.success(intl('saenext.components.arms.StopSuccessfully'));
          } else {
            reject();
            Message.error(intl('saenext.components.arms.StopFailed'));
          }
        });
      },
    });
  };

  return (
    <>
      <div style={{ margin: '-50px 0 16px', textAlign: 'right' }}>
        <Button type="primary" onClick={disabledArms}>
          {intl('saenext.components.arms.Stop')}
        </Button>
      </div>
      <ArmsIframe
        regionId={regionId}
        applicationId={applicationId}
        url={`${CachedData.confLink('feature:armsnext:url')}/tracing#/tracing/${regionId}?appId=${applicationId}&tab=appOverview&source=TRACE&from=now-30m&to=now&refresh=off&language=${programmingLanguage}`}
      />
    </>
  );
};

export default AppNextOverview;
