import React, { useState, useRef, useEffect } from 'react';
import { Message, Button, Icon, Form, Radio } from '@ali/cnd';
import _ from 'lodash';
import services from "~/services";
import { getTimes } from '../../../utils/format';
import { intl } from '@ali/cnd';
import CachedData from '../../../cache/common';

type Props = {
  applicationID: string;
  applicationName: string;
  minInstanceCount: number;
  redirectArms: (callback: any) => void;
};

type InitState = {
  isFinal?: boolean;
  isFailed?: boolean;
  isSuccess?: boolean;
};

const RadioGroup = Radio.Group;

const ArmsMonitor = (props: Props) => {
  const { applicationID, applicationName, minInstanceCount, redirectArms } = props;
  const timer = useRef(null);
  // 轮询次数
  const loopCount = useRef(60);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRefresh, setIsRefresh] = useState<boolean>(false);

  const [internalState, setInternalState] = useState<InitState>({
    isFinal: false,
    isFailed: false,
    isSuccess: false,
  });

  // 是否已经开启
  const [isArmsOpened, setIsArmsOpened] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [language, setLanguage] = useState('java');

  useEffect(() => {
    return () => timer.current && clearInterval(timer.current);
  }, []);

  const enableArms = async () => {
    setIsLoading(true);
    try {
      const data = await services.updateWebApplication({
        applicationName,
        applicationID,
        content: {
          applicationID,
          applicationName,
          enableAppMetric: true,
          programmingLanguage: language,
          effectiveImmediately: true,
        },
        customErrorHandle: (error, data) => {
          openArmsFailed(error.message);
          return error;
        },
      });
      const { enableAppMetric } = data;
      if (enableAppMetric) {
        const { version: latestVersion } = data;
        const { versionId } = latestVersion;
        timer.current = setInterval(() => {
          getInstances(versionId, () => {
            setIsLoading(false);
          });
        }, 1000 * 5);
      }
    } catch (error) {
      openArmsFailed(error.message);
    }
  };

  const openArmsFailed = (message) => {
    setInternalState({
      isFinal: true,
      isFailed: true,
      isSuccess: false,
    });
    setIsLoading(false);
    setErrorMessage(message);
  };

  const getInstances = async (versionId, callback) => {
    loopCount.current = loopCount.current - 1;
    const [start, end] = getTimes('last_15_minutes');
    const data = await services.getAppVersionInstances({
      applicationID,
      applicationName,
      startTime: start,
      endTime: end,
      qualifier: versionId,
    });
    const { instances = [] } = data;
    if (instances.length >= minInstanceCount) {
      // 最新版本实例数 大于等于 最小实例数
      callback && callback();
      setInternalState({
        isFinal: true,
        isFailed: false,
        isSuccess: true,
      });
      clearInterval(timer.current);
      return;
    }
    if (!loopCount.current) {
      // 轮询结束
      callback && callback();
      openArmsFailed(intl('saenext.components.arms.TheApplicationInstanceStartupTimed'));
      clearInterval(timer.current);
    }
  };

  const renderContent = () => {
    const { isFinal, isSuccess, isFailed } = internalState;
    if (isFinal && isSuccess) {
      return (
        <>
          <Icon type="success">
            <span style={{ color: '#333', fontSize: 14, fontWeight: 500, marginLeft: 8 }}>
              {intl('saenext.components.arms.ApplicationMonitoringEnabledSuccessfully')}
            </span>
          </Icon>
          <div style={{ display: 'flex', alignItems: 'center', marginTop: 8, marginLeft: 28 }}>
            {intl('saenext.components.arms.ApplicationMonitoringIsEnabledAnd')}

            <Button
              text
              type="primary"
              loading={isRefresh}
              style={{ marginLeft: 4, marginRight: 4 }}
              onClick={() => {
                // 请求接口 刷新isArmsOpened的值
                setIsLoading(false);
                setIsRefresh(true);
                setIsArmsOpened(false);
                redirectArms && redirectArms(() => setIsRefresh(false));
              }}
            >
              {intl('saenext.components.arms.Refresh')}
            </Button>
            {intl('saenext.components.arms.ViewTheLatestMonitoringData')}
          </div>
        </>
      );
    }

    if (isFinal && isFailed) {
      return (
        <>
          <Icon type="error">
            <span style={{ color: '#333', fontSize: 14, fontWeight: 500, marginLeft: 8 }}>
              {intl('saenext.components.arms.FailedToEnableApplicationMonitoring')}
            </span>
          </Icon>
          <div style={{ display: 'flex', alignItems: 'center', marginTop: 8, marginLeft: 28 }}>
            Error Message:{errorMessage || '-'}
            <Button
              text
              type="primary"
              style={{ marginLeft: 4, marginRight: 4 }}
              onClick={() => {
                setInternalState({
                  isFinal: false,
                  isFailed: false,
                  isSuccess: false,
                });
                setIsLoading(false);
              }}
            >
              {intl('saenext.components.arms.TryToEnableItAgain')}
            </Button>
          </div>
        </>
      );
    }
    return (
      <>
        <Form labelAlign="top">
          <Form.Item
            label={intl('saenext.arms.web.LanguageType')}
            required
            help={
              language === 'golang' ? (
                <>
                  <span>{intl('saenext.arms.web.SelectGoLanguageApplicationMonitoring')}</span>
                  <a
                    className="ml-s"
                    href={CachedData.confLink('help:sae:installing-probes-for-golang-applications')}
                    target="_blank"
                  >
                    {intl('saenext.arms.web.OperationDocument')}
                  </a>
                </>
              ) : null
            }
          >
            <RadioGroup value={language} onChange={(val) => setLanguage(val as string)}>
              <Radio key="java" value="java">
                Java
              </Radio>
              <Radio key="golang" value="golang">
                Go
              </Radio>
            </RadioGroup>
          </Form.Item>
        </Form>
        <div className="flex">
          <Button
            loading={isLoading}
            onClick={enableArms}
            disabled={minInstanceCount === 0}
            type={isLoading ? 'normal' : 'primary'}
          >
            {isLoading
              ? intl('saenext.components.arms.ApplicationMonitoringIsOn')
              : intl('saenext.components.arms.EnableApplicationMonitoring')}
          </Button>
          {isLoading ? (
            <span style={{ marginLeft: 16, color: '#999' }}>
              {intl('saenext.components.arms.ItMayTakeToMinutes')}
            </span>
          ) : null}
        </div>
      </>
    );
  };

  return (
    <>
      <Message type="notice">
        {isArmsOpened ? (
          <div className="text-line">
            {intl('saenext.components.arms.TheServerlessApplicationEngineAutomatically')}
          </div>
        ) : (
          <>
            <div className="text-line">
              {intl('saenext.components.arms.TheServerlessApplicationEngineAutomatically.1')}
            </div>
            <div className="text-line">
              {intl('saenext.components.arms.CurrentlyApplicationMonitoringIsNot')}

              <span style={{ color: '#ff6a00' }}>
                {isLoading
                  ? intl('saenext.components.arms.DuringTheStartupProcessThe')
                  : intl('saenext.components.arms.ANewVersionOfThe')}
              </span>
            </div>
          </>
        )}
      </Message>
      {minInstanceCount === 0 ? (
        <Message type="warning" className="mt-s mb-s">
          {intl('saenext.components.arms.CurrentlyYourApplicationDoesNot')}
        </Message>
      ) : null}

      {isArmsOpened ? (
        <>Loading ...</>
      ) : (
        <div style={{ height: 370, position: 'relative' }}>
          <img
            style={{
              width: '100%',
              height: '100%',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'top',
              backgroundSize: 'cover',
              objectFit: 'cover',
            }}
            src="https://img.alicdn.com/imgextra/i3/O1CN01JJ97sS1OjQDuV6GRo_!!6000000001741-1-tps-3840-740.gif"
          />

          <div
            style={{ position: 'absolute', top: 32, left: 0, background: 'transparent', zIndex: 1 }}
          >
            {renderContent()}
          </div>
        </div>
      )}
    </>
  );
};

export default ArmsMonitor;
