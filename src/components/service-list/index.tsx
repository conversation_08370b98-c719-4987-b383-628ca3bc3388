import React, { useEffect, useState } from 'react';
import { intl, CndTable, Copy, Link, Actions, LinkButton } from '@ali/cnd';
import services from '~/services';
import { get, isEmpty, map } from 'lodash';
import ServiceMeta from '~/components/app-detail/micro-app/msc/ServiceMeta';

const ServiceListList = props => {
  const { regionId, namespaceId } = props;

  const [refreshIndex, setRefreshIndex] = useState(0);

  useEffect(() => {
    setRefreshIndex(Date.now());
  }, [namespaceId]);

  const fetchData = async params => {
    const { current = 1, pageSize = 10, ServiceName = '', AppName = '' } = params;
    const res = await services.ListNamespacedAppServices({
      params: {
        NamespaceId: !namespaceId || namespaceId === 'all' ? '' : namespaceId,
      },
    });
    let serviceList = get(res, 'Data', []);
    if (ServiceName || AppName) {
      serviceList = serviceList.filter(
        item => item.ServiceName.includes(ServiceName) && item.AppName.includes(AppName),
      );
    }
    let currentPageServices = serviceList.slice((current - 1) * pageSize, current * pageSize);
    const currentAppId = Array.from(new Set(map(currentPageServices, item => item.AppId)));
    const currentAppEnabledMsc = await Promise.all(
      currentAppId.map(item => {
        return services.describeMicroApplicationConfig({
          AppId: item,
        });
      }),
    );
    let appEnabledMscMap = {};
    map(currentAppEnabledMsc, item => {
      Reflect.set(
        appEnabledMscMap,
        item.Data.AppId,
        !isEmpty(get(item, 'Data.MseApplicationId', '')),
      );
    });
    currentPageServices = map(currentPageServices, item => {
      return {
        ...item,
        enableMsc: appEnabledMscMap[item.AppId] || false,
      };
    });
    return {
      data: currentPageServices,
      total: serviceList.length,
    };
  };

  const search = {
    defaultDataIndex: 'ServiceName',
    defaultSelectedDataIndex: 'ServiceName',
    // onlySupportOne: true,
    options: [
      {
        label: intl('saenext.regionId.service-list.ServiceName'),
        dataIndex: 'ServiceName',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.regionId.service-list.EnterAServiceName'),
        },
      },
      {
        label: intl('saenext.regionId.service-list.ApplicationName'),
        dataIndex: 'AppName',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.regionId.service-list.EnterAnApplicationName'),
        },
      },
    ],
  };

  const columns = [
    {
      key: 'ServiceName',
      title: intl('saenext.components.msc.ServiceList.ServiceName'),
      dataIndex: 'ServiceName',
      cell: value => {
        return <Copy text={value}>{value}</Copy>;
      },
    },
    {
      key: 'AppName',
      title: intl('saenext.regionId.service-list.Application'),
      dataIndex: 'AppName',
      cell: (value, index, record) => {
        return (
          <Copy text={value}>
            <Link to={`/${regionId}/app-list/${record.AppId}/micro-app/base`}>{value}</Link>
          </Copy>
        );
      },
    },
    {
      key: 'ServiceType',
      title: intl('saenext.components.msc.ServiceList.ServiceType'),
      dataIndex: 'ServiceType',
      cell: value => <div>{value || '-'}</div>,
    },
    {
      key: 'ServiceVersion',
      title: intl('saenext.components.msc.ServiceList.VersionNumber'),
      dataIndex: 'ServiceVersion',
      cell: value => <div>{value || '-'}</div>,
    },
    {
      key: 'ServiceGroup',
      title: intl('saenext.components.msc.ServiceList.Group'),
      dataIndex: 'ServiceGroup',
      cell: value => <div>{value || '-'}</div>,
    },
    {
      key: 'operations',
      title: intl('saenext.components.msc.ServiceList.Operation'),
      width: 120,
      cell: (value, index, record) => {
        return (
          <Actions>
            <ServiceMeta record={record} applicationID={record?.AppId}>
              <LinkButton disabled={!record?.enableMsc}>
                {intl('saenext.components.msc.ServiceList.Metadata')}
              </LinkButton>
            </ServiceMeta>
          </Actions>
        );
      },
    },
  ];

  return (
    <CndTable
      fetchData={fetchData}
      showRefreshButton
      refreshIndex={refreshIndex}
      search={search}
      columns={columns}
    />
  );
};
export default ServiceListList;
