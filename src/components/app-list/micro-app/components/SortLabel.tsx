import { Icon } from '@ali/cnd';
import _ from 'lodash';
import { map } from 'lodash';
import React, { useState, Fragment, useRef } from 'react';

const SortLabel = (props) => {
  const { dataSource, handler = () => { }, OrderBy = '', Reverse = '', firstSort = 'desc' } = props;
  const [curKey, setCurKey] = useState(OrderBy);
  const [curType, setCurType] = useState(
    typeof Reverse == 'boolean' ? (Reverse ? 'asc' : 'desc') : '',
  );
  const timeRef = useRef(null);
  const length = dataSource.length;
  const hanleClick = (key, type) => {
    clearTimeout(timeRef.current);
    timeRef.current = setTimeout(() => {
      // const type = Reverse ? 'des' : 'asc'

      const sortList = [
        { type: '', reverse: '' },
        { type: 'desc', reverse: false },
        { type: 'asc', reverse: true },
      ];
      const typeIndex = sortList.findIndex((v) => v.reverse === Reverse);
      const nextIndex = firstSort === 'desc' ? (typeIndex + 1) % 3 : (typeIndex + 2) % 3;
      const nextType = sortList[nextIndex].type;

      setCurKey(key);
      setCurType(nextType);
      handler(key, nextType);
    }, 100);
  };
  const getCurrent = (key, type) => {
    return curKey === key && curType === type;
  };
  const getStytle = (item, type) => {
    return {
      width: 6,
      cursor: 'pointer',
      color: `${getCurrent(_.get(item, 'key'), type) ? '#0070cc' : '#888'}`,
    };
  };
  return (
    <Fragment>
      {map(dataSource, (item, index) => {
        return (
          <span key={item.key}>
            <span onClick={() => hanleClick(_.get(item, 'key'), 'desc')}>
              {_.get(item, 'name')}
              {/* xconsole bug，正序逆序icon颠倒 */}
              <Icon type="descending" size="xs" style={getStytle(item, 'asc')} />
              <Icon type="ascending" size="xs" style={getStytle(item, 'desc')} />
            </span>
            {Number(index) < length - 1 && <span style={{ paddingLeft: 4 }}>/</span>}
          </span>
        );
      })}
    </Fragment>
  );
};

export default SortLabel;
