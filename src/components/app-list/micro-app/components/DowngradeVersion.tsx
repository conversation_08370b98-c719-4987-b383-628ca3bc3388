import { intl } from '@ali/cnd';
import React, { useState, useMemo } from 'react';
import { Dialog, Message, Form, Field, Select, Checkbox, Divider } from '@ali/cnd';
import { AppVersionMap } from '~/constants/application';
import services from '~/services';
import { get, has, filter } from 'lodash';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/cache/common';
import { LANGUAGE_TYPE } from '~/components/shared/DeploySelectorField/constant';

const versionsDataSource = Object.keys(AppVersionMap).map((key) => ({
  value: key,
  label: AppVersionMap[key].label,
}));
const DowngradeVersion = (props) => {
  const { enableNewSaeVersionDowngrade, enableNewSaeVersion, isMultipleNewAccount, multipleNewAccountStdEnable, appInfo, onClose, onOK } = props;
  const field = Field.useField();
  const [confirmOK, setConfirmOK] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [grayDowngradeConfirm, setGrayDowngradeConfirm] = useState(false);

  const handleOK = () => {
    field.validate(async (errors, values) => {
      if (!errors) {
        setSubmitLoading(true);
        const appIds =
          appInfo?.Children?.length > 0
            ? [appInfo?.AppId, ...appInfo?.Children?.map((item) => item?.AppId)]
            : [appInfo?.AppId];
        const appInfoRes = await Promise.all(
          appIds.map((appId) => {
            return services.describeMicroApplicationConfig({
              AppId: appId,
            });
          }),
        );
        const appConfigs = appInfoRes.map((item) => get(item, 'Data', {}));
        const deployRes = await Promise.all(
          appConfigs.map((appInfo) => {
            const regexImage = /^Image/i;
            const packageType = regexImage.test(appInfo?.PackageType) ? 'image' : 'micro_package';
            let params = {
              ...appInfo,
              NewSaeVersion: values?.targetVersion,
            };
            if (packageType === 'micro_package') {
              Reflect.set(
                params,
                'PackageVersion',
                `${new Date().getTime()}-${appInfo?.NewSaeVersion || 'std'}:${
                  values?.targetVersion
                }`,
              );
            }
            // 降级为轻量版
            if (values?.targetVersion === 'lite') {
              // 开了微服务治理，需要将微服务治理配置清空
              if (has(appInfo, 'MicroserviceEngineConfig')) {
                Reflect.set(params, 'MicroserviceEngineConfig', '');
              }
              // 轻量版不支持应用监控
              Reflect.set(params, 'EnableNewArms', false);
            }
            // 降级为标准版，标准版go/python有应用监控不做处理，其他EnableNewArms设置为false
            if (values?.targetVersion === 'std') {
              if (
                appInfo?.ProgrammingLanguage !== LANGUAGE_TYPE.GO &&
                appInfo?.ProgrammingLanguage !== LANGUAGE_TYPE.PYTHON
              ) {
                Reflect.set(params, 'EnableNewArms', false);
              }
            }
            return services.deployMicroApplication({
              params,
            });
          }),
        );
        setSubmitLoading(false);
        if (deployRes.every((item) => item.Success)) {
          onOK();
        } else {
          Message.error(intl('saenext.components.UpgradeVersion.VersionUpgradeFailed'));
        }
      }
    });
  };

  const tagetVersionDataSource = useMemo(() => {
    let _versionsDataSource = [];
    if (enableNewSaeVersion || (isMultipleNewAccount && multipleNewAccountStdEnable)) {
      _versionsDataSource = versionsDataSource;
    } else {
      _versionsDataSource = filter(versionsDataSource, item => {
        return item.value !== 'std';
      });
    }
    return filter(_versionsDataSource, item => {
      const curVersion = appInfo?.NewSaeVersion || 'std';
      return AppVersionMap[item.value].key < AppVersionMap[curVersion].key;
    });
  }, [isMultipleNewAccount, multipleNewAccountStdEnable]);

  return (
    <Dialog
      title={intl('saenext.micro-app.components.DowngradeVersion.VersionDowngrade')}
      visible={true}
      onClose={onClose}
      onCancel={onClose}
      onOk={enableNewSaeVersionDowngrade ? handleOK : onClose}
      style={{ width: 640 }}
      okProps={{
        disabled: appInfo?.Children?.length > 0 ? !(confirmOK && grayDowngradeConfirm) : !confirmOK,
        loading: submitLoading,
      }}
    >
      {!enableNewSaeVersionDowngrade && (
        <Message type="warning" className="mb-l">
          <span style={{ lineHeight: '20px', marginTop: 16 }}>
            {intl('saenext.micro-app.components.DowngradeVersion.SomeFunctionsOfTheApplication')}{' '}
            <a
              href={`${CachedData.confLink('feature:smartservice:url')}/service/create-ticket`}
              target="_blank"
            >
              {intl('saenext.micro-app.components.DowngradeVersion.SubmitATicket')}
            </a>{' '}
            {intl(
              'saenext.micro-app.components.DowngradeVersion.ContactTheServerlessApplicationEngine',
            )}
          </span>
        </Message>
      )}
      {enableNewSaeVersionDowngrade && (
        <div>
          <Message type="warning" className="mb-l">
            {intl('saenext.micro-app.components.DowngradeVersion.NoteTheDowngradeOperationWill')}
          </Message>
          <Form field={field} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
            <Form.Item
              label={intl('saenext.components.UpgradeVersion.CurrentVersion')}
              required
              asterisk={false}
            >
              <Select
                name="curVersion"
                disabled
                value={appInfo?.NewSaeVersion || 'std'}
                dataSource={versionsDataSource}
                style={{ width: '100%' }}
              />
            </Form.Item>
            <Form.Item
              label={intl('saenext.components.UpgradeVersion.TargetVersion')}
              required
              requiredMessage={intl('saenext.components.UpgradeVersion.SelectTheTargetVersion')}
              asterisk={false}
            >
              <Select
                name="targetVersion"
                dataSource={tagetVersionDataSource}
                placeholder={intl('saenext.components.UpgradeVersion.SelectTheTargetVersion')}
                style={{ width: '100%' }}
                menuProps={{
                  footer: (
                    <div style={{ padding: '0 4px', margin: '0 -4px' }}>
                      <Divider style={{ marginBottom: 0, marginTop: 4 }} />
                      <div style={{ paddingLeft: 8 }}>
                        <ExternalLink
                          label={intl(
                            'saenext.components.UpgradeVersion.ViewDetailedVersionComparison',
                          )}
                          url={CachedData.confLink('help:sae:app-multiple-versions')}
                        />
                      </div>
                    </div>
                  ),
                }}
              />
            </Form.Item>
            {appInfo?.Children?.length > 0 && (
              <Form.Item label=" " style={{ marginBottom: 0 }}>
                <Checkbox
                  checked={grayDowngradeConfirm}
                  onChange={() => setGrayDowngradeConfirm(!grayDowngradeConfirm)}
                >
                  {intl(
                    'saenext.micro-app.components.DowngradeVersion.ThereIsAGrayscaleApplication',
                  )}
                </Checkbox>
              </Form.Item>
            )}
            <Form.Item label=" ">
              <Checkbox checked={confirmOK} onChange={() => setConfirmOK(!confirmOK)}>
                {intl('saenext.micro-app.components.DowngradeVersion.IAmAwareOfThe')}
              </Checkbox>
            </Form.Item>
          </Form>
        </div>
      )}
    </Dialog>
  );
};

export default DowngradeVersion;
