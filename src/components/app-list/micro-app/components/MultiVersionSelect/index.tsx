import { intl } from '@ali/cnd';
import React, { useState, useMemo } from 'react';
import { Button } from '@ali/cnd';
import { AppVersionMap } from '~/constants/application';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/cache/common';
import './index.less';

const backgroundColorMap = {
  lite: '#F7F7F7',
  std: '#EAF2FF',
  pro: '#F8F4EA',
};
/**
 * 新用户（多版本发布之后开通SAE的用户）不加白 --- 轻量版+专业版
 * 老用户/新用户 --- 加白：轻量版+标准版+专业版  未加白：不区分版本
 */

const MultiVersionSelect = (props) => {
  const { isMultipleNewAccount, multipleNewAccountStdEnable,enableNewSaeVersion, handleOk } = props;
  const [version, setVersion] = useState('pro');
  const gridTemplateColumns = useMemo(() => {
    // 存量用户加白 或者 多版本新用户加白可展示标准版
    if (enableNewSaeVersion || (isMultipleNewAccount && multipleNewAccountStdEnable)) {
      return '1fr 1fr 1fr';
    } else {
      return '1fr 1fr';
    }
  }, [isMultipleNewAccount, enableNewSaeVersion, multipleNewAccountStdEnable]);

  const versionsOptions = useMemo(() => {
    return Object.keys(AppVersionMap).map(key => ({
      ...AppVersionMap[key],
      value: key,
      visible:
        key === 'std'
          ? (enableNewSaeVersion && !isMultipleNewAccount) ||
            (isMultipleNewAccount && multipleNewAccountStdEnable)
          : true,
      disabled: false,
    }));
  }, [enableNewSaeVersion, isMultipleNewAccount, multipleNewAccountStdEnable]);

  return (
    <div
      className="multi-version-select"
      // @ts-ignore
      style={{ '--grid-template-columns': gridTemplateColumns }}
    >
      <div className="mb-l">
        {isMultipleNewAccount
          ? intl('saenext.components.MultiVersionSelect.SaeProvidesLightweightAndProfessional')
          : intl('saenext.components.MultiVersionSelect.SaeProvidesThreeVersionsLightweight')}
      </div>
      <div className="mb-l">
        <span style={{ fontWeight: 500 }}>
          {intl('saenext.components.MultiVersionSelect.SelectAnApplicationVersion')}
        </span>
        <ExternalLink
          label={intl('saenext.components.MultiVersionSelect.ViewDetailedVersionComparison')}
          url={CachedData.confLink('help:sae:app-multiple-versions')}
        />
      </div>
      <div className="multi-version-select-group">
        {versionsOptions.map((item: any) => {
          return item.visible ? (
            <div
              className={`multi-version-select-item ${
                version === item.value ? 'multi-version-select-item-active' : ''
              } ${item.value === 'pro' ? 'pro-item' : ''} ${
                item?.disabled ? 'multi-version-select-item-disabled' : ''
              }`}
              // @ts-ignore
              style={{ '--multi-version-item-backgroundColor': backgroundColorMap[item.value] }}
              onClick={() => !item?.disabled && setVersion(item.value)}
            >
              <div className="multi-version-select-item-title">
                <img src={item.icon} />
                <span style={{ fontSize: 14, marginLeft: 4 }}>{item.label}</span>
              </div>
              <div className="multi-version-select-item-desc">
                <div style={{ color: '#333', fontWeight: 500, marginBottom: 12 }}>{item.desc}</div>
                <div
                  style={{ color: '#555' }}
                  dangerouslySetInnerHTML={{ __html: item.subDesc }}
                ></div>
              </div>
              {/* <div className="multi-version-select-item-feature">
               {item.feature.map(feature => {
                 return (
                   <Row gutter={16} style={{ lineHeight: '24px' }}>
                     <Col span={8}>{feature.label}</Col>
                     <Col>{item.featureDatasource[feature.dataIndex]}</Col>
                   </Row>
                 );
               })}
              </div> */}
              {item.value === 'pro' && (
                <div className="recommend-pro">
                  {intl('saenext.components.MultiVersionSelect.Recommend')}
                </div>
              )}
            </div>
          ) : (
            <></>
          );
        })}
      </div>
      <div className="multi-version-select-footer">
        <Button
          type="primary"
          className="mr-s"
          onClick={() => {
            handleOk(version);
          }}
        >
          {intl('saenext.components.MultiVersionSelect.CreateAnApplication')}
        </Button>
      </div>
    </div>
  );
};
export default MultiVersionSelect;
