.multi-version-select {
  .multi-version-select-group {
    display: grid;
    grid-template-columns: var(--grid-template-columns);
    grid-column-gap: 0px;

    .multi-version-select-item {
      position: relative;
      // width: 360px;
      padding: 16px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      box-sizing: content-box;
      cursor: pointer;
      background:
        linear-gradient(to right, var(--multi-version-item-backgroundColor), white),
        linear-gradient(to bottom, var(--multi-version-item-backgroundColor), white);
      background-blend-mode: normal;

      &:hover {
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16);
      }

      .multi-version-select-item-title {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
      }

      .multi-version-select-item-feature {
        margin-top: 16px;
        padding: 16px;
        background: #fff;
        border-radius: 4px;
      }
      .recommend-pro{
        position: absolute; /* 绝对定位 */
        top: 6px; /* 调整文字距离三角形顶部的距离 */
        right: 2px; /* 调整文字相对于三角形的位置 */
        transform: rotate(45deg); /* 将文字旋转45度以匹配三角形角度 */
        color: white; /* 设置文字颜色 */
        font-size: 12px; /* 设置文字大小 */
        font-weight: 500;
      }
    }
    .pro-item::before{
      content: ""; /* 伪元素必须设置content属性 */
      position: absolute; /* 绝对定位 */
      top: 0; /* 定位到顶部 */
      right: 0; /* 定位到右侧外边 */
      width: 0; /* 三角形宽度为0 */
      height: 0; /* 三角形高度为0 */
      border-style: solid; /* 边框样式 */
      border-width: 45px 0 0 45px; /* 控制三角形的大小和形状 */
      border-color: #ff6347 transparent transparent transparent; /* 设置三角形颜色 */
    }

    .multi-version-select-item-active {
      border: 2px solid #0064c8;
    }
    .multi-version-select-item-disabled{
      cursor:not-allowed;
    }
  }

  .multi-version-select-footer {
    text-align: center;
    margin-top: 30px;
  }
}