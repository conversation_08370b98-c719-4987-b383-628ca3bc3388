import { intl } from '@ali/cnd';
import { Message, useService } from '@ali/cnd';
import React, { useEffect } from 'react';
import AddPropsWrap from '~/components/shared/AddPropsWrap';
import services from '~/services';
import exportJsonToExcel from '~/utils/jsonToExcel';
import appsJsonFormat from '../utils/appsJsonFormat';

const DownloadWrap = (props) => {
  const { children, ...restProps } = props;

  const getApps = async ({ CurrentPage = 1, PageSize = 100, result = [] }) => {
    const params = {
      CurrentPage,
      PageSize,
    };
    const res = await services.listMicroApplications({
      params,
      customErrorHandle(err, serviceConfig, callback) {
        if (err) {
          Message.error(err.message);
        }
      },
    });
    const { Data: { Applications = [], TotalSize } = {} as any } = res || {};

    result.push(...Applications);
    if (CurrentPage * PageSize < TotalSize) {
      await getApps({ CurrentPage: CurrentPage + 1, PageSize, result });
    }
    return result;
  };

  const getAllApps = async () => {
    const result = await getApps({});
    return result;
  };

  const { loading, data: appList, run } = useService(getAllApps, {}, { manual: true });

  useEffect(() => {
    if (!loading && appList?.length > 0) {
      dataToExcel();
    }
  }, [loading, appList]);

  const dataToExcel = () => {
    exportJsonToExcel(
      appsJsonFormat(appList),
      intl('saenext.micro-app.components.DownloadWrap.ApplicationList'),
    );
  };

  return (
    <AddPropsWrap {...restProps} loading={loading} onClick={run}>
      {children}
    </AddPropsWrap>
  );
};

export default DownloadWrap;
