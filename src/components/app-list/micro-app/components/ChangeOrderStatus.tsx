import { intl } from '@ali/cnd';
import React, { FC, useState, useEffect } from 'react';
import { Message, Icon, useHistory, LinkButton } from '@ali/cnd';
import _, { isEmpty } from 'lodash';

type Props = {
  regionId: string;
  namespaceId: string;
  namespaceList: any[];
  lastChangeOrderId: string;
  lastChangeOrderStatus: string;
};

const ChangeOrderStatus: FC<Props> = (props) => {
  const history = useHistory();
  const { regionId, namespaceId, namespaceList = [], lastChangeOrderId, lastChangeOrderStatus } = props;
  if (_.isEmpty(lastChangeOrderStatus)) return null;

  const [namespaceName, setNamespaceName] = useState('');

  useEffect(() => {
    const namespace = _.find(namespaceList, item => item.NamespaceId === namespaceId);
    if (!namespace) return;
    const { NamespaceName = '' } = namespace || {};
    setNamespaceName(NamespaceName);
    
  }, [namespaceId, JSON.stringify(namespaceList)]);

  const statusMap = {
    running: {
      text: intl('saenext.micro-app.components.change-order-status.TheCurrentNamespaceHasA'),
      type: 'warning',
    },
    success: {
      text: intl('saenext.micro-app.components.change-order-status.TheChangeProcessOfThe'),
      type: 'success',
    },
    fail: {
      text: intl('saenext.micro-app.components.change-order-status.TheChangeProcessOfThe.1'),
      type: 'error',
    },
    abort: {
      text: intl('saenext.micro-app.components.change-order-status.TheChangeProcessOfThe.2'),
      type: 'help',
    },
  };
  const option = statusMap[_.lowerCase(lastChangeOrderStatus)];
  if (isEmpty(option)) return null;
  return (
    <Message className="mt-s" type={_.get(option, 'type', 'warning')}>
      <div className="flex">
        <span>{option.text}</span>
        <LinkButton 
          className='ml-xs'
          onClick={() => {
            if (!lastChangeOrderId || !namespaceName) return;
            window.open(`/${regionId}/namespace/${namespaceId}/record/${lastChangeOrderId}?name=${namespaceName}`);
          }}
        >
          {lastChangeOrderId}
          <Icon type="external_link" size="xs" className='ml-xs'/>
        </LinkButton>
      </div>
    </Message>
  );
};

export default ChangeOrderStatus;
