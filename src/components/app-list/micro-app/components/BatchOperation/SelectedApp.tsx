import { Feature, intl, Balloon } from '@ali/cnd';
import React, { FC } from 'react';
import { Tag } from '@ali/cnd';
import _, { filter, includes } from 'lodash';

type Props = {
  value?: string[];
  onChange?: (value: string[]) => void;
  dataSource: string[];
  mode?: 'normal' | 'selectable' | 'closeable' | 'disabled';
  disabledAppIds?: string[];
  eType?: string;
};
const SelectedApp: FC<Props> = (props) => {
  const { value = [], onChange, dataSource, mode = 'normal', disabledAppIds = [] } = props;
  if (dataSource.length === 0) return null;
  const handleChangeTag = (checked: boolean, primaryKey: string) => {
    if (checked) {
      onChange([...value, primaryKey]);
    } else {
      onChange(filter(value, (id) => id !== primaryKey));
    }
  };

  return (
    <>
      <div className="mb-l">
        {intl(
          'saenext.components.batch-operation.selected-app.SelectedApplicationsSelectedrowkeyslength',
          { selectedRowKeysLength: value.length - disabledAppIds.length },
        )}
      </div>
      <Tag.Group>
        {dataSource.map((primaryKey) => {
          const [appName, appId] = _.split(primaryKey, '&&');
          const isDisabled = disabledAppIds.includes(appId);
          const tagNode = (
            <Tag.Selectable
              key={appId}
              checked={includes(value, primaryKey)}
              onChange={(checked) => handleChangeTag(checked, primaryKey)}
              disabled={isDisabled}
            >
              {appName}
            </Tag.Selectable>
          );

          if (mode === 'selectable') {
            return isDisabled? <Balloon.Tooltip trigger={<span>{tagNode}</span>} >
              {intl('saenext.app-list.micro-app.LightweightEditionDoesNotSupport')}
            </Balloon.Tooltip> : tagNode;
          } else if (mode === 'closeable') {
            return (
              <Tag.Closeable
                key={appId}
                afterClose={() => handleChangeTag(false, primaryKey)}
              >
                {appName}
              </Tag.Closeable>
            );
          } else if (mode === 'normal') {
            return <Tag key={appId}>{appName}</Tag>;
          }
        })}
      </Tag.Group>
    </>
  );
};

export default SelectedApp;
