import React, {
  forwardRef,
  ForwardRefRenderFunction,
  useContext,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import {
  Balloon,
  Button,
  ConsoleContext,
  Dialog,
  Icon,
  Message,
  Radio,
  SlidePanel,
  intl,
  useHistory,
} from '@ali/cnd';
import { EType, EStrategy, TStrategy, ActionTitle, ApiMap, MessageMap, TextMap, ButtonTextMap } from './type';
import SelectedApp from './SelectedApp';
import _ from 'lodash';
import { getAppTypeEnabled, getParams } from '~/utils/global';
import useOpenApi from '~/utils/useOpenApi';
import If from '~/components/shared/If';
export * from './type';
import FeatureContext from '~/utils/featureContext';

interface IRef {
  toogleVisible: () => void;
}

type Props = {
  type: EType;
  disabled: boolean;
  selectedRowKeys: string[];
  selectedRecords?: any[];
  cb: () => Promise<any>;
  className?: string;
};

const BatchOperation: ForwardRefRenderFunction<IRef, Props> = (props, ref) => {
  const { disabled, selectedRowKeys = [], selectedRecords = [], type = EType.START, cb, className } = props;
  const history = useHistory();
  const { region } = useContext(ConsoleContext);
  const { feature } = useContext(FeatureContext);
  const regionId = region.getCurrentRegionId();
  const [selectedApps, setSelectedApps] = useState<string[]>([]);
  const [isShowing, setIsShowing] = useState(false);
  const [timeType, setTimeType] = useState<TStrategy>(EStrategy.NOW);
  const [liteAppIds, setLiteAppIds] = useState<string[]>([]);

  const namespaceId = getParams('namespaceId');
  const { webEnabled } = getAppTypeEnabled(regionId);

  const text = TextMap[type];
  const paramsMap = {
    [EType.START]: {
      Version: webEnabled ? '2.0' : undefined,
      NamespaceId: namespaceId,
    },
    [EType.STOP]: {
      Version: webEnabled ? '2.0' : undefined,
      NamespaceId: namespaceId,
    },
    [EType.BATCH_ENABLE_IDLE]: {
      EnableIdle: true,
      NamespaceId: namespaceId,
    },
    [EType.BATCH_DISABLE_IDLE]: {
      EnableIdle: false,
      NamespaceId: namespaceId,
    },
  };
  const params = paramsMap[type] || {};
  const buttonText = ButtonTextMap[type];

  useEffect(() => {
    if ((type === EType.BATCH_ENABLE_IDLE || type === EType.BATCH_DISABLE_IDLE) && feature.liteIdle === false) {
      setLiteAppIds(selectedRecords.filter(record => record.NewSaeVersion === 'lite').map(record => record.AppId));
    }
  }, [selectedRecords, type, feature.liteIdle]);

  const {
    loading: batchLoading,
    run: BatchApplications,
    error: batchError,
  } = useOpenApi(
    'serverless',
    ApiMap[type],
    {
      ...params,
      AppIds: _
        .chain(selectedApps)
        .filter(key => {
          const [, appId] = key.split('&&');
          return !liteAppIds.includes(appId);
        })
        .map(key => {
          const [, appId] = key.split('&&');
          return appId;
        })
        .join(',')
        .value(),
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    if (isShowing) {
      setSelectedApps(selectedRowKeys);
    }
  }, [selectedRowKeys, isShowing]);

  useImperativeHandle(ref, () => ({
    toogleVisible,
  }));

  const handleTimeTypeChange = (value: TStrategy) => {
    setTimeType(value);
    if (value === EStrategy.INTERVAL) {
      Dialog.alert({
        title: intl('saenext.components.batch-operation.Confirm'),
        content: intl('saenext.components.batch-operation.DoYouWantToJump'),

        onOk: () => {
          history.push(`/${region.getCurrentRegionId()}/namespace/${namespaceId}/crontab`);
        },
        onCancel: () => {
          setTimeType(EStrategy.NOW);
        },
        onClose: () => {
          setTimeType(EStrategy.NOW);
        },
      });
    }
  };

  const handleSubmit = async () => {
    const res = await BatchApplications();
    const action = ActionTitle[type];

    if (type === EType.BATCH_ENABLE_IDLE || type === EType.BATCH_DISABLE_IDLE) {
      if (res.Success) {
        Message.success(intl('saenext.components.BatchOperation.ActionSucceeded', { action: action }));
      } else {
        Message.error(intl('saenext.components.BatchOperation.ActionFailed', { action: action }));
      }
    } else {
      const { Data } = res;
      if (!Data) return;
      Message.success(intl('saenext.components.BatchOperation.ActionSucceeded', { action: action }));
    }
    setIsShowing(false);
    await cb();
  };

  const toogleVisible = () => {
    setIsShowing((prev) => !prev);
  };

  return (
    <>
      <Button
        className={className}
        disabled={disabled}
        type="primary"
        onClick={() => setIsShowing((prev) => !prev)}
      >
        {buttonText}
      </Button>
      <SlidePanel
        title={ActionTitle[type]}
        onCancel={() => setIsShowing(false)}
        onOk={handleSubmit}
        isShowing={isShowing}
        isProcessing={batchLoading}
        okText={intl('button.ok')}
        cancelText={intl('button.cancel')}
        processingText={intl('button.processing')}
      >
        <SelectedApp
          mode='selectable'
          value={selectedApps}
          onChange={setSelectedApps}
          dataSource={selectedRowKeys}
          disabledAppIds={liteAppIds}
        />
        <If condition={type === EType.START || type === EType.STOP}>
          <div className="mt-l mb-l">
            {intl('saenext.components.batch-operation.TextPolicy', { text: text })}
          </div>
          <Radio.Group value={timeType} onChange={handleTimeTypeChange}>
            <Radio id={EStrategy.NOW} value={EStrategy.NOW}>
              <span>{intl('saenext.components.batch-operation.TextNow', { text: text })}</span>
              <Balloon
                trigger={
                  <Icon
                    size="small"
                    style={{ marginLeft: 4, color: '#0070cc', lineHeight: '15px' }}
                    type="help"
                  />
                }
                closable={false}
                align="t"
              >
                {intl('saenext.components.batch-operation.ApplicableScenarioTemporarySingleO')}
              </Balloon>
            </Radio>
            <Radio id={EStrategy.INTERVAL} value={EStrategy.INTERVAL}>
              <span>{intl('saenext.components.batch-operation.TimingText', { text: text })}</span>
              <Balloon
                trigger={
                  <Icon
                    size="small"
                    style={{ marginLeft: 4, color: '#0070cc', lineHeight: '15px' }}
                    type="help"
                  />
                }
                closable={false}
                align="t"
              >
                {intl('saenext.components.batch-operation.ApplicableScenarioPlannedSingleOr')}
              </Balloon>
            </Radio>
          </Radio.Group>
        </If>
        <Message type={type === EType.UPGRADE_ARMS ? 'notice' : 'warning'} className="mt-l">
          <p>{MessageMap[type]}</p>
        </Message>
      </SlidePanel>
    </>
  );
};

export default forwardRef(BatchOperation);
