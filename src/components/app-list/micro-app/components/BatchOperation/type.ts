import { intl } from '@ali/cnd';
import CachedData from '~/cache/common';

export enum EType {
  START = 'start',
  STOP = 'stop',
  UPGRADE_ARMS = 'upgradeArms',
  DOWNGRADE_ARMS = 'downgradeArms',
  BATCH_ENABLE_IDLE = 'batchEnableIdle',
  BATCH_DISABLE_IDLE = 'batchDisableIdle',
}

export type TType = `${EType}`;

export const ApiMap = {
  [EType.START]: 'BatchStartApplications',
  [EType.STOP]: 'BatchStopApplications',
  [EType.UPGRADE_ARMS]: 'BatchUpgradeApplicationApmService',
  [EType.DOWNGRADE_ARMS]: 'BatchDowngradeApplicationApmService',
  [EType.BATCH_ENABLE_IDLE]: 'UpdateAppMode',
  [EType.BATCH_DISABLE_IDLE]: 'UpdateAppMode',
};

export const ActionTitle = {
  [EType.START]: intl('saenext.components.batch-operation.StartApplicationsInBatches'),
  [EType.STOP]: intl('saenext.components.batch-operation.StopApplicationsInBatches'),
  [EType.UPGRADE_ARMS]: intl(
    'saenext.components.BatchOperation.type.EnableArmsAdvancedMonitoringIn',
  ),
  [EType.DOWNGRADE_ARMS]: intl(
    'saenext.components.BatchOperation.type.DisableArmsAdvancedMonitoringIn',
  ),
  [EType.BATCH_ENABLE_IDLE]: intl('saenext.app-list.micro-app.BatchEnableIdle'),
  [EType.BATCH_DISABLE_IDLE]: intl('saenext.app-list.micro-app.BatchDisableIdle'),
};

export const MessageMap = {
  [EType.START]: intl('saenext.components.batch-operation.AfterYouStartApplicationsIn'),
  [EType.STOP]: intl('saenext.components.batch-operation.AfterYouStopApplicationsIn'),
  [EType.UPGRADE_ARMS]: intl.html(
    'saenext.components.BatchOperation.type.ServerlessTheApplicationEngineIntegrates.new',{
      href:CachedData.confLink('help:arms:billing-overview')
    }
  ),
  [EType.DOWNGRADE_ARMS]: intl.html(
    'saenext.components.BatchOperation.type.AfterArmsAdvancedMonitoringIs',
  ),
  [EType.BATCH_ENABLE_IDLE]: intl('saenext.basic-info.IdleMode.IdleSwitch.AfterTheIdleModeIs'),
  [EType.BATCH_DISABLE_IDLE]: intl('saenext.basic-info.IdleMode.IdleSwitch.AfterTheIdleModeIs.1'),
};

export const TextMap = {
  [EType.STOP]: intl('saenext.components.batch-operation.Stop'),
  [EType.BATCH_ENABLE_IDLE]: intl('saenext.components.batch-operation.Enable'),
  [EType.BATCH_DISABLE_IDLE]: intl('saenext.components.batch-operation.Disable'),
  [EType.START]: intl('saenext.components.batch-operation.Start'),
};


export const ButtonTextMap = {
  [EType.STOP]: intl('saenext.components.batch-operation.StopApplicationsInBatches'),
  [EType.BATCH_ENABLE_IDLE]: intl('saenext.components.batch-operation.EnableIdleModeInBatches'),
  [EType.BATCH_DISABLE_IDLE]: intl('saenext.components.batch-operation.DisableIdleModeInBatches'),
  [EType.START]: intl('saenext.components.batch-operation.StartApplicationsInBatches'),
};

export interface IAppItem {
  AppId: string;
  AppName: string;
  // others...
  [key: string]: any;
}

export enum EStrategy {
  NOW = 'now',
  INTERVAL = 'interval',
}

export type TStrategy = `${EStrategy}`;
