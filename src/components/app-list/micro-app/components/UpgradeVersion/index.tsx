import { intl } from '@ali/cnd';
import React, { useState, useEffect } from 'react';
import { Dialog, Message, Form, Field, Select, Checkbox, Divider } from '@ali/cnd';
import { AppVersionMap } from '~/constants/application';
import services from '~/services';
import { get } from 'lodash';
import ExternalLink from '~/components/shared/ExternalLink';
import CachedData from '~/cache/common';
import { LANGUAGE_TYPE } from '~/components/shared/DeploySelectorField/constant';

const versionsDataSource = Object.keys(AppVersionMap).map((key) => ({
  value: key,
  label: AppVersionMap[key].label,
}));

const UpgradeVersion = (props) => {
  const { appInfo, onClose, onOK } = props;
  const field = Field.useField();
  const [confirmOK, setConfirmOK] = useState(false);
  const [grayUpgradeConfirm, setGrayUpgradeConfirm] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  const handleOK = () => {
    field.validate(async (errors, values) => {
      if (!errors) {
        setSubmitLoading(true);
        const appIds =
          appInfo?.Children?.length > 0
            ? [appInfo?.AppId, ...appInfo?.Children?.map((item) => item?.AppId)]
            : [appInfo?.AppId];
        console.log('appIds',appIds)
        const appInfoRes = await Promise.all(
          appIds.map((appId) => {
            return services.describeMicroApplicationConfig({
              AppId: appId,
            });
          }),
        );
        const appConfigs = appInfoRes.map((item) => get(item, 'Data', {}));
        const deployRes = await Promise.all(
          appConfigs.map((appInfo) => {
            const regexImage = /^Image/i;
            const packageType = regexImage.test(appInfo?.PackageType)?'image':'micro_package';
            let params = {
              ...appInfo,
              NewSaeVersion: values?.targetVersion,
            };
            if(packageType==='micro_package'){
              Reflect.set(params,'PackageVersion',`${new Date().getTime()}-${appInfo?.NewSaeVersion || 'std'}:${values?.targetVersion}`);
            }
            // 标准版->专业版 && java应用：EnableNewArms 设置为true
            if (
              (!appInfo?.NewSaeVersion || appInfo?.NewSaeVersion === 'std') &&
              values?.targetVersion === 'pro' &&
              appInfo?.ProgrammingLanguage === LANGUAGE_TYPE.JAVA
            ) {
              Reflect.set(params, 'EnableNewArms', true);
            }
            return services.deployMicroApplication({
              params,
            });
          }),
        );
        console.log('deployRes', deployRes);
        setSubmitLoading(false);
        if (deployRes.every((item) => item.Success)) {
          onOK();
        } else {
          Message.error(intl('saenext.components.UpgradeVersion.VersionUpgradeFailed'));
        }
      }
    });
  };

  const tagetVersionDataSource = versionsDataSource.filter((item) => {
    const curVersion = appInfo?.NewSaeVersion || 'std';
    return AppVersionMap[item.value].key > AppVersionMap[curVersion].key && item.value !== 'std';
  });

  return (
    <Dialog
      title={intl('saenext.components.UpgradeVersion.VersionUpgrade')}
      visible={true}
      onClose={onClose}
      onCancel={onClose}
      onOk={handleOK}
      style={{ width: 640 }}
      okProps={{
        disabled: appInfo?.Children?.length > 0 ? !(confirmOK && grayUpgradeConfirm) : !confirmOK,
        loading: submitLoading,
      }}
    >
      <Message type="warning" className="mb-l">
        {intl('saenext.components.UpgradeVersion.NoteTheUpgradeOperationWill')}
      </Message>
      <Form field={field} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Form.Item
          label={intl('saenext.components.UpgradeVersion.CurrentVersion')}
          required
          asterisk={false}
        >
          <Select
            name="curVersion"
            disabled
            value={appInfo?.NewSaeVersion || 'std'}
            dataSource={versionsDataSource}
            style={{ width: '100%' }}
          />
        </Form.Item>
        <Form.Item
          label={intl('saenext.components.UpgradeVersion.TargetVersion')}
          required
          requiredMessage={intl('saenext.components.UpgradeVersion.SelectTheTargetVersion')}
          asterisk={false}
        >
          <Select
            name="targetVersion"
            dataSource={tagetVersionDataSource}
            placeholder={intl('saenext.components.UpgradeVersion.SelectTheTargetVersion')}
            style={{ width: '100%' }}
            menuProps={{
              footer: (
                <div style={{ padding: '0 4px', margin: '0 -4px' }}>
                  <Divider style={{ marginBottom: 0, marginTop: 4 }} />
                  <div style={{ paddingLeft: 8 }}>
                    <ExternalLink
                      label={intl(
                        'saenext.components.UpgradeVersion.ViewDetailedVersionComparison',
                      )}
                      url={CachedData.confLink('help:sae:app-multiple-versions')}
                    />
                  </div>
                </div>
              ),
            }}
          />
        </Form.Item>
        {appInfo?.Children?.length > 0 && (
          <Form.Item label=" " style={{ marginBottom: 0 }}>
            <Checkbox
              checked={grayUpgradeConfirm}
              onChange={() => setGrayUpgradeConfirm(!grayUpgradeConfirm)}
            >
              {intl('saenext.components.UpgradeVersion.ThereIsAGrayscaleApplication')}
            </Checkbox>
          </Form.Item>
        )}
        <Form.Item label=" ">
          <Checkbox checked={confirmOK} onChange={() => setConfirmOK(!confirmOK)}>
            {intl('saenext.components.UpgradeVersion.IAmAwareOfThe')}
          </Checkbox>
        </Form.Item>
      </Form>
    </Dialog>
  );
};

export default UpgradeVersion;
