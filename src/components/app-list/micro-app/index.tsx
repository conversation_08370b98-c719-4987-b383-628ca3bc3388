import React, { useState, useContext, useEffect } from 'react';
import { Link, MenuButton, StatusIndicator, StatusType, ToolTipCondition, intl } from '@ali/cnd';
import CndTable, { ISearch } from '@ali/cnd-table';
import services from '~/services';
import { getParams, microAppLink, setSearchParams } from '~/utils/global';
import _, { get, pickBy } from 'lodash';
import SortLabel from './components/SortLabel';
import {
  Copy,
  Truncate,
  Actions,
  Button,
  Balloon,
  Icon,
  ConsoleContext,
  Message,
  useHistory,
  DataFields,
  RegionGuidance,
  Instance,
  Dialog,
} from '@ali/cnd';
import {
  TagSelector,
  TagFilter,
  TagTips,
  BatchTagEditor,
  withTagIntlProvider,
} from '@ali/xconsole-rc-tags';
import * as C from './constant';
import { AppVersionMap } from '~/constants/application';
import { getTags } from './utils';
import '@ali/xconsole-rc-tags/dist/index.css';
import CachedData from '~/cache/common';
import ChangeOrderStatus from './components/ChangeOrderStatus';
import BatchOperation, { EType } from './components/BatchOperation';
import MultiVersionSelect from './components/MultiVersionSelect';
import UpgradeVersion from './components/UpgradeVersion';
import DowngradeVersion from './components/DowngradeVersion';
import useOpenApi from '~/utils/useOpenApi';
import If from '~/components/shared/If';
import DownloadWrap from './components/DownloadWrap';
import useMseOpenStatus from '~/hooks/useMseOpenStatus';
import FeatureContext from '~/utils/featureContext';
import { jsonParse } from '~/utils/transfer-data';
import moment from 'moment';
import { confFeature } from '@alicloud/console-one-conf';

const { LinkButton } = Actions;
const { Tooltip } = Balloon;

const TagSelectorIntl = withTagIntlProvider(TagSelector);
const TagFilterIntl = withTagIntlProvider(TagFilter);
const TagTipsIntl = withTagIntlProvider(TagTips);
const BatchTagEditorIntl = withTagIntlProvider(BatchTagEditor);

type Props = {
  history: any;
  namespaceList?: any[];
  namespaceId: string;
  stateful?: string;
  tableUniqueKey?: string;
};

export const MicroListKey = 'micro-app';

const MicroApp = (props: Props) => {
  const { namespaceList, namespaceId, stateful, tableUniqueKey: MicroListKey} = props;
  const { region } = useContext(ConsoleContext);
  const currentRegionId = region.getCurrentRegionId();
  const history = useHistory();
  const NamespaceId = namespaceId
    ? namespaceId === 'all' || namespaceId === 'null'
      ? ''
      : namespaceId
    : '';
    

  const sortStorageKey = `${MicroListKey}-instance-sort`;
  const { OrderBy = '', Reverse = '' } = sortStorageKey
    ? jsonParse(localStorage.getItem(sortStorageKey))
    : {};

  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [pageRefresh, setPageRefresh] = useState(0);
  const [tags, setTags] = useState(getTags());
  const [instancesSort, setInstancesSort] = useState<{
    OrderBy: string;
    Reverse: string | boolean;
  }>({ OrderBy, Reverse });
  const [tagDialogVisible, setTagDialogVisible] = useState(false);
  const [auth, setAuth] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [multiVersionVisible, setMultiVersionVisible] = useState(false);
  const [curAppInfo, setCurAppInfo] = useState(null);
  const [upgradeVisible, setUpgradeVisible] = useState(false);
  const [downgradeVisible, setDowngradeVisible] = useState(false);
  const { feature, inDebtData, webFeature } = useContext(FeatureContext);
  const {
    idle = false,
    allowUpdateAppAliasName = false,
    enableNewSaeVersionDowngrade = false,
  } = feature;
  const [selectedRecords, setSelectedRecords] = useState<any[]>([]);

  const {
    openStatus: { mse2: mseOpenStatus },
  } = useMseOpenStatus();

  const ref = React.useRef(null);
  const initRef = React.useRef(false);

  const {
    data: namespaceResources,
    run: fetchDescribeNamespaceResources,
    cancel: cancelDescribeNamespaceResources,
    error: Error,
  } = useOpenApi(
    'serverless',
    'DescribeNamespaceResources',
    { NamespaceId },
    { pollingInterval: 5000, manual: true, disableErrorPrompt: true },
  );

  const { LastChangeOrderRunning, LastChangeOrderStatus, LastChangeOrderId } = _.get(
    namespaceResources,
    'Data',
    {},
  );

  const AccountOpenTime = webFeature?.AccountOpenTime;
  const multipleVersionsTime = get(window, 'ALIYUN_CONSOLE_GLOBAL.multipleVersionsTime');
  // 是否多版本新用户 --- 轻量版+专业版
  const isMultipleNewAccount =
    AccountOpenTime && multipleVersionsTime
      ? AccountOpenTime > moment(multipleVersionsTime).valueOf()
      : false;
  // 判断老用户是否加白 --- 加白：轻量版+标准版+专业版  未加白：不区分版本
  const enableNewSaeVersion = feature?.enableNewSaeVersion;
  const isSupportMultiVersions = enableNewSaeVersion || isMultipleNewAccount;
  const saeOldConsoleEnable = confFeature('sae_1.0_console_enable');
  // 多版本新用户是否加白 加白则支持标准版
  const multipleNewAccountStdEnable = confFeature('multipleNewAccount_std_enable');
  // 无状态应用 并且具备多版本新用户或闲置模式 可以展示批量闲置按钮
  const canBatchEnableIdle = ( stateful !== 'true' && (idle || isMultipleNewAccount));
  const isIntl = CachedData.isSinSite();
  useEffect(() => {
    if (Error?.message.includes(intl('saenext.app-list.micro-app.PleaseLogIn'))) {
      return cancelDescribeNamespaceResources();
    }
  }, [Error, cancelDescribeNamespaceResources]);

  useEffect(() => {
    resetPageNumber();
    if (NamespaceId === 'none') return;
    if (_.isEmpty(NamespaceId)) return cancelDescribeNamespaceResources();
    fetchDescribeNamespaceResources();
  }, [NamespaceId]);

  const fetchData = async (params) => {
    // 未知命名空间
    if (NamespaceId === 'none') {
      return {
        data: [],
        total: 0,
      };
    }

    const filtersMap = _.values(C.FILTERS);
    setSearchParams(_.pick(params, filtersMap));
    const keys = Object.keys(params);
    const fieldType = _.find(keys, (k) => {
      return _.includes(filtersMap, k) && !_.isEmpty(_.get(params, k));
    });

    const _params = {
      CurrentPage: params.current,
      PageSize: params.pageSize,
      ClusterType: '8',
      EventSubmitDoQuery: 1,
      PhysicalRegionId: currentRegionId,
      NamespaceId,
      ...instancesSort,
      FieldType: fieldType,
      FieldValue: _.get(params, fieldType),
      IsStateful: stateful,
    };
    if (tags && tags.length > 0) {
      const tempTag = _.map(tags, (item) => {
        if (item.tagValue) {
          return { key: item.tagKey, value: item.tagValue };
        }
        return { key: item.tagKey };
      });
      _.set(_params, 'Tags', tempTag);
    }
    const res = await services.listMicroApplications({
      params: _params,
      customErrorHandle(err, serviceConfig, callback) {
        if (err) {
          setAuth(err.code !== 'AuthenticationFailed');
          callback(err);
          return {
            data: [],
            total: 0,
          };
        }
      },
    });
    const { Data: { Applications, TotalSize } = {} as any } = res || {};
    // primaryKey 选中应用后，方便展示 应用名称
    return {
      data: _.map(Applications, (item) => ({
        ...item,
        primaryKey: `${item.AppName}&&${item.AppId}`,
        children: _.map(item.Children, (child) => ({
          ...child,
          primaryKey: `${child.AppName}&&${child.AppId}`,
        })),
      })),
      total: TotalSize,
    };
  };

  const resetPageNumber = () => {
    if (!initRef.current) {
      initRef.current = true;
      return;
    }
    const pageStorageKey = `${MicroListKey}-pagination`;
    const pageCache = localStorage.getItem(pageStorageKey);
    const _curPagination = jsonParse(pageCache) || {};
    const newPagination = {
      ..._curPagination,
      curCurrent: 1,
    };
    localStorage.setItem(pageStorageKey, JSON.stringify(newPagination));
    setPageRefresh(pageRefresh + 1);
  };

  const handleUpdateAppAliasName = async (record, value) => {
    if (value === record?.AppName) {
      return;
    }
    Message.loading(intl('saenext.app-list.micro-app.TheApplicationNameIsBeing'));
    const res = await services.UpdateAppAliasName({
      AppId: record?.AppId,
      AppAliasName: value,
    });
    if (res?.Success) {
      Message.success(intl('saenext.app-list.micro-app.TheApplicationNameHasBeen'));
      setRefreshIndex(Date.now());
    }
  };

  const renderNamespace = (NamespaceId = currentRegionId, index, record) => {
    const namespace = _.find(namespaceList, { NamespaceId });
    const { NamespaceName = '' } = namespace || {};

    if (!namespace) {
      return NamespaceId;
    }

    const trigger = (
      <a href={`/${currentRegionId}/namespace/${NamespaceId}/base/?name=${NamespaceName}`}>
        {NamespaceName}
      </a>
    );

    return (
      <Balloon.Tooltip trigger={trigger}>
        <DataFields
          dataSource={namespace}
          items={[
            {
              dataIndex: 'NamespaceName',
              label: intl('saenext.app-list.micro-app.NamespaceName'),
              span: 24,
            },
            {
              dataIndex: 'NamespaceId',
              label: intl('saenext.app-list.micro-app.NamespaceId'),
              span: 24,
            },
            {
              dataIndex: 'VpcId',
              label: intl('saenext.app-list.micro-app.Vpc'),
              span: 24,
            },
          ]}
        />
      </Balloon.Tooltip>
    );
  };

  const handleInstanceSortChange = (key, type) => {
    const _reverse = type === '' ? '' : type === 'asc';
    const _key = type === '' ? '' : key;
    localStorage.setItem(sortStorageKey, JSON.stringify({ OrderBy: _key, Reverse: _reverse }));
    setInstancesSort({ OrderBy: _key, Reverse: _reverse });
    resetPageNumber();
  };

  const onTagSelect = (t) => {
    const exist = _.find(tags, (item) => item.tagKey === t.tagKey);
    const newTags = exist
      ? _.map(tags, (item) => {
        if (item.tagKey === t.tagKey) {
          return { ...item, tagValue: t.tagValue };
        }
        return item;
      })
      : [...tags, t];
    handleChangeTag(newTags);
  };

  const handleNavToCreateApp = (version?, path = '') => {
    const paramsObject = {
      namespaceId: NamespaceId && NamespaceId !== 'none' ? `${NamespaceId}` : '',
      version: version || '',
      stateful: stateful || '',
    };
    const filteredParams = pickBy(paramsObject, (value) => !!value);
    const search = new URLSearchParams(filteredParams);
    const searchStr = search.toString();
    history.push(`/${currentRegionId}/create-app/micro${path}?${searchStr}`);
  };

  const handleChangeTag = (v) => {
    setSearchParams({
      tagKeys: _.map(v, (item) => item.tagKey),
      tagValues: _.map(v, (item) => item.tagValue).filter(Boolean),
    });
    setTags(v);
    setRefreshIndex(Date.now());
  };

  const columns = [
    {
      key: 'AppName',
      title: `${intl('saenext.app-list.micro-app.ApplicationId')} / ${intl(
        'saenext.app-list.micro-app.ApplicationName',
      )}`,
      dataIndex: 'AppName',
      width: 200,
      cell: (value, index, record) => {
        const { v2Link } = microAppLink(record.AppId, currentRegionId);
        return (
          <div>
            {/* 应用id跳转为满足右键新标签页打开改为自定义组件 */}
            <Link to={v2Link}>
              <Copy text={record?.AppId}>
                <Truncate type="width" threshold={150} align="t">
                  {record?.AppId}
                </Truncate>
              </Copy>
            </Link>
            <Instance
              // link={{
              //   value: record?.AppId,
              //   onClick: () => {
              //     history.push(v2Link);
              //   },
              //   truncateProps: {
              //     type: 'width',
              //     threshold: 150,
              //   },
              // }}
              text={{
                value: value,
                showCopy: true,
                title: intl('saenext.app-list.micro-app.ModifyTheApplicationName'),
                truncateProps: {
                  type: 'length',
                  threshold: 39,
                  style: {
                    maxWidth: 150,
                    wordBreak: 'break-all',
                  },
                },
                rules: [
                  {
                    required: true,
                    message: intl(
                      'saenext.app-create.web-app.BasicCreator.TheApplicationNameCannotBe',
                    ),
                  },
                  {
                    pattern: new RegExp(
                      `^[a-zA-Z]([0-9a-zA-Z\-]{0,${inDebtData?.MaxAppNameLength || 36}})$`,
                    ),
                    message: intl('saenext.app-create.web-app.BasicCreator.MicroNameHelp', {
                      maxLength: inDebtData?.MaxAppNameLength || 36,
                    }),
                  },
                  {
                    validator: async (rule, value, callback) => {
                      if (value !== record?.AppName) {
                        const checkResult = await services.CheckAppNameV2({
                          NamespaceId: record?.NamespaceId || currentRegionId,
                          AppName: value,
                        });
                        if (
                          checkResult?.Success &&
                          !checkResult?.Data?.Available &&
                          record?.AppId !== checkResult?.Data?.AppId
                        ) {
                          callback(
                            intl('saenext.micro-app.ModifyTheApplicationName.conflict.tip', {
                              appName:
                                checkResult?.Data?.AppAliasName || checkResult?.Data?.AppName,
                            }),
                          );
                        } else {
                          callback();
                        }
                      } else {
                        callback();
                      }
                    },
                    trigger: ['onBlur', 'onChange'],
                  },
                ],

                onChange: allowUpdateAppAliasName
                  ? (v) => {
                    handleUpdateAppAliasName(record, v);
                  }
                  : null,
              }}
              truncateProps={{
                type: 'width',
                threshold: 140,
              }}
              footer={
                record.appDeletingStatus ? (
                  <span style={{ color: 'red' }}>
                    {intl('saenext.app-list.micro-app.Deleting')}
                  </span>
                ) : null
              }
            />
          </div>
        );
      },
      lock: 'left',
    },
    {
      key: 'Tags',
      title: intl('saenext.app-list.micro-app.Label'),
      dataIndex: 'Tags',
      width: 100,
      cell: (value, index, record) => (
        <TagTipsIntl
          regionId={currentRegionId}
          resourceId={record.AppId}
          resourceType={C.RESOURCE_TYPE}
          dataSource={_.map(value, (item) => ({ tagKey: item.Key, tagValue: item.Value }))}
          tagEditorProps={{
            onFinish: (t, { submitStatus }) =>
              submitStatus === 'success' && setRefreshIndex(Date.now()),
          }}
        />
      ),
    },
    {
      key: 'NewSaeVersion',
      title: intl('saenext.app-list.micro-app.Version'),
      dataIndex: 'NewSaeVersion',
      width: 120,
      disabled: !(enableNewSaeVersion || isMultipleNewAccount),
      cell: (value) => {
        const item = value ? AppVersionMap[value] : AppVersionMap['std'];
        return (
          <div className="flex items-center">
            <img src={item.icon} width={14} />
            <span style={{ marginLeft: 4 }}>{item.label}</span>
          </div>
        );
      },
    },
    {
      key: 'NamespaceId',
      title: intl('saenext.app-list.micro-app.Namespace'),
      dataIndex: 'NamespaceId',
      width: 150,
      cell: renderNamespace,
    },
    {
      key: 'ScaleRuleType',
      title: intl('saenext.app-list.micro-app.EnableStatusOfElasticPolicy'),
      dataIndex: 'ScaleRuleType',
      width: 150,
      cell: (value, index, record) => {
        const { ScaleRuleType, ScaleRuleEnabled, RunningInstances } = record;
        const status = !ScaleRuleType ? 'NotSet' : ScaleRuleEnabled ? 'Enabled' : 'NotEnabled';
        const StatusMap = {
          NotSet: {
            type: 'disabled',
            icon: '',
            text: intl('saenext.app-list.micro-app.NotSet'),
          },
          NotEnabled: {
            type: 'disabled',
            icon: 'stopcircle-fill',
            text: intl('saenext.app-list.micro-app.NotEnabled'),
          },
          Enabled: {
            type: 'success',
            icon: 'playcircle-fill',
            text: intl('saenext.app-list.micro-app.Enabled'),
          },
        };
        const { type, icon, text } = StatusMap[status];

        const { v2Link } = microAppLink(record.AppId, currentRegionId);

        return (
          <StatusIndicator type={type as StatusType} iconType={icon}>
            {text}
            {/* <If condition={ScaleRuleEnabled && ScaleRuleType !== 'timing'}>
            <span className="ml-s">
              {intl('saenext.app-list.micro-app.NumberOfInstancesRunninginstances', {
                RunningInstances: RunningInstances,
              })}
            </span>
            </If> */}
            <If condition={!ScaleRuleType}>
              <Link
                to={`${v2Link}?tab=hpa`}
                // @ts-ignore
                className="ml-s"
                data-spm-protocol="i"
              >
                {intl('saenext.app-list.micro-app.Configuration')}
              </Link>
            </If>
          </StatusIndicator>
        );
      },
    },
    {
      key: 'ScaleRuleEnabled',
      title: (
        <SortLabel
          dataSource={[
            { key: 'runnings', name: intl('saenext.app-list.micro-app.CurrentInstances') },
            { key: 'instances', name: intl('saenext.app-list.micro-app.TargetInstances') },
          ]}
          handler={handleInstanceSortChange}
          {...instancesSort}
        />
      ),

      dataIndex: 'ScaleRuleEnabled',
      width: 190,
      cell: (value, index, record) => {
        // if (record.ScaleRuleEnabled && record.ScaleRuleType && record.ScaleRuleType != 'timing') {
        //   return '--';
        // }
        return `${record.RunningInstances}/${record.Instances}`;
      },
    },
    {
      key: 'Spec',
      title: intl('saenext.app-list.micro-app.InstanceType'),
      dataIndex: 'Cpu',
      width: 140,
      cell: (value, index, record) => {
        const { Cpu, Mem } = record;
        const cpuNum = Cpu / 1000;
        const memNum = Mem / 1024;
        return intl('saenext.app-list.micro-app.CpunumCoreMemnumGb', {
          cpuNum: cpuNum,
          memNum: memNum,
        });
      },
    },
    {
      key: 'EnableIdle',
      dataIndex: 'EnableIdle',
      title: intl('saenext.app-list.micro-app.EnableIdle'),
      width: 160,
      disabled: !(isMultipleNewAccount || idle),
      cell: (value, index, record) => {
        return value
          ? intl('saenext.app-list.micro-app.Enabled.1')
          : intl('saenext.app-list.micro-app.NotEnabled.1');
      },
    },
    {
      key: 'AppDescription',
      title: intl('saenext.app-list.micro-app.ApplicationDescription'),
      dataIndex: 'AppDescription',
      width: 180,
      cell: (value, index, record) => {
        if (!value) {
          return '--';
        }
        return (
          <Truncate
            type="width"
            threshold={120}
            align="t"
            style={{ width: '100%', wordBreak: 'break-all' }}
          >
            <span style={{ width: '100%', wordBreak: 'break-all' }}>{value}</span>
          </Truncate>
        );
      },
    },
    {
      key: 'opt',
      title: intl('saenext.app-list.micro-app.Operation'),
      dataIndex: 'opt',
      width: 150,
      cell: (value, index, record) => {
        const NewSaeVersion = record?.NewSaeVersion || '';
        const { v1Link, v2Link, enable } = microAppLink(record.AppId, currentRegionId);
        const grayVisbile =
          !record.BaseAppId &&
          (((!NewSaeVersion || NewSaeVersion === 'std') &&
            record?.ProgrammingLanguage === 'java') ||
            NewSaeVersion === 'pro');
        const grayDisabled = !record.MseEnabled || record.MseNamespaceId === 'default';
        const versionKey = NewSaeVersion ? AppVersionMap[NewSaeVersion].key : 1;
        return (
          // @ts-ignore
          <Actions threshold={2} wrap={true}>
            <Link
              href={!enable && v1Link}
              to={enable && v2Link}
              // @ts-ignore
              target={!enable && '_blank'}
            >
              {intl('saenext.app-list.micro-app.Management')}
            </Link>
            <LinkButton
              key="copy"
              disabled={isInDebt}
              onClick={() => {
                const { AppId, AppName, IsStateful } = record;
                const statefulParam = stateful ? `&stateful=${IsStateful}` : '';
                history.push(
                  `/${currentRegionId}/app-list/${AppId}/copy-app/micro?name=${AppName}${statefulParam}`,
                );
              }}
            >
              {intl('saenext.app-list.micro-app.Copy')}
            </LinkButton>
            {(enableNewSaeVersion || isMultipleNewAccount) &&
              versionKey < 2 &&
              !record?.BaseAppId && (
                <ToolTipCondition
                  show={record?.MseNamespaceId === 'sae-pro'}
                  tip={intl('saenext.app-list.micro-app.TheCurrentMseVersionIs')}
                  align="l"
                >
                  <LinkButton
                    onClick={() => {
                      setCurAppInfo(record);
                      setUpgradeVisible(true);
                    }}
                    disabled={record?.MseNamespaceId === 'sae-pro'}
                  >
                    {intl('saenext.app-list.micro-app.Upgrade')}
                  </LinkButton>
                </ToolTipCondition>
              )}
            {(enableNewSaeVersion || isMultipleNewAccount) &&
              versionKey > 0 &&
              !record?.BaseAppId &&
              enableNewSaeVersionDowngrade && (
                <LinkButton
                  onClick={() => {
                    setCurAppInfo(record);
                    setDowngradeVisible(true);
                  }}
                >
                  {intl('saenext.app-list.micro-app.Downgrade')}
                </LinkButton>
              )}
            <LinkButton
              key="gray"
              visible={grayVisbile}
              disabled={grayDisabled}
              onClick={(e) => {
                const { AppId, AppName } = record;
                history.push(`/${currentRegionId}/app-list/${AppId}/gray-app?name=${AppName}`);
              }}
            >
              <ToolTipCondition
                show={grayDisabled}
                tip={
                  mseOpenStatus ? (
                    <a
                      href={`/${currentRegionId}/app-list/${record.AppId}/micro-app/msc-overview`}
                      target="_blank"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {intl('saenext.app-list.micro-app.EnableMseMicroserviceGovernanceFor')}
                    </a>
                  ) : (
                    <a
                      href={`${CachedData.confLink('feature:common-buy')}/?commodityCode=${isIntl ? 'mse_basic_public_intl' : 'mse_basic_public_cn'
                        }`}
                      target="_blank"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {intl('saenext.app-list.micro-app.UpgradeToMseMicroserviceGovernance')}
                    </a>
                  )
                }
              >
                {intl('saenext.app-list.micro-app.CreateAGrayscaleApplication')}
              </ToolTipCondition>
            </LinkButton>
            {!(
              CachedData.isSinSite() ||
              isSupportMultiVersions ||
              (!saeOldConsoleEnable && moment().isAfter('2025-05-31 23:59:59'))
            ) && (
                <a href={v1Link} data-spm-protocol="i" target="_blank">
                  {intl('saenext.app-list.micro-app.PreviousConsole')}
                </a>
              )}
          </Actions>
        );
      },
      lock: 'right',
    },
  ];

  const search = {
    children: (
      <TagSelectorIntl
        regionId={currentRegionId}
        resourceType={C.RESOURCE_TYPE}
        onTagSelect={onTagSelect}
      />
    ),

    afterFilterRender: tags.length > 0 && (
      <TagFilterIntl
        dataSource={tags}
        onChange={(v) => handleChangeTag(v)}
        hasClear
        onClear={() => handleChangeTag([])}
      />
    ),

    defaultDataIndex: 'appName',
    defaultSelectedDataIndex: 'appName',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.app-list.micro-app.ApplicationName'),
        dataIndex: C.FILTERS.APP_NAME,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.app-list.micro-app.EnterAnApplicationName'),
        },
        defaultValue: getParams(C.FILTERS.APP_NAME),
      },
      {
        label: intl('saenext.app-list.micro-app.GrayscaleApplicationName'),
        dataIndex: C.FILTERS.GRAY_APP_NAME,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.app-list.micro-app.EnterAGrayscaleApplicationName'),
        },
        defaultValue: getParams(C.FILTERS.GRAY_APP_NAME),
      },
      {
        label: intl('saenext.app-list.micro-app.ApplicationId'),
        dataIndex: C.FILTERS.APP_ID,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.app-list.micro-app.EnterAnApplicationId'),
        },
        defaultValue: getParams(C.FILTERS.APP_ID),
      },
      {
        label: 'SLB IP',
        dataIndex: C.FILTERS.SLB_IP,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.app-list.micro-app.EnterSlbIp'),
        },
        defaultValue: getParams(C.FILTERS.SLB_IP),
      },
      {
        label: intl('saenext.app-list.micro-app.InstanceIp'),
        dataIndex: C.FILTERS.INSTANCE_IP,
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.app-list.micro-app.EnterTheIpAddressOf'),
        },
        defaultValue: getParams(C.FILTERS.INSTANCE_IP),
      },
      // {
      //   label: '应用版本',
      //   dataIndex: 'versions',
      //   template: 'multiple',
      //   templateProps: {
      //     placeholder: '请选择应用版本',
      //     dataSource: Object.keys(AppVersionMap).map(key => ({
      //       label: AppVersionMap[key].label,
      //       value: key,
      //     })),
      //   },
      // },
    ],
  };

  const selectDisabled = getParams('namespaceId') === 'all' || LastChangeOrderRunning;

  const selection = () => {
    const [type, setType] = useState(EType.UPGRADE_ARMS);
    const isZero = selectedRowKeys.length === 0;
    const disabled = getParams('namespaceId') === 'all' || LastChangeOrderRunning;
    const cb = async () => {
      setRefreshIndex(Date.now());
      fetchDescribeNamespaceResources();
    };

    const openBatchSlide = (type: EType) => {
      setType(type);
      ref.current.toogleVisible();
    };

    const handleBatchEnableIdle = (type: EType) => {
      if (
        selectedRecords &&
        feature.liteIdle === false &&
        (selectedRecords.every((record) => record.NewSaeVersion === 'lite'))
      ) {
        Message.error(intl('saenext.app-list.micro-app.LightweightEditionDoesNotSupport'));
        return;
      } else {
        setType(type);
        ref.current.toogleVisible();
      }
    };

    return (
      <>
        <BatchOperation
          type={EType.STOP}
          disabled={isZero || disabled}
          selectedRowKeys={selectedRowKeys}
          cb={cb}
        />

        <BatchOperation
          type={EType.START}
          disabled={isZero || disabled}
          selectedRowKeys={selectedRowKeys}
          cb={cb}
        />

        <MenuButton
          label={intl('saenext.app-list.micro-app.MoreBatchOperations')}
          autoWidth={false}
        >
          <MenuButton.Item disabled={isZero || disabled} onClick={() => setTagDialogVisible(true)}>
            {intl('saenext.app-list.micro-app.SetLabelsInBatches')}
          </MenuButton.Item>
          <MenuButton.Item
            disabled={isZero || disabled}
            onClick={() => openBatchSlide(EType.UPGRADE_ARMS)}
          >
            <Balloon.Tooltip
              trigger={intl('saenext.app-list.micro-app.EnableArmsAdvancedMonitoringIn')}
            >
              {intl('saenext.app-list.micro-app.OnlyJavaApplicationsAreSupported')}
            </Balloon.Tooltip>
          </MenuButton.Item>
          <MenuButton.Item
            disabled={isZero || disabled}
            onClick={() => openBatchSlide(EType.DOWNGRADE_ARMS)}
          >
            <Balloon.Tooltip
              trigger={intl('saenext.app-list.micro-app.DisableArmsAdvancedMonitoringIn')}
            >
              {intl('saenext.app-list.micro-app.OnlyJavaApplicationsAreSupported')}
            </Balloon.Tooltip>
          </MenuButton.Item>

          {(canBatchEnableIdle) && <MenuButton.Item disabled={isZero || disabled} onClick={() => handleBatchEnableIdle(EType.BATCH_ENABLE_IDLE)}>
            {intl('saenext.app-list.micro-app.BatchEnableIdle')}
          </MenuButton.Item>}

          {(canBatchEnableIdle) && <MenuButton.Item disabled={isZero || disabled} onClick={() => handleBatchEnableIdle(EType.BATCH_DISABLE_IDLE)}>
            {intl('saenext.app-list.micro-app.BatchDisableIdle')}
          </MenuButton.Item>}
        </MenuButton>

        {!LastChangeOrderRunning && (
          <>
            <span style={{ marginLeft: 4 }}>
              {intl('saenext.app-list.micro-app.SelectedrowkeyslengthApplicationsSelected', {
                selectedRowKeysLength: selectedRowKeys.length,
              })}
            </span>
            {disabled && (
              <Balloon
                trigger={
                  <Icon
                    size="small"
                    style={{ marginLeft: 4, color: '#0070cc', lineHeight: '15px' }}
                    type="help"
                  />
                }
                closable={false}
                align="t"
              >
                {intl('saenext.app-list.micro-app.CurrentlyYouHaveSelectedAll')}
              </Balloon>
            )}
          </>
        )}

        <BatchTagEditorIntl
          regionId={currentRegionId}
          resources={_.map(selectedRowKeys, (primaryKey) => {
            const [appName, appId] = _.split(primaryKey, '&&');
            return {
              resourceId: appId,
              resourceName: appName,
            };
          })}
          resourceType={C.RESOURCE_TYPE}
          onCancel={(tags) => {
            setTagDialogVisible(false);
          }}
          onFinish={(t, { submitStatus }) => {
            submitStatus === 'success' && setRefreshIndex(Date.now());
            setTagDialogVisible(false);
          }}
          visible={tagDialogVisible}
        />

        <BatchOperation
          className="none"
          ref={ref}
          type={type}
          disabled={isZero || disabled}
          selectedRowKeys={selectedRowKeys}
          selectedRecords={selectedRecords}
          cb={cb}
        />
      </>
    );
  };

  const handleRowProps = (record) => {
    if (_.get(record, 'BaseAppId', false)) {
      return {
        style: { background: '#edeff0' },
      };
    }
  };

  // 无权限或者欠费
  const disabled = !auth || isInDebt;
  const renderOperationBtn = () => {
    const createBtn = (
      <Button
        name="createservlessapp"
        type="primary"
        disabled={disabled || NamespaceId === 'none'}
        onClick={() => {
          if (enableNewSaeVersion || isMultipleNewAccount) {
            setMultiVersionVisible(true);
          } else {
            handleNavToCreateApp('');
          }
        }}
        data-tracker="createservlessapp"
      >
        {intl('saenext.app-list.micro-app.CreateAnApplication')}
      </Button>
    );

    const yamlButton = !CachedData.isSinSite() && (
      <Button
        className="ml-s"
        disabled={disabled || NamespaceId === 'none'}
        onClick={() => handleNavToCreateApp('', '/yaml')}
      >
        {intl('saenext.app-list.micro-app.YamlDeploymentApplication')}
      </Button>
    );

    if (enableNewSaeVersion || isMultipleNewAccount) {
      // 老用户加白 or 多版新用户
      return (
        <>
          <Balloon
            title=""
            visible={multiVersionVisible}
            trigger={createBtn}
            triggerType={'click'}
            align="rt"
            popupStyle={{
              maxWidth: 860,
              padding: '30px 28px',
            }}
            arrowPointToCenter={false}
            onClose={() => {
              setMultiVersionVisible(false);
            }}
          >
            <MultiVersionSelect
              isMultipleNewAccount={isMultipleNewAccount}
              enableNewSaeVersion={enableNewSaeVersion}
              multipleNewAccountStdEnable={multipleNewAccountStdEnable}
              handleOk={(version) => {
                setMultiVersionVisible(false);
                handleNavToCreateApp(version);
              }}
            />
          </Balloon>
          {yamlButton}
        </>
      );
    } else {
      return (
        <>
          {createBtn}
          {yamlButton}
        </>
      );
    }
  };

  const operation = renderOperationBtn();

  const secondaryOperation = (
    <Tooltip
      trigger={
        <DownloadWrap>
          <Button type="normal" style={{ padding: '0 8px' }}>
            <Icon type="download" />
          </Button>
        </DownloadWrap>
      }
      align="t"
    >
      {intl('saenext.app-list.micro-app.ExportApplicationList')}
    </Tooltip>
  );

  const emptyContent = () => {
    if (auth) {
      return <RegionGuidance currentRegion={currentRegionId} />;
    }
    return (
      <p>
        {intl('saenext.app-list.micro-app.YourAccountDoesNotHave')}

        <a href={CachedData.confLink('help:sae:sae-permission-assistant')} target="_blank">
          {intl('saenext.app-list.micro-app.ApplyForPermission')}
        </a>
        {intl('saenext.app-list.micro-app.ThenViewAndOperate')}
      </p>
    );
  };

  const tipRender = () => {
    if (!disabled) return null;
    return (
      <Message
        type="error"
        className="mt-s"
        title={intl('saenext.app-list.micro-app.AccountExceptionReminder')}
      >
        <div className="text-line mt-s">
          {intl('saenext.app-list.micro-app.ThisMayBeBecauseYou')}
        </div>
      </Message>
    );
  };

  return (
    <>
      {tipRender()}
      <ChangeOrderStatus
        regionId={currentRegionId}
        namespaceId={NamespaceId}
        namespaceList={namespaceList}
        lastChangeOrderId={LastChangeOrderId}
        lastChangeOrderStatus={LastChangeOrderStatus}
      />

      <CndTable
        fetchData={fetchData}
        primaryKey="primaryKey"
        columns={columns as []}
        className="mtable-tree"
        refreshIndex={refreshIndex}
        selection={selection}
        showRefreshButton
        recordCurrent
        key={pageRefresh}
        uniqueKey={MicroListKey}
        isUseStorage
        useLocalStorage
        isTree
        style={{ marginTop: 8, display: 'block' }}
        pagination={{ pageSizeList: [5, 10, 20, 50, 100] }}
        search={search as ISearch}
        operation={operation}
        // @ts-ignore
        secondaryOperation={secondaryOperation}
        rowProps={handleRowProps}
        emptyContent={emptyContent()}
        rowSelection={{
          getProps: (record, index) => {
            if (_.isEmpty(NamespaceId)) {
              return { disabled: true };
            }
          },
          selectedRowKeys,
          onChange(selected, records) {
            setSelectedRowKeys(selected);
            setSelectedRecords(records);
          },
          titleProps: () => ({
            style: { display: 'flex' },
            disabled: selectDisabled,
            children: (
              <If condition={selectDisabled}>
                <Balloon
                  trigger={
                    <Icon
                      className="color-primary"
                      style={{ marginLeft: '-2px', position: 'absolute' }}
                      size="xs"
                      type="help"
                    />
                  }
                  closable={false}
                  align="t"
                >
                  {intl('saenext.app-list.micro-app.CurrentlyYouHaveSelectedAll')}
                </Balloon>
              </If>
            ),
          }),
        }}
      />

      {upgradeVisible && (
        <UpgradeVersion
          appInfo={curAppInfo}
          onClose={() => {
            setCurAppInfo(null);
            setUpgradeVisible(false);
          }}
          onOK={() => {
            setCurAppInfo(null);
            setUpgradeVisible(false);
            setRefreshIndex(Date.now());
          }}
        />
      )}

      {downgradeVisible && (
        <DowngradeVersion
          enableNewSaeVersionDowngrade={enableNewSaeVersionDowngrade}
          enableNewSaeVersion={enableNewSaeVersion}
          isMultipleNewAccount={isMultipleNewAccount}
          multipleNewAccountStdEnable={multipleNewAccountStdEnable}
          appInfo={curAppInfo}
          onClose={() => {
            setCurAppInfo(null);
            setDowngradeVisible(false);
          }}
          onOK={() => {
            setCurAppInfo(null);
            setDowngradeVisible(false);
            setRefreshIndex(Date.now());
          }}
        />
      )}
    </>
  );
};

export default MicroApp;
