import { isEmpty } from 'lodash';
import { getParams } from '~/utils/global';

export const getTags = () => {
  try {
    const tagKeys = getParams('tagKeys');
    const tagValues = getParams('tagValues');
    let newTagKeys = [];
    let newTagValues = [];
    if (tagKeys) {
      newTagKeys = JSON.parse(tagKeys);
    }
    if (tagValues) {
      newTagValues = JSON.parse(tagValues);
    }
    if (isEmpty(newTagKeys)) {
      return [];
    }
    return newTagKeys.map((item, index) => ({
      tagKey: item,
      tagValue: newTagValues[index],
    }));
  } catch (error) {
    return [];
  }
};
