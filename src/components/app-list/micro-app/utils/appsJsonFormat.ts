import { jsonStringify } from '~/utils/transfer-data';
import { intl } from '@ali/cnd';
import { forEach, isEmpty } from 'lodash';
import { getCookieByKeyName } from '~/utils/hacker';

const appsJsonFormat = (appList: any[]) => {
  const expandedAppList = expandWithChildren(appList);
  const formatData = expandedAppList.map((item) => {
    const data = {};
    forEach(columns, (column, index) => {
      const { dataIndex, title, cell } = column;
      const value = item[dataIndex];
      const customeValue = cell?.(value, index, item);
      data[column.title] = customeValue || value;
    });
    return data;
  });
  return formatData;
};

/**
 * 将每个元素的 children 插入到该元素之后。
 * @param array 原始数据数组
 * @returns 新的数组，其中每个元素的 children 已经插入到了该元素之后。
 */
const expandWithChildren = (array) => {
  const result = [];

  for (const item of array) {
    result.push(item);

    if (item.Children && Array.isArray(item.Children) && item.Children.length > 0) {
      const newPorps = {
        BaseAppName: item.AppName,
      };
      addNewPropsToArr(item.Children, newPorps);
      result.push(...item.Children);
    }
  }

  return result;
};

const addNewPropsToArr = (arr, props) => {
  // 遍历数组
  for (let item of arr) {
    Object.assign(item, props);
  }
};

const columns = [
  {
    title: intl('saenext.micro-app.basic-info.AppBaseInfo.ApplicationId'),
    dataIndex: 'AppId',
  },
  {
    title: intl('saenext.app-list.micro-app.ApplicationName'),
    dataIndex: 'AppName',
  },
  {
    title: intl('saenext.app-list.micro-app.Label'),
    dataIndex: 'Tags',
    cell: (value, index, record) => jsonStringify(value),
  },
  {
    title: intl('saenext.app-list.micro-app.Namespace'),
    dataIndex: 'NamespaceId',
    cell: (value, index, record) => {
      return record.NamespaceId || getCookieByKeyName('currentRegionId');
    },
  },
  {
    title: intl('saenext.app-list.micro-app.EnableStatusOfElasticPolicy'),
    dataIndex: 'ScaleRuleType',
    cell: (value, index, record) => {
      if (isEmpty(record.ScaleRuleType)) {
        return intl('saenext.app-list.micro-app.NotSet');
      } else if (!record.ScaleRuleEnabled) {
        return intl('saenext.app-list.micro-app.NotEnabled');
      } else if (record.ScaleRuleEnabled) {
        return intl('saenext.app-list.micro-app.Enabled');
      }
    },
  },
  {
    title: intl('saenext.app-list.micro-app.TargetInstances'),
    dataIndex: 'Instances',
  },
  {
    title: intl('saenext.app-list.micro-app.CurrentInstances'),
    dataIndex: 'RunningInstances',
  },
  {
    title: 'Cpu/' + intl('saenext.pages.overview.RecentCoreIndicator.Nuclear'),
    dataIndex: 'Cpu',
    cell: (value) => value / 1000,
  },
  {
    title: 'Mem/' + intl('saenext.pages.overview.RecentCoreIndicator.Gib'),
    dataIndex: 'Mem',
    cell: (value) => value / 1024,
  },
  // {
  //   title: intl('saenext.app-list.micro-app.InstanceType'),
  //   dataIndex: 'Cpu',
  //   cell: (value, index, record) => {
  //     const { Cpu, Mem } = record;
  //     const cpuNum = Cpu / 1000;
  //     const memNum = Mem / 1024;
  //     return intl('saenext.app-list.micro-app.CpunumCoreMemnumGb', {
  //       cpuNum: cpuNum,
  //       memNum: memNum,
  //     });
  //   },
  // },
  {
    title: intl('saenext.app-list.micro-app.ApplicationDescription'),
    dataIndex: 'AppDescription',
  },
  {
    title: intl('saenext.micro-app.utils.appsJsonFormat.BaselineApplication'),
    dataIndex: 'BaseAppName',
  },
];

export default appsJsonFormat;
