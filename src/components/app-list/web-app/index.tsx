import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useContext, useState, useEffect, useMemo, useRef } from 'react';
import { Link } from 'dva/router';
import {
  ConsoleContext,
  Copy,
  Button,
  Icon,
  Table,
  DateTime,
  Actions,
  RiskConfirm,
  Message,
  Balloon,
  DataFields,
  ErrorPrompt2 as errorPrompt,
  RegionGuidance
} from '@ali/cnd';
import { AUTH_TYPE_MAP } from '~/constants/application';
import { trackResource, AES_CONSTANT } from '~/tracker';
import _ from 'lodash';
import { getParams } from '~/utils/global';
import services from '~/services';
import CachedData from '~/cache/common';
import SortLabel from '../micro-app/components/SortLabel';
import { jsonParse } from '~/utils/transfer-data';
import WafLabel from '~/components/app-detail/web-app/basic-info/WafLabel';
import CndTable, { ISearch } from '@ali/cnd-table';
import ChangeOrderStatus from '../micro-app/components/ChangeOrderStatus';
import BatchOperation, { EType } from '../micro-app/components/BatchOperation';
import useOpenApi from '~/utils/useOpenApi';

const { LinkButton } = Actions;

export default ({ history, onChange = (isEmpty: boolean) => {}, namespaceList = [] }) => {
  const { region } = useContext(ConsoleContext);
  const currentRegionId = region.getCurrentRegionId();
  const namespaceId = getParams('namespaceId')
    ? getParams('namespaceId') === 'all' || getParams('namespaceId') === 'null'
      ? ''
      : getParams('namespaceId')
    : '';
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const [showRegionGuide, setShowRegionGuide] = useState(false);
  const [instancesSort, setInstancesSort] = useState<{
    OrderBy: string;
    Reverse: string | boolean;
  }>({ OrderBy: '', Reverse: '' });
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [regionsApps, setRegionsApps] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const {
    data: namespaceResources,
    run: fetchDescribeNamespaceResources,
    cancel: cancelDescribeNamespaceResources,
  } = useOpenApi(
    'serverless',
    'DescribeNamespaceResources',
    { NamespaceId: namespaceId },
    { manual: true, pollingInterval: 5000, disableErrorPrompt: true },
  );
  const { LastChangeOrderRunning, LastChangeOrderStatus, LastChangeOrderId } = _.get(
    namespaceResources,
    'Data',
    {},
  );

  useEffect(() => {
    if (namespaceId === 'none') return;
    if (_.isEmpty(namespaceId)) return cancelDescribeNamespaceResources();
    fetchDescribeNamespaceResources();
  }, [namespaceId]);

  const fetchData = async (params) => {
    // 未知命名空间
    if (namespaceId === 'none') {
      return {
        data: [],
        total: 0,
      };
    }

    let appList = await getWebApplications();
    appList = _.map(appList, (item) => {
      const { application } = item;
      return {
        ...application,
        status: item.status,
        primaryKey: `${application.applicationName}&&${application.applicationId}`,
      };
    });

    appList = _.orderBy(
      appList,
      [
        (item) => {
          const { lastModifiedTime } = item;
          return lastModifiedTime ? new Date(lastModifiedTime).getTime() : 0;
        },
      ],

      ['desc'],
    );

    if (!namespaceId && !appList.length) {
      // 当前region全部命名空间无应用
      setShowRegionGuide(true);
      getOtherRegionApplications();
    } else {
      setShowRegionGuide(false);
    }

    // onChange && onChange(appList.length === 0);
    window.localStorage.setItem('EXIST_APPS', `${appList.length}`);

    const { data } = filterApps(appList, params);

    return {
      data,
      total: appList.length,
    };
  };

  const getWebApplications = async (nextToken = '', data = []) => {
    const result =
      (await services.listWebApplicationsStatus({
        limit: 50,
        nextToken,
        namespaceID: namespaceId,
      })) || {};
    data.push(...(result.applications || []));
    if (result.nextToken) {
      await getWebApplications(result.nextToken, data);
    }
    return data;
  };

  const stopApplicationRequest = async ({ namespaceID, applicationID }) => {
    return await services.stopWebApplication(
      {
        NamespaceId: namespaceID,
        ApplicationId: applicationID,
      },
      (err, data, callback) => {
        if (err.code === 'ApplicationStopped') {
          return true;
        } else {
          callback();
        }
      },
    );
  };

  const deleteApplication = async ({ namespaceID, applicationID }) => {
    try {
      const result = await services.deleteWebApplication(
        {
          NamespaceId: namespaceID,
          ApplicationId: applicationID,
        },
        region.getCurrentRegionId(),
      );
      return result;
    } catch (error) {
      if (error.code === 'FetcherErrorRiskCancelled') {
        // 风控弹窗点取消
        return {};
      } else if (error.code === 'ProvisionConfigExist') {
        // 实例未完全停止，轮询删除
        return new Promise((resolve, reject) => {
          setTimeout(async () => {
            const result = await deleteApplication({ namespaceID, applicationID });
            resolve(result);
          }, 1000);
        });
      } else {
        // 通用控制台弹窗
        errorPrompt(error);
      }
    }
  };

  const deleteConfirm = async (record) => {
    const promise = new Promise(async (resolve, reject) => {
      const resultStop = await stopApplicationRequest(record);
      if (!resultStop) {
        Message.error(intl('saenext.app-list.web-app.StopFailed'));
        reject();
        return;
      }

      const result = await deleteApplication(record);
      if (result?.Data?.ApplicationId) {
        Message.success(intl('saenext.app-list.web-app.DeletedSuccessfully'));
        const existApps = parseInt(window.localStorage.getItem('EXIST_APPS')) || 0;
        trackResource({
          behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.DELETE,
          stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
          existApps,
        });
        setRefreshIndex(Date.now());
        resolve(result);
      } else {
        reject();
      }
    });
    return promise;
  };

  const getOtherRegionApplications = async () => {
    const { regions = [] } = _.get(window, 'ALIYUN_CONSOLE_GLOBAL', {}) as any;

    const otherRegions = _.filter(regions, (item) => item.id !== currentRegionId);

    const regionsApps = await Promise.all(
      _.map(otherRegions, async (item) => {
        const { applications = [], nextToken } =
          (await services.listWebApplications({ limit: 10 }, { region: item.id })) || {};

        return {
          id: item.id,
          count: nextToken ? '10+' : applications.length,
        };
      }),
    );
    const regionsExistApp = _.filter(regionsApps, (item) => item.count);
    setRegionsApps(regionsExistApp);
  };

  const handleInstanceSortChange = (key, type) => {
    const _reverse = type === '' ? '' : type === 'asc';
    const _key = type === '' ? '' : key;
    setInstancesSort({ OrderBy: _key, Reverse: _reverse });
    setRefreshIndex(Date.now());
  };

  const filterApps = (data, params) => {
    const { current, pageSize, applicationName } = params;
    let _applications = [...data];
    if (applicationName) {
      _applications = _.filter(data, (item) => _.includes(item.applicationName, applicationName));
    }

    if (instancesSort.OrderBy) {
      if (instancesSort.Reverse !== '') {
        _applications.sort((a, b) => {
          const aValue = _.get(a, instancesSort.OrderBy);
          const bValue = _.get(b, instancesSort.OrderBy);
          if (aValue === bValue) return 0;
          return instancesSort.Reverse === true
            ? aValue > bValue
              ? 1
              : -1
            : aValue > bValue
            ? -1
            : 1;
        });
      }
    }

    _applications = _applications.slice((current - 1) * pageSize, current * pageSize);

    return {
      data: _applications,
      total: _applications.length,
    };
  };

  const columns = [
    {
      title: intl('saenext.app-list.web-app.ApplicationName'),
      dataIndex: 'applicationName',
      width: 180,
      lock: 'left',
      cell: (value, index, record) => {
        const { applicationID } = record;
        // 优先取id
        const _value = applicationID || value;
        const namespaceId = record.namespaceID || getParams('namespaceId');
        return (
          <Link
            to={`/${currentRegionId}/app-list/${_value}/web-app/base?namespaceId=${namespaceId}&name=${value}`}
          >
            <Copy text={value}>{value}</Copy>
          </Link>
        );
      },
    },
    {
      title: intl('saenext.app-list.web-app.PublicAccessAddress'),
      dataIndex: 'urlInternet',
      width: 260,
      cell: (value, index, record) => {
        if (!value) return '-';
        const { customDomainName, applicationID, applicationName } = record;
        const customDomains =
          jsonParse(customDomainName) ||
          (customDomainName &&
            customDomainName?.split(',')?.map((item) => ({
              domainName: item,
            })));
        const renderLink = (domian) => {
          const { domainName: link, wafConfig } = domian;
          return (
            <span className="align-center">
              <a href={'http://' + link} target="_blank">
                <Copy text={link} style={{ wordBreak: 'break-all' }}>
                  {link}
                </Copy>
              </a>
              {wafConfig && (
                <WafLabel
                  record={domian}
                  namespaceId={namespaceId}
                  refresh={() => setRefreshIndex(Date.now())}
                />
              )}
            </span>
          );
        };

        return (
          <ul className="list-disc">
            <li style={{ wordBreak: 'break-all' }}>
              <a href={value} target="_blank">
                <Copy text={value}>{value}</Copy>
              </a>
            </li>
            {!_.isEmpty(customDomains) && (
              <li>
                <span className="flex">
                  {renderLink(customDomains[0])}

                  {customDomains.length > 1 && (
                    <Balloon
                      trigger={
                        <span className="color-primary ml-s">
                          {intl('saenext.app-list.web-app.More')}
                        </span>
                      }
                      alignEdge
                      closable={false}
                    >
                      <ul className="list-disc">{customDomains.map(renderLink)}</ul>
                    </Balloon>
                  )}
                </span>
              </li>
            )}
          </ul>
        );
      },
    },
    {
      title: intl('saenext.app-list.web-app.Namespace'),
      dataIndex: 'namespaceID',
      width: 180,
      cell: (value, index, record) => {
        if (!value) return '-';
        const namespace = _.find(namespaceList, (item) => item.NamespaceId === namespaceId);
        const { NamespaceName = '' } = namespace || {};
        const { namespaceName } = record;

        if (!namespace) return namespaceName;

        const trigger = (
          <a href={`/${currentRegionId}/namespace/${namespaceId}/base/?name=${NamespaceName}`}>
            {NamespaceName}
          </a>
        );

        return (
          <Balloon.Tooltip trigger={trigger}>
            <DataFields
              dataSource={namespace}
              items={[
                {
                  dataIndex: 'NamespaceName',
                  label: intl('saenext.app-list.web-app.NamespaceName'),
                  span: 24,
                },
                {
                  dataIndex: 'NamespaceId',
                  label: intl('saenext.app-list.web-app.NamespaceId'),
                  span: 24,
                },
                {
                  dataIndex: 'VpcId',
                  label: intl('saenext.app-list.web-app.Vpc'),
                  span: 24,
                },
              ]}
            />
          </Balloon.Tooltip>
        );
      },
    },
    {
      title: (
        <>
          <SortLabel
            dataSource={[
              {
                key: 'status.instanceCount',
                name: intl('saenext.app-list.web-app.CurrentInstances'),
              },
            ]}
            handler={handleInstanceSortChange}
            {...instancesSort}
          />

          <span className="ml-xs mr-xs">/</span>
          {intl('saenext.app-list.web-app.MinimumAndMaximumNumberOf')}
        </>
      ),

      dataIndex: 'status',
      width: 200,
      cell: (value) => {
        const {
          instanceCount = 0,
          scaleConfig: { minimumInstanceCount = 0, maximumInstanceCount = 0 } = {},
        } = value || {};
        return `${instanceCount} / ${minimumInstanceCount} - ${maximumInstanceCount}`;
      },
    },
    {
      title: intl('saenext.app-list.web-app.EntranceTypeOfData'),
      dataIndex: 'httpTriggerConfig.disableURLInternet',
      width: 140,
      cell: (value) => {
        return !value ? (
          <div>{intl('saenext.app-list.web-app.AllInternetAndIntranet')}</div>
        ) : (
          <div>{intl('saenext.app-list.web-app.Intranet')}</div>
        );
      },
    },
    {
      title: intl('saenext.app-list.web-app.CallAuthentication'),
      dataIndex: 'httpTriggerConfig.authType',
      width: 140,
      cell: (value) => <div>{AUTH_TYPE_MAP[value]}</div>,
    },
    {
      title: intl('saenext.app-list.web-app.CreationTimeChangeTime'),
      dataIndex: 'createdTime',
      width: 200,
      cell: (value, index, record) => (
        <>
          <DateTime value={value} /> <br />
          <DateTime value={record.lastModifiedTime} />
        </>
      ),
    },
    {
      title: intl('saenext.app-list.web-app.Operation'),
      dataIndex: 'applicationName',
      width: 120,
      lock: 'right',
      cell: (value, index, record) => (
        <>
          <Actions>
            <RiskConfirm
              title={intl('saenext.app-list.web-app.DeleteApplicationValue', { value: value })}
              message={intl('saenext.app-list.web-app.AreYouSureYouWant', {
                value: value,
              })}
              confirmKey={value}
              confirmPlaceholer={intl('saenext.app-list.web-app.EnterAnApplicationName')}
              onConfirm={async () => deleteConfirm(record)}
            >
              <LinkButton>{intl('saenext.app-list.web-app.Delete')}</LinkButton>
            </RiskConfirm>
            <LinkButton
              disabled={isInDebt}
              onClick={() => {
                const { applicationID, applicationName } = record;
                history.push(
                  `/${currentRegionId}/app-list/${applicationID}/copy-app/web?name=${applicationName}`,
                );
              }}
            >
              {intl('saenext.app-list.web-app.Copy')}
            </LinkButton>
          </Actions>
        </>
      ),
    },
  ];

  const search = {
    defaultDataIndex: 'applicationName',
    defaultSelectedDataIndex: 'applicationName',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.app-list.web-app.ApplicationName'),
        dataIndex: 'applicationName',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.app-list.web-app.EnterAnApplicationName'),
        },
        defaultValue: getParams('applicationName'),
      },
    ],
  };

  const selection = () => {
    const isZero = selectedRowKeys.length === 0;
    const disabled = getParams('namespaceId') === 'all' || LastChangeOrderRunning;
    const cb = async () => {
      setRefreshIndex(Date.now());
      fetchDescribeNamespaceResources();
    };
    return (
      <>
        <BatchOperation
          type={EType.STOP}
          disabled={isZero || disabled}
          selectedRowKeys={selectedRowKeys}
          cb={cb}
        />

        <BatchOperation
          type={EType.START}
          disabled={isZero || disabled}
          selectedRowKeys={selectedRowKeys}
          cb={cb}
        />

        {!LastChangeOrderRunning && (
          <>
            <span style={{ marginLeft: 4 }}>
              {intl('saenext.app-list.web-app.SelectedrowkeyslengthApplicationsSelected', {
                selectedRowKeysLength: selectedRowKeys.length,
              })}
            </span>
            {disabled && (
              <Balloon
                trigger={
                  <Icon
                    size="small"
                    style={{ marginLeft: 4, color: '#0070cc', lineHeight: '15px' }}
                    type="help"
                  />
                }
                closable={false}
                align="t"
              >
                {intl('saenext.app-list.web-app.CurrentlyYouHaveSelectedAll')}
              </Balloon>
            )}
          </>
        )}
      </>
    );
  };

  const operation = (
    <Button
      type="primary"
      disabled={isInDebt || namespaceId === 'none'}
      onClick={() => {
        trackResource({
          behavior: AES_CONSTANT.RESOURCE_BEHAVIOR_TYPE.CREATE,
          stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
          existApps: parseInt(window.localStorage.getItem('EXIST_APPS')),
        });
        const search = namespaceId && namespaceId !== 'none' ? `namespaceId=${namespaceId}` : '';
        history.push(`/${currentRegionId}/create-app/web?${search}`);
      }}
    >
      {intl('saenext.app-list.web-app.CreateAnApplication')}
    </Button>
  );

  const emptyContent = () => {
    if (showRegionGuide) {
      return (
        <RegionGuidance
          currentRegion={currentRegionId}
          onRegionClick={(regionId) => {
            history.push(`/${regionId}/app-list/web`);
          }}
          regionList={regionsApps}
        />
      );
    }
    return <p>{intl('saenext.app-list.web-app.NoData')}</p>;
  };

  const tipRender = () => {
    if (!isInDebt) return null;
    return (
      <Message
        type="error"
        className="mt-s"
        title={intl('saenext.app-list.web-app.AccountExceptionReminder')}
      >
        <div className="text-line mt-s">{intl('saenext.app-list.web-app.ThisMayBeBecauseYou')}</div>
      </Message>
    );
  };

  return (
    <>
      {tipRender()}
      <ChangeOrderStatus
        regionId={currentRegionId}
        namespaceId={namespaceId}
        namespaceList={namespaceList}
        lastChangeOrderId={LastChangeOrderId}
        lastChangeOrderStatus={LastChangeOrderStatus}
      />

      <CndTable
        style={{ marginTop: 8, display: 'block' }}
        fetchData={fetchData}
        primaryKey="primaryKey"
        columns={columns as []}
        refreshIndex={refreshIndex}
        selection={selection}
        showRefreshButton
        recordCurrent
        pagination={{
          pageSizeList: [10, 20, 50, 100],
        }}
        search={search as ISearch}
        operation={operation}
        emptyContent={emptyContent()}
        rowSelection={{
          getProps: (record, index) => {
            if (_.isEmpty(namespaceId)) {
              return { disabled: true };
            }
          },
          selectedRowKeys,
          onChange(selected, records) {
            setSelectedRowKeys(selected);
          },
        }}
      />
    </>
  );
};
