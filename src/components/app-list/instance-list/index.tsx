import React, { useState, useEffect, useContext } from 'react';
import {
  Actions,
  Balloon,
  Button,
  ConsoleContext,
  Copy,
  Dialog,
  intl,
  Link,
  LinkButton,
  Message,
  ToolTipCondition,
  Truncate,
  CndTable,
  Menu,
  Dropdown,
} from '@ali/cnd';
import _, { isEmpty, get, noop, map } from 'lodash';
import { getParams } from '~/utils/global';
import services from '~/services';
import moment from 'moment';
import If from '~/components/shared/If';
import CachedData from '~/cache/common';
import { confFeature } from '@alicloud/console-one-conf';
import InstanceStatus from '~/components/app-detail/micro-app/basic-info/InstanceGroup/InstanceStatus';
import SlideModalWrap from '~/components/shared/SlideModalWrap';
import RealtimeLog from '~/components/app-detail/micro-app/log-control/log-control/RealtimeLog';
import EventList from '~/components/shared/EventList';
import WebshellContent from '~/components/app-detail/micro-app/basic-info/InstanceDetail/WebshellContent';
import InstanceDebugDialog from '~/components/app-detail/micro-app/basic-info/InstanceGroup/InstanceDebugDialog';
import './index.less';
const { Tooltip } = Balloon;

const statusOptions = [
  {
    key: '',
    label: intl('saenext.app-list.instance-list.All'),
  },
  {
    key: 'Creating',
    label: intl('saenext.app-list.instance-list.Creating'),
  },
  {
    key: 'Running',
    label: intl('saenext.app-list.instance-list.Running'),
  },
  {
    key: 'Deleting',
    label: intl('saenext.app-list.instance-list.Deleting'),
  },
  {
    key: 'Completed',
    label: intl('saenext.app-list.instance-list.Ended'),
  },
  {
    key: 'Error',
    label: intl('saenext.app-list.instance-list.Exception'),
  },
];

const InstanceList = (props) => {
  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();

  const isInstanceWhiteList = get(
    window,
    'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS.isInstanceWhiteList',
  );
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [curInstanceContainerStatus, setCurInstanceContainerStatus] = useState('');
  const NamespaceId = getParams('namespaceId')
    ? getParams('namespaceId') === 'all' || getParams('namespaceId') === 'null'
      ? ''
      : getParams('namespaceId')
    : '';

  useEffect(() => {
    setRefreshIndex(Date.now());
  }, [NamespaceId, curInstanceContainerStatus]);

  const fetchData = async (params) => {
    // 未知命名空间
    if (NamespaceId === 'none') {
      return {
        data: [],
        total: 0,
      };
    }
    const { current, pageSize, ...restParams } = params;
    const res = await services.DescribeNamespaceInstances({
      params: {
        NamespaceId,
        CurrentPage: current,
        PageSize: pageSize,
        InstanceContainerStatus: curInstanceContainerStatus,
        ...restParams,
      },
    });
    let list = get(res, 'Data.Instances', []);
    const vSwitchIds = Array.from(new Set(map(list, (item) => item.VSwitchId)));
    const vSwitchInfos = await Promise.all(
      vSwitchIds.map((item) => {
        return services.DescribeVSwitchAttributes({
          params: {
            VSwitchId: item,
          },
        });
      }),
    );
    let vSwitchIdMap = {};
    map(vSwitchInfos, (item) => {
      Reflect.set(vSwitchIdMap, item.VSwitchId, item);
    });

    list = list.map((item, index) => ({
      ...item,
      primaryKey: `${item.InstanceId}&&${item.AppId}`,
      VSwitchName: vSwitchIdMap[item.VSwitchId]?.VSwitchName || '',
      ZoneId: vSwitchIdMap[item.VSwitchId]?.ZoneId || '',
    }));

    return {
      data: list,
      total: get(res, 'Data.TotalSize', 0),
    };
  };
  const handleRestartInstance = (type, instanceId?, appId?) => {
    let InstanceIds = '';
    let AppId = '';
    if (type === 'batch') {
      const instanceIds = selectedRowKeys.map((item) => item.split('&&')[0]);
      const appIds = selectedRowKeys.map((item) => item.split('&&')[1]);
      if (new Set(appIds).size !== 1) {
        return Message.error(intl('saenext.app-list.instance-list.SelectAnInstanceOfThe'));
      }
      InstanceIds = instanceIds.join(',');
      AppId = appIds[0];
    } else {
      InstanceIds = instanceId;
      AppId = appId;
    }
    Dialog.confirm({
      title: intl('saenext.basic-info.InstanceGroup.InstanceList.RestartTheInstance'),
      content: intl('saenext.basic-info.InstanceGroup.InstanceList.AreYouSureYouWant'),
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res =
            (await services.restartMicroInstances({
              AppId,
              InstanceIds,
            })) || {};
          if (res?.Success) {
            resolve(true);
            Message.success(intl('saenext.basic-info.InstanceGroup.InstanceList.RestartSucceeded'));
          }
        });
      },
    });
  };

  const handleDeleteInstance = (appId, InstanceIds) => {
    Dialog.confirm({
      title: intl('saenext.basic-info.InstanceGroup.InstanceList.DeleteAnInstance'),
      content: intl('saenext.basic-info.InstanceGroup.InstanceList.AfterDeletingTheInstanceThe'),
      onOk: () => {
        return new Promise(async (resolve, reject) => {
          const res =
            (await services.deleteMicroInstances({
              AppId: appId,
              InstanceIds,
            })) || {};
          if (res?.Success) {
            resolve(true);
            Message.success(
              intl('saenext.basic-info.InstanceGroup.InstanceList.DeletedSuccessfully'),
            );
          }
        });
      },
    });
  };
  const selection = () => {
    return (
      <>
        <Button
          type="primary"
          disabled={selectedRowKeys.length === 0}
          onClick={() => handleRestartInstance('batch')}
        >
          {intl('saenext.basic-info.InstanceGroup.InstanceList.RestartInstancesInBatches')}
        </Button>
        <span className="ml">
          {intl('saenext.basic-info.InstanceGroup.InstanceList.CurrentlyYouHaveSelectedA', {
            selectedRowKeysLength: selectedRowKeys.length,
          })}
        </span>
      </>
    );
  };

  const search = {
    defaultDataIndex: 'InstanceId',
    defaultSelectedDataIndex: 'InstanceId',
    onlySupportOne: true,
    options: [
      {
        label: intl('saenext.basic-info.InstanceGroup.InstanceList.InstanceId'),
        dataIndex: 'InstanceId',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.basic-info.InstanceGroup.InstanceList.SearchByInstanceId'),
        },
      },
      {
        label: intl('saenext.app-list.instance-list.ApplicationName'),
        dataIndex: 'AppName',
        template: 'input',
        templateProps: {
          placeholder: intl('saenext.app-list.instance-list.EnterAnApplicationName'),
        },
      },
    ],
  };
  const renderStatusColumnTitle = () => {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div>{intl('saenext.basic-info.InstanceGroup.InstanceList.RunningStatus')}</div>
        <Dropdown
          trigger={
            <svg
              viewBox="64 64 896 896"
              focusable="false"
              data-icon="filter"
              width="1em"
              height="1em"
              fill={curInstanceContainerStatus ? '#0064c8' : '#bfbfbf'}
              aria-hidden="true"
              data-spm-anchor-id="5176.ecscore_server.0.i2.11bd4df5LLZNJO"
            >
              <path
                d="M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"
                data-spm-anchor-id="5176.ecscore_server.0.i0.11bd4df5LLZNJO"
              ></path>
            </svg>
          }
          triggerType={['click']}
        >
          <Menu
            className="status-menu"
            onItemClick={(k) => {
              setCurInstanceContainerStatus(k);
            }}
          >
            {map(statusOptions, (item) => {
              return (
                <Menu.Item
                  key={item.key}
                  className={curInstanceContainerStatus == item.key ? 'active' : ''}
                >
                  {item.label}
                </Menu.Item>
              );
            })}
          </Menu>
        </Dropdown>
      </div>
    );
  };
  return (
    <CndTable
      primaryKey="primaryKey"
      fetchData={fetchData}
      search={search}
      showRefreshButton
      refreshIndex={refreshIndex}
      rowSelection={{
        getProps: (record, index) => {
          if (
            (!isInstanceWhiteList && record.InstanceContainerStatus !== 'Running') ||
            !NamespaceId
          ) {
            return { disabled: true };
          }
        },
        selectedRowKeys,
        onChange(selected, records) {
          setSelectedRowKeys(selected);
        },
      }}
      selection={selection}
    >
      <CndTable.Column
        title={intl('saenext.basic-info.InstanceGroup.InstanceList.InstanceId')}
        lock="left"
        dataIndex="InstanceId"
        width={240}
        cell={(val, index, record) => {
          return (
            <>
              <Link
                to={`/${regionId}/app-list/${record?.AppId}/micro-app/instance?groupId=${record?.GroupId}&instanceId=${val}`}
              >
                <Truncate
                  style={{ display: 'inline-block' }}
                  position="middle"
                  threshold={150}
                  type="width"
                >
                  {val}
                </Truncate>
              </Link>

              <Copy text={val} showIcon></Copy>
            </>
          );
        }}
      />

      <CndTable.Column
        title={renderStatusColumnTitle()}
        dataIndex="InstanceContainerStatus"
        width={180}
        cell={(val, index, record) => {
          return (
            <InstanceStatus
              appId={record?.AppId}
              associateEip={record?.AssociateEip}
              groupId={record?.GroupId}
              refresh={() => {
                setRefreshIndex(Date.now());
              }}
              record={record}
              showRestart={confFeature('instance_restarts')}
            />
          );
        }}
      />

      <CndTable.Column
        title={intl('saenext.app-list.instance-list.Application')}
        dataIndex="AppName"
        width={200}
        cell={(val, index, record) => {
          return (
            <Copy text={val}>
              <Link to={`/${regionId}/app-list/${record.AppId}/micro-app/base`}>{val}</Link>
            </Copy>
          );
        }}
      />

      <CndTable.Column
        title="vSwitch"
        dataIndex="VSwitchId"
        width={220}
        cell={(val, index, record) => {
          const zoneName = _.toUpper(_.last(record.ZoneId as string));
          return (
            <Copy text={val}>
              <a
                href={`${CachedData.confLink('feature:vpc:url')}/vpc/${regionId}/switches/${val}`}
                target="_blank"
              >
                {intl(
                  'saenext.basic-info.InstanceGroup.InstanceList.ZoneZonenameTargetvswitchvswitchname',
                  { zoneName: zoneName, targetVSwitchVSwitchName: record.VSwitchName },
                )}
              </a>
            </Copy>
          );
        }}
      />

      <CndTable.Column
        title={intl('saenext.basic-info.InstanceGroup.InstanceList.IpAddress')}
        dataIndex="InstanceContainerIp"
        width={180}
        cell={(val, index, record) => {
          const { InstanceContainerIp, Eip } = record;
          return (
            <>
              <If condition={Eip}>
                <p>{intl('saenext.basic-info.InstanceGroup.InstanceList.EipBomb', { Eip: Eip })}</p>
              </If>
              {InstanceContainerIp && (
                <p>
                  {intl(
                    'saenext.basic-info.InstanceGroup.InstanceList.InstancecontaineripPrivate',
                    { InstanceContainerIp: InstanceContainerIp },
                  )}
                </p>
              )}
            </>
          );
        }}
      />

      <CndTable.Column
        title={`${intl('saenext.basic-info.InstanceGroup.InstanceList.Image')}/${intl(
          'saenext.basic-info.InstanceGroup.InstanceList.Version',
        )}`}
        dataIndex="ImageUrl"
        width={240}
        cell={(val, index, record) => {
          const { PackageType, ImageUrl, PackageVersion } = record;
          if (PackageType === 'Image' || _.includes(PackageType, 'IMAGE_PHP')) {
            const reg = /\/.+\/.+$/;
            const url = ImageUrl.match(reg);
            return <Tooltip trigger={`...${url || ImageUrl}`}>{ImageUrl}</Tooltip>;
          } else {
            return PackageVersion;
          }
        }}
      />

      <CndTable.Column
        title={intl('saenext.basic-info.InstanceGroup.InstanceList.Runtime')}
        dataIndex="CreateTimeStamp"
        width={100}
        cell={(val, index, record) => {
          if (!val) {
            return '--';
          } else {
            return moment(val).fromNow(true);
          }
        }}
      />

      <CndTable.Column
        title={intl('saenext.basic-info.InstanceGroup.InstanceList.Operation')}
        width={220}
        lock="right"
        cell={(val, index, record) => {
          const { InstanceContainerStatus, DebugStatus, InstanceId } = record;
          return (
            <Actions threshold={3}>
              <SlideModalWrap
                title={intl('saenext.basic-info.InstanceGroup.InstanceList.RealTimeLog')}
                width={1200}
                onMaskClick={noop}
                onClose={noop}
                content={
                  <RealtimeLog
                    regionId={regionId}
                    appConfig={{
                      ProgrammingLanguage: record?.ProgrammingLanguage,
                    }}
                    applicationId={record?.AppId}
                    podId={InstanceId}
                  />
                }
              >
                <LinkButton>
                  {intl('saenext.basic-info.InstanceGroup.InstanceList.RealTimeLog')}
                </LinkButton>
              </SlideModalWrap>
              <SlideModalWrap
                title={'Webshell'}
                width={1200}
                onMaskClick={noop}
                onClose={noop}
                content={
                  <WebshellContent
                    regionId={regionId}
                    appId={record?.AppId}
                    groupId={record?.GroupId}
                    instanceId={InstanceId}
                    sidecarId={''}
                  />
                }
              >
                <LinkButton
                  disabled={
                    !(
                      InstanceContainerStatus === 'Running' ||
                      InstanceContainerStatus === 'Terminating'
                    )
                  }
                >
                  Webshell
                </LinkButton>
              </SlideModalWrap>
              <SlideModalWrap
                title={intl('saenext.basic-info.InstanceGroup.InstanceList.Event')}
                width={1200}
                onMaskClick={noop}
                onClose={noop}
                content={
                  <EventList
                    regionId={regionId}
                    namespaceId={NamespaceId}
                    appId={record?.AppId}
                    filters={{
                      ObjectKind: 'Pod',
                      ObjectName: InstanceId,
                    }}
                  />
                }
              >
                <LinkButton>
                  {intl('saenext.basic-info.InstanceGroup.InstanceList.Event')}
                </LinkButton>
              </SlideModalWrap>
              <ToolTipCondition
                show={!isInstanceWhiteList && InstanceContainerStatus !== 'Running'}
                tip={
                  InstanceContainerStatus !== 'Running'
                    ? intl('saenext.basic-info.InstanceGroup.InstanceList.TheInstanceIsInA')
                    : intl(
                        'saenext.basic-info.InstanceGroup.InstanceList.TheApplicationIsChangingPlease',
                      )
                }
                align="t"
              >
                <LinkButton
                  type="primary"
                  text
                  disabled={!isInstanceWhiteList && InstanceContainerStatus !== 'Running'}
                  onClick={() => handleRestartInstance('single', record.InstanceId, record?.AppId)}
                >
                  {intl('saenext.basic-info.InstanceGroup.InstanceList.Restart')}
                </LinkButton>
              </ToolTipCondition>
              <ToolTipCondition
                show={!isInstanceWhiteList && DebugStatus}
                tip={
                  DebugStatus
                    ? intl(
                        'saenext.basic-info.InstanceGroup.InstanceList.OneClickDebuggingIsEnabled',
                      )
                    : intl(
                        'saenext.basic-info.InstanceGroup.InstanceList.TheApplicationIsChangingPlease',
                      )
                }
                align="t"
              >
                <InstanceDebugDialog
                  appId={record?.AppId}
                  groupId={record?.GroupId}
                  instanceId={InstanceId}
                  refresh={() => {
                    setRefreshIndex(Date.now());
                  }}
                >
                  <LinkButton type="primary" text disabled={!isInstanceWhiteList && DebugStatus}>
                    {intl('saenext.basic-info.InstanceGroup.InstanceList.OneClickDebugging')}
                  </LinkButton>
                </InstanceDebugDialog>
              </ToolTipCondition>
              <LinkButton
                type="primary"
                text
                onClick={() => handleDeleteInstance(record?.AppId, record.InstanceId)}
              >
                {intl('saenext.basic-info.InstanceGroup.InstanceList.Delete')}
              </LinkButton>
            </Actions>
          );
        }}
      />
    </CndTable>
  );
};

export default InstanceList;
