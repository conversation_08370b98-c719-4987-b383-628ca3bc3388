import { intl } from '@ali/cnd';
import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from 'react';
import { Loading, SlidePanel, Message, Dialog, Radio, Button, Icon } from '@ali/cnd';
import { get, isEmpty } from 'lodash';
import services from '~/services';
import { getParams, unshiftZero } from '~/utils/global';
import WebPackageConfig from '~/components/shared/DeploySelectorField/web-app/PackageConfig';
import ProcessField from './version-create/ProcessField';
import { DEPLOY_TYPE, PIPELINE_STATUS, PACKAGE_TYPE } from '~/constants/application';

const RadioGroup = Radio.Group;
type Props = {
  history: any;
  urlSearch: any;
  regionId: string;
  appVersionId: string;
  applicationID: string;
  setRefreshIndex: (date: number) => void;
};

export default forwardRef((props: Props, ref) => {
  const applicationName = getParams('name');
  const { history, urlSearch, regionId, appVersionId, applicationID, setRefreshIndex } = props;
  const refPackage = useRef(null);
  const [showing, setShowing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const params = useRef({});
  const packageParams = useRef({});
  const [visible, setVisible] = useState(false);
  const [effectiveImmediately, setEffectiveImmediately] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);
  // 构建数据
  const [buildData, setBuildData] = useState({});
  const [isUpdateSuccess, setIsUpdateSuccess] = useState(false);
  const [pipelineRunId, setPipelineRunId] = useState('');
  const [buildStatus, setBuildStatus] = useState(PIPELINE_STATUS.FAIL);

  useEffect(() => {
    if (!showing || isEmpty(appVersionId)) return;
    initWebPackageData();
  }, [appVersionId, showing]);

  useImperativeHandle(ref, () => ({
    show: () => setShowing(true),
    hide: () => setShowing(false),
  }));

  const initWebPackageData = async () => {
    setIsLoading(true);
    const data = await services.getAppVersionConfig({
      applicationID,
      applicationName,
      qualifier: appVersionId,
    });
    params.current = {
      ...data,
      instanceConcurrency: data?.instanceConcurrency,
      caPort: data?.caPort,
      cpu: data?.cpu,
      memorySize: data?.memorySize,
      timeout: data?.timeout,
      // imageConfig: data?.imageConfig,
      // 更新传null 表示之前配置不变, 传{}表示要去掉之前的配置
      nasConfig: get(data, 'nasConfig', null),
      ossMountConfig: get(data, 'ossMountConfig', null),
      environmentVariables: get(data, 'environmentVariables', null),
      logConfig: get(data, 'logConfig', null),
      slsConfig: get(data, 'slsConfig', null),
      startupProbe: get(data, 'startupProbe', null),
      livenessProbe: get(data, 'livenessProbe', null),
    };

    // 对应版本的代码包信息
    const res = await services.DescribeBuildPipelineRun({
      VersionId: appVersionId,
      ApplicationId: applicationID,
      ApplicationName: applicationName,
    });
    const { Data = {} } = res;
    const { PackageConfig = {}, BuildConfig = {} } = Data;
    setBuildData({
      PackageType: PackageConfig?.PackageType,
      UploadType: 'upload',
      PackageUrl: PackageConfig?.PackageUrl,
      FileName: PackageConfig?.PackageName,
      PackageVersion: PackageConfig?.PackageVersion,
      RunCommand: BuildConfig?.RunCommand,
      // 缺少 Jdk Container,
      Jdk: BuildConfig?.RuntimeVersion,
      Container: get(BuildConfig, 'TomcatConfig.Version'),
    });
    setIsLoading(false);
  };

  const closePackageSlide = () => {
    setShowing(false);
    setTimeout(() => {
      setIsUpdateSuccess(false);
      if (pipelineRunId) {
        setRefreshIndex && setRefreshIndex(Date.now());
        setPipelineRunId('');
      }
    }, 200);
  };

  const okPackageSlide = async () => {
    const result = await refPackage.current.submit();
    if (result?.error) {
      Message.error(result?.error);
      return;
    }
    const { Jdk, PackageType, PackageUrl, FileName, PackageVersion, RunCommand = '' } = result;
    const buildConfig = {
      RunCommand,
      BuildType: 'Buildpack',
      RuntimeVersion: Jdk,
      RuntimeType: 'java',
    };
    if (PackageType === PACKAGE_TYPE.WAR) {
      const container = result.WebContainer || result.EdasContainerVersion;
      Reflect.set(buildConfig, 'TomcatConfig', { Version: container });
    }

    packageParams.current = {
      BuildConfig: buildConfig,
      PackageConfig: {
        PackageType,
        PackageUrl,
        PackageVersion, // 包的版本
        PackageName: FileName, // 文件名
      },
      ImageConfig: {
        InstanceType: 'SHARE_ACREE',
      },
    };
    setVisible(true);
  };

  const updateAppPackage = async () => {
    setIsSubmit(true);
    const result = await services.updateBuildPipeline({
      params: {
        content: {
          ApplicationId: applicationID,
          ApplicationName: applicationName,
          Enabled: true,
          ...packageParams.current,
          DeployConfig: {
            UpdateApplicationInput: JSON.stringify({
              ...params.current,
              effectiveImmediately,
            }),
          },
        },
      },
      customErrorHandle: (err, data, callback) => {
        setIsSubmit(false);
        setVisible(false);
        setIsUpdateSuccess(false);
        setPipelineRunId('');
        callback && callback();
      },
    });
    const { PipelineRunId: _pipelineRunId } = result;
    setIsSubmit(false);
    setVisible(false);
    setPipelineRunId(_pipelineRunId);
    setIsUpdateSuccess(true);
  };

  const title = useMemo(() => {
    const version = unshiftZero(appVersionId);
    let _title = intl(
      'saenext.components.version-list.UpdatePackagePanel.VersionVersionConfigurationCodePackage',
      { version: version },
    );
    if (isUpdateSuccess) {
      _title = intl(
        'saenext.components.version-list.UpdatePackagePanel.PipelinerunidProgressPreview',
        { pipelineRunId: pipelineRunId },
      );
    }
    return _title;
  }, [appVersionId, isUpdateSuccess]);

  const customFooter = useMemo(() => {
    if (isUpdateSuccess) {
      return (
        <>
          <Button
            type="primary"
            onClick={() => {
              setIsUpdateSuccess(false);
            }}
          >
            {intl('saenext.components.version-list.UpdatePackagePanel.ReturnConfiguration')}
          </Button>
          <Button
            className="ml-s"
            onClick={() => {
              // if (buildStatus !== PIPELINE_STATUS.SUCCESS) {
              //   setIsUpdateSuccess(false);
              //   // 这里分开写是因为避免成功时关闭出现创建页面
              // } else {
              //   setShowing(false);
              //   setIsUpdateSuccess(false);
              //   setRefreshIndex && setRefreshIndex(Date.now());
              //   setPipelineRunId('');
              // }
              // 直接关闭
              setShowing(false);
              setIsUpdateSuccess(false);
              setRefreshIndex && setRefreshIndex(Date.now());
              setPipelineRunId('');
            }}
          >
            {intl('saenext.components.version-list.UpdatePackagePanel.Close')}
          </Button>
        </>
      );
    }
    return (
      <>
        <Button type="primary" onClick={okPackageSlide}>
          {intl('saenext.components.version-list.UpdatePackagePanel.Ok')}
        </Button>
        <Button className="ml-s" onClick={closePackageSlide}>
          {intl('saenext.components.version-list.UpdatePackagePanel.Cancel')}
        </Button>
      </>
    );
  }, [isUpdateSuccess, buildStatus]);

  return (
    <>
      <SlidePanel
        title={title}
        width="large"
        isShowing={showing}
        customFooter={customFooter}
        onClose={closePackageSlide}
      >
        <Loading visible={isLoading} className="full-width full-height">
          {/* {
             isUpdateSuccess ? (
               <ProcessField
                 history={history}
                 urlSearch={urlSearch}
                 regionId={regionId}
                 deployType={DEPLOY_TYPE.WEB_PACKAGE}
                 pipelineRunId={pipelineRunId}
                 applicationId={applicationID}
                 applicationName={applicationName}
                 setBuildStatus={(_buildStatus) => { setBuildStatus(_buildStatus) }}
               />
             ) : (
               <WebPackageConfig 
                 ref={refPackage}
                 initValue={buildData}
                 style={{ marginTop: 100 }}
               />
             )
            } */}
          <WebPackageConfig
            ref={refPackage}
            initValue={buildData}
            style={{ display: isUpdateSuccess ? 'none' : 'block' }}
          />

          <ProcessField
            history={history}
            urlSearch={urlSearch}
            regionId={regionId}
            deployType={DEPLOY_TYPE.WEB_PACKAGE}
            pipelineRunId={pipelineRunId}
            applicationId={applicationID}
            applicationName={applicationName}
            style={{ display: isUpdateSuccess ? 'block' : 'none' }}
            setBuildStatus={(_buildStatus) => {
              setBuildStatus(_buildStatus);
            }}
          />
        </Loading>
      </SlidePanel>
      <Dialog
        size="small"
        title={
          <div className="flex">
            <Icon type="help_fill" style={{ color: '#ffd813' }} />
            <span className="ml-s">
              {intl('saenext.components.version-list.UpdatePackagePanel.WhichTrafficPolicyWillYou')}
            </span>
          </div>
        }
        visible={visible}
        onOk={updateAppPackage}
        okProps={{
          loading: isSubmit,
        }}
        onCancel={() => setVisible(false)}
        onClose={() => setVisible(false)}
      >
        <RadioGroup
          itemDirection="ver"
          value={effectiveImmediately}
          onChange={(value) => setEffectiveImmediately(value as boolean)}
        >
          <Radio value={true}>
            {intl(
              'saenext.components.version-list.UpdatePackagePanel.EnableTrafficAutomaticallyAfterDeployment',
            )}
          </Radio>
          <Radio value={false}>
            {intl(
              'saenext.components.version-list.UpdatePackagePanel.AfterTheDeploymentIsCompleted',
            )}
          </Radio>
        </RadioGroup>
      </Dialog>
    </>
  );
});
