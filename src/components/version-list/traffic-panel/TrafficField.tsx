import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import { Message, Icon } from '@ali/cnd';
import { TableField, NumberField, SelectField, RadioField } from '@ali/deep';
import Form from '@ali/deep-form';
import { each, orderBy } from 'lodash';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import NumberRangeField from '@ali/deep-number-range-field';
// import CnGreyRuleField from './GrayField';
import _ from 'lodash';
import services from '~/services';

const fieldLayout = {
  labelAlign: 'left',
  labelTextAlign: 'left',
  wrapperCol: { span: 19 },
  labelCol: { span: 5 },
};

type State = {
  currentVersions: any;
  refreshIndex: number;
  limitInstances: number;
};

type Props = {
  versions: { label: any; value: string }[];
  scaleConfig: {
    minimumInstanceCount: number;
    maximumInstanceCount: number;
    alwaysAllocateCPU: boolean;
  };
};

export default class TrafficField extends Component<Props, State> {
  private field = React.createRef() as any;
  private refRule = React.createRef() as any;
  constructor(props) {
    super(props);
    this.state = {
      currentVersions: {},
      refreshIndex: 0,
      limitInstances: 50,
    };
    this.setValues = this.setValues.bind(this);
    this.getValues = this.getValues.bind(this);
    this.validation = this.validation.bind(this);
    this.onchange = this.onchange.bind(this);
  }

  componentDidMount(): void {
    this.getLimitInstances();
  }

  getLimitInstances = async () => {
    const res = await services.GetAccountSettings();
    const { maximumInstancesPerApplication = 50 } = res;
    this.setState({
      limitInstances: maximumInstancesPerApplication,
    });
  };

  setValues(value) {
    this.setState({ currentVersions: value });
    this.field.setValue(value);
  }

  validation(value) {
    let sum = 0;
    for (let item of value) {
      if (!item) {
        return intl(
          'saenext.version-list.TrafficConfPanel.TrafficField.IncompleteVersionTrafficConfiguration',
        );
      }
      const { versionId, weight } = item;
      if (!versionId) {
        return intl(
          'saenext.version-list.TrafficConfPanel.TrafficField.IncompleteVersionTrafficConfiguration',
        );
      }
      if (weight !== 0 && !weight) {
        return intl(
          'saenext.version-list.TrafficConfPanel.TrafficField.IncompleteVersionTrafficConfiguration',
        );
      }
      sum = sum + weight;
    }
    if (sum !== 100) {
      return intl('saenext.version-list.TrafficConfPanel.TrafficField.TheSumOfAllVersion');
    }
    return true;
  }

  validationGrey = (value) => {
    const validateRes = this.refRule?.getInstance()?.controlRef.ruleValidate();
    if (!validateRes) {
      return ' ';
    }
    return true;
  };

  onchange(val) {
    // TODO
    this.setState({
      refreshIndex: Date.now(),
    });
  }

  getValues() {
    return new Promise((resolve, reject) => {
      this.field.validate((error, values) => {
        if (error) return reject(error);
        // values 表单值
        const { versionWeight = [], scaleConfig = {}, resolvePolicy, routePolicy } = values;

        const params = {};
        const { start: minimumInstanceCount, end: maximumInstanceCount } = scaleConfig;

        //alwaysAllocateCPU 初始值，不做更改
        const { alwaysAllocateCPU } = this.props.scaleConfig;

        const _scaleConfig = {
          minimumInstanceCount,
          maximumInstanceCount,
          alwaysAllocateCPU,
        };

        Object.assign(params, { scaleConfig: _scaleConfig });

        if (resolvePolicy === 'Random') {
          // 流量配置 选择流量配置最高的作为主版本
          const _dataSource = orderBy(versionWeight, ['weight'], ['desc']);
          const [main, ...alphas] = _dataSource;
          const { versionId } = main;

          const _traffic = { versionId, additionalVersionWeight: {} };
          if (alphas.length) {
            let additionalVersionWeight = {};
            each(alphas, (alpha) => {
              const { versionId: alphaVersion, weight: alphaWeight } = alpha;
              if (alphaWeight) {
                Reflect.set(additionalVersionWeight, alphaVersion, parseInt(alphaWeight, 10) / 100);
              }
            });
            _traffic.additionalVersionWeight = additionalVersionWeight;
          }

          Object.assign(params, {
            traffic: {
              resolvePolicy,
              ..._traffic,
            },
          });
        }
        if (resolvePolicy === 'Content') {
          Object.assign(params, {
            traffic: {
              resolvePolicy,
              routePolicy,
            },
          });
        }
        resolve(params);
      });
    });
  }

  onRefresh = () => {
    this.setState({
      refreshIndex: Date.now(),
    });
  };

  versionsFilter() {
    const { versions } = this.props;
    const { versionWeight } = this.field.getValue?.() || {};
    const versionsSelected = _.map(versionWeight, 'versionId');
    const versionsDisabled = _.map(versions, (item) => {
      if (versionsSelected && versionsSelected.includes(item.value)) {
        // @ts-ignore
        item.disabled = true;
      } else {
        // @ts-ignore
        delete item.disabled;
      }
      return item;
    });

    return versionsDisabled;
  }

  render() {
    const { resolvePolicy } = (this.field.getValue && this.field.getValue()) || {};

    return (
      <>
        <div>
          {intl(
            'saenext.version-list.TrafficConfPanel.TrafficField.TheTrafficConfigurationFunctionIs',
          )}
        </div>
        <Message type="notice" className="mt-s mb">
          <div className="text-line">
            {intl('saenext.version-list.TrafficConfPanel.TrafficField.TheSumOfTheTraffic')}
          </div>
          <div className="text-line">
            {intl('saenext.version-list.TrafficConfPanel.TrafficField.IfTheVersionOfThe')}
          </div>
          <div className="text-line">
            {intl('saenext.version-list.TrafficConfPanel.TrafficField.TrafficRouteAdjustmentIsNot')}
          </div>
        </Message>
        <Form
          ref={(c) => {
            if (c) {
              this.field = c.getInstance();
            }
          }}
        >
          <RadioField
            required
            label={intl('saenext.version-list.TrafficConfPanel.TrafficField.TrafficScenarios')}
            defaultValue={false}
            {...fieldLayout}
            dataSource={[
              {
                value: false,
                text: (
                  <TextWithBalloon
                    align="r"
                    text={intl('saenext.version-list.TrafficConfPanel.TrafficField.RollingUpgrade')}
                    tips={intl(
                      'saenext.version-list.TrafficConfPanel.TrafficField.InTheRollingUpgradeScenario',
                    )}
                  />
                ),
              },
            ]}
          />

          {/* <RadioField
             required
             label="灰度类型"
             name="resolvePolicy"
             defaultValue={'Random'}
             onChange={this.onRefresh}
             {...fieldLayout}
             dataSource={[
               {
                 value: 'Random',
                 text: (
                   <TextWithBalloon
                     align='r'
                     text={'按流量百分比灰度'}
                     tips={''}
                   />
                 )
               },
               {
                 value: 'Content',
                 text: (
                   <TextWithBalloon
                     align='r'
                     text={'按内容灰度'}
                     tips={''}
                   />
                 )
               }
             ]}
            /> */}
          {resolvePolicy === 'Random' && (
            <TableField
              required
              name="versionWeight"
              label={intl('saenext.version-list.TrafficConfPanel.TrafficField.TrafficMode')}
              layout="TILED"
              showIndex={false}
              showSortable={false}
              {...fieldLayout}
              minItems={1}
              maxItems={5}
              className="full24-tiled"
              addButtonText={intl(
                'saenext.version-list.TrafficConfPanel.TrafficField.TrafficConfiguration',
              )}
              showDeleteConfirm={false}
              onChange={this.onchange}
              delButtonText={<Icon type="delete" />}
              validation={[
                {
                  type: 'customValidate',
                  param: this.validation,
                },
              ]}
            >
              <SelectField
                name="versionId"
                placeholder={intl(
                  'saenext.version-list.TrafficConfPanel.TrafficField.PleaseSelectAVersion',
                )}
                dataSource={this.versionsFilter()}
              />

              <NumberField
                name="weight"
                defaultValue={0}
                min={0}
                max={100}
                placeholder=" "
                innerAfter={<>%</>}
              />
            </TableField>
          )}

          {/* {resolvePolicy === 'Content' && (
            <CnGreyRuleField
              name="routePolicy"
              labelCol={5}
              ref={(ref) => (this.refRule = ref)}
              validation={[
                {
                  type: 'customValidate',
                  param: this.validationGrey,
                },
              ]}
            />
          )} */}

          <NumberRangeField
            required
            min={0}
            label={intl(
              'saenext.version-list.TrafficConfPanel.TrafficField.AutomaticScalingOfInstances',
            )}
            {...fieldLayout}
            name="scaleConfig"
            validation={[
              {
                type: 'required',
                message: intl(
                  'saenext.version-list.TrafficConfPanel.TrafficField.SelectARangeOfAuto',
                ),
              },
              {
                type: 'customValidate',
                param: (value) => {
                  const { start, end } = value;
                  if (start === undefined || !end) {
                    return intl(
                      'saenext.version-list.TrafficConfPanel.TrafficField.SelectARangeOfAuto',
                    );
                  }
                  if (start > end) {
                    return intl(
                      'saenext.version-list.TrafficConfPanel.TrafficField.SelectTheCorrectRangeOf',
                    );
                  }
                  if (end > this.state.limitInstances) {
                    return intl(
                      'saenext.version-list.TrafficConfPanel.TrafficField.TheMaximumNumberOfSingle',
                      { thisStateLimitInstances: this.state.limitInstances },
                    );
                  }
                  return true;
                },
              },
            ]}
          />
        </Form>
      </>
    );
  }
}
