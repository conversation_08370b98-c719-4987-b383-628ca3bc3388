import { intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { SlidePanel, Loading, Message } from '@ali/cnd';
import services from '~/services';
import { orderBy } from 'lodash';
import { getParams, unshiftZero } from '~/utils/global';
import { AES_CONSTANT, trackCore } from '~/tracker';
import TrafficField from './TrafficField';

interface ITrafficConfProps {
  appVersions: { versionId: string; [key: string]: any }[];
  scaleConfig: {
    minimumInstanceCount: number;
    maximumInstanceCount: number;
    alwaysAllocateCPU: boolean;
  };
  applicationID: string;
  setStartTimer: (scale?: any) => void;
  setRefreshIndex: (date: number) => void;
  setScaleConfig?: (scale: any) => void;
}

export default forwardRef((props: ITrafficConfProps, ref) => {
  const { applicationID, appVersions, scaleConfig, setStartTimer, setRefreshIndex } = props;
  const [isProcessing, setIsProcessing] = useState(false);
  const [visible, setVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [versions, setVersions] = useState([]);
  const field = useRef(null);
  const oldTraffic = useRef(null);
  const applicationName = getParams('name');

  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }));

  useEffect(() => {
    // 回显
    if (!visible) return;
    const _versions = appVersions.map((version) => {
      const { versionId } = version;
      const title = unshiftZero(versionId);
      return {
        label: title,
        value: versionId,
      };
    });
    setVersions(_versions);
    getAppVersionTraffic();
  }, [visible]);

  const getAppVersionTraffic = async () => {
    setIsLoading(true);
    const data = await services.getAppVersionTraffic({
      applicationID,
      applicationName,
    });
    const {
      resolvePolicy = 'Random',
      versionId,
      additionalVersionWeight = {},
      routePolicy = {},
    } = data;
    const keys = Object.getOwnPropertyNames(additionalVersionWeight) || [];
    let versionWeight = [];
    if (keys.length) {
      // TODO
      const _dataSource = [];
      let alphaWeight = 0;
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const value = additionalVersionWeight[key];
        if (!value) continue;
        let _weight = 0;
        const _value = value.toFixed(2);
        const [integer, decimals] = _value.split('.');
        if (parseInt(integer, 10)) {
          _weight = 100;
        } else {
          _weight = parseInt(decimals, 10);
        }
        alphaWeight = alphaWeight + _weight;
        _dataSource.push({ versionId: key, weight: _weight });
      }
      const weight = 100 - alphaWeight;
      _dataSource.push({ versionId, weight });

      versionWeight = orderBy(_dataSource, ['weight'], ['desc']);
    } else {
      versionWeight = [{ versionId, weight: 100 }];
    }

    const { minimumInstanceCount, maximumInstanceCount } = scaleConfig;

    const values = {
      resolvePolicy,
      versionWeight,
      routePolicy,
      scaleConfig: {
        start: minimumInstanceCount,
        end: maximumInstanceCount,
      },
    };
    field?.current.setValues(values);
    oldTraffic.current = {
      ...values,
      trafficConf:
        resolvePolicy === 'Random'
          ? { resolvePolicy, versionId, additionalVersionWeight }
          : { resolvePolicy, routePolicy },
    };
    setIsLoading(false);
  };

  const handleSubmit = async () => {
    const values = await field?.current.getValues();
    const { versionWeight } = field.current?.field.getValue?.() || {};

    const { scaleConfig, traffic } = values;

    const { minimumInstanceCount, maximumInstanceCount } = scaleConfig;
    const { trafficConf: oldTrafficConf, scaleConfig: oldScaleConf } = oldTraffic?.current;
    // 判断 scaleConfig 有无改变
    const isDiffScale =
      oldScaleConf.start !== minimumInstanceCount || oldScaleConf.end !== maximumInstanceCount;

    // 判断 traffic 有无改变
    const isDiffTraff = JSON.stringify(traffic) !== JSON.stringify(oldTrafficConf);

    if (!isDiffScale && !isDiffTraff) {
      Message.error(
        intl('saenext.version-list.TrafficConfPanel.ModifyTheConfigurationAndResubmit'),
      );
      return;
    }

    setIsProcessing(true);
    try {
      const resTraffic =
        isDiffTraff &&
        (await services.updateAppVersionTraffic(applicationID, applicationName, traffic));

      scaleConfig.alwaysAllocateCPU = false;
      const resScale =
        isDiffScale &&
        (await services.updateApplicationScale(applicationID, applicationName, scaleConfig));

      if ((isDiffTraff && !resTraffic) || (isDiffScale && !resScale)) {
        setIsProcessing(false);
        return;
      }

      trackCore({
        behavior: AES_CONSTANT.CORE_BEHAVIOR_TYPE.TRAFFIC,
        stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
      });
    } catch (err) {
      Message.error(err.message);
    }
    // 启动计时器
    setStartTimer &&
      setStartTimer({
        isDiffTraff,
        isDiffScale,
        scaleConfig,
        versionWeight,
      });
    setRefreshIndex && setRefreshIndex(Date.now());
    setIsProcessing(false);
    setVisible(false);
  };

  const handleClose = () => {
    setIsProcessing(false);
    setVisible(false);
  };

  return (
    <SlidePanel
      title={intl('saenext.version-list.TrafficConfPanel.TrafficConfiguration')}
      isShowing={visible}
      onOk={handleSubmit}
      onCancel={handleClose}
      onClose={handleClose}
      isProcessing={isProcessing}
      width="large"
      okText={intl('saenext.version-list.TrafficConfPanel.Ok')}
      cancelText={intl('saenext.version-list.TrafficConfPanel.Cancel')}
    >
      <Loading visible={isLoading} style={{ display: 'block', height: '100%' }}>
        <TrafficField ref={field} versions={versions} scaleConfig={scaleConfig} />
      </Loading>
    </SlidePanel>
  );
});
