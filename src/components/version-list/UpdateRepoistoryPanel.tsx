import { intl } from '@ali/cnd';
import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from 'react';
import { Loading, SlidePanel, Message, Radio, Button } from '@ali/cnd';
import { get, isEmpty } from 'lodash';
import services from '~/services';
import { getParams, unshiftZero } from '~/utils/global';
import RepoistoryConfig from '~/components/shared/DeploySelectorField/RepoistoryConfig';
import ProcessField from './version-create/ProcessField';
import { DEPLOY_TYPE, PIPELINE_STATUS } from '~/constants/application';

// @ts-ignore
const RadioGroup = Radio.Group;
type Props = {
  history: any;
  urlSearch: any;
  regionId: string;
  appVersionId: string;
  applicationID: string;
  setRefreshIndex: (date: number) => void;
};

export default forwardRef((props: Props, ref) => {
  const applicationName = getParams('name');
  const { history, urlSearch, regionId, appVersionId, applicationID, setRefreshIndex } = props;
  const refRepoistory = useRef(null);
  const [showing, setShowing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const params = useRef({});
  const repoParams = useRef({});
  // @ts-ignore
  const [effectiveImmediately, setEffectiveImmediately] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);
  // 构建数据
  const [buildData, setBuildData] = useState({});
  const [isUpdateSuccess, setIsUpdateSuccess] = useState(false);
  const [pipelineRunId, setPipelineRunId] = useState('');
  const [buildStatus, setBuildStatus] = useState(PIPELINE_STATUS.FAIL);

  useEffect(() => {
    if (!showing || isEmpty(appVersionId)) return;
    initRepoistoryData();
  }, [appVersionId, showing]);

  useImperativeHandle(ref, () => ({
    show: () => setShowing(true),
    hide: () => setShowing(false),
  }));

  const initRepoistoryData = async () => {
    setIsLoading(true);
    const data = await services.getAppVersionConfig({
      applicationID,
      applicationName,
      qualifier: appVersionId,
    });
    params.current = {
      ...data,
      instanceConcurrency: data?.instanceConcurrency,
      caPort: data?.caPort,
      cpu: data?.cpu,
      memorySize: data?.memorySize,
      timeout: data?.timeout,
      // imageConfig: data?.imageConfig,
      // 更新传null 表示之前配置不变, 传{}表示要去掉之前的配置
      nasConfig: get(data, 'nasConfig', null),
      ossMountConfig: get(data, 'ossMountConfig', null),
      environmentVariables: get(data, 'environmentVariables', null),
      logConfig: get(data, 'logConfig', null),
      slsConfig: get(data, 'slsConfig', null),
      startupProbe: get(data, 'startupProbe', null),
      livenessProbe: get(data, 'livenessProbe', null),
    };

    const res = await services.describeBuildPipeline({
      ApplicationId: applicationID,
      ApplicationName: applicationName,
    });
    const { Data: _buildData } = res;
    setBuildData(_buildData);
    setIsLoading(false);
  };

  const closeRepoistorySlide = () => {
    setShowing(false);
    setTimeout(() => {
      setIsUpdateSuccess(false);
      if (pipelineRunId) {
        setRefreshIndex && setRefreshIndex(Date.now());
        setPipelineRunId('');
      }
    }, 200);
  };

  const okRepoistorySlide = async () => {
    setIsSubmit(true);
    const result = await refRepoistory.current.submit();
    if (result?.error) {
      setIsSubmit(false);
      Message.error(result?.error);
      return;
    }
    repoParams.current = result;
    updateAppRepoistory();
  };

  const updateAppRepoistory = async () => {
    const result = await services.updateBuildPipeline({
      params: {
        content: {
          Enabled: true,
          ...repoParams.current,
          ApplicationId: applicationID,
          ApplicationName: applicationName,
          DeployConfig: {
            UpdateTraffic: get(repoParams.current, 'DeployConfig.UpdateTraffic', false),
            UpdateApplicationInput: JSON.stringify({
              ...params.current,
              effectiveImmediately,
            }),
          },
        },
      },
      customErrorHandle: (err, data, callback) => {
        setIsSubmit(false);
        setIsUpdateSuccess(false);
        setPipelineRunId('');
        callback && callback();
      },
    });
    const { PipelineRunId: _pipelineRunId } = result;
    setIsSubmit(false);
    setPipelineRunId(_pipelineRunId);
    setIsUpdateSuccess(true);
  };

  const title = useMemo(() => {
    const version = unshiftZero(appVersionId);
    let _title = intl(
      'saenext.components.version-list.UpdateRepoistoryPanel.VersionVersionConfigureSourceCode',
      { version: version },
    );
    if (isUpdateSuccess) {
      _title = intl(
        'saenext.components.version-list.UpdateRepoistoryPanel.PipelinerunidProgressPreview',
        { pipelineRunId: pipelineRunId },
      );
    }
    return _title;
  }, [appVersionId, isUpdateSuccess]);

  const customFooter = useMemo(() => {
    if (isUpdateSuccess) {
      return (
        <>
          <Button
            type="primary"
            onClick={() => {
              setIsUpdateSuccess(false);
            }}
          >
            {intl('saenext.components.version-list.UpdateRepoistoryPanel.ReturnConfiguration')}
          </Button>
          <Button
            onClick={() => {
              // if (buildStatus !== PIPELINE_STATUS.SUCCESS) {
              //   setIsUpdateSuccess(false);
              //   // 这里分开写是因为避免成功时关闭出现创建页面
              // } else {
              //   setShowing(false);
              //   setIsUpdateSuccess(false);
              //   setRefreshIndex && setRefreshIndex(Date.now());
              //   setPipelineRunId('');
              // }
              setShowing(false);
              setIsUpdateSuccess(false);
              setRefreshIndex && setRefreshIndex(Date.now());
              setPipelineRunId('');
            }}
          >
            {intl('saenext.components.version-list.UpdateRepoistoryPanel.Close')}
          </Button>
        </>
      );
    }
    return (
      <div>
        <Button type="primary" onClick={okRepoistorySlide} loading={isSubmit}>
          {intl('saenext.components.version-list.UpdateRepoistoryPanel.Ok')}
        </Button>
        <Button className="ml-s" onClick={closeRepoistorySlide}>
          {intl('saenext.components.version-list.UpdateRepoistoryPanel.Cancel')}
        </Button>
      </div>
    );
  }, [isUpdateSuccess, buildStatus]);

  return (
    <>
      <SlidePanel
        title={title}
        width="large"
        isShowing={showing}
        customFooter={customFooter}
        onClose={closeRepoistorySlide}
      >
        <Loading visible={isLoading} className="full-width full-height">
          {/* {
             isUpdateSuccess ? (
               <ProcessField
                 history={history}
                 urlSearch={urlSearch}
                 regionId={regionId}
                 deployType={DEPLOY_TYPE.REPOISTORY}
                 pipelineRunId={pipelineRunId}
                 applicationId={applicationID}
                 applicationName={applicationName}
                 setBuildStatus={(_buildStatus) => { setBuildStatus(_buildStatus) }}
               />
             ) : (
               <RepoistoryConfig 
                 ref={refRepoistory}
                 initValue={buildData}
               />
             )
            } */}
          <RepoistoryConfig
            ref={refRepoistory}
            initValue={buildData}
            style={{ display: isUpdateSuccess ? 'none' : 'block' }}
          />

          <ProcessField
            history={history}
            urlSearch={urlSearch}
            regionId={regionId}
            deployType={DEPLOY_TYPE.REPOISTORY}
            pipelineRunId={pipelineRunId}
            applicationId={applicationID}
            applicationName={applicationName}
            style={{ display: isUpdateSuccess ? 'block' : 'none' }}
            setBuildStatus={(_buildStatus) => {
              setBuildStatus(_buildStatus);
            }}
          />
        </Loading>
      </SlidePanel>
    </>
  );
});
