import { intl } from '@ali/cnd';
import React, { useRef, useState, useEffect } from 'react';
import { Loading, Timeline, Icon, Button } from '@ali/cnd';
import services from '~/services';
import _ from 'lodash';
import { unshiftZero } from '~/utils/global';
import {
  DEPLOY_TYPE,
  PIPELINE_STATUS,
  InitPackageSteps,
  InitRepoistorySteps,
} from '~/constants/application';

interface Props {
  style?: any;
  history: any;
  urlSearch: any;
  regionId: string;
  deployType: string;
  pipelineRunId: string;
  applicationId: string;
  applicationName: string;
  setBuildStatus: (status) => void;
}

export const StepStatus = {
  [PIPELINE_STATUS.RUNNING]: {
    icon: <Icon type="loading" size="small" />,
  },
  [PIPELINE_STATUS.SUCCESS]: {
    icon: <Icon type="success" size="small" />,
  },
  [PIPELINE_STATUS.FAIL]: {
    icon: <Icon type="error" size="small" />,
  },
  [PIPELINE_STATUS.WAIT]: {
    icon: <Icon type="minus_fill" size="small" />,
  },
  [PIPELINE_STATUS.STOP]: {
    icon: <Icon type="minus_fill" size="small" />,
  },
  [PIPELINE_STATUS.CANCEL]: {
    icon: <Icon type="minus_fill" size="small" />,
  },
};

const ProcessField = (props: Props) => {
  const {
    style,
    history,
    urlSearch,
    regionId,
    deployType,
    pipelineRunId,
    applicationId,
    applicationName,
    setBuildStatus = () => {},
  } = props;
  const _processData =
    deployType === DEPLOY_TYPE.WEB_PACKAGE ? InitPackageSteps : InitRepoistorySteps;
  const [isLoading, setIsLoading] = useState(false);
  const [processData, setProcessData] = useState(_processData);

  const timerRef = useRef(null);

  useEffect(() => {
    getBuildProcess();
    return () => {
      setClearTimer();
    };
  }, [pipelineRunId]);

  const getBuildProcess = async () => {
    setIsLoading(true);
    await getBuildPipelineRun();
    setIsLoading(false);
  };

  const getBuildPipelineRun = async () => {
    if (!pipelineRunId) return;
    const res = await services.DescribeBuildPipelineRun({
      ApplicationId: applicationId,
      ApplicationName: applicationName,
      PipelineRunId: pipelineRunId,
    });
    const { Data = {} } = res;
    const { Status, Steps = [] } = Data;

    // Status 可以拿到流水线的状态 但是后端状态更新不及时，当流水线状态是成功时，子步骤可能仍在运行，此时不能标定为结束
    let buildStatus = Status;
    if (Status === PIPELINE_STATUS.SUCCESS) {
      // 检查子步骤 是否未结束，如果有未结束的子步骤，则流水线状态为运行
      const isIncomplete = Steps.some((item) =>
        [PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT].includes(item.Status),
      );
      if (isIncomplete) {
        // 实际流出未完成
        buildStatus = PIPELINE_STATUS.RUNNING;
      } else {
        // 流程完成 清除定时器
        setClearTimer();
      }
    }

    const firstStep = Steps[0];
    // 前端体验优化,当流水线的状态状态是 运行中，如果第一步是等待⌛️，设置为运行中
    if (buildStatus === PIPELINE_STATUS.RUNNING && firstStep.Status === PIPELINE_STATUS.WAIT) {
      Data.Steps[0].Status = PIPELINE_STATUS.RUNNING;
    }

    // 1、停止 流水线的终态，将子步骤设为停止
    if (buildStatus === PIPELINE_STATUS.STOP) {
      Data.Steps = Data.Steps.map((item) => {
        const status = item.Status === PIPELINE_STATUS.RUNNING ? PIPELINE_STATUS.STOP : item.Status;
        return { ...item, Status: status };
      });
      setClearTimer();
    }

    // 2、失败 流水线的终态，将子步骤设为失败
    if (buildStatus === PIPELINE_STATUS.FAIL) {
      Data.Steps = Data.Steps.map((item) => {
        const status = item.Status === PIPELINE_STATUS.RUNNING ? PIPELINE_STATUS.FAIL : item.Status;
        return { ...item, Status: status };
      });
      setClearTimer();
    }

    const { StartTime, PackageConfig = {}, BuildConfig = {} } = Data;
    const { PackageName, PackageVersion, PackageType, PackageUrl } = PackageConfig;
    const { BuildType, BeforeBuildCommand, RunCommand, WorkingDir } = BuildConfig;

    //  代码包部署 只要 CreateEnv ImageBuild CreateVersion 三个步骤
    let packageSteps = Steps;
    if (deployType === DEPLOY_TYPE.WEB_PACKAGE) {
      packageSteps = Steps.filter(
        (step) =>
          step.Id === 'CreateEnv' || step.Id === 'ImageBuild' || step.Id === 'CreateVersion',
      );
    }

    setProcessData({
      ...Data,
      Status: buildStatus,
      Steps: packageSteps,
      StartTime: StartTime,
      BuildType,
      BeforeBuildCommand,
      RunCommand,
      WorkingDir,
      PackageName,
      PackageVersion,
      PackageType,
      PackageUrl,
    });
    setBuildStatus && setBuildStatus(buildStatus);

    // 当流水线的状态状态是 运行中 ｜ 等待中 开启轮训
    if ([PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT].includes(buildStatus)) {
      setStartTimer();
    }
  };

  // 启动计时器
  const setStartTimer = () => {
    setClearTimer();
    timerRef.current = setInterval(() => {
      getBuildPipelineRun();
    }, 1000 * 1.5);
  };

  // 清除计时器
  const setClearTimer = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  return (
    <Loading style={style} visible={isLoading} className="full-width full-height">
      <Timeline>
        {processData.Steps.map((step) => {
          // @ts-ignore
          const { StartTime, Status, Name, Id, Result, Description } = step;
          const { icon: dot } = StepStatus[Status];
          const time = StartTime ? new Date(StartTime).toLocaleString() : '';
          return (
            <Timeline.Item
              key={Id}
              title={Name}
              dot={dot}
              time={time}
              style={{ minHeight: '80px' }}
              content={
                <>
                  <div className="mb-s" style={{ color: '#666' }}>
                    {Description}
                  </div>
                  {processData?.VersionId && Id === 'CreateVersion' ? (
                    <span>
                      {intl('saenext.version-list.CreateVersion.ProcessField.TheNewVersionOfThe')}
                      <span className="ml-xs mr-xs" style={{ fontWeight: 'bold' }}>
                        {unshiftZero(processData.VersionId)}
                      </span>
                      {
                        !_.isEmpty(_.get(processData, 'CodeConfig')) && _.get(processData, 'DeployConfig.UpdateTraffic', false) ? (
                          <span>
                            {intl('saenext.web-app.build-record.RepoistoryPanel.TheTrafficOfTheInitial')}
                          </span>
                        ) : (
                          <span>
                            {intl('saenext.web-app.build-record.RepoistoryPanel.YouCanGoToThe')}
                          </span>
                        )
                        }
                    </span>
                  ) : Status === PIPELINE_STATUS.FAIL ? (
                    <div>
                      <div>{Result}</div>
                      <div>
                        {intl('saenext.version-list.CreateVersion.ProcessField.Click')}

                        <Button
                          text
                          type="primary"
                          className="ml-xs mr-xs"
                          onClick={() => {
                            history.push(
                              `/${regionId}/app-list/${applicationId}/web-app/structure${urlSearch}`,
                            );
                          }}
                        >
                          {pipelineRunId}
                        </Button>
                        {intl('saenext.version-list.CreateVersion.ProcessField.GoToBuildRecordsTo')}
                      </div>
                    </div>
                  ) : (
                    <span>{Result}</span>
                  )}
                </>
              }
            />
          );
        })}
      </Timeline>
    </Loading>
  );
};

export default ProcessField;
