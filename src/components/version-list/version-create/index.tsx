import { intl } from '@ali/cnd';
import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useRef,
  useMemo,
} from 'react';
import { SlidePanel, Loading, Message, Button } from '@ali/cnd';
import _ from 'lodash';
import services from '~/services';
import Create<PERSON>ield from './CreateField';
import ProcessField from './ProcessField';
import { getParams, unshiftZero } from '~/utils/global';
import { AES_CONSTANT, trackCore } from '~/tracker';
import { DEPLOY_TYPE, PACKAGE_TYPE, PIPELINE_STATUS } from '~/constants/application';
import { parseHostsWeb } from '~/components/shared/CustomHostField/utils';

interface Props {
  history: any;
  urlSearch: any;
  regionId: string;
  appVersionId: string;
  applicationID: string;
  setRefreshIndex: (date: number) => void;
  refreshWebApp: () => void;
  appData: any;
  deployType: string;
  newVersionId: string;
}
export default forwardRef((props: Props, ref) => {
  const {
    history,
    urlSearch,
    regionId,
    appVersionId,
    applicationID,
    appData = {},
    newVersionId,
    setRefreshIndex,
    refreshWebApp,
    deployType = DEPLOY_TYPE.IMAGE,
  } = props;
  const field = useRef(null);
  const applicationName = getParams('name');
  const [visible, setVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [vpcId, setVpcId] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [scaleConfig, setScaleConfig] = useState({ start: 2, end: 10 });
  const [rootLogEnabled, setRootLogEnabled] = useState(false);
  const [isUpdateSuccess, setIsUpdateSuccess] = useState(false);
  const [pipelineRunId, setPipelineRunId] = useState('');
  const [buildStatus, setBuildStatus] = useState(PIPELINE_STATUS.FAIL);

  useEffect(() => {
    // 处理回显
    if (!visible) return;
    initAppVersionDetail();
  }, [visible]);

  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }));

  const initAppVersionDetail = async () => {
    setIsLoading(true);
    const params = {
      applicationID,
      applicationName,
      qualifier: appVersionId,
    };
    const customErrorHandle = (err, data, callback) => {
      setIsLoading(false);
      callback && callback();
    };
    const data = await services.getAppVersionConfig(params, customErrorHandle);

    //获取当前vpc配置
    const { vpcConfig = {} } = appData;

    const {
      imageConfig,
      description,
      instanceConcurrency,
      cpu,
      memorySize,
      logConfig,
      scaleConfig,
      // enableAppMetric = false,
      // programmingLanguage,
      customHostAlias,
      ...rest
    } = data;
    let _deployConfig = {
      type: DEPLOY_TYPE.IMAGE,
      image: imageConfig?.image,
      registryConfig: imageConfig?.registryConfig,
      accelerationType: 'Default',
    };
    switch (deployType) {
      case DEPLOY_TYPE.WEB_PACKAGE:
        const _packageValues = await initWebPackageData();
        // @ts-ignore
        _deployConfig = {
          ..._packageValues,
          type: DEPLOY_TYPE.WEB_PACKAGE,
        };
        break;
      case DEPLOY_TYPE.REPOISTORY:
        const _repoistoryValues = await initRepoistoryData();
        _deployConfig = {
          ..._repoistoryValues,
          type: DEPLOY_TYPE.REPOISTORY,
        };
        break;
    }
    // 基础配置
    const values = {
      description,
      spec: {
        cpu,
        memory: Math.ceil(parseInt(memorySize, 10) / 1024),
      },
      instanceConcurrency,
      deployConfig: _deployConfig,
      ...rest,
      vpcConfig, //用最新版本的vpc配置覆盖
    };

    // 高级配置
    if (data.command && data.args) {
      // 启动命令设置 startCmd
      Object.assign(values, {
        startCmd: {
          Command: (JSON.parse(data.command) || [])[0],
          CommandArgs: JSON.parse(data.args),
        },
      });
    }

    // 环境变量
    if (Object.getOwnPropertyNames(data.environmentVariables || {}).length) {
      Object.assign(values, {
        environmentVariables: { ...data.environmentVariables },
      });
    }

    if (customHostAlias?.hostAliases?.length) {
      Object.assign(values, { hostsArr: parseHostsWeb(customHostAlias) });
    }

    // 日志
    if (data.logConfig || data.slsConfig) {
      Object.assign(values, { logConfig: data?.logConfig });
      Object.assign(values, {
        logTupleConfig: {
          logConfig: _.get(data, 'logConfig', null),
          slsConfig: _.get(data, 'slsConfig', null),
        },
      });
      let _logEnabled = !!Object.keys(data?.logConfig || {}).length;
      const _slsEnabled = !!Object.keys(data?.slsConfig || {}).length;
      if (logConfig) {
        _logEnabled = !!logConfig.project;
      }
      setRootLogEnabled(_logEnabled || _slsEnabled);
    }

    // 健康检查
    if (data.startupProbe || data.livenessProbe) {
      Object.assign(values, {
        healthCheckConfig: {
          startupProbe: _.get(data, 'startupProbe', null),
          livenessProbe: _.get(data, 'livenessProbe', null),
        },
      });
    }

    // 应用监控
    // Object.assign(values, {
    //   armsConfig: {
    //     enableAppMetric: _.get(data, 'enableAppMetric', false),
    //     programmingLanguage: _.get(data, 'programmingLanguage', null),
    //   },
    // });

    // 网络设置
    if (data?.vpcConfig?.vpcId) {
      values.vpcConfig.enableVpc = true;
      setVpcId(data?.vpcConfig?.vpcId);
    }
    setScaleConfig(scaleConfig);
    field?.current.setValues(values);

    setIsLoading(false);
  };

  const initRepoistoryData = async () => {
    const res = await services.describeBuildPipeline({
      ApplicationId: applicationID,
      ApplicationName: applicationName,
    });
    const { Data: _repoistoryValues } = res;
    return _repoistoryValues;
  };

  // @ts-ignore
  const initWebPackageData = async () => {
    // 创建版本 调最新的流水线 describeBuildPipeline
    // 编辑版本 调版本对应的流水线 DescribeBuildPipelineRun
    let res = {};
    if (appVersionId) {
      res = await services.DescribeBuildPipelineRun({
        VersionId: appVersionId,
        ApplicationId: applicationID,
        ApplicationName: applicationName,
      });
    } else {
      res = await services.describeBuildPipeline({
        ApplicationId: applicationID,
        ApplicationName: applicationName,
      });
    }
    // @ts-ignore
    const { Data = {} } = res;
    const { PackageConfig = {}, BuildConfig = {} } = Data;
    const _packageValues = {
      PackageType: PackageConfig?.PackageType,
      UploadType: 'upload',
      PackageUrl: PackageConfig?.PackageUrl,
      FileName: PackageConfig?.PackageName,
      PackageVersion: PackageConfig?.PackageVersion,
      RunCommand: BuildConfig?.RunCommand,
      // 缺少 Jdk Container,
      Jdk: BuildConfig?.RuntimeVersion,
      Container: _.get(BuildConfig, 'TomcatConfig.Version'),
    };
    return _packageValues;
  };

  const handleSubmit = async () => {
    const values = await field?.current.getValues();
    const { params, deployParams } = values;
    const { type, payload } = deployParams;

    switch (type) {
      case DEPLOY_TYPE.IMAGE:
        // 镜像部署 直接调用更新应用接口
        webAppImageDeploy(params);
        break;
      case DEPLOY_TYPE.WEB_PACKAGE:
        // 调用 updateBuildPipeline
        webAppPackageDeploy(params, payload);
        break;
      case DEPLOY_TYPE.REPOISTORY:
        webAppRepoistoryDeploy(params, payload);
        break;
      default:
        break;
    }
  };

  const webAppImageDeploy = async (params) => {
    setIsProcessing(true);
    try {
      const result = await services.updateAppVersions(applicationID, applicationName, {
        ...params,
        applicationID,
      });
      if (result) {
        setVisible(false);
        setRefreshIndex && setRefreshIndex(Date.now());
        refreshWebApp && refreshWebApp();
        trackCore({
          behavior: AES_CONSTANT.CORE_BEHAVIOR_TYPE.VERSION,
          stage: AES_CONSTANT.AES_STAGE_TYPE.SUCCESS,
        });
      }
    } catch (err) {
      Message.error(err.message);
    }
    setIsProcessing(false);
  };

  const webAppPackageDeploy = async (params, packageParams) => {
    setIsProcessing(true);
    const {
      Jdk,
      PackageType,
      PackageUrl,
      FileName,
      PackageVersion,
      RunCommand = '',
    } = packageParams;
    const buildConfig = {
      RunCommand,
      BuildType: 'Buildpack',
      RuntimeVersion: Jdk,
      RuntimeType: 'java',
    };
    if (PackageType === PACKAGE_TYPE.WAR) {
      const container = packageParams.WebContainer || packageParams.EdasContainerVersion;
      Reflect.set(buildConfig, 'TomcatConfig', { Version: container });
    }
    const result = await services.updateBuildPipeline({
      params: {
        content: {
          ApplicationId: applicationID,
          ApplicationName: applicationName,
          BuildConfig: buildConfig,
          PackageConfig: {
            PackageType,
            PackageUrl,
            PackageVersion, // 包的版本
            PackageName: FileName, // 文件名
          },
          ImageConfig: {
            InstanceType: 'SHARE_ACREE',
          },
          DeployConfig: {
            UpdateApplicationInput: JSON.stringify(params),
          },
        },
      },
      customErrorHandle: (err, data, callback) => {
        setIsProcessing(false);
        setIsUpdateSuccess(false);
        setPipelineRunId('');
        callback && callback();
      },
    });
    const { PipelineRunId: _pipelineRunId } = result;
    setIsProcessing(false);
    setPipelineRunId(_pipelineRunId);
    setIsUpdateSuccess(true);
  };

  const webAppRepoistoryDeploy = async (params, repoParams) => {
    setIsProcessing(true);
    const result = await services.updateBuildPipeline({
      params: {
        content: {
          ...repoParams,
          DeployConfig: {
            UpdateTraffic: _.get(repoParams, 'DeployConfig.UpdateTraffic', false),
            UpdateApplicationInput: JSON.stringify(params),
          },
          Enabled: true,
          ApplicationId: applicationID,
          ApplicationName: applicationName,
        },
      },
      customErrorHandle: (err, data, callback) => {
        setIsProcessing(false);
        setIsUpdateSuccess(false);
        setPipelineRunId('');
        callback && callback();
      },
    });
    const { PipelineRunId: _pipelineRunId } = result;
    setIsProcessing(false);
    setPipelineRunId(_pipelineRunId);
    setIsUpdateSuccess(true);
  };

  const handleClose = () => {
    setVisible(false);
    setTimeout(() => {
      setIsProcessing(false);
      setIsUpdateSuccess(false);
      if (pipelineRunId) {
        setRefreshIndex && setRefreshIndex(Date.now());
        setPipelineRunId('');
      }
    }, 200);
  };

  const title = useMemo(() => {
    const version = unshiftZero(newVersionId);
    let _title = intl('saenext.version-list.CreateVersion.CreateVersionVersion', {
      version: version,
    });
    if (!isUpdateSuccess) {
      _title = appVersionId
        ? intl('saenext.version-list.CreateVersion.EditAndDeployANew')
        : intl('saenext.version-list.CreateVersion.CreateVersionVersion', { version: version });
    } else {
      _title = intl('saenext.version-list.CreateVersion.PipelinerunidProgressPreview', {
        pipelineRunId: pipelineRunId,
      });
    }
    return _title;
  }, [appVersionId, isUpdateSuccess]);

  const customFooter = useMemo(() => {
    if (isUpdateSuccess) {
      return (
        <>
          <Button
            type="primary"
            onClick={() => {
              setIsUpdateSuccess(false);
            }}
          >
            {intl('saenext.version-list.CreateVersion.ReturnConfiguration')}
          </Button>
          <Button
            onClick={() => {
              // if (buildStatus !== PIPELINE_STATUS.SUCCESS) {
              //   setIsUpdateSuccess(false);
              //   // 这里分开写是因为避免成功时关闭出现创建页面
              // } else {
              //   setVisible(false);
              //   setIsUpdateSuccess(false);
              //   setRefreshIndex && setRefreshIndex(Date.now());
              //   refreshWebApp && refreshWebApp();
              //   setPipelineRunId('');
              // }

              setVisible(false);
              setIsUpdateSuccess(false);
              setRefreshIndex && setRefreshIndex(Date.now());
              refreshWebApp && refreshWebApp();
              setPipelineRunId('');
            }}
          >
            {intl('saenext.version-list.CreateVersion.Close')}
          </Button>
        </>
      );
    }
    return (
      <div>
        <Button type="primary" loading={isProcessing} onClick={handleSubmit}>
          {intl('saenext.version-list.CreateVersion.Ok')}
        </Button>
        <Button className="ml-s" onClick={handleClose}>
          {intl('saenext.version-list.CreateVersion.Cancel')}
        </Button>
      </div>
    );
  }, [isUpdateSuccess, isProcessing, buildStatus]);

  return (
    <SlidePanel
      title={title}
      width={1000}
      isShowing={visible}
      onClose={handleClose}
      customFooter={customFooter}
    >
      <Loading visible={isLoading} className="full-width full-height">
        <CreateField
          ref={field}
          vpcId={vpcId}
          deployType={deployType}
          scaleConfig={scaleConfig}
          rootLogEnabled={rootLogEnabled}
          style={{
            display: isUpdateSuccess && deployType !== DEPLOY_TYPE.IMAGE ? 'none' : 'block',
          }}
        />

        <ProcessField
          history={history}
          urlSearch={urlSearch}
          regionId={regionId}
          deployType={deployType}
          pipelineRunId={pipelineRunId}
          applicationId={applicationID}
          applicationName={applicationName}
          setBuildStatus={(_buildStatus) => {
            setBuildStatus(_buildStatus);
          }}
          style={{
            display: isUpdateSuccess && deployType !== DEPLOY_TYPE.IMAGE ? 'block' : 'none',
          }}
        />
      </Loading>
    </SlidePanel>
  );
});
