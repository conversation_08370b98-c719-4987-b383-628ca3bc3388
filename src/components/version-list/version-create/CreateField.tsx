import { intl } from '@ali/cnd';
import React, { Component } from 'react';
import Form from '@ali/deep-form';
import { Collapse, CpuMemSelectorField, ConsoleContext } from '@ali/cnd';
import { RadioField, NumberField } from '@ali/deep';
import { DockerCommandEditorField, EnvEditorField } from '@ali/cnd';
import { CpuMemDataSource } from '../../app-create/constants';
// import ArmsMonitorField from '~/components/shared/ArmsMonitorField/web-app';
import DeploySelectorField from '~/components/shared/DeploySelectorField';
import NasSelectorField from '~/components/shared/NasSelectorField/web-app';
import OssSelectorField from '~/components/shared/OssSelectorField/web-app';
import HealthCheckField from '~/components/shared/HealthCheckField/web-app/index_back';
import LogSelectorNewField from '~/components/shared/LogSelectorField/index_back';
import LogSelectorOldField from '~/components/shared/LogSelectorField/index';
import cls from 'classnames';
import _ from 'lodash';
import CachedData from '~/cache/common';
import { DEPLOY_TYPE } from '~/constants/application';
import CustomHostField from '~/components/shared/CustomHostField';
import { formatHostsWeb, validateHostsWeb } from '~/components/shared/CustomHostField/utils';
import { objValueStringfy } from '~/utils/transfer-data';

const Panel = Collapse.Panel;

type Align = 'left' | 'top' | 'inset';
type TextAlign = 'left' | 'right';

const fieldLayout = {
  labelAlign: 'left' as Align,
  labelTextAlign: 'left' as TextAlign,
  wrapperCol: { span: 17 },
  labelCol: { span: 7, style: { width: 170, maxWidth: 170 } },
};

type Props = {
  vpcId: string;
  style?: any;
  deployType: string;
  scaleConfig: { start: number; end: number };
  rootLogEnabled: boolean;
};

type State = {
  initValues: any;
  showHealthCheck?: boolean;
  showLogSelector?: boolean;
  description: any;
  maxConcurrent: number;
};

export default class CreateField extends Component<Props, State> {
  private field = React.createRef() as any;
  private logSelectorField = React.createRef() as any;
  private healthCheckField = React.createRef() as any;
  private nasSelectorField = React.createRef as any;
  private ossSelectorField = React.createRef as any;

  constructor(props) {
    super(props);
    this.state = {
      initValues: {},
      showHealthCheck: false,
      showLogSelector: false,
      description: '',
      maxConcurrent: 200,
    };
    this.setValues = this.setValues.bind(this);
    this.getValues = this.getValues.bind(this);
    this.commandEditorChange = this.commandEditorChange.bind(this);
    // @ts-ignore
    this.regionId = ConsoleContext._currentValue.region.getCurrentRegionId();
  }

  componentDidMount() {
    const _asiWhiteFeature = _.get(window, 'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS.asi-enable');
    const _asiRegionFeature = _.get(window, 'ALIYUN_CONSOLE_GLOBAL.onAsiFeature', []);
    let _showHealthCheck = !!_asiRegionFeature.length;
    let _showLogSelector = !!_asiRegionFeature.length;
    if (_asiWhiteFeature && _asiRegionFeature.length) {
      const regions = _.map(_asiRegionFeature, (item) => item.id);
      // @ts-ignore
      if (!regions.includes(this.regionId)) {
        _showHealthCheck = false;
        _showLogSelector = false;
      }
    }
    this.setState({
      showHealthCheck: _asiWhiteFeature && _showHealthCheck,
      showLogSelector: _asiWhiteFeature && _showLogSelector,
    });

    const uid = _.get(window, 'ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK');
    const grayRules = _.get(window, 'ALIYUN_CONSOLE_GLOBAL.instanceConcurrencyLimit', {});
    let _maxConcurrent = 200;
    if (!_.isEmpty(grayRules)) {
      const key = _.findKey(grayRules, (value) => _.includes(value, uid));
      _maxConcurrent = key ? Number(key) : this.state.maxConcurrent;
    }
    this.setState({
      maxConcurrent: _maxConcurrent
    });
  }

  setValues(value) {
    this.setState({
      initValues: value,
    });
    this.field.setValue(value);
  }

  getValues() {
    return new Promise((resolve, reject) => {
      this.field.validate(async (error, values) => {
        const logValidate = this.logSelectorField?.getInstance()?.controlRef?.validate;
        const logValidateResult = logValidate && (await logValidate());

        let healthValidateResult = true;
        if (this.healthCheckField.current) {
          const healthValidate = this.healthCheckField?.getInstance()?.controlRef?.validate;
          healthValidateResult = healthValidate && healthValidate();
        }

        const nasValidate = this.nasSelectorField?.getInstance()?.controlRef?.validate;
        const nasValidateResult = nasValidate && (await nasValidate());

        const ossValidate = this.ossSelectorField?.getInstance()?.controlRef?.validate;
        const ossValidateResult = ossValidate && (await ossValidate());

        if (logValidateResult !== true) return;
        if (healthValidateResult !== true) return;
        if (nasValidateResult !== true) return;
        if (ossValidateResult !== true) return;
        if (error) {
          resolve('');
          return;
        }

        // values 表单值
        const {
          deployConfig,
          instanceConcurrency,
          effectiveImmediately = false,
          spec,
          caPort,
          timeout,
          nasConfig,
          // armsConfig = { enableAppMetric: false },
          ossMountConfig,
          logConfig = {},
          logTupleConfig = {},
          healthCheckConfig = {},
          hostsArr = [],
        } = values;
        const { type } = deployConfig;
        let imageConfig = {
          accelerationType: 'Default',
          // @ts-ignore
          image: `registry.${this.regionId}.aliyuncs.com/sae-serverless-demo/sae-demo:web-express-helloworld-v1.0`,
        };
        let deployParams = {
          type: DEPLOY_TYPE.IMAGE,
          payload: null,
        };

        switch (type) {
          case DEPLOY_TYPE.IMAGE:
            deployParams = {
              type: DEPLOY_TYPE.IMAGE,
              payload: null,
            };
            imageConfig = { ...deployConfig };
            break;
          case DEPLOY_TYPE.REPOISTORY:
            deployParams = {
              type: DEPLOY_TYPE.REPOISTORY,
              payload: { ...deployConfig },
            };
            break;
          case DEPLOY_TYPE.WEB_PACKAGE:
            deployParams = {
              type: DEPLOY_TYPE.WEB_PACKAGE,
              payload: { ...deployConfig },
            };
            break;
        }
        const params = {
          imageConfig,
          effectiveImmediately,
          instanceConcurrency,
          caPort,
          timeout,
          nasConfig,
          ossMountConfig,
        };
        Object.assign(params, { cpu: spec.cpu, memorySize: spec.memory * 1024 });

        // 启动命令设置
        if (values.startCmd) {
          const { Command, CommandArgs } = values.startCmd;
          if (Command && CommandArgs?.length >= 2) {
            Object.assign(params, {
              command: JSON.stringify([Command]),
              args: JSON.stringify(CommandArgs),
            });
          }
        }

        // 环境变量
        if (values.environmentVariables) {
          Object.assign(params, { environmentVariables: objValueStringfy({ ...values.environmentVariables }) });
        }

        // hosts
        Object.assign(params, { customHostAlias: formatHostsWeb(hostsArr) });

        // 日志兼容老版本传参
        if (!this.state.showLogSelector && !_.isEmpty(logConfig)) {
          Object.assign(params, { logConfig: { ...logConfig } });
        }

        if (this.state.showLogSelector && !_.isEmpty(logTupleConfig)) {
          Object.assign(params, { logConfig: logTupleConfig?.logConfig });
          Object.assign(params, { slsConfig: logTupleConfig?.slsConfig });
        }

        // 应用健康检查设置
        Object.assign(params, { startupProbe: healthCheckConfig?.startupProbe });
        Object.assign(params, { livenessProbe: healthCheckConfig?.livenessProbe });
        // Object.assign(params, { enableAppMetric: armsConfig?.enableAppMetric });
        // if (_.get(armsConfig, 'enableAppMetric')) {
        //   Object.assign(params, { programmingLanguage: armsConfig?.programmingLanguage || 'java' });
        // }
        resolve({ params, deployParams });
      });
    });
  }

  deployValidation = (value) => {
    switch (value.type) {
      case DEPLOY_TYPE.IMAGE:
        return value.image
          ? true
          : intl('saenext.version-list.CreateVersion.CreateField.SelectAnImage');
      case DEPLOY_TYPE.REPOISTORY:
        return Object.keys(value.CodeConfig).length > 0 && Object.keys(value.BuildConfig).length > 0
          ? true
          : intl('saenext.version-list.CreateVersion.CreateField.SetTheSourceCodeContinuous');
      case DEPLOY_TYPE.WEB_PACKAGE:
      case DEPLOY_TYPE.MICRO_PACKAGE:
        return value.PackageUrl
          ? true
          : intl('saenext.version-list.CreateVersion.CreateField.PleaseConfigureTheCodePackage');
      default:
        return intl(
          'saenext.version-list.CreateVersion.CreateField.NoApplicationDeploymentMethodSelected',
        );
    }
  };

  onFieldChange = (value, item) => {
    if (item.name === 'startCmd') {
      this.commandEditorChange(item.value);
    }
  };

  commandEditorChange(val) {
    if (val && val.Command && val.Command === '/bin/bash') {
      this.setState({
        description: (
          <div
            style={{
              color: 'red',
            }}
          >
            {intl('saenext.version-list.CreateVersion.CreateField.ThisCommandLineToolIs')}
          </div>
        ),
      });
    } else {
      this.setState({
        description: '',
      });
    }
  }

  render() {
    const { style = {}, deployType } = this.props;
    const { initValues, showHealthCheck, showLogSelector, description } = this.state;

    const {
      hostsArr,
      healthCheckConfig: {
        livenessProbe = null,
        startupProbe = null,
      } = {},
      logTupleConfig: {
        slsConfig = {},
      } = {}
    } = initValues;

    return (
      <Form
        style={style}
        className="pb-xl"
        onChange={this.onFieldChange}
        ref={(c) => {
          if (c) {
            this.field = c.getInstance();
          }
        }}
      >
        <Collapse
          style={{ border: 'none' }}
          defaultExpandedKeys={['0', '1', '2', '3', '4', '5', '6', '7', '8']}
        >
          <Panel
            title={intl(
              'saenext.version-list.CreateVersion.CreateField.NewVersionBasicInformation',
            )}
            className="border"
          >
            <DeploySelectorField
              required
              appType="web"
              name="deployConfig"
              label={intl(
                'saenext.version-list.CreateVersion.CreateField.ApplicationDeploymentMethod',
              )}
              vpcId={this.props.vpcId}
              className="deploy-selector mb-s"
              allowTypes={[deployType]}
              defaultValue={{
                type: DEPLOY_TYPE.IMAGE,
                // @ts-ignore
                image: `registry.${this.regionId}.aliyuncs.com/sae-serverless-demo/sae-demo:web-express-helloworld-v1.0`,
                accelerationType: 'Default',
              }}
              validation={[
                {
                  type: 'customValidate',
                  param: this.deployValidation,
                },
              ]}
            />

            {
              deployType !== DEPLOY_TYPE.REPOISTORY ? (
                <RadioField
                  required
                  name="effectiveImmediately"
                  label={intl('saenext.version-list.CreateVersion.CreateField.NewVersionTrafficPolicy')}
                  {...fieldLayout}
                  direction="ver"
                  defaultValue={false}
                  dataSource={[
                    {
                      value: false,
                      text: intl(
                        'saenext.version-list.CreateVersion.CreateField.AfterTheDeploymentIsCompleted',
                      ),
                    },
                    {
                      value: true,
                      text:
                        intl('saenext.version-list.CreateVersion.CreateField.EnableTrafficAutomaticallyAfterDeployment') +
                        (this.props.deployType ===
                          DEPLOY_TYPE.REPOISTORY ?
                          intl('saenext.version-list.CreateVersion.CreateField.SourceCodeDeploymentDoesNot') :
                          ''
                        ),
                      disabled: this.props.deployType === DEPLOY_TYPE.REPOISTORY,
                    },
                  ]}
                />
              ) : null
            }

          </Panel>

          <Panel
            title={intl('saenext.version-list.CreateVersion.CreateField.FullHostingOfHttpTraffic')}
            className="border  mt-xl"
          >
            <NumberField
              required
              name="caPort"
              defaultValue={8080}
              label={intl('saenext.version-list.CreateVersion.CreateField.HttpListeningPort')}
              min={1}
              max={65536}
              {...fieldLayout}
              className={cls({ 'full24-width': true })}
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.version-list.CreateVersion.CreateField.EnterTheHttpListeningPort',
                  ),
                },
              ]}
            />

            <NumberField
              required
              name="timeout"
              label={intl('saenext.version-list.CreateVersion.CreateField.RequestTimeout')}
              {...fieldLayout}
              defaultValue={10}
              min={1}
              max={3600}
              className={cls({ 'full24-width': true })}
              innerAfter={
                <span>{intl('saenext.version-list.CreateVersion.CreateField.Seconds')}</span>
              }
              style={{ width: '100%' }}
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.version-list.CreateVersion.CreateField.PleaseFillInTheTimeout',
                  ),
                },
              ]}
            />
          </Panel>

          <Panel
            title={intl('saenext.version-list.CreateVersion.CreateField.CapacitySettings')}
            className="border mt-xl"
          >
            {/* @ts-ignore */}
            <CpuMemSelectorField
              required
              hasClear
              name="spec"
              label={intl('saenext.version-list.CreateVersion.CreateField.SingleInstanceType')}
              {...fieldLayout}
              dataSource={CpuMemDataSource}
              className={cls({ 'full24-width': true })}
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.version-list.CreateVersion.CreateField.SelectASingleInstanceType',
                  ),
                },
              ]}
            />

            <NumberField
              required
              name="instanceConcurrency"
              label={intl(
                'saenext.version-list.CreateVersion.CreateField.NumberOfConcurrentRequestsFor',
              )}
              {...fieldLayout}
              className={cls({ 'full24-width': true })}
              defaultValue={10}
              min={1}
              max={this.state.maxConcurrent}
              innerAfter={
                <span>{intl('saenext.version-list.CreateVersion.CreateField.Times')}</span>
              }
              validation={[
                {
                  type: 'required',
                  message: intl(
                    'saenext.version-list.CreateVersion.CreateField.EnterTheNumberOfConcurrent',
                  ),
                },
              ]}
            />
          </Panel>

          <Panel
            title={
              <span>
                {intl('saenext.version-list.CreateVersion.CreateField.StartCommandSettings')}
                <a onClick={(evt) => {
                  evt.stopPropagation();
                }} className='ml-l' target='_blank' href={CachedData.confLink('help:sae:set-startup-command')}>{intl('saenext.app-create.micro-app.AdvanceCreator.HowToSetStartupCommands')}</a>
              </span>
            }
            className="border mt-xl"
          >
            {/* @ts-ignore */}
            <DockerCommandEditorField
              // required
              popupStyle={{ marginLeft: -16, paddingRight: 16 }}
              name="startCmd"
              description={description}
            />
          </Panel>

          {/* <Panel
            title="应用监控"
            className="border mt-xl"
          >
            <ArmsMonitorField name="armsConfig" deployType={deployType} />
          </Panel> */}

          {showLogSelector ? (
            <Panel
              title={intl(
                'saenext.version-list.CreateVersion.CreateField.LogMonitoringMetricsSettings',
              )}
              className="border mt-xl"
            >
              <LogSelectorNewField
                className="w-100"
                isUpdate={true}
                name="logTupleConfig"
                style={{ marginBottom: 0 }}
                rootLogEnabled={this.props.rootLogEnabled}
                showSlsConfig={!_.isEmpty(slsConfig)}
                ref={(ref) => (this.logSelectorField = ref)}
              />
            </Panel>
          ) : (
            <Panel
              title={intl('saenext.version-list.CreateVersion.CreateField.LogConfiguration')}
              className="border mt-xl"
            >
              <LogSelectorOldField
                name="logConfig"
                logEnabled={CachedData.getOpenStatus('logStatus')}
                ref={(ref) => {
                  this.logSelectorField = ref;
                }}
              />
            </Panel>
          )}

          <Panel
            title={intl('saenext.version-list.CreateVersion.CreateField.EnvironmentVariables')}
            className="border mt-xl"
          >
            {/* @ts-ignore */}
            <EnvEditorField
              name="environmentVariables"
              validation={[
                {
                  type: 'customValidate',
                  param: (value, rule) => {
                    if (_.isEmpty(value)) {
                      return true;
                    }
                    for (const key in value) {
                      const val = value[key];
                      if (!key || !val) {
                        return intl(
                          'saenext.version-list.CreateVersion.CreateField.CompleteTheEnvironmentVariables',
                        );
                      }
                    }
                    return true;
                  },
                },
              ]}
            />
          </Panel>

          {hostsArr &&
            <Panel
              title={intl('saenext.app-create.micro-app.AdvanceCreator.HostsBindingSettings')}
              className="border mt-xl"
            >
              <CustomHostField
                name="hostsArr"
                validation={[
                  {
                    type: 'customValidate',
                    param: validateHostsWeb,
                  },
                ]}
              />
            </Panel>
          }

          {showHealthCheck && (livenessProbe || startupProbe) ? (
            <Panel
              title={intl(
                'saenext.version-list.CreateVersion.CreateField.ApplyHealthCheckSettings',
              )}
              className="border mt-xl"
            >
              <HealthCheckField
                name="healthCheckConfig"
                isUpdate={true}
                ref={(ref) => (this.healthCheckField = ref)}
              />
            </Panel>
          ) : null}

          <Panel
            title={intl('saenext.version-list.CreateVersion.CreateField.PersistentStorage')}
            className="border mt-xl"
          >
            <NasSelectorField
              name="nasConfig"
              type="version"
              formField={this.field.field}
              ref={(ref) => (this.nasSelectorField = ref)}
            />

            <div className="border-t mt-l mb-l" />
            <OssSelectorField name="ossMountConfig" ref={(ref) => (this.ossSelectorField = ref)} />
          </Panel>
        </Collapse>
      </Form>
    );
  }
}
