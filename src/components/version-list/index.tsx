import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Button,
  Icon,
  Table,
  DateTime,
  Actions,
  Dialog,
  Copy,
  Balloon,
  Message,
  Badge,
  StatusIndicator,
  CndTable
} from '@ali/cnd';
import Truncate from '@ali/cnd-truncate';
import CreateVersion from './version-create';
import TrafficConfPanel from './traffic-panel';
import services from '~/services';
import { getParams, setSearchParams, unshiftZero } from '~/utils/global';
import { Link } from 'dva/router';
import { each, orderBy, get, forEach, isEmpty, map, includes } from 'lodash';
import { getTimes } from '~/components/shared/TimeRangeSelector';
import getAppDomains from '~/utils/getAppDomains';
import { AES_CONSTANT, trackCore } from '~/tracker';
import InstanceStatus from '~/components/version-detail/instance-list/InstanceStatus';
import DeployLogPanel from './DeployLogPanel';
import DeployStatus from './DeployStatus';
import UpdateImagePanel from './UpdateImagePanel';
import UpdateRepoistoryPanel from './UpdateRepoistoryPanel';
import UpdatePackagePanel from './UpdatePackagePanel';
import { DEPLOY_TYPE, PIPELINE_STATUS } from '~/constants/application';
import CachedData from '~/cache/common';
import { C_REPOSITORY } from '~/components/shared/DeploySelectorField/constant';

const { LinkButton } = Actions;
const Expand = {
  Up: 'up',
  Down: 'down',
};

export default (props) => {
  const {
    match: {
      params: { regionId, id: applicationID },
    },
    history,
    location: { search }, // ?后参数
    setRefreshIndex: refreshWebApp,
    appData,
    deployType = DEPLOY_TYPE.IMAGE,
  } = props;
  const isInDebt = CachedData.getOpenStatus('inDebtStatus');
  const refPanel = useRef(null);
  const refTable = useRef(null);
  const refLog = useRef(null);
  const refImage = useRef(null);
  const refRepoistory = useRef(null);
  const refPackage = useRef(null);
  const timerRef = useRef(null);
  const timerBuildRef = useRef(null);
  const timeInitValue = 'last_15_minutes';
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [appVersions, setAppVersions] = useState([]);
  const [versionId, setVersionId] = useState<string | null>();
  const [scaleConfig, setScaleConfig] = useState({
    minimumInstanceCount: 0,
    maximumInstanceCount: 0,
    alwaysAllocateCPU: false,
  });
  const [factsInstance, setFactsInstance] = useState(new Map());
  const [domainList, setDomainList] = useState([]);
  const [isLoop, setIsLoop] = useState(false);
  const applicationName = getParams('name');
  const latestScaleConfig = useRef(null);
  const [deployStatus, setDeployStatus] = useState({});
  const [factsBuildRecord, setFactsBuildRecord] = useState(new Map());
  const [runPipeline, setRunPipeline] = useState([]);
  const [failPipeline, setFailPipeline] = useState([]);
  const [successPipeline, setSuccessPipeline] = useState([]);
  const [badgeCount, setBadgeCount] = useState(0);
  const [badgeColor, setBadgeColor] = useState('#c82727');
  const [currentError, setCurrentError] = useState('');
  const [collapsed, setCollapsed] = useState(false);

  // 创建的新版本的Id
  const newVersionId = useRef(0);

  const action = getParams('action');
  useEffect(() => {
    if (action === 'create') {
      showCreateVersion();
      setSearchParams({ action: '' });
      const { pathname, search } = window.location;
      history.replace(pathname + search);
    }
  }, []);

  useEffect(() => {
    // 组合接口获取版本列表信息

    async function getVersionList() {
      setLoading(true);
      await getApplicationVersions();
      setLoading(false);
    }

    getVersionList();
    // 当前实例数
    getAppVersionInstances();
    // 构建记录
    getAppVersionBuildRecord();
  }, [refreshIndex]);

  useEffect(() => {
    // 获取应用域名
    getAppAccessDomain();
    // 实例数范围
    getAppInstancesRange();

    return () => {
      setClearTimer();
      clearInterval(timerBuildRef?.current);
      timerBuildRef.current = null;
    };
  }, []);

  const getApplicationVersions = async () => {
    const data = await services.getListAppVersions({
      applicationID,
      applicationName,
      limit: 100,
    });
    let { versions = [] } = data;
    versions = orderBy(
      versions,
      [
        (item) => {
          const { weight } = item;
          return weight || 0;
        },
      ],

      ['desc'],
    );
    setAppVersions(versions);
    forEach(versions, (item) => {
      const { versionId } = item;
      const _versionId = parseInt(versionId, 10);
      if (_versionId > newVersionId.current) {
        newVersionId.current = _versionId;
      }
    });
    newVersionId.current += 1;
  };

  const getAppAccessDomain = async () => {
    const result = await getAppDomains(applicationID, applicationName);
    setDomainList(result);
  };

  // 提前获取获取应用扩缩实例范围
  const getAppInstancesRange = async () => {
    const data = await services.getApplicationScale({
      applicationID,
      applicationName,
    });
    setScaleConfig(data);
    latestScaleConfig.current = data;
  };

  // 提前获取获取应用所有实例
  const getAppVersionInstances = async (loopConf?: any) => {
    // 版本列表 默认获取15分钟之内的所有运行中实例
    const [startTime, endTime] = getTimes(timeInitValue);
    const data = await services.getAppVersionInstances({
      applicationName,
      startTime,
      endTime,
      applicationID,
    });
    const { instances = [], versionStatus = {}, currentError } = data;
    const _factsInstance = new Map();

    each(instances, (instance) => {
      const { versionId, status } = instance;
      const isExist = _factsInstance.has(versionId);
      const instances = isExist ? _factsInstance.get(versionId) : {};
      const total = get(instances, 'total', []);
      const idle = get(instances, 'idle', []);
      const busy = get(instances, 'busy', []);
      total.push(instance);
      if (status === 'Idle') {
        idle.push(instance);
      }
      if (status === 'Busy') {
        busy.push(instance);
      }
      instances.total = total;
      instances.idle = idle;
      instances.busy = busy;
      _factsInstance.set(versionId, instances);
    });

    setCurrentError(currentError);
    setDeployStatus(versionStatus);
    setFactsInstance(_factsInstance);

    if (loopConf) {
      const { versionWeight, scaleConfig } = loopConf;
      // 版本的实例数与状态是否对应，且未最终态
      let versionIsEnd = true;
      each(versionStatus, ({ status }, versionId) => {
        // 版本状态为更新中
        if (status === 'Updating') {
          versionIsEnd = false;
        }

        // 实例已清除，状态已完成，还未清除状态
        if (!_factsInstance.get(versionId)?.total?.length && status === 'Completed') {
          versionIsEnd = false;
        }
      });

      _factsInstance.forEach((instance: any, versionId) => {
        // 实例刚生成，还没有状态
        if (instance?.total?.length > 0 && !versionStatus[versionId]) {
          versionIsEnd = false;
        }
      });

      each(versionWeight, (item) => {
        const { versionId, weight } = item;
        // 版本配置了流量，但是没实例
        if (
          scaleConfig.minimumInstanceCount > 0 &&
          weight > 0 &&
          !_factsInstance.get(versionId)?.total?.length
        ) {
          versionIsEnd = false;
        }
        // 版本的流量配置已去掉，但是实例还未清除
        if (
          (scaleConfig.minimumInstanceCount === 0 || !weight) &&
          _factsInstance.get(versionId)?.total?.length
        ) {
          versionIsEnd = false;
        }
      });
      if (!versionIsEnd) {
        // 版本状态未结束，不结束轮询
        return;
      }

      const currentInstances = instances.length;
      const { loopTraffic, loopScale } = loopConf;
      if (loopTraffic) {
        // 流量配置
        if (currentInstances >= latestScaleConfig.current?.minimumInstanceCount) {
          setClearTimer();
        }
      }

      if (loopScale && loopScale === Expand.Up) {
        // 扩容
        // 当instances数量 >= 最小实例数范围时停止轮询
        if (currentInstances >= latestScaleConfig.current?.minimumInstanceCount) {
          setClearTimer();
        }
      }

      if (loopScale && loopScale === Expand.Down) {
        // 缩容
        // 当instances数量 <= 最小实例数范围时停止轮询
        if (currentInstances <= latestScaleConfig.current?.minimumInstanceCount) {
          setClearTimer();
        }
      }
    }
  };

  // 获取构建记录跟版本一一对应 显示代码包和 仓库地址
  const getAppVersionBuildRecord = async () => {
    const res = await services.listBuildPipelineRuns({
      ApplicationId: applicationID,
      ApplicationName: applicationName,
      PageNo: 1,
      PageSize: 100,
    });
    const { Items: buildList = [] } = res;
    const _factsBuildRecord = new Map();
    // 当构建记录 有运行中 开启轮训
    const _runPipeline = [];
    let _failPipeline = [];
    let _successPipeline = [];
    const latestRecord = buildList[0];
    const runStatus = [PIPELINE_STATUS.RUNNING, PIPELINE_STATUS.WAIT];
    const failStatus = [PIPELINE_STATUS.FAIL, PIPELINE_STATUS.CANCEL];
    each(buildList, (record) => {
      const { VersionId, Status } = record;
      _factsBuildRecord.set(VersionId, record);
      if (includes(runStatus, Status)) {
        _runPipeline.push(record);
      }
      if (includes(failStatus, Status)) {
        _failPipeline.push(record);
      }
      if (Status === PIPELINE_STATUS.SUCCESS) {
        _successPipeline.push(record);
      }
    });
    const _badgeCount = _failPipeline.length || _successPipeline.length;
    const _badgeColor = _failPipeline.length ? '#c82727' : '#29a64e';
    setFactsBuildRecord(_factsBuildRecord);
    setRunPipeline(_runPipeline);
    setBadgeCount(_badgeCount);
    setBadgeColor(_badgeColor);
    setFailPipeline(_failPipeline);
    setSuccessPipeline(_successPipeline);

    if (_runPipeline.length > 0) {
      if (timerBuildRef?.current) return;
      timerBuildRef.current = setInterval(() => {
        getAppVersionBuildRecord();
      }, 1000 * 1.5);
    }
    if (isEmpty(_runPipeline) && timerBuildRef?.current) {
      clearInterval(timerBuildRef.current);
      timerBuildRef.current = null;
      // 说明开启过定时器 这时如果第一条为成功 更新版本列表
      if (latestRecord?.Status === PIPELINE_STATUS.SUCCESS) {
        getApplicationVersions();
      }
    }
  };

  const showCreateVersion = () => {
    trackCore({
      behavior: AES_CONSTANT.CORE_BEHAVIOR_TYPE.VERSION,
      stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
    });
    setVersionId(null);
    refPanel?.current.show();
  };

  const showTrafficConf = () => {
    trackCore({
      behavior: AES_CONSTANT.CORE_BEHAVIOR_TYPE.TRAFFIC,
      stage: AES_CONSTANT.AES_STAGE_TYPE.TRIGGER,
    });
    refTable?.current.show();
  };

  const updateAppVersion = (version) => {
    const { versionId } = version;
    setVersionId(versionId);
    refPanel?.current.show();
  };

  const updateAppImage = (version) => {
    const { versionId } = version;
    setVersionId(versionId);
    refImage?.current.show();
  };

  const updateAppRepoistory = (version) => {
    const { versionId } = version;
    setVersionId(versionId);
    refRepoistory?.current.show();
  };

  const updateAppPackage = (version) => {
    const { versionId } = version;
    setVersionId(versionId);
    refPackage?.current.show();
  };

  const deleteAppVersion = (version) => {
    const { versionId } = version;
    Dialog.confirm({
      title: intl('saenext.components.version-list.DeleteVersion'),
      content: (
        <div style={{ width: 460 }} className="text-line">
          {intl.html('saenext.components.version-list.DeleteThisVersion', {
            versionId: unshiftZero(versionId),
          })}
        </div>
      ),

      messageProps: {
        type: 'warning',
      },
      okProps: { children: intl('saenext.components.version-list.Delete') },
      onOk: () => {
        return services
          .deleteAppVersion({
            applicationID,
            applicationName,
            versionID: versionId,
          })
          .then(() => {
            setRefreshIndex(Date.now());
          });
      },
    });
  };

  const deleteBatchAppVersion = (selectedRowKeys) => {
    Dialog.confirm({
      title: intl('saenext.components.version-list.BatchDeleteVersions'),
      content: (
        <div style={{ width: 460 }} className="text-line">
          {intl('saenext.components.version-list.AfterDeletionTheSystemCannot')}
        </div>
      ),

      messageProps: {
        type: 'warning',
      },
      okProps: { children: intl('saenext.components.version-list.Delete') },
      onOk: () => {
        return services
          .deleteBatchAppVersion({
            applicationID,
            applicationName,
            versionIds: JSON.stringify(selectedRowKeys),
          })
          .then((res) => {
            if (isEmpty(res)) {
              setRefreshIndex(Date.now());
            }
          });
      },
    });
  };

  // 启动一个计时器 用来刷新版本的当前实例数
  const setStartTimer = (updateConf) => {
    setClearTimer();
    setIsLoop(true);
    const { isDiffTraff, isDiffScale, versionWeight, scaleConfig } = updateConf;
    const loopConf = { loopTraffic: false, loopScale: false, versionWeight, scaleConfig };
    if (isDiffTraff) {
      // 更新了 流量配比
      Reflect.set(loopConf, 'loopTraffic', true);
    }
    if (isDiffScale) {
      // 更新了 实例数范围
      const { scaleConfig: _scaleConfig } = updateConf;
      latestScaleConfig.current = _scaleConfig;
      const isExpandCapacity =
        _scaleConfig.minimumInstanceCount >= scaleConfig.minimumInstanceCount
          ? Expand.Up
          : Expand.Down;
      setScaleConfig(_scaleConfig);
      // 当同时更新之后 实例数范围优先级高
      Reflect.set(loopConf, 'loopTraffic', false);
      Reflect.set(loopConf, 'loopScale', isExpandCapacity);
    }
    refreshWebApp && refreshWebApp(Date.now());
    timerRef.current = setInterval(() => {
      getAppVersionInstances(loopConf);
    }, 1000 * 1.5);
  };

  const setClearTimer = () => {
    if (timerRef?.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setIsLoop(false);
    }
  };

  const cellProps = (_, __, dataIndex) => {
    if (dataIndex === 'ScaleConfig') {
      return {
        colSpan: 1,
        rowSpan: appVersions.length,
        style: {
          borderLeft: '1px solid #E3E4E6',
          borderRight: '1px solid #E3E4E6',
        },
      };
    }
  };

  const onInstanceShell = (versionId, instanceId) => {
    const uri = `/${regionId}/monitor/${applicationID}/${versionId}/${instanceId}${search}`;
    window.open(uri, '_blank');
  };

  const onInstanceLog = (versionId) => {
    setVersionId(versionId);
    refLog?.current.show();
  };

  const onInstanceMonitor = (versionId) => {
    history.push(`/${regionId}/app-list/${applicationID}/version/${versionId}/monitor${search}`);
  };

  const rowSelection = {
    mode: 'multiple',
    getProps: (record) => {
      const weight = get(record, 'weight', 0);
      if (weight > 0) {
        return { disabled: true };
      }
      return { disabled: false };
    },
  };

  return (
    <>
      {isInDebt ? (
        <Message
          type="error"
          className="mb-l"
          title={intl('saenext.components.version-list.AccountExceptionReminder')}
        >
          {intl('saenext.components.version-list.ThisMayBeBecauseYou')}
        </Message>
      ) : null}
      {!isEmpty(currentError) ? (
        <Message
          type="error"
          className="mb-s"
          title={
            <div className="flex pointer" onClick={() => setCollapsed(!collapsed)}>
              <span className="mr-xs">
                {intl('saenext.components.version-list.ApplicationStartupFailed')}
              </span>
              <span>
                {collapsed ? <Icon type="up" size="small" /> : <Icon type="down" size="small" />}
              </span>
            </div>
          }
        >
          {collapsed ? <pre style={{ margin: 0 }}>{currentError}</pre> : null}
        </Message>
      ) : null}

      <CndTable
        loading={loading}
        dataSource={appVersions}
        pagination={false}
        operation={
          <div>
            <Button className="mr-s" type="primary" disabled={isInDebt} onClick={showCreateVersion}>
              {intl('saenext.components.version-list.CreateANewVersion')}
            </Button>
            <Button className="mr-s" disabled={isInDebt} onClick={showTrafficConf}>
              {intl('saenext.components.version-list.TrafficConfiguration')}
            </Button>
            <Balloon
              trigger={
                <Button>
                  {intl('saenext.components.version-list.AccessApplications')}
                  <Icon type="external_link" />
                </Button>
              }
              triggerType="hover"
            >
              {domainList.map(domain => (
                <div>
                  <Copy text={domain}>
                    <a href={domain} target="_blank" className="block mb-xs">
                      {domain}
                    </a>
                  </Copy>
                </div>
              ))}
            </Balloon>
            {deployType && deployType !== DEPLOY_TYPE.IMAGE ? (
              <Badge count={badgeCount} className="ml-s" style={{ backgroundColor: badgeColor }}>
                <Balloon
                  trigger={
                    <Button
                      onClick={() => {
                        history.push(
                          `/${regionId}/app-list/${applicationID}/web-app/structure${search}`,
                        );
                      }}
                    >
                      {intl('saenext.components.version-list.BuildRecords')}

                      <Icon type="chart_bar" />
                    </Button>
                  }
                  triggerType="hover"
                >
                  <>
                    <div className="flex mb-xs">
                      <StatusIndicator shape="dot" type="error" className="mr-xs" />
                      <span style={{ color: '#666 ' }}>
                        {intl('saenext.components.version-list.NumberOfPipelineBuildFailures')}
                      </span>
                      <span className="ml-l" style={{ color: '#333 ' }}>
                        {failPipeline.length}
                      </span>
                    </div>
                    <div className="flex mb-xs">
                      <StatusIndicator shape="dot" type="success" className="mr-xs" />
                      <span style={{ color: '#666 ' }}>
                        {intl('saenext.components.version-list.NumberOfSuccessfulAssemblyLines')}
                      </span>
                      <span className="ml-l" style={{ color: '#333 ' }}>
                        {successPipeline.length}
                      </span>
                    </div>
                    <div className="flex">
                      {intl('saenext.components.version-list.YouCanUse')}

                      <LinkButton
                        className="ml-xs mr-xs"
                        onClick={() => {
                          history.push(
                            `/${regionId}/app-list/${applicationID}/web-app/structure${search}`,
                          );
                        }}
                      >
                        {intl('saenext.components.version-list.BuildRecords')}
                      </LinkButton>
                      {intl('saenext.components.version-list.ViewPipelineExecutionLogsAnd')}
                    </div>
                  </>
                </Balloon>
              </Badge>
            ) : null}
          </div>
        }
        secondaryOperation={
          <Button className="mb-s" onClick={() => setRefreshIndex(Date.now())}>
            <Icon type="refresh" />
          </Button>
        }
        search={{
          afterFilterRender: (
            <>
              {isLoop ? (
                <Message type="warning" className="mb-s">
                  <div className="text-line">
                    {intl('saenext.components.version-list.TheCurrentNumberOfInstances')}
                  </div>
                </Message>
              ) : null}

              {!isEmpty(runPipeline) ? (
                <Message type="warning" className="mb-s">
                  <div className="text-line">
                    {intl('saenext.components.version-list.YourCurrentAssemblyLine')}

                    <span className="ml-xs mr-xs">
                      {map(runPipeline, record => (
                        <span>{record?.PipelineRunId}</span>
                      ))}
                    </span>
                    {intl('saenext.components.version-list.TheStatusIsRunning')}

                    <Icon type="loading" size="xs" className="ml-xs" />
                    {intl('saenext.components.version-list.BuildingANewVersionYou')}
                    <LinkButton
                      className="ml-xs mr-xs"
                      onClick={() => {
                        history.push(
                          `/${regionId}/app-list/${applicationID}/web-app/structure${search}`,
                        );
                      }}
                    >
                      {intl('saenext.components.version-list.BuildRecords')}
                    </LinkButton>
                    {intl('saenext.components.version-list.ViewPipelineExecutionLogsAnd')}
                  </div>
                </Message>
              ) : null}

              <Message type="notice" className="mb-s">
                <div className="text-line">
                  {intl('saenext.components.version-list.ASingleApplicationCanRetain')}
                </div>
                <div className="text-line">
                  {intl('saenext.components.version-list.WhenTheApplicationTrafficFluctuates')}
                </div>
              </Message>
            </>
          ),
        }}
        cellProps={cellProps}
        primaryKey="versionId"
        // @ts-ignore
        rowSelection={rowSelection}
        // @ts-ignore
        columns={[
          {
            title: intl('saenext.components.version-list.VersionName'),
            dataIndex: 'versionId',
            lock: 'left',
            width: 100,
            cell: value => {
              return (
                // @ts-ignore
                <Link
                  to={`/${regionId}/app-list/${applicationID}/version/${value}/configure${search}`}
                >
                  <Copy text={unshiftZero(value)} style={{ minWidth: 80 }}>
                    {unshiftZero(value)}
                  </Copy>
                </Link>
              );
            },
          },
          {
            title: intl('saenext.components.version-list.Status'),
            dataIndex: 'versionId',
            width: 140,
            cell: (value, index, record) => {
              let _deployStatus = deployStatus[value];
              // 特殊情况 流量非0的版本，如果当前实例数大于0 versionStatus不存在该版本，状态就展示 运行中
              if (!_deployStatus) {
                const weight = record.weight;
                const instances = factsInstance.get(value) || {};
                const { total = [] } = instances;
                if (total.length > 0 && weight > 0) {
                  _deployStatus = { status: 'Completed', errorMessage: '' };
                }
              }
              return (
                <div style={{ minWidth: 120 }}>
                  <DeployStatus
                    value={_deployStatus}
                    scalingFailedcb={() => {
                      history.push(`/operations-management/event-center?scalingFailed=true`);
                    }}
                  />
                </div>
              );
            },
          },
          {
            title: intl('saenext.components.version-list.TargetTraffic'),
            dataIndex: 'weight',
            width: 100,
            cell: value => {
              let _weight = '';
              if (value) {
                const _value = value.toFixed(2);
                const [integer, decimals] = _value.split('.');
                if (parseInt(integer, 10)) {
                  _weight = '100';
                } else {
                  _weight = parseInt(decimals, 10).toFixed();
                }
              }
              return (
                <span style={{ display: 'block', minWidth: 80 }}>
                  {_weight ? `${_weight}%` : '-'}
                </span>
              );
            },
          },
          {
            title: (
              <div>
                {deployType === DEPLOY_TYPE.IMAGE ? (
                  <span className="pl-l">{intl('saenext.components.version-list.Image')}</span>
                ) : deployType === DEPLOY_TYPE.WEB_PACKAGE ? (
                  <span>{intl('saenext.components.version-list.Package')}</span>
                ) : (
                  <span>
                    {intl('saenext.components.version-list.SourceCodeRepositoryCommitId')}
                  </span>
                )}
              </div>
            ),

            dataIndex: 'image',
            width: 260,
            cell: (value, __, record) => {
              const { versionId } = record;
              const buildRecord = factsBuildRecord.get(versionId) || {};
              switch (deployType) {
                case DEPLOY_TYPE.IMAGE:
                  return (
                    <div className="flex">
                      <Truncate threshold={198} type="width" position="start">
                        {value}
                      </Truncate>
                      <Copy text={value} />
                    </div>
                  );

                case DEPLOY_TYPE.WEB_PACKAGE:
                  const { PackageConfig = {} } = buildRecord;
                  return (
                    <div className="flex">
                      {isEmpty(PackageConfig)
                        ? '--'
                        : intl(
                            'saenext.components.version-list.VersionPackageconfigpackageversionPackageNamePackageconfigpackagename',
                            {
                              PackageConfigPackageVersion: PackageConfig.PackageVersion,
                              PackageConfigPackageName: PackageConfig.PackageName,
                            },
                          )}
                    </div>
                  );

                case DEPLOY_TYPE.REPOISTORY:
                  const { CodeConfig = {} } = buildRecord;
                  return (
                    <>
                      {isEmpty(CodeConfig) ? (
                        '--'
                      ) : (
                        <div className="flex">
                          {C_REPOSITORY[CodeConfig.Provider]?.svg()}

                          <div className="ml-xs">
                            <div style={{ marginBottom: 2 }}>{CodeConfig.RepoFullName}</div>
                            <Truncate threshold={150} type="width" position="middle">
                              <a href={`${CodeConfig.CommitUrl}`} target="_blank">
                                {CodeConfig.CommitId}
                              </a>
                            </Truncate>
                          </div>
                        </div>
                      )}
                    </>
                  );
              }
            },
          },
          {
            title: intl('saenext.components.version-list.NumberOfCurrentInstancesWith'),
            dataIndex: 'versionId',
            width: 190,
            cell: value => {
              const instances = factsInstance.get(value) || {};
              const { total = [], idle = [], busy = [] } = instances;
              return (
                <span>
                  {total.length ? `${total.length} (${busy.length} / ${idle.length})` : 0}
                </span>
              );
            },
          },
          {
            title: intl('saenext.components.version-list.NumberOfInstancesTotal'),
            dataIndex: 'ScaleConfig',
            width: 140,
            cell: () => {
              return (
                <>
                  <span>{scaleConfig?.minimumInstanceCount}</span>
                  <span className="ml-s mr-s">-</span>
                  <span>{scaleConfig?.maximumInstanceCount}</span>
                </>
              );
            },
          },
          {
            title: intl('saenext.components.version-list.CreationTime'),
            dataIndex: 'createdTime',
            width: 190,
            cell: value => <DateTime value={value} />,
          },
          {
            title: intl('saenext.components.version-list.Operation'),
            width: 280,
            lock: 'right',
            cell: (value, index, record) => {
              const instances = factsInstance.get(record.versionId) || {};
              const { total = [] } = instances || {};
              return (
                <>
                  {/* @ts-ignore */}
                  <Actions threshold={4}>
                    {/* @ts-ignore */}
                    <LinkButton onClick={() => onInstanceLog(record.versionId)}>
                      {intl('saenext.components.version-list.Log')}
                    </LinkButton>
                    {/* @ts-ignore */}
                    <LinkButton onClick={() => onInstanceMonitor(record.versionId)}>
                      {intl('saenext.components.version-list.Monitoring')}
                    </LinkButton>
                    {deployType === DEPLOY_TYPE.IMAGE ? (
                      <LinkButton disabled={isInDebt} onClick={() => updateAppImage(record)}>
                        {intl('saenext.components.version-list.ConfigureImages')}
                      </LinkButton>
                    ) : null}

                    {deployType === DEPLOY_TYPE.WEB_PACKAGE ? (
                      <LinkButton disabled={isInDebt} onClick={() => updateAppPackage(record)}>
                        {intl('saenext.components.version-list.ConfigureCodePackage')}
                      </LinkButton>
                    ) : null}

                    {deployType === DEPLOY_TYPE.REPOISTORY ? (
                      <LinkButton disabled={isInDebt} onClick={() => updateAppRepoistory(record)}>
                        {intl('saenext.components.version-list.ConfigureSourceCodeRepository')}
                      </LinkButton>
                    ) : null}

                    <Balloon
                      closable={false}
                      align="l"
                      trigger={<LinkButton disabled={!total.length}>Webshell</LinkButton>}
                      triggerType="hover"
                    >
                      <>
                        {total.length &&
                          total.map(instance => {
                            const { versionId, instanceId, status } = instance;
                            return (
                              <div className="flex mb-s">
                                <LinkButton
                                  style={{ minWidth: 242 }}
                                  onClick={() => onInstanceShell(versionId, instanceId)}
                                >
                                  {instanceId}
                                </LinkButton>
                                <InstanceStatus value={status} />
                              </div>
                            );
                          })}
                      </>
                    </Balloon>
                    <LinkButton disabled={isInDebt} onClick={() => updateAppVersion(record)}>
                      {intl('saenext.components.version-list.Edit')}
                    </LinkButton>
                    <LinkButton disabled={record.weight} onClick={() => deleteAppVersion(record)}>
                      {intl('saenext.components.version-list.Delete')}
                    </LinkButton>
                  </Actions>
                </>
              );
            },
          },
        ]}
        selection={({ selectedRowKeys }: { selectedRowKeys: any[] }) => {
          return (
            <div>
              <Button
                type="primary"
                disabled={selectedRowKeys.length === 0}
                onClick={() => {
                  deleteBatchAppVersion(selectedRowKeys);
                }}
              >
                {intl('saenext.components.version-list.DeleteApplicationVersionsInBatches')}
              </Button>
              <span className="ml">
                {intl('saenext.components.version-list.SelectedrowkeyslengthApplicationsSelected', {
                  selectedRowKeysLength: selectedRowKeys.length,
                })}
              </span>
            </div>
          );
        }}
      />

      <CreateVersion
        ref={refPanel}
        history={history}
        urlSearch={search}
        appData={appData}
        regionId={regionId}
        appVersionId={versionId}
        deployType={deployType}
        newVersionId={newVersionId.current.toString()}
        applicationID={applicationID}
        setRefreshIndex={setRefreshIndex}
        refreshWebApp={() => refreshWebApp(Date.now())}
      />

      <TrafficConfPanel
        ref={refTable}
        scaleConfig={scaleConfig}
        appVersions={appVersions}
        applicationID={applicationID}
        setRefreshIndex={setRefreshIndex}
        setStartTimer={setStartTimer}
        setScaleConfig={setScaleConfig}
      />

      <DeployLogPanel
        ref={refLog}
        appData={appData}
        regionId={regionId}
        appVersionId={versionId}
        applicationID={applicationID}
      />

      <UpdateImagePanel
        ref={refImage}
        regionId={regionId}
        appVersionId={versionId}
        applicationID={applicationID}
        setRefreshIndex={setRefreshIndex}
      />

      <UpdateRepoistoryPanel
        ref={refRepoistory}
        history={history}
        urlSearch={search}
        regionId={regionId}
        appVersionId={versionId}
        applicationID={applicationID}
        setRefreshIndex={setRefreshIndex}
      />

      <UpdatePackagePanel
        ref={refPackage}
        history={history}
        urlSearch={search}
        regionId={regionId}
        appVersionId={versionId}
        applicationID={applicationID}
        setRefreshIndex={setRefreshIndex}
      />
    </>
  );
};
