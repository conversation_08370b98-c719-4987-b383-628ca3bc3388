import { intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import {
  SlidePanel,
  Message,
  Actions,
  Select,
  Button,
  Icon,
  useRoaApi,
  Dialog,
  Loading,
} from '@ali/cnd';
import TimeRangeSelector, { getTimes } from '~/components/shared/TimeRangeSelector';
import _ from 'lodash';
import { LogEditor } from '@ali/cnd';
import { getParams, unshiftZero } from '~/utils/global';
import services from '~/services';
import InstanceStatus from '../version-detail/instance-list/InstanceStatus';

const { LinkButton } = Actions;

type Props = {
  regionId: string;
  appVersionId: string;
  applicationID: string;
  appData: AppItem;
};

export default forwardRef((props: Props, ref) => {
  const { regionId, appData, appVersionId, applicationID } = props;

  const applicationName = getParams('name');
  const timeInitValue = 'last_15_minutes';
  const [start, end] = getTimes(timeInitValue);
  const [startTime, setStartTime] = useState(start);
  const [endTime, setEndTime] = useState(end);
  const [visible, setVisible] = useState(false);
  const [isOpensls, setIsOpensls] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [instanceId, setInstanceId] = useState('');
  const [instancesData, setInstancesData] = useState([]);
  const [value, setVaule] = useState();
  const child = useRef(null);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [selectedTimeKey, updateSelectedTimeKey] = useState(timeInitValue);

  const params = { applicationID, applicationName };
  const content = {
    applicationID,
    applicationName,
    startTime,
    endTime,
    versionId: appVersionId,
    instanceId,
  };
  const { run } = useRoaApi(
    'serverless',
    'GetWebApplicationLogs',
    { params, content },
    { manual: true, disableErrorPrompt: true },
  );

  useEffect(() => {
    if (!visible || _.isEmpty(appVersionId)) return;
    initInstanceLog();
  }, [refreshIndex, appVersionId, visible]);

  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }));

  const initInstanceLog = async () => {
    setIsLoading(true);
    const { logConfig = { logstore: '', project: '' } } = appData;
    const { logstore, project } = logConfig;
    const _isOpensls = !!(logstore && project);
    setIsOpensls(_isOpensls);

    const _instancesData = await getAppVersionInstances();
    const instance = _instancesData[0];
    if (instance) {
      const _instanceId = instance.value;
      setInstanceId(_instanceId);
      getInstanceLogData({
        params,
        content: { ...content, instanceId: _instanceId },
      });
    }
    setIsLoading(false);
  };

  const getAppVersionInstances = async () => {
    const data = await services.getAppVersionInstances({
      applicationID,
      applicationName,
      startTime,
      endTime,
      qualifier: appVersionId,
    });
    const { instances = [] } = data;
    const instancesData = _.map(instances, (instance) => ({
      value: instance.instanceId,
      label: (
        <>
          <InstanceStatus value={instance.status} />
          <span style={{ marginLeft: 10 }}>{instance.instanceId}</span>
        </>
      ),
    }));
    setInstancesData(instancesData);
    return instancesData;
  };

  const getInstanceLogData = async (params) => {
    const data = await run(params);

    if (data instanceof Error) {
      onError(data);
      return;
    }

    const logData = _.map(data?.logEntrys, (val) => val.message.replace(/\/r$/, ''));
    const _value = logData.join('');
    // @ts-ignore
    setVaule(_value);
  };

  const onLogTimeChange = (value) => {
    const { start, end, key } = value;
    setEndTime(end);
    setStartTime(start);
    updateSelectedTimeKey(key);
    setRefreshIndex(Date.now());
  };

  const onInstanceChange = async (_instanceId) => {
    setIsLoading(true);
    setInstanceId(_instanceId);
    // @ts-ignore
    setVaule('');
    await getInstanceLogData({
      params,
      content: { ...content, instanceId: _instanceId },
    });
    setIsLoading(false);
  };

  const refreshLog = () => {
    const [start, end] = getTimes(selectedTimeKey);
    if (isOpensls) {
      child?.current.onChild([start, end]);
    }
    setEndTime(end);
    setStartTime(start);
    setRefreshIndex(Date.now());
  };

  const onError = (error) => {
    const { code } = error;

    if (code === 'IndexConfigNotExist' || code === 'IndexForInstanceMetricsNotExists') {
      Dialog.alert({
        title: intl('saenext.components.version-list.DeployLogPanel.QueryFailed'),
        content: (
          <>
            <p>{intl('saenext.components.version-list.DeployLogPanel.TheIndexInTheLogstore')}</p>
            <p className="color-orange">
              {intl('saenext.components.version-list.DeployLogPanel.YouCanClickTheFix')}
            </p>
          </>
        ),

        onOk: createLogIndex,
        okProps: { children: intl('saenext.components.version-list.DeployLogPanel.FixNow') },
      });
    }
  };

  const createLogIndex = async () => {
    const promise = new Promise(async (resolve, reject) => {
      try {
        const {
          logConfig: { project, logstore },
        } = appData || {};
        const result = await services.CreateWebLogIndex({
          params: {
            applicationID,
            applicationName,
          },
          content: {
            project,
            logstore,
          },
        });
        if (result) {
          Dialog.confirm({
            title: intl('saenext.components.version-list.DeployLogPanel.FixedSuccessfully'),
            className: 'fc-dialog-body',
            content: (
              <div className="mt-s" style={{ lineHeight: '22px' }}>
                {intl(
                  'saenext.components.version-list.DeployLogPanel.CongratulationsYourConfigurationHasBeen',
                )}
                <br />
                <span className="color-orange">
                  {intl(
                    'saenext.components.version-list.DeployLogPanel.NoteTheRepairOperationTakes',
                  )}
                </span>
              </div>
            ),

            okProps: { children: intl('saenext.components.version-list.DeployLogPanel.Confirm') },
            footerActions: ['ok'],
          });
          resolve(result);
        } else {
          reject();
        }
      } catch (err) {
        reject(err);
      }
    });
    return promise;
  };

  const handleClose = () => {
    setVisible(false);
    // @ts-ignore
    setVaule();
    setInstanceId('');
  };

  const onInstanceShell = () => {
    if (!instanceId) return;
    const uri = `/${regionId}/monitor/${applicationID}/${appVersionId}/${instanceId}?name=${applicationName}`;
    window.open(uri, '_blank');
  };

  const version = unshiftZero(appVersionId);

  return (
    <SlidePanel
      title={intl('saenext.components.version-list.DeployLogPanel.VersionVersionLogs', {
        version: version,
      })}
      isShowing={visible}
      onClose={handleClose}
      width={1200}
    >
      <>
        {!isOpensls ? (
          <Message type="notice" className="mb-s">
            {intl('saenext.components.version-list.DeployLogPanel.AMaximumOfLogsAt')}

            <LinkButton className="ml-s mr-s" disabled={!instanceId} onClick={onInstanceShell}>
              Webshell
            </LinkButton>
            {intl('saenext.components.version-list.DeployLogPanel.ViewLogsOnline')}
          </Message>
        ) : null}

        <div className="log-panel" style={{ marginTop: !isOpensls ? 8 : 0 }}>
          <div style={{ flexWrap: 'wrap' }} className="flex full-width">
            {isOpensls ? (
              <TimeRangeSelector
                onRef={child}
                defaultTime={false}
                timeInitValue={timeInitValue}
                periodInitValue={60}
                onTimeChanged={onLogTimeChange}
              />
            ) : null}

            <Select
              label={intl('saenext.components.version-list.DeployLogPanel.VersionInstance')}
              className="mb-s"
              style={{ minWidth: 400 }}
              value={instanceId}
              dataSource={instancesData}
              onChange={onInstanceChange}
            />
          </div>
          <Button className="ml-l" onClick={refreshLog}>
            <Icon type={'refresh'} />
          </Button>
        </div>
        <Loading
          className="mt-s"
          style={{
            width: '100%',
            overflow: 'auto',
          }}
          visible={isLoading}
        >
          <LogEditor value={value} />
        </Loading>
      </>
    </SlidePanel>
  );
});
