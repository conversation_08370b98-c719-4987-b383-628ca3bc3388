import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Icon, Description, Tab, Grid, Loading, Timeline } from '@ali/cnd';
import { getParams, unshiftZero } from '~/utils/global';
import { LogEditor } from '@ali/cnd';
import services from '~/services';
import { detailItems, PIPELINE_STATUS, STATUS_CONSTANT, TabItem } from './Constant';
import _ from 'lodash';

const { Row, Col } = Grid;
const TimelineItem = Timeline.Item;

type Props = {
  applicationId: string;
  pipelineRunId: string;
  onBuildList: () => void;
};

export default (props: Props) => {
  const { applicationId, pipelineRunId, onBuildList } = props;

  const applicationName = getParams('name');
  const [data, setData] = useState<any>();
  const [bindListener, setBindListener] = useState(false);
  const [curTab, setCurTab] = useState<TabItem>(TabItem.Log);
  const [logData, setLogData] = useState('');
  const tmpLogData = useRef('');
  const currentOffset = useRef(0);

  const handleBuild = async () => {
    const { Status } = data;
    if (
      Status === PIPELINE_STATUS.FAIL ||
      Status === PIPELINE_STATUS.SUCCESS ||
      Status === PIPELINE_STATUS.STOP
    ) {
      // 重新发起构建
      setLogData('');
      tmpLogData.current = '';
      const res = await services.copyBuildPipelineRun({
        ApplicationId: applicationId,
        ApplicationName: applicationName,
        PipelineRunId: pipelineRunId,
      });
      if (res) {
        onBuildList();
      }
    } else if (Status === PIPELINE_STATUS.RUNNING) {
      // 停止构建
      const res = await services.stopBuildPipelineRun({
        ApplicationId: applicationId,
        PipelineRunId: pipelineRunId,
      });
      if (res) {
        getRunDetailData();
      }
    }
  };

  const getRunDetailData = async () => {
    const res = await services.DescribeBuildPipelineRun({
      ApplicationId: applicationId,
      ApplicationName: applicationName,
      PipelineRunId: pipelineRunId,
    });
    if (res) {
      const { Data = {} } = res;
      const { StartTime, PackageConfig = {}, BuildConfig = {} } = Data;
      /*** 由于后端状态更新不及时，此处为前端体验优化，可酌情修改 */
      // 确保流水线已成功但子步骤仍存在 running, 将状态设为 running 以持续请求更新
      const syncStatus =
        Data.Status === PIPELINE_STATUS.SUCCESS &&
        Data.Steps.filter(
          (e) => e.Status === PIPELINE_STATUS.RUNNING || e.Status === PIPELINE_STATUS.WAIT,
        ).length > 0
          ? PIPELINE_STATUS.RUNNING
          : Data.Status;
      if (syncStatus === PIPELINE_STATUS.RUNNING && Data.Steps[0].Status === PIPELINE_STATUS.WAIT) {
        Data.Steps[0].Status = PIPELINE_STATUS.RUNNING;
      }
      if (syncStatus === PIPELINE_STATUS.STOP) {
        Data.Steps = Data.Steps.map((e) => {
          const tmpStatus = e.Status === PIPELINE_STATUS.RUNNING ? PIPELINE_STATUS.STOP : e.Status;
          return { ...e, Status: tmpStatus };
        });
      }
      if (syncStatus === PIPELINE_STATUS.FAIL) {
        // 若流水线已失败, 将 running 的子步骤设为失败
        Data.Steps = Data.Steps.map((e) => {
          const tmpStatus = e.Status === PIPELINE_STATUS.RUNNING ? PIPELINE_STATUS.FAIL : e.Status;
          return { ...e, Status: tmpStatus };
        });
      }
      /*** 前端优化结束 */
      const { PackageName, PackageVersion, PackageType, PackageUrl } = PackageConfig;
      const { BuildType, BeforeBuildCommand, RunCommand, WorkingDir } = BuildConfig;
      setData({
        ...Data,
        Status: syncStatus,
        StartTime: StartTime,
        BuildType,
        BeforeBuildCommand,
        RunCommand,
        WorkingDir,
        PackageName,
        PackageVersion,
        PackageType,
        PackageUrl,
      });
      if (syncStatus === PIPELINE_STATUS.WAIT || syncStatus === PIPELINE_STATUS.RUNNING) {
        new Promise((resolve) => {
          setTimeout(() => {
            getRunDetailData();
            resolve(1);
          }, 1000);
        });
      }
    }
  };

  const getRunDetailLogs = async (Offset = currentOffset.current, Limit = 200) => {
    const res = await services.listBuildPipelineRunsLogs({
      ApplicationId: applicationId,
      ApplicationName: applicationName,
      PipelineRunId: pipelineRunId,
      Offset,
      Limit,
    });
    if (res) {
      const { NextOffset, Items } = res;
      currentOffset.current = NextOffset;
      const extraLog = Items.map((e) => e.Content).join('\n') + (Items.length > 0 ? '\n' : '');
      if (Offset === 0) {
        tmpLogData.current = extraLog;
      } else {
        tmpLogData.current += extraLog;
      }
      setLogData(tmpLogData.current);
    }
  };

  const handleScroll = (position) => {
    const target = document.getElementsByClassName('log-container')[0];
    const { clientHeight, scrollHeight } = target;
    if (position === 'bot') {
      target.scrollTo(0, scrollHeight - clientHeight - 10); // 预留10px避免触发 scrollTrigger
    } else {
      target.scrollTo(0, 0);
    }
  };

  const scrollTrigger = () => {
    const container = document.getElementsByClassName('log-container')[0];
    const { scrollTop, clientHeight, scrollHeight } = container;
    if (scrollHeight - scrollTop - clientHeight < 1) {
      getRunDetailLogs();
    }
  };

  useEffect(() => {
    getRunDetailData();
  }, [pipelineRunId]);

  useEffect(() => {
    if (data) {
      getRunDetailLogs();
    }
  }, [data]);

  useEffect(() => {
    if (logData && !bindListener) {
      const container = document.getElementsByClassName('log-container')[0];
      container?.addEventListener('scroll', _.throttle(scrollTrigger, 1000), false);
      setBindListener(true);
      return () => {
        container?.removeEventListener('scroll', () => scrollTrigger());
      };
    }
  }, [logData]);

  const renderTimeline = () => {
    const timelineItems = data?.Steps.map((e) => {
      const { StartTime, Status, Name, Id, Result } = e;
      if (Id === 'CreateEnv' || Id === 'ImageBuild' || Id === 'CreateVersion') {
        //简化展示的代码包部署的步骤
        const startDate = new Date(StartTime);
        return (
          <TimelineItem
            key={Id}
            title={Name}
            dot={STATUS_CONSTANT[Status].icon}
            time={StartTime ? startDate.toLocaleString() : ''}
            content={
              data.VersionId && Id === 'CreateVersion' ? (
                <span>
                  {intl('saenext.version-list.BuildRecordPanel.BuildDetail.TheNewVersionOfThe')}
                  <span style={{ fontWeight: 'bold' }}>{unshiftZero(data.VersionId)}</span>
                </span>
              ) : (
                Result
              )
            }
          />
        );
      }
      return null; // 返回 null 或其他元素，以满足 JSX 的语法要求
    });

    return (
      timelineItems &&
      timelineItems.filter((e) => {
        return e !== null;
      })
    );
  };

  const renderDetailLogs = () => {
    return (
      <div style={{ overflow: 'hidden', height: '100%' }}>
        <div style={{ position: 'relative' }}>
          <Tab shape="wrapped" activeKey={curTab} onChange={(key: TabItem) => setCurTab(key)}>
            <Tab.Item
              title={intl('saenext.version-list.BuildRecordPanel.BuildDetail.BuildLogs')}
              key={TabItem.Log}
            />
            <Tab.Item
              title={intl('saenext.version-list.BuildRecordPanel.BuildDetail.ExecutionDetails')}
              key={TabItem.Detail}
            />
          </Tab>

          <Button
            type="primary"
            onClick={handleBuild}
            style={{ position: 'absolute', right: 0, top: 8 }}
          >
            {data?.Status && STATUS_CONSTANT[data?.Status].buttonLabel}
          </Button>
        </div>
        {curTab === TabItem.Log && (
          <>
            <div className="flex border-l" style={{ flexDirection: 'row-reverse' }}>
              <Button className="mr-l" text type="primary" onClick={() => handleScroll('bot')}>
                <Icon type="arrow-down" />
                {intl('saenext.version-list.BuildRecordPanel.BuildDetail.Bottom')}
              </Button>
              <Button className="mr-l" text type="primary" onClick={() => handleScroll('top')}>
                <Icon type="arrow-up" />
                {intl('saenext.version-list.BuildRecordPanel.BuildDetail.Top')}
              </Button>
              <Button className="mr-l" text type="primary" onClick={() => getRunDetailLogs(0)}>
                <Icon type="refresh" />
                {intl('saenext.version-list.BuildRecordPanel.BuildDetail.Refresh')}
              </Button>
            </div>
            <div className="log-container">
              <LogEditor
                value={logData || 'Log generating...'}
                className="border-none"
                style={{ height: 'calc(100vh - 120px)' }}
              />
            </div>
          </>
        )}

        {curTab === TabItem.Detail && (
          <div className={`pl-l pr-l full-height border-l'`}>
            <Description
              title={intl('saenext.version-list.BuildRecordPanel.BuildDetail.ExecutionDetails')}
              dataSource={data}
              items={detailItems}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <Loading visible={_.isEmpty(data)} className="full-width full-height">
      <div className="cicd-detail-container">
        <Row gutter={0} wrap className="mt-l" style={{ height: 'auto' }}>
          <Col span={24}>
            <Timeline>{renderTimeline()}</Timeline>
          </Col>
        </Row>
        <Row className="mt-l">
          <Col span={24}>{renderDetailLogs()}</Col>
        </Row>
      </div>
    </Loading>
  );
};
