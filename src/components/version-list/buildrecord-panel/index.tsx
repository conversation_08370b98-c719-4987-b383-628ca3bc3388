import { intl } from '@ali/cnd';
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { SlidePanel, Breadcrumb } from '@ali/cnd';
import _ from 'lodash';
import BuildList from './BuildList';
import BuildDetail from './BuildDetail';

interface Props {
  applicationId: string;
  regionId: string;
  history: any;
  location: any;
  setRefreshIndex: (date: number) => void;
}
const PageEnum = {
  List: 'list',
  Detail: 'detail',
};

export default forwardRef((props: Props, ref) => {
  const {
    history,
    location: { search },
    regionId,
    applicationId,
    setRefreshIndex,
  } = props;
  const [visible, setVisible] = useState(false);
  const [buildId, setBuildId] = useState('');
  const [currentPage, setCurrrentPage] = useState(PageEnum.List);

  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }));

  useEffect(() => {
    // 处理回显
    if (!visible) return;
  }, [visible]);

  const onBuildDetail = (buildId) => {
    setBuildId(buildId);
    setCurrrentPage(PageEnum.Detail);
  };

  const onBuildList = () => {
    setCurrrentPage(PageEnum.List);
    setBuildId('');
  };

  const handleClose = () => {
    setVisible(false);
    setRefreshIndex(Date.now());
    setCurrrentPage(PageEnum.List);
  };

  const onVersionDetail = (versionId) => {
    if (versionId) {
      history.push(
        `/${regionId}/app-list/${applicationId}/version/${versionId}/configure${search}`,
      );
    }
  };

  return (
    <SlidePanel
      title={
        currentPage === PageEnum.List ? (
          <span>{intl('saenext.version-list.BuildRecordPanel.BuildRecords')}</span>
        ) : (
          <div className="flex" style={{ cursor: 'pointer' }} onClick={onBuildList}>
            <svg className="icon" viewBox="0 0 1024 1024" p-id="2170" width="24" height="40">
              <path
                d="M97.834667 542.165333L384 828.330667 444.330667 768l-213.333334-213.333333H938.666667v-85.333334H230.997333l213.333334-213.333333L384 195.626667 97.834667 481.834667a42.666667 42.666667 0 0 0 0 60.330666z"
                fill="#333333"
                p-id="2171"
              ></path>
            </svg>
            <span className="ml-l">{buildId}</span>
          </div>
        )
      }
      width={1200}
      isShowing={visible}
      onClose={handleClose}
    >
      <>
        {currentPage === PageEnum.List ? (
          <BuildList
            applicationId={applicationId}
            onBuildDetail={onBuildDetail}
            onVersionDetail={onVersionDetail}
          />
        ) : (
          <>
            <Breadcrumb>
              <Breadcrumb.Item style={{ cursor: 'pointer' }} onClick={onBuildList}>
                {intl('saenext.version-list.BuildRecordPanel.BuildList')}
              </Breadcrumb.Item>
              <Breadcrumb.Item>
                {intl('saenext.version-list.BuildRecordPanel.BuildDetailsBuildid', {
                  buildId: buildId,
                })}
              </Breadcrumb.Item>
            </Breadcrumb>
            <BuildDetail
              onBuildList={onBuildList}
              applicationId={applicationId}
              pipelineRunId={buildId}
            />
          </>
        )}
      </>
    </SlidePanel>
  );
});
