import { intl } from '@ali/cnd'; /* eslint react/prop-types: 0 */
import React, { useState, useEffect } from 'react';
import { Button, Icon, CndTable, Instance, LinkButton, DateTime } from '@ali/cnd';
import { getParams, unshiftZero, getDurationString } from '~/utils/global';
import services from '~/services';
import BuildStatus from './BuildStatus';

type Props = {
  applicationId: string;
  onBuildDetail: (buildId) => void;
  onVersionDetail: (versionId) => void;
};

const Status = {
  FAIL: 'failed',
  STOP: 'failed',
  CANCEL: 'warning',
  SUCCESS: 'succeeded',
};

export default (props: Props) => {
  const { applicationId, onBuildDetail, onVersionDetail } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshIndex, setRefreshIndex] = useState(0);
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(100);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const applicationName = getParams('name');

  useEffect(() => {
    getBuildList();
  }, [refreshIndex, page, pageSize]);

  const getBuildList = async () => {
    setLoading(true);
    const _dataSource = await services.listBuildPipelineRuns({
      ApplicationId: applicationId,
      ApplicationName: applicationName,
      PageNo: page,
      PageSize: pageSize,
    });
    setDataSource(_dataSource.Items);
    if (_dataSource.Total < pageSize) {
      setTotal((page - 1) * pageSize + _dataSource.Total);
    } else {
      setTotal(page * pageSize + 1);
    }
    setLoading(false);
  };

  return (
    <>
      <div className="flex justify-between">
        <div></div>
        <Button onClick={() => setRefreshIndex(Date.now())}>
          <Icon type="refresh" />
        </Button>
      </div>
      <CndTable
        loading={loading}
        pagination={{
          total,
          current: page,
          pageSize,
          pageSizeList: [10, 20, 50, 100],
          onChange: setPage,
          onPageSizeChange: setPageSize,
          hideOnlyOnePage: false,
        }}
        className="mt-s"
        dataSource={dataSource}
        // @ts-ignore
        columns={[
          {
            title: intl('saenext.version-list.BuildRecordPanel.BuildList.BuildId'),
            dataIndex: 'PipelineRunId',
            cell: (value, index, record) => {
              return (
                // @ts-ignore
                <Instance
                  // 'succeeded' | 'failed' | 'warning' | 'info
                  status={Status[record.Status] || 'info'}
                  link={{
                    value: value,
                    onClick: () => onBuildDetail(value),
                  }}
                />
              );
            },
          },
          {
            title: intl('saenext.version-list.BuildRecordPanel.BuildList.Status'),
            dataIndex: 'Status',
            cell: (value) => <BuildStatus value={value} />,
          },
          {
            title: intl('saenext.version-list.BuildRecordPanel.BuildList.ApplicationVersion'),
            dataIndex: 'VersionId',
            cell: (value) =>
              value ? (
                <LinkButton onClick={() => onVersionDetail(value)}>{unshiftZero(value)}</LinkButton>
              ) : (
                '-'
              ),
          },
          {
            title: intl('saenext.version-list.BuildRecordPanel.BuildList.Package'),
            dataIndex: 'PackageConfig',
            cell: (value) =>
              value
                ? intl('saenext.version-list.BuildRecordPanel.BuildList.Version') +
                  value.PackageVersion +
                  intl('saenext.version-list.BuildRecordPanel.BuildList.PackageName') +
                  value.PackageName
                : '-',
          },
          {
            title: intl('saenext.version-list.BuildRecordPanel.BuildList.CreationTime'),
            dataIndex: 'CreateTime',
            cell: (value) => <DateTime value={value}></DateTime>,
          },
          {
            title: intl('saenext.version-list.BuildRecordPanel.BuildList.BuildDuration'),
            dataIndex: 'BuildDuration',
            cell: (value) => getDurationString(value),
          },
        ]}
      />
    </>
  );
};
