import { intl } from '@ali/cnd';
import { StatusIndicator } from '@ali/cnd';
import React, { memo } from 'react';

export const MAP = {
  RUNNING: {
    type: 'loading',
    text: intl('saenext.version-list.BuildRecordPanel.BuildStatus.Running'),
  },
  SUCCESS: {
    type: 'success',
    text: intl('saenext.version-list.BuildRecordPanel.BuildStatus.BuiltSuccessfully'),
  },
  FAIL: {
    type: 'error',
    text: intl('saenext.version-list.BuildRecordPanel.BuildStatus.BuildFailed'),
  },
  WAIT: {
    type: 'loading',
    text: intl('saenext.version-list.BuildRecordPanel.BuildStatus.Queuing'),
  },
  STOP: {
    type: 'minus_fill',
    text: intl('saenext.version-list.BuildRecordPanel.BuildStatus.Terminated'),
  },
  CANCEL: {
    type: 'minus_fill',
    text: intl('saenext.version-list.BuildRecordPanel.BuildStatus.Canceled'),
  },
};

export const Status = {
  Busy: 'Busy',
  Idle: 'Idle',
  Destroyed: 'Destroyed',
};

const BuildStatus = (props) => {
  const { value } = props;
  if (!value) {
    return <span>--</span>;
  }
  const { type, text } = MAP[value] || {};
  if (!type) {
    return <span>--</span>;
  }
  return (
    <StatusIndicator type={type} shape="icon">
      {text}
    </StatusIndicator>
  );
};

export default memo(BuildStatus);
