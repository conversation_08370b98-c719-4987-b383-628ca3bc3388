import { intl } from '@ali/cnd';
import React from 'react';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import TextSnippet from '~/components/shared/TextSnippet';
import { Icon } from '@ali/cnd';
import moment from 'moment';
import { getDurationString } from '~/utils/global';

export enum TabItem {
  Log = 'log',
  Detail = 'detail',
}

export enum PIPELINE_STATUS {
  FAIL = 'FAIL',
  RUNNING = 'RUNNING',
  SUCCESS = 'SUCCESS',
  WAIT = 'WAIT',
  STOP = 'STOP',
  CANCEL = 'CANCEL',
}

export const STATUS_CONSTANT = {
  [PIPELINE_STATUS.RUNNING]: {
    label: intl('saenext.version-list.BuildRecordPanel.Constant.Running'),
    buttonLabel: intl('saenext.version-list.BuildRecordPanel.Constant.Cancel'),
    icon: <Icon type="loading" size="small" />,
    color: '#1DC11D',
  },
  [PIPELINE_STATUS.SUCCESS]: {
    label: intl('saenext.version-list.BuildRecordPanel.Constant.BuiltSuccessfully'),
    buttonLabel: intl('saenext.version-list.BuildRecordPanel.Constant.Rebuild'),
    icon: <Icon type="success" size="small" />,
    color: '#1DC11D',
  },
  [PIPELINE_STATUS.FAIL]: {
    label: intl('saenext.version-list.BuildRecordPanel.Constant.BuildFailed'),
    buttonLabel: intl('saenext.version-list.BuildRecordPanel.Constant.Retry'),
    icon: <Icon type="error" size="small" />,
    color: '#C82727',
  },
  [PIPELINE_STATUS.WAIT]: {
    label: intl('saenext.version-list.BuildRecordPanel.Constant.Queuing'),
    buttonLabel: intl('saenext.version-list.BuildRecordPanel.Constant.Queuing'),
    icon: <Icon type="minus_fill" size="small" />,
    color: '#1DC11D',
  },
  [PIPELINE_STATUS.STOP]: {
    label: intl('saenext.version-list.BuildRecordPanel.Constant.Terminated'),
    buttonLabel: intl('saenext.version-list.BuildRecordPanel.Constant.Rebuild'),
    icon: <Icon type="minus_fill" size="small" />,
    color: '#C82727',
  },
  [PIPELINE_STATUS.CANCEL]: {
    label: intl('saenext.version-list.BuildRecordPanel.Constant.Canceled'),
    buttonLabel: intl('saenext.version-list.BuildRecordPanel.Constant.Rebuild'),
    icon: <Icon type="minus_fill" size="small" />,
    color: '#C82727',
  },
};

export const descriptionItems = [
  {
    dataIndex: 'StartTime',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.StartCreationTime'),
    render: (value) => moment(value).format('lll'),
  },
  {
    dataIndex: 'PackageConfig.PackageVersion',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.PackageVersion'),
  },
  {
    dataIndex: 'PackageConfig.PackageName',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.PackageName'),
  },
];

export const detailItems = [
  {
    dataIndex: 'PipelineRunId',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.BuildId'),
  },
  {
    dataIndex: 'Status',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.BuildStatus'),
    render: (value) => STATUS_CONSTANT[value].label,
  },
  {
    dataIndex: 'CreateTime',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.CreationTime'),
    render: (value) => moment(value).format('lll'),
  },
  {
    dataIndex: 'StartTime',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.StartTime'),
    render: (value) => moment(value).format('lll'),
  },
  {
    dataIndex: 'WaitDuration',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.QueuingTime'),
    render: (value) => getDurationString(value),
  },
  {
    dataIndex: 'Steps',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.TotalBuildDuration'),
    render: (value) => getDurationString(value.reduce((pre, cur) => pre + (cur.Duration || 0), 0)),
  },
  {
    dataIndex: 'PackageConfig',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.PackageVersion'),
    render: (value) => value?.PackageVersion,
  },
  {
    dataIndex: 'PackageConfig',
    label: intl('saenext.version-list.BuildRecordPanel.Constant.PackageName'),
    render: (value) => value?.PackageName,
  },
];

export const stepTableColumns = [
  {
    title: '',
    width: 40,
    dataIndex: 'Status',
    cell: (value) => STATUS_CONSTANT[value]?.icon,
  },
  {
    title: intl('saenext.version-list.BuildRecordPanel.Constant.PerformSteps'),
    width: 150,
    dataIndex: 'Name',
    cell: (value, idx, item) => <TextWithBalloon text={item.Name} tips={item.Description} />,
  },
  {
    title: intl('saenext.version-list.BuildRecordPanel.Constant.Details'),
    dataIndex: 'Result',
    cell: (value) => <TextSnippet text={value} maxLength={40} />,
  },
  {
    title: intl('saenext.version-list.BuildRecordPanel.Constant.TimeConsuming'),
    dataIndex: 'Duration',
    cell: (value) => (value ? getDurationString(value) : '-'),
  },
];
