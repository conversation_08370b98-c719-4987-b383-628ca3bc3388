import { intl } from '@ali/cnd';
import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useContext,
} from 'react';
import { Loading, SlidePanel, Tab, Message, Dialog, Radio, Icon } from '@ali/cnd';
import { set, get, isEmpty } from 'lodash';
import services from '~/services';
import { getParams, unshiftZero } from '~/utils/global';
import { GetRepo } from '~/services/acr';
import Cr from '~/components/shared/ImageSelectorField/cr';
import Demo from '~/components/shared/ImageSelectorField/demo';
import Custom from '~/components/shared/ImageSelectorField/custom';
import parseUrl, { Type } from '~/components/shared/ImageSelectorField/utils';
import FeatureContext from '~/utils/featureContext';
import PrivateRegistry from '../shared/ImageSelectorField/PrivateRegistry';

const RadioGroup = Radio.Group;
type Props = {
  regionId: string;
  appVersionId: string;
  applicationID: string;
  setRefreshIndex: (date: number) => void;
};

export default forwardRef((props: Props, ref) => {
  const { regionId, appVersionId, applicationID, setRefreshIndex } = props;
  const refTab = useRef(null);
  const [showing, setShowing] = useState(false);
  const [parseConfig, setParseConfig] = useState({ type: Type.cr, parseResult: {} });
  const [isLoading, setIsLoading] = useState(false);
  const params = useRef({});
  const [visible, setVisible] = useState(false);
  const [effectiveImmediately, setEffectiveImmediately] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);

  const { feature } = useContext(FeatureContext);
  const { customRegistry } = feature;

  const applicationName = getParams('name');

  useEffect(() => {
    if (!showing || isEmpty(appVersionId)) return;
    initImageData();
  }, [appVersionId, showing]);

  useImperativeHandle(ref, () => ({
    show: () => setShowing(true),
    hide: () => setShowing(false),
  }));

  const initImageData = async () => {
    setIsLoading(true);
    const data = await services.getAppVersionConfig({
      applicationID,
      applicationName,
      qualifier: appVersionId,
    });
    // const value = get(data, 'imageConfig', {});
    const { imageConfig, ...restData } = data;
    params.current = {
      ...restData,
      instanceConcurrency: data?.instanceConcurrency,
      caPort: data?.caPort,
      cpu: data?.cpu,
      memorySize: data?.memorySize,
      timeout: data?.timeout,
      // imageConfig: data?.imageConfig,
      // 更新传null 表示之前配置不变, 传{}表示要去掉之前的配置
      nasConfig: get(data, 'nasConfig', null),
      ossMountConfig: get(data, 'ossMountConfig', null),
      environmentVariables: get(data, 'environmentVariables', null),
      logConfig: get(data, 'logConfig', null),
      slsConfig: get(data, 'slsConfig', null),
      startupProbe: get(data, 'startupProbe', null),
      livenessProbe: get(data, 'livenessProbe', null),
    };
    const { image, instanceId, registryConfig } = imageConfig;
    if (image) {
      const result = parseUrl(regionId, image, registryConfig);
      if (result.type === Type.cr) {
        if (image.indexOf(`${regionId}.cr.aliyuncs.com`) > -1) {
          // 企业镜像
          // @ts-ignore
          result.parseResult.type = 'cr';
        } else {
          // 个人版镜像
          // @ts-ignore
          result.parseResult.type = 'acr';
        }
        if (instanceId) {
          set(result, 'parseResult.instanceId', instanceId);
        }
        // @ts-ignore: 个人版镜像验证是否存在
        const { repoNamespace, repoName } = result.parseResult;
        if (repoNamespace) {
          try {
            await GetRepo({ RepoNamespace: repoNamespace, RepoName: repoName });
          } catch (_e) {
            // 获取不到信息强制转换到自定义
            result.type = Type.custom;
            result.parseResult = { image };
          }
        }
      }
      console.log('result===', result);
      setParseConfig(result);
    }
    setIsLoading(false);
  };

  const closeImageSlide = () => setShowing(false);

  const okImageSlide = async () => {
    const result = await refTab.current.submit();
    if (result.error) {
      Message.error(result.error);
      return;
    }
    Reflect.set(params.current, 'imageConfig', { ...result, accelerationType: 'Default' });
    setVisible(true);
  };

  const updateAppImage = async () => {
    setIsSubmit(true);
    const res = await services.updateAppVersions(applicationID, applicationName, {
      // @ts-ignore
      applicationID,
      ...params.current,
      effectiveImmediately,
    });
    setIsSubmit(false);
    if (res.applicationID) {
      setVisible(false);
      setShowing(false);
      setRefreshIndex && setRefreshIndex(Date.now());
    }
  };

  const version = unshiftZero(appVersionId);

  return (
    <>
      <SlidePanel
        title={intl(
          'saenext.components.version-list.UpdateImagePanel.VersionVersionConfigureImages',
          { version: version },
        )}
        isShowing={showing}
        width="large"
        onClose={closeImageSlide}
        onCancel={closeImageSlide}
        onOk={okImageSlide}
      >
        <Loading visible={isLoading} className="full-width full-height">
          <Tab
            shape="wrapped"
            activeKey={parseConfig.type}
            className="mb"
            onChange={(key: Type) => setParseConfig({ type: key, parseResult: {} })}
          >
            <Tab.Item
              title={intl('saenext.components.version-list.UpdateImagePanel.MyAlibabaCloudImage')}
              key={Type.cr}
            ></Tab.Item>
            <Tab.Item
              title={intl('saenext.components.version-list.UpdateImagePanel.DemoImage')}
              key={Type.demo}
            ></Tab.Item>
            <Tab.Item
              title={intl('saenext.components.version-list.UpdateImagePanel.CustomImage')}
              key={Type.custom}
            ></Tab.Item>
            {customRegistry && (
              <Tab.Item
                title={intl(
                  'saenext.components.version-list.UpdateImagePanel.ThirdPartyPrivateImageRepository',
                )}
                key={Type.private}
              >
                <PrivateRegistry ref={refTab} initValue={parseConfig.parseResult} />
              </Tab.Item>
            )}
          </Tab>
          {parseConfig.type === Type.cr && (
            <Cr ref={refTab} initValue={parseConfig.parseResult} appType="web" />
          )}
          {parseConfig.type === Type.demo && (
            <Demo ref={refTab} initValue={parseConfig.parseResult} appType="web" />
          )}
          {parseConfig.type === Type.custom && (
            <Custom ref={refTab} initValue={parseConfig.parseResult} />
          )}
        </Loading>
      </SlidePanel>
      <Dialog
        size="small"
        title={
          <div className="flex">
            <Icon type="help_fill" style={{ color: '#ffd813' }} />
            <span className="ml-s">
              {intl('saenext.components.version-list.UpdateImagePanel.WhichTrafficPolicyWillYou')}
            </span>
          </div>
        }
        visible={visible}
        onOk={updateAppImage}
        okProps={{
          loading: isSubmit,
        }}
        onCancel={() => setVisible(false)}
        onClose={() => setVisible(false)}
      >
        <RadioGroup
          itemDirection="ver"
          value={effectiveImmediately}
          onChange={(value) => setEffectiveImmediately(value as boolean)}
        >
          <Radio value={true}>
            {intl(
              'saenext.components.version-list.UpdateImagePanel.EnableTrafficAutomaticallyAfterDeployment',
            )}
          </Radio>
          <Radio value={false}>
            {intl('saenext.components.version-list.UpdateImagePanel.AfterTheDeploymentIsCompleted')}
          </Radio>
        </RadioGroup>
      </Dialog>
    </>
  );
});
