import { intl } from '@ali/cnd';
import { StatusIndicator, LinkButton } from '@ali/cnd';
import React, { memo } from 'react';
import TextWithBalloon from '~/components/shared/TextWithBalloon';
import { includes, noop } from 'lodash';

export const MAP = {
  Completed: {
    type: 'success',
    text: intl('saenext.components.version-list.DeployStatus.Running'),
  },
  Updating: {
    type: 'loading',
    text: intl('saenext.components.version-list.DeployStatus.Updating'),
  },
  Error: {
    type: 'error',
    text: intl('saenext.components.version-list.DeployStatus.DeploymentFailed'),
  },
  ScalingFailed: {
    type: 'error',
    text: intl('saenext.components.version-list.DeployStatus.ElasticExpansionFailed'),
  },
};

export const Status = {
  Busy: 'Busy',
  Idle: 'Idle',
  Destroyed: 'Destroyed',
};

const DeployStatus = (props) => {
  const { value, scalingFailedcb = noop } = props;
  if (!value || !value.status) {
    return <span>--</span>;
  }
  const { status, errorMessage } = value;
  let _value = status;
  if (status === 'Updating' && errorMessage) {
    _value = 'Error';
  }
  const { type, text } = MAP[_value] || {};
  if (!type) {
    return <span>--</span>;
  }

  return (
    <StatusIndicator type={type} shape="icon">
      {includes(['Error', 'ScalingFailed'], _value) ? (
        <TextWithBalloon
          align="r"
          text={text}
          tips={
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <span>
                Error message:{' '}
                {errorMessage.split(/\n/g).map((msg) => (
                  <div>{msg}</div>
                ))}
              </span>
              {_value === 'ScalingFailed' ? (
                <span>
                  {intl('saenext.components.version-list.DeployStatus.Go')}
                  <LinkButton className="mr-xs ml-xs" onClick={scalingFailedcb}>
                    {intl('saenext.components.version-list.DeployStatus.EventCenter')}
                  </LinkButton>
                  {intl('saenext.components.version-list.DeployStatus.ViewDetails')}
                </span>
              ) : null}
            </div>
          }
        />
      ) : (
        text
      )}
    </StatusIndicator>
  );
};

export default memo(DeployStatus);
