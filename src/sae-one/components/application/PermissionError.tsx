import React from 'react';
import { Icon, Message, Button, Balloon } from '@ali/cnd';
import { intl } from '@ali/cnd';

interface PermissionErrorProps {
  /**
   * 模块名称
   */
  moduleName: string;
  /**
   * 接口名称
   */
  apiName: string;
  /**
   * 需要的权限策略
   */
  requiredPolicy: string;
  /**
   * 显示方式: 'inline' | 'message' | 'balloon'
   */
  type?: 'inline' | 'message' | 'balloon';
  /**
   * 错误代码
   */
  errorCode?: string;
  /**
   * 帮助文档链接
   */
  helpUrl?: string;
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  /**
   * 是否显示详细信息
   */
  showDetails?: boolean;
  /**
   * 子元素（用于 balloon 模式）
   */
  children?: React.ReactNode;
}

const PermissionError: React.FC<PermissionErrorProps> = ({
  moduleName,
  apiName,
  requiredPolicy,
  type = 'inline',
  errorCode,
  helpUrl,
  style,
  showDetails = true,
  children
}) => {
  const getDetailedMessage = () => {
    return (
      <div>
        <div style={{ fontWeight: 'bold', marginBottom: 8 }}>
          {intl('saenext.components.PermissionError.AccessDenied')}
        </div>
        <div style={{ marginBottom: 4 }}>
          <strong>{intl('saenext.components.PermissionError.Module')}: </strong>
          {moduleName}
        </div>
        <div style={{ marginBottom: 4 }}>
          <strong>{intl('saenext.components.PermissionError.Interface')}: </strong>
          {apiName}
        </div>
        <div style={{ marginBottom: 4 }}>
          <strong>{intl('saenext.components.PermissionError.RequiredPolicy')}: </strong>
          <code style={{ background: '#f5f5f5', padding: '2px 4px', borderRadius: 2 }}>
            {requiredPolicy}
          </code>
        </div>
        {errorCode && (
          <div style={{ marginBottom: 4 }}>
            <strong>{intl('saenext.components.PermissionError.ErrorCode')}: </strong>
            {errorCode}
          </div>
        )}
        {helpUrl && (
          <div style={{ marginTop: 8 }}>
            <Button 
              text 
              type="primary" 
              size="small"
              onClick={() => window.open(helpUrl, '_blank')}
            >
              {intl('saenext.components.PermissionError.ViewPermissionHelp')}
            </Button>
          </div>
        )}
      </div>
    );
  };

  const getSimpleMessage = () => {
    return (
      <span>
        {intl('saenext.components.PermissionError.SimpleMessage', {
          moduleName,
          requiredPolicy
        })}
      </span>
    );
  };

  const renderContent = () => {
    const content = showDetails ? getDetailedMessage() : getSimpleMessage();
    
    if (type === 'message') {
      return (
        <Message 
          type="warning" 
          style={style}
          title={intl('saenext.components.PermissionError.PermissionRequired')}
        >
          {content}
        </Message>
      );
    }

    if (type === 'balloon') {
      return (
        <Balloon
          trigger={children || (
            <span style={{ color: '#ff3333', cursor: 'pointer' }}>
              <Icon type="warning" size="xs" style={{ color: '#ff3333', marginRight: 4 }} />
              {intl('saenext.components.PermissionError.NoPermission')}
            </span>
          )}
          align="t"
          triggerType="hover"
          closable={false}
        >
          {content}
        </Balloon>
      );
    }

    // inline 模式
    return (
      <div style={{ display: 'flex', alignItems: 'center', color: '#ff3333', ...style }}>
        <Icon type="warning" size="xs" style={{ color: '#ff3333', marginRight: 4 }} />
        {showDetails ? content : getSimpleMessage()}
      </div>
    );
  };

  return renderContent();
};

export default PermissionError; 