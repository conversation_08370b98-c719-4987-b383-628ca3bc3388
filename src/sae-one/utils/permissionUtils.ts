export interface PermissionConfig {
  moduleName: string;
  apiName: string;
  requiredPolicy: string;
  helpUrl?: string;
  description?: string;
}

export interface PermissionError {
  code: string;
  message: string;
  moduleName: string;
  apiName: string;
  requiredPolicy: string;
  helpUrl?: string;
}

/**
 * 权限配置映射表
 */
export const PERMISSION_CONFIG: Record<string, PermissionConfig> = {
  // ALB 相关权限
  'alb.ListLoadBalancers': {
    moduleName: '应用型负载均衡器(ALB)',
    apiName: 'ListLoadBalancers',
    requiredPolicy: 'AliyunSLBReadOnlyAccess',
    helpUrl: 'https://help.aliyun.com/document_detail/66384.html',
    description: '查看ALB实例列表权限'
  },
  
  // CLB 相关权限
  'clb.ListLoadBalancers': {
    moduleName: '传统型负载均衡器(CLB)',
    apiName: 'DescribeLoadBalancers',
    requiredPolicy: 'AliyunSLBReadOnlyAccess',
    helpUrl: 'https://help.aliyun.com/document_detail/27582.html',
    description: '查看CLB实例列表权限'
  },
  
  // NLB 相关权限
  'nlb.ListLoadBalancers': {
    moduleName: '网络型负载均衡器(NLB)',
    apiName: 'ListLoadBalancers',
    requiredPolicy: 'AliyunNLBReadOnlyAccess',
    helpUrl: 'https://help.aliyun.com/document_detail/608823.html',
    description: '查看NLB实例列表权限'
  },
  
  // MSE 相关权限
  'mse.ListGateways': {
    moduleName: '微服务引擎(MSE)',
    apiName: 'ListGateways',
    requiredPolicy: 'AliyunMSEReadOnlyAccess',
    helpUrl: 'https://help.aliyun.com/document_detail/150338.html',
    description: '查看MSE网关实例列表权限'
  },
  
  // APIG 相关权限
  'apig.GetGateways': {
    moduleName: 'API网关(APIG)',
    apiName: 'GetGateways',
    requiredPolicy: 'AliyunApiGatewayReadOnlyAccess',
    helpUrl: 'https://help.aliyun.com/document_detail/29487.html',
    description: '查看API网关实例列表权限'
  },
  
  // VPC 相关权限
  'vpc.DescribeVpcs': {
    moduleName: '专有网络(VPC)',
    apiName: 'DescribeVpcs',
    requiredPolicy: 'AliyunVPCReadOnlyAccess',
    helpUrl: 'https://help.aliyun.com/document_detail/34962.html',
    description: '查看VPC信息权限'
  },
  
  // ECS 相关权限
  'ecs.DescribeSecurityGroups': {
    moduleName: '云服务器(ECS)',
    apiName: 'DescribeSecurityGroups',
    requiredPolicy: 'AliyunECSReadOnlyAccess',
    helpUrl: 'https://help.aliyun.com/document_detail/25556.html',
    description: '查看安全组信息权限'
  }
};

/**
 * 检查错误码是否为权限错误
 */
export const isForbidden = (code: string): boolean => {
  return (
    code === "Forbidden" ||
    code === "Forbidden.RAM" ||
    code === "AccessDenied" ||
    code === "NoPermission" ||
    code === "Forbidden.NoPermission" ||
    code === "AUTHENTICATION_FAILED" ||
    code.includes("Forbidden")
  );
};

/**
 * 根据配置Key创建权限错误对象
 */
export const createPermissionError = (
  configKey: string, 
  errorCode: string, 
  errorMessage?: string
): PermissionError => {
  const config = PERMISSION_CONFIG[configKey];
  
  if (!config) {
    throw new Error(`Permission config not found for key: ${configKey}`);
  }
  
  return {
    code: errorCode,
    message: errorMessage || `缺少${config.moduleName}的${config.description}`,
    moduleName: config.moduleName,
    apiName: config.apiName,
    requiredPolicy: config.requiredPolicy,
    helpUrl: config.helpUrl
  };
};

/**
 * 获取权限配置
 */
export const getPermissionConfig = (configKey: string): PermissionConfig | null => {
  return PERMISSION_CONFIG[configKey] || null;
};

/**
 * 创建自定义错误处理函数
 */
export const createCustomErrorHandler = (
  configKey: string,
  onPermissionError?: (error: PermissionError) => void,
  onOtherError?: (error: any) => void
) => {
  return (error: any, data?: any, callback?: () => void) => {
    const forbidden = isForbidden(error.code);
    
    if (forbidden) {
      const permissionError = createPermissionError(configKey, error.code, error.message);
      onPermissionError?.(permissionError);
    } else {
      onOtherError?.(error);
      callback?.();
    }
    
    if (!forbidden) {
      callback?.();
    }
  };
}; 