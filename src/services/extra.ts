import createService from '~/utils/request';

const PRODUCT = 'serverless';

interface IRCommonParams {
  params: any;
  customErrorHandle?: Function;
}

interface IRGetAppMseService {
  AppId: string;
  EnableAhas?: boolean;
  FreeVersion?: number;
};

export const listMseNacosInstances = ({ params, customErrorHandle }) =>
  createService({
    product: 'mse',
    action: 'ListClusters',
    params,
    customErrorHandle,
  });

export const listMseNacosNamespaces = ({
  params,
  customErrorHandle,
}: {
  params: { InstanceId: string };
  customErrorHandle?: Function;
}) =>
  createService({
    product: 'mse',
    action: 'ListEngineNamespaces',
    params,
    customErrorHandle,
  });

export const getTraceApp = ({ params, customErrorHandle }: IRCommonParams) =>
  createService({
    product: 'arms',
    action: 'GetTraceApp',
    params,
    customErrorHandle,
  });

export const getArmsDashboardUrl = ({ params, customErrorHandle }: IRCommonParams) =>
  createService({
    product: 'serverless',
    action: 'GetArmsDashboardUrl',
    params,
    customErrorHandle,
  });

export const createOIDCProvider = ({ params }) =>
  createService({
    product: 'serverless',
    action: 'CreateOIDCProvider',
    params,
  });

export const CheckCommercialStatus = ({ params }) =>
  createService({
    product: 'arms',
    action: 'CheckCommercialStatus',
    params,
    ignoreError: true,
  });

export const GetCommercialStatus = ({ params }) =>
  createService({
    product: 'arms',
    action: 'GetCommercialStatus',
    params,
    ignoreError: true,
  });

export const GetAgentDownloadUrlV2 = ({ params }) =>
  createService({
    product: 'arms',
    action: 'GetAgentDownloadUrlV2',
    params,
  });

/**prometheus监控——start */

// 检查 Prometheus 服务是否开通
export const GetCmsService = ({ params }) =>
  createService({
    product: 'cms20240330',
    action: 'GetCmsService',
    params,
});
// 创建或查询接入策略
export const CreateIntegrationPolicy = ({ params, customErrorHandle }: IRCommonParams) =>
  createService({
    product: 'cms20240330',
    action: 'CreateIntegrationPolicy',
    options: { apiType: 'roa' as any },
    params,
    customErrorHandle,
});
// 查询指定策略下所有接入记录
export const ListAddonReleases = ({params}) =>
  createService({
    product: 'cms20240330',
    action: 'ListAddonReleases',
    params,
});
// 创建 AddonRelase 记录
export const CreateAddonRelease = (params) =>
  createService({
    product: 'cms20240330',
    action: 'CreateAddonRelease',
    options: { apiType: 'roa' as any },
    params,
});
// 变更AddonRelase 记录
export const UpdateAddonRelease = (params) =>
  createService({
    product: 'cms20240330',
    action: 'UpdateAddonRelease',
    options: { apiType: 'roa' as any },
    params,
});

export const DeleteAddonRelease = ({params}) =>
  createService({
    product: 'cms20240330',
    action: 'DeleteAddonRelease',
    params,
});

export const ListIntegrationPolicyStorageRequirements = ({ params }) =>
  createService({
    product: 'cms20240330',
    action: 'ListIntegrationPolicyStorageRequirements',
    params,
  });

/**prometheus监控——end */

export const getAppMseStatus = ({
  params,
  customErrorHandle
}: {
  params: IRGetAppMseService;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationMseService',
  params,
  customErrorHandle,
});

export const enableAppMseStatus = ({
  params,
  customErrorHandle
}: {
  params: IRGetAppMseService,
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'CreateApplicationMseService',
  params,
  customErrorHandle,
});

export const disableAppMseStatus = ({
  params,
  customErrorHandle
}: {
  params: IRGetAppMseService;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DeleteApplicationMseService',
  params,
  customErrorHandle,
});

export const getUserStatus = ({
  customErrorHandle,
}: {
  customErrorHandle?: Function;
}) => createService({
  product: 'mse',
  action: 'GetUserStatus',
  customErrorHandle,
});

export const DescribeUserBusinessStatus = (params) => createService({ 
  product: 'Ubsms-inner', 
  action: 'DescribeUserBusinessStatus', 
  options: { apiType: 'inner' } as any, 
  params,
});

export const closeAdvancedMonitor = ({
  params,
  customErrorHandle
}: {
  params: { RegionId: string; AppId: string };
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DowngradeApplicationApmService',
  params,
  customErrorHandle,
});

export const openAdvancedMonitor = ({
  params,
  customErrorHandle
}: {
  params: { RegionId: string; AppId: string };
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'UpgradeApplicationApmService',
  params,
  customErrorHandle,
});

export const getOpenVCluster = ({
  params,
  customErrorHandle,
}: {
  params: { RegionId: string; ClusterType: string, Product: string };
  customErrorHandle?: Function;
}) => createService({
  product: 'arms',
  action: 'OpenVCluster',
  params,
  customErrorHandle,
});

export const getArmsEnable = ({
  params,
  customErrorHandle,
}: {
  params: { RegionId: string; AppId: string };
  customErrorHandle?: Function;
}) => createService({
  product: 'serverless',
  action: 'QueryArmsEnable',
  params,
  customErrorHandle,
});

export const getServiceConsole = ({
  params,
  customErrorHandle,
}: {
  params: { RegionId: string; ProductType: string; SourceType: string; QueryData: string };
  customErrorHandle?: Function;
}) => createService({
  product: 'arms',
  action: 'QueryServiceDetailConsoleInfo',
  params,
  customErrorHandle,
});


// 获取资源包
export const getResourcePackage = ({
  params = {},
  customErrorHandle,
}) => createService({
  product: 'BssOpenApi',
  action: 'QueryResourcePackageInstances',
  params: {
    ...params,
    ProductCode: 'sae',
  },
  customErrorHandle
});