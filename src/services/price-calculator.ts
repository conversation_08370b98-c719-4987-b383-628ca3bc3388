import createService from '~/utils/request';

const PRODUCT = 'serverless';
const OPTION = {
  apiType: 'roa' as any,
}

export const getPriceEstimate = (
  params: {
    content: {
      Duration: number;
      Features: Array<any>;
      Version: "v1" | "v2"; // v1 -> 老资源包 v2 -> 新 CU
    }
  },
) => createService({
  product: PRODUCT,
  action: 'GetPriceEstimate',
  params,
  options: OPTION
});

export const DescribeInstanceStatus = (
  params,
) => createService({
  product: 'ecs',
  action: 'DescribeInstanceStatus',
  params,
});

export const DescribeMetricListFromProxy = (
  params,
) => createService({
  product: 'metrics20180308',
  action: 'DescribeMetricListFromProxy',
  params,
});