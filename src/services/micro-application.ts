import { ApiType } from '@ali/cnd';
import callOpenApi from '~/utils/callOpenApi';
import createService from '~/utils/request';
const PRODUCT = 'serverless';

export const getResourceStatistics = (
  params: { AppId: string }
) => createService({
  product: PRODUCT,
  action: 'QueryResourceStatics',
  params
});

export const  describeMicroApplicationStatus = (
  params: { AppId: string },
  ignoreError?,
) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationStatus',
  params,
  ignoreError,
});

export const queryMicroAppArmsStatus = (
  params: { AppId: string },
  ignoreError?,
) => createService({
  product: PRODUCT,
  action: 'QueryArmsEnable',
  params,
  ignoreError,
});

export const describeMicroApplicationConfig = (
  params: { AppId: string, VersionId?: string },
  ignoreError?,
  customErrorHandle?: Function,
) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationConfig',
  params,
  ignoreError,
  customErrorHandle,
});

export const createMicroApplication = (
  params,
) => createService({
  product: PRODUCT,
  action: 'CreateApplication',
  params,
  options: {
    apiType: 'roa' as ApiType,
  }
});

export const deployMicroApplication = (
  params,
) => createService({
  product: PRODUCT,
  action: 'DeployApplication',
  params,
  options: {
    apiType: 'roa' as ApiType,
  }
});

// 删除风控封装
export const deleteMicroApplication = ({ params, options }) => callOpenApi(
  PRODUCT,
  'DeleteApplication',
  params,
  options,
) as Promise<any>;

// export const deleteMicroApplication = (
//   params: { AppId: string }
// ) => createService({
//   product: PRODUCT,
//   action: 'DeleteApplication',
//   params,
// });

export const stopMicroApplication = (
  params: { AppId: string }
) => createService({
  product: PRODUCT,
  action: 'StopApplication',
  params,
});

export const startMicroApplication = (
  params: { AppId: string }
) => createService({
  product: PRODUCT,
  action: 'StartApplication',
  params,
});

export const restartMicroApplication = (
  params: {
    AppId: string,
    MinReadyInstances: number,
    MinReadyInstanceRatio: number,
  }
) => createService({
  product: PRODUCT,
  action: 'RestartApplication',
  params,
});

export const rescaleMicroApplication = (
  params: {
    AppId: string,
    MinReadyInstances: number,
    MinReadyInstanceRatio: number,
  }
) => createService({
  product: PRODUCT,
  action: 'RescaleApplication',
  params,
});

export const disableMicroAppArms = (
  params: {
    AppId: string,
  }
) => createService({
  product: PRODUCT,
  action: 'DisableArms',
  params,
});

export const enableMicroAppArms = (
  params: {
    AppId: string,
  }
) => createService({
  product: PRODUCT,
  action: 'EnableArms',
  params,
});

export const DescribeApplicationGroups = (
  params: { AppId: string },
  ignoreError?,
) =>
  createService({
    product: PRODUCT,
    action: 'DescribeApplicationGroups',
    params,
    ignoreError,
  });

export const DescribeApplicationInstances = (
  params: {
    AppId: string,
    GroupId: string,
    PageSize?: number,
    CurrentPage?: number
  },
  ignoreError?,
) => createService({
    product: PRODUCT,
    action: 'DescribeApplicationInstances',
    params,
    ignoreError,
  });

export const DebugInstance = (
  params: {
    AppId: string,
    InstanceId: string,
  },
) => createService({
  product: PRODUCT,
  action: 'DebugInstance',
  params,
});

export const disenableMsc = (
  params: {
    AppId: string,
  }
) => createService({
  product: PRODUCT,
  action: 'DeleteApplicationMseService',
  params,
});

export const upgradeMsc = (
  params
) => createService({
  product: PRODUCT,
  action: 'UpgradeApplicationMseService',
  params,
});

export const restartMicroInstances = (
  params: {
    AppId: string,
    InstanceIds: string,
  }
) => createService({
  product: PRODUCT,
  action: 'RestartInstances',
  params,
});

export const deleteMicroInstances = (
  params: {
    AppId: string,
    InstanceIds: string,
  }
) => createService({
  product: PRODUCT,
  action: 'DeleteInstances',
  params,
});

export const UpdateAppSecurityGroup = (
  params: {
    AppId: string,
    SecurityGroupId: string,
  }
) => createService({
  product: PRODUCT,
  action: 'UpdateAppSecurityGroup',
  params,
});

export const ListVSwitches = (
  params
) => createService({
  product: PRODUCT,
  action: 'DescribeVSwitches',
  params
});

export const DescribeRegions = (
  params?
) => createService({
  product: PRODUCT,
  action: 'DescribeRegions',
  params
});

export const UpdateApplicationVswitches = (
  params: {
    AppId: string,
    VSwitchId: string,
  }
) => createService({
  product: PRODUCT,
  action: 'UpdateApplicationVswitches',
  params
});

export const UpdateApplicationDescription = (
  params: {
    AppId: string,
    AppDescription: string,
  }
) => createService({
  product: PRODUCT,
  action: 'UpdateApplicationDescription',
  params
});

export const DescribeInstanceSpecifications = (
) => createService({
  product: PRODUCT,
  action: 'DescribeInstanceSpecifications',
});

export const RescaleApplicationVertically = (
  params,
) => createService({
  product: PRODUCT,
  action: 'RescaleApplicationVertically',
  params,
});

export const getAppScaleMonitor = ({
  params,
  customErrorHandle,
}: {
  params: {
    RegionId: string;
    AppId: string;
    InstanceId?: string;
    MetricName: string;
    StartTime: number;
    EndTime: number;
    Interval: number;
    PrometheusUrl?: string;
    PrometheusToken?: string;
    PrometheusQuery?: string;
    ScalarOnly?:boolean;
  };
  customErrorHandle?: Function;
}) =>
  createService({
    product: PRODUCT,
    action: 'GetScaleMonitor',
    params,
    customErrorHandle,
  });

export const updateAppMode = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'UpdateAppMode',
  params,
});

export const listMicroAppVersions = (
  params: { AppId: string },
) => createService({
  product: PRODUCT,
  action: 'ListAppVersions',
  params,
});

export const getPackageVersionAccessableUrl = (
  params: {
    AppId: string,
    PackageVersion?: string,
  },
  ignoreError?,
) => createService({
  product: PRODUCT,
  action: 'GetPackageVersionAccessableUrl',
  params,
  ignoreError,
});

export const rollbackApplicationMicro = (
  params,
) => createService({
  product: PRODUCT,
  action: 'RollbackApplication',
  params,
});

export const getWebshellToken = (
  params,
) => createService({
  product: PRODUCT,
  action: 'GetWebshellToken',
  params,
});

export const downloadFiles = (
  params,
  ignoreError?,
) => createService({
  product: PRODUCT,
  action: 'DownloadFiles',
  params,
  ignoreError,
});

export const uploadFiles = (
  params,
  ignoreError?,
) => createService({
  product: PRODUCT,
  action: 'UploadFiles',
  params,
  ignoreError,
});

export const analysisActualCost = ({
  params,
  ignoreError = true,
}) => createService({
  product: PRODUCT,
  action: 'AnalysisActualCost',
  params,
  ignoreError,
});

export const analysisReduceCost = ({
  params,
  ignoreError = true,
}) => createService({
  product: PRODUCT,
  action: 'AnalysisReduceCost',
  params,
  ignoreError,
});

export const  CheckApplicationInstanceVersion = (
  params: { AppId: string },
  ignoreError?,
) => createService({
  product: PRODUCT,
  action: 'CheckApplicationInstanceVersion',
  params,
  ignoreError,
});

export const DescribeInstanceLifecycleTimeline = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'DescribeInstanceLifecycleTimeline',
  params,
});

export const CheckAppNameV2 = (
  params,
  ignoreError = false,
) => createService({
  product: PRODUCT,
  action: 'CheckAppNameV2',
  params,
  ignoreError
});

export const UpdateAppAliasName = (
  params,
) => createService({
  product: PRODUCT,
  action: 'UpdateAppAliasName',
  params,
});
