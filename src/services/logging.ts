import createService from '~/utils/request';

const PRODUCT = 'sls';

export const ListProjects = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'ListProject',
  params,
  customErrorHandle
 });

export const ListStores = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) =>
  createService({
    product: PRODUCT,
    action: 'ListLogstore',
    params,
    customErrorHandle,
  });

export const ListLogtails = ({
  params
}) => createService({
  product: 'serverless',
  action: 'ListLogtails',
  params
});

export const DescribeUserBusinessStatus = (
  params
) => createService({
  product: 'Ubsms-inner',
  action: 'DescribeUserBusinessStatus',
  options: { apiType: 'inner' } as any,
  params
});

export const CreateWebLogResource = (
  params
) => createService({
  product: 'serverless',
  action: 'CreateWebLogResource',
  options: { apiType: 'roa' as any },
  params
});

export const CreateWebLogIndex = (
  params
) => createService({
  product: 'serverless',
  action: 'CreateWebLogIndex',
  options: { apiType: 'roa' as any },
  params
});

export const getSlsLogs = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: 'serverless',
  action: 'ListLogConfigs',
  params,
  customErrorHandle
});

export const getInstanceLog = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: 'serverless',
  action: 'DescribeInstanceLog',
  params,
  customErrorHandle
});
