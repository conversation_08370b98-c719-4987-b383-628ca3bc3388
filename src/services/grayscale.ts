import createService from '~/utils/request';

const PRODUCT = 'serverless';

interface IRCommonParams {
  params: any;
  customErrorHandle?: Function;
};


export const getSwimlaneGateway = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: 'mse',
  action: 'ListSwimmingLaneGateway',
  params,
  customErrorHandle,
});

export const getListGateway = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: 'mse',
  action: 'ListGateway',
  params,
  customErrorHandle,
});

export const getSwimlaneGroupApps = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'ListApplicationsForSwimmingLane',
  params,
  customErrorHandle,
});

export const getSwimlaneGroups = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'ListAllSwimmingLaneGroups',
  params,
  customErrorHandle
});

export const buildSwimlaneGroup = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'CreateOrUpdateSwimmingLaneGroup',
  params,
  customErrorHandle
});

export const deleteSwimlaneGroup = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'DeleteSwimmingLaneGroup',
  params,
  customErrorHandle
});

export const getSwimlaneGroupTags = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'ListSwimmingLaneGroupTags',
  params,
  customErrorHandle,
});

export const getSwimlaneGatewayRoute = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'ListSwimmingLaneGatewayRoutes',
  params,
  customErrorHandle,
});

export const buildSwimlane = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'CreateOrUpdateSwimmingLane',
  params,
  customErrorHandle
});

export const getSwimlanes = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'ListAllSwimmingLanes',
  params,
  customErrorHandle
});

export const deleteSwimlane = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: 'mse',
  action: 'DeleteSwimmingLane',
  params,
  customErrorHandle
});

export const getSwimlanePathPercent = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: 'mse',
  action: 'ListSwimPathPercent',
  params,
  customErrorHandle
});

export const getSwimlaneGroupAppOverview = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: 'mse',
  action: 'QueryAppSummaryMetricsOverview',
  params,
  customErrorHandle,
});

export const updateSwimlaneEnable = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: PRODUCT,
  action: 'UpdateSwimmingLaneEnableAttribute',
  params,
  customErrorHandle
});

export const getApigListGateway = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: 'APIG',
  action: 'ListGateways',
  params,
  customErrorHandle,
});

export const CheckCommodityStatus = ({ customErrorHandle }) =>
  createService({
    product: 'APIG',
    action: 'CheckCommodityStatus',
    customErrorHandle,
  });

export const getApigGateway = ({
  params,
  customErrorHandle,
}: IRCommonParams) => createService({
  product: 'APIG',
  action: 'GetGateway',
  params,
  customErrorHandle,
});



