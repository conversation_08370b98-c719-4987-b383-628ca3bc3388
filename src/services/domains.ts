import createService from '~/utils/request';

const PRODUCT = 'serverless';
const OPTION = {
  apiType: 'roa' as any,
}

export const listCustomDomains = (
  params
) => createService({
  product: PRODUCT, 
  action: 'ListWebCustomDomains',  
  params,
});

export const getCustomDomain = (
  params, ignoreError?
) => createService({
  product: PRODUCT, 
  action: 'DescribeWebCustomDomain',  
  params, 
  ignoreError,
});

export const createCustomDomain = (
  params,
  customErrorHandle?,
) => createService({
  product: PRODUCT, 
  action: 'CreateWebCustomDomain', 
  options: OPTION, 
  params,
  customErrorHandle,
});

export const updateCustomDomain = (
  params,
  customErrorHandle?,
) => createService({
  product: PRODUCT, 
  action: 'UpdateWebCustomDomain', 
  options: OPTION, 
  params,
  customErrorHandle,
});

export const deleteCustomDomain = (
  params
) => createService({
  product: PRODUCT, 
  action: 'DeleteWebCustomDomain', 
  options: OPTION, 
  params,
});

export const DescribeUserCertificateList = (
  params
) => createService({
  product: 'cas', 
  action: 'DescribeUserCertificateList',  
  params,
});

export const DescribeUserCertificateDetail = (
  params
) => createService({
  product: 'cas', 
  action: 'DescribeUserCertificateDetail',  
  params,
});