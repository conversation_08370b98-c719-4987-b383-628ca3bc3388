import moment from 'moment';
import createService from '~/utils/request';

const PRODUCT = 'serverless';

interface IRGetAppMetric {
  RegionId: string;
  Limit?: number;
  AppSource?: 'web' | 'micro_service';
  CpuStrategy?: 'always' | 'request';
};

interface IRGetAppWarningMetric extends IRGetAppMetric {
  StartTime?: number;
  EndTime?: number;
};

interface IRGetAppOrderMetric extends IRGetAppMetric {
  CreateTime?: number;
  AppId?: string;
  CoType?: string;
  OrderBy?: string;
};

interface IRGetAppArmsMetric extends IRGetAppMetric {
  StartTime?: number;
  EndTime?: number;
};


export const getEventLevelCount = (
  params: { RegionId: string, AppId?: string },
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'DescribeAppEventLevelCount',
  params,
  customErrorHandle
});

export const getUserAppsInfo = (
  params: { RegionId: string },
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'DescribeUserAppsInfo',
  params,
  customErrorHandle
});

export const getResourceQuota = (
  params: { RegionId: string },
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'DescribeResourceQuota',
  params,
  customErrorHandle
});

export const getResourceMeasure = (
  params: { RegionId: string },
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'DescribeUserMeasureInfo',
  params: {
    ...params,
    StartTime: moment().startOf('month').valueOf(),
    EndTime: moment().valueOf(),
  },
  customErrorHandle
});

export const getUserMeasure = (
  params: {
    RegionIds?: string;
    RegionId?: string;
    NamespaceId?: string;
    AppSource: 'web' | 'micro_service' | 'job' | 'micro_service,web';
    CpuStrategy: 'always' | 'request';
    StartTime: number;
    EndTime: number;
    WindowType: 'day' | 'month';
    NewSaeVersion?: string;
  },
  options = {},
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'ListUserMeasure',
  params,
  options,
  customErrorHandle
});

// 实例总数异常
export const getAvailabilityMetric = (
  params: IRGetAppMetric,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetAvailabilityMetric',
  params,
  customErrorHandle
});

// Warning事件次数
export const getWarningEventMetric = (
  params: IRGetAppWarningMetric,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetWarningEventMetric',
  params,
  customErrorHandle
});

// 变更单失败次数
export const getChangeOrderErrorMetric = (
  params: IRGetAppOrderMetric,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetChangeOrderMetric',
  params: {
    OrderBy: 'error',
    ...params,
  },
  customErrorHandle
});

// 变更单失败率
export const getChangeOrderErrorPercentMetric = (
  params: IRGetAppOrderMetric,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetChangeOrderMetric',
  params: {
    ...params,
    OrderBy: 'errorPercent'
  },
  customErrorHandle
});

// 弹性生效的应用
export const getScaleAppMetric = (
  params: IRGetAppMetric,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetScaleAppMetric',
  params,
  customErrorHandle
});

// 应用错误数
export const getArmsTopNErrorMetric = (
  params: IRGetAppArmsMetric,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetArmsTopNMetric',
  params: {
    ...params,
    OrderBy: 'error'
  },
  customErrorHandle
});

// 应用总请求量
export const getArmsTopNCountMetric = (
  params: IRGetAppArmsMetric,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetArmsTopNMetric',
  params: {
    ...params,
    OrderBy: 'count'
  },
  customErrorHandle
});

// 应用平均响应时间
export const getArmsTopNRtMetric = (
  params: IRGetAppArmsMetric,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetArmsTopNMetric',
  params: {
    ...params,
    OrderBy: 'rt'
  },
  customErrorHandle
});

// 获取资源包
export const getResourcePackage = ({
  params = {},
  customErrorHandle,
}) => createService({
  product: 'BssOpenApi',
  action: 'QueryResourcePackageInstances',
  params: {
    ...params,
    ProductCode: 'sae',
  },
  customErrorHandle
});




