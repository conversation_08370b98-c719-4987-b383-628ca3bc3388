import createService from '~/utils/request';

const PRODUCT = 'serverless';

export const createContact = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'CreateContact',
  params,
  customErrorHandle,
});

export const updateContact = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'UpdateContact',
  params,
  customErrorHandle,
});

export const getContacts = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'ListContacts',
  params,
  customErrorHandle,
});

export const deleteContact = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DeleteContact',
  params,
  customErrorHandle,
});

export const getRamUsers = ({
  params = {},
  customErrorHandle = (err, serviceConfig, callback) => {}
}) => createService({
  product: 'ram',
  action: 'ListUsers',
  params,
  customErrorHandle,
});

export const getRamUser = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: 'ram',
  action: 'GetUser',
  params,
  customErrorHandle,
});

export const combineContacts = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'CombineRamAndArmsContact',
  params,
  customErrorHandle,
});
