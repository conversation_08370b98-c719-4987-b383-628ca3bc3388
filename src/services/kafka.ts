import createService from '~/utils/request';

const PRODUCT = 'serverless';

export const DescribeKafkaInstance = ({
  params,
}) => createService({
  product: PRODUCT, 
  action: 'DescribeKafkaInstance',  
  params,
});

export const DescribeKafkaTopic = ({
  params,
}) => createService({
  product: PRODUCT, 
  action: 'DescribeKafkaTopic',  
  params,
});

export const getKafkaLogs = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({ 
  product: 'serverless', 
  action: 'DescribeAppKafkaLogConfigList', 
  params,
  customErrorHandle
});