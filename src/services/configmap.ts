import createService from '~/utils/request';

const PRODUCT = 'serverless';
const OPTION = {
  apiType: 'roa' as any,
}

export const ListNamespacedConfigMaps = (params) => createService({ product: PRODUCT, action: 'ListNamespacedConfigMaps', params });

export const ListResourceRevisions = (params) => createService({ product: PRODUCT, action: 'ListResourceRevisions', params });

export const CreateConfigMap = (params) => createService({ product: PRODUCT, action: 'CreateConfigMap', options: OPTION, params });

export const DescribeConfigMap = (params) => createService({ product: PRODUCT, action: 'DescribeConfigMap', params });

export const UpdateConfigMap = (params) => createService({ product: PRODUCT, action: 'UpdateConfigMap', options: OPTION, params });

export const DeleteConfigMap = (params) => createService({ product: PRODUCT, action: 'DeleteConfigMap', options: OPTION, params });