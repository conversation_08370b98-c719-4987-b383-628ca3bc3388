import createService from '~/utils/request';

const PRODUCT = 'serverless';

interface IRUnbindSourceCodeAccount {
  Provider: string;
  AccountId: string;
}

interface IRCreateSourceCodeAccount {
  Code: string;
  Provider: string;
}

interface IRListSourceCodeAccounts {
  Provider: string
}

interface IRListSourceCodeRepositories {
  Provider: string;
  AccountId: string;
  OrganizationId?: string;
}

interface IRListSourceCodeRepoBranches {
  Provider: string;
  AccountId: string;
  OrganizationId?: string;
  RepoId: string;
  RepoFullName: string;
}

interface IRGetAuthorizationUrl {
  Provider: string;
}

export const unbindSourceCodeAccount = (
  params: IRUnbindSourceCodeAccount,
  options?,
) => createService({
  product: PRODUCT,
  action: 'UnbindSourceCodeAccount',
  params,
  options,
});

export const createSourceCodeAccount = (
  params: IRCreateSourceCodeAccount,
  options?,
) => createService({
  product: PRODUCT,
  action: 'CreateSourceCodeAccount',
  params,
  options,
});

export const listSourceCodeAccounts = (
  params: IRListSourceCodeAccounts
) => createService({
  product: PRODUCT,
  action: 'ListSourceCodeAccounts',
  params
});

export const listSourceCodeRepositories = (
  params: IRListSourceCodeRepositories
) => createService({
  product: PRODUCT,
  action: 'ListSourceCodeRepositories',
  params
});

export const listSourceCodeRepoBranches = (
  params: IRListSourceCodeRepoBranches
) => createService({
  product: PRODUCT,
  action: 'ListSourceCodeRepoBranches',
  params
});

export const getAuthorizationUrl = (
  params: IRGetAuthorizationUrl
) => createService({
  product: PRODUCT,
  action: 'GetAuthorizationUrl',
  params
});