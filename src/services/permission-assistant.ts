import createService from '~/utils/request';

const PRODUCT = 'serverless';

export const GetPermissionAssistantRecords = (
  params,
  customErrorHandle?,
) => createService({
  product: PRODUCT,
  action: 'GetPermissionAssistantRecords',
  params,
  customErrorHandle,
});

export const GetPermissionAssistantResult = (
  params,
  customErrorHandle?,
) => createService({
  product: PRODUCT,
  action: 'GetPermissionAssistantResult',
  params,
  customErrorHandle,
});

export const DeletePermissionAssistantRecord = (
  params,
  customErrorHandle?,
) => createService({
  product: PRODUCT,
  action: 'DeletePermissionAssistantRecord',
  params,
  customErrorHandle,
});

export const DescribePermissionAssistant = () => createService({
  product: PRODUCT,
  action: 'DescribePermissionAssistantV2',
});
