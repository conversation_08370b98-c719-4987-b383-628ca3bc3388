import createService from '../utils/request';

const PRODUCT = 'serverless';

export const getAppChangeRecord = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'ListChangeOrders',
  params,
  customErrorHandle,
});

export const getAppChangeOrder = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribeChangeOrder',
  params,
  customErrorHandle,
});

export const getAppPipeline = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribePipeline',
  params,
  customErrorHandle,
});

export const getStageInstances = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribeAggregationStage',
  params,
  customErrorHandle,
});

export const restartPodInstances = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'RestartInstances',
  params,
  customErrorHandle,
});

export const deletePodInstances = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DeleteInstances',
  params,
  customErrorHandle,
});

export const rollbackChangeOrder = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'AbortAndRollbackChangeOrder',
  params,
  customErrorHandle,
});

export const redeployChangeOrder = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'OperationProceed',
  params,
  customErrorHandle,
});

export const abortChangeOrder = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'AbortChangeOrder',
  params,
  customErrorHandle,
});

export const confirmChangeOrderSuccess = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'ConfirmChangeOrderSuccess',
  params,
});

export const getNextBatchPipeline = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'ConfirmPipelineBatch',
  params,
  customErrorHandle,
});
export const ApprovalOperationRequest = params =>
  createService({
    product: PRODUCT,
    action: 'ApprovalOperationRequest',
    params,
  });

export const ListContactsWithoutDetailInfo = () =>
  createService({
    product: PRODUCT,
    action: 'ListContactsWithoutDetailInfo',
  });




