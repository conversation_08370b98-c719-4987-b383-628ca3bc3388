import callOpenApi from '~/utils/callOpenApi';
import createService from '~/utils/request';

const PRODUCT = 'serverless';
const OPTION = {
  apiType: 'roa' as any,
}

interface IRUpdateWebApplication {
  applicationID: string,
  applicationName: string;
  enableAppMetric: boolean;
  programmingLanguage?: string;
  effectiveImmediately: boolean;
}

interface IRGetAppVersionInstance {
  applicationID: string;
  applicationName: string;
  // 不传statuses 返回busy+idle的实例
  statuses?: string[];
  startTime: number;
  endTime: number;
  // 版本Id
  qualifier?: string;
};

interface IRGetWebApplication {
  applicationID: string;
  applicationName: string;
  qualifier?: string;
};

interface IRGetAppVersions {
  applicationID: string;
  applicationName: string;
  nextToken?: string;
  direction?: string;
  limit?: number;
};

interface IRGetAppVersionConf {
  applicationID: string;
  applicationName: string;
  // 版本Id
  qualifier?: string;
};

interface IRGetAppVersionInstance {
  applicationID: string;
  applicationName: string;
  // 不传statuses 返回busy+idle的实例
  statuses?: string[];
  startTime: number;
  endTime: number;
  // 版本Id
  qualifier?: string;
};

interface IRDeleteAppVersion {
  applicationID: string;
  applicationName: string;
  versionID: string;
}

interface IRDeleteBatchAppVersion {
  applicationID: string;
  applicationName: string;
  versionIds: string;
}

interface IRUpdateAppVersion {
  applicationName: string;
  imageConfig: {
    image: string;
    instanceID?: string;
    accelerationType: 'Default';
  },
  effectiveImmediately: boolean;
  description?: string;
  cpu: number;
  memorySize: number;
  scaleConfig: {
    minimumInstanceCount: number;  // 实例数范围
    maximumInstanceCount: number;
    alwaysAllocateCPU: boolean;  // 开启闲置计费
  };
  instanceConcurrency: number;  // 单实例并发请求数
  command?: string;
  args?: string;
  environmentVariables?: object;
  customHealthCheckConfig?: {
    httpGetUrl: string;
    initialDelaySeconds: number;
    periodSeconds: number;
    timeoutSeconds: number;
    failureThreshold: number;
    successThreshold: number;
  };
  vpcConfig?: {
    vpcId: string;
    vSwitchIds: string[];
    securityGroupId: string;
  };
  diskSize: 512;
  runtime: 'custom-container';
  handler: 'index.handler';
  internetAccess?: boolean;
}


interface IRGetAppTrafficConf {
  applicationID: string;
  applicationName: string;
};

interface IRUpdateAppTrafficConf {
  versionId: string;
  additionalVersionWeight: {
    [key: string]: number;
  }
};
interface IRUpdateWebAttributes {
  versionId?: string;
  description?: string;
  httpTriggerConfig: {
    [key: string]: number;
  }
};

interface IRGetAppScaleConf {
  applicationID: string;
  applicationName: string;
};

interface IRUpdateAppScaleConf {
  alwaysAllocateCPU: boolean;
  maximumInstanceCount: number;
  minimumInstanceCount: number;
};

interface IRGetAppVersionMetrics {
  applicationID: string;
  applicationName: string;
  versionID?: string;
  metrics: string;
  startTime: number;
  endTime: number;
  period: number;
  fillZero: boolean;
}

interface IRGetAppVersionInstanceMetrics {
  applicationID: string;
  applicationName: string;
  instanceID: string;
  metrics: string;
  startTime: number;
  endTime: number;
  period: number;
  fillZero: boolean;
}

interface IRGETInstanceExecAuthorization {
  stdin: boolean;
  stderr: boolean;
  stdout: boolean;
  tty: boolean;
  command: string[];
}

interface IRGetAppInstanceCpuMem {
  applicationID: string;
  applicationName: string;
  versionID?: string;
  instanceID?: string;
  startTime: number;
  endTime: number;
  pageNumber: number;
  pageSize: number;
}

interface IRGetAppStatics {
  regionId: string;
  applicationID: string;
  applicationName: string;
  endTime: number;
  startTime: number;
};

interface IRGetAppCost {
  Cpu: number;
  Memory: number;
  Workload: 'web_always' | 'web_request' | 'micro_service';
  ResourceType?: string;
  NewSaeVersion?: string;
};

interface IRUpdateWebApplication {
  applicationID: string,
  applicationName: string;
  enableAppMetric: boolean;
  effectiveImmediately: boolean;
}


export const getWebApplication = (
  params: IRGetWebApplication,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetWebApplicationInner',
  params,
  customErrorHandle
});

export const listWebApplications = (
  params,
  options?,
) => createService({
  product: PRODUCT,
  action: 'ListWebApplicationsInner',
  params,
  options,
});

export const listWebApplicationsForPermissionAssistant = (
  params,
  options?,
) => createService({
  product: PRODUCT,
  action: 'ListWebApplicationsForPermissionAssistant',
  params,
  options,
});

export const listWebApplicationsStatus = (
  params
) => createService({
  product: PRODUCT,
  action: 'ListWebApplicationsWithStatus',
  params
});

export const listMicroApplications = (
  config
) => createService({
  product: PRODUCT,
  action: 'ListApplications',
  ...config
});

export const createApplication = (
  params
) => createService({
  product: PRODUCT,
  action: 'CreateWebApplicationInner',
  options: OPTION,
  params
});

// export const deleteWebApplication = (
//   params,
//   customErrorHandle?,
// ) => createService({
//   product: PRODUCT,
//   action: 'DeleteWebApplicationInner',
//   options: OPTION,
//   params,
//   customErrorHandle,
// });

export const deleteWebApplication = (
  params,
  region,
) => callOpenApi(
  PRODUCT,
  'DeleteWebApplication',
  params,
  {
    region,
  }
) as Promise<any>;

// 获取应用版本 done
export const getListAppVersions = (
  params: IRGetAppVersions
) => createService({
  product: PRODUCT,
  action: 'ListApplicationVersions',
  params
});


// 创建应用版本
export const updateAppVersions = (
  applicationID: string,
  applicationName: string,
  content: IRUpdateAppVersion,
) => createService({
  product: PRODUCT,
  action: 'UpdateWebApplicationInner',
  options: OPTION,
  params: {
    params: {
      applicationID,
      applicationName
    },
    content: {
      ...content
    },
  }
});


// 删除应用版本
export const deleteAppVersion = (
  params: IRDeleteAppVersion
) => createService({
  product: PRODUCT,
  action: 'DeleteApplicationVersion',
  options: OPTION,
  params: { params },
});

// 删除应用版本
export const deleteBatchAppVersion = (
  params: IRDeleteBatchAppVersion
) => createService({
  product: PRODUCT,
  action: 'BatchDeleteApplicationVersion',
  options: OPTION,
  params: { params },
});


// 获取应用版本流量配置
export const getAppVersionTraffic = (
  params: IRGetAppTrafficConf
) => createService({
  product: PRODUCT,
  action: 'GetApplicationTrafficConfig',
  params
});


// 更新应用版本流量配置
export const updateAppVersionTraffic = (
  applicationID: string,
  applicationName: string,
  content: IRUpdateAppTrafficConf,
) => createService({
  product: PRODUCT,
  action: 'UpdateApplicationTrafficConfig',
  options: OPTION,
  params: {
    params: {
      applicationID,
      applicationName
    },
    content: {
      ...content
    }
  }
});

// 更新应用访问设置
export const updateWebAttributes = (
  applicationID: string,
  applicationName: string,
  content: IRUpdateWebAttributes,
) => createService({
  product: PRODUCT,
  action: 'UpdateWebAttributes',
  options: OPTION,
  params: {
    params: {
      applicationID,
      applicationName
    },
    content: {
      ...content
    }
  }
});


// 获取应用扩缩实例范围
export const getApplicationScale = (
  params: IRGetAppScaleConf
) => createService({
  product: PRODUCT,
  action: 'GetApplicationScaleConfig',
  params
});


// 更新应用扩缩实例范围
export const updateApplicationScale = (
  applicationID: string,
  applicationName: string,
  content: IRUpdateAppScaleConf,
) => createService({
  product: PRODUCT,
  action: 'UpdateApplicationScaleConfig',
  options: OPTION,
  params: {
    params: {
      applicationID,
      applicationName
    },
    content: {
      ...content
    }
  }
});


// 获取应用版本配置 done
export const getAppVersionConfig = (
  params: IRGetAppVersionConf,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetWebApplicationInner',
  params,
  customErrorHandle,
});


// 获取应用版本实例 done
export const getAppVersionInstances = (
  params: IRGetAppVersionInstance
) => createService({
  product: PRODUCT,
  action: 'ListApplicationInstances',
  params,
  ignoreError: true,
});


// 获取应用版本基础监控
export const getAppVersionMetrics = (
  params: IRGetAppVersionMetrics
) => createService({
  product: PRODUCT,
  action: 'ListApplicationMetrics',
  params
});


// 获取应用版本实例基础监控
export const getAppVersionInstanceMetrics = (
  params: IRGetAppVersionInstanceMetrics
) => createService({
  product: PRODUCT,
  action: 'ListInstanceMetrics',
  params
});


// 获取应用版本实例 vCPU、内存使用量/配额
export const getAppInstanceCpuMem = (
  params: IRGetAppInstanceCpuMem
) => createService({
  product: PRODUCT,
  action: 'ListInstancesMetrics',
  params
});


// 获取ws参数
export const getInstanceExecAuthorization = (
  applicationName: string,
  instanceID: string,
  applicationID: string,
  content: IRGETInstanceExecAuthorization,
) => createService({
  product: PRODUCT,
  action: 'InstanceExecAuthorization',
  options: OPTION,
  params: {
    params: {
      applicationName,
      instanceID,
      applicationID,
    },
    content: {
      ...content
    },
  }
});


export const getWebAppStatics = (
  params: IRGetAppStatics,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'GetWebResourceStatics',
  params,
  customErrorHandle
});

export const checkAppName = ({
  params,
  options = {},
  ignoreError = true,
}) => createService({
  product: PRODUCT,
  action: 'CheckAppName',
  params,
  options,
  ignoreError,
});


export const updateWebAppVpc = (
  applicationID: string,
  applicationName: string,
  content: {
    vpcConfig?: {
      vpcId: string;
      vSwitchIds: string[];
      securityGroupId: string;
    };
    internetAccess: boolean;
  },
) => createService({
  product: PRODUCT,
  action: 'UpdateWebApplicationMeta',
  options: OPTION,
  params: {
    params: {
      applicationID,
      applicationName
    },
    content: {
      ...content
    },
  }
});

export const startWebApplication = (
  params: {
    NamespaceId: string;
    ApplicationId: string;
  }
) => createService({
  product: PRODUCT,
  action: 'StartWebApplication',
  params,
});

export const stopWebApplication = (
  params: {
    NamespaceId: string;
    ApplicationId: string;
  },
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'StopWebApplication',
  params,
  customErrorHandle,
});

export const getApplicationCost = (
  params: IRGetAppCost,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'DescribeConfigurationPrice',
  params,
  customErrorHandle
});

export const listAppEvents = (
  params: IRGetAppCost,
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'ListAppEvents',
  params,
  customErrorHandle
});

// 更新应用 实际就是创建了一个版本
export const updateWebApplication = ({
  applicationID,
  applicationName,
  content,
  customErrorHandle,
} : {
  applicationID: string,
  applicationName: string,
  content: IRUpdateWebApplication,
  customErrorHandle?: Function,
}) => createService({
  product: PRODUCT,
  action: 'UpdateWebApplicationInner',
  options: OPTION,
  params: {
    params: {
      applicationID,
      applicationName
    },
    content: {
      ...content
    },
  },
  customErrorHandle,
});

export const getEventLevelCount = (
  params: { RegionId: string, AppId?: string },
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'DescribeAppEventLevelCount',
  params,
  customErrorHandle
});

export const getGrayTagRoutes = (
  params: { RegionId?: string, AppId: string },
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'ListGreyTagRoute',
  params,
  customErrorHandle
});

export const deleteGrayTagRoute = (
  params: { GreyTagRouteId: number },
  customErrorHandle?: Function
) => createService({
  product: PRODUCT,
  action: 'DeleteGreyTagRoute',
  params,
  customErrorHandle
});

export const getApplicationGroups = ({
  params,
  customErrorHandle,
}: {
  params: { AppId: string };
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationGroups',
  params,
  customErrorHandle
});

export const getApplicationInstances = ({
  params,
  customErrorHandle
}: {
  params: {
    AppId: string,
    GroupId?: string,
    PipelineId?: string,
    PageSize?: number,
    CurrentPage?: number,
    Reverse?: boolean,
  },
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationInstances',
  params,
  customErrorHandle,
});

export const getMetricsDatapoints = ({
  params,
  customErrorHandle,
}: {
  params: {
    MetricName: string,
    StartTime: number,
    EndTime: number,
    Dimensions: { [key: string]: string }
  };
  customErrorHandle?: Function;
}) => createService({
  product: 'metrics20190101',
  action: 'DescribeMetricList',
  params: {
    Namespace: 'acs_serverless',
    Period: 60,
    ...params,
  },
  customErrorHandle
});

export const createGreyTagRoute = ({
  params,
  customErrorHandle
}) => createService({
  product: PRODUCT,
  action: 'CreateGreyTagRoute',
  params,
  customErrorHandle,
});

export const updateGreyTagRoute = ({
  params,
  customErrorHandle
}) => createService({
  product: PRODUCT,
  action: 'UpdateGreyTagRoute',
  params,
  customErrorHandle,
});

export const queryNlbZones = ({ params, customErrorHandle }) =>
  createService({
    product: 'nlb',
    action: 'DescribeZones',
    params,
    customErrorHandle,
  });

export const DescribeVpcs = ({
  params,
  customErrorHandle
}) => createService({
  product: 'vpc',
  action: 'DescribeVpcs',
  params,
  customErrorHandle
});

export const DescribeVSwitches = ({
  params,
  customErrorHandle
}) => createService({
  product: 'vpc',
  action: 'DescribeVSwitches',
  params,
  customErrorHandle
});

export const getNlbLoadBalancers = ({ params, customErrorHandle }) =>
  createService({
    product: 'nlb',
    action: 'ListLoadBalancers',
    params,
    customErrorHandle,
  });

export const DescribeApplicationNlbs = ({ params }) =>
  createService({
    product: PRODUCT,
    action: 'DescribeApplicationNlbs',
    params,
  });
export const BindNlb = ({ params }) =>
  createService({
    product: PRODUCT,
    action: 'BindNlb',
    params,
  });

export const UnbindNlb = ({ params }) =>
  createService({
    product: PRODUCT,
    action: 'UnbindNlb',
    params,
  });


export const DescribeNamespaceInstances = ({ params }) =>
  createService({
    product: PRODUCT,
    action: 'DescribeNamespaceInstances',
    params,
  });

export const ListNamespacedAppServices = ({ params }) =>
  createService({
    product: PRODUCT,
    action: 'ListNamespacedAppServices',
    params,
  });