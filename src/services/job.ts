import callOpenApi from '~/utils/callOpenApi';
import createService from '~/utils/request';
const PRODUCT = 'serverless';

export const CreateJob = (config) => createService({
  product: PRODUCT,
  action: 'CreateJob',
  ...config,
});

export const UpdateJob = (config) => createService({
  product: PRODUCT,
  action: 'UpdateJob',
  ...config,
});

export const ListJobs = (config) => createService({
  product: PRODUCT,
  action: 'ListJobs',
  ...config,
});

export const ExecJob = (config) => createService({
  product: PRODUCT,
  action: 'ExecJob',
  ...config,
});

export const SuspendJob = (config) => createService({
  product: PRODUCT,
  action: 'SuspendJob',
  ...config,
});

export const DescribeJob = (config) => createService({
  product: PRODUCT,
  action: 'DescribeJob',
  ...config,
});

export const DescribeJobStatus = (config) => createService({
  product: PRODUCT,
  action: 'DescribeJobStatus',
  ...config,
});

export const BatchUpdateJobs = (config) => createService({
  product: PRODUCT,
  action: 'BatchUpdateJobs',
  ...config,
});


// export const DeleteJob = (config) => createService({
//   product: PRODUCT,
//   action: 'DeleteJob',
//   ...config,
// });

// 删除风控封装
export const DeleteJob = ({ params, options }) => callOpenApi(
  PRODUCT,
  'DeleteJob',
  params,
  options,
) as Promise<any>;

export const ListChangeOrders = (config) => createService({
  product: PRODUCT,
  action: 'ListChangeOrders',
  ...config,
});

export const DescribeJobHistory = (config) => createService({
  product: PRODUCT,
  action: 'DescribeJobHistory',
  ...config,
});

export const DeleteHistoryJob = (config) => createService({
  product: PRODUCT,
  action: 'DeleteHistoryJob',
  ...config,
});