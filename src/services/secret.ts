import createService from '~/utils/request';

const PRODUCT = 'serverless';
const OPTION = {
  apiType: 'roa' as any,
}

export const ListSecrets = (params) => createService({
  product: PRODUCT,
  action: 'ListSecrets',
  params
});

export const CreateSecret = (params) => createService({
  product: PRODUCT,
  action: 'CreateSecret',
  options: OPTION,
  params
});

export const DescribeSecret = (params) => createService({
  product: PRODUCT,
  action: 'DescribeSecret',
  params
});

export const UpdateSecret = (params) => createService({
  product: PRODUCT,
  action: 'UpdateSecret',
  options: OPTION,
  params
});

export const DeleteSecret = (params) => createService({
  product: PRODUCT,
  action: 'DeleteSecret',
  options: OPTION,
  params
});