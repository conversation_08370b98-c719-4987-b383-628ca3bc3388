import * as common from './common';
import * as application from './application';
import * as microApplication from './micro-application';
import * as domains from './domains';
import * as vpc from './vpc';
import * as logging from './logging';
import * as sourceCode from './sourceCode';
import * as pipeline from './pipeline';
import * as nas from './nas';
import * as oss from './oss';
import * as role from './role';
import * as configMap from './configmap';
import * as ubsms from './ubsms';
import * as kafka from './kafka';
import * as namespace from './namespace';
import * as crontab from './crontab';
import * as priceCalculator from './price-calculator';
import * as overview from './overview';
import * as eventCenter from './event-center';
import * as extra from './extra';
import * as microServices from './microServices';
import * as scaleRule from './scale-rule';
import * as acr from './acr';
import * as job from './job';
import * as gateway from './gateway';
import * as mseGateway from './mseGateway';
import * as changeOrder from './changeOrder';

export * from './secret';
export * from './crontab';
export * from './application';
export * from './micro-application';
export * from './package';
export * from './enterprise';
export * from './permission-assistant';
export * from './grayscale';
export * from './job';
export * from './scale-rule';
export * from './microServices';
export * from './acr';

export default {
  ...common,
  ...application,
  ...microApplication,
  ...domains,
  ...vpc,
  ...logging,
  ...sourceCode,
  ...pipeline,
  ...nas,
  ...oss,
  ...role,
  ...configMap,
  ...ubsms,
  ...kafka,
  ...namespace,
  ...crontab,
  ...priceCalculator,
  ...overview,
  ...eventCenter,
  ...microServices,
  ...scaleRule,
  ...acr,
  ...job,
  ...extra,
  ...gateway,
  ...mseGateway,
  ...changeOrder,
};
