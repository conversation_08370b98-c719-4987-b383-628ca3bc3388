import createService from '~/utils/request';

const PRODUCT = 'serverless';
const OPTION = {
  apiType: 'roa' as any,
}
interface IRUpdateBuildPipelineAttributes {
  ApplicationId: string;
  ApplicationName: string;
  PipelineId?: string;
  Enabled: boolean;
}

interface IRDescribeBuildPipeline {
  ApplicationId: string;
  ApplicationName: string;
  PipelineId?: string;
}

interface IRListBuildPipelineRuns {
  ApplicationId: string;
  ApplicationName: string;
  Status?: string;
  StartTime?: number;
  EndTime?: number;
  PageNo?: number;
  PageSize?: number;
}

interface IRCopyBuildPipelineRun {
  ApplicationId: string;
  ApplicationName: string;
  PipelineRunId: string;
}

interface IRStopBuildPipelineRun {
  ApplicationId: string;
  PipelineRunId: string;
}

interface IRListBuildPipelineRunsLogs {
  ApplicationId: string;
  ApplicationName: string;
  PipelineRunId: string;
  Offset?: number;
  Limit?: number;
}

interface IRDescribeBuildPipelineRun {
  ApplicationId: string;
  ApplicationName: string;
  PipelineRunId?: string;
  VersionId?: string;
}

export const createBuildPipeline = (
  params
) => createService({
  product: PRODUCT,
  action: 'CreateBuildPipeline',
  options: OPTION,
  params
});

export const updateBuildPipeline = ({
  params,
  customErrorHandle,
}) => createService({
  product: PRODUCT,
  action: 'UpdateBuildPipeline',
  options: OPTION,
  params,
  customErrorHandle,
});

// 更新流水线开关
export const updateBuildPipelineAttributes = (
  params: IRUpdateBuildPipelineAttributes
) => createService({
  product: PRODUCT,
  action: 'UpdateBuildPipelineAttributes',
  params
});

// 查询流水线详情
export const describeBuildPipeline = (
  params: IRDescribeBuildPipeline
) => createService({
  product: PRODUCT,
  action: 'DescribeBuildPipeline',
  params
});

// 查询流水线实例列表
export const listBuildPipelineRuns = (
  params: IRListBuildPipelineRuns
) => createService({
  product: PRODUCT,
  action: 'ListBuildPipelineRuns',
  params
});

// 复制流水线运行实例
export const copyBuildPipelineRun = (
  params: IRCopyBuildPipelineRun
) => createService({
  product: PRODUCT,
  action: 'CopyBuildPipelineRun',
  params
});

// 停止流水线运行实例
export const stopBuildPipelineRun = (
  params: IRStopBuildPipelineRun
) => createService({
  product: PRODUCT,
  action: 'StopBuildPipelineRun',
  params
});

// 查询流水线实例日志
export const listBuildPipelineRunsLogs = (
  params: IRListBuildPipelineRunsLogs
) => createService({
  product: PRODUCT,
  action: 'ListBuildPipelineRunLogs',
  params
});

// 查询流水线实例详情
export const DescribeBuildPipelineRun = (
  params: IRDescribeBuildPipelineRun
) => createService({
  product: PRODUCT,
  action: 'DescribeBuildPipelineRun',
  params
});