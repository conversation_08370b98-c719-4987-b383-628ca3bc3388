import createService from '~/utils/request';

export const GetRole = ({
  params,
  customErrorHandle
}) => createService({
  action: 'GetRole',
  product: 'resourcegroup',
  params,
  customErrorHandle,
});

export const CheckServiceLinkedRoleExistence = ({
  params
}) => createService({
  action: 'CheckServiceLinkedRoleExistence',
  product: 'ResourceManager',
  params,
});

export const CreateServiceLinkedRole = ({
  params,
  customErrorHandle
}) => createService({
  action: 'CreateServiceLinkedRole',
  product: 'resourcegroup',
  params,
  customErrorHandle,
});

export const ListRoles = ({
  params,
  customErrorHandle = (err, data, callback) => {},
}) => createService({
  action: 'ListRoles',
  product: 'ram',
  params,
  customErrorHandle,
});