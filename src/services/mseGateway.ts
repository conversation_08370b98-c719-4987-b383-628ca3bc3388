import createService from '../utils/request';

interface IParams<T> {
  params: T,
  customErrorHandle?: (error: { code: string }, params: T, cb?: () => void) => void;
}

const PRODUCT = 'serverless';
const MSE = 'mse';

interface IRGetMseGateway {
  PageNumber: number;
  PageSize: number;
  GatewayUniqueId?: string;
  FilterParams: object;
};

export const getListGateway = (
  {
    params, customErrorHandle,
  }: IParams<IRGetMseGateway>
) => createService({
  product: MSE,
  action: 'ListGateway',
  params, customErrorHandle
});

export const getDomainList = (
  {
    params,
    customErrorHandle
  }:IParams<IRGetMseGateway>
) => createService({
  product: MSE,
  action: 'ListGatewayDomain',
  params,
  customErrorHandle
});

export const getRegExpCheck = (
  params,
) => createService({
  product: MSE,
  action: 'GetRegExpCheck',
  params
});

export const getRegExpTest = (
  params,
) => createService({
  product: MSE,
  action: 'GetRegExpTest',
  params
});

export const listApplications = (
  params,
) => createService({
  product: PRODUCT,
  action: 'ListApplications',
  params
});

export const listAppServicesPage = (
  params,
) => createService({
  product: PRODUCT,
  action: 'ListAppServicesPage',
  params
});

export const getMicroAppStatus = (
  params,
) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationConfig',
  params
});

export const listMseNacosInstances = (
  {
    params, customErrorHandle,
  }: IParams<any>
) => createService({
  product: MSE,
  action: 'ListClusters',
  params, customErrorHandle
});

export const listMseNacosNamespaces = (
  params,
) => createService({
  product: MSE,
  action: 'ListEngineNamespaces',
  params
});

export const listMseNacosServices = (
  params,
) => createService({
  product: MSE,
  action: 'ListAnsServices',
  params
});

export const describeAppServiceDetail = (
  params,
) => createService({
  product: PRODUCT,
  action: 'DescribeAppServiceDetail',
  params
});

export const createMSEIngress = (
  params,
) => createService({
  product: PRODUCT,
  action: 'CreateMSEIngress',
  params
});

export const describeMSEIngress = (
  params,
  customErrorHandle?,
) => createService({
  product: PRODUCT,
  action: 'DescribeMSEIngress',
  params,
  customErrorHandle,
});

export const getServiceList = ({
  params,
  customErrorHandle
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: MSE,
  action: 'GetServiceList',
  params,
  customErrorHandle
});

export const updateMSEIngress = (
  params,
) => createService({
  product: PRODUCT,
  action: 'UpdateMSEIngress',
  params
});

export const deleteMSEIngress = (
  params,
) => createService({
  product: PRODUCT,
  action: 'DeleteMSEIngress',
  params
});