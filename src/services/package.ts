import createService from '~/utils/request';

const PRODUCT = 'serverless';
// const OPTION = {
//   apiType: 'roa' as any,
// }


export const DescribeComponents = (
  config
) => createService({
  action: 'DescribeComponents',
  product: PRODUCT, 
  ...config,
});

export const DescribeEdasContainers = (
  config
) => createService({
  action: 'DescribeEdasContainers',
  product: PRODUCT, 
  ...config,
});

export const AssignUploadSignature = (
  config
) => createService({
  action: 'AssignUploadSignature',
  product: PRODUCT, 
  ...config,
});

export const AssignUploadSignatureV2 = (
  config
) => createService({
  action: 'AssignUploadSignatureV2',
  product: PRODUCT, 
  ...config,
});