import createService from '~/utils/request';
const PRODUCT = 'serverless';

export const ListTimerRules = (
  config
) => createService({
  product: PRODUCT,
  action: 'ListTimerRules',
  ...config
});

export const CreateTimerRule = (
  config
) => createService({
  product: PRODUCT,
  action: 'CreateTimerRule',
  ...config
});

export const UpdateTimerRule = (
  config
) => createService({
  product: PRODUCT,
  action: 'UpdateTimerRule',
  ...config
});

export const DisableTimerRule = (
  config
) => createService({
  product: PRODUCT,
  action: 'DisableTimerRule',
  ...config
});

export const EnableTimerRule = (
  config
) => createService({
  product: PRODUCT,
  action: 'EnableTimerRule',
  ...config
});

export const DeleteTimerRule = (
  config
) => createService({
  product: PRODUCT,
  action: 'DeleteTimerRule',
  ...config
});

export const ListTriggerTimes = (
  config
) => createService({
  product: 'oos20200101',
  action: 'ListTriggerTimes',
  ignoreError: true,
  ...config
});