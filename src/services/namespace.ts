import createService from '~/utils/request';
import { IHttpRequired } from './gateway';

const PRODUCT = 'serverless';

interface IRGetNamespace{
  RegionId?: string;
  PageSize: number;
  CurrentPage: number;
};

interface IRDeleteNamespace extends IHttpRequired{
  NamespaceId: string;
};


interface IRNamespace {
  NamespaceId: string;
  NamespaceName: string;
  NamespaceDescription: string;
  VpcId: string;
};

export const getBelongNamespace = (
  params: IRGetNamespace,
) => createService({
  product: PRODUCT,
  action: 'DescribeNamespaces',
  params
});

export const deleteNamespace = (
  params: IRDeleteNamespace,
) => createService({
  product: PRODUCT,
  action: 'DeleteNamespace',
  params
});

export const createNamespace = (
  params: IRNamespace,
) => createService({
  product: PRODUCT,
  action: 'CreateNamespace',
  params
});

export const updateNamespace = (
  params: IRNamespace,
) => createService({
  product: PRODUCT,
  action: 'UpdateNamespace',
  params
});

export const getApplicationConf = (
  params: {
    AppId: string;
    RegionId: string;
  }
) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationConfig',
  params
});

export const getNamespaceListV2 = ({
  params,
  customErrorHandle,
}: {
  params: IRGetNamespace;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'ListNamespacesV2',
  params,
  customErrorHandle,
});

export const createNamespaceV2 = ({
  params,
  customErrorHandle
}: {
  params: IRNamespace;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'CreateNamespaceV2',
  params,
  customErrorHandle
});

export const getNamespaceResources = (
  params: {
    NamespaceId: string;
  },
) => createService({
  product: PRODUCT,
  action: 'DescribeNamespaceResources',
  params
});

export const getNamespaceResourcesV2 = (
  params: {
    NamespaceId: string;
  },
) => createService({
  product: PRODUCT,
  action: 'DescribeNamespaceResourcesV2',
  params
});

export const getNamespaceDescribeV2 = (
  params: {
    NamespaceId: string;
  },
) => createService({
  product: PRODUCT,
  action: 'DescribeNamespaceV2',
  params
});

export const updateNamespaceVpc = (
  params: {
    VpcId: string;
    NamespaceId: string;
    RegionId: string;
  }
) => createService({
  product: PRODUCT,
  action: 'UpdateNamespaceVpc',
  params
});

export const getChangeRecord = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'ListNamespaceChangeOrders',
  params,
  customErrorHandle,
});

export const getChangeOrder = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribeChangeOrder',
  params,
  customErrorHandle,
});

export const getPipeline = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribePipeline',
  params,
  customErrorHandle,
});

export const getNestedStage = ({
  params,
  customErrorHandle,
}: {
  params;
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'DescribeNestedStage',
  params,
  customErrorHandle,
});

export const getBelongVpcs = (
  params: {
    Force: boolean;
    RegionId: string;
  }
) => createService({
  product: PRODUCT,
  action: 'DescribeVpcs',
  params
});

export const DescribeNetworkQuotas = (
  params
) => createService({
  product: 'Netana',
  action: 'DescribeNetworkQuotas',
  params
});


export const getApplications = ({
  params,
  customErrorHandle,
}: {
  params: {
    NamespaceId: string;
    RegistryType: 0 | 2;
    NacosInstanceId?: string;
    NacosNamespaceId?: string;
  };
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'ListApplicationsForGatewayRoute',
  params,
  customErrorHandle,
});

export const getServices = ({
  params,
  customErrorHandle,
}: {
  params: {
    AppId: string;
    ServiceType: 'springCloud' | 'dubbo' | 'k8sService',
    RegistryType: 0 | 2 | 9;
    NacosInstanceId?: string;
    NacosNamespaceId?: string;
    PageNumber: number;
    PageSize: number;
  };
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'ListAppServices',
  params,
  customErrorHandle,
});

export const getServicePorts = ({
  params,
  customErrorHandle,
}: {
  params: {
    AppId: string;
    ServiceType: 'springCloud' | 'dubbo' | 'k8sService',
    RegistryType: 0 | 2 | 9;
    NacosInstanceId?: string;
    NacosNamespaceId?: string;
    ServiceName: string;
    ServiceGroup: string;
    ServiceVersion?: string;

  };
  customErrorHandle?: Function;
}) => createService({
  product: PRODUCT,
  action: 'GetAppService',
  params,
  customErrorHandle,
});
