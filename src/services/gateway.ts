import { ApiType } from '@ali/cnd';
import createService from '../utils/request';

const PRODUCT = 'serverless';
const LOADBALANCE = 'slb';

export interface IHttpRequired {
  RegionId?: string;
}

interface IRGetIngresses extends IHttpRequired {
  NamespaceId: string;
  AppId?: string;
  CurrentPage?: number;
  PageSize?: number;
};

interface IRGetLoadBalance extends IHttpRequired {
  Address?: string;
  AddressType?: string;
  PageNumber?: number;
  PageSize?: number;
  VpcId?: string | number;
  LoadBalancerId?: string | number;
};

interface IRGetLoadBalanceInfo extends IHttpRequired {
  LoadBalancerId: string | number;
};

interface IRGetNamespaceResources extends IHttpRequired {
  NamespaceId: string;
};

interface IRGetCertificates extends IHttpRequired {
  ServerCertificateId?: number | string;
}

interface IRGetSSLCertificate extends IHttpRequired {
  ShowSize: number;
  CurrentPage: number;
  SearchValue?: string | number;
};

interface IRGetAlbBalance extends IHttpRequired {
  NextToken: string;
  MaxResults: number;
  AddressType?: string;
};

interface IRGetIngressInfo extends IHttpRequired {
  IngressId: number;
};

interface IRCheckIngress extends IHttpRequired {
  IngressId: number;
  NamespaceId: string;
};

interface IRDeleteIngress extends IHttpRequired {
  IngressId: number;
};

interface IRGetAppList extends IHttpRequired {
  NamespaceId: string;
};

interface IRIngresses extends IHttpRequired {
  Description: string;
  ListenerProtocol: string;
  SlbId: string;
  ListenerPort: string;
  NamespaceId: string;
  Rules?: IRule[];       // as string
  DefaultRule: IRule;   // as string
  LoadBalanceType: string;
  CertIds: string;
  IngressId?: string | number;
}

interface IRule {
  appId: string;
  backendProtocol: string;
  containerPort: number;
  domain?: string;
  path?: string;
  appName?: string;
};

interface IParams<T> {
  params: T,
  customErrorHandle?: (error: { code: string }, params: T, cb?: () => void) => void;
}


export const getListIngresses = (
  params: IRGetIngresses,
) => createService({
  product: PRODUCT,
  action: 'ListIngresses',
  params
});


export const getLoadBalancers = (
  {
    params,
    customErrorHandle,
  }: IParams<IRGetLoadBalance>
) => createService({
  product: LOADBALANCE,
  action: 'DescribeLoadBalancers',
  params, customErrorHandle,
});

export const getLogsDownloadAttribute = ({ params, customErrorHandle }) =>
  createService({
    product: LOADBALANCE,
    action: 'DescribeAccessLogsDownloadAttribute',
    params,
    customErrorHandle,
  });

export const getLoadBalanceInfo = (
  {
    params,
    customErrorHandle
  }:IParams<IRGetLoadBalanceInfo>
) => createService({
  product: LOADBALANCE,
  action: 'DescribeLoadBalancerAttribute',
  params,
  customErrorHandle
});


export const getNamespaceResources = (
  params: IRGetNamespaceResources,
) => createService({
  product: PRODUCT,
  action: 'DescribeNamespaceResources',
  params
});

export const getServerCertificates = ({
  params,
  customErrorHandle
}: IParams<IRGetCertificates>
) => createService({
  product: LOADBALANCE,
  action: 'DescribeServerCertificates',
  params,
  customErrorHandle
});

export const getServerCaCertificates = (
  {
    params, customErrorHandle,
  }: IParams<IRGetCertificates>
) => createService({
  product: LOADBALANCE,
  action: 'DescribeCACertificates',
  params, customErrorHandle
});

export const getSSLCertificateList = (
  {
    params, customErrorHandle
  }: IParams<IRGetSSLCertificate>
) => createService({
  product: 'cas20200619',
  action: 'DescribeSSLCertificateList',
  params, customErrorHandle,
});

export const GetProductQuota = (
  {
    params,
  }
) => createService({
  product: 'quotas',
  action: 'GetProductQuota',
  params,
});

export const getAlbLoadBalancers = (
  {
    params,
    customErrorHandle,
  }: IParams<IRGetAlbBalance>
) => createService({
  product: 'alb',
  action: 'ListLoadBalancers',
  params, customErrorHandle
});

export const getIngressInfo = (
  params: IRGetIngressInfo,
) => createService({
  product: PRODUCT,
  action: 'DescribeIngress',
  params
});

export const checkIngressWarn = (
  params: IRCheckIngress,
) => createService({
  product: PRODUCT,
  action: 'CheckIngressConfigWarn',
  params
});

export const disableIngressWarn = (
  params: IRCheckIngress,
) => createService({
  product: PRODUCT,
  action: 'DisableIngressConfigWarn',
  params
});

export const deleteIngress = (
  params: IRDeleteIngress,
) => createService({
  product: PRODUCT,
  action: 'DeleteIngress',
  params
});

export const getApplicationList = (
  params: IRGetAppList,
) => createService({
  product: PRODUCT,
  action: 'ListApplications',
  params
});

export const syncUserLoadBalancer = (
  params: IHttpRequired,
) => createService({
  product: PRODUCT,
  action: 'SyncUserLoadBalancer',
  params
});

export const createIngressGateway = (
  params: IRIngresses,
) => createService({
  product: PRODUCT,
  action: 'CreateIngress',
  params
});

// export const updateIngressGateway = (
//   params: IRIngresses,
//   content: { Rules: IRule[] }
// ) => createService({
//   product: PRODUCT,
//   action: 'UpdateIngress',
//   options: {
//     apiType: 'roa' as any,
//   },
//   params: {
//     params: {
//       ...params
//     },
//     content: {
//       ...content
//     },
//   }
// });

export const updateIngressGateway = (
  params
) => createService({
  product: PRODUCT,
  action: 'UpdateIngress',
  params,
  options: {
    apiType: 'roa' as ApiType,
  }
});

export const getCustomSecurityPolicies = (
  {
    params, customErrorHandle,
  }: IParams<{ MaxResults: number, RegionId: string }>
) => createService({
  product: 'alb',
  action: 'ListSecurityPolicies',
  params, customErrorHandle
});

export const getSystemSecurityPolicies = (
  params: { RegionId: string },
) => createService({
  product: 'alb',
  action: 'ListSystemSecurityPolicies',
  params
});


interface IRGetHttpApiGateways {
 pageSize: number;
 pageNumber: number;
 gatewayId?: string;
 name?: string;
}
export const getHttpApiGateways = ({ params, customErrorHandle }: IParams<IRGetHttpApiGateways>) =>
  createService({
    product: 'APIG',
    action: 'ListGateways',
    params,
    customErrorHandle,
  });

export const getHttpApiDomains = ({ params, customErrorHandle }) =>
  createService({
    product: 'APIG',
    action: 'ListDomains',
    params,
    customErrorHandle,
  });

export const getHttpApiRoute = ({ params, customErrorHandle }) =>
  createService({
    product: PRODUCT,
    action: 'GetHttpApiRoute',
    params,
    customErrorHandle,
  });

export const createHttpApiRoute = ({ params, content }) =>
  createService({
    product: PRODUCT,
    action: 'CreateHttpApiRoute',
    options: { apiType: 'roa' as any },
    params: {
      params: { ...params },
      content: { ...content },
    },
  });

export const updateHttpApiRoute = ({ params, content }) =>
  createService({
    product: PRODUCT,
    action: 'UpdateHttpApiRoute',
    options: { apiType: 'roa' as any },
    params: {
      params: { ...params },
      content: { ...content },
    },
  });

// apig---发布HTTP路由
export const deployHttpApiRoute = ({ params }) =>
  createService({
    product: PRODUCT,
    action: 'DeployHttpApiRoute',
    params
  });

// apig---检查路由是否冲突
export const detectHttpApiConflicts = ({ params, content}) =>
  createService({
    product: 'APIG',
    action: 'DetectHttpApiConflicts',
    options: { apiType: 'roa' as any },
    params: {
      params: { ...params },
      content: { ...content },
    },
  });

// apig---删除路由
export const deleteHttpApiRoute = ({ params }) =>
  createService({
    product: PRODUCT,
    action: 'DeleteHttpApiRoute',
    params,
  });

  export const offlineRouter=({ params, content})=>{
    createService({
      product: 'APIG',
      action: 'UndeployHttpApi',
      options: { apiType: 'roa' as any },
      params: {
        params: { ...params },
        content: { ...content },
      },
    });
  }





