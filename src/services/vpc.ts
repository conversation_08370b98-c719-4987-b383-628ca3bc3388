import createService from '~/utils/request';
import { noop } from 'lodash';

export const DescribeVpcs = ({
  params,
  ignoreError = false,
  customErrorHandle = noop,
}) => createService({
  product: 'vpc',
  action: 'DescribeVpcs',
  params,
  ignoreError,
  customErrorHandle
});

export const DescribeVSwitches = ({
  params,
  ignoreError = false,
  customErrorHandle
}) => createService({
  product: 'vpc',
  action: 'DescribeVSwitches',
  params,
  ignoreError,
  customErrorHandle
});

export const DescribeSecurityGroups = (
  params
) => createService({
  product: 'ecs',
  action: 'DescribeSecurityGroups',
  params
});

export const DescribeVSwitchAttributes = ({ params }) =>
  createService({
    product: 'vpc',
    action: 'DescribeVSwitchAttributes',
    params,
  });