import createService from '~/utils/request';
const PRODUCT = 'serverless';

export const ListEventType = params =>
  createService({
    product: PRODUCT,
    action: 'ListEventType',
    params,
  });

export const ListNamespacesForPermissionAssistant = params =>
  createService({
    product: PRODUCT,
    action: 'ListNamespacesForPermissionAssistant',
    params,
  });
export const ListApplicationsForPermissionAssistant = (params, options?) =>
  createService({
    product: PRODUCT,
    action: 'ListApplicationsForPermissionAssistant',
    params,
    options,
  });
export const ListEventRecord = params =>
  createService({
    product: PRODUCT,
    action: 'ListEventRecord',
    params,
  });

export const ListContacts = params =>
  createService({
    product: PRODUCT,
    action: 'ListContacts',
    params,
  });

export const CreateEventSubscribeRule = params =>
  createService({
    product: PRODUCT,
    action: 'CreateEventSubscribeRule',
    params,
  });
export const UpdateEventSubscribeRule = params =>
  createService({
    product: PRODUCT,
    action: 'UpdateEventSubscribeRule',
    params,
  });

export const ListEventSubscribeRule = params =>
  createService({
    product: PRODUCT,
    action: 'ListEventSubscribeRule',
    params,
  });

export const DeleteEventSubscribeRule = params =>
  createService({
    product: PRODUCT,
    action: 'DeleteEventSubscribeRule',
    params,
  });

export const ListOperationApprovalRules = ({
  params,
  customErrorHandle = (err, serviceConfig, callback) => {}
}) =>
  createService({
    product: PRODUCT,
    action: 'ListOperationApprovalRules',
    params,
    customErrorHandle,
  });

export const ListApprovedOperationTypes = () =>
  createService({
    product: PRODUCT,
    action: 'ListApprovedOperationTypes',
    ignoreError: true,
  });

export const DeleteOperationApprovalRule = params =>
  createService({
    product: PRODUCT,
    action: 'DeleteOperationApprovalRule',
    params,
  });
export const ListRamUsers = ({
  params = {},
  customErrorHandle = (err, serviceConfig, callback) => {}
}) =>
  createService({
    product: 'ram',
    action: 'ListUsers',
    params,
    customErrorHandle,
  });

export const ListContactsWithoutDetailInfo = () =>
  createService({
    product: PRODUCT,
    action: 'ListContactsWithoutDetailInfo',
  });

export const CreateOperationApprovalRule = params =>
  createService({
    product: PRODUCT,
    action: 'CreateOperationApprovalRule',
    params,
  });

export const UpdateOperationApprovalRule = params =>
  createService({
    product: PRODUCT,
    action: 'UpdateOperationApprovalRule',
    params,
  });

export const ListOperationApprovalRecords = params =>
  createService({
    product: PRODUCT,
    action: 'ListOperationApprovalRecords',
    params,
  });

export const ApprovalOperationRequest = params =>
  createService({
    product: PRODUCT,
    action: 'ApprovalOperationRequest',
    params,
  });

export const ResendApprovalNotification = params =>
  createService({
    product: PRODUCT,
    action: 'ResendApprovalNotification',
    params,
  });

export const CancelOperationApproval = params =>
  createService({
    product: PRODUCT,
    action: 'CancelOperationApproval',
    params,
  });
