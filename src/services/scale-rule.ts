import createService from '~/utils/request';

const PRODUCT = 'serverless';

export const createApplicationScalingRule = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'CreateApplicationScalingRule',
  params,
});

export const updateApplicationScalingRule = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'UpdateApplicationScalingRule',
  params,
});

export const describeApplicationScalingRules = ({
  params,
  options = {},
}) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationScalingRules',
  params,
  options,
});

export const deleteApplicationScalingRule = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'DeleteApplicationScalingRule',
  params,
});

export const enableApplicationScalingRule = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'EnableApplicationScalingRule',
  params,
});

export const disableApplicationScalingRule = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'DisableApplicationScalingRule',
  params,
});

export const describeApplicationScalingRuleStatus = ({
  params,
}) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationScalingRuleStatus',
  params,
});