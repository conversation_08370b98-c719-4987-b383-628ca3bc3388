import createService from '~/utils/request';

const PRODUCT = 'serverless';

export const GetAccountSettings = (
  params?
) => createService({
  product: PRODUCT,
  action: 'GetAccountSettings',
  params
});

export const GetWebQuota = ({
  ignoreError = false,
}) => createService({
  product: PRODUCT,
  action: 'GetWebQuota',
  ignoreError,
});

export const DescribeNamespaceList = (config) => createService({
  product: PRODUCT,
  action: 'DescribeNamespaceList',
  ...config,
});

export const CheckFeatureConfig = (params?,options?) => createService({
  product: PRODUCT,
  action: 'CheckFeatureConfig',
  params,
  options,
});

export const checkInDebt = (params?,options?) => createService({
  product: PRODUCT,
  action: 'CheckInDebt',
  params,
  options,
});

export const getWebFeatureConfig = (options?) => createService({
  product: PRODUCT,
  action: 'GetWebFeatureConfig',
  options,
  ignoreError: true,
});

export const checkMseRole = () => createService({
  product: 'mse',
  action: 'CheckServiceLinkRole',
  ignoreError: true,
});

export const createMseRole = () => createService({
  product: 'mse',
  action: 'InitializeServiceLinkRole',
  ignoreError: true,
});

export const DescribeApplicationGroups = (params: { AppId: string }) =>
  createService({
    product: PRODUCT,
    action: 'DescribeApplicationGroups',
    params,
  });

export const DescribeApplicationInstances = (params: { AppId: string; GroupId: string, PageSize?: number, CurrentPage?: number }) =>
  createService({
    product: PRODUCT,
    action: 'DescribeApplicationInstances',
    params,
  });

export const getInstanceLog = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'DescribeInstanceLog',
  params,
  customErrorHandle
});

export const getKafkaLogs = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'DescribeAppKafkaLogConfigList',
  params,
  customErrorHandle
});

export const getApplicationSlbs = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'DescribeApplicationSlbs',
  params,
  customErrorHandle
});

export const getSlsLogs = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'ListLogConfigs',
  params,
  customErrorHandle
});

export const getSlbQuota = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'DescribeSlbQuota',
  params,
  customErrorHandle
});

export const getAvailableSlbs = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'ListAppAvailableSlbs',
  params,
  customErrorHandle
});

export const getSlbWarnState = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'CheckApplicationSlbConfigWarn',
  params,
  customErrorHandle
});

export const getLoadBalancerAttribute = (
  params,
) => createService({
  product: 'slb',
  action: 'DescribeLoadBalancerHTTPSListenerAttribute',
  params
});

export const setLoadBalancerHTTPSListenerAttribute = (
  params,
) => createService({
  product: 'slb',
  action: 'SetLoadBalancerHTTPSListenerAttribute',
  params
});

export const setBindSlb = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'BindSlb',
  params,
  customErrorHandle
});

export const setUnbindSlb = ({
  params,
  customErrorHandle = (error, values, callback?) => {},
}) => createService({
  product: PRODUCT,
  action: 'UnbindSlb',
  params,
  customErrorHandle
});

export const getLoadBalancerWarmup = (
  params,
) => createService({
  product: PRODUCT,
  action: 'DescribeAppSlbWarmup',
  params
});

export const closeLoadBalancerWarmup = (
  params,
) => createService({
  product: PRODUCT,
  action: 'DeleteSlbWarmup',
  params
});

export const updateLoadBalancerWarmup = (
  params,
) => createService({
  product: PRODUCT,
  action: 'CreateSlbWarmup',
  params
});

export const DescribeVSwitches = ({
  params,
  customErrorHandle
}) => createService({
  product: 'vpc',
  action: 'DescribeVSwitches',
  params,
  customErrorHandle
});

export const QueryAlbZones = ({
  params,
}) => createService({
  product: 'alb',
  action: 'DescribeZones',
  params
});

export const getAccountQuota =( customErrorHandle ) => createService({
  product: 'BssOpenApi',
  action: 'QueryAccountBalance',
  customErrorHandle
});

