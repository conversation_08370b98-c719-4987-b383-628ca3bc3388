import createService from '~/utils/request';

export const SentinelListTopNAppsSummaryMetricOfAppType = ({
  params,
  ignoreError,
}) => createService({
  product: 'ahas', 
  action: 'SentinelListTopNAppsSummaryMetricOfAppType',
  params,
  ignoreError,
});

export const getMseUserStatus = ({
  ignoreError,
  customErrorHandle,
}: {
  ignoreError?: boolean;
  customErrorHandle?: Function;
}) => createService({
  product: 'mse',
  action: 'GetUserStatus',
  customErrorHandle,
  ignoreError,
});