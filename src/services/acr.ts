import { createService } from '@ali/cnd';

const ARC_PRODUCT = 'acr';
const RC_PRODUCT = 'cr';

// 获取个人版实例
export const GetInstance = createService(ARC_PRODUCT, 'GetInstance');

export const GetUserInfo = createService(ARC_PRODUCT, 'GetUserInfo');

// 获取仓库信息
export const GetRepo = createService(ARC_PRODUCT, 'GetRepo');

// 获取个人命名空间
export const ListNamespace = createService(ARC_PRODUCT, 'ListNamespace');

// 个人版实例列表
export const ListRepoByNamespace = createService(ARC_PRODUCT, 'ListRepoByNamespace');

// 个人版 tag 列表
export const listRepoTag = createService(ARC_PRODUCT, 'ListRepoTag');

// 个人 tag 列表
export const ListTagRepos = async (params: { RepoNamespace: string; RepoName: string }) => {
  const service = createService(ARC_PRODUCT, 'ListRepoTag');
  let page = 1;
  let total = 0;
  const tags = [];
  do {
    const { data } = await service({ Page: page, PageSize: 100, ...params });
    page += 1;
    total = data.total;
    tags.push(...data.tags)
  } while (tags.length < total);

  return tags;
};


// 企业实例接口
export const ListInstance = async () => {
  const service = createService(RC_PRODUCT, 'ListInstance');

  let page = 1;
  let total = 0;
  const instances = [];
  do {
    const result = await service({ PageNo: page });
    page += 1;
    total = result.TotalCount;
    instances.push(...result.Instances)
  } while (instances.length < total);

  return instances;
}
export const ListRepoTag = async (params) => {
  const service = createService(RC_PRODUCT, 'ListRepoTag');
  let page = 1;
  let total = 0;
  const images = [];
  do {
    const result = await service({ ...params, PageNo: page, PageSize: 1000 });
    page += 1;
    total = result.TotalCount;
    images.push(...result.Images)
  } while (images.length < total);

  return images;
}
export const CrListNamespace = async (params) => {
  const service = createService(RC_PRODUCT, 'ListNamespace');
  let page = 1;
  let total = 0;
  const namespace = [];
  do {
    const result = await service({ ...params, PageNo: page, PageSize: 1000 });
    page += 1;
    total = result.TotalCount;
    namespace.push(...result.Namespaces)
  } while (namespace.length < total);

  return namespace;
}
export const ListInstanceEndpoint = createService(RC_PRODUCT, 'ListInstanceEndpoint');
export const ListRepository = createService(RC_PRODUCT, 'ListRepository');

export const GetArtifactBuildRule = createService(RC_PRODUCT, 'GetArtifactBuildRule');

export const CreateArtifactBuildRule = createService(RC_PRODUCT, 'CreateArtifactBuildRule');

