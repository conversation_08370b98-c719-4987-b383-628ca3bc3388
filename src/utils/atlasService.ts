import { apilot } from '@ali/apilot';

const atlasService = apilot.create({
  type: 'atlas',
  atlasConfig: {
    debugMode: false,
  },
});

const msaService = atlasService.create({
  productCode: 'MsaCompose',
});

const request = async (action, params) => {
  const res = await msaService(action, {
    params: {
      ...params,
      product: 'sae',
    },
  });
  return res?.data || res;
};

export const ApplyResourceJson = async (params) => {
  return await request('ApplyResourceJson', params);
};

export const GetResourceJson = async (params) => {
  return await request('GetResourceJson', params);
};

