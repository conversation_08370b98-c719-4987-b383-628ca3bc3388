import { intl } from '@ali/cnd';
import { ErrorCodeConfig } from '@alicloud/xconsole-error-center';
import { get, isEmpty, omitBy } from 'lodash';

const errorOption = (error) => {
  const errorConfigMap: Record<string, Partial<ErrorCodeConfig>> = {
    ServiceUnavailable: {
      title: intl('saenext.src.utils.commonErrorOption.ServiceUnavailable'),
      message: intl('saenext.src.utils.commonErrorOption.SorryTheServiceIsNot'),
      confirmLabel: intl('saenext.src.utils.commonErrorOption.RefreshThePage'),
      confirmHref: window.location.href,
    },
  };

  const { Description, Message } = get(error, 'response.data.data', {})

  const defaultConfig = {
    message: Description || Message,
  }

  const result = {
    errorConfig: errorConfigMap[error?.code] || defaultConfig,
  };

  return omitBy(result, isEmpty);
};

export default errorOption;
