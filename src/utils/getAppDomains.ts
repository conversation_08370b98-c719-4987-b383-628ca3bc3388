import services from "~/services";
import _ from 'lodash';
import { lowerFirstData } from "./transfer-data";

export default async function getAppDomains(applicationID, applicationName) {

  const getAppInternetDomain = async () => {
    const { urlInternet = '' } = await services.getAppVersionConfig({
      applicationID,
      applicationName,
    })
    return urlInternet;
  }

  const getAppCustomDomains = async () => {
    const res = await services.listCustomDomains({
      ApplicationId: applicationID,
      Limit: 100,
      Prefix: '',
      NextToken: '',
    });
    const {
      data: {
        webCustomDomains = []
      } = {}
    } = lowerFirstData(res) || {};

    const domains = [];
    _.forEach(webCustomDomains, (item) => {
      const { domainName,
        protocol,
        defaultForwardingAppName,
        routeConfig: {
          routes = []
        } = {}
      } = item;
      const isHttps = protocol.includes('HTTPS');
      const domain = (isHttps ? 'https://' : 'http://') + domainName;

      if (defaultForwardingAppName) {
        domains.push(domain);
      }

      _.forEach(routes, (route) => {
        const { path, applicationName: routeAppName } = route;
        if (routeAppName === applicationName) {
          domains.push(`${domain}${path}`);
        }
      });
    });
    return domains;
  }

  const [appDomain = '', customDomains = []] = await Promise.all([getAppInternetDomain(), getAppCustomDomains()]);

  const result = [];
  if (customDomains.length) {
    result.push(...customDomains);
  }
  if (appDomain) {
    result.push(appDomain);
  }
  
  return result;
}
