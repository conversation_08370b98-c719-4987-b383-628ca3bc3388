export async function removeUserView() {
  const cuid = getCookieByKeyName('inner_oneconsole_aliyunpk');
  if (cuid) {
    window.sessionStorage.setItem('DISABLE_HACK_USER', cuid);
  }
  window.localStorage.removeItem('HACK_USER');
  window.location.reload();
}

export function getCookieByKeyName(keyName) {
  let result = '';
  let cookieList = (document.cookie && document.cookie.split(';')) || [];
  cookieList.map(str => {
    let [_key = '', _val = ''] = str.split('=') || [];
    if (_key.trim() === keyName) {
      result = _val;
    }
  });
  return result.trim();
}

export function setCookieByKeyName(value, config?) {
  const { 
    maxAge, 
    domain = '.console.aliyun.com', 
    path = '/' 
  } = config || {};
  let cookie = `inner_oneconsole_aliyunpk=${encodeURIComponent(value)}`;
  if (domain) {
    cookie += `; domain=${domain}`;
  }
  if (path) {
    cookie += `; path=${path}`;
  }
  if (maxAge && typeof maxAge === 'number') {
    cookie += `; max-age=${maxAge}`;
  }
  document.cookie = cookie;
}