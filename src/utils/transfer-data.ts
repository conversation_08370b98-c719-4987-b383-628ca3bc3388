import { isPlainObject, lowerFirst, upperFirst, isArrayLikeObject, map } from 'lodash';

// 循环遍历，全部转小写
const lowerFirstData = object => {
  // eslint-disable-next-line guard-for-in
  for (const key in object) {
    const value = object[key];
    if (isPlainObject(value) || isArrayLikeObject(value)) {
      lowerFirstData(value);
    }
    delete object[key];
    object[lowerFirst(key)] = value;
  }
  return object;
};

// 循环遍历，全部转大写
const upperFirstData = object => {
  // eslint-disable-next-line guard-for-in
  for (const key in object) {
    const value = object[key];
    if (isPlainObject(value) || isArrayLikeObject(value)) {
      upperFirstData(value);
    }
    delete object[key];
    object[upperFirst(key)] = value;
  }
  return object;
};

// 只做第一层的大写转换
const upperFirstData1 = object => {
  // eslint-disable-next-line guard-for-in
  if (isArrayLikeObject(object)) {
    for (const key in object) {
      const value = object[key];
      if (isPlainObject(value) || isArrayLikeObject(value)) {
        upperFirstData1(value);
      }
    }
    return object;
  }
  for (const key in object) {
    const value = object[key];
    delete object[key];
    object[upperFirst(key)] = value;
  }
  return object;
};

// 只做第一层的小写转换
const lowerFirstData1 = object => {
  // eslint-disable-next-line guard-for-in
  if (isArrayLikeObject(object)) {
    for (const key in object) {
      const value = object[key];
      if (isPlainObject(value) || isArrayLikeObject(value)) {
        lowerFirstData1(value);
      }
    }
    return object;
  }
  for (const key in object) {
    const value = object[key];
    delete object[key];
    object[lowerFirst(key)] = value;
  }
  return object;
};

const jsonStringify = (value) => {
  try {
    return JSON.stringify(value);
  } catch (err) {
    throw new Error('stringify error');
  }
};

const jsonParse = (value) => {
  if (!value) return '';
  try {
    return JSON.parse(value);
  } catch (err) {
    // throw new Error('parse error');
  }
};

const TransferString = (content) => {
  var string = content;
  try {
    string = string.replace(/[\r\n]/g, '');
  } catch (e) {
    console.log(e.message);
  }
  return string;
};

const translateSelectDataSource = (list = [], valueKeyList, labelKey) => {
  if (list.length === 0) {
    return []
  }
  return map(list, (v) => {
    return {
      ...v,
      value: v[valueKeyList[0]] ?? v[valueKeyList[1]],
      label: v[labelKey],
    };
  });
};

const handleWinLineBreak = (val) => {
  if (!val) return '';

  if (window.navigator.userAgent.indexOf('Windows') > -1) {
    return val.replace(/\r\n/g, '\n');  //处理Windows换行符
  } else {
    return val;
  }
}

const objToDataSource = (object) => {
  const dataSource = [];
  for (const key in object) {
    const value = object[key];
    dataSource.push({
      label: value,
      value: key
    });
  }
  return dataSource;
};

const objValueStringfy = (object = {}) => {
  const result = {};
  for (const key in object) {
    const value = object[key];
    result[key] = String(value);
  }
  return result;
};

// 对象数组转成chart需要的数组
export const convertToChartArr = (objArr = [], keyNames = [], processFuns = []) => {
  return objArr.map(obj => keyNames.map((key, index) => {
    const processFun = processFuns[index];
    if (processFun) {
      return processFun(obj[key]);
    }
    return obj[key];
  }))
}

export const multiplyByThousand = (str: string): number => {
  const num = parseFloat(str);
  return num * 1000;
};


export {
  lowerFirstData,
  upperFirstData,
  upperFirstData1,
  lowerFirstData1,
  jsonStringify,
  jsonParse,
  TransferString,
  translateSelectDataSource,
  handleWinLineBreak,
  objToDataSource,
  objValueStringfy,
};