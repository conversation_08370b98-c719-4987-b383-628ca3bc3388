import moment from 'moment';

const RECENT_TIMES = {
  last_5_minutes: () => [moment().valueOf() - 5 * 60 * 1000, moment().valueOf()],
  last_10_minutes: () => [moment().valueOf() - 10 * 60 * 1000, moment().valueOf()],
  last_15_minutes: () => [moment().valueOf() - 15 * 60 * 1000, moment().valueOf()],
  last_half_hour: () => [moment().valueOf() - 30 * 60 * 1000, moment().valueOf()],
  last_hour: () => [moment().valueOf() - 60 * 60 * 1000, moment().valueOf()],
  last_3_hours: () => [moment().valueOf() - 3 * 60 * 60 * 1000, moment().valueOf()],
  last_6_hours: () => [moment().valueOf() - 6 * 60 * 60 * 1000, moment().valueOf()],
  last_24_hours: () => [moment().valueOf() - 24 * 60 * 60 * 1000, moment().valueOf()],
  half_one_day: () => [moment().valueOf() - 12 * 60 * 60 * 1000, moment().valueOf()],
  half_7_day: () => [
    moment().subtract(7, 'days').valueOf(),
    moment().valueOf(),
    ,
    moment().valueOf(),
  ],
  today: () => [moment().startOf('day').valueOf(), moment().valueOf()],
  last_three_days: () => [moment().valueOf() - 3 * 24 * 60 * 60 * 1000, moment().valueOf()],
  this_week: () => [moment().startOf('week').add(1, 'd').valueOf(), moment().valueOf()],
  last_two_weeks: () => [moment().valueOf() - 2 * 7 * 24 * 60 * 60 * 1000, moment().valueOf()],
  last_three_weeks: () => [moment().valueOf() - 3 * 7 * 24 * 60 * 60 * 1000, moment().valueOf()],
  this_month: () => [moment().startOf('month').valueOf(), moment().valueOf()],
  last_two_months: () => [moment().valueOf() - 2 * 30 * 24 * 60 * 60 * 1000, moment().valueOf()],
};

function isNumber(value) {
  return typeof value === 'number' && isFinite(value);
}


export function getTimes(key) {
  const getTimes = RECENT_TIMES[key];
  if (getTimes) {
    const [start, end] = getTimes();
    return [toSecond(start), toSecond(end)];
  } else {
    return [];
  }
}

export function toSecond(timestamp) {
  if (timestamp === -1) {
    return -1;
  } else if (isNumber(timestamp)) {
    return Math.floor(timestamp / 1000) * 1000;
  } else {
    return timestamp;
  }
}