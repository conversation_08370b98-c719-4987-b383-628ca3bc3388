import { createFetcher } from '@alicloud/console-fetcher-proxy';
import { IFnConsoleApi } from '@alicloud/console-fetcher-basic/build/types/types';
import { defaultDevEnv, IS_PRE_OR_LOCAL } from '~/constants';

const {
  callOpenApi: originalCallOpenApi
} = createFetcher({}, {}, { riskVersion: '3.0' });

 // 第三个参数为开启3.0风控

const addHeaders = (options) => {
  options.headers = options.headers || {};
  const headers = {};
  if (IS_PRE_OR_LOCAL && defaultDevEnv !== 'web') {
    headers['X-EDAS-AT-ROUTER-KEY'] = defaultDevEnv;
  }

  if (Object.keys(headers).length > 0) {
    options.headers['x-oneconsole-diy-header'] = JSON.stringify(headers);
  }
}

const callOpenApi: IFnConsoleApi = (product: string, action: string, params: any, options: any = {}) => {
  addHeaders(options);
  
  return originalCallOpenApi(product, action, params, options);
};

export default callOpenApi;