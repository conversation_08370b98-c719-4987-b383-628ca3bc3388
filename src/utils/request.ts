import { createService as createServiceXconsole, ErrorPrompt2 as errorPrompt } from '@ali/cnd';
import { IOptions } from '@alicloud/xconsole-service/lib/types';
import { AxiosInstance } from 'axios';
import commonErrorHandle from './commonErrorHandle';
import CachedData from '../cache/common';
import { trackRequestErr } from '../tracker';
import errorOption from './commonErrorOption';
import { defaultDevEnv, IS_PRE_OR_LOCAL } from '../constants';

interface IConfig {
  product: string;
  action?: string;
  options?: IOptions;
  instance?: AxiosInstance;
  params?: Record<string, any>;
  ignoreError?: boolean;
  customErrorHandle?: Function;
}

function addHeaders(options) {
  options.headers = options.headers || {};
  const headers = {
    // 'x-disable-container-reuse': 'True',
  };
  if (CachedData.isUserView()) {
    headers['x-sae-account-id'] = CachedData.currentViewUserId();
    headers['x-sae-is-taishan-hacker'] = 'true';
  }

  if (IS_PRE_OR_LOCAL && defaultDevEnv !== 'web') {
    headers['X-EDAS-AT-ROUTER-KEY'] = defaultDevEnv;
  }

  if (Object.keys(headers).length > 0) {
    options.headers['x-oneconsole-diy-header'] = JSON.stringify(headers);
  }

}

const createService = async (config: IConfig) => {
  const {
    product,
    action,
    options = {
      headers: {}
    },
    instance,
    params,
    ignoreError = false,
    customErrorHandle
  } = config;
  if (product === 'mse' || product === 'nas' || product === 'arms') {
    options.headers = {
      'x-oneconsole-no-host': true, //请求线上
    };
  }
  if(product === 'APIG'){
    options.headers = {
      'x-oneconsole-visit-online': true, //请求线上
    };
  }

  addHeaders(options);

  try {
    const result = await createServiceXconsole(
      product,
      action,
      options,
      instance as any
    )(params);
    // TODO: result.successResponse
    return result;

  } catch (error) {

    const errorPromptTracker = (error) => {
      trackRequestErr({
        action,
        code: error?.code,
        message: error?.response?.data?.message,
        pathname: window.location.pathname,
        region: error?.details?.body?.region,
        requestId: error?.requestId,
        product,
      });
      errorPrompt(error, errorOption(error));
    };

    if (customErrorHandle) {
      return customErrorHandle(error, params, () => errorPromptTracker(error));
    }

    const _error = commonErrorHandle(error);
    if (!_error || ignoreError || options.ignoreError) return;
    errorPromptTracker(_error);
  }
};

export default createService;
