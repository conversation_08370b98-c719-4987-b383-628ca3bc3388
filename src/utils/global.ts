import { intl } from '@ali/cnd';
import { find, get, includes, isEmpty } from 'lodash';
import moment from 'moment';
import CachedData from '~/cache/common';
import { repoUrl } from '~/components/shared/DeploySelectorField/constant';

export function getParams(name) {
  const searchParams = new URLSearchParams(window.location.search.substr(1));
  return searchParams.get(name);
}

export function setSearchParams(params) {
  const out = new URL(window.location.href);
  Object.keys(params).forEach((key) => {
    isEmpty(params[key])
      ? out.searchParams.delete(key)
      : out.searchParams.set(
          key,
          typeof params[key] === 'object' ? JSON.stringify(params[key]) : String(params[key]),
        );
  });
  const url = out.toString();
  if (url !== window.location.href && window.history.pushState) {
    window.history.replaceState({ path: url }, '', url);
  }
}

export function removeParams(name) {
  let removeList = [];
  const nameType = Object.prototype.toString.call(name);
  if (nameType === '[object String]') {
    removeList.push(name);
  } else if (nameType === '[object Array]') {
    removeList = name;
  } else if (nameType === '[object Object]') {
    removeList = Object.keys(name);
  } else {
    return;
  }
  const search = new URLSearchParams(window.location.search);
  removeList.forEach((key) => {
    search.delete(key);
  });
  const url = `${window.location.pathname}?${search.toString()}`;
  if (window.history.replaceState) {
    window.history.replaceState({}, '', url);
  } else {
    window.location.hash = url;
  }
}

export function isNumber(val) {
  var regPos = /^\d+(\.\d+)?$/;
  var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数
  if (regPos.test(val) || regNeg.test(val)) {
    return true;
  } else {
    return false;
  }
}

export function useNumber(target, key) {
  if (!target || !key) return 0;
  if (!target[key]) return 0;
  if (!isNumber(target[key])) return 0;
  if (isInteger(target[key])) return Number(target[key]);
  return Number(target[key].toFixed(2));
}

export function replaceAll(string, search, replace) {
  return string.split(search).join(replace);
}

export function isJSON(str) {
  if (typeof str == 'string') {
    try {
      var obj = JSON.parse(str);
      if (typeof obj == 'object' && obj) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
  return false;
}

export function isInteger(value) {
  return !isNaN(parseFloat(value)) && parseInt(value) === parseFloat(value);
}

export function yAxisFormatter(value) {
  const bytes = parseInt(value, 10);
  if (bytes >= 1000000000) {
    const count = (bytes / 1000000000).toFixed(1);
    const order = isInteger(count) ? parseInt(count) : count;
    return `${order}x10⁹`;
  }
  if (bytes >= 100000000) {
    const count = (bytes / 100000000).toFixed(1);
    const order = isInteger(count) ? parseInt(count) : count;
    return `${order}x10⁸`;
  }
  if (bytes >= 10000000) {
    const count = (bytes / 10000000).toFixed(1);
    const order = isInteger(count) ? parseInt(count) : count;
    return `${order}x10⁷`;
  }
  if (bytes >= 1000000) {
    const count = (bytes / 1000000).toFixed(1);
    const order = isInteger(count) ? parseInt(count) : count;
    return `${order}x10⁶`;
  }
  if (bytes >= 100000) {
    const count = (bytes / 100000).toFixed(1);
    const order = isInteger(count) ? parseInt(count) : count;
    return `${order}x10⁵`;
  }
  if (bytes >= 10000) {
    const count = (bytes / 10000).toFixed(1);
    const order = isInteger(count) ? parseInt(count) : count;
    return `${order}x10⁴`;
  }
  // if (bytes >= 1000) {
  //   const count = (bytes / 1000).toFixed(1);
  //   const order = isInteger(count) ? parseInt(count) : count;
  //   return `${order}x10³`;
  // }
  return value;
}

export function unshiftZero(value) {
  if (!value) return '--';
  if (value.length === 3) return value;
  if (value.length === 2) return `0${value}`;
  if (value.length === 1) return `00${value}`;
}

export function getRepoFullUrl(provider, repoFullname) {
  return provider && repoFullname && `${repoUrl[provider]?.console}/${repoFullname}`;
}

export function handleUrlParams(action, key, value = '') {
  const url = new URL(window.location.href);
  const params = new URLSearchParams(url.search);
  if (action === 'set') {
    params.set(key, value);
  } else if (action === 'delete') {
    key.split('&').forEach((k) => {
      params.delete(k);
    });
  }
  return `${location.pathname}?${params.toString()}`;
}

function roundFun(value, n) {
  return Math.round(value * Math.pow(10, n)) / Math.pow(10, n);
}

export function intlNumberFormat(num) {
  return new Intl.NumberFormat(CachedData.lang).format(num || 0);
}

export function formatNumber(value, round = true) {
  if (value === '') {
    return '--';
  }
  if (value === -1) {
    return null;
  }
  let number = Number(value);
  const isChinese = CachedData.lang === 'zh-CN';
  if (isChinese) {
    const w = number / 10000;
    if (w > 100) {
      if (w >= 10000) {
        return `${intlNumberFormat(roundFun(w / 10000, 2))} ${intl('unit.hundredsMillion')}`;
      }
      return `${intlNumberFormat(round ? Math.round(w) : w)}${intl('unit.tenThousands')}`;
    } else if (w > 10) {
      return `${intlNumberFormat(round ? roundFun(w, 1) : w)}${intl('unit.tenThousands')}`;
    } else if (w > 1) {
      return `${intlNumberFormat(round ? roundFun(w, 2) : w)}${intl('unit.tenThousands')}`;
    }
  } else {
    const k = number / 1000;

    if (k > 100) {
      return `${intlNumberFormat(round ? Math.round(k) : k)}k`;
    } else if (k > 10) {
      return `${intlNumberFormat(round ? roundFun(k, 1) : k)}k`;
    } else if (k > 1) {
      return `${intlNumberFormat(round ? roundFun(k, 2) : k)}k`;
    }
  }
  return roundFun(number, number > 10 ? 1 : 3);
}

export const formatDecimalStr = (str, n = 3) => {
  const num = Number(str);
  if (isInteger(num)) return num;
  return isNaN(num) ? str : num.toFixed(n);
};

export function getDurationString(duration: number) {
  if (duration <= 0) {
    return 0;
  }
  if (duration < 1000) {
    return duration + intl('saenext.src.utils.global.Milliseconds');
  }
  const target = moment.duration(duration);
  const d = target.days();
  const h = target.hours();
  const m = target.minutes();
  const s = target.seconds() + (target.milliseconds() > 500 ? 1 : 0);
  const format =
    h > 0
      ? intl('saenext.src.utils.global.HHoursMMinutesS')
      : m > 0
        ? intl('saenext.src.utils.global.MMinutesSSeconds')
        : intl('saenext.src.utils.global.SSeconds');
  return moment({ h, m, s }).format(format);
}

// 判断PHP版本号>=7.4
export function isPhpHighVersion(version) {
  if (!version) return;
  var pattern = /(\d+)\.(\d+)/; // 正则表达式匹配主版本号和次版本号
  var result = pattern.exec(version);
  var majorVersion = parseInt(result[1]);
  var minorVersion = parseInt(result[2]);

  if (majorVersion > 7 || (majorVersion === 7 && minorVersion >= 4)) {
    return true;
  } else {
    return false;
  }
}

export function compareVersions(v1: string, v2: string): number {
  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);

  const maxLength = Math.max(v1Parts.length, v2Parts.length);

  for (let i = 0; i < maxLength; i++) {
    const num1 = v1Parts[i] || 0;
    const num2 = v2Parts[i] || 0;

    if (num1 > num2) return 1;
    if (num1 < num2) return -1;
  }

  return 0;
}

// 判断当前region web和micro是否开服
export function getAppTypeEnabled(regionId) {
  const version1Regions = get(window, 'ALIYUN_CONSOLE_CONFIG.REGIONS', []);
  const { regions = [] } = get(window, 'ALIYUN_CONSOLE_GLOBAL', {}) as any;

  const webEnabled = !!find(regions, { id: regionId });
  const microEnabled = !!find(version1Regions, { regionId });

  return {
    webEnabled,
    microEnabled,
  };
}

export const microAppLink = (appId, regionId) => {
  const { sae1_enable } = get(window, 'ALIYUN_CONSOLE_CONFIG.FEATURE_STATUS', {} as any);
  const isPre = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';

  const origin_saenext = isPre
    ? CachedData.confLink('feature:pre-saenext:url')
    : CachedData.confLink('feature:saenext:url');
  const origin_sae = isPre
    ? CachedData.confLink('feature:pre-sae:url')
    : CachedData.confLink('feature:sae:url');

  const originUrl = `${origin_saenext}/${regionId}/app-list/micro`

  const v1Link = `${origin_sae}/#/AppList/AppDetail?appId=${appId}&regionId=${regionId}&fromNewSae=true&origin=${encodeURIComponent(originUrl)}`;
  const v2Link = `/${regionId}/app-list/${appId}/micro-app/base`;

  return {
    link: sae1_enable ? v2Link : v1Link,
    enable: sae1_enable,
    v1Link,
    v2Link
  }
}

enum ETab {
  web = 'web',
  micro = 'micro',
}
type TTab = keyof typeof ETab;

export function getDefaultAppType(regionId, paramsName) {
  const { webEnabled, microEnabled } = getAppTypeEnabled(regionId);

  if (webEnabled && !microEnabled) {
    return ETab.web;
  }
  if (microEnabled && !webEnabled) {
    return ETab.micro;
  }

  const temp = getParams(paramsName);
  if (temp in ETab) {
    return temp as TTab;
  }
  return ETab.web;
}

export const isTypeImage = (packageType) => {
  return packageType === 'Image' || includes(packageType, 'IMAGE_PHP');
}

export const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// 获取字符串字节数
export function strByteSize(str) {
  if (!str) {
      return 0;
  }
  // 用到了Blob对象,Blob对象代表了一段二进制数据
  return new Blob([str]).size;
}

export const setCookieByKeyName = (name, value, days?) => {
  let expires = '';
  if (days) {
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    expires = `; expires=${date.toUTCString()}`;
  }
  window.document.cookie = `${name}=${value || ''}${expires}; path=/`;
}

export const getCookieByKeyName = (keyName) => {
  let result = '';
  let cookieList = (document.cookie && document.cookie.split(';')) || [];
  cookieList.map(str => {
    let [_key = '', _val = ''] = str.split('=') || [];
    if (_key.trim() === keyName) {
      result = _val;
    }
  });
  return result.trim();
}


export const convertToCu = (val, type: 'cpu' | 'mem') => {
  if (type === 'cpu') {
    return val * 3600;
  } else if (type === 'mem') {
    return val * 900;
  }
}

export const toCu = (value, fixed = 2) => {
  const cu = { value, unit: 'CU' };

  if (value >= 100000000) {
    cu.unit = intl('saenext.micro-app.basic-info.MicroAppStatics.BillionCu');
    const _value = value / 100000000;
    cu.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return cu;
  }

  if (value >= 10000) {
    cu.unit = intl('saenext.micro-app.basic-info.MicroAppStatics.Wancu');
    const _value = value / 10000;
    cu.value = isInteger(_value) ? _value : parseFloat(_value.toFixed(fixed));
    return cu;
  }

  return cu;
};

export const hasSection = (section: string, enumObj: any): boolean => {
  return Object.values(enumObj).includes(section);
}


export const getArmsDomain = (regionId: string) => {
  if (regionId.startsWith('cn-')) {
    return 'https://arms.console.aliyun.com';
  }
  if (regionId.startsWith('ap-northeast-')) {
    return 'https://arms-jp.console.aliyun.com';
  }
  if (regionId.startsWith('ap-southeast-') || regionId.startsWith('ap-south-')) {
    return 'https://arms-ap-southeast-1.console.aliyun.com';
  }
  if (regionId.startsWith('eu-')) {
    return 'https://arms-eu.console.aliyun.com';
  }
  if (regionId.startsWith('us-')) {
    return 'https://arms-us-west-1.console.aliyun.com';
  }
  return 'https://arms.console.aliyun.com';
}
