import { lazy as reactLazy } from 'react';
function retry(fn, retriesLeft = 5, interval = 1000) {
  return new Promise<any>((resolve, reject) => {
    fn()
      .then(resolve)
      .catch(error => {
        setTimeout(() => {
          if (retriesLeft === 1) {
            reject(error);
            return;
          }

          retry(fn, retriesLeft - 1, interval).then(resolve, reject);
        }, interval);
      });
  });
}

export function lazy(callback) {
  return reactLazy(() => retry(callback));
}