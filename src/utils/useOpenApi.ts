import { useOpen<PERSON>pi } from '@ali/cnd';
import CachedData from '~/cache/common';

export default (code: string, action: string, params?, opt?, useFetcher?: boolean) => {
  let _opt = opt;
  const headers = {
    // 'x-disable-container-reuse': 'True',
  };
  if (CachedData.isUserView()) {
    headers['x-sae-account-id'] = CachedData.currentViewUserId();
    headers['x-sae-is-taishan-hacker'] = 'true';
  }

  if (Object.keys(headers).length > 0) {
    _opt = {
      ..._opt,
      headers: {
        'x-oneconsole-diy-header': JSON.stringify(headers),
      }
    }
  } 
  return useOpen<PERSON>pi(code, action, params, _opt, useFetcher);
};
