import { intl } from '@ali/cnd';
import React from 'react';
import { Dialog } from '@ali/cnd';
import CachedData from '~/cache/common';

export function enabledHandle() {
  Dialog.confirm({
    title: intl('saenext.src.utils.accountHandle.YouHaveNotActivatedServerless'),
    content: (
      <>
        <div className="banner">
          <img src="https://img.alicdn.com/imgextra/i4/O1CN01A3o2KI1oZVrH67Te5_!!*************-2-tps-174-195.png" />
          <div className="describe">
            {intl('saenext.src.utils.accountHandle.ServerlessAppEngineIsA')}
          </div>
        </div>
      </>
    ),
    footerActions: ['ok'],
    okProps: { children: intl('saenext.src.utils.accountHandle.DirectActivation') },
    // cancelProps: { children: intl('saenext.src.utils.accountHandle.ActivateAndExperienceSae') },
    onOk: () => {
      window.location.href = CachedData.isSinSite() ? `${CachedData.confLink('feature:common-buy')}/?commodityCode=sae_postpay_public_intl` : `${CachedData.confLink('feature:common-buy')}/?commodityCode=sae#/buy`;
    },
    onClose: () => {
      window.location.href = `https://www.${
        CachedData.isSinSite() ? 'alibabacloud' : 'aliyun'
      }.com`;
    },
    closeMode: ['close'],
  });
}

export function inDebtHandle() {
  Dialog.alert({
    title: intl('saenext.src.utils.accountHandle.OverduePaymentReminder'),
    content: (
      <div className="mt-l" style={{ lineHeight: '24px' }}>
        <div>{intl('saenext.src.utils.accountHandle.CurrentlyYourAccountHasBeen')}</div>
        <div>{intl('saenext.src.utils.accountHandle.WebApplicationsYouCannotCreate')}</div>
        <div>{intl('saenext.src.utils.accountHandle.MicroserviceApplicationsYouCannotCreate')}</div>
        <div>{intl('saenext.src.utils.accountHandle.IfTheOverduePaymentPeriod')}</div>
        <div className="mt-l">
          {intl('saenext.src.utils.accountHandle.ToEnsureYourBusinessSecurity')}
          <a href={`${CachedData.confLink('feature:usercenter2:url')}/finance/fund-management/recharge`} target="_blank">
            {intl('saenext.src.utils.accountHandle.RechargeCenter')}
          </a>
          {intl('saenext.src.utils.accountHandle.RechargeBeforeUse')}
        </div>
      </div>
    ),

    okProps: { children: intl('saenext.src.utils.accountHandle.Ok') },
    cancelProps: { children: intl('saenext.src.utils.accountHandle.Cancel') },
    onOk: () => {
      window.open(`${CachedData.confLink('feature:usercenter2:url')}/finance/fund-management/recharge`, '_blank');
    },
  });
}

export function inDebtOverdueHandle() {
  Dialog.alert({
    title: intl('saenext.src.utils.accountHandle.OverduePaymentReminder.1'),
    content: (
      <div className="mt-l" style={{ lineHeight: '24px' }}>
        <div>{intl('saenext.src.utils.accountHandle.YourAccountIsOverdueAnd')}</div>
        <div>
          {intl('saenext.src.utils.accountHandle.IfYouNeedToContinue')}
          <a href={`${CachedData.confLink('feature:usercenter2:url')}/finance/fund-management/recharge`} target="_blank">
            {intl('saenext.src.utils.accountHandle.RechargeCenter')}
          </a>
          {intl('saenext.src.utils.accountHandle.RechargeBeforeUse')}
        </div>
      </div>
    ),

    footerActions: ['ok'],
    okProps: { children: intl('saenext.src.utils.accountHandle.RechargeUse') },
    onOk: () => {
      window.location.href = `${CachedData.confLink('feature:usercenter2:url')}/finance/fund-management/recharge`;
    },
    closeMode: [],
  });
}
