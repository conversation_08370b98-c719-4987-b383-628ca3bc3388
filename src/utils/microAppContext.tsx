import { createContext } from 'react';

type type = 'preview' | 'deploy' | '';

const MicroAppContext = createContext({
  appConfig: {} as any,
  appStatus: {} as any,
  instanceGroup: [] as any,
  setInstanceGroup: (instanceGroup: any) => {},
  resourceQuota: {} as any,
  scaleRules: [] as any,
  setScaleRules: (scaleRules: any) => {},
  scaleRuleCounts: {} as any,
  setScaleRuleCounts: (scaleRuleCounts: any) => {},
  scaleRuleEnabled: false,
  setScaleRuleEnabled: (scaleRuleEnabled: boolean) => {},
  scaleRef: {} as any,
  slbQpsRt: {} as any,
  setSlbQpsRt: (slbQpsRt: any) => {},
  slbQpsRtPermission: true,
  setSlbQpsRtPermission: (permission: boolean) => {},
  type: '' as type,
  setType: (type: type) => {},
  mseVersion: null as number | null,
  mscUpdateStatus: '',
});
export default MicroAppContext;
