import { Dialog, intl } from '@ali/cnd';
import CachedData from '~/cache/common';

export default function commonErrorHandle(error) {
  const { code, message } = error;

  if (code === 'AccessDenied' && message === 'SAE service is not enabled for current user.') return;

  if (code === 'QuotaExceeded') {
    Dialog.alert({
      title: intl('saenext.src.utils.commonErrorHandle.OperationFailed'),
      content: intl('saenext.src.utils.commonErrorHandle.TheOperationFailedAndThe'),
      footerActions: ['ok'],
    });

    return;
  }

  if (code === 'Missing.OOSServiceRole') {
    Dialog.alert({
      title: intl('saenext.src.utils.commonErrorHandle.OperationFailed'),
      content: intl.html('saenext.src.utils.commonErrorHandle.YouMustSpecifyOosserviceroleA.new', {
        docHref: CachedData.confLink('help:sae:manage-a-scheduled-start-and-stop-rule'),
        ramHref: `${CachedData.confLink('feature:ram:url')}/roles`,
      }),
      footerActions: ['cancel'],
    });
    return;
  }

  if (error.code === 'ApplicationNotFound') {
    // if (document.querySelector('.next-overlay-wrapper')) return;
    // Dialog.alert({
    //   title: intl('saenext.src.utils.commonErrorHandle.TheApplicationOrVersionDoes'),
    //   content: intl('saenext.src.utils.commonErrorHandle.TheApplicationOrVersionDoes.1'),
    //   onOk: () => {
    //     const {
    //       location: { pathname, search },
    //     } = window;
    //     if (pathname.includes('/app-list') && pathname.includes('/version')) {
    //       const appVersionListPath = pathname.replace(
    //         /\/version(\/.*)?$/,
    //         '/web-app/version',
    //       );
    //       window.xconsoleHistory.replace(`${appVersionListPath}${search}`);
    //     }
    //     if (pathname.includes('/app-list') && pathname.includes('/web-app')) {
    //       const [_, regionId] = pathname.split('/');
    //       window.xconsoleHistory.replace(`/${regionId}/app-list/web`);
    //     }
    //   },
    //   okProps: {
    //     children: intl('saenext.src.utils.commonErrorHandle.Return'),
    //   },
    //   footerActions: ['ok'],
    //   closeable: false,
    // });
    return {};
  }

  return error;
}
