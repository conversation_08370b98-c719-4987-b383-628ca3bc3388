import { utils, write } from 'xlsx';
import { saveAs } from 'file-saver';

/**
 * 将 JSON 数据导出为 Excel 文件
 * @param jsonData JSON 格式的数据
 * @param fileName 输出文件名
 */
const exportJsonToExcel = (jsonData: any[], fileName: string): void => {
    // 将 JSON 数据转换为工作表
    const worksheet = utils.json_to_sheet(jsonData);

    // 创建工作簿
    const workbook = { Sheets: { data: worksheet }, SheetNames: ['data'] };

    // 将工作簿写入二进制文件
    const excelBuffer = write(workbook, { bookType: 'xlsx', type: 'array' });

    // 使用 FileSaver 进行文件保存
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
    saveAs(blob, `${fileName}.xlsx`);
}

export default exportJsonToExcel;