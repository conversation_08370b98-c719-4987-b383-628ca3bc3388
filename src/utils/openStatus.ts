import { get } from 'lodash';
import CachedData from '~/cache/common';
import services from '~/services';
import * as ubsmsService from '~/services/ubsms';

export const getProductOpenStatus = async (product) => {
  const {
    Statuses: { Status = [] },
  } = await ubsmsService.DescribeUserBusinessStatus({
    ServiceCode: product,
  });
  const enabled = !!Status.find(s => s.StatusKey === 'enabled' && s.StatusValue !== 'false');

  CachedData.setProductOpenStatus({
    [product]: enabled
  });

  return enabled;
}

export const getProductOpenStatusViper = (product) => {
  const { enabled } = get(window, ['ALIYUN_CONSOLE_CONFIG', 'OPEN_STATUS', product], { enabled: 'false' });
  return enabled === 'true';
}

export const getMseOpenStatus = async () => {
  const { Data } = await services.getMseUserStatus({
    ignoreError: true,
  }) || {};
  
  if (!Data) return;

  // Status    是否开通  1 未开通、2 开通
  // Version   开通版本  0 基础版、 1 专业版、 2 企业版
  // FreeVersion  是否开通试用版  0 未开通、1 开通试用版、 2 试用版到期
  const { Status = 1, Version = 0, FreeVersion = 0 } = Data;
  const mseStatus = {
    Status,
    Version,
    FreeVersion,
  };
  CachedData.mseStatus = mseStatus;
  return mseStatus;
}