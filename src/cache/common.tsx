import { intl } from '@ali/cnd';
import { get } from 'lodash';
import { confLinkGen } from '@alicloud/console-one-conf';

const SAU_CHANNELS = [
  'vco_mpkintl_saudijv',
  'vco_mpkintl_saudijv01',
  'vco_mpkintl_saudijv02',
  'vco_mpkintl_saudijv03',
  'ali_testforJV',
];

export const REGION_MAP = {
  'cn-zhangjiakou-na62-a01': 'cn-zhangjiakou-corp',
};

const NO_IDE_USERS = ['1232990198441898', '****************'];

const INTERNAL_USERS = [...NO_IDE_USERS, '1431999136518149', '1427231014269807'];

const NOT_SUPPORTED_REGIONS = [
  {
    id: 'cn-wulanchabu',
    name: {
      'en-US': 'China (Ulanqab)',
      'zh-CN': intl('saenext.src.cache.common.NorthChinaWulanchabu'),
    },
  },
  {
    id: 'cn-heyuan',
    name: { 'en-US': 'China (Heyuan)', 'zh-CN': intl('saenext.src.cache.common.SouthChinaHeyuan') },
  },
  {
    id: 'cn-guangzhou',
    name: {
      'en-US': 'China (Guangzhou)',
      'zh-CN': intl('saenext.src.cache.common.SouthChinaGuangzhou'),
    },
  },
  {
    id: 'ap-northeast-2',
    name: {
      'en-US': 'South Korea (Seoul)',
      'zh-CN': intl('saenext.src.cache.common.SouthKoreaSeoul'),
    },
  },
  {
    id: 'ap-southeast-6',
    name: {
      'en-US': 'Philippines (Manila)',
      'zh-CN': intl('saenext.src.cache.common.PhilippinesManila'),
    },
  },
  {
    id: 'me-east-1',
    name: { 'en-US': 'UAE (Dubai)', 'zh-CN': intl('saenext.src.cache.common.UaeDubai') },
  },
];

class CachedData {
  static lang = '';
  static regions = [];
  static regionNamesMap = {};
  static riskControlMap = {};
  static openStatus = {};
  static mseStatus = {} as {
    Status: number;
    Version: number;
    FreeVersion: number;
  };
  static qualifierInfo = {};
  static alertMetrics = null;
  static webIdeTipsContent = {
    zh: '',
    en: '',
  };
  static fcTestInvokeUploadData = {
    file: '',
    fileName: '',
    fileSize: '',
  };
  static confLink = function (...args) {
    return '';
  };

  static initialize() {
    const lang = CachedData.getAliyunConsoleConfig('LOCALE') || '';
    CachedData.lang = lang;

    const out = [];
    (window.ALIYUN_CONSOLE_CONFIG.REGIONS || []).forEach((r) => {
      out.push({
        id: REGION_MAP[r.regionId] || r.regionId,
        name: r.name,
      });
      CachedData.regionNamesMap[r.regionId] = r.name;
    });
    CachedData.regions = out;
    const [confLink] = confLinkGen(
      {
        ...CachedData.getConfLink(),
      },
      '{}',
    );
    // 获取渠道链接
    CachedData.confLink = confLink;
  }

  static supportUserView() {
    const supportAccounts = ['****************', '****************', '****************'];
    return supportAccounts.includes(window.ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK);
  }

  static isOdinView() {
    return CachedData.supportUserView() && CachedData.isStsAccount();
  }

  static isDarkTheme() {
    return !!document.querySelector('.theme-xconsole-dark.theme-dark');
  }

  static isUserView() {
    return CachedData.supportUserView() && !!localStorage.getItem('HACK_USER');
  }

  static currentViewUserId() {
    return localStorage.getItem('HACK_USER');
  }

  static isChinese() {
    return window.ALIYUN_CONSOLE_CONFIG.LANG === 'zh';
  }

  static getSlackUrl() {
    // @ts-ignore
    return window.SLACK_URL;
  }

  static isFinanceCloud() {
    return window.ALIYUN_CONSOLE_CONFIG.CHANNEL === 'FINANCE';
  }

  static isSinSite() {
    return ['SIN', ...SAU_CHANNELS].includes(window.ALIYUN_CONSOLE_CONFIG.CHANNEL);
  }

  static isSauSite() {
    return SAU_CHANNELS.includes(window.ALIYUN_CONSOLE_CONFIG.CHANNEL);
  }

  static isOfficialSite() {
    return window.ALIYUN_CONSOLE_CONFIG.CHANNEL === 'OFFICIAL';
  }

  static isPublicSite() {
    return ['OFFICIAL', 'SIN'].includes(window.ALIYUN_CONSOLE_CONFIG.CHANNEL);
  }

  static setOpenStatus(openStatus) {
    CachedData.openStatus = openStatus;
  }

  static setProductOpenStatus(productOpenStatus: { [key: string]: boolean }) {
    CachedData.openStatus = {
      ...CachedData.openStatus,
      ...productOpenStatus,
    };
  }

  static setWebIdeTipsContent(content) {
    CachedData.webIdeTipsContent = content;
  }

  static getOpenStatus(key) {
    let productCode;
    if (key === 'serviceStatus') {
      productCode = 'sae';
    } else if (key === 'logStatus') {
      productCode = 'sls';
    } else if (key.endsWith('Status')) {
      productCode = key.substring(0, key.length - 6);
    } else {
      productCode = key;
    }
    return CachedData.openStatus[productCode];
  }

  static getQualifierDescription(key, serviceName, regionId) {
    if (
      get(CachedData.qualifierInfo, 'serviceName') === serviceName &&
      get(CachedData.qualifierInfo, 'regionId') === regionId
    ) {
      return get(CachedData.qualifierInfo, `descriptions.${key}`);
    } else {
      return '';
    }
  }

  static setQualifierInfo(qualifierInfo) {
    CachedData.qualifierInfo = qualifierInfo;
  }

  static getAlertMetrics() {
    return CachedData.alertMetrics;
  }

  static setAlertMetrics(alertMetrics) {
    CachedData.alertMetrics = alertMetrics;
  }

  static getMainUserId() {
    return CachedData.supportUserView() && CachedData.isUserView()
      ? CachedData.currentViewUserId()
      : window.ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK;
  }

  static getCurrentUserId() {
    return (
      CachedData.getAliyunConsoleConfig('CURRENT_PK') ||
      CachedData.getAliyunConsoleConfig('MAIN_ACCOUNT_PK')
    );
  }

  static getAliyunConsoleConfig(key) {
    return get(window, `ALIYUN_CONSOLE_CONFIG.${key}`);
  }

  static isSubAccount() {
    return get(window, 'ALIYUN_CONSOLE_CONFIG.ACCOUNT_TYPE') === 'sub';
  }

  static isStsAccount() {
    return get(window, 'ALIYUN_CONSOLE_CONFIG.ACCOUNT_TYPE') === 'sts';
  }

  static isMainAccount() {
    return get(window, 'ALIYUN_CONSOLE_CONFIG.ACCOUNT_TYPE') === 'main';
  }

  static isVirtualService() {
    return window.location.host.indexOf('4service') > -1;
  }

  static isSupportedPublicLayer(layer) {
    if ('puppeteer' === layer) {
      return 'Puppeteer';
    } else if ('torch' === layer) {
      return 'PyTorch';
    }
  }

  static isHighGcUser() {
    const { NOT_GC_5, NOT_GC_6, NOT_GC_7 } = window.ALIYUN_CONSOLE_CONFIG?.FEATURE_STATUS || {};

    return !NOT_GC_5 || !NOT_GC_6 || !NOT_GC_7;
  }

  static isPre() {
    return window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';
  }

  static isDev() {
    return (
      window.ALIYUN_CONSOLE_CONFIG.fEnv !== 'pre' && window.ALIYUN_CONSOLE_CONFIG.fEnv !== undefined
    );
  }

  static isProduction() {
    return window.ALIYUN_CONSOLE_CONFIG.fEnv === undefined;
  }

  static getRegions() {
    return [...CachedData.regions];
  }

  static getRegionName(regionId) {
    return CachedData.regionNamesMap[regionId] || regionId;
  }

  static isInternalUser() {
    return (
      INTERNAL_USERS.includes(window.ALIYUN_CONSOLE_CONFIG.CURRENT_PK) ||
      INTERNAL_USERS.includes(window.ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK)
    );
  }

  static isNoOnlineIdeUser() {
    return (
      NO_IDE_USERS.includes(window.ALIYUN_CONSOLE_CONFIG.CURRENT_PK) ||
      NO_IDE_USERS.includes(window.ALIYUN_CONSOLE_CONFIG.MAIN_ACCOUNT_PK)
    );
  }

  static getRegionSortNumber(regionId) {
    return [
      'cn-hangzhou',
      'cn-shanghai',
      'cn-qingdao',
      'cn-beijing',
      'cn-zhangjiakou',
      'cn-huhehaote',
      'cn-shenzhen',
      'cn-chengdu',
      'cn-hongkong',
      'ap-southeast-1',
      'ap-southeast-2',
      'ap-southeast-3',
      'ap-southeast-5',
      'ap-northeast-1',
      'eu-central-1',
      'eu-west-1',
      'us-west-1',
      'us-east-1',
      'ap-south-1',
    ].indexOf(regionId);
  }

  static setFcTestInvokeUploadData(files) {
    CachedData.fcTestInvokeUploadData = files;
  }
  static getConfLink() {
    return CachedData.getAliyunConsoleConfig('CHANNEL_LINKS');
  }

}

CachedData.initialize();

export default CachedData;
