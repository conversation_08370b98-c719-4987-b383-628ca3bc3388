import './init'
import { intl } from '@ali/cnd';
import React, { useState, useEffect, Suspense, lazy } from 'react';
import ErrorBoundary from '~/components/shared/ErrorBoundary';
import services from '~/services';
import { getParams } from '~/utils/global';
import FeatureContext from './utils/featureContext';
import { ConfigProvider } from '@ali/cnd';
import { get, isEmpty } from 'lodash';
import { enabledHandle, inDebtOverdueHandle } from '~/utils/accountHandle';
import CachedData from '~/cache/common';
import { getCookieByKeyName, setCookieByKeyName } from '~/utils/hacker';
import en_US from '@alifd/next/lib/locale/en-us';
import zh_CN from '@alifd/next/lib/locale/zh-cn';
import { ConfigProvider as ConfigProviderDeep } from '@ali/deep';
import Locale from '@ali/deep/lib/locale';
import { IS_PRE, IS_PRE_OR_LOCAL } from "./constants";
import ModalProvider from './components/shared/Modal/Provider';
import { MultiVersionSelector } from './components/shared/MultiVersionSelector';
import { DevEnvSelector } from './components/shared/DevEnvSelector';
import Modal from '~/components/shared/Modal';
import MessengerRegionBar from './components/shared/MessengerRegionBar';

const FetchUbsms = lazy(() => import('~/components/shared/FetchUbsms'));

declare global {
  interface Window {
    xconsoleHistory: any;
    curRegionId: string;
    ALIYUN_CONSOLE_GLOBAL: any;
    resourceBaseUrl: string;
  }
}

function addUserViewMark() {
  // 用户id TODO
  const uid = getCookieByKeyName('inner_oneconsole_aliyunpk');

  if (CachedData.supportUserView() && uid && uid !== sessionStorage.getItem('DISABLE_HACK_USER')) {
    localStorage.setItem('HACK_USER', uid);
  }

  if (!CachedData.supportUserView()) {
    localStorage.removeItem('HACK_USER');
  }

  const odinMockUid = getParams('mockUid');

  if (CachedData.supportUserView() && CachedData.isUserView() || odinMockUid) {
    const user = CachedData.currentViewUserId() || odinMockUid;
    if (user) {
      return (
        <div className='hacker-watermark'>
          {intl('saenext.src.app.UserUserPerspective', { user: user })}
        </div>
      )
    }
  }
}

const Metadata = ({ children }) => {
  const uid = getParams('hackerUid');
  if (uid) {
    setCookieByKeyName(uid);
    const url = window.location.href.split('?')[0];
    if (window.history.replaceState) {
      window.history.replaceState(null, '', url);
    }
  }

  const hackerViewMark = addUserViewMark();

  return (
    <>
      {hackerViewMark}
      {children}
    </>
  );
};

export default (app, App) => {
  return ({ history }) => {
    window.xconsoleHistory = history;
    // @ts-ignore
    window.__ALIYUN_WIDGET_USE_DIFFERENT_WIND_RUNTIME__ = true
    const { location = {} } = history;
    const { pathname = '' } = location;
    const [feature, setFeature] = useState({});
    const [openStatus, setOpenStatus] = useState({});
    const [webFeature, setWebFeature] = useState({});
    const [saeEanble, setSaeEnable] = useState(false);
    const [saeInDebt, setSaeInDebt] = useState(false);
    const [saeInDebtOverdue, setSaeInDebtOverdue] = useState(false);
    const [inDebtData, setInDebtData] = useState({});
    const value = { feature, setFeature, openStatus, setOpenStatus, webFeature, inDebtData };

    const saeCode = CachedData.isSinSite() ? 'alisae' : 'serverless';

    useEffect(() => {
      // 无状态路由需要跳过 DescribeUserBusinessStatus
      if (pathname === '/price-calculator') return;
      checkEnabledFeature();
    }, []);

    const checkEnabledFeature = async () => {
      // 优先检查开通
      const openStatus = get(window, 'ALIYUN_CONSOLE_CONFIG.OPEN_STATUS') || {};

      const productUbsms = get(openStatus, saeCode) || {};
      // 开通状态
      let _saeEanble = get(productUbsms, 'enabled') === 'true';
      // 欠费状态 false 未欠费 此处获取有延迟
      let _saeInDebt = get(productUbsms, 'inDebt') === 'true';
      // 欠费超期状态 false 未超期 此处获取有延迟
      let _saeInDebtOverdue = get(productUbsms, 'inDebtOverdue') === 'true';

      if (isEmpty(productUbsms)) {
        const res = await getEnabledStatus();
        const { enabled, inDebt, inDebtOverdue } = res;
        _saeEanble = enabled;
        _saeInDebt = inDebt;
        _saeInDebtOverdue = inDebtOverdue;
      }

      // 如果未欠费 重新查一下欠费接口
      if (_saeEanble && (!_saeInDebt || !_saeInDebtOverdue)) {
        const res = await getInDebtStatus();
        const { inDebt, inDebtOverdue } = res;
        _saeInDebt = inDebt;
        _saeInDebtOverdue = inDebtOverdue;
      }

      setSaeEnable(_saeEanble);
      setSaeInDebt(_saeInDebt);
      setSaeInDebtOverdue(_saeInDebtOverdue);

      // 如果是未开通
      if (!_saeEanble) {
        enabledHandle();
        return;
      }

      // 如果是欠费超期
      if (_saeInDebtOverdue) {
        inDebtOverdueHandle();
        return;
      }

      const _feature = await getProductFeature();
      setFeature(_feature);

      getWebFeature();
    };

    const getWebFeature = async () => {
      const webFeature = await checkWebFeature();
      setWebFeature(webFeature);
    }

    const getEnabledStatus = async () => {
      const result = await services.DescribeUserBusinessStatus({
        ServiceCode: saeCode,
      });
      const {
        Statuses: { Status = [] },
      } = result;
      const enabled = Status.find((s) => s.StatusKey === 'enabled' && s.StatusValue !== 'false');
      const inDebt = Status.find((s) => s.StatusKey === 'inDebt' && s.StatusValue !== 'false');
      const inDebtOverdue = Status.find(
        (s) => s.StatusKey === 'inDebtOverdue' && s.StatusValue !== 'false',
      );
      return {
        enabled: !!enabled,
        inDebt: !!inDebt,
        inDebtOverdue: !!inDebtOverdue,
      };
    };

    const getInDebtStatus = async () => {
      const res = await services.checkInDebt();
      const { Data = {} } = res || {};
      const { CheckInDebt } = Data;
      setInDebtData(Data);
      return {
        inDebt: CheckInDebt === 'InDebt',
        inDebtOverdue: CheckInDebt === 'InDebtOverdue',
      };
    };
    const getProductFeature = async () => {
      const featureConfig = await services.CheckFeatureConfig();
      let _feature = {};
      if (featureConfig && featureConfig.Data) {
        _feature = featureConfig.Data;
      }
      return _feature;
    };


    const checkWebFeature = async () => {
      const res = await services.getWebFeatureConfig();

      if (res && res.Data) {
        return res.Data;
      } else {
        return {};
      }
    }

    return (
        <ErrorBoundary>
          <MessengerRegionBar setFeature={setFeature} setWebFeature={setWebFeature} setInDebtData={setInDebtData} />
          <FeatureContext.Provider value={value}>
            <Metadata>
              <Suspense fallback={null}>
                <FetchUbsms
                  saeEanble={saeEanble}
                  saeInDebt={saeInDebt}
                  saeInDebtOverdue={saeInDebtOverdue}
                  setOpenStatus={setOpenStatus}
                />
              </Suspense>
              <ConfigProvider locale={CachedData.lang === 'zh-CN' ? zh_CN : en_US}>
                <ConfigProviderDeep locale={CachedData.lang === 'zh-CN' ? Locale?.zhCN : Locale?.enUS}>
                  <ModalProvider>
                    <App />
                    {IS_PRE && <MultiVersionSelector />}
                    {IS_PRE_OR_LOCAL && <DevEnvSelector />}
                    <Modal />
                  </ModalProvider>
                </ConfigProviderDeep>
              </ConfigProvider>
            </Metadata>
          </FeatureContext.Provider>
        </ErrorBoundary>
    );
  };
};
