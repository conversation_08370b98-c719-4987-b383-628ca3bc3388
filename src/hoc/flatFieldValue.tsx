import { forEach, get, isEmpty, pick } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';

const flatFieldValue = (config) => (WrappedComponent) => {
  const FlatedComponent = (props) => {
    const {
      names,
      nameOptions,
    } = config;

    const {
      field,
    } = props;

    const { init, getValues, getErrors } = field;

    const fieldValue = getValues(names);

    const pickedValue = pick(fieldValue, names);

    const [value, setValue] = useState(pickedValue);

    useEffect(() => {
      setValue(pickedValue);
    }, [JSON.stringify(pickedValue)])

    const onChange = (newValue) => {
      forEach(names, (name) => {
        field.setValue(name, newValue[name]);
      })
      field.validate(names);
    };

    const errorsObj = getErrors(names);
    
    const errorArr = useMemo(() => {
      const result = [];
      for(const key in errorsObj) {
        const errArr = errorsObj[key];
        if (!isEmpty(errArr)) {
          result.push(...errArr);
        }
      }
      return result;
    }, [JSON.stringify(errorsObj)])

    const nameOptionMap = typeof nameOptions === 'function' ? nameOptions(value) : nameOptions;

    return (
      <>
        <WrappedComponent {...props} value={value} onChange={onChange} />
        <div className="text-warning">{errorArr.join(",")}</div>
        <div className="none">
          {
            names.map((name) => (
              <div
                key={name}
                {...init(name, {
                  initValue: pickedValue[name],
                  ...get(nameOptionMap, name, {}),
                })}
              />
            ))
          }
        </div>
      </>
    )

  }

  return FlatedComponent;
};

export default flatFieldValue;