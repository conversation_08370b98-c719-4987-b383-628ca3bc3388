import React, { useContext, useEffect, useState } from 'react';
import { ConsoleContext, intl, LinkButton, Dialog } from '@ali/cnd';
import { get } from 'lodash';
import services from './services';
import CachedData from './cache/common';
import FeatureContext from '~/utils/featureContext';
import moment from 'moment';
import { confFeature } from '@alicloud/console-one-conf';

const defaultSide = {
  title: intl('saenext.src.sidebar.ServerlessApplicationEngineSae'),
  navs: [
    {
      title: intl('saenext.src.sidebar.OverviewPage'),
      key: '/overview',
      visible: true,
    },
    {
      title: intl('saenext.src.sidebar.Namespace'),
      key: '/:regionId/namespace',
    },
    {
      title: intl('saenext.src.sidebar.ApplicationManagement'),
      key: '/:regionId/app-list',
      subNav: [
        {
          title: intl('saenext.src.sidebar.ApplicationList'),
          key: '/:regionId/app-list/micro',
        },
        {
          title: intl('saenext.src.sidebar.InstanceList'),
          key: '/:regionId/app-list/instance-list',
        },
      ],
    },
    {
      title: intl('saenext.src.sidebar.WebApplications'),
      key: '/:regionId/app-list/web',
      visible: false,
    },
    {
      title: intl('saenext.src.sidebar.TaskTemplateList'),
      key: '/:regionId/job-list',
      visible: !CachedData.isSinSite(),
    },
    {
      title: intl('saenext.src.sidebar.ConfigurationManagement'),
      key: '/:regionId/config-management',
      subNav: [
        {
          title: intl('saenext.src.sidebar.DistributedConfigurationAcm'),
          key: '/:regionId/config-management/acm',
        },
        {
          title: intl('saenext.src.sidebar.ConfigurationItemConfigmap'),
          key: '/:regionId/config-management/config-map',
        },
        {
          title: intl('saenext.src.sidebar.Secret'),
          key: '/:regionId/config-management/secret',
        },
      ],
    },
    {
      title: intl('saenext.src.sidebar.TrafficManagement'),
      key: '/:regionId/traffic-management',
      subNav: [
        {
          title: intl('saenext.src.sidebar.GatewayRouting'),
          key: '/:regionId/traffic-management/gateway-route/list',
        },
        {
          title: intl('saenext.src.sidebar.MicroserviceGovernance'),
          key: '/:regionId/traffic-management/msc',
        },
      ],
    },
    {
      title: intl('saenext.src.sidebar.OMManagement'),
      key: '/operations-management',
      subNav: [
        {
          title: intl('saenext.src.sidebar.BasicMonitoring'),
          key: '/operations-management/monitor',
        },
        // {
        //   title:'告警管理',
        //   key:'/operations-management/alarm',
        // },
        {
          title: intl('saenext.src.sidebar.EventCenter'),
          key: '/operations-management/event-center',
        },
        {
          title: intl('saenext.src.sidebar.PermissionAssistant'),
          key: '/operations-management/permission-assistant',
        },
        {
          title: intl('saenext.src.sidebar.ApprovalManagement'),
          key: '/operations-management/approval',
        },
      ],
    },
    {
      title: intl('saenext.src.sidebar.PriceCalculator'),
      key: '/:regionId/price-calculator',
      visible: !CachedData.isSinSite(),
    },
    {
      title: intl('saenext.src.sidebar.ApplicationCenter'),
      key: '/:regionId/scene-market',
      visible: !CachedData.isSinSite(),
    },
  ],
};

export default function useSidebar() {
  const [sidebar, setSidebar] = useState(defaultSide);
  const { feature, webFeature } = useContext(FeatureContext);

  const { region } = useContext(ConsoleContext);
  const regionId = region.getCurrentRegionId();
  const saeOldConsoleEnable = confFeature('sae_1.0_console_enable');
  // 支持多版本的老用户
  const { enableNewSaeVersion } = feature;
  // 是否多版本新用户 --- 轻量版+专业版
  const AccountOpenTime = webFeature?.AccountOpenTime;
  const multipleVersionsTime = get(window, 'ALIYUN_CONSOLE_GLOBAL.multipleVersionsTime');
  const isMultipleNewAccount =
    AccountOpenTime && multipleVersionsTime
      ? AccountOpenTime > moment(multipleVersionsTime).valueOf()
      : false;
  const isSupportMultiVersions = enableNewSaeVersion || isMultipleNewAccount;
  const checkWebFeature = async () => {
    const res = await services.getWebFeatureConfig();

    const enableWebApplication = get(res, 'Data.EnableWebApplication');

    if (enableWebApplication) {
      // @ts-ignore
      sidebar.navs[2].title = intl('saenext.src.sidebar.MicroserviceApplications');
      sidebar.navs[3].visible = true;
    } else {
      // @ts-ignore
      sidebar.navs[2].title = intl('saenext.src.sidebar.ApplicationManagement');
      sidebar.navs[3].visible = false;
    }
    if (
      CachedData.isSinSite() ||
      isSupportMultiVersions ||
      (!saeOldConsoleEnable && moment().isAfter('2025-05-31 23:59:59'))
    ) {
      sidebar.title = intl('saenext.src.sidebar.ServerlessApplicationEngineSae');
    } else {
      // @ts-ignore
      sidebar.title = (
        <div>
          <div>{intl('saenext.src.sidebar.ServerlessApplicationEngineSae')}</div>
          <div style={{ fontSize: 12 }}>
            <LinkButton
              onClick={() => {
                const dialog = Dialog.confirm({
                  title: intl('saenext.returnTo.oldConsole'),
                  content: (
                    <>
                      {intl.html('saenext.oldConsole.OfflineAnnouncement', {
                        href: CachedData.confLink('help:sae:old-console-offline-announcement'),
                      })}
                    </>
                  ),

                  style: {
                    width: 480,
                  },
                  onOk: () => {
                    window.open(
                      `https://${
                        window.location.host?.startsWith('pre-') ? 'pre-sae' : 'sae'
                      }.console.aliyun.com/#/Overview?fromNewSae=true`,
                    );
                  },
                  onCancel: () => {
                    dialog.hide();
                  },
                });
              }}
            >
              {intl('saenext.returnTo.oldConsole')}
            </LinkButton>
          </div>
        </div>
      );
    }
    setSidebar({ ...sidebar });
  };

  useEffect(() => {
    checkWebFeature();
  }, [regionId, isSupportMultiVersions]);

  return sidebar;
}
