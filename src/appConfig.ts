import { AppConfig } from '@ali/cnd';
import commonErrorHandle from './utils/commonErrorHandle';
import { get, isEmpty, map } from 'lodash';

const config: AppConfig = {};

const regions = get(window, 'ALIYUN_CONSOLE_CONFIG.STATIC_API.regions.data.Regions.Region') as any;
if (!isEmpty(regions)) {
  window.ALIYUN_CONSOLE_CONFIG.REGIONS = map(regions, (region) => {
    return {
      name: region.LocalName,
      regionId: region.RegionId,
    };
  });
}

const regionList = map(window.ALIYUN_CONSOLE_CONFIG.REGIONS, (region) => {
  return {
    ...region,
    id: region.regionId,
  };
});

// ConsoleBase 相关配置
config.region = {
  regionList,
  regionbarVisiblePaths: [
    '/:regionId/app-list/web',
    '/:regionId/app-list/micro',
    '/:regionId/app-list/instance-list',
    '/:regionId/create-app/micro/yaml',
    '/:regionId/job-list',
    '/:regionId/namespace',
    '/:regionId/app-list/:id/micro-app/*',
    '/:regionId/app-list/:id/web-app/*',
    '/:regionId/namespace/:id/*',
    '/:regionId/create-job',
    '/:regionId/scene-market*',
    '/:regionId/config-management/acm',
    '/:regionId/config-management/config-map',
    '/:regionId/config-management/secret',
    '/:regionId/traffic-management/gateway-route/list',
    '/:regionId/traffic-management/msc',
    '/operations-management/approval',
    '/operations-management/monitor'
  ],
};

config.errorCenter = {
  enable: true, // 配置为 false 的话不会出现报错弹窗
  errorConfig: (error) => {
    const err = commonErrorHandle(error);
    if (!err) return;

    return {
      // @ts-ignore
      message: `action: ${error?.details?.body?.action}` + '<br/>' + (error.message || error.code || ''),
    };
  },
};

config.consoleMenu = {
  notDisplayPath: ['/price-calculator'],
  collapsedPath: [
    '/:regionId/app-list/*/*',
    '/:regionId/create-app/*',
    '/:regionId/monitor/*',
    '/:regionId/namespace/*',
    '/:regionId/traffic-management/msc/grayscale-overview',
    '/:regionId/job-list/*',
    '/:regionId/config-management/acm/acm-detail',
    '/:regionId/traffic-management/gateway-route/create',
    '/:regionId/traffic-management/gateway-route/edit',
  ],
};

export default config;
