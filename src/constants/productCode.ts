import { intl } from '@ali/cnd';
import CachedData from '~/cache/common';
export const PRODUCT_CODE = {
  arms: {
    name: intl('saenext.src.constants.productCode.ApplicationRealTimeMonitoringService'),
  },
  nas: {
    name: intl('saenext.src.constants.productCode.FileSystemNas'),
    commodityCode: 'naspost',
  },
  oss: {
    name: intl('saenext.src.constants.productCode.ObjectStorageOss'),
  },
  sls: {
    name: intl('saenext.src.constants.productCode.LogServiceSls'),
  },
  ahas: {
    name: intl('saenext.src.constants.productCode.ApplicationHighAvailabilityServiceAhas'),
    feature: intl('saenext.src.constants.productCode.CurrentLimitingDegradation'),
    commodityCode: 'ahas_post',
  },
  alikafka: {
    name: intl('saenext.src.constants.productCode.MessageQueueKafka'),
    commodityCode: 'alikafka_post',
  },
  mse2: {
    name: intl('saenext.src.constants.productCode.MseMicroserviceGovernanceEnterpriseEdition'),
    feature: intl('saenext.src.constants.productCode.CurrentLimitingDegradation'),
    commodityCode: 'mse_basic_dp_cn',
  },
};

export const getBuyLink = (product) => {
  const commodityCode = PRODUCT_CODE[product]?.commodityCode || product;
  return `${CachedData.confLink('feature:common-buy')}/?commodityCode=${commodityCode}`;
};
