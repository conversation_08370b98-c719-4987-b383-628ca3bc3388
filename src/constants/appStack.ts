import { intl } from '@ali/cnd';
import CachedData from '~/cache/common';

const isPre = window.ALIYUN_CONSOLE_CONFIG.fEnv === 'pre';
const origin = isPre
? CachedData.confLink('feature:pre-saenext:url')
: CachedData.confLink('feature:saenext:url');

export const ProductType = {
  SAE: intl('saenext.src.constants.appStack.ServerlessApplicationEngine'),
  SLB: intl('saenext.src.constants.appStack.LoadBalancing'),
  NAS: intl('saenext.src.constants.appStack.FileStorage'),
  ECS: intl('saenext.src.constants.appStack.CloudServer'),
  VPC: intl('saenext.src.constants.appStack.Vpc'),
};

export const ResourceType = {
  CLB: {
    name: intl('saenext.src.constants.appStack.LoadBalancing'),
    link: (id, regionId) => `${CachedData.confLink('feature:slb:url')}/slb/${regionId}/slbs/${id}`,
  },
  Application: {
    name: intl('saenext.src.constants.appStack.Application'),
    link: (id, regionId) =>
      `${origin}/${regionId}/app-list/${id}/micro-app/base`
  },
  Namespace: {
    name: intl('saenext.src.constants.appStack.Namespace'),
    link: (id, regionId) =>
      `${origin}/${regionId}/namespace/${id}/base`
  },
  MountTarget: {
    name: intl('saenext.src.constants.appStack.MountPoint'),
    link: (id, regionId) =>
      `${CachedData.confLink('feature:nasnext:url')}/${regionId}/filesystem/${id?.match(/^(.*?)-/)[1]}/mount`,
  },
  FileSystem: {
    name: intl('saenext.src.constants.appStack.FileSystem'),
    link: (id, regionId) => `${CachedData.confLink('feature:nasnext:url')}/${regionId}/filesystem/${id}`,
  },
  SecurityGroup: {
    name: intl('saenext.src.constants.appStack.SecurityGroup'),
    link: (id, regionId) =>
      `${CachedData.confLink('feature:ecs:url')}/securityGroupDetail/region/${regionId}/groupId/${id}/detail`,
  },
  VPC: {
    name: intl('saenext.src.constants.appStack.Vpc'),
    link: (id, regionId) => `${CachedData.confLink('feature:vpcnext:url')}/vpc/${regionId}/vpcs/${id}`,
  },
  VSwitch: {
    name: intl('saenext.src.constants.appStack.Switch'),
    link: (id, regionId) => `${CachedData.confLink('feature:vpcnext:url')}/vpc/${regionId}/switches/${id}`,
  },
};

export enum RESOURCE_STATUS {
  'NORMAL' = 'NORMAL',
  'ERROR' = 'ERROR',
}

export const ResourceStatus = {
  [RESOURCE_STATUS.NORMAL]: {
    label: intl('saenext.src.constants.appStack.Normal'),
    type: 'success',
  },
  [RESOURCE_STATUS.ERROR]: {
    label: intl('saenext.src.constants.appStack.Exception'),
    type: 'error',
  },
};
