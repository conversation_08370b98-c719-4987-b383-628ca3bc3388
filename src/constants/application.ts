import { intl } from '@ali/cnd';

export enum LANGUAGE_TYPE {
  JAVA = 'java',
  PHP = 'php',
  PYTHON = 'python',
  GO = 'golang',
  DOTNET = 'dotnet',
  OTHER = 'other',
}

export const LANGUAGE_NAME = {
  [LANGUAGE_TYPE.JAVA]: 'Java',
  [LANGUAGE_TYPE.PHP]: 'PHP',
  [LANGUAGE_TYPE.PYTHON]: 'Python',
  [LANGUAGE_TYPE.GO]: 'Golang',
  [LANGUAGE_TYPE.DOTNET]: '.NET',
}

export const AUTH_TYPE_MAP = {
  anonymous: intl('saenext.src.constants.application.NoAuthenticationRequired'),
  function: intl('saenext.src.constants.application.SignatureAuthentication'),
};
export enum REPO_TYPE {
  GITHUB = 'github',
  GITEE = 'gitee',
  GITLAB = 'gitlab',
}

export enum DEPLOY_TYPE {
  IMAGE = 'image',
  REPOISTORY = 'repoistory',
  MICRO_PACKAGE = 'micro-package',
  WEB_PACKAGE = 'web-package',
}

export enum PACKAGE_TYPE {
  WAR = 'War',
  JAR = 'FatJar',
  PHPZIP = 'PhpZip',
  PYTHONZIP = 'PythonZip',
  DOTNETZIP = 'DotnetZip',
}

export enum PIPELINE_STATUS {
  FAIL = 'FAIL',
  RUNNING = 'RUNNING',
  SUCCESS = 'SUCCESS',
  WAIT = 'WAIT',
  STOP = 'STOP',
  CANCEL = 'CANCEL',
}

export const StepStatus = {
  [PIPELINE_STATUS.RUNNING]: {
    icon: 'loading',
    text: intl('saenext.src.constants.application.Running'),
  },
  [PIPELINE_STATUS.SUCCESS]: {
    icon: 'success',
    text: intl('saenext.src.constants.application.Success'),
  },
  [PIPELINE_STATUS.FAIL]: {
    icon: 'error',
    text: intl('saenext.src.constants.application.Failed'),
  },
  [PIPELINE_STATUS.WAIT]: {
    icon: 'disabled',
    text: intl('saenext.src.constants.application.Waiting'),
  },
  [PIPELINE_STATUS.STOP]: {
    icon: 'disabled',
    text: intl('saenext.src.constants.application.Stopped'),
  },
  [PIPELINE_STATUS.CANCEL]: {
    icon: 'disabled',
    text: intl('saenext.src.constants.application.Canceled'),
  },
};

export const InitPackageSteps = {
  VersionId: 0,
  Status: 'RUNNING',
  Steps: [
    {
      Id: 'CreateEnv',
      Name: intl('saenext.src.constants.application.EnvironmentCreation'),
      Result: intl('saenext.src.constants.application.Running.2'),
      Status: 'RUNNING',
    },
    {
      Id: 'ImageBuild',
      Name: intl('saenext.src.constants.application.ImageBuilding'),
      Result: intl('saenext.src.constants.application.Queuing'),
      Status: 'WAIT',
    },
    {
      Id: 'CreateVersion',
      Name: intl('saenext.src.constants.application.VersionCreation'),
      Result: intl('saenext.src.constants.application.Queuing'),
      Status: 'WAIT',
    },
  ],
};

export const InitRepoistorySteps = {
  VersionId: 0,
  Status: 'RUNNING',
  Steps: [
    {
      Id: 'CreateEnv',
      Name: intl('saenext.src.constants.application.EnvironmentCreation'),
      Result: intl('saenext.src.constants.application.Running.2'),
      Status: 'RUNNING',
    },
    {
      Id: 'GitClone',
      Name: intl('saenext.src.constants.application.CodePull'),
      Result: intl('saenext.src.constants.application.Queuing'),
      Status: 'WAIT',
    },
    {
      Id: 'SourceBuild',
      Name: intl('saenext.src.constants.application.SourceCodeConstruction'),
      Result: intl('saenext.src.constants.application.Queuing'),
      Status: 'WAIT',
    },
    {
      Id: 'ImageBuild',
      Name: intl('saenext.src.constants.application.ImageBuilding'),
      Result: intl('saenext.src.constants.application.Queuing'),
      Status: 'WAIT',
    },
    {
      Id: 'CreateVersion',
      Name: intl('saenext.src.constants.application.VersionCreation'),
      Result: intl('saenext.src.constants.application.Queuing'),
      Status: 'WAIT',
    },
  ],
};

const versionFeature = [
  {
    dataIndex: 'appHost',
    label: intl('saenext.src.constants.application.ApplicationHosting'),
  },
  {
    dataIndex: 'appMaintenance',
    label: intl('saenext.src.constants.application.ApplicationOM'),
  },
  {
    dataIndex: 'enterpriseAuth',
    label: intl('saenext.src.constants.application.EnterprisePermissionManagement'),
  },
  {
    dataIndex: 'msc',
    label: intl('saenext.src.constants.application.Microservices'),
  },
  {
    dataIndex: 'arms',
    label: intl('saenext.src.constants.application.RealTimeMonitoring'),
  },
  {
    dataIndex: 'cpuBurst',
    label: 'CPU Burst',
  },
  {
    dataIndex: 'idle',
    label: intl('saenext.src.constants.application.IdleMode'),
  },
];

export const AppVersionMap = {
  lite: {
    key: 0,
    label: intl('saenext.src.constants.application.LightweightVersion'),
    icon: 'https://img.alicdn.com/imgextra/i4/O1CN01VgJS7L1ZlgJmMHFCW_!!6000000003235-55-tps-24-26.svg',
    desc: intl('saenext.src.constants.application.TheMinimalistWebApplicationHosting'),
    subDesc: intl('saenext.src.constants.application.ApplicableScenariosApplicableToLightweight'),
    // feature: versionFeature.filter(item => item.dataIndex !== 'cpuBurst'),
    // featureDatasource: {
    //   appHost: '应用生命周期管理',
    //   appMaintenance: '日志、事件、弹性伸缩、CI/CD等',
    //   enterpriseAuth: '权限控制、多环境管理、审批设置',
    //   msc: '注册中心、分布式配置管理',
    //   arms: '基础监控',
    //   idle: '支持',
    // },
  },
  std: {
    key: 1,
    label: intl('saenext.src.constants.application.StandardEdition'),
    icon: 'https://img.alicdn.com/imgextra/i3/O1CN01fgrjyp26pEiEFigvg_!!6000000007710-55-tps-23-26.svg',
    desc: intl('saenext.src.constants.application.ForAnAssembledMicroserviceApplication'),
    subDesc: intl('saenext.src.constants.application.ApplicableScenariosApplicableToMicroservice'),
    // feature: versionFeature,
    // featureDatasource: {
    //   appHost: '应用生命周期管理',
    //   appMaintenance: '日志、事件、弹性伸缩、CI/CD等',
    //   enterpriseAuth: '权限控制、多环境管理、审批设置',
    //   msc: '注册中心、分布式配置管理',
    //   arms: '基础监控、应用监控（监控概览、接口调用监控、JVM 监控、容器监控等）',
    //   cpuBurst: '支持',
    //   idle: '支持',
    // },
  },
  pro: {
    key: 2,
    label: intl('saenext.src.constants.application.ProfessionalEdition'),
    icon: 'https://img.alicdn.com/imgextra/i4/O1CN01EroZp81i6J52F83RR_!!6000000004363-55-tps-24-28.svg',
    desc: intl('saenext.src.constants.application.OneStopMicroserviceApplicationMonitoring'),
    subDesc: intl(
      'saenext.src.constants.application.ApplicableScenariosApplicableToMicroservice.1',
    ),
    // subDesc: '适用场景：适用于微服务架构应用（如 Spring Cloud、Dubbo），且需要应用监控和微服务治理（如无损上下线、流量防护、全链路灰度等的场景',
    // feature: versionFeature,
    // featureDatasource: {
    //   appHost: '应用生命周期管理',
    //   appMaintenance: '日志、事件、弹性伸缩、CI/CD等',
    //   enterpriseAuth: '权限控制、多环境管理、审批设置',
    //   msc:
    //     '注册中心、分布式配置管理、微服务治理（无损上下线、金丝雀灰度、流量防护、全链路灰度、同可用区优先路由）',
    //   arms:
    //     '基础监控、应用监控（监控概览、接口调用监控、JVM 监控、容器监控、异常分析、错误分析、调用链分析、Arthas 诊断等）',
    //   cpuBurst: '支持',
    //   idle: '支持',
    // },
  },
};
