import { intl } from '@ali/cnd';
import { get } from 'lodash';
import { getCookieByKeyName } from '../utils/hacker';

// 预发、本地多环境
const fEnv = get(window, 'ALIYUN_CONSOLE_CONFIG.fEnv');
const hostname = get(window, 'location.hostname');

export const IS_PRE_OR_LOCAL =
  fEnv === 'pre' || hostname.includes('localhost') || hostname.includes('127.0.0.1') || hostname.includes('my.console.aliyun.com');

export const IS_PRE = fEnv === 'pre';

export const X_MULTI_VERSION_ARR = window.ALIYUN_CONSOLE_GLOBAL.X_MULTI_VERSION_ARR || [
  { label: intl('saenext.src.constants.Default'), value: '0.8.20' },
];

export const DEV_ENV_LIST = get(window, 'ALIYUN_CONSOLE_GLOBAL.devEnvList', []);

export const defaultDevEnv = getCookieByKeyName('saeEnvRedirect') || DEV_ENV_LIST[0]?.value;
