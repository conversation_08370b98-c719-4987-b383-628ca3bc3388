type Align = 'left' | 'right' | undefined;
type WrapperAlign = "bottom" | "baseline" | "center" | "top" | "stretch"

const C = {
  FORM_LAYOUT: {
    labelCol: {
      fixedSpan: 6,
    },

    wrapperCol: {
      span: 18,
    },

    labelAlign: 'left' as 'left',
    labelTextAlign: 'left' as <PERSON><PERSON>,
  },
  FORM_LAYOUT_LARGE: {
    labelCol: {
      fixedSpan: 10,
    },

    wrapperCol: {
      span: 14,
    },

    labelTextAlign: 'right' as Align,
  },
  FORM_LAYOUT_XLARGE: {
    labelCol: {
      fixedSpan: 10,
    },

    wrapperCol: {
      span: 20,
    },

    labelTextAlign: 'right' as Align,
  },
  FORM_LAYOUT_SMALL: {
    labelCol: {
      fixedSpan: 4,
    },

    wrapperCol: {
      span: 20,
    },

    labelTextAlign: 'right' as Align,
  },
  FORM_CUSTOM_MIDDLE: {
    labelCol: {
      fixedSpan: 7,
    },

    wrapperCol: {
      span: 17,
    },

    labelTextAlign: 'right' as <PERSON><PERSON>,
  },
  FORM_CUSTOM_MIDDLE_LABEL_LEFT: {
    labelCol: {
      fixedSpan: 7,
    },

    wrapperCol: {
      span: 17,
    },

    labelTextAlign: 'left' as Align,
  },
  FORM_BOOTSTRAP_MIDDLE: {
    labelCol: {
      fixedSpan: 7,
    },

    wrapperCol: {
      span: 18,
    },

    labelTextAlign: 'right' as Align,
  },
  FORM_BOOTSTRAP_EDIT: {
    labelCol: {
      fixedSpan: 7,
    },

    wrapperCol: {
      span: 14,
    },

    labelTextAlign: 'right' as Align,
  },
  FORM_LAYOUT_LEFT: {
    labelAlign: 'left' as 'left',
    labelTextAlign: 'left' as 'left',
    wrapperCol: { span: 18, align: 'center' as WrapperAlign },
    labelCol: { span: 6, style: { width: 220, maxWidth: 220 } },
  }
};

export default C;