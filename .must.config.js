// more config: http://gitlab.alibaba-inc.com/parrot/parrot-tool-must/blob/master/doc/config.md
module.exports = {
  extract: {
    name: 'saenext',
    sourcePath: 'src',
    fileType: 'ts',
    prettier: true,
    // include: (path) => {
    //   return path.includes('constant')
    // },
    exclude: (path) => {
      if(path.indexOf('constants/ignoreMust') !==-1 ){
        return true;
      }
      return false;
    },
    macro: {
      path: 'src/locales',
      method: 'intl("$key$")',
      import: "import { intl } from '@ali/cnd'",
    },
    babel: {
      allowImportExportEverywhere: true,
      decoratorsBeforeExport: true,
      plugins: [
        'asyncGenerators',
        'classProperties',
        'decorators-legacy',
        'doExpressions',
        'exportExtensions',
        'exportDefaultFrom',
        'typescript',
        'functionSent',
        'functionBind',
        'jsx',
        'objectRestSpread',
        'dynamicImport',
        'numericSeparator',
        'optionalChaining',
        'optionalCatchBinding',
      ],
    },
    isNeedUploadCopyToMedusa: true,
    sourceLang: 'zh-CN',
  },
};
