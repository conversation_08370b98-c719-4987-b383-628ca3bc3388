{"name": "@ali/sae", "version": "1.0.0", "description": "## 开发", "main": "src/index.js", "scripts": {"start": "node build.js && xconsole start", "build": "node build.js && NODE_ENV=production xconsole build", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "lint": "eslint src/"}, "repository": {"type": "git", "url": "http://gitlab.alibaba-inc.com/mamba/sae.git"}, "author": "mamba", "dependencies": {"@ali/aes-tracker": "^3.1.0", "@ali/aes-tracker-plugin-api": "^3.1.1", "@ali/aes-tracker-plugin-autolog": "^3.0.5", "@ali/aes-tracker-plugin-emogine": "^3.0.11", "@ali/aes-tracker-plugin-event": "^3.0.0", "@ali/aes-tracker-plugin-jserror": "^3.0.3", "@ali/aes-tracker-plugin-pv": "^3.0.5", "@ali/aes-tracker-plugin-resourceError": "^3.0.4", "@ali/apilot": "^1.5.16", "@ali/aplus_universal": "^5.2.2", "@ali/cnd": "^1.1.50", "@ali/deep": "^1.43.12", "@ali/deep-form": "^1.1.9", "@ali/deep-form-helper": "^1.3.21", "@ali/deep-number-range-field": "^1.0.1", "@ali/deep-radio-field": "^1.1.4", "@ali/widget-loader": "^3.12.3", "@ali/xconsole-rc-tags": "^2.5.2", "@alicloud/alfa-react": "^1.6.1", "@alicloud/console-base-conf-parse-env": "^1.4.4", "@alicloud/console-components": "2.x", "@alicloud/console-components-slide-panel": "^3.0.8", "@alicloud/console-one-conf": "^1.7.9", "@alicloud/xconsole-error-center": "^2.3.50", "@alicloud/xconsole-service": "^2.5.10", "@alifd/next": "^1.27.21", "@alife/aisc-widgets": "^3.1.20", "@lezer/highlight": "^1.2.1", "@lezer/lr": "^1.4.2", "@monaco-editor/react": "4.4.1", "@prometheus-io/lezer-promql": "^0.304.1", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "file-saver": "^2.0.5", "js-sls-logger": "^2.0.2", "js-yaml": "^4.1.0", "jsonlint-mod": "^1.7.6", "lodash": "^4.17.21", "moment": "^2.22.1", "monaco-editor": "0.30.1", "prop-types": "^15.8.1", "react": "^16.12.0", "react-code-diff-lite": "^1.0.14", "react-dom": "^16.12.0", "react-hot-loader": "^4.1.2", "react-json-view": "^1.19.1", "react-resize-observer": "^1.1.1", "styled-components": "^5.3.11", "url": "^0.11.1", "xlsx": "^0.18.5", "xterm-addon-fit": "^0.5.0", "xterm-for-react": "^1.0.4"}, "devDependencies": {"@ali/xconsole": "^2.0.0", "@types/lodash": "^4.14.197", "@types/react": "^16.14.0", "@typescript-eslint/eslint-plugin": "^4.4.0", "@typescript-eslint/parser": "^4.4.0", "eslint": "^7.32.0", "eslint-config-ali": "^11.1.1", "eslint-plugin-import": "^2.11.0", "eslint-plugin-react": "^7.7.0", "eslint-plugin-react-hooks": "^1.1.0-alpha.1", "prettier": "^1.19.1"}, "overrides": {"@alicloud/console-components": "2.x", "@alicloud/console-components-table": "2.x", "@alicloud/console-components-slide-panel": "^3.0.8"}, "resolutions": {"@alicloud/console-base-messenger": "1.18.10", "@alicloud/console-components": "2.x", "@alicloud/console-components-table": "2.x", "@alicloud/console-components-slide-panel": "^3.0.8"}, "engines": {"node": ">=10.0.0", "install-node": "20.0.0"}, "private": true, "keywords": ["sae", "serverless"], "license": "ISC"}