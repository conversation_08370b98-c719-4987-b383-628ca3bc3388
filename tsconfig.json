{
  "compilerOptions": {
    "outDir": "./build",
    "jsx": "react",
    "module": "esnext",
    "target": "es5",
    "esModuleInterop": true,
    "lib": ["dom", "es2015", "scripthost"],
    "allowJs": true,
    "moduleResolution": "node",
    "baseUrl": ".",
    "skipLibCheck": true,
    "experimentalDecorators": true,
//    "noUnusedLocals": true,
    "importHelpers": true,
    "paths": {
      "~/*": ["./src/*"],
      "react": [ "./node_modules/@types/react"]
    },
  },
  "include": ["./src/**/*", "build.js"],
  "exclude": ["node_modules"]
}
